/*
 * @Author: tianh
 * @Date: 2022-04-18 15:52:12
 * @LastEditors: tianh
 * @LastEditTime: 2022-05-10 14:16:30
 * @Descripttion:
 */
import React, { Fragment, useState, useEffect } from 'react';
import { Card, Switch, message } from 'antd';
import style from './style.less';
import { getNetworkFlow, get_traffic_output } from '@/services/monitor';
import moment from 'moment';
import { toThousands } from '@/utils/utils';

const echarts = require('echarts');
const Simple = (props: any) => {
  let timer: any = null;
  const { getSwitch, isSimple } = props;
  const [flowData, setflowData] = useState<any>([]);
  const [flowSpeed, setflowSpeed] = useState<any>({
    min: 0,
    max: 100,
    out_total: 0,
  });
  const initnetwork = () => {
    if (flowData.speed) {
      let chartDom = document.getElementById('network');
      let myChart = echarts.init(chartDom);
      let text = '';
      let data = flowData.speed;
      let time = flowData.speed.createTime.map((item: any) => {
        return moment(item).format('YYYY-MM-DD HH:mm:ss');
      });
      delete data.createTime;
      let series: {
        name: string;
        type: string;
        data: any;
      }[] = [];
      Object.keys(data).map((item, index) => {
        series.push({
          name: `${item}(Mbps)`,
          type: 'line',
          data: data[item],
        });
      });
      Object.keys(flowData.total).map((item, index) => {
        let num = toThousands(flowData.total[item]);
        let str = `${item}:  ${num}(MB)        `;
        text = text + str;
      });
      let option = {
        title: {
          text: text,
          left: 'left',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          data: time, // x轴坐标名称
          type: 'category',
        },
        yAxis: {
          // type: 'value',
          // axisLabel: {
          //   // formatter: '{value}/mbps', //字符串模板
          //   formatter: (value: any) => {
          //     console.log(2121122, value);
          //   },
          // },
        },
        series: series,
      };
      option && myChart.setOption(option);
    }
  };
  /**
   * @name:
   * @description: 获取网口流量数据
   * @param {*}
   * @return {*}
   */
  const getFlow = () => {
    getNetworkFlow().then((res) => {
      if (res.flag) {
        setflowData(res.data);
      }
    });
  };
  const getFlowData = () => {
    get_traffic_output().then((res) => {
      if (res.flag) {
        setflowSpeed(res.data);
      } else {
        message.error(res.message);
      }
    });
  };

  const initFlow = () => {
    let chartDom = document.getElementById('flow');
    let myChart = echarts.init(chartDom);
    if (flowData.total) {
      let total = 0;
      let data = { ...flowData.speed };
      delete data.createTime;
      Object.keys(data).forEach((item) => {
        let length = data[item].length;
        total = total + data[item][length - 1];
      });
      let max = 1000;
      if (total > 100000) {
        max = 200000;
      } else if (total > 10000) {
        max = 100000;
      } else if (total > 1000) {
        max = 10000;
      }

      let option = {
        tooltip: {
          formatter: '{a} <br/>{b} : {c}%',
        },
        series: [
          {
            name: 'Pressure',
            type: 'gauge',
            progress: {
              show: true,
            },
            detail: {
              valueAnimation: true,
              formatter: '{value}',
            },
            min: 0,
            // max: flowSpeed.max,
            max:max,
            data: [
              {
                value: total,
                name: 'Mpbs',
              },
            ],
          },
        ],
      };
      myChart.setOption(option);
    }
  };

  useEffect(() => {
    initnetwork();
    initFlow();
  }, [flowData]);

  // useEffect(() => {
  //   initFlow();
  // }, [flowSpeed]);
  useEffect(() => {
    getFlow();
    // getFlowData();
    timer = setInterval(() => {
      getFlow();
      // getFlowData();
    }, 5000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  return (
    <Fragment>
      <div className={style.top_title}>
        <div>
          <span className={style.title}>流量监控</span>
        </div>
        <div>
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <div style={{ display: 'flex' }}>
        <Card
          style={{ width: '69%' }}
          title={
            <div>
              <span>网口流量统计</span>
            </div>
          }
        >
          <div className={style.cupCharts} id="network"></div>
        </Card>
        <Card title="流量吞吐" style={{ width: '29%' }}>
          <div className={style.flow} id="flow"></div>
        </Card>
      </div>
    </Fragment>
  );
};

export default Simple;
