#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
import time
from utils.base_knowledge import BaseKnowledge
from utils.utils import flask_response


class GetTask(BaseKnowledge):
    """获取任务名称&任务ID"""

    def __init__(self):
        """初始化参数"""
        super(GetTask, self).__init__(col="back_explore", filter_name="")

    def get(self):
        """从mongoDB中查询任务"""
        data = list(self.mongo_client.find(self.col, {}, {'celeryId': 1, 'taskName': 1, 'startTime': 1, 'endTime': 1}))
        if not data:
            return flask_response("MongoDB return None data", True, [])
        res = []
        for task in data:
            if not task['celeryId']:
                continue

            task_dict = {
                "celeryId": task['celeryId'],
                "taskName": task['taskName'],
                'startTime': int(time.time()) * 1000 if not task["startTime"] else int(
                    time.mktime(time.strptime(task["startTime"], "%Y-%m-%d %H:%M:%S"))) * 1000,
                'endTime': int(time.time()) * 1000 if not task["endTime"] else int(
                    time.mktime(time.strptime(task["endTime"], "%Y-%m-%d %H:%M:%S"))) * 1000
            }
            res.append(task_dict)
        return flask_response("", True, res)

    @staticmethod
    def get_filed():
        """获取killchains字段"""
        return {
            "name": "lockheedKillchains",
            "list": []
        }
