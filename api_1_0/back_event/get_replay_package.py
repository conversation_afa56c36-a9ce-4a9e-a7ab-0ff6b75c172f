#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>


from utils import param_check
from flask import request
from flask_restful import Resource
from utils.param_check import Validator
from utils.utils import flask_response
from celery_tasks import get_replay_package


class PackageReplay(Resource):
    '''根据给定时间段下载回放数据包(异步执行)'''

    @param_check.check_flask_args(Validator('replay_package_check'), request)
    def get(self, **kwargs):
        startTime = int(kwargs["args"].get("startTime"))
        stopTime = int(kwargs["args"].get("stopTime"))
        taskId = kwargs["args"].get("taskId")
        if startTime > stopTime:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})

        get_replay_package.get_package.delay(startTime, stopTime, taskId)

        return flask_response("", True, {})
