/*
 * @Author: tianh
 * @Date: 2021-11-17 11:28:55
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-21 15:22:43
 * @Descripttion:
 */
import { clearData, getPolicy, getTaskData, saveForm, setAlermData, submitPolicy } from '@/services/configs';
import { policyList } from '@/utils/enumList';
import { QuestionCircleOutlined } from "@ant-design/icons";
import type { RadioChangeEvent } from 'antd';
import {
  Button,
  Card,
  Form,
  Input,
  Radio,
  Tooltip,
  message
} from 'antd';
import cn from 'classnames';
import React, { useEffect, useState } from 'react';
import {pick} from 'lodash'
import style from '../style.less';
import FileRecoverySettings from './FileRecoverySettings';

const runTip = `运行模式主要是为了最优化使用系统资源而设置；偏实时：在端口外接流量作为主要的使用场景时选择此模式。偏回放：在使用数据包进行回放作为主要使用场景时选择此模式。`;

const Index = (props: any) => {
  const [form] = Form.useForm();
  const [detail, setdetail] = useState({
    divice: '',
    performance: '',
    realtime: false,
    software: '',
    alarm_filter: true,
    alarm_merge: '',
    alarm_out: '',
  });
  const pcapSavePolicyVisible = React.useMemo(() => {
    const seriesNumber = detail?.divice?.slice(1);
    return seriesNumber != '5000' && seriesNumber != '8000';
  }, [detail]);
  const [policy, setpolicy] = useState('');

  // 保存运行模式
  const onSubmit = () => {
    const params = pick(detail, ['realtime', 'divice'])
    saveForm(params).then((res: any) => {
      if (res.flag) {
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    });
  };

  // 获取基础配置
  const getConfigs = () => {
    getTaskData().then((res: any) => {
      if (res.flag) {
        setdetail(res.data.detail);
      }
    });
  };

  // 切换偏回放 偏实时
  const playback = (e: RadioChangeEvent) => {
    setdetail({ ...detail, realtime: e.target.value });
  };

  // 告警风暴抑制开关
  const handleAlert = (e: RadioChangeEvent) => {
    setdetail({ ...detail, alarm_filter: e.target.value });
  }

  // 修改合并阈值
  const handleChangeMerge = (e: any) => {
    setdetail({ ...detail, alarm_merge: e.target.value });
  };

  // 修改只保留前几
  const handleChangeOut = (e: any) => {
    setdetail({ ...detail, alarm_out: e.target.value });
  };

  // 保存风暴抑制
  const submitAlarm = () => {
    if (Number(detail.alarm_merge) < 2 || Number(detail.alarm_merge) > 65535) {
      message.error('相同告警合并阈值，请输入2~65535');
      return;
    }
    if (Number(detail.alarm_out) < 2 || Number(detail.alarm_out) > 65535) {
      message.error('同一批次保留告警数，请输入2~65535');
      return;
    }
    form.validateFields().then((value: any) => {
      setAlermData({ performance: detail.performance, alarmFilter: detail.alarm_filter, alarmMerge: String(detail.alarm_merge), alarmOut: String(detail.alarm_out) }).then((res) => {
        if (res.flag) {
          message.success('修改成功');
        }
      });
    });
  };

  // 保存白名单配置
  const savePolicy = () => {
    submitPolicy({ policy: policy }).then(res => {
      if (res.flag) {
        message.success(res.message);
      }
    });
  };

  // 获取白名单配置
  const getPolicyConfig = () => {
    getPolicy().then(res => {
      if (res.flag) {
        setpolicy(res.data.policy);
      }
    });
  };

  // 改变白名单策略
  const changeWhite = (e: RadioChangeEvent) => {
    setpolicy(e.target.value);
  }

  // 清除数据
  const handleDel = () => {
    clearData().then(res => { });
    message.success('操作成功');
  };

  useEffect(() => {
    getConfigs();
    getPolicyConfig();
  }, []);

  return (
    <div className={style.box}>
      <Card title={<span className="font-bold">设备信息</span>} bordered style={{ width: '100%' }}>
        <div className={style.basics_box}>
          <div>设备型号：{detail.divice}</div>
          <div>标定性能：{detail.performance}</div>
          <div>软件版本：{detail.software}</div>
        </div>
      </Card>
      <div className={style.bottomCard}>
        <Card
          bordered
          style={{ width: '100%' }}
          title={<div>
            <span className={cn('font-bold mr-2', style.btn_right)}>运行模式</span>
            <Tooltip
              title={runTip}
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
          }
        >
          <div className={style.basics_box}>
            <Radio.Group value={detail.realtime} onChange={playback}>
              <Radio value={true}>偏实时</Radio>
              <Radio value={false}>偏回放</Radio>
            </Radio.Group>
            <Button
              style={{ marginLeft: 24 }}
              type="primary"
              onClick={onSubmit}
            >
              保存
            </Button>
          </div>
        </Card>
      </div>
      <div className={style.bottomCard}>
        <Card title={<span className="font-bold">告警风暴抑制</span>} bordered style={{ width: '100%' }}>
          <div className={style.alertCurb}>
            <div className={style.mergeLimit}>
              <Radio.Group value={detail.alarm_filter} onChange={handleAlert}>
                <Radio value={true}>开</Radio>
                <Radio value={false}>关</Radio>
              </Radio.Group>
            </div>
            <div className={style.mergeLimit}>
              <span>相同告警合并阈值
                <Tooltip title="'相同告警'指的是源、目的IP相同，且规则ID、情报ID或者模型名称也相同的告警。合并是在同一时间间隔（20s）内进行统计处理的。默认阈值为2">
                  <QuestionCircleOutlined />
                </Tooltip>
                ：</span>
              <Input value={detail.alarm_merge} onChange={handleChangeMerge} placeholder='2~65535' />
            </div>
            <div className={style.mergeLimit}>
              <span>同一规则/情报/模型产生的不同告警，同一批次只保留前
                <Tooltip title="在同—时间间隔范围内(20秒)，同—规则/情报/模型可能会被不同IP之间的流量命中，从而产生多个告警事件，不同IP是有意义的，默认保留前10个不同告警，请酌情修改。">
                  <QuestionCircleOutlined />
                </Tooltip>
                ：</span>
              <Input value={detail.alarm_out} onChange={handleChangeOut} placeholder='2~65535' />
              条
            </div>
            <div>
              <Button
                type="primary"
                onClick={submitAlarm}
              >
                保存
              </Button>
            </div>
          </div>
        </Card>
      </div>
      {pcapSavePolicyVisible && <div className={style.bottomCard}>
        <Card title={<div>
          <span className={cn('font-bold mr-2', style.btn_right)}>全流量留存策略</span>
          <Tooltip
            title={
              <div>
                说明:
                <br />
                在流量存储之前判断是否过滤白名单
                <br />
                <br />
                <span>智能白名单过滤</span>
                ：系统预定义白名单 + 用户自定义白名单
                <br />
                <br />
                <span>自定义白名单过滤</span>
                ：仅过滤用户自定义白名单
                <br />
                <br />
                <span>全量存储</span>
                ：不过滤白名单
              </div>
            }
          >
            <QuestionCircleOutlined />
          </Tooltip>
        </div>} bordered style={{ width: '100%' }}>
          <Radio.Group value={policy} onChange={changeWhite}>
            {policyList.map(item => {
              return (
                <Radio key={item.value} value={item.value}>
                  {item.key}
                </Radio>
              );
            })}
          </Radio.Group>

          <Button className={style.btn} onClick={savePolicy} type="primary">
            保存
          </Button>
        </Card>
      </div>}
      <FileRecoverySettings />
    </div>
  );
};
export default Index;
