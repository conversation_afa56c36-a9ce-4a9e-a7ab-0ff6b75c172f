#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_es_template.py
# @time: 2019/07/18
# @software: PyCharm

import os
import json
from utils.logger import get_ndr_logger

LOG = get_ndr_logger('api_log', __file__)


class Singleton:
    __cls = dict()

    def __init__(self, cls):
        self.__key = cls

    def __call__(self, *args, **kwargs):
        if self.__key not in self.cls:
            self[self.__key] = self.__key(*args, **kwargs)
        return self[self.__key]

    def __setitem__(self, key, value):
        self.cls[key] = value

    def __getitem__(self, item):
        return self.cls[item]

    @property
    def cls(self):
        return self.__cls

    @cls.setter
    def cls(self, cls):
        self.__cls = cls


@Singleton
class MongoTemplate:
    def read_template(self, mongo_template_name):
        """从文件加载 es template"""
        try:
            dir_path = os.path.abspath(os.path.split(os.path.abspath(os.path.realpath(__file__)))[0])
            f = open(os.path.join(dir_path, "{0}.json".format(mongo_template_name)), "r")
            json_data = json.load(f)
            f.close()
            return json_data
        except Exception as e:
            LOG.error(e)
