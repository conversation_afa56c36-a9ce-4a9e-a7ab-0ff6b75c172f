import React, { useEffect, useMemo, useState } from 'react';
import cs from 'classnames';
import moment from 'moment';
import CountUp from 'react-countup';

import { Section } from './Section';
import { DashCard } from './DashCard';
import { StaticNumber } from './StaticNumber';

import HighRiskHostCard, { TimeRange } from './UnSettledThreat/HighRiskHostCard';
import AttackerCard from './UnSettledThreat/AttackerCard';
import RiskHostCard from './UnSettledThreat/RiskHostCard';
import SceneMonitorCard from './UnSettledThreat/SceneMonitorCard';
import AttackCountryCard from './EventAnalysisChartCards/AttackCountryCard';
import EventTrendsCard from './EventAnalysisChartCards/EventTrendsCard';
import ThreatNamesCard from './EventAnalysisChartCards/ThreatNamesCard';
import ThreatLevelCard from './EventAnalysisChartCards/ThreatLevelCard';
import AttackDirectionCard from './EventAnalysisChartCards/AttackDirectionCard';
import { AttackChainCard } from './AttackChainCard';
import DateRangePicker from '@/components/DateRangePicker';

import style from './index.less'

const timeRange = JSON.parse(sessionStorage.getItem('dateRange') || '[]')

const Overview = () => {
  const [dateRange, setDateRange] = useState((timeRange.length ? timeRange : [moment().startOf('day'), moment().endOf('day')])?.map((x: number) => moment(x)))
  const [highRiskStastic, setHighRiskStastic] = useState({ total: 0, diff: 0 })
  const [attackerStastic, setAttackerStastic] = useState({ total: 0, diff: 0 })
  const [riskStastic, setRiskStastic] = useState({ total: 0, diff: 0 })
  const [eventStastic, setEventStastic] = useState({ total: 0, diff: 0, successTotal: 0, suspiciousTotal: 0, attemptTotal: 0 })


  const handleDateRangeChange = (dateRange?: any) => {
    setDateRange(dateRange);
    sessionStorage.setItem('dateRange', JSON.stringify(dateRange?.map((x: moment.Moment) => x.toDate().getTime())))
  }

  const currentTimeRange: TimeRange = useMemo(() => (
    {
      start_time: dateRange[0].valueOf(),
      end_time: dateRange[1].valueOf()
    }
  ), [dateRange])

  useEffect(() => {
    if (!timeRange.length) {
      sessionStorage.setItem('dateRange', JSON.stringify([currentTimeRange.start_time, currentTimeRange.end_time]))
    }
  }, [])

  return (
    <div className={style.overview_wrap}>
      <DateRangePicker
        className="fixed top-2 left-65 z-20"
        value={dateRange}
        // disabled={loading}
        onChange={handleDateRangeChange}
      />
      <div className={style.overview_content}>
      <Section className={style.static_info} title='统计信息'>
        <DashCard title="高风险主机">
            <StaticNumber total={highRiskStastic.total} diff={highRiskStastic.diff} />
        </DashCard>
        <DashCard title="攻击者">
            <StaticNumber total={attackerStastic.total} diff={attackerStastic.diff} />
        </DashCard>
        <DashCard title="风险主机">
            <StaticNumber total={riskStastic.total} diff={riskStastic.diff} />
        </DashCard>
          <DashCard className={style.threat_event} title="威胁事件">
            <StaticNumber total={eventStastic.total} diff={eventStastic.diff} />
          <div className='flex gap-2'>
            <CountUp
              className={cs(style.custom_dot, style.custom_dot__success)}
              duration={1}
              delay={0}
              preserveValue prefix='成功: '
              end={eventStastic.successTotal}
            />
            <CountUp
              className={cs(style.custom_dot, style.custom_dot__suspend)}
              duration={1}
              delay={0}
              preserveValue prefix='可疑: '
              end={eventStastic.suspiciousTotal}
            />
            <CountUp
              className={cs(style.custom_dot, style.custom_dot__try)}
              duration={1}
              delay={0}
              preserveValue prefix='尝试: '
              end={eventStastic.attemptTotal}
            />
          </div>
        </DashCard>
          <AttackChainCard timeRange={currentTimeRange} />
        </Section>
        <Section title='待处置威胁'>
          <div className={style.pending_threats}>
            <HighRiskHostCard
              onStaticData={setHighRiskStastic}
              timeRange={currentTimeRange}
            />
            <AttackerCard
              onStaticData={setAttackerStastic}
              timeRange={currentTimeRange}
            />
            <RiskHostCard
              onStaticData={setRiskStastic}
              timeRange={currentTimeRange}
            />
            <SceneMonitorCard
              timeRange={currentTimeRange}
            />
          </div>
        </Section>
        <Section title='事件分析'>
          <div className={style.event_analysis}>
            <AttackCountryCard timeRange={currentTimeRange} />
            <EventTrendsCard timeRange={currentTimeRange} onStaticData={setEventStastic} />
            <ThreatNamesCard timeRange={currentTimeRange} />
            <div className='flex gap-3'>
              <ThreatLevelCard timeRange={currentTimeRange} />
              <AttackDirectionCard timeRange={currentTimeRange} />
            </div>
          </div>
        </Section>
      </div>
    </div>
  );
}

export default Overview;


