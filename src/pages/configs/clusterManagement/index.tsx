/*
 * @Author: tianh
 * @Date: 2021-11-18 17:37:15
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-30 11:51:09
 * @Descripttion:
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Table, Popconfirm, message, Input, Modal, Form, Select, Switch, Tooltip } from 'antd';
import { useHistory } from '@/hooks/global';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { clusterList, addCluster, delNodes, remvoeNode, setMainNode,joinGroup } from '@/services/configs';
// import style from './style.less';
import moment from 'moment';
import { ellipsis, handleEmpty } from '@/utils/utils';
import { render } from 'react-dom';
const { Option } = Select;
const Index = () => {
  const [form] = Form.useForm();
  const history = useHistory();
  const [dataSource, setdataSource] = useState<any>([]);
  const [total, settotal] = useState(0)
  const [selectedRows, setselectedRows] = useState([]);
  const [params, setparams] = useState({
    page:1,
    pageSize:99
  });
  const [visible, setvisible] = useState(false);
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox');
  const columns = [
    {
      title: '节点名称',
      dataIndex: 'node_name',
      render: (t: any, record: any) => {
        if (record.node_role === 'master') {
          return (
            <p>
              {t}{' '}
              <span style={{ display: 'inline-block', padding: 4, background: '#296dff', color: '#fff', borderRadius: 6 }}>
                当前节点
              </span>
            </p>
          );
        } else {
          return t;
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'node_status',
      render: (t: any, record: any) => {
        if (t === 'online') {
          return '在线';
        } else if (t === 'offline') {
          return '离线';
        } else {
          return '已从集群中移';
        }
      },
    },
    {
      title: '身份',
      dataIndex: 'node_role',
      render: (t: any) => {
        if (t === 'master') {
          return '主节点';
        } else if (t === 'slave') {
          return '从节点';
        }
      },
    },
    {
      title: 'IP地址',
      dataIndex: 'node_ip',
    },
    {
      title: '备注',
      dataIndex: 'description',
    },

    {
      title: '操作',
      dataIndex: '',
      render: (t: any, record: any) => {
        return (
          <div>
            <Popconfirm
              title="确定易主吗?"
              onConfirm={() => {
                handleChange(record);
              }}
            >
              <Button type="link" disabled={record.node_role === 'master'}>
                易主
              </Button>
            </Popconfirm>

           
            <Popconfirm
              title="确定加入吗?"
              onConfirm={() => {
                handleJoin(record);
              }}
            >
              <Button type="link">
                加入集群
              </Button>
            </Popconfirm>
            <Popconfirm
              title="确定退出吗?"
              onConfirm={() => {
                handleExit(record);
              }}
            >
              <Button type="link">
                退出集群
              </Button>
            </Popconfirm>
            
          </div>
        );
      },
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: [], selectedRows: any) => {
      setselectedRows(selectedRows);
    },
    getCheckboxProps(record: any) {
      if (record.node_role === 'master') {
        return {
          disabled: true,
        };
      }
    },

    selectedRows,
  };
  const roleList = [
    {
      value: 'master',
      label: '主节点',
    },
    {
      label: '从节点',
      value: 'slave',
    },
  ];
  const handleAdd = () => {
    setvisible(true);
  };
  const handleSubmit = () => {
    form.validateFields().then((value: any) => {
      addCluster(value).then((res) => {
        if (res.flag) {
          message.success(res.message);
          setvisible(false);
          getList();
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const handleDelete = () => {
    if (!selectedRows || !selectedRows.length) {
      return '请选择数据';
    }
    let arr = selectedRows.map((item: any) => {
      return item.node_name;
    });
    delNodes({ node_name: arr }).then((res) => {
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };


  const handleChange = (value: any) => {
    setMainNode({ node_name: value.node_name }).then((res) => {
      if (res.flag) {
        message.success('操作成功');
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleJoin = (value:any)=>{
    joinGroup({ node_name: value.node_name }).then((res) => {
      if (res.flag) {
        message.success('操作成功');
        getList();
      } else {
        message.error(res.message);
      }
    });
  }
  const handleExit = (value:any)=>{
    remvoeNode({ node_name: value.node_name }).then((res) => {
      if (res.flag) {
        message.success('操作成功');
        getList();
      } else {
        message.error(res.message);
      }
    });

  }
  const getList = () => {
    clusterList(params).then((res) => {
      if (res.flag) {
        setselectedRows([]);
        settotal(res.data.total)
        setdataSource(res.data.cols);
      }
    });
  };

 
   
  useEffect(() => {
    getList();
  }, []);

  return (
    <div>
      <div className="p-4 bg-d0 b b-solid b-brd6">
        <Button type="primary" onClick={handleAdd} ghost className="margin_right_10">
          添加
        </Button>
        <Popconfirm title="确定删除吗?" onConfirm={handleDelete}>
          <Button type="primary" ghost danger className="margin_right_10">
            删除
          </Button>
        </Popconfirm>
      </div>
      <div className="w-100% mt-4">
        <DndProvider backend={HTML5Backend}>
          <Table
         rowKey="_id"
            columns={columns}
            dataSource={dataSource}
           
            pagination={false}
            rowSelection={{
              type: selectionType,
              ...rowSelection,
            }}
          />
        </DndProvider>
      </div>

      <Modal
        title="新增节点"
        onCancel={() => {
          setvisible(false);
        }}
        onOk={handleSubmit}
        destroyOnClose={true}
        maskClosable={false}
        visible={visible}
      >
        <Form form={form}>
          <Form.Item name="node_name" label="节点名称" rules={[{ required: true, message: '请填写' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="node_role" label="节点身份" rules={[{ required: true, message: '请选择' }]}>
            <Select>
              {roleList.map((item) => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="node_ip" label="IP地址" rules={[{ required: true, message: '请填写' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label="备注" rules={[{ required: true, message: '请填写' }]}>
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Index;
