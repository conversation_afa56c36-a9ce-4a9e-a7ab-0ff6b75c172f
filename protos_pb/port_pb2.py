# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: port.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nport.proto\x12\x04port\",\n\x0bReplyHeader\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\"\r\n\x0bPortRequest\"M\n\x0cPortResponse\x12\x1e\n\x03msg\x18\x01 \x01(\x0b\x32\x11.port.ReplyHeader\x12\x1d\n\x05ports\x18\x02 \x03(\x0b\x32\x0e.port.PortInfo\"|\n\x08PortInfo\x12\x0f\n\x07port_id\x18\x01 \x01(\r\x12\x13\n\x0bport_status\x18\x02 \x01(\r\x12\x11\n\tlink_mode\x18\x03 \x01(\r\x12\x0f\n\x07\x61\x64\x64ress\x18\x04 \x01(\r\x12\x0f\n\x07netmask\x18\x05 \x01(\r\x12\x15\n\rether_address\x18\x06 \x01(\x0c\"\x11\n\x0fPortLinkRequest\"]\n\x10PortLinkResponse\x12\x1e\n\x03msg\x18\x01 \x01(\x0b\x32\x11.port.ReplyHeader\x12)\n\x0bstatus_list\x18\x02 \x03(\x0b\x32\x14.port.PortLinkStatus\"6\n\x0ePortLinkStatus\x12\x0f\n\x07port_id\x18\x01 \x01(\r\x12\x13\n\x0blink_status\x18\x02 \x01(\r\"M\n\x1a\x43reateForwardPolicyRequest\x12\x0c\n\x04mode\x18\x01 \x01(\r\x12\x0f\n\x07in_port\x18\x02 \x01(\r\x12\x10\n\x08out_port\x18\x03 \x01(\r\"-\n\x1a\x44\x65leteForwardPolicyRequest\x12\x0f\n\x07in_port\x18\x01 \x01(\r\"3\n\x12LinkModeSetRequest\x12\x0f\n\x07port_id\x18\x01 \x01(\r\x12\x0c\n\x04mode\x18\x02 \x01(\r\"I\n\x14IPv4ConfigSetRequest\x12\x0f\n\x07port_id\x18\x01 \x01(\r\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\x12\x0f\n\x07netmask\x18\x03 \x01(\t\"L\n\x14MirroringPortRequest\x12\x0f\n\x07port_id\x18\x01 \x01(\r\x12\x10\n\x08group_id\x18\x02 \x01(\r\x12\x11\n\tdirection\x18\x03 \x01(\r\"+\n\x18UndoMirroringPortRequest\x12\x0f\n\x07port_id\x18\x01 \x01(\r\"L\n\x14IPv6ConfigSetRequest\x12\x0f\n\x07port_id\x18\x01 \x01(\r\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\x12\x12\n\nprefix_len\x18\x03 \x01(\r2\xed\x04\n\x0bPortService\x12\x33\n\x08GetPorts\x12\x11.port.PortRequest\x1a\x12.port.PortResponse\"\x00\x12?\n\x0cGetPortLinks\x12\x15.port.PortLinkRequest\x1a\x16.port.PortLinkResponse\"\x00\x12L\n\x13\x43reateForwardPolicy\x12 .port.CreateForwardPolicyRequest\x1a\x11.port.ReplyHeader\"\x00\x12L\n\x13\x44\x65leteForwardPolicy\x12 .port.DeleteForwardPolicyRequest\x1a\x11.port.ReplyHeader\"\x00\x12<\n\x0bLinkModeSet\x12\x18.port.LinkModeSetRequest\x1a\x11.port.ReplyHeader\"\x00\x12@\n\rIPv4ConfigSet\x12\x1a.port.IPv4ConfigSetRequest\x1a\x11.port.ReplyHeader\"\x00\x12@\n\rMirroringPort\x12\x1a.port.MirroringPortRequest\x1a\x11.port.ReplyHeader\"\x00\x12H\n\x11UndoMirroringPort\x12\x1e.port.UndoMirroringPortRequest\x1a\x11.port.ReplyHeader\"\x00\x12@\n\rIPv6ConfigSet\x12\x1a.port.IPv6ConfigSetRequest\x1a\x11.port.ReplyHeader\"\x00\x62\x06proto3')



_REPLYHEADER = DESCRIPTOR.message_types_by_name['ReplyHeader']
_PORTREQUEST = DESCRIPTOR.message_types_by_name['PortRequest']
_PORTRESPONSE = DESCRIPTOR.message_types_by_name['PortResponse']
_PORTINFO = DESCRIPTOR.message_types_by_name['PortInfo']
_PORTLINKREQUEST = DESCRIPTOR.message_types_by_name['PortLinkRequest']
_PORTLINKRESPONSE = DESCRIPTOR.message_types_by_name['PortLinkResponse']
_PORTLINKSTATUS = DESCRIPTOR.message_types_by_name['PortLinkStatus']
_CREATEFORWARDPOLICYREQUEST = DESCRIPTOR.message_types_by_name['CreateForwardPolicyRequest']
_DELETEFORWARDPOLICYREQUEST = DESCRIPTOR.message_types_by_name['DeleteForwardPolicyRequest']
_LINKMODESETREQUEST = DESCRIPTOR.message_types_by_name['LinkModeSetRequest']
_IPV4CONFIGSETREQUEST = DESCRIPTOR.message_types_by_name['IPv4ConfigSetRequest']
_MIRRORINGPORTREQUEST = DESCRIPTOR.message_types_by_name['MirroringPortRequest']
_UNDOMIRRORINGPORTREQUEST = DESCRIPTOR.message_types_by_name['UndoMirroringPortRequest']
_IPV6CONFIGSETREQUEST = DESCRIPTOR.message_types_by_name['IPv6ConfigSetRequest']
ReplyHeader = _reflection.GeneratedProtocolMessageType('ReplyHeader', (_message.Message,), {
  'DESCRIPTOR' : _REPLYHEADER,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.ReplyHeader)
  })
_sym_db.RegisterMessage(ReplyHeader)

PortRequest = _reflection.GeneratedProtocolMessageType('PortRequest', (_message.Message,), {
  'DESCRIPTOR' : _PORTREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.PortRequest)
  })
_sym_db.RegisterMessage(PortRequest)

PortResponse = _reflection.GeneratedProtocolMessageType('PortResponse', (_message.Message,), {
  'DESCRIPTOR' : _PORTRESPONSE,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.PortResponse)
  })
_sym_db.RegisterMessage(PortResponse)

PortInfo = _reflection.GeneratedProtocolMessageType('PortInfo', (_message.Message,), {
  'DESCRIPTOR' : _PORTINFO,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.PortInfo)
  })
_sym_db.RegisterMessage(PortInfo)

PortLinkRequest = _reflection.GeneratedProtocolMessageType('PortLinkRequest', (_message.Message,), {
  'DESCRIPTOR' : _PORTLINKREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.PortLinkRequest)
  })
_sym_db.RegisterMessage(PortLinkRequest)

PortLinkResponse = _reflection.GeneratedProtocolMessageType('PortLinkResponse', (_message.Message,), {
  'DESCRIPTOR' : _PORTLINKRESPONSE,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.PortLinkResponse)
  })
_sym_db.RegisterMessage(PortLinkResponse)

PortLinkStatus = _reflection.GeneratedProtocolMessageType('PortLinkStatus', (_message.Message,), {
  'DESCRIPTOR' : _PORTLINKSTATUS,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.PortLinkStatus)
  })
_sym_db.RegisterMessage(PortLinkStatus)

CreateForwardPolicyRequest = _reflection.GeneratedProtocolMessageType('CreateForwardPolicyRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATEFORWARDPOLICYREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.CreateForwardPolicyRequest)
  })
_sym_db.RegisterMessage(CreateForwardPolicyRequest)

DeleteForwardPolicyRequest = _reflection.GeneratedProtocolMessageType('DeleteForwardPolicyRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEFORWARDPOLICYREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.DeleteForwardPolicyRequest)
  })
_sym_db.RegisterMessage(DeleteForwardPolicyRequest)

LinkModeSetRequest = _reflection.GeneratedProtocolMessageType('LinkModeSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _LINKMODESETREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.LinkModeSetRequest)
  })
_sym_db.RegisterMessage(LinkModeSetRequest)

IPv4ConfigSetRequest = _reflection.GeneratedProtocolMessageType('IPv4ConfigSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _IPV4CONFIGSETREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.IPv4ConfigSetRequest)
  })
_sym_db.RegisterMessage(IPv4ConfigSetRequest)

MirroringPortRequest = _reflection.GeneratedProtocolMessageType('MirroringPortRequest', (_message.Message,), {
  'DESCRIPTOR' : _MIRRORINGPORTREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.MirroringPortRequest)
  })
_sym_db.RegisterMessage(MirroringPortRequest)

UndoMirroringPortRequest = _reflection.GeneratedProtocolMessageType('UndoMirroringPortRequest', (_message.Message,), {
  'DESCRIPTOR' : _UNDOMIRRORINGPORTREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.UndoMirroringPortRequest)
  })
_sym_db.RegisterMessage(UndoMirroringPortRequest)

IPv6ConfigSetRequest = _reflection.GeneratedProtocolMessageType('IPv6ConfigSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _IPV6CONFIGSETREQUEST,
  '__module__' : 'port_pb2'
  # @@protoc_insertion_point(class_scope:port.IPv6ConfigSetRequest)
  })
_sym_db.RegisterMessage(IPv6ConfigSetRequest)

_PORTSERVICE = DESCRIPTOR.services_by_name['PortService']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REPLYHEADER._serialized_start=20
  _REPLYHEADER._serialized_end=64
  _PORTREQUEST._serialized_start=66
  _PORTREQUEST._serialized_end=79
  _PORTRESPONSE._serialized_start=81
  _PORTRESPONSE._serialized_end=158
  _PORTINFO._serialized_start=160
  _PORTINFO._serialized_end=284
  _PORTLINKREQUEST._serialized_start=286
  _PORTLINKREQUEST._serialized_end=303
  _PORTLINKRESPONSE._serialized_start=305
  _PORTLINKRESPONSE._serialized_end=398
  _PORTLINKSTATUS._serialized_start=400
  _PORTLINKSTATUS._serialized_end=454
  _CREATEFORWARDPOLICYREQUEST._serialized_start=456
  _CREATEFORWARDPOLICYREQUEST._serialized_end=533
  _DELETEFORWARDPOLICYREQUEST._serialized_start=535
  _DELETEFORWARDPOLICYREQUEST._serialized_end=580
  _LINKMODESETREQUEST._serialized_start=582
  _LINKMODESETREQUEST._serialized_end=633
  _IPV4CONFIGSETREQUEST._serialized_start=635
  _IPV4CONFIGSETREQUEST._serialized_end=708
  _MIRRORINGPORTREQUEST._serialized_start=710
  _MIRRORINGPORTREQUEST._serialized_end=786
  _UNDOMIRRORINGPORTREQUEST._serialized_start=788
  _UNDOMIRRORINGPORTREQUEST._serialized_end=831
  _IPV6CONFIGSETREQUEST._serialized_start=833
  _IPV6CONFIGSETREQUEST._serialized_end=909
  _PORTSERVICE._serialized_start=912
  _PORTSERVICE._serialized_end=1533
# @@protoc_insertion_point(module_scope)
