# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import port_pb2 as port__pb2


class PortServiceStub(object):
    """The acl service definition.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetPorts = channel.unary_unary(
                '/port.PortService/GetPorts',
                request_serializer=port__pb2.PortRequest.SerializeToString,
                response_deserializer=port__pb2.PortResponse.FromString,
                )
        self.GetPortLinks = channel.unary_unary(
                '/port.PortService/GetPortLinks',
                request_serializer=port__pb2.PortLinkRequest.SerializeToString,
                response_deserializer=port__pb2.PortLinkResponse.FromString,
                )
        self.CreateForwardPolicy = channel.unary_unary(
                '/port.PortService/CreateForwardPolicy',
                request_serializer=port__pb2.CreateForwardPolicyRequest.SerializeToString,
                response_deserializer=port__pb2.ReplyHeader.FromString,
                )
        self.DeleteForwardPolicy = channel.unary_unary(
                '/port.PortService/DeleteForwardPolicy',
                request_serializer=port__pb2.DeleteForwardPolicyRequest.SerializeToString,
                response_deserializer=port__pb2.ReplyHeader.FromString,
                )
        self.LinkModeSet = channel.unary_unary(
                '/port.PortService/LinkModeSet',
                request_serializer=port__pb2.LinkModeSetRequest.SerializeToString,
                response_deserializer=port__pb2.ReplyHeader.FromString,
                )
        self.IPv4ConfigSet = channel.unary_unary(
                '/port.PortService/IPv4ConfigSet',
                request_serializer=port__pb2.IPv4ConfigSetRequest.SerializeToString,
                response_deserializer=port__pb2.ReplyHeader.FromString,
                )
        self.MirroringPort = channel.unary_unary(
                '/port.PortService/MirroringPort',
                request_serializer=port__pb2.MirroringPortRequest.SerializeToString,
                response_deserializer=port__pb2.ReplyHeader.FromString,
                )
        self.UndoMirroringPort = channel.unary_unary(
                '/port.PortService/UndoMirroringPort',
                request_serializer=port__pb2.UndoMirroringPortRequest.SerializeToString,
                response_deserializer=port__pb2.ReplyHeader.FromString,
                )
        self.IPv6ConfigSet = channel.unary_unary(
                '/port.PortService/IPv6ConfigSet',
                request_serializer=port__pb2.IPv6ConfigSetRequest.SerializeToString,
                response_deserializer=port__pb2.ReplyHeader.FromString,
                )


class PortServiceServicer(object):
    """The acl service definition.
    """

    def GetPorts(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPortLinks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateForwardPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteForwardPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LinkModeSet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IPv4ConfigSet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MirroringPort(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UndoMirroringPort(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IPv6ConfigSet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PortServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetPorts': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPorts,
                    request_deserializer=port__pb2.PortRequest.FromString,
                    response_serializer=port__pb2.PortResponse.SerializeToString,
            ),
            'GetPortLinks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPortLinks,
                    request_deserializer=port__pb2.PortLinkRequest.FromString,
                    response_serializer=port__pb2.PortLinkResponse.SerializeToString,
            ),
            'CreateForwardPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateForwardPolicy,
                    request_deserializer=port__pb2.CreateForwardPolicyRequest.FromString,
                    response_serializer=port__pb2.ReplyHeader.SerializeToString,
            ),
            'DeleteForwardPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteForwardPolicy,
                    request_deserializer=port__pb2.DeleteForwardPolicyRequest.FromString,
                    response_serializer=port__pb2.ReplyHeader.SerializeToString,
            ),
            'LinkModeSet': grpc.unary_unary_rpc_method_handler(
                    servicer.LinkModeSet,
                    request_deserializer=port__pb2.LinkModeSetRequest.FromString,
                    response_serializer=port__pb2.ReplyHeader.SerializeToString,
            ),
            'IPv4ConfigSet': grpc.unary_unary_rpc_method_handler(
                    servicer.IPv4ConfigSet,
                    request_deserializer=port__pb2.IPv4ConfigSetRequest.FromString,
                    response_serializer=port__pb2.ReplyHeader.SerializeToString,
            ),
            'MirroringPort': grpc.unary_unary_rpc_method_handler(
                    servicer.MirroringPort,
                    request_deserializer=port__pb2.MirroringPortRequest.FromString,
                    response_serializer=port__pb2.ReplyHeader.SerializeToString,
            ),
            'UndoMirroringPort': grpc.unary_unary_rpc_method_handler(
                    servicer.UndoMirroringPort,
                    request_deserializer=port__pb2.UndoMirroringPortRequest.FromString,
                    response_serializer=port__pb2.ReplyHeader.SerializeToString,
            ),
            'IPv6ConfigSet': grpc.unary_unary_rpc_method_handler(
                    servicer.IPv6ConfigSet,
                    request_deserializer=port__pb2.IPv6ConfigSetRequest.FromString,
                    response_serializer=port__pb2.ReplyHeader.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'port.PortService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class PortService(object):
    """The acl service definition.
    """

    @staticmethod
    def GetPorts(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/GetPorts',
            port__pb2.PortRequest.SerializeToString,
            port__pb2.PortResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPortLinks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/GetPortLinks',
            port__pb2.PortLinkRequest.SerializeToString,
            port__pb2.PortLinkResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CreateForwardPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/CreateForwardPolicy',
            port__pb2.CreateForwardPolicyRequest.SerializeToString,
            port__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteForwardPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/DeleteForwardPolicy',
            port__pb2.DeleteForwardPolicyRequest.SerializeToString,
            port__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LinkModeSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/LinkModeSet',
            port__pb2.LinkModeSetRequest.SerializeToString,
            port__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IPv4ConfigSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/IPv4ConfigSet',
            port__pb2.IPv4ConfigSetRequest.SerializeToString,
            port__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def MirroringPort(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/MirroringPort',
            port__pb2.MirroringPortRequest.SerializeToString,
            port__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UndoMirroringPort(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/UndoMirroringPort',
            port__pb2.UndoMirroringPortRequest.SerializeToString,
            port__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IPv6ConfigSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/port.PortService/IPv6ConfigSet',
            port__pb2.IPv6ConfigSetRequest.SerializeToString,
            port__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
