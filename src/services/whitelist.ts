import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export const getWhiteData = (data: object) => {
    return request(`whitelist${parseQueryString(data)}`, {
        method: 'get',
        data
    })
}

export const delWhiteData = (data: any) => {
    return request('whitelist', {
        method: 'delete',
        data
    })
}
export const addWhite = (data: object)=>{
    return request('whitelist',{
        method:'post',
        data
    })
}

export const editWhite = (data: object)=>{
    return request('whitelist',{
        method:'put',
        data
    })
}