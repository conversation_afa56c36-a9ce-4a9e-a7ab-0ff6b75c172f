# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : detector_factory.py
# @Software: PyCharm

"""异常检测器工厂类"""

from typing import Dict, Any, Optional, Tuple
from .base_detector import BaseDetector
from .time_series_detector import TimeSeriesDetector
from .baseline_detector import BaselineDetector
from .repeat_connection_detector import RepeatConnectionDetector
from .long_connection_detector import LongConnectionDetector
from .geo_anomaly_detector import GeoAnomalyDetector
from utils.logger import get_ndr_logger
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class DetectorFactory:
    """异常检测器工厂类"""
    
    # 检测器映射
    DETECTOR_MAP = {
        'time_series': TimeSeriesDetector,
        'baseline': BaselineDetector,
        'repeat_conn': RepeatConnectionDetector,
        'long_conn': LongConnectionDetector,
        'geo_anomaly': GeoAnomalyDetector
    }
    
    @classmethod
    def create_detector(cls, task_config: Dict[str, Any]) -> Optional[BaseDetector]:
        """
        创建异常检测器
        
        Args:
            task_config: 任务配置
            
        Returns:
            检测器实例或None
        """
        try:
            model_type = task_config.get('model_type')
            if not model_type:
                LOG.error("任务配置中缺少model_type")
                return None
            
            detector_class = cls.DETECTOR_MAP.get(model_type)
            if not detector_class:
                LOG.error(f"不支持的检测模型类型: {model_type}")
                return None
            
            # 创建检测器实例
            detector = detector_class(task_config)
            LOG.info(f"成功创建检测器: {model_type}")
            
            return detector
            
        except Exception as e:
            LOG.error(f"创建检测器失败: {str(e)}")
            return None
    
    @classmethod
    def get_supported_models(cls) -> Dict[str, str]:
        """获取支持的检测模型列表"""
        return {
            'time_series': '时间序列趋势模型 - 基于Z-score的统计异常检测',
            'baseline': '基线偏移检测 - 长周期行为基线对比分析',
            'repeat_conn': '重复连接检测 - 异常重复连接行为识别',
            'long_conn': '长连接分析 - 异常时长连接检测',
            'geo_anomaly': '地理/归属异常 - IP地理位置和ASN变化检测'
        }
    
    @classmethod
    def validate_task_config(cls, task_config: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证任务配置
        
        Args:
            task_config: 任务配置
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 检查必需字段
            required_fields = ['model_type', 'target_type', 'target_value']
            for field in required_fields:
                if field not in task_config:
                    return False, f"缺少必需字段: {field}"
            
            # 检查模型类型
            model_type = task_config['model_type']
            if model_type not in cls.DETECTOR_MAP:
                return False, f"不支持的模型类型: {model_type}"
            
            # 检查目标类型
            target_type = task_config['target_type']
            if target_type not in ['ip', 'domain']:
                return False, f"不支持的目标类型: {target_type}"
            
            # 检查目标值
            target_value = task_config['target_value']
            if not target_value or not isinstance(target_value, str):
                return False, "目标值不能为空"
            
            # 模型特定验证
            validation_result = cls._validate_model_specific_config(task_config)
            if not validation_result[0]:
                return validation_result
            
            return True, ""
            
        except Exception as e:
            return False, f"配置验证失败: {str(e)}"
    
    @classmethod
    def _validate_model_specific_config(cls, task_config: Dict[str, Any]) -> Tuple[bool, str]:
        """验证模型特定配置"""
        model_type = task_config['model_type']
        detection_config = task_config.get('detection_config', {})
        
        try:
            if model_type == 'time_series':
                # 时间序列模型验证
                z_threshold = detection_config.get('z_score_threshold', 3.0)
                if not isinstance(z_threshold, (int, float)) or z_threshold <= 0:
                    return False, "z_score_threshold必须是正数"
                
                time_window = detection_config.get('time_window_hours', 1)
                if not isinstance(time_window, int) or time_window <= 0:
                    return False, "time_window_hours必须是正整数"
            
            elif model_type == 'baseline':
                # 基线检测模型验证
                baseline_periods = detection_config.get('baseline_periods', [7, 30, 90])
                if not isinstance(baseline_periods, list) or not baseline_periods:
                    return False, "baseline_periods必须是非空列表"
                
                deviation_threshold = detection_config.get('deviation_threshold', 3.0)
                if not isinstance(deviation_threshold, (int, float)) or deviation_threshold <= 1:
                    return False, "deviation_threshold必须大于1"
            
            elif model_type == 'repeat_conn':
                # 重复连接模型验证
                frequency_threshold = detection_config.get('frequency_threshold', 100)
                if not isinstance(frequency_threshold, int) or frequency_threshold <= 0:
                    return False, "frequency_threshold必须是正整数"
                
                time_window = detection_config.get('time_window_minutes', 60)
                if not isinstance(time_window, int) or time_window <= 0:
                    return False, "time_window_minutes必须是正整数"
            
            elif model_type == 'long_conn':
                # 长连接模型验证
                duration_thresholds = detection_config.get('duration_thresholds', {})
                if duration_thresholds and not isinstance(duration_thresholds, dict):
                    return False, "duration_thresholds必须是字典类型"
            
            elif model_type == 'geo_anomaly':
                # 地理异常模型验证
                if task_config['target_type'] != 'ip':
                    return False, "地理异常检测只支持IP目标类型"
                
                baseline_days = detection_config.get('baseline_days', 30)
                if not isinstance(baseline_days, int) or baseline_days <= 0:
                    return False, "baseline_days必须是正整数"
            
            return True, ""
            
        except Exception as e:
            return False, f"模型配置验证失败: {str(e)}"
    
    @classmethod
    def get_default_config(cls, model_type: str) -> Dict[str, Any]:
        """获取模型默认配置"""
        default_configs = {
            'time_series': {
                'z_score_threshold': 3.0,
                'time_window_hours': 1,
                'baseline_days': 7,
                'metric_type': 'bytes'
            },
            'baseline': {
                'baseline_periods': [7, 30, 90],
                'deviation_threshold': 3.0,
                'min_baseline_samples': 5,
                'metric_types': ['connections', 'bytes', 'unique_ips']
            },
            'repeat_conn': {
                'frequency_threshold': 100,
                'time_window_minutes': 60,
                'unique_threshold': 10,
                'pattern_types': ['ip_to_domain', 'ip_to_ip', 'port_scan']
            },
            'long_conn': {
                'duration_thresholds': {
                    'http': 600,
                    'https': 1800,
                    'tcp': 3600,
                    'dns': 30,
                    'default': 1800
                },
                'min_bytes_threshold': 1024,
                'percentile_threshold': 95
            },
            'geo_anomaly': {
                'baseline_days': 30,
                'min_baseline_records': 10,
                'geo_change_threshold': 0.8,
                'asn_change_threshold': 0.7,
                'suspicious_countries': ['RU', 'CN', 'KP', 'IR', 'SY']
            }
        }
        
        return default_configs.get(model_type, {})
