import React, { useEffect, useState } from 'react';
import cs from 'classnames';
import { Cascader } from 'antd';

import styleModule from './index.less';

export type Props = {
  value?: any[];
  onChange?: (values: string[]) => void
  style?: React.CSSProperties;
  loading?: boolean;
  className?: string;
  options: any[];
}

function ThreatNamesSelector({
  style,
  className,
  value,
  options = [],
  loading,
  onChange,
}: Props): JSX.Element {
  const [cascaderValue, setValue] = useState<any[]>([]);

  useEffect(() => {
    const _value: any[] = [];
    value?.forEach((childValue: string) => {
      for (const option of options) {
        if (option.children) {
          const childOption = option.children.find((child: any) => child.value === childValue);
          if (childOption) {
            _value.push([option.value, childValue]);
          }
        }
      }
    });

    const backfillCascaderValue = _value.map((item) => {
      for (const option of options) {
        if (option.children && item[0] === option.value) {
          if (item.length >= option.children.length + 1) {
            return [item[0]]
          }
          return item
        }
      }
    })

    setValue(backfillCascaderValue)
  }, [value])

  const handleChange = (values: any[]) => {
    const _values = values.map((item: any[]) => {
      if (item.length === 1) {
        return item[0].split(',');
      }
      const [parent, ...children] = item
      const allNames = parent.split(',')
      if (allNames.length === children.length) {
        return allNames.split(',')
      }

      return children;
    });

    onChange?.(_values.flat()?.join()?.split(','));
  }

  return (
    <Cascader
      dropdownClassName={styleModule.threat_name_dropdown}
      className={cs('self-center', className)}
      multiple
      allowClear
      loading={loading}
      style={style}
      value={cascaderValue}
      maxTagCount="responsive"
      options={options}
      onChange={handleChange}
    />
  );
}

export default ThreatNamesSelector;
