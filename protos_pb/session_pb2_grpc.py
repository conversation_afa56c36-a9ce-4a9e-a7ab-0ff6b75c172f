# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
import session_pb2 as session__pb2


class SessionStub(object):
    """The session service definition.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetStatSetting = channel.unary_unary(
                '/session.Session/SetStatSetting',
                request_serializer=session__pb2.StatSetting.SerializeToString,
                response_deserializer=session__pb2.ReplyHeader.FromString,
                )
        self.GetStatSetting = channel.unary_unary(
                '/session.Session/GetStatSetting',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=session__pb2.StatSettingReply.FromString,
                )
        self.GetStats = channel.unary_unary(
                '/session.Session/GetStats',
                request_serializer=session__pb2.StatsRequest.SerializeToString,
                response_deserializer=session__pb2.StatsReply.FromString,
                )
        self.ResetStats = channel.unary_unary(
                '/session.Session/ResetStats',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ListSessions = channel.unary_unary(
                '/session.Session/ListSessions',
                request_serializer=session__pb2.SessionsRequest.SerializeToString,
                response_deserializer=session__pb2.SessionsReply.FromString,
                )
        self.GetSession = channel.unary_unary(
                '/session.Session/GetSession',
                request_serializer=session__pb2.SessionRequest.SerializeToString,
                response_deserializer=session__pb2.SessionReply.FromString,
                )
        self.ResetSessions = channel.unary_unary(
                '/session.Session/ResetSessions',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ListRelations = channel.unary_unary(
                '/session.Session/ListRelations',
                request_serializer=session__pb2.RelationsRequest.SerializeToString,
                response_deserializer=session__pb2.RelationsReply.FromString,
                )
        self.GetRelation = channel.unary_unary(
                '/session.Session/GetRelation',
                request_serializer=session__pb2.RelationRequest.SerializeToString,
                response_deserializer=session__pb2.RelationReply.FromString,
                )
        self.ResetRelations = channel.unary_unary(
                '/session.Session/ResetRelations',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )


class SessionServicer(object):
    """The session service definition.
    """

    def SetStatSetting(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStatSetting(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStats(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetStats(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListSessions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSession(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetSessions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListRelations(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRelation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetRelations(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SessionServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetStatSetting': grpc.unary_unary_rpc_method_handler(
                    servicer.SetStatSetting,
                    request_deserializer=session__pb2.StatSetting.FromString,
                    response_serializer=session__pb2.ReplyHeader.SerializeToString,
            ),
            'GetStatSetting': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStatSetting,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=session__pb2.StatSettingReply.SerializeToString,
            ),
            'GetStats': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStats,
                    request_deserializer=session__pb2.StatsRequest.FromString,
                    response_serializer=session__pb2.StatsReply.SerializeToString,
            ),
            'ResetStats': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetStats,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ListSessions': grpc.unary_unary_rpc_method_handler(
                    servicer.ListSessions,
                    request_deserializer=session__pb2.SessionsRequest.FromString,
                    response_serializer=session__pb2.SessionsReply.SerializeToString,
            ),
            'GetSession': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSession,
                    request_deserializer=session__pb2.SessionRequest.FromString,
                    response_serializer=session__pb2.SessionReply.SerializeToString,
            ),
            'ResetSessions': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetSessions,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ListRelations': grpc.unary_unary_rpc_method_handler(
                    servicer.ListRelations,
                    request_deserializer=session__pb2.RelationsRequest.FromString,
                    response_serializer=session__pb2.RelationsReply.SerializeToString,
            ),
            'GetRelation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRelation,
                    request_deserializer=session__pb2.RelationRequest.FromString,
                    response_serializer=session__pb2.RelationReply.SerializeToString,
            ),
            'ResetRelations': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetRelations,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'session.Session', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Session(object):
    """The session service definition.
    """

    @staticmethod
    def SetStatSetting(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/SetStatSetting',
            session__pb2.StatSetting.SerializeToString,
            session__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetStatSetting(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/GetStatSetting',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            session__pb2.StatSettingReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/GetStats',
            session__pb2.StatsRequest.SerializeToString,
            session__pb2.StatsReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ResetStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/ResetStats',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListSessions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/ListSessions',
            session__pb2.SessionsRequest.SerializeToString,
            session__pb2.SessionsReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSession(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/GetSession',
            session__pb2.SessionRequest.SerializeToString,
            session__pb2.SessionReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ResetSessions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/ResetSessions',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListRelations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/ListRelations',
            session__pb2.RelationsRequest.SerializeToString,
            session__pb2.RelationsReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRelation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/GetRelation',
            session__pb2.RelationRequest.SerializeToString,
            session__pb2.RelationReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ResetRelations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/session.Session/ResetRelations',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
