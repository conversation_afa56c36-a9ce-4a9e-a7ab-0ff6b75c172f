import React, { useState } from "react";
import cs from 'classnames';
import { Checkbox, Select } from "antd";
import { CheckboxChangeEvent } from "antd/lib/checkbox";

import { riskDetailOptions } from "@/utils/enumList";

import style from './index.less'

interface CheckboxWithSelectProps {
  label?: string;
  value?: number[];
  onChange?: (value: string) => void;
}

// 带下拉选择框的Checkbox
const CheckboxWithSelect = ({ label, value, onChange }: CheckboxWithSelectProps) => {
  const [isChecked, setIsChecked] = useState<boolean>(!!value?.length);
  const [selectedValues, setSelectedValues] = useState<number[]>(value || []);
  const handleCheckboxChange = (e: CheckboxChangeEvent) => {
    setIsChecked(e.target.checked);
    if (!e.target.checked) {
      setSelectedValues([]);
      onChange?.('');
    }
  };

  const handleSelectChange = (value: number[]) => {
    setSelectedValues(value);
    onChange?.(value.toString());
  };

  return (
    <div className={cs('flex item-center gap-5', style.checkbox_select_row_height)}>
      <Checkbox
        className={cs('self-center', style.checkbox_selector_label_width)}
        onChange={handleCheckboxChange}
        checked={isChecked}
      >
        {label}
      </Checkbox>
      {
        isChecked && (
          <Select
            className='self-center'
            mode="multiple"
            style={{ width: '200px' }}
            maxTagCount={1}
            onChange={handleSelectChange}
            value={selectedValues}
            options={riskDetailOptions}
          />
        )
      }
    </div>


  )
}

export default CheckboxWithSelect