/*
 * @Author: 田浩
 * @Date: 2021-09-15 11:43:08
 * @LastEditors: tianh
 * @LastEditTime: 2021-11-24 11:15:00
 * @Descripttion:
 */
import React, { Fragment } from 'react';
import { Form, Input, Select, Button } from 'antd';
import { FlowType } from '@/utils/enumList';
const { Option } = Select;

const AddFlowList = () => {
  const [form] = Form.useForm();
  const [detail, setDetail] = React.useState([
    {
      tag: '',
      target: '',
      type: '',
    },
  ]);

  const formData = ['tag', 'target', 'type'];

  const deleteOne = (i: any) => {
    const lists = form.getFieldsValue();
    for (let index in lists) {
      const nIndex = Number(index);
      if (nIndex >= i && lists[nIndex + 1]) {
        formData.map(item =>
          form.setFieldsValue({
            [`${nIndex}.${item}`]: lists[nIndex + 1][item],
          }),
        );
      }
    }
    setDetail([...detail.slice(0, i), ...detail.slice(i, detail.length - 1)]);
  };

  const newItem = () => {
    setDetail([
      ...detail,
      {
        tag: '',
        target: '',
        type: '',
      },
    ]);
  };

  return (
    <Fragment>
      <Form form={form} layout="inline">
        {detail.map((item, index) => (
          <div key={index}>
            <Form.Item
              label="值"
              initialValue={item[formData[0]]}
              name={`${index}.${formData[0]}`}
              rules={[{ required: true, message: '请填写' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="类型"
              rules={[{ required: true, message: '请选择' }]}
              name={`${index}.${formData[1]}`}
              initialValue={item[formData[1]]}
            >
              <Select>
                {FlowType.map(item => {
                  return (
                    <Option value={item.value} key={item.key}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item
              label="备注"
              name={`${index}.${formData[2]}`}
              rules={[{ required: true, message: '请填写' }]}
              initialValue={item[formData[2]]}
            >
              <Input />
            </Form.Item>
            <Form.Item>
              {index === 0 ? (
                <Button type="primary" onClick={newItem}>
                  增加
                </Button>
              ) : (
                <Button type="primary" onClick={() => deleteOne(index)}>
                  删除
                </Button>
              )}
            </Form.Item>
          </div>
        ))}
      </Form>
    </Fragment>
  );
};

export default AddFlowList;
