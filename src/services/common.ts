import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';
// Select获取国家
export const getCountry = (data: object) => {
  return request(`knowledge/country${parseQueryString(data)}`, {
    method: 'GET',
    data,
  });
}

// Select 威胁等级
export const getThreatLevel = (data: object) => {
  return request(`knowledge/threat_level${parseQueryString(data)}`, {
    method: 'GET',
    data,
  });

}

// form任务名称
export const getTaskName = (data: any) => {
  return request(`knowledge/task`, {
    method: 'get',
    data,
  });
};

// form模型名称
export const getModalName = (data: any) => {
  return request(`knowledge/model_name`, {
    method: 'get',
    data,
  });
};
