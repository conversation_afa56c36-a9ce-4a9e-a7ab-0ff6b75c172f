#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import psutil

from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils import param_check
from flask import request
from utils.es_function import get_range
from utils.es_function import get_country_term
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from config.config import RULE_INDEX
# from config.config import NIC
from utils.best_show_size import size_format
from api_1_0.dpilog.dpilog import DpilogResource


ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class AllFlowSum(NdrResource):
    '''网卡流量汇总'''

    field_template = {
        "occurred": "occurredTime",
        "observed": "observedTime"
    }

    def __init__(self):
        super(AllFlowSum, self).__init__(es_template="all_flow")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        # 开始时间&结束时间校验
        response = self.time_verify(**kwargs)
        if response is not True:
            return response

        start_time = kwargs["startTime"]
        stop_time = kwargs["stopTime"]
        time_mode = kwargs["timeMode"]
        time_mode = self.field_template[time_mode]

        # 应从clickhouse中统计数据
        ch_client = DpilogResource()
        sql = "select sum(orig_ip_bytes) as recv, sum(resp_ip_bytes) as send from dpilog_conn " \
              "where ts between %s and %s" % (start_time[:-3], stop_time[:-3])

        msg, ch_data = ch_client.get_val_from_database(sql)
        if not ch_data:
            send = 0
            recv = 0
        else:
            send = ch_data[0]['send']
            recv = ch_data[0]['recv']

        all_flow = send + recv
        show_send = size_format(send, "B")
        show_recv = size_format(recv, "B")
        show_all = size_format(all_flow, "B")

        # 从es中获取threatFlow
        ranges = get_range(time_mode, start_time, stop_time)
        self.es_template["query"]["bool"]["must"].append(ranges)

        try:
            res = self.es_client.search(index=RULE_INDEX, body=self.es_template)
        except Exception as e:
            err_info = "error: " + str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(err_info, False, [])

        try:
            data = self.data_format(res, show_send, show_recv, show_all)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [])

    @staticmethod
    def data_format(data, show_send, show_recv, show_all):

        aggs_data = data["aggregations"]
        total_flow = aggs_data["total_flow"]["value"]
        result = {
            "threatFlow": size_format(total_flow),
            "incomeFlow": show_recv,
            "outgoFlow": show_send,
            "totalFlow": show_all
        }

        return result
