/*
 * @Author: 田浩
 * @Date: 2021-07-28 11:12:30
 * @LastEditors: tianh
 * @LastEditTime: 2022-05-10 10:59:53
 * @Descripttion:
 */
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export const hardware = (data:any) => {
  return request(`monitor/cpu${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const memory = (data:any) => {
  return request(`monitor/mem${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const disk = (data:any) => {
  return request(`monitor/disk${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const service = () => {
  return request(`monitor/service`, {
    method: 'get',
  });
};

export const handleStatus = (data: any) => {
  return request(`monitor/service`, {
    method: 'put',
    data,
  });
};
export const getNetworkFlow = () => {
  return request(`monitor/interface`, {
    method: 'get',
  });
};

export const get_traffic_output = () => {
  return request('monitor/traffic_output', { method: 'get' });
};

export const get_new_conn_speed = () => {
  return request('monitor/new_conn_speed', {
    method: 'get',
  });
};
export const flowEvent = () => {
  return request(`monitor/traffic_event`, {
    method: 'get',
  });
};

export const new_conn_top = () => {
  return request(`monitor/new_conn_top`, {
    method: 'get',
  });
};

export const appflow = (data: any) => {
  return request(`monitor/application_traffic`, {
    method: 'post',
    data,
  });
};

export const createData = () => {
  return request(`monitor/create_conn_data`, {
    method: 'get',
  });
};

export const getSettings = () => {
  return request(`monitor/mail_config`, {
    method: 'get',
  });
};
export const save_settings = (data: any) => {
  return request('monitor/mail_config', {
    method: 'post',
    data,
  });
};

export const getNodes = (data:any)=>{
  return request(`cluster/nodes${parseQueryString(data)}`)
}