{
    "type": "object",
    "required": ["startTime", "stopTime"],
    "properties": {
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}