# -*- coding: utf-8 -*-
# @Time    : 2019-04-29 15:54
# <AUTHOR> hachi
# @File    : authentication.py
# @Software: PyCharm

RULE_INDEX = "rule-eve"
IOC_INDEX = "ioc-eve"
MODEL_INDEX = "model-eve"
FILE_INDEX = "file-eve"
HOST_IP = "ndr-host"
PACKAGE_DIR = "/tmp/"
# 页面数据(PCAP)包下载的最大值:500MB, 超过会截断.
PACKAGE_SIZE_MAX = 524288000
# 通过swtich脚本切换
NIC = "eno1"


class FlaskConfig:
    IP = '0.0.0.0'
    PORT = 7001
    HOST = HOST_IP
    DEBUG = True


class MongoDBConfig:
    MONGO_HOST = 'mongo'
    MONGO_PORT = 27017
    # MONGO_USERNAME = "ndradmin"
    MONGO_USERNAME = "ksbox"
    # MONGO_PASSWORD = "1qaz@WSXndr"
    MONGO_PASSWORD = "FBgxC5PWAre"
    MONGO_AuthSource = 'admin'
    MONGO_MIN_LIMIT = 100
    MONGO_NIGHT_LIMIT = 2000


class ClickHouseConfig:
    CH_HOST = 'clickhouse'
    CH_PORT = 8123
    CH_USER = 'default'
    CH_PASSWORD = 'ndr@ck++'
    CH_DATABASE = 'ndr'
    CH_ALL_PROTO = [
        'conn', 'http', 'dns', 'ftp', 'mail', 'ssl', 'files', 'icmp', 'mysql', 'login', 'dhcp', 'telnet', 'nfs',
        'modbus', 'snmp', 'tftp', 'netbios', 'rip', 'igmp', 'smb', 'mssql'
    ]
    CH_ALL_TABLES = [
        'dpilog_' + i for i in CH_ALL_PROTO
    ]


class BackStreamConfig:
    BackStream_HOST = HOST_IP
    BackStream_PORT = 8005
    BackUsername = 'ndr'
    BackPassword = '1qaz@WSXndr'


class ElasticSearchConfig:
    ElasticSearch_HOST = 'elasticsearch'
    ElasticSearch_PORT = 9200
    ES_CONN_TIMEOUT = 60000
    ElasticSearch_USER = 'elastic'
    ElasticSearch_PASS = 'ndr@elastic++'


class RedisConfig:
    Host = 'redis'
    Port = 6379
    Password = 'QaHJ7SR4uY'


class SharkdConfig:
    Host = 'sharkd'
    Port = 11180


class SystemFeaturePath:
    RULES_PATH = "/opt/feature/"
    HDP_DEFAULT_FEATURE = RULES_PATH + 'hdp_default.feature'
    HDP_DEFAULT_SIGPACK_PATH = '/opt/hdp/config/ips-sigpack-en.dat'
    USER_DB = "/opt/feature/db/knowledge_feature.db"
    VUL_TABLE = "vul_rules"
    DEFAULT_USER = "admin"
    DEFAULT_RULE_GROUP_EN = {
        "default_allRules": "00000001",
        "default_apt_attack": "00000002",
        "default_exploit_attack": "00000003",
        "default_remote_ctrl": "00000004",
        "default_malware": "00000005"
    }
    DEFAULT_RULE_GROUP_CN = {
        "default_allRules": "包含系统全部规则",
        "default_exploit_attack": "漏洞利用相关规则",
        "default_remote_ctrl": "远程控制相关规则",
        "default_apt_attack": "APT攻击相关规则",
        "default_malware": "恶意软件相关规则"
    }


class ReplayFeaturePath:
    RULES_PATH = "/opt/feature/replay/"
    RULES_PATH_HDP = "/opt/feature/replay/hdp_"


class LogPath:
    Log_PATH = "/var/log/ndr"


class PcapPath:
    Pcap_Path = "/data/moloch/raw/"


class DetectProg:
    Hdp_Prog = "sudo /opt/hdp/start.sh"
    Hdp_SigCompile = "python37 /opt/hdp/tools/ips_sig_pack.py"
    TcpReplay = "/usr/share/tcpreplay"


class KnowledgeNDR:
    TempPath = "/opt/feature/db/rule_temp/"
    Path = "/opt/feature/db/"
    ZipPwd = "KNDR_ti-ioc!x0%#1qaz@WSX".encode()


class Spark:
    ReplayPath = "/opt/bdp/spark_model_replay.sh"
    RealtimePath = "/opt/bdp/spark_model_stream.sh"
    TempPath = "/opt/bdp/temp.sh"


class SupervisorConfig:
    User = "ndr"
    Password = "1qaz@WSXndr"


class UserConfig:
    Password = 'pbkdf2:sha256:150000$Df8rv06n$03006fde0ce5488' \
               '2b089113c193d34152cde12e427061fc3f14aa1ba94f73ef6'
    Secret_Key = 'pbkdf2:sha256:150000$qVMAKsO8$4e2a98d41fcf0' \
                 'e1b009c06a4ebb264f297ea57e7aa86cef51c758378385dcc54'


class CustomizedPcapPath:
    Path = "/var/ndr_datasource/"


class StenoPcapPath:
    Path = "/var/ndr_warning_pcaps/"


class NASConfig:
    Path = "/mnt/nas/"
    PcapPath = '/var/ndr_datasource/tmp'


class FileReport:
    Path = "/var/ndr_file_reports/"


class CommandTimeOut:
    timeout = 720  # 命令超时12min，强制终止防止阻塞


class NdrLog:
    class Type:
        LOGIN = 'login'
        OPERATE = 'operate'
        RUN = 'run'

    ZipPwd = "1qaz@WSXndr"
    ZipPath = '/var/log/'
    OriginalPath = '/var/log/ndr/'
    DownloadPath = ZipPath + 'download_log/'
    ESLogPath = ''
    MongodbLogPath = ''


class FtpPcapPath:
    Path = "/tmp/ndr_ftp_pcap_cache/"


class StenographerConfig:
    url = 'https://127.0.0.1:1234/query'
    instance = {
        'key': '/opt/stenographer/certs/client_key.pem',
        'cert': '/opt/stenographer/certs/client_cert.pem',
        'ca': '/opt/stenographer/certs/ca_cert.pem'
    }
    port = {
        "tap0": "2234",
        "tap1": "3234",
        "tap2": "4234",
        "tap3": "5234",
        "tap4": "6234",
        "tap5": "7234",
        "tap6": "8234",
        "tap7": "9234",
    }


class IocCfg:
    TempPath = "/opt/feature/db/ioc_temp/"
    AllDbPath = "/opt/feature/db/KNDR_IOC.db"
    ImportExcelPath = "/opt/feature/db/"
    DefaultDbPath = "/opt/feature/db/KNDR_IOC_default.db"
    DefaultDbPathBK = "/opt/feature/db/KNDR_IOC_default.db.bk"
    ZipPwd = "KNDR_ti-ioc!x0%#1qaz@WSX"
    # DefaultZIPPath = "/opt/feature/db/ioc.zip"
    # DefaultZIPPathBK = "/opt/feature/db/ioc.zip.bk"


class WhiteListCfg:
    DbPath = "/opt/feature/db/white_list.db"
    ImportExcelPath = "/opt/feature/db/excel/"
    WhiteRulePath = "/opt/feature/whitelist.rule"
    WhiteListDefaultPath = "/opt/feature/whitelist_default.feature"
    Hdp_WhiteSigPath = '/opt/hdp/config/white-sigpack-en.dat'
    Hdp_WhiteCompile = "python37 /opt/hdp/tools/white_list_sig_pack.py"


class MbCfg:
    DbPath = "/opt/feature/db/customized_mb.db"
    ImportExcelPath = "/opt/feature/db/excel/"
    DefaultDbPath = "/opt/feature/db/customized_mb_default.db"
    DefaultDbPathBK = "/opt/feature/db/customized_mb_default.db.bk"
    DefaultZIPPath = "/opt/feature/db/mb.zip"
    DefaultZIPPathBK = "/opt/feature/db/mb.zip.bk"


class GrpcCfg:
    RemoteAddr = '**********:50051'


class FileRestoreCfg:
    COLLECTION = 'file_restore_config'

    APPID_MAP = {
        "HTTP": 31,
        "FTP": 10,
        "SMB": 45,
        "NFS": 52,
        "SMTP": 15,
        "POP3": 16,
        "IMAP": 17,
        "TFTP": 12
    }

    TYPEID_MAP = {
        'office': [3, 4, 5, 6, 7, 8],
        "pdf": [9],
        "rtf": [11],
        "pe": [1],
        "elf": [2],
        "zip": [18],
        "gzip": [17],
        "7z": [16],
        "bz2": [21],
        "rar": [22],
        "tar": [20],
        "xz": [19],
        "jsp": [25],
        "asp": [24],
        "php": [26],
        "shell": [27],
        "python": [28],
        "flash": [35],
        "gif": [32],
        "bpg": [33],
        "png": [31],
        "jpeg": [34],
        "apk": [22],
        "java": [22],
        "lnk": [12],
        "html": [29],
        "xml": [30]
    }
