{
	"type": "object",
	"required": ["page"],
	"properties": {
	    "q": {
	        "type": "string",
	        "default": "",
            "pattern": r"^.{0,128}$"
	    },
		"type": {
	        "type": "string",
			"enum": ["login", "operate", "run", ""],
			"default": ""
		},
		"page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}