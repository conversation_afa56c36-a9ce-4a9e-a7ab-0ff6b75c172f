import React from 'react';
import style from './index.less';
const Index = (props: any) => {
  const { start = {
    x: 0,
    y: 0,
  }, line = {
    x: 0,
    y: 0,
  }, end = {
    x: 0,
    y: 0,
  } } = props
  return (
    <svg className={style.container}>
      <path
        className={style.connector}
        d={`M${start.x},${start.y} L${start.x + 15},${start.y} L${line.x},${line.y
          }`}
      />
      <path
        className={style.connector}
        d={`M${end.x + 0.6666666},${end.y + 0.3333333} m-5.5,-5 l5,5 l-5,5 M${end.x
          },${end.y} L${end.x - 15},${end.y} L${end.x - 35},${line.y} L${line.x
          },${line.y}`}
      />
    </svg>
  );
};
export default Index;
