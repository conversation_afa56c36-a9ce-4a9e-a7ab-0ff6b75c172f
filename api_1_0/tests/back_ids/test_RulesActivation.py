#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: test_RulesActivation.py
# @time: 2019/07/16
# @software: PyCharm
import unittest
from api_1_0.router import app, api
import json
from api_1_0.back_rules.rules_status_manager import RulesActivation


class TestRulesActivation(unittest.TestCase):

    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        self.query_string = {
            "enableTime": "enable"
        }

    def tearDown(self):
        pass

    # 缺少必要字段
    def test_lack_of_required_parameters(self):
        self.query_string.pop("enableTime")
        resp_dict = self.post(RulesActivation, self.query_string)
        self.assertEqual(resp_dict["flag"], False)

    # enableTime error
    def test_post_illegal_of_enableTime(self):
        illegal_action = ["", "sadasd", "enbbaa", "1213__swde", "*&^^", "##$$./", "2019-0-1", "", "2019-10-1",
                          "2019-10-34", "2019-10-21 ", " 2019-10-21", "2019 -10-21"]
        for line in illegal_action:
            self.query_string["enableTime"] = line
            resp_dict = self.put(RulesActivation, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    def post(self, url, data):
        response = self.client.post(api.url_for(url), json=data)
        resp_dict = json.loads(response.data)
        return resp_dict

    def get(self, url):
        response = self.client.get(api.url_for(url))
        resp_dict = json.loads(response.data)
        return resp_dict

    def put(self, url, data):
        response = self.client.put(api.url_for(url, taskId="1111111"), json=data)
        resp_dict = json.loads(response.data)
        return resp_dict


if __name__ == '__main__':
    suite = unittest.TestSuite()
    suite.addTest()
    # 执行测试
    runner = unittest.TextTestRunner()
    runner.run(suite)
