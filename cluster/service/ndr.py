# 创建一个自定义集群服务
from cluster.service.service import KSPService
from cluster.utils.file import is_safe_path
import logging
import subprocess

from api_1_0.hdp_grpc.die import *
from cluster.service.utils import run_command, file_hash

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s -%(levelname)s - %(funcName)s [%(lineno)d]: %(message)s',
    datefmt='%m-%d %H:%M:%S'
)
logger = logging.getLogger("myclusterlogger")

# 继承 KSPService 类
class MyClusterService(KSPService):

    # 定义一个新的远程操作，接收对端发来的 text，显示并回传
    def exposed_echo(self, text):
        logger.info('Received echo request.')
        print(text)
        logger.info('Sent echo reply.')
        return 'echoed: ' + text

    def exposed_mv_file(self, src_file_path, dst_file_path):
        logger.info('Received mv file request.')
        if not is_safe_path(dst_file_path):
            return False

        cmd = ['sudo', 'mv', src_file_path, dst_file_path]
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))
            return False
        return True

    def exposed_file_hash(self, filename):
        logger.info('Received file hash request.')
        value = file_hash(filename)
        return value

    def exposed_request_pcap(self, query_str, file_path, url_list):
        logger.info('Received pcap request.')

        from api_1_0.back_event.get_package import request_pcap
        request_pcap(query_str, file_path, url_list)

    def exposed_hdp_restart(self):
        logger.info('Received hdp restart request.')
        cmd = ['sudo', 'supervisorctl', '-u', 'ndr', '-p', '1qaz@WSXndr', 'restart', 'hdp']
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))

        return

    def exposed_hdp_update_portmask(self, port_mask):
        logger.info('Received hdp update port mask request.')
        cmd = ['sed', '-i', 's/^port_mask.*/port_mask = %s/g' % port_mask, '/opt/hdp/config/conf.ini']
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))
            return stdout.decode('utf-8', errors='replace')

        cmd = ['supervisorctl', '-u', 'ndr', '-p', '1qaz@WSXndr', 'restart', 'hdp']
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))

        return stdout.decode('utf-8', errors='replace')

    def exposed_hdp_status(self):
        logger.info('Received hdp status request.')
        cmd = ['sudo', 'supervisorctl', '-u', 'ndr', '-p', '1qaz@WSXndr', 'status', 'hdp']
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))

        return stdout.decode('utf-8', errors='replace')

    def exposed_hdp_set_file_protocol(self, file_app_cfg, file_app_list: list):
        logger.info('Received hdp set file protocol request.')
        ret, msg = run_command('sed -i "s/^file_app.*/file_app = %s/g" /opt/hdp/config/conf.ini' % file_app_cfg)
        if not ret:
            logger.error('Failed to set hdp set file protocol request.')
        return grpc_set_file_protocol(file_app_list)

    def exposed_hdp_set_file_type(self, file_type_cfg):
        logger.info('Received hdp set file type request.')
        ret, msg = run_command('sed -i "s/^file_type.*/file_type = %s/g" /opt/hdp/config/conf.ini' % hex(file_type_cfg))
        if not ret:
            logger.error('Failed to set hdp set file type request.')
        return grpc_set_file_type(file_type_cfg)

    def exposed_hdp_set_file_size(self, min_size, max_size):
        logger.info('Received hdp set file size request.')
        ret, msg = run_command('sed -i "s/^file_size.*/file_size = (%d,%d)/g" /opt/hdp/config/conf.ini' % (min_size, max_size))
        if not ret:
            logger.error('Failed to set hdp set file size request.')
        return grpc_set_file_size(min_size, max_size)

    def exposed_hdp_feature_add(self, rule_path, rule_info_path):
        logger.info('Received hdp feature add request.')
        return grpc_feature_add(rule_path, rule_info_path)

    def exposed_hdp_feature_upgrade(self, sid_list: list):
        logger.info('Received hdp feature upgrade request.')
        return grpc_feature_upgrade(sid_list)

    def exposed_hdp_feature_operate(self, sid_list: list):
        logger.info('Received hdp feature operate request.')
        return grpc_feature_operate(sid_list)

    def exposed_hdp_newssnlog_switch(self, enable):
        logger.info('Received hdp newssnlog switch request.')
        return grpc_newssnlog_switch(enable)

    def exposed_service(self, op, services:list):
        logger.info('Received service request.')
        result = ''
        if not services:
            ret, msg = run_command('docker ps | grep -v Up | grep -v CONTAINER')
            if ret:
                result += msg

            ret, msg = run_command('/diag.sh sv status | grep -v RUNNING | grep -v sandbox')
            if ret:
                result += msg
        else:
            for service in services:
                msg = service_op(op, service)
                result += msg

        return result


def service_op(op, service):
    if service in ['hdp', 'avmgr', 'host_celery', 'sandbox-submit']:
        ret, msg = run_command(f'supervisorctl -u ndr -p 1qaz@WSXndr {op} {service}')
    else:
        ret, msg = run_command(f'docker {op} {service}')

    return msg