/*
 * @Author: tianh
 * @Date: 2022-04-18 11:19:32
 * @LastEditors: tianh
 * @LastEditTime: 2022-05-07 11:02:36
 * @Descripttion:
 */
import React, { useEffect, useState } from 'react';
import { Card, Table, message, Tooltip, Switch } from 'antd';
import style from './style.less';
import {
  get_traffic_output,
  get_new_conn_speed,
  new_conn_top,
  flowEvent,
  appflow,
} from '@/services/monitor';
import moment from 'moment';
import { ellipsis } from '@/utils/utils';
const echarts = require('echarts');
let app_list: never[] = [];
const Detail = (props: any) => {
  let timer: any = null;
  const { getSwitch, isSimple } = props;
  const [flowSpeed, setflowSpeed] = useState<any>({
    min: 0,
    max: 100,
    out_total: 0,
  });
  const [connectSpeed, setconnectSpeed] = useState({
    min: 0,
    max: 100,
    speed: 0,
  });
  const [top, settop] = useState({
    receive_data: [],
    send_data: [],
  });
  const [appflowData, setappflowData] = useState([]);
  const [dataSource, setdataSource] = useState([]);
  const columns = [
    {
      title: '编号',
      dataIndex: 'num',
    },
    {
      title: '类型',
      dataIndex: 'model_name',
    },
    {
      title: '描述',
      dataIndex: 'model_info',
      render: (t: string) => {
        return <Tooltip title={t}>{ellipsis(t)}</Tooltip>;
      },
    },
  ];
  let colorList = [
    '#FF0000',
    '#FF8C00',
    '#FFFF00',
    '#00FF00',
    '#48D1CC',
    '#1E90FF',
    '#8A2BE2',
    '#FF1493',
    '#808080',
    '#FFD700',
  ];

  useEffect(() => {
    handleRefresh();
    timer = setInterval(() => {
      handleRefresh();
    }, 5000);
    return () => {
      clearInterval(timer);
    };
  }, []);
  useEffect(() => {
    initFlow();
  }, [flowSpeed]);
  useEffect(() => {
    initSpeed();
  }, [connectSpeed]);
  useEffect(() => {
    initLaunch();
    initAccept();
  }, [top]);
  useEffect(() => {
    initAppFlow();
  }, [appflowData]);
  const handleRefresh = () => {
    getFlowData();
    getSpeedData();
    getTop10();
    getFlowEnvent();
    getappFlowData();
  };
  const getappFlowData = () => {
    appflow({ app_list: app_list }).then(res => {
      if (res.flag) {
        app_list = res.data.app_list;
        setappflowData(res.data.app_data);
      } else {
        message.error(res.message);
      }
    });
  };
  const getFlowEnvent = () => {
    flowEvent().then(res => {
      if (res.flag) {
        setdataSource(res.data.alerts);
      } else {
        message.error(res.message);
      }
    });
  };
  const getFlowData = () => {
    get_traffic_output().then(res => {
      if (res.flag) {
        setflowSpeed(res.data);
      } else {
        message.error(res.message);
      }
    });
  };
  const getSpeedData = () => {
    get_new_conn_speed().then(res => {
      if (res.flag) {
        setconnectSpeed(res.data.speed);
      } else {
        message.error(res.message);
      }
    });
  };
  const getTop10 = () => {
    new_conn_top().then(res => {
      if (res.flag) {
        settop(res.data);
      } else {
        message.error(res.message);
      }
    });
  };
  const initFlow = () => {
    let chartDom = document.getElementById('flow');
    let myChart = echarts.init(chartDom);
    let option = {
      tooltip: {
        formatter: '{a} <br/>{b} : {c}%',
      },
      series: [
        {
          name: 'Pressure',
          type: 'gauge',
          progress: {
            show: true,
          },
          detail: {
            valueAnimation: true,
            formatter: '{value}',
          },
          min: flowSpeed.min,
          max: flowSpeed.max,
          data: [
            {
              value: flowSpeed.out_total,
              name: 'Mpbs',
            },
          ],
        },
      ],
    };
    myChart.setOption(option);
  };

  // 初始化流量吞吐
  const initSpeed = () => {
    let chartDom = document.getElementById('speed');
    let myChart = echarts.init(chartDom);
    let option = {
      tooltip: {
        formatter: '{a} <br/>{b} : {c}%',
      },
      series: [
        {
          name: 'Pressure',
          type: 'gauge',
          progress: {
            show: true,
          },
          detail: {
            valueAnimation: true,
            formatter: '{value}',
          },
          min: connectSpeed.min,
          max: connectSpeed.max,
          data: [
            {
              value: connectSpeed.speed,
              name: 'connection/s',
            },
          ],
        },
      ],
    };
    myChart.setOption(option);
  };

  // 初始化应用流量图
  const initAppFlow = () => {
    let chartDom = document.getElementById('appFlow');
    let myChart = echarts.init(chartDom);
    if (appflowData && appflowData.length) {
      let time = appflowData.map((item: any) => {
        return moment(item.time).format('YYYY-MM DD HH:mm:ss');
      });
      let legend: string[] = [];

      Object.keys(appflowData[0].app).map(key => {
        legend.push(key);
        // if (key !== 'time') {
        // }
      });
      let series = legend.map(item => {
        return {
          name: item,
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: 0,
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
          },
          emphasis: {
            focus: 'series',
          },
          data: [],
        };
      });
      series.forEach(ele => {
        appflowData.forEach((item: any) => {
          Object.keys(item.app).forEach((items: any) => {
            if (ele.name === items) {
              ele.data.push(item.app[items]);
            }
          });
        });
      });

      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
        },
        legend: {
          data: legend,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: time,
          },
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              formatter: '{value} kb'
            }
          },
        ],
        series: series,
      };
      myChart.setOption(option);
    }
  };

  // 初始化发起连接数top10
  const initLaunch = () => {
    let chartDom = document.getElementById('launch');
    let myChart = echarts.init(chartDom);
    let barDataY = top.send_data.map((item: any) => item.src_ip);
    let barDataX = top.send_data.map((item: any) => item.total);
    barDataY.reverse();
    barDataX.reverse();
    let option = {
      grid: { containLabel: true },
      xAxis: { type: 'value' },
      yAxis: { type: 'category', data: barDataY },

      series: [
        {
          type: 'bar',
          itemStyle: {
            // 定制显示（按顺序）,实现不同颜色的柱体
            color: (params: any) => {
              return colorList[params.dataIndex];
            },
          },
          data: barDataX, // 数据
        },
      ],
    };
    myChart.setOption(option);
  };

  // 初始化接收数top10
  const initAccept = () => {
    let chartDom = document.getElementById('accept');
    let myChart = echarts.init(chartDom);
    let barDataY = top.receive_data.map((item: any) => item.dst_ip);
    let barDataX = top.receive_data.map((item: any) => item.total);
    barDataY.reverse();
    barDataX.reverse();
    let option = {
      grid: { containLabel: true },
      xAxis: { type: 'value' },
      yAxis: { type: 'category', data: barDataY },

      series: [
        {
          type: 'bar',
          itemStyle: {
            // 定制显示（按顺序）,实现不同颜色的柱体
            color: (params: any) => {
              return colorList[params.dataIndex];
            },
          },
          data: barDataX, // 数据
        },
      ],
    };
    myChart.setOption(option);
  };
  return (
    <div>
      <div className={style.top_title}>
        <div>
          <span className={style.title}>流量监控</span>
          <div className={style.switch_box}>
            <Switch checked={isSimple} onChange={getSwitch} />
          </div>
          <span className={style.detail_text}>详细模式</span>
        </div>
        <div>
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <div className={style.top}>
        <Card title="流量吞吐" style={{ width: '49%' }}>
          <div className={style.flow} id="flow"></div>
        </Card>
        <Card title="连接新建速率" style={{ width: '49%' }}>
          <div className={style.flow} id="speed"></div>
        </Card>
      </div>

      <Card title="应用流量图" className={style.alone_box}>
        <div id="appFlow" className={style.appFlow}></div>
      </Card>
      <div className={style.connect}>
        <Card title="发起连接数TOP10" style={{ width: '49%' }}>
          <div id="launch" className={style.connect_item}></div>
        </Card>
        <Card title="接收连接数TOP10" style={{ width: '49%' }}>
          <div id="accept" className={style.connect_item}></div>
        </Card>
      </div>
      <Card title="流量事件" className={style.alone_box}>
        <div>
          <Table dataSource={dataSource} pagination={false} columns={columns} />
        </div>
      </Card>
    </div>
  );
};
export default Detail;
