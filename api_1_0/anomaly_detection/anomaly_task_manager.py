# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : anomaly_task_manager.py
# @Software: PyCharm

"""异常检测任务管理API"""

import uuid
from datetime import datetime
from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from utils.database import MongoDB
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class AnomalyTaskList(Resource):
    """异常检测任务列表管理"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'anomaly_tasks'
    
    
    def get(self):
        """获取异常检测任务列表"""
        try:
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 10))
            status = request.args.get('status')
            model_type = request.args.get('model_type')
            
            # 构建查询条件
            query = {}
            if status:
                query['status'] = status
            if model_type:
                query['model_type'] = model_type
            
            # 分页查询
            skip = (page - 1) * page_size
            tasks = list(self.mongodb.find(
                self.collection, 
                query, 
                {'_id': 0}
            ).sort('create_time', -1).skip(skip).limit(page_size))
            
            total = self.mongodb.count(self.collection, query)
            
            # 格式化时间戳
            for task in tasks:
                if 'create_time' in task:
                    task['create_time'] = int(task['create_time'].timestamp() * 1000)
                if 'update_time' in task:
                    task['update_time'] = int(task['update_time'].timestamp() * 1000)
                if 'last_run_time' in task:
                    task['last_run_time'] = int(task['last_run_time'].timestamp() * 1000)
            
            result = {
                'tasks': tasks,
                'total': total,
                'page': page,
                'page_size': page_size
            }
            
            return flask_response('获取任务列表成功', True, result)
            
        except Exception as e:
            LOG.error(f"获取任务列表失败: {str(e)}")
            return flask_response(f'获取任务列表失败: {str(e)}', False, {})
    
    
    def post(self):
        """创建异常检测任务"""
        try:
            data = request.get_json()
            
            # 验证必需字段
            required_fields = ['name', 'model_types', 'target_type', 'target_value', 'task_type']
            for field in required_fields:
                if field not in data:
                    return flask_response(f'缺少必需字段: {field}', False, {})

            # 验证模型类型
            valid_models = ['time_series', 'baseline', 'repeat_conn', 'long_conn', 'geo_anomaly']
            model_types = data['model_types']
            if not isinstance(model_types, list) or not model_types:
                return flask_response('model_types必须是非空数组', False, {})

            for model_type in model_types:
                if model_type not in valid_models:
                    return flask_response(f'不支持的模型类型: {model_type}', False, {})

            # 验证任务类型
            task_type = data['task_type']
            if task_type not in ['historical', 'scheduled']:
                return flask_response('task_type必须是historical或scheduled', False, {})
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 构建任务数据
            task_data = {
                'task_id': task_id,
                'name': data['name'],
                'description': data.get('description', ''),
                'model_types': data['model_types'],  # 支持多个模型
                'target_type': data['target_type'],  # ip, domain
                'target_value': data['target_value'],
                'task_type': data['task_type'],  # historical, scheduled
                'time_config': self._build_time_config(data),
                'alert_config': data.get('alert_config', {'enabled': True, 'severity_threshold': 'medium'}),
                'status': 'active',
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'last_run_time': None,
                'run_count': 0,
                'creator': data.get('creator', 'system')
            }
            
            # 保存到数据库
            self.mongodb.insert_one(self.collection, task_data)
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"创建异常检测任务: {data['name']}")
            LOG.info(f"创建异常检测任务成功: {task_id}")
            
            return flask_response('创建任务成功', True, {'task_id': task_id})
            
        except Exception as e:
            LOG.error(f"创建任务失败: {str(e)}")
            return flask_response(f'创建任务失败: {str(e)}', False, {})

    def _build_time_config(self, data):
        """构建时间配置"""
        task_type = data['task_type']

        if task_type == 'historical':
            # 历史回溯任务
            return {
                'type': 'historical',
                'start_time': data.get('start_time'),  # 开始时间戳
                'end_time': data.get('end_time'),      # 结束时间戳
                'time_window_hours': data.get('time_window_hours', 1)  # 检测窗口
            }
        elif task_type == 'scheduled':
            # 定时任务
            return {
                'type': 'scheduled',
                'schedule_type': data.get('schedule_type', 'daily'),  # daily, weekly, monthly
                'interval_hours': data.get('interval_hours', 24),
                'time_range_hours': data.get('time_range_hours', 1),
                'start_date': data.get('start_date'),  # 开始日期
                'end_date': data.get('end_date'),      # 结束日期（最多一年）
                'enabled_days': data.get('enabled_days', [1,2,3,4,5,6,7])  # 启用的星期几
            }
        else:
            return {}


class AnomalyTaskDetail(Resource):
    """异常检测任务详情管理"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'anomaly_tasks'
    
    
    def get(self, task_id):
        """获取任务详情"""
        try:
            task = self.mongodb.find_one(self.collection, {'task_id': task_id}, {'_id': 0})
            
            if not task:
                return flask_response('任务不存在', False, {})
            
            # 格式化时间戳
            if 'create_time' in task:
                task['create_time'] = int(task['create_time'].timestamp() * 1000)
            if 'update_time' in task:
                task['update_time'] = int(task['update_time'].timestamp() * 1000)
            if 'last_run_time' in task and task['last_run_time']:
                task['last_run_time'] = int(task['last_run_time'].timestamp() * 1000)
            
            return flask_response('获取任务详情成功', True, task)
            
        except Exception as e:
            LOG.error(f"获取任务详情失败: {str(e)}")
            return flask_response(f'获取任务详情失败: {str(e)}', False, {})
    
    
    def put(self, task_id):
        """更新任务"""
        try:
            data = request.get_json()
            
            # 检查任务是否存在
            existing_task = self.mongodb.find_one(self.collection, {'task_id': task_id})
            if not existing_task:
                return flask_response('任务不存在', False, {})
            
            # 更新数据
            update_data = {
                'update_time': datetime.now()
            }
            
            # 允许更新的字段
            updatable_fields = ['name', 'description', 'schedule_config', 'detection_config', 'alert_config', 'status']
            for field in updatable_fields:
                if field in data:
                    update_data[field] = data[field]
            
            # 执行更新
            self.mongodb.update_one(self.collection, {'task_id': task_id}, {'$set': update_data})
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"更新异常检测任务: {task_id}")
            LOG.info(f"更新异常检测任务成功: {task_id}")
            
            return flask_response('更新任务成功', True, {})
            
        except Exception as e:
            LOG.error(f"更新任务失败: {str(e)}")
            return flask_response(f'更新任务失败: {str(e)}', False, {})
    
    
    def delete(self, task_id):
        """删除任务"""
        try:
            # 检查任务是否存在
            existing_task = self.mongodb.find_one(self.collection, {'task_id': task_id})
            if not existing_task:
                return flask_response('任务不存在', False, {})
            
            # 删除任务
            self.mongodb.delete(self.collection, {'task_id': task_id})
            
            # 同时删除相关的检测结果
            self.mongodb.delete('anomaly_results', {'task_id': task_id})
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"删除异常检测任务: {task_id}")
            LOG.info(f"删除异常检测任务成功: {task_id}")
            
            return flask_response('删除任务成功', True, {})
            
        except Exception as e:
            LOG.error(f"删除任务失败: {str(e)}")
            return flask_response(f'删除任务失败: {str(e)}', False, {})
