syntax = "proto3";

import "google/protobuf/empty.proto";

package session;

// The session service definition.
service Session {
  rpc SetStatSetting (StatSetting) returns (ReplyHeader) {}
  rpc GetStatSetting (google.protobuf.Empty) returns (StatSettingReply) {}
  rpc GetStats (StatsRequest) returns (StatsReply) {}
  rpc ResetStats (google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc ListSessions (SessionsRequest) returns (SessionsReply) {}
  rpc GetSession (SessionRequest) returns (SessionReply) {}
  rpc ResetSessions (google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc ListRelations (RelationsRequest) returns (RelationsReply) {}
  rpc GetRelation (RelationRequest) returns (RelationReply) {}
  rpc ResetRelations (google.protobuf.Empty) returns (google.protobuf.Empty) {}
//  rpc SetAgingTime (Proto) returns () {}
}

message ReplyHeader {
  uint32 code = 1;
  string message = 2;
}

message SessionsRequest {
  uint32 anchor = 1;
  uint32 addr_family = 2;
  Key key = 3;
}

message SessionsReply {
  ReplyHeader msg = 1;
  uint32 anchor = 2;
  repeated SessionData session = 3;
}

message Key {
  string src_ip = 1;
  string dst_ip = 2;
  uint32 src_port = 3;
  uint32 dst_port = 4;
  uint32 protocol = 5;
}

message SessionData {
  Key key = 1;
  uint32 flags = 2;
  uint32 app_id = 3;
  uint32 status = 4;
}

message SessionRequest {
  Key key = 1;
}

message SessionReply {
  ReplyHeader msg = 1;
  SessionData session = 2;
}

// 关联表
message RelationData {
  Key key = 1;
  uint32 flags = 2;
  uint32 app_id = 3;
}

message RelationRequest {
  Key key = 1;
}

message RelationReply {
  ReplyHeader msg = 1;
  RelationData relation = 2;
}

message RelationsRequest {
  uint32 anchor = 1;
  Key key = 2;
}

message RelationsReply {
  ReplyHeader msg = 1;
  uint32 anchor = 2;
  repeated RelationData relation = 3;
}

message StatSetting {
  bool enable = 1;
}

message StatSettingReply {
  ReplyHeader msg = 1;
  StatSetting setting = 2;
}

message StatsRequest {
}

message StatsReply {
  ReplyHeader msg = 1;
  uint32 sessions = 2;
  uint32 tcp_sessions = 3;
  uint32 udp_sessions = 4;
  uint32 other_sessions = 5;
  uint32 relations = 6;
}