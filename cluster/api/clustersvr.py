#!/usr/bin/env python3
# coding:utf-8

import rpyc
rpyc.core.channel.Channel.COMPRESSION_LEVEL = 0
rpyc.core.stream.SocketStream.MAX_IO_CHUNK = 65355*10
from rpyc.utils.server import ThreadedServer

from cluster.settings import *
from cluster.utils.logger import get_logger
from cluster.service.service import KSPService
from cluster.modules.auth import KSPAuthenticator, KSSLAuthenticator


logger = get_logger(__name__)

class ClusterServer:
    def __init__(self,
                 bind_ip="0.0.0.0",
                 port=CLUSTER_SERVER_PORT,
                 service=KSPService,
                 node_id="",
                 ssl_key=CLUSTER_SSL_CLIENT_KEY,
                 ssl_cert=CLUSTER_SSL_CLIENT_CERT,
                 ssl_ca=CLUSTER_SSL_CA_CERT,
                 psk=CLUSTER_PRE_SHARED_KEY):
        self.bind_ip = bind_ip
        self.cluster_port = port
        self.service = service
        self.node_id = node_id
        self.ssl_key = ssl_key
        self.ssl_cert = ssl_cert
        self.ssl_ca = ssl_ca
        self.psk = psk
        authenticator = KSSLAuthenticator(
            ssl_key, ssl_cert,
            ssl_ca, psk=psk)
        self.server = ThreadedServer(
        service,
        hostname=bind_ip,
        port=port,
        authenticator=authenticator,
        registrar=None,
        protocol_config={'allow_public_attrs': True, "allow_pickle": True, "sync_request_timeout": None},
        auto_register=False
    )

    def start(self):
        self.server.start()

    def stop(self):
        self.server.close()