/*
 * @Author: tianh
 * @Date: 2022-05-30 17:02:50
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-16 10:44:39
 * @Descripttion:
 */
import { detail_path, get_path } from '@/services/collect';
import { timeTypeColect } from '@/utils/enumList';
import { ellipsis, getQueryVariable } from '@/utils/utils';
import { SearchOutlined } from '@ant-design/icons';
import { Button, Collapse, DatePicker, Form, Input, Select, Table, Tooltip, message } from 'antd';
import _ from 'lodash';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { InterGraphData, InterLinks, InterParams } from './interface';
import style from './style.less';

const echarts = require('echarts');
const { Panel } = Collapse;
const { RangePicker } = DatePicker;
const { Option } = Select;

const PathAnalysis = () => {
  let myChart: any;
  const [form] = Form.useForm();
  const [params, setparams] = useState<InterParams>({
    time: [],
    timeType: 'observedTime',
    ip: '',
  });
  const [currentIp, setCurrentIp] = useState('');
  const [tableData, setTableData] = useState<any>([]);
  const [nodesData, setNodesData] = useState<InterGraphData>({
    nodes: [],
    links: [],
  });

  const columns = [
    {
      title: 'ip',
      dataIndex: 'ip',
      width: 150,
    },
    {
      title: 'src/dst',
      dataIndex: 'source',
    },
    {
      title: 'ports',
      dataIndex: 'ports',
      render: (t: any) => {
        return (
          <Tooltip placement="topLeft" arrowPointAtCenter title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: '协议',
      dataIndex: 'application',
      render: (t: any) => {
        return (
          <Tooltip placement="topLeft" arrowPointAtCenter title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      render: (t: any, record: any) => {
        return (
          <>
            <Button
              type="link"
              disabled={!!nodesData.nodes.filter((item: any) => item.id === record.ip).length}
              onClick={() => {
                addNode(record);
              }}
            >
              加入
            </Button>
            <Button
              type="link"
              disabled={!nodesData.nodes.filter((item: any) => item.id === record.ip).length}
              onClick={() => {
                subNode(record);
              }}
            >
              移除
            </Button>
          </>
        );
      },
    },
  ];

  // 搜索
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      value.timeType = 'observedTime';
      setCurrentIp(value.ip);
      getNodesData(value, 'resetOld');
      getTableData(value);
    });
  };
  // 加入节点
  const addNode = (row: any) => {
    setNodesData({
      nodes: [...nodesData.nodes, { id: row.ip, name: row.ip, nodeType: '2' }],
      links: [...nodesData.links, { source: currentIp, target: row.ip }],
    });
  };

  // 移除节点
  const subNode = (row: any) => {
    // 判断该节点是否可被移除；该节点有多个边不能被移除
    const judgeLinks = nodesData.links.filter((item) => {
      return item.source === row.ip || item.target === row.ip;
    }).length;
    if (judgeLinks > 1) {
      message.warning('该节点非叶子节点，无法移除');
    } else {
      const newNodes = nodesData.nodes.filter((item: any) => item.id !== row.ip);
      // 节点移除后，相关的边也被移除
      const newLinks = nodesData.links.filter((item: any) => !(item.target === row.ip));

      setNodesData({
        nodes: newNodes,
        links: newLinks,
      });
    }
  };

  // 将接口数据处理为渲染所需要的数据
  const handleNodesData = (resData: { ip: {}; access_ip: {} }, origin?: string) => {
    let newNodes;
    let newLinks;
    const centerIp = Object.keys(resData.ip).map((item) => {
      return {
        id: item,
        name: item,
        nodeType: resData.ip[item],
      };
    });

    const linksIp = Object.keys(resData.access_ip).map((item) => {
      return {
        id: item,
        name: item,
        nodeType: resData.access_ip[item],
      };
    });

    newNodes = centerIp.concat(linksIp);

    newLinks = Object.keys(resData.access_ip).map((item: string) => {
      return {
        source: Object.keys(resData.ip)[0],
        target: item,
      };
    });

    // 搜索使用，不需要加原始数据
    if (origin !== 'resetOld') {
      // 将新数据边集合与旧数据边集合合并加去重和去除source target 相同的数据
      newLinks = _.uniqWith([...nodesData.links, ...newLinks], _.isEqual).reduce((arr: any, item: InterLinks) => {
        const hasLike = arr.some((item1: any) => item.source === item1.target && item.target === item1.source);
        return hasLike ? arr : [...arr, item];
      }, []);

      newNodes = _.uniqBy([...nodesData.nodes, ...newNodes], 'id');
    }

    setNodesData({
      nodes: newNodes,
      links: newLinks,
    });
  };

  // 获取节点图数据
  const getNodesData = (params: InterParams, origin?: string) => {
    const apiParams = {
      startTime: moment(params.time[0]).valueOf().toString(),
      stopTime: moment(params.time[1]).valueOf().toString(),
      ip: params.ip,
      timeType: params.timeType,
    };
    get_path(apiParams).then((res) => {
      if (res.flag) {
        handleNodesData(res.data, origin);
      } else {
        message.error(res.message);
      }
    });
  };

  // 获取列表数据
  const getTableData = (params: InterParams) => {
    const apiParams = {
      startTime: moment(params.time[0]).valueOf().toString(),
      stopTime: moment(params.time[1]).valueOf().toString(),
      ip: params.ip,
      timeType: params.timeType,
    };
    detail_path(apiParams).then((res) => {
      if (res.flag) {
        setTableData(res.data);
      } else {
        message.error(res.message);
      }
    });
  };

  // 访问链图标自适应
  const handleResize = () => {
    if (myChart) {
      myChart?.resize();
    }
  };

  useEffect(() => {
    myChart = echarts.init(document.getElementById('main'));

    let option = {
      dataZoom: {
        // 放大和缩放
        type: 'inside',
      },

      title: {},
      tooltip: {
        trigger: 'axis',
        triggerOn: 'click',
      },
      series: [
        {
          roam: true,
          type: 'graph', // 类型:关系图
          layout: 'force', //图的布局，类型为力导图
          symbolSize: 50, // 调整节点的大小
          edgeSymbol: ['circle', 'arrow'],
          edgeSymbolSize: [1, 2],
          edgeLabel: {
            normal: {
              textStyle: {
                fontSize: 20,
              },
            },
          },
          force: {
            repulsion: 400, //节点之间的斥力因子。支持数组表达斥力范围，值越大斥力越大。
            edgeLength: 80, //边的两个节点之间的距离，值越小则长度越长，这个距离也会受 repulsion影响。
            gravity: 0.03, //节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
            // layoutAnimation: true, //初始化时转动动画
            animation: false,
          },
          draggable: true,
          lineStyle: {
            normal: {
              width: 2,
            },
          },
          itemStyle: {
            color: function (params: any) {
              if (currentIp === params.name) {
                return '#2f4554';
              } else if (params.data.nodeType === 'alarm') {
                return '#FF0000';
              } else {
                return '#1890ff';
              }
            },
          },
          label: {
            show: true,
          },

          // 数据
          data: nodesData.nodes,
          links: nodesData.links,

          tooltip: {
            show: true,
            trigger: 'item',
            triggerOn: 'click',
            enterable: true,
          },
        },
      ],
    };
    myChart.setOption(option);

    // 重新点击之前取消点击事件，避免重复执行多次
    myChart.off('click');
    myChart.on('click', function (node: any) {
      if (node.dataType == 'node') {
        setCurrentIp(node.name);
        // 重新渲染
        setNodesData({ ...nodesData });
        getTableData({ ...params, ip: node.data.id });
      }
    });

    // 取消div右键事件
    document.getElementById('main').oncontextmenu = function (e) {
      e.preventDefault();
    };
    // 取消图表右击事件
    myChart.off('contextmenu');
    // 添加右键事件
    myChart.on('contextmenu', (node: any) => {
      if (node.dataType == 'node') {
        getNodesData({ ...params, ip: node.data.id });
      }
    });
  }, [nodesData, getTableData]);

  useEffect(() => {
    if (getQueryVariable('startTime') && getQueryVariable('stopTime') && getQueryVariable('ip')) {
      const time = [moment(parseInt(getQueryVariable('startTime'))), moment(parseInt(getQueryVariable('stopTime')))];
      const nowParams = {
        time,
        ip: getQueryVariable('ip'),
        timeType: 'observedTime',
      };

      setCurrentIp(getQueryVariable('ip'));
      setparams({ ...nowParams });
      form.setFieldsValue({ ...nowParams });
      getNodesData({ ...nowParams });
      getTableData({ ...nowParams });
    }

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form initialValues={params} form={form} className="!flex gap-3">
            <Form.Item
              label="时间范围"
              name="time"
              rules={[
                {
                  required: true,
                  message: '请填写!',
                },
              ]}
            >
              <DatePicker.RangePicker
                allowClear
                className="w-100%"
                ranges={{
                  近一年: [moment().add(-1, 'year'), moment()],
                  近半年: [moment().add(-6, 'month'), moment()],
                  近一月: [moment().add(-1, 'month'), moment()],
                  近一周: [moment().add(-7, 'd'), moment()],
                  近一天: [moment().add(-1, 'd'), moment()],
                  今天: [moment().startOf('day'), moment().endOf('day')],
                  本周: [moment().startOf('week'), moment().endOf('week')],
                  本月: [moment().startOf('month'), moment().endOf('month')],
                  本年度: [moment().startOf('year'), moment().endOf('year')],
                }}
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
            <Form.Item
              label="IP地址"
              name="ip"
              rules={[
                {
                  required: true,
                  message: '请填写!',
                },
              ]}
            >
              <Input placeholder="请输入IP地址" allowClear />
            </Form.Item>
            <Form.Item>
              <div className="group_btns">
                <Button className="searchBtn" type="primary" onClick={onSubmit} icon={<SearchOutlined />}>
                  搜索
                </Button>
              </div>
            </Form.Item>
          </Form>
        </Panel>
      </Collapse>
      <div className={style.bottom_charts}>
        <div className="absolute top-0 left-0 w-100% h-100vh" id="main"></div>
        {currentIp ? (
          <Collapse className={style.table_box}>
            <Panel header={currentIp} key="1">
              <Table
                rowKey={(r: any, i: any) => i}
                columns={columns}
                dataSource={tableData}
                pagination={{
                  showSizeChanger: tableData?.length > 10,
                  showTotal: (total: number) => `共 ${total} 条`,
                }}
              />
            </Panel>
          </Collapse>
        ) : null}
      </div>
    </div>
  );
};
export default PathAnalysis;
