{
    "type": "object",
    "required": ["id_list", "type", "confirm"],
    "properties": {
        "id_list": {
            "type": "array",
            "items": {
                "type": "string",
                "pattern": r"^.{20}"
              }
        },
        "type": {
			"type": "string",
			"default": "",
			"enum": ["file", "vul", "ioc", "model"]
		},
		"confirm" : {
            "type": "number",
            "enum": [0, 1, 2, 3, 4]
		}
    }
}