import React, { useEffect, useState } from 'react';
import cn from 'classnames';
import { Spin } from 'antd';
import { useHistory } from '@/hooks/global';
import { pick } from 'lodash';
import { getFileReportDetail } from '@/services/collect';
import s from './style.less';
import BaseInfo from './FileDetail/baseInfo';
import DownInfo from './FileDetail/downInfo';
import TabsInfo from './FileDetail/tabsInfo';
const FileDetail = () => {
  const [detail, setDetail] = useState({});
  const [loading, setLoading] = useState(false);
  const history = useHistory();
  const params = JSON.parse(history.location.query.params);
  const base = pick(params, ['md5', 'virus_name.knownsec', 'filetype', 'threatLevel','labels','pcap_filename','threatName']);
  const down = pick(params, ['reportPath','filename', 'filetype', 'dstIpGeoInfo', 'flow', 'killchain', 'srcIpGeoInfo']);
  const fileInfo = pick(params, ['virus_name.knownsec', 'filetype', 'filesize', 'crc32', 'sha256','virus_name.clamav']);
  const getInfo = async () => {
    setLoading(true);
    const filePath = params.reportPath;
    const res = await getFileReportDetail({ filePath });
    if (res.flag) {
      setDetail({ ...res.data, fileInfo });
    }
    setLoading(false);
  };
  useEffect(() => {
    getInfo();
  }, []);
  return (
    <Spin tip="加载中..." spinning={loading}>
      <div className={cn('of-hidden', s.detailBox)} style={{ height: 'calc(100vh - 74px)' }}>
        <BaseInfo base={base} />
        <DownInfo down={down} />
        <TabsInfo detail={detail} />
      </div>
    </Spin>
  );
};
export default FileDetail;
