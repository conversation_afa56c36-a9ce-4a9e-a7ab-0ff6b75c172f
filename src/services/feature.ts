/*
 * @Author: tianh
 * @Date: 2021-09-30 14:15:00
 * @LastEditors: tianh
 * @LastEditTime: 2022-01-05 11:41:38
 * @Descripttion:
 */
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';
// 特征管理table
export const getFeatureTable = (data: object) => {
  return request(`feature${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
// 禁用
export const disableData = (data: object) => {
  return request(`feature/enable_status`, {
    method: 'post',
    data,
  });
};
// 启用
export const enableData = (data: object) => {
  return request(`feature/enable_status`, {
    method: 'post',
    data,
  });
};

// 添加表单
export const addRule = (data: object) => {
  return request(`feature/customer`, {
    method: 'post',
    data,
  });
};

// 编辑特征
export const EditRule = (data: object) => {
  return request(`feature/customer`, {
    method: 'put',
    data,
  });
};

// 特征组table
export const getGroupData = (data: object) => {
  return request(`feature/group${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

// 删除特征组
export const delFeatureGroup = (data: object) => {
  return request(`feature/group/id`, {
    method: 'delete',
    data,
  });
};

// 添加特征组
export const addFeatureGroup = (data: object) => {
  return request(`feature/group`, {
    method: 'post',
    data,
  });
};

// 删除自定义特征
export const delCustom = (data: { sid: any }) => {
  return request(`feature/customer`, {
    method: 'delete',
    data,
  });
};

// 从组内删除/添加
export const groupDelData = (data: object) => {
  return request(`feature/group/id`, {
    method: 'put',
    data,
  });
};
