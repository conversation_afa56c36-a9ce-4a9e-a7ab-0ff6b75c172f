.killChains{
  display: flex;
  align-items: center;
  justify-content: center;
}
.kill<PERSON>hain {

  .killChainImg {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  .success {
    background-image: url('./imgs/success.svg');

    &.active {
      background-image: url('../../assets/images/killchains7.svg');
    }
  }

  .Installation {
    background-image: url('./imgs/Beacon.svg');

    &.active {
      background-image: url('../../assets/images/killchains5.svg');
    }
  }

  .CnC {
    background-image: url('./imgs/CnC.svg');

    &.active {
      background-image: url('../../assets/images/killchains6.svg');
    }
  }

  .Delivery {
    background-image: url('./imgs/Delivery.svg');

    &.active {
      background-image: url('../../assets/images/killchains3.svg');
    }
  }

  .Exploitation {
    background-image: url('./imgs/Exploitation.svg');

    &.active {
      background-image: url('../../assets/images/killchains4.svg');
    }
  }

  .Reconnaissance {
    background-image: url('./imgs/Recon.svg');

    &.active {
      background-image: url('../../assets/images/killchains1.svg');
    }
  }

  .Weaponization {
    background-image: url('./imgs/Weaponization.svg');

    &.active {
      background-image: url('../../assets/images/killchains2.svg');
    }
  }

  .dot {
    width: 8px;
    height: 8px;
    margin-right: 3px;
    border: 1px solid #979797;
    border-radius: 50%;
    cursor: pointer;
  }

  // .active {
  //   width: 9px;
  //   height: 9px;
  //   background: #484b5b;
  //   border: 0;
  // }
}

.single{
  display: block;
}