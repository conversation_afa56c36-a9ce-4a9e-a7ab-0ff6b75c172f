#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import os
import json
import requests

from flask import request

from api_3_0.work_bench import basic
from config.config import SharkdConfig, NdrLog
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)
SHARKD_URL = 'http://%s:%s' % (SharkdConfig.Host, SharkdConfig.Port)


class LoadPcapFile(basic.Basic):

    @param_check.check_flask_args(Validator("pcap_info"), request)
    def get(self, **kwargs):
        """
        pcap-load获取
        :return:
        """
        try:
            pcap_path = kwargs["pcap_name"]
            file_name = self.get_hash_file_name(pcap_path)
            self.get_current_user_id(request)
            data = {
                "fileName": file_name,
                "command": '{"req":"load","file":"%s"}' % pcap_path,
                "user": self.user_id
            }
            LOG.error("pcap_path: %s, file_name: %s" % (pcap_path, file_name))
            requests.post(url=SHARKD_URL, json=data)
            return flask_response('获取成功', True, {})
        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error("reason: %s" % str(e))
            return flask_response(err_info, False, {})
        

class PcapList(basic.Basic):

    @param_check.check_flask_args(Validator("pcap_info"), request)
    def get(self, **kwargs):
        """
        pcap包信息总览获取
        :return:
        """
        try:
            pcap_path = kwargs["pcap_name"]
            file_name = self.get_hash_file_name(pcap_path)
            skip = kwargs["skip"]
            limit = kwargs["limit"]
            self.get_current_user_id(request)
            data = {
                "fileName": file_name,
                "command": '{"req":"frames", "color": "true", "skip":%s, "limit":%s}' % (skip, limit),
                "user": self.user_id
            }
            res = requests.post(url=SHARKD_URL, json=data)
            msg = json.loads(res.content)["data"]
            return flask_response('获取成功', True, msg)
        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error("reason: %s" % str(e))
            return flask_response(err_info, False, {})


class PcapData(basic.Basic):

    @param_check.check_flask_args(Validator("pcap_info"), request)
    def get(self, **kwargs):
        """
        pcap包信息详情
        :return:
        """
        try:
            pcap_path = kwargs["pcap_name"]
            file_name = self.get_hash_file_name(pcap_path)
            frame = kwargs["frame"]
            self.get_current_user_id(request)
            data = {
                "fileName": file_name,
                "command": '{"req":"frame", "frame":%s, "proto":"true", "bytes": "true"}' % frame,
                "user": self.user_id
            }
            res = requests.post(url=SHARKD_URL, json=data)
            msg = json.loads(res.content)["data"]
            return flask_response('获取成功', True, msg)
        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error("reason: %s" % str(e))
            return flask_response(err_info, False, {})


class PcapContent(basic.Basic):

    @param_check.check_flask_args(Validator("pcap_info"), request)
    def get(self, **kwargs):
        """
        pcap包内容
        :return:
        """
        try:
            pcap_path = kwargs["pcap_name"]
            file_name = self.get_hash_file_name(pcap_path)
            follow = kwargs["follow"]
            filter = kwargs["filter"]
            self.get_current_user_id(request)
            data = {
                "fileName": file_name,
                "command": '{"req":"follow", "follow": "%s", "filter":"%s"}' % (follow, filter),
                "user": self.user_id
            }
            res = requests.post(url=SHARKD_URL, json=data)
            msg = json.loads(res.content)["data"]
            return flask_response('获取成功', True, msg)
        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error("reason: %s" % str(e))
            return flask_response(err_info, False, {})


class PcapBye(basic.Basic):

    @param_check.check_flask_args(Validator("pcap_info"), request)
    def get(self, **kwargs):
        """
        pcap-bye
        :return:
        """
        try:
            pcap_path = kwargs["pcap_name"]
            file_name = self.get_hash_file_name(pcap_path)
            self.get_current_user_id(request)
            data = {
                "fileName": file_name,
                "command": '{"req":"bye"}',
                "user": self.user_id
            }
            requests.post(url=SHARKD_URL, json=data)
            return flask_response('获取成功', True, {})
        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error("reason: %s" % str(e))
            return flask_response(err_info, False, {})
