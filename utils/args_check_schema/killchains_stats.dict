{
    "type": "object",
    "required": ["startTime", "stopTime"],
    "properties": {
        "timeSlice": {
            "type": "string",
            "default": "Minute",
            "enum": ["Minute", "Hour", "Day", "Week", "Month"],
        },
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "pageSize": {
            "type": "string",
            "default": "5",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}