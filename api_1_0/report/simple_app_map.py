#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import json
from copy import deepcopy
from flask import request
from api_1_0.report.base_map import BaseMap
from utils.param_check import Validator
from utils.utils import flask_response
from utils import param_check
from utils.logger import get_ndr_logger
from utils.best_show_size import size_format

API_LOG = get_ndr_logger('api_log', __file__)


class AppMap(BaseMap):
    '''应用层协议流量图'''

    def __init__(self):
        super(AppMap, self).__init__("flow.proto.keyword")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        es_return = self.get_es_data(**kwargs)
        message = es_return[1]
        if not es_return[0]:
            return flask_response(message, False, {})

        try:
            data = self.data_format(message)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(message, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, message):
        docs = []
        # 无法涵盖到所有的协议，返回前五的即可
        total_proto = dict()
        total = {}
        data_list = message["aggregations"]["group_by"]["buckets"]
        for data in data_list:
            app_dict = dict()
            time_stamp = data["key"]
            appFlow = dict()
            for app_proto in data["group_by"]["buckets"]:
                app = app_proto["key"]
                # 单位，返回为Mb
                flow_value = app_proto["totalFlow"]["value"]
                if app in total_proto.keys():
                    total_proto[app] += flow_value
                else:
                    total_proto[app] = 0
                    total_proto[app] += flow_value
                appFlow[app] = {
                    "actualSize": flow_value,
                    "showSize": size_format(flow_value)
                }
            app_dict["time"] = time_stamp
            app_dict["appFlow"] = appFlow
            docs.append(app_dict)
        # 只展示前五的数据
        proto_key = sorted(total_proto.keys(), key=lambda x: total_proto[x], reverse=True)[:5]
        copy_proto = deepcopy(total_proto)
        for key, value in copy_proto.items():
            if key not in proto_key:
                total_proto.pop(key)
                continue
            total[key] = {
                "actualSize": value,
                "showSize": size_format(value)
            }
        for doc in docs:
            copy_app = deepcopy(doc["appFlow"])
            for key, _ in copy_app.items():
                if key not in proto_key:
                    doc["appFlow"].pop(key)

        proto_list = total.keys()
        reslut_docs = self.slice_time_data(docs, "appFlow", is_app=True, proto_list=proto_list)
        data_return = {
            "total": total,
            "slice_flow": reslut_docs
        }
        return data_return
