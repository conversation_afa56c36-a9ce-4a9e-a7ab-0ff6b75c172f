#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils import param_check
from flask import request
from utils.es_function import get_range
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from config.config import RULE_INDEX
from utils.es_function import get_country_term


ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class IpLocation(NdrResource):
    '''IP地址地理位置$威胁得分接口'''

    field_template = {
        "occurred": "occurredTime",
        "observed": "observedTime"
    }

    def __init__(self):
        super(IpLocation, self).__init__(es_template="ip_location")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        # 开始时间&结束时间校验
        response = self.time_verify(**kwargs)
        if response is not True:
            return response

        start_time = kwargs["startTime"]
        stop_time = kwargs["stopTime"]
        time_mode = kwargs["timeMode"]
        country = kwargs["country"]
        time_mode = self.field_template[time_mode]
        ranges = get_range(time_mode, start_time, stop_time)
        self.es_template["query"]["bool"]["must"].append(ranges)
        if country:
            country_term = get_country_term(country)
            self.es_template["query"]["bool"]["must"].append(country_term)

        try:
            res = self.es_client.search(index=RULE_INDEX, body=self.es_template)
        except Exception as e:
            err_info = "error: " + str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(err_info, False, [])

        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [])

    @staticmethod
    def data_format(data):

        result = list()
        country_buckets = data["aggregations"]["country"]["buckets"]
        for ip_buckets in country_buckets:
            # 国家为空时
            if ip_buckets["key"] == "":
                continue
            # todo ip权重显示
            buckets = ip_buckets["ip"]["buckets"]
            for bucket in buckets:
                ip_dict = {}
                ip_dict["ip"] = bucket["key"]
                ip_dict["threatScore"] = bucket["maxThreatScore"]["value"]
                attackIpGeoip = bucket["attack_ip_hits"]["hits"]["hits"][0]["_source"]["attackIpGeoip"]
                ip_dict["city"] = attackIpGeoip.get("city_name", "")
                ip_dict["country"] = attackIpGeoip.get("country_name", "")
                ip_dict["latitude"] = attackIpGeoip.get("latitude", "")
                ip_dict["longitude"] = attackIpGeoip.get("longitude", "")
                result.append(ip_dict)

        return result
