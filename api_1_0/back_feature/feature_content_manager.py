# -*- coding: utf-8 -*-
# @Time    : 2019-05-05 14:10
# <AUTHOR> hachi
# @File    : feature_content_manager.py
# @Software: PyCharm
"""
特征内容处理模块
"""
import re
import os
import shutil
import time
import json
import sqlite3
from datetime import datetime
from flask import abort, request
from flask_restful import Resource

from api_1_0.hdp_grpc.die import grpc_feature_operate, grpc_feature_add
from api_1_0.knowledge.get_killchains import GetKillchains
from config.config import NdrLog, DetectProg, KnowledgeNDR
from config.config import SystemFeaturePath
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils import param_check
from utils.param_check import Validator
from utils.utils import flask_response, run_command
from utils.json_format import JSONEncoder
from utils.param_check import check_flask_args
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.ndr_base import NdrResource
from utils.utils import get_hdp_feature_str_from_query
from utils.cluster import *
from api_1_0.auth.auths import Authenticate
from celery_tasks.feature_activate_task import periodic_task

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class FeatureListResource(Resource):
    """
    获取所有特征
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    # 查询条件解析
    @staticmethod
    def query_condition(number, kill_chains, threat_flag, vul_type, vul_info, threat_min_score,
                        threat_max_score, mark, feature_status, group_id, control):
        """
        :param number:
        :param kill_chains:
        :param threat_flag:
        :param vul_type:
        :param vul_info:
        :param threat_min_score:
        :param threat_max_score:
        :param featureStatus:
        :return:
        """
        condition = {"message": "", "flag": False, "data": {"$and": []}}
        if number != "0":
            condition['data']["$and"].append({"sid": number})

        if kill_chains != ['']:
            condition['data']["$and"].append({"$or": [{"lockheedKillchainEN": {"$in": kill_chains}}]})

        if threat_flag != "":
            condition['data']["$and"].append({"threatFlag": {'$regex': threat_flag, '$options': 'i'}})

        if vul_type != "":
            condition['data']["$and"].append({"vulType": vul_type})

        if mark == "1":
            condition['data']["$and"].append({"isCustomer": True})

        if mark == "2":
            condition['data']["$and"].append({"isCustomer": False})

        if vul_info != "":
            condition['data']["$and"].append({"vulName": {'$regex': vul_info, '$options': 'i'}})

        if feature_status != "":
            condition['data']["$and"].append({"featureStatus": feature_status})

        if group_id != "":
            condition['data']["$and"].append({"featureGroup": {"$elemMatch": {"$eq": group_id}}})

        if control != "":
            condition['data']["$and"].append({"control": control})

        if threat_min_score > threat_max_score:
            condition["message"] = "The maximum score cannot be less than the minimum score"
            return condition
        condition["flag"] = True
        condition['data']["$and"].append({"threatScore": {'$gt': threat_min_score, '$lt': threat_max_score}})
        return condition

    @check_flask_args(Validator("feature_get_all_schema"), request)
    def get(self, **kwargs):
        """
        获取当前特征的状态
        :return:
        """
        number = kwargs["args"]["number"]
        kill_chains = kwargs["args"]["killChains"].split(',')
        threat_flag = kwargs["args"]["threatFlag"]
        vul_type = kwargs["args"]["vulType"]
        vul_info = kwargs["args"]["vulInfo"]
        threat_min_score = int(kwargs["args"]["threatMinScore"])
        threat_max_score = int(kwargs["args"]["threatMaxScore"])
        page = int(kwargs["args"]["page"])
        page_size = int(kwargs["args"]["pageSize"])
        mark = kwargs["args"]["mark"]
        feature_status = kwargs["args"]["featureStatus"]
        group_id = kwargs["args"]["group_id"]
        control = kwargs["args"]["control"]
        try:
            condition = self.query_condition(number, kill_chains, threat_flag, vul_type, vul_info, threat_min_score,
                                             threat_max_score, mark, feature_status, group_id, control)
            feature = self.mongodb.find("feature", condition["data"], {"_id": 0,
                                                                       "sid": 1,
                                                                       "author": 1,
                                                                       "vulName": 1,
                                                                       "vulType": 1,
                                                                       "cve": 1,
                                                                       "lockheedKillchainStage": 1,
                                                                       "lockheedKillchainCN": 1,
                                                                       "lockheedKillchainEN": 1,
                                                                       "threatFlag": 1,
                                                                       "alterInfo": 1,
                                                                       "submitTime": 1,
                                                                       "is0day": 1,
                                                                       "featureContent": 1,
                                                                       "attackIp": 1,
                                                                       "victimIp": 1,
                                                                       "threatScore": 1,
                                                                       "appProto": 1,
                                                                       "updateTime": 1,
                                                                       "featureStatus": 1,
                                                                       "isCustomer": 1,
                                                                       "control": 1,
                                                                       "owner": 1}) \
                .sort([("updateTime", -1)]).limit(page_size).skip((page - 1) * page_size)

            cols = []
            for col in feature:
                if not col['isCustomer']:
                    col.pop('featureContent')
                col = JSONEncoder().encode(col)
                cols.append(json.loads(col))

            data = {"search_count": self.mongodb.find("feature", condition["data"]).count(),
                    "sidList": cols,
                    "page": page,
                    "pageSize": page_size
                    }
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征获取成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error("feature get failed,reason: %s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征获取失败")
            return flask_response('feature get failed', False, {})


class FeatureModify(NdrResource):

    def __init__(self):
        super(FeatureModify, self).__init__(mongo_db="ndr")
        self.col = "feature"
        self.user_db = SystemFeaturePath.USER_DB
        self.vul_table = SystemFeaturePath.VUL_TABLE
        self.tlv_bin_file_prefix = '/tmp/' + str(hash(time.time()))

    def query_check(self, query, update=False):
        sid = query["sid"]
        feature_content = query["featureContent"]
        # 验证sid是否已经存在
        conn = sqlite3.connect(self.user_db)
        executor = conn.cursor()
        sql = "select * from {0} where sid='{1}'".format(self.vul_table, query["sid"])
        sid_exist = executor.execute(sql).fetchone()
        conn.close()

        if update and not sid_exist:
            return "未找到该特征，请确认更新对象"

        if not update and sid_exist:
            return "该sid不可用"

        # 验证规则中sid的一致性
        try:
            content_sid = re.search(r"sid:\s*(\d+);", feature_content).group(1)
        except Exception:
            return "请检查特征内容"
        if sid != content_sid:
            return "特征ID需保持一致"

        # attack_ip跟victim_ip保持对立
        if query["attackIp"] == query["victimIp"]:
            return "攻击者跟受害者不能为同一ip对象"

        return

    def get_query(self, kwargs):
        owner = Authenticate().get_current_user()[0]
        query = {
            "sid": kwargs["sid"],
            "author": kwargs["author"],
            "appProto": kwargs["appProto"],
            "vulName": kwargs["vulName"],
            "vulType": kwargs["vulType"],
            "cve": kwargs["cve"],
            "lockheedKillchainEN": kwargs["lockheedKillchainEN"],
            "threatFlag": kwargs["threatFlag"],
            "threatScore": int(kwargs["threatScore"]),
            "alterInfo": kwargs["alterInfo"],
            "is0day": kwargs["is0day"],
            "featureContent": kwargs["featureContent"],
            "attackIp": kwargs["attackIp"],
            "victimIp": kwargs["victimIp"],
            "featureStatus": kwargs["featureStatus"],
            "isCustomer": kwargs["isCustomer"],
            "featureGroup": [],
            "owner": owner,
            "control": "user"
        }
        return query

    def feature_content_check(self, feature_content):
        command = DetectProg.Hdp_SigCompile + " --checkRule '%s'" % feature_content
        code = os.system(command) >> 8
        if 0 == code:
            result = "process successfully."
        else:
            result = "process failed."

        return result

    @param_check.check_flask_args(Validator("customer_feature_post"), request)
    def post(self, **kwargs):
        if not kwargs["vulName"]:
            return flask_response("告警名称不能为空", False, {})
        content_str = kwargs["featureContent"].strip()
        if "sid" in content_str:
            return flask_response("自定义特征的特征内容中不需要sid选项字段，请去掉！！", False, {})
        conn = sqlite3.connect(self.user_db)
        executor = conn.cursor()
        sid_arr = executor.execute(
            "SELECT max(cast(sid as integer)) from {0} where isCustomer=1 ".format(self.vul_table)).fetchone()
        sid = 1
        if sid_arr[0]:
            sid = int(sid_arr[0]) + 1
        sid_str = 'sid: ' + str(sid) + ';'
        content = list(content_str)
        content.insert(len(content) - 1, sid_str)
        featureContent = ''.join(content)
        kwargs['sid'] = str(sid)
        kwargs["featureContent"] = featureContent
        query = self.get_query(kwargs)
        feature_content = query["featureContent"].strip()

        # 参数校验
        check_result = self.query_check(query)
        if check_result:
            return flask_response(check_result, False, {})
        # 特征内容正确性验证
        if os.path.exists(KnowledgeNDR.TempPath):
            shutil.rmtree(KnowledgeNDR.TempPath)
        os.makedirs(KnowledgeNDR.TempPath)
        with open(KnowledgeNDR.TempPath + "singleCheck.feature", 'w') as single_check_file:
            # 生成解析规则临时文件
            single_check_file.write(feature_content + '\n')
        ret, msg = run_command(
            '%s --checkPath %s' % (DetectProg.Hdp_SigCompile, KnowledgeNDR.TempPath + "singleCheck.feature"))
        if not ret:
            return flask_response("特征有误，请重新检查", False, {"error": msg[-500:]})
        # 使参数完整
        now_time = int(time.time())
        format_time = str(datetime.fromtimestamp(now_time))
        extra_query = {
            "lockheedKillchainStage": list(GetKillchains.killchain_map.keys()).index(query["lockheedKillchainEN"]) + 1,
            "lockheedKillchainCN": GetKillchains.killchain_map.get(query["lockheedKillchainEN"]),
            "submitTime": format_time.split(" ")[0],
            "updateTime": format_time
        }
        query.update(extra_query)
        # 添加到default_rules特征组
        feat_grp = self.db.find_one('feature_group', {"groupName": "default_allRules"}, {'groupId': 1})
        if not feat_grp:
            return flask_response("特征添加到default_allRules特征组出错，请重新检查", False, {})

        query['featureGroup'].append(feat_grp['groupId'])

        # 添加到sqlite
        try:
            self.update_sqlite(query)
        except Exception as e:
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征添加失败：%s。" % str(e))
            return flask_response(str(e), False, {})

        # 写入mongo库
        self.db.insert_one(self.col, query)
        ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征[%s]添加成功。" % query["sid"])
        cluster_sync_file(self.user_db)
        return flask_response("自定义特征添加成功", True, {})

    @param_check.check_flask_args(Validator("customer_feature"), request)
    def put(self, **kwargs):
        query = self.get_query(kwargs)
        feature = self.db.find_one("feature", {"sid": query["sid"]})
        query["featureStatus"] = feature["featureStatus"]
        query["featureGroup"] = feature["featureGroup"]
        feature_content = query["featureContent"].strip()

        # 参数校验
        check_result = self.query_check(query, update=True)
        if check_result:
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征修改失败：%s。" % check_result)
            return flask_response(check_result, False, {})

        # 特征正确性验证
        check_result = self.feature_content_check(feature_content)
        if "successfully" not in check_result:
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征[%s]修改失败：参数有误。" % query["sid"])
            return flask_response("特征有误，请重新检查", False, {})

        # 使参数完整
        now_time = int(time.time())
        format_time = str(datetime.fromtimestamp(now_time))
        # 保持原先mongo库中规则状态
        feature_content = self.db.find_one(self.col, {"sid": query["sid"]})
        feature_status = feature_content["featureStatus"]
        owner = feature_content["owner"]
        extra_query = {
            "lockheedKillchainStage": list(GetKillchains.killchain_map.keys()).index(query["lockheedKillchainEN"]) + 1,
            "lockheedKillchainCN": GetKillchains.killchain_map.get(query["lockheedKillchainEN"]),
            "featureStatus": feature_status,
            "owner": owner,
            "updateTime": format_time,
            # 不更新提交时间
            # "submitTime": format_time,
        }
        query.update(extra_query)

        # 更新sqlite
        try:
            self.update_sqlite(query, update=True)
        except Exception as e:
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征添加失败：%s。" % str(e))
            return flask_response(str(e), False, {})

        # 更新mongo
        update_data = {
            "$set": query,
        }
        self.db.update_one(self.col, {"sid": query["sid"]}, update_data)

        if feature["featureStatus"] == "enable":
            # 拉起特征
            periodic_task.delay()
            # 先删除原来的规则再添加
            grpc_feature_operate([int(query["sid"])])
            # 获取当前最新的特征数据,确保数据一致
            feature_content = self.db.find_one(self.col, {"sid": query['sid']})
            # hdp修改rule
            with open("/tmp/modifyRule.feature", 'w') as modify_rule_file:
                # 生成解析规则临时文件
                feat_str = get_hdp_feature_str_from_query(feature_content)
                if not feat_str:
                    LOG.error("Feature data not correct, %s", str(feature_content))
                    return flask_response("自定义特征更新失败,数据有误", False, {})
                modify_rule_file.write(feat_str)
            # 生成两个tlv二进制文件，hdp添加rule时需要使用这两个二进制文件
            # （self.tlv_bin_file_prefix + _detect.bin和self.tlv_bin_file_prefix + _describe.bin）
            ret, msg = run_command('%s --featurePath %s --saveTlvPath %s' % (
                DetectProg.Hdp_SigCompile,
                "/tmp/modifyRule.feature",
                self.tlv_bin_file_prefix
            ))
            if not ret:
                return flask_response("特征有误，请重新检查", False, {})

            cluster_sync_file(self.user_db)
            grpc_feature_add(self.tlv_bin_file_prefix + '_detect.bin', self.tlv_bin_file_prefix + '_describe.bin')
            cluster_hdp_feature_modify([int(query["sid"])], self.tlv_bin_file_prefix + '_detect.bin', self.tlv_bin_file_prefix + '_describe.bin')

        ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征[%s]修改成功。" % query['sid'])


        return flask_response("自定义特征更新成功", True, {})

    @param_check.check_flask_args(Validator("customer_delete"), request)
    def delete(self, **kwargs):
        sid = kwargs["sid"]
        # 验证sid是否存在
        sid_exist = self.db.find_one(self.col, {"sid": sid})
        if not sid_exist:
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征[%s]删除失败：不存在。" % sid)
            return flask_response("该特征不存在", False, {})
        if sid_exist["featureStatus"] == "enable":
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征[%s]删除失败：已启用。" % sid)
            return flask_response("请先禁用该特征", False, {})

        # 从mongo删除
        self.db.delete(self.col, {"sid": sid})
        ndr_log_to_box(NdrLog.Type.OPERATE, "自定义特征[%s]删除成功。" % sid)
        # 从db库删除
        conn = sqlite3.connect(self.user_db)
        executor = conn.cursor()
        executor.execute("delete from {0} where sid='{1}'".format(self.vul_table, sid))
        conn.commit()
        conn.close()

        # hdp删除rule
        grpc_feature_operate([int(sid)])
        cluster_hdp_feature_operate([int(sid)])

        cluster_sync_file(self.user_db)
        return flask_response("特征[{0}]删除成功".format(sid), True, {})

    def update_sqlite(self, query, update=False):
        conn = sqlite3.connect(self.user_db)
        executor = conn.cursor()
        score = int(query["threatScore"])
        if score >= 80:
            threat_level = 2
        elif 60 <= score < 80:
            threat_level = 1
        else:
            threat_level = 0

        values = [
            query["sid"],
            query["author"],
            query["appProto"],
            query["vulName"],
            query["vulType"],
            query["cve"],
            query["lockheedKillchainStage"],
            query["lockheedKillchainCN"],
            query["lockheedKillchainEN"],
            query["threatFlag"],
            int(query["threatScore"]),
            query["alterInfo"],
            query["updateTime"],
            query["is0day"],
            query["featureContent"],
            False,
            query["attackIp"],
            query["victimIp"],
            threat_level,
            1,
            query["featureStatus"]
        ]

        # 插入数据
        if not update:
            values.insert(12, query["submitTime"])
        # 更新数据，先删除，后插入
        else:
            sql = "select submitTime from {0} where sid='{1}'".format(self.vul_table, query["sid"])
            submit_time = executor.execute(sql).fetchone()[0]
            values.insert(12, submit_time)
            executor.execute("delete from {0} where sid='{1}'".format(self.vul_table, query["sid"]))

        sql = "insert into vul_rules VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
        executor.execute(sql, values)
        conn.commit()
        conn.close()
