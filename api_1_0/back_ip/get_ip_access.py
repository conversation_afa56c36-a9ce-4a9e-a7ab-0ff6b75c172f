#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
""" 获取 ip 访问路径 """
import ipaddress

from flask import request
from flask_restful import Resource

from api_1_0.utils.flask_log import ndr_log_to_box
from config.config import ClickHouseConfig, NdrLog
from utils.ndr_base import NdrResource
from utils.utils import flask_response
from utils.param_check import Validator, check_flask_args
from utils.logger import get_ndr_logger
from clickhouse_driver import Client as CH_client

ES_LOG = get_ndr_logger('es_log', __file__)
CK_LOG = get_ndr_logger('ck_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class IpAccessRelation(NdrResource):
    """ 获取 ip 访问路径 """

    def __init__(self):
        super(IpAccessRelation, self).__init__(es_template="ip_access_relation")
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)

    def get_val_from_database(self, sql):
        ret_list = list()
        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except:
            CK_LOG.error("ClickHouse数据库连接或执行失败！")
            return ret_list
        if raw is None or not raw[0]:
            return ret_list
        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]
        for i in range(len(raw_val)):
            data = {}
            for idx, item in enumerate(raw_val[i]):
                if isinstance(item, bytes):
                    item = str(item)
                data[raw_key[idx][0]] = item
            ret_list.append(data)
        return ret_list

    @check_flask_args(Validator("ip_access_relation"), request)
    def post(self, **kwargs):
        start_time = int(kwargs["startTime"])
        stop_time = int(kwargs["stopTime"])
        ip_list = kwargs["ip_list"]
        ip = kwargs["ip"]
        time_simp = kwargs["timeType"]
        time_dict = {
            "occurredTime": "ts",
            "observedTime": "create_time"
        }
        time_range = {
            "range": {
                time_simp: {
                    "gte": start_time,
                    "lte": stop_time
                }
            }
        }
        data_return = {"ip": {ip: ""}, "access_ip": {}}
        self.es_template["query"]["bool"]["must"][0] = time_range
        self.es_template["query"]["bool"]["must"][1]["bool"]["should"][0]["match"]["flow.src_ip"] = ip
        self.es_template["query"]["bool"]["must"][1]["bool"]["should"][1]["match"]["flow.dst_ip"] = ip
        try:
            res = self.es_client.search(index="rule-eve,ioc-eve,model-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params %s, reason: %s' % (self.es_template, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "ip访问路径获取失败")
            return flask_response(message, False, {})
        try:
            self.data_format(res, ip, data_return, ip_list)
            if data_return["access_ip"].__len__() < 30:
                time_cond = '%s >= %s and %s <= %s' % (
                    time_dict[time_simp], start_time / 1000, time_dict[time_simp], stop_time / 1000)
                src_ip = "(src_ipv6=toIPv6('%s'))" % ip if ':' in ip else "(src_ipv4=toIPv4('%s'))" % ip
                dst_ip = "(dst_ipv6=toIPv6('%s'))" % ip if ':' in ip else "(dst_ipv4=toIPv4('%s'))" % ip
                ip_cond = "(%s or %s)" % (src_ip, dst_ip)
                sql_str = "select distinct(dst_ip,src_ip) as ip from dpilog_conn where %s and %s" % (
                    time_cond, ip_cond)
                res_list = self.get_val_from_database(sql_str)
                for i in res_list:
                    if ip == i["ip"][0]:
                        if ipaddress.ip_address(i["ip"][1]).is_private and i["ip"][1] not in ip_list \
                                and i["ip"][1] not in data_return["access_ip"].keys():
                            data_return["access_ip"][i["ip"][1]] = "normal"
                    else:
                        if ipaddress.ip_address(i["ip"][0]).is_private and i["ip"][0] not in ip_list \
                                and i["ip"][0] not in data_return["access_ip"].keys():
                            data_return["access_ip"][i["ip"][0]] = "normal"
                    if data_return["access_ip"].__len__() >= 30:
                        break
            if "alarm" in data_return["access_ip"].values():
                data_return["ip"][ip] = "alarm"
            else:
                data_return["ip"][ip] = "normal"
            ndr_log_to_box(NdrLog.Type.OPERATE, "ip访问路径获取成功")
            return flask_response("", True, data_return)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params %s,reason: %s' % (res, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "ip访问路径获取失败")
            return flask_response(err_info, False, {})

    @staticmethod
    def data_format(es_data, ip, data_return, ip_list):
        """
        :param es_data:
        :return:
        """
        for bucket in es_data["aggregations"]["group_id"]["buckets"]:
            data_info = bucket["doc_ids"]["hits"]["hits"][0]["_source"]
            src_ip = data_info["flow"]["src_ip"].strip()
            dst_ip = data_info["flow"]["dst_ip"].strip()
            if ip == src_ip:
                if dst_ip not in ip_list:
                    data_return["access_ip"][dst_ip] = "alarm"
            else:
                if src_ip not in ip_list:
                    data_return["access_ip"][src_ip] = "alarm"
            if data_return["access_ip"].__len__() >= 30:
                break


class IpAccessDetail(Resource):
    """ 获取 ip 访问详情 """

    def __init__(self):
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)

    def get_val_from_database(self, sql):
        ret_list = list()
        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except:
            CK_LOG.error("ClickHouse数据库连接或执行失败！")
            return ret_list
        if raw is None or not raw[0]:
            return ret_list
        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]
        for i in range(len(raw_val)):
            data = {}
            for idx, item in enumerate(raw_val[i]):
                if isinstance(item, bytes):
                    item = str(item)
                data[raw_key[idx][0]] = item
            ret_list.append(data)
        return ret_list

    @check_flask_args(Validator("ip_access_relation"), request)
    def get(self, **kwargs):
        start_time = int(kwargs["startTime"])
        stop_time = int(kwargs["stopTime"])
        ip = kwargs["ip"]
        time_simp = kwargs["timeType"]
        if time_simp == "occurredTime":
            time_cond = 'ts >= %s and ts <= %s' % (start_time / 1000, stop_time / 1000)
        else:
            time_cond = 'create_time >= %s and create_time <= %s' % (start_time / 1000, stop_time / 1000)
        src_ip = "(src_ipv6=toIPv6('%s'))" % ip if ':' in ip else "(src_ipv4=toIPv4('%s'))" % ip
        dst_ip = "(dst_ipv6=toIPv6('%s'))" % ip if ':' in ip else "(dst_ipv4=toIPv4('%s'))" % ip
        dst_str = "select dst_ip, groupUniqArray(dst_port) as port_list, groupUniqArray(application) as app_list " \
                  "from dpilog_conn where %s and %s group by dst_ip " % (time_cond, src_ip)
        src_str = "select src_ip, groupUniqArray(dst_port) as port_list, groupUniqArray(application) as app_list " \
                  "from dpilog_conn where %s and %s group by src_ip " % (time_cond, dst_ip)
        data_return = []
        dst_list = self.get_val_from_database(dst_str)
        src_list = self.get_val_from_database(src_str)
        for i in dst_list:
            dst_data = {"ip": i["dst_ip"],
                        "source": "dst",
                        "ports": i["port_list"],
                        "application": i["app_list"]}
            data_return.append(dst_data)
        for i in src_list:
            src_data = {"ip": i["src_ip"],
                        "source": "src",
                        "ports": i["port_list"],
                        "application": i["app_list"]}
            data_return.append(src_data)
        ndr_log_to_box(NdrLog.Type.OPERATE, "ip访问详情获取成功")
        return flask_response("", True, data_return)
