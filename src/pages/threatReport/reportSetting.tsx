import React, { useEffect, useState } from 'react';
import { reportAltOptions, reportCycleOptions, reportTypeOptions } from '@/utils/enumList';
import { Button, Card, Checkbox, Col, Form, Input, Radio, RadioChangeEvent, Row, message } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

import { getThreatReportConfig, setThreatReportConfig } from '@/services/threatReport';
import { decryptData, encryptData } from './utils';
import { SECRET_KEY } from './hooks';

import style from './index.less'

const { Item } = Form

const allCycleValues = reportCycleOptions.map((option) => option.value)

const reportSetting = () => {
  const [indeterminate, setIndeterminate] = useState(true);
  const [reportSetting, setReportSetting] = useState({});
  const [checkAll, setCheckAll] = useState(false);
  const [loading, setLoading] = useState(false);
  const [encryptDisabled, setEncryptDisabled] = useState<boolean>(false);
  const [radioValues, setRadioValues] = useState({
    isEncrypted: false,
    hasWaterMark: false,
    hasCustomCompany: false,
  });

  const [form] = Form.useForm();


  // 通过返回的报告格式，判断是否只有html，以初始化密码checkbox的状态
  const updateEncryptState = (values: string[]) => {
    if (values.every((type) => type === 'html')) {
      setEncryptDisabled(true)
      form.setFieldsValue({
        password: ''
      })

      setRadioValues((prev) => ({
        ...prev,
        isEncrypted: false,
      }))
    } else {
      setEncryptDisabled(false)
    }
  };

  const onRadioChange = ({ target: { value } }: RadioChangeEvent, key: string) => {
    setRadioValues(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleOnTypeChange = (values: CheckboxValueType[]) => {
    updateEncryptState(values as string[])
  }

  const handleOnCycleChange = (values: CheckboxValueType[]) => {
    setIndeterminate(!!values.length && values.length < allCycleValues.length);
    setCheckAll(values.length === allCycleValues.length);
  };

  const onCheckAllCycleChange = (e: CheckboxChangeEvent) => {
    form.setFieldsValue({
      period: e.target.checked ? allCycleValues : []
    })

    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  const handleSubmit = (values: any) => {
    const { encrypt = '', watermark = '', custom_name = '' } = values

    setThreatReportConfig({
      ...values,
      encrypt: encryptData(encrypt, SECRET_KEY),
      watermark,
      custom_name,
    }).then(({ flag }) => {
      if (flag) {
        message.success(`周期报告设置修改成功`)
      } else {
        message.error(`周期报告设置修改失败`)
      }
    }).catch((reason) => {
      message.error(reason)
    })
  };

  const getConfig = () => {
    setLoading(true);
    getThreatReportConfig()
      .then(({ flag, data, message }) => {
        if (!flag) {
          throw new Error(message);
        }
        const { period, format, encrypt } = data;
        
        setReportSetting({
          ...data,
          encrypt: decryptData(encrypt, SECRET_KEY)
        });

        setIndeterminate(!!period.length && period.length < allCycleValues.length);

        updateEncryptState(format);

        setCheckAll(period.length === allCycleValues.length);

        setRadioValues({
          isEncrypted: !!data.encrypt,
          hasWaterMark: !!data.watermark,
          hasCustomCompany: !!data.custom_name,
        });

        setLoading(false);
      }).catch((reason: string) => {
        message.error(reason);
      });
  };

  useEffect(() => {
    getConfig();
  }, []);

  return (
    <Card
      title="周期报告设置"
      bordered={false}
      loading={loading}
    >
      <Form
        form={form}
        initialValues={reportSetting}
        onFinish={handleSubmit}
      >
        <Row>
          <Col>
            <Item
              label="报告周期"
            >
              <Checkbox
                indeterminate={indeterminate}
                onChange={onCheckAllCycleChange}
                checked={checkAll}
              >
                全选
              </Checkbox>
            </Item>
          </Col>
          <Col>
            <Item
              name="period"
            >
              <Checkbox.Group
                options={reportCycleOptions}
                onChange={handleOnCycleChange}
              />
            </Item>
          </Col>
        </Row>

        <Item
          label="报告格式"
          name="format"
        >
          <Checkbox.Group
            options={reportTypeOptions}
            onChange={handleOnTypeChange}
          />
        </Item>

        <Row>
          <Col >
            <Item
              label="报告加密"
              tooltip={{ title: '加密只针对PDF和Word格式的报告有效', }}
            >
              <Radio.Group
                disabled={encryptDisabled}
                options={reportAltOptions}
                value={radioValues.isEncrypted}
                onChange={(e) => onRadioChange(e, 'isEncrypted')}
              />

            </Item>
          </Col>
          <Col span={10}>
            {
              radioValues.isEncrypted &&
              <Item
                label="密码"
                name="encrypt"
                rules={[{ required: true }]}
              >
                <Input.Password
                  placeholder="请输入密码"
                />
              </Item>
            }
          </Col>
        </Row>

        <Row>
          <Col >
            <Item
              label="水印"
              tooltip={{ title: '水印功能只针对PDF和Word格式的报告有效' }}
            >
              <Radio.Group
                options={reportAltOptions}
                value={radioValues.hasWaterMark}
                onChange={(e) => onRadioChange(e, 'hasWaterMark')}
              />
            </Item>
          </Col>
          <Col span={10}>
            {
              radioValues.hasWaterMark &&
              <Item
                label="水印"
                name="watermark"
                rules={[{ required: true }]}
              >
                <Input
                  placeholder="请输入水印"
                />
              </Item>
            }
          </Col>
        </Row>

        <Row>
          <Col >
            <Item
              label="自定义单位"
            >
              <Radio.Group
                options={reportAltOptions}
                value={radioValues.hasCustomCompany}
                onChange={(e) => onRadioChange(e, 'hasCustomCompany')}
              />
            </Item>
          </Col>
          <Col span={10}>
            {
              radioValues.hasCustomCompany &&
              <Item
                label="单位"
                name="custom_name"
                rules={[{ required: true }]}
              >
                <Input
                  placeholder="请输入单位名称"
                />
              </Item>
            }
          </Col>
        </Row>
        <Item>
          <Button className={style.report_setting_submit_btn} type="primary" htmlType="submit">
            提交
          </Button>
        </Item>
      </Form>
    </Card>
  );
};
export default reportSetting;