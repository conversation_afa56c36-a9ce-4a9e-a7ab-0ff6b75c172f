# -*- coding: utf-8 -*-
# @Time    : 2024-09-03 16:10
# <AUTHOR> wu
# @File    : cert_analyis_task.py
# @Software: PyCharm
import copy
from bson import ObjectId
from datetime import datetime
from celery_tasks import app
from celery import current_task
from utils.database import BaseMongoDB
from utils.es_data_format import cert_alert_get_ipc_info
from clickhouse_driver import Client as CH_client
from config.config import ClickHouseConfig, NdrLog
from api_1_0.thirdparty.cert.hive import HIVE_STATUS_SUCCESS, HIVE_STATUS_FAILED, HIVE_STATUS_RUNNING
from utils.logger import get_ndr_logger

LOG = get_ndr_logger(NdrLog.Type.RUN, __file__)
RETENTION_COL = "cert_hive_retention"


@app.task()
def start_analyis_task(task_name, update_id, data_types: str):
    result = []
    status = HIVE_STATUS_SUCCESS
    mg = BaseMongoDB("ndr")
    sql = ""
    try:
        mg.update(RETENTION_COL, {"name": task_name}, {'celery_id': current_task.request.id})
        LOG.info('Retention task name [%s] start success.' % task_name)

        ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database='hive')

        def get_clickhouse_data(sql):
            raw = ch_client.execute(sql, with_column_types=True)
            # raw_val is column value list
            raw_val = raw[0]
            # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
            raw_key = raw[1]
            ck_raw_list = []
            for i in range(len(raw_val)):
                data = {}
                for idx, item in enumerate(raw_val[i]):
                    if isinstance(item, bytes):
                        item = str(item)
                    data[raw_key[idx][0]] = item
                ck_raw_list.append(data)

            return ck_raw_list

        for data_type in data_types.split(','):
            # sql = f"select * from apt_{data_type} where update_id='{update_id}'"
            # raw = ch_client.execute(sql, with_column_types=True)
            ## raw_val is column value list
            # raw_val = raw[0]
            ## raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
            # raw_key = raw[1]
            # raw_list = []
            # for i in range(len(raw_val)):
            #    data = {}
            #    for idx, item in enumerate(raw_val[i]):
            #        if isinstance(item, bytes):
            #            item = str(item)
            #        data[raw_key[idx][0]] = item
            #    raw_list.append(data)

            if data_type == 'conn':
                sql = "select min(c_stream_time) as start_time, max(c_stream_time) as end_time, " \
                      "c_src_ipv4, groupUniqArray(c_src_port) as src_port, " \
                      "c_dest_ipv4, groupUniqArray(c_dest_port) as dest_port, " \
                      "count(*) as connections, sum(c_up_bytes) as up_bytes, sum(c_down_bytes) as down_bytes " \
                      f"from apt_conn where update_id='{update_id}' group by c_src_ipv4, c_dest_ipv4"
                raw_list = get_clickhouse_data(sql)
                for i in raw_list:
                    result.append({
                        'id': str(ObjectId()),
                        'src_ip': str(i['c_src_ipv4']),
                        'src_ip_city_name': cert_alert_get_ipc_info(str(i['c_src_ipv4'])),
                        'src_port': i['src_port'][:10],
                        'dst_ip': str(i['c_dest_ipv4']),
                        'dst_ip_city_name': cert_alert_get_ipc_info(str(i['c_dest_ipv4'])),
                        'dst_port': i['dest_port'][:10],
                        'start_time': i['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': i['end_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'url': '',
                        'body': '',
                        'connections': i['connections'],
                        'upstream': i['up_bytes'],
                        'downstream': i['down_bytes'],
                        'flag': False
                    })
            elif data_type == 'tls':
                sql = "select t1.start_time, t1.end_time, t1.c_src_ipv4, t1.src_port, t1.c_dest_ipv4, " \
                      "t1.dest_port, t1.connections, IFNULL(t2.up_bytes, 0) as up_bytes, " \
                      "IFNULL(t2.down_bytes, 0) as down_bytes " \
                      "from (" \
                      "select min(c_time) as start_time, max(c_time) as end_time, " \
                      "c_src_ipv4, groupUniqArray(c_src_port) as src_port, " \
                      "c_dest_ipv4, groupUniqArray(c_dest_port) as dest_port, count(*) as connections " \
                      f"from apt_tls where update_id='{update_id}' " \
                      "group by c_src_ipv4, c_dest_ipv4) as t1 " \
                      "left join (" \
                      "select c_src_ipv4, c_dest_ipv4, sum(c_up_bytes) as up_bytes, sum(c_down_bytes) as down_bytes " \
                      f"from apt_conn where update_id='{update_id}' " \
                      "group by c_src_ipv4, c_dest_ipv4) as t2 " \
                      "on t1.c_src_ipv4=t2.c_src_ipv4 and t1.c_dest_ipv4=t2.c_dest_ipv4"
                raw_list = get_clickhouse_data(sql)
                for i in raw_list:
                    result.append({
                        'id': str(ObjectId()),
                        'src_ip': str(i['c_src_ipv4']),
                        'src_ip_city_name': cert_alert_get_ipc_info(str(i['c_src_ipv4'])),
                        'src_port': i['src_port'][:10],
                        'dst_ip': str(i['c_dest_ipv4']),
                        'dst_ip_city_name': cert_alert_get_ipc_info(str(i['c_dest_ipv4'])),
                        'dst_port': i['dest_port'][:10],
                        'start_time': i['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': i['end_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'url': '',
                        'body': '',
                        'connections': i['connections'],
                        'upstream': i['up_bytes'],
                        'downstream': i['down_bytes'],
                        'flag': False
                    })
            elif data_type == 'dns':
                sql = "select t1.start_time, t1.end_time, t1.c_src_ipv4, t1.src_port, t1.c_dest_ipv4, " \
                      "t1.connections, IFNULL(t2.up_bytes, 0) as up_bytes, " \
                      "IFNULL(t2.down_bytes, 0) as down_bytes " \
                      "from (" \
                      "select min(c_time) as start_time, max(c_time) as end_time, " \
                      "c_sip as c_src_ipv4, groupUniqArray(c_sport) as src_port, " \
                      "c_dip as c_dest_ipv4, count(*) as connections " \
                      f"from apt_dns where update_id='{update_id}' " \
                      "group by c_src_ipv4, c_dest_ipv4) as t1 " \
                      "left join (" \
                      "select c_src_ipv4, c_dest_ipv4, sum(c_up_bytes) as up_bytes, sum(c_down_bytes) as down_bytes " \
                      f"from apt_conn where update_id='{update_id}' " \
                      "group by c_src_ipv4, c_dest_ipv4) as t2 " \
                      "on t1.c_src_ipv4=t2.c_src_ipv4 and t1.c_dest_ipv4=t2.c_dest_ipv4"
                raw_list = get_clickhouse_data(sql)
                for i in raw_list:
                    result.append({
                        'id': str(ObjectId()),
                        'src_ip': str(i['c_src_ipv4']),
                        'src_ip_city_name': cert_alert_get_ipc_info(str(i['c_src_ipv4'])),
                        'src_port': i['src_port'][:10],
                        'dst_ip': str(i['c_dest_ipv4']),
                        'dst_ip_city_name': cert_alert_get_ipc_info(str(i['c_dest_ipv4'])),
                        'dst_port': [],
                        'start_time': i['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': i['end_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'url': '',
                        'body': '',
                        'connections': i['connections'],
                        'upstream': i['up_bytes'],
                        'downstream': i['down_bytes'],
                        'flag': False
                    })
            elif data_type == 'url':
                sql = "select t1.start_time, t1.end_time, t1.c_src_ipv4, t1.src_port, t1.c_dest_ipv4, " \
                      "t1.dest_port, t1.uri, t1.body, t1.connections, IFNULL(t2.up_bytes, 0) as up_bytes, " \
                      "IFNULL(t2.down_bytes, 0) as down_bytes " \
                      "from (" \
                      "select min(c_time) as start_time, max(c_time) as end_time, " \
                      "c_src_ipv4, groupUniqArray(c_src_port) as src_port, " \
                      "c_dest_ipv4, groupUniqArray(c_dest_port) as dest_port, " \
                      "any(url_body_tuple.1) as uri, any(url_body_tuple.2) as body, count(*) as connections " \
                      "from (" \
                      "select c_time, c_src_ipv4, c_src_port, c_dest_ipv4, c_dest_port, " \
                      "tuple(c_uri, c_req_body) as url_body_tuple " \
                      f"from apt_url where update_id='{update_id}') " \
                      "group by c_src_ipv4, c_dest_ipv4) as t1 " \
                      "left join (" \
                      "select c_src_ipv4, c_dest_ipv4, " \
                      "sum(c_up_bytes) as up_bytes, sum(c_down_bytes) as down_bytes " \
                      f"from apt_conn where update_id='{update_id}' " \
                      "group by c_src_ipv4, c_dest_ipv4) as t2 " \
                      "on t1.c_src_ipv4=t2.c_src_ipv4 and t1.c_dest_ipv4=t2.c_dest_ipv4"
                raw_list = get_clickhouse_data(sql)
                for i in raw_list:
                    result.append({
                        'id': str(ObjectId()),
                        'src_ip': str(i['c_src_ipv4']),
                        'src_ip_city_name': cert_alert_get_ipc_info(str(i['c_src_ipv4'])),
                        'src_port': i['src_port'][:10],
                        'dst_ip': str(i['c_dest_ipv4']),
                        'dst_ip_city_name': cert_alert_get_ipc_info(str(i['c_dest_ipv4'])),
                        'dst_port': i['dest_port'][:10],
                        'start_time': i['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': i['end_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        'url': i['uri'],
                        'body': i['body'],
                        'connections': i['connections'],
                        'upstream': i['up_bytes'],
                        'downstream': i['down_bytes'],
                        'flag': False
                    })
            else:
                raise Exception('ERROR: can not support data type: %s' % data_type)

        ch_client.disconnect()
        LOG.info(sql)
        LOG.info('Retention task name [%s] excute success.' % task_name)
    except Exception as e:
        status = HIVE_STATUS_FAILED
        result = []
        LOG.error('Retention task name [%s] start failed! Reason: %s' % (task_name, str(e)))
    finally:
        for i in result:
            mg.update_one(RETENTION_COL, {"name": task_name}, {'$push': {'result': i}})

        mg.update(RETENTION_COL, {"name": task_name}, {
            'status': status,
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        mg.close()


@app.task()
def stop_analyis_task(task_name):
    try:
        mg = BaseMongoDB("ndr")

        mg_data = mg.find_one(RETENTION_COL, {'name': task_name})
        if not mg_data:
            LOG.error('Retention task name [%s] not exists!' % task_name)
            return

        if mg_data['status'] != HIVE_STATUS_RUNNING or mg_data['celery_id']:
            LOG.error('Retention task name [%s] already stopped!' % task_name)
            return

        app.control.revoke(mg_data['celery_id'], terminate=True)

        mg.update(RETENTION_COL, {"name": task_name}, {
            'status': HIVE_STATUS_SUCCESS,
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        mg.close()

        LOG.info('Retention task name [%s] stop success.' % task_name)
    except Exception as e:
        LOG.error('Retention task name [%s] stop failed! Reason: %s' % (task_name, str(e)))
