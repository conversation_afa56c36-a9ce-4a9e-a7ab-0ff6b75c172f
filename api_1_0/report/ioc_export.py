#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import time
from flask import request, send_file, make_response
import pandas as pd
from flask import request

from utils.es_data_format import es_data_export
from utils.ndr_base import NdrResource
from utils import param_check
from config.config import RULE_INDEX, IOC_INDEX
from utils.param_check import Validator
from utils.es_function import *
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from utils.database import MongoDB

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class IocExport(NdrResource):
    """ 获取情报列表 """

    field_temp = {
        "celeryId": "celeryId.keyword",
        "threatLevel": "threatLevel.keyword",
        "confirm": "confirm"
    }

    def __init__(self):
        super(IocExport, self).__init__(es_template="ioc_list")
        self.mongodb = MongoDB("ndr")
        self.excel_file_path = ''
        self.export_data = []

    @param_check.check_flask_args(Validator('ioc_list'), request)
    def post(self, **kwargs):
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        self.excel_file_path = '/tmp/ioc_report_%s.xlsx' % time_str
        es_index = get_es_index(IOC_INDEX, int(kwargs["startTime"]), int(kwargs["stopTime"]))
        es_index_list = []
        es_index_not = []
        for i in es_index:
            # 判断是否存在索引
            if self.es_client.indices.exists(i):
                es_index_list.append(i)
            else:
                es_index_not.append(i)
        if not es_index_list:
            return flask_response('index[%s] not exist' % str(es_index_not), True, {})
        option_args = {
            "celeryId": kwargs["celeryId"],
            "threatLevel": kwargs["threatLevel"],
            "threatScore": kwargs["threatScore"],
            "mbinfo": kwargs["mbinfo"],
            "threatType": kwargs["threatType"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        self.format_es_temp(option_args)
        es_template = get_es_template(self.es_template, kwargs)
        if not kwargs["groupKey"]:
            es_template['from'] = 0
            es_template['size'] = 50000
        elif kwargs["groupKey"] == "uniqueId":
            es_template['aggs']['group_id']['aggs']['bucket_truncate']['bucket_sort']['from'] = 0
            es_template['aggs']['group_id']['aggs']['bucket_truncate']['bucket_sort']['size'] = 50000
            es_template['aggs']['group_id']['aggs']['start_time'] = {"min": {"field": "observedTime"}}
        else:
            es_template['aggs']['group_id']['aggs']['bucket_truncate']['bucket_sort']['from'] = 0
            es_template['aggs']['group_id']['aggs']['bucket_truncate']['bucket_sort']['size'] = 50000
        try:
            res = self.es_client.search(index=es_index_list, body=es_template, request_timeout=60)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            return flask_response(message, False, {})
        try:
            self.export_data = es_data_export(kwargs["groupKey"], res, self.mongodb)
            self.export_excel()
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})
        return make_response(send_file(self.excel_file_path, as_attachment=True))

    def format_es_temp(self, option_args):
        """
        :param option_args:
        :return:
        """
        es_must_list = []
        for key, value in option_args.items():
            if value in ["", None]:
                continue
            if key == "mbinfo":
                query = get_mbinfo(option_args["mbinfo"])
                self.es_template["query"]["bool"]["must"].append(query)
            elif key == "threatScore":
                threat_score_start, threat_score_stop = value.split('-')
                self.es_template["query"]["bool"]["must"].append(
                    get_range("threatScore", int(threat_score_start), int(threat_score_stop)))
            elif key == "threatType":
                query = get_contain(key, value)
                self.es_template["query"]["bool"]["must"].append(query)
            else:
                search_args = {self.field_temp[key]: value}
                args_format = {"term": search_args}
                es_must_list.append(args_format)
        self.es_template["query"]["bool"]["must"].extend(es_must_list)

    def export_excel(self):
        data = pd.DataFrame(self.export_data)
        writer = pd.ExcelWriter(self.excel_file_path)  # 写入Excel文件
        data.to_excel(writer)
        writer.save()
