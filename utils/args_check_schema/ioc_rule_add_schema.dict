{
    "type": "object",
    "required": ["ioc_type", "ioc"],
    "properties": {
       "threat_type": {
            "type": "string",
            "default": ""
        },
        "ioc": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,64}$"
        },
        "ioc_type": {
	         "type": "string",
	         "enum": ["ip", "domain"],
	         "default": ""
        },
        "discoverytime": {
            "type": "string",
            "default": "",
	        "pattern": r"^\d{12,13}$"
        },
        "protocols": {
            "type": "string",
            "default": ""
        },
        "ports": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,64}$"
        },
        "tools": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,512}$"
        },
        "labels": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,512}$"
        },
        "score": {
            "type": "integer",
            "default": 0,
	        "minimum": 0,
	        "maximum": 100
        },
        "organizations": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,64}$"
        },
        "reference": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,512}$"
        },
        "killchainstage": {
            "type": "string",
            "enum": ["Reconnaissance", "Weaponization", "Delivery", "Exploitation", "Installation", "Command and Control", "Actions on Objective"],
            "default": ""
        }
    }
}