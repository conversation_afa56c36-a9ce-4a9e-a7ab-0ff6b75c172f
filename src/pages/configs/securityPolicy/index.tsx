/*
 * @Author: tianh
 * @Date: 2021-11-18 17:37:15
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-30 11:51:09
 * @Descripttion:
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  Table,
  Popconfirm,
  message,
  Upload,
  Modal,
  Progress,
  Switch,
  Tooltip,
} from 'antd';
import { useHistory } from '@/hooks/global';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { QuestionCircleOutlined, UploadOutlined, DownloadOutlined   } from '@ant-design/icons';
import {
  getsecurityPolicyList,
  editsecurityPolicy,
  sortsecurityPolicy,
  deletesecurityPolicy,
  exportSecurityPolicy,
} from '@/services/configs';
import style from './style.less';
import moment from 'moment';
import update from 'immutability-helper';
import { ellipsis, handleEmpty } from '@/utils/utils';

const Index = () => {
  const ref = useRef();
  const type = 'DraggableBodyRow';
  const history = useHistory();
  const [uploadVisible, setuploadVisible] = useState(false);
  const [dataSource, setdataSource] = useState<any>([]);
  const [selectedRows, setselectedRows] = useState([]);
  const [defaultPercent, setdefaultPercent] = useState(0);
  const [params, setparams] = useState({});
  const [visible, setvisible] = useState(false);
  const [detail, setdetail] = useState<any>({});
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>(
    'checkbox',
  );
  const columns = [
    {
      title: '策略名',
      dataIndex: 'policy_name',
    },
    {
      title: '源接口',
      dataIndex: 'source_if_list',
      render: (t: any, record: any) => {
        if (t && t.length) {
          return t.toString();
        } else if (record.policy_name === 'default_policy') {
          return 'all';
        }
      },
    },
    {
      title: '源主机',
      dataIndex: 'source_host_ip',
      render: (t: any) => {
        return <Tooltip title={t.toString()}>{ellipsis(t.toString())}</Tooltip>;
      },
    },
    {
      title: '特征组',
      dataIndex: 'rule_groups',
      render: (t: any) => {
        if (t) {
          return t.toString();
        }
      },
    },
    {
      title: '情报',
      dataIndex: 'ioc_enable',
      render: (t: boolean) => {
        return t ? '开' : '关';
      },
    },
    {
      title: '模型',
      dataIndex: 'model_enable',
      render: (t: boolean) => {
        return t ? '开' : '关';
      },
    },
    {
      title: '响应动作',
      dataIndex: 'state',
      render: (t: boolean, record: any) => {
        if (!record.email.is_enable && !record.syslog.is_enable) {
          return '日志告警';
        } else {
          let str = `日志告警  ${record.syslog.log_trigger_ip && record.syslog.log_trigger_ip.length
            ? `\xa0\xa0\|\xa0\xa0\ Syslog for [${record.syslog.log_trigger_ip.toString()}]`
            : ''
            } ${record.email.log_trigger_ip && record.email.log_trigger_ip.length
              ? `\xa0\xa0\|\xa0\xa0\ Email for [${record.email.log_trigger_ip.toString()}]`
              : ''
            }
`;
          return <Tooltip title={str}>{ellipsis(str)}</Tooltip>;
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      render: (t: any, record: any) => {
        return (
          <Switch
            onChange={() => {
              changeState(record);
            }}
            disabled={record.policy_name === 'default_policy'}
            checked={t === 'disable' ? false : true}
          />
        );
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
    },

    {
      title: '操作',
      dataIndex: '',
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              type="link"
              disabled={
                record.policy_name !== 'default_policy' &&
                record.state === 'enable'
              }
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                showDetail(record);
              }}
            >
              查看详情
            </Button>
          </div>
        );
      },
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys: [], selectedRows: any) => {
      setselectedRows(selectedRows);
    },
    getCheckboxProps(record: any) {
      if (record.policy_name === 'default_policy') {
        return {
          disabled: true,
        };
      }
    },

    selectedRows,
  };
  const handleAdd = () => {
    sessionStorage.setItem('securityPolicyStatus', 'add');
    history.push(`/app/mica/securityPolicyForm`);
  };
  const handleDelete = () => {
    if (!selectedRows || !selectedRows.length) {
      return '请选择数据';
    }
    let arr = selectedRows.map((item: any) => {
      return item.unique_id;
    });
    deletesecurityPolicy({ unique_id_list: arr }).then(res => {
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  const showDetail = (value: any) => {
    setdetail(value);
    setvisible(true);
  };
  const changeState = (value: any) => {
    // value.action = 'set';
    console.log(value);
    let data = {
      action: 'set',
      unique_id: value.unique_id,
      state: value.state === 'enable' ? 'disable' : 'enable',
    };

    editsecurityPolicy(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  const sortData = (value: any) => {
    for (let i = 0; i < value.length; i++) {
      value[i].priority = value.length - i - 1;
    }
    console.log(value);
    let arr = value.map((item: any) => {
      return {
        unique_id: item.unique_id,
        priority: item.priority,
      };
    });
    sortsecurityPolicy({
      priority_list: arr,
    }).then(res => {
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  const exportJson = () => {
    exportSecurityPolicy().then(res => {
      if (res.flag) {
        //将JSON 对象转换为字符串并格式化
        let dataJson = JSON.stringify(res.data, null, 4);
        // let num = parseInt((Math.random() + 1) * Math.pow(10, 10 - 1));
        message.success(res.message);
        downJson(dataJson);
      }
    });
  };
  const handleEdit = (value: any) => {
    if (value.policy_name === 'default_policy') {
      value.source_if_list = ['all'];
    }

    sessionStorage.setItem('securityPolicyStatus', 'edit');
    sessionStorage.setItem('securityPolicyForm', JSON.stringify(value));
    history.push(`/app/mica/securityPolicyForm`);
  };
  const getList = () => {
    getsecurityPolicyList(params).then(res => {
      if (res.flag) {
        setselectedRows([]);
        setdataSource(res.data);
      }
    });
  };
  const handleDown = (value: any) => {
    let index = value.priority;
    console.log(index);
    let temp = dataSource[dataSource.length - index - 1];
    dataSource[dataSource.length - index - 1] =
      dataSource[dataSource.length - index];
    dataSource[dataSource.length - index] = temp;
    setdataSource([...dataSource]);
    for (let i = 0; i < dataSource.length; i++) {
      dataSource[i].priority = dataSource.length - i - 1;
    }
    setdataSource(dataSource);
    sortsecurityPolicy({
      priority_list: dataSource,
    }).then(res => {
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleUp = (value: any) => {
    let index = value.priority;
    let temp = dataSource[dataSource.length - index - 2];
    dataSource[dataSource.length - index - 2] =
      dataSource[dataSource.length - index - 1];
    dataSource[dataSource.length - index - 1] = temp;
    console.log(dataSource);

    for (let i = 0; i < dataSource.length; i++) {
      dataSource[i].priority = dataSource.length - i - 1;
    }
    setdataSource(dataSource);
  };
  const DraggableBodyRow = ({
    index,
    moveRow,
    className,
    style,
    ...restProps
  }) => {
    const ref = useRef();
    const [{ isOver, dropClassName }, drop] = useDrop({
      accept: type,
      collect: monitor => {
        const { index: dragIndex } = monitor.getItem() || {};
        if (dragIndex === index) {
          return {};
        }
        return {
          isOver: monitor.isOver(),
          dropClassName:
            dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
        };
      },
      drop: (item: any) => {
        if (dataSource[item.index].policy_name === 'default_policy') {
          return;
        }
        console.log(index);
        if (index === dataSource.length - 1) {
          return;
        }
        moveRow(item.index, index);
        // let arr = dataSource.map((item: any) => {
        //   return {
        //     unique_id: item.unique_id,
        //     priority: item.priority,
        //     policy_name: item.policy_name,
        //   };
        // });
        // let first = { ...arr[index] };
        // let second = { ...arr[item.index] };
        // arr[index] = second;
        // arr[item.index] = first;

        // for (let i = 0; i < arr.length; i++) {
        //   arr[i].priority = arr.length - i - 1;
        // }
        // sortsecurityPolicy({ priority_list: arr }).then(res => {
        //   if (res.flag) {
        //     message.success(res.message);
        //     getList();
        //   } else {
        //     message.error(res.message);
        //   }
        // });
      },
    });
    const [, drag] = useDrag({
      type,
      item: { index },
      collect: monitor => ({
        isDragging: monitor.isDragging(),
      }),
    });
    drop(drag(ref));

    return (
      <tr
        ref={ref}
        className={`${className}${isOver ? dropClassName : ''}`}
        style={{ cursor: 'move', ...style }}
        {...restProps}
      />
    );
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      const dragRow = dataSource[dragIndex];

      setdataSource(
        update(dataSource, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow],
          ],
        }),
      );

      sortData(
        update(dataSource, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow],
          ],
        }),
      );
    },
    [dataSource],
  );
  // 关闭进度条
  const toggleVisibleStatusFalse = () => {
    setuploadVisible(false);
  };
  // 展示进度条
  const toggleVisibleStatusTrue = (percent: any) => {
    setuploadVisible(true);
    setdefaultPercent(percent);
  };
  const downJson = (content: any) => {
    console.log(content);
    // 下载文件方法
    let eleLink = document.createElement('a');
    let date = moment().format('YYYY-MM-DD');
    eleLink.download = `ks_ndr_security_policy_${date}` + '.json';
    eleLink.style.display = 'none';
    // 字符内容转变成blob地址
    let blob = new Blob([content]);
    eleLink.href = URL.createObjectURL(blob);
    // 触发点击
    document.body.appendChild(eleLink);
    eleLink.click();
    // 然后移除
  };
  useEffect(() => {
    getList();
  }, []);
  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const importProps = {
    showUploadList: false,
    name: 'fileName',
    onChange: (info: {
      file: {
        status: string;
        percent: number;
        response: { flag: any; message: any };
      };
    }) => {
      if (info.file.status === 'uploading') {
        toggleVisibleStatusTrue(Math.floor(info.file.percent));
      } else if (!info.file.response.flag) {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
        return;
      } else if (info.file.status === 'done') {
        setTimeout(() => {
          message.success(
            `上传成功,${info.file.response.message}` || '上传成功',
          );
          toggleVisibleStatusFalse();
          getList();
        }, 3000);
      } else if (info.file.status === 'error') {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
      }
    },
    beforeUpload: (file: any) => {
      let pos = file.name.lastIndexOf('.');
      let result = file.name.substring(pos + 1, file.name.length);

      if (!result.includes('json')) {
        message.error('仅支持上传json格式的文件');
        return Upload.LIST_IGNORE;
      }
    },
    action: '/mica-api/api/v1/security_policy/import',
  };

  return (
    <div>
      <div className="p-4 bg-d0 b b-solid b-brd6">
        <Button type="primary" onClick={handleAdd} ghost className='margin_right_10'>
          添加
        </Button>
        <Popconfirm title="确定删除吗?" onConfirm={handleDelete}>
          <Button type="primary"  ghost danger className='margin_right_10'>
            删除
          </Button>
        </Popconfirm>
        <Upload accept=".json" {...importProps}>
          <Button
            type="primary"
          // onClick={handleImport}
          className='margin_right_10'
          icon={<UploadOutlined />}
          ghost
          >
            导入
          </Button>
        </Upload>
        <Button type="primary" onClick={exportJson} icon={<DownloadOutlined />} ghost>
          导出
        </Button>
      </div>
      <div className="w-100% mt-4">
        <DndProvider backend={HTML5Backend}>
          <Table
            rowKey="unique_id"
            columns={columns}
            dataSource={dataSource}
            components={components}
            pagination={false}
            rowSelection={{
              type: selectionType,
              ...rowSelection,
            }}
            onRow={(record, index) => ({
              index,
              moveRow,
            })}
          />
        </DndProvider>
      </div>
      <Modal
        destroyOnClose={true}
        title="文件正在上传..."
        visible={uploadVisible}
        maskClosable={false}
        keyboard={false}
        footer={null}
        closable={false}
      >
        <Progress percent={defaultPercent} status="active" />
      </Modal>
      <Modal
        title="查看详情"
        onCancel={() => {
          setvisible(false);
        }}
        onOk={() => {
          setvisible(false);
        }}
        destroyOnClose={true}
        maskClosable={false}
        visible={visible}
      >
        {visible ? (
          <div className={style.detail}>
            <p>
              <span className={style.title}>策略名：</span>
              {detail.policy_name}
            </p>
            <div>
              <span className={style.title}>检测源配置：</span>
              <p className={style.indent_title}>
                源接口：
                {detail.policy_name === 'default_policy' ? 'all' : detail.source_if_list.length ? detail.source_if_list.toString() : '-'}
              </p>
              <p className={style.indent_title}>
                源主机：
                {detail.source_host_ip.length ? detail.source_host_ip.toString() : '-'}
              </p>
            </div>
            <div>
              <span className={style.title}>安全能力集配置：</span>
              <p className={style.indent_title}>
                规则组：
                {detail.rule_groups.length ? detail.rule_groups.toString() : '-'}
              </p>
              <p className={style.indent_title}>情报匹配：{detail.ioc_enable ? 'Yes' : 'No'}</p>
              <p className={style.indent_title}>模型匹配：{detail.model_enable ? 'Yes' : 'No'}</p>
              <p className={style.indent_title}>文件匹配：{detail.file_enable ? 'Yes' : 'No'}</p>
            </div>
            <div>
              <span className={style.title}>响应动作配置：</span>
              <p className={style.indent_title}>
                威胁日志告警输出：{detail.threat_log ? 'Yes' : 'No'}
              </p>
              <p className={style.indent_title}>
                威胁告警邮件输出：{detail.email.is_enable ? 'Yes' : 'No'}
              </p>
              <p className={style.indent_title_4}>
                关联ip：
                {detail.email.log_trigger_ip.length ? detail.email.log_trigger_ip.toString() : '-'}
              </p>
              <p className={style.indent_title_4}> SMTP服务器地址：{handleEmpty(detail.email.mail_server)}</p>
              <p className={style.indent_title_4}> SMTP端口：{handleEmpty(detail.email.server_port)}</p>
              <p className={style.indent_title_4}> 邮箱账号：{handleEmpty(detail.email.account)}</p>
              <p className={style.indent_title_4}> 邮箱密码：{detail.email.password ? '******' : '-'}</p>
              <p className={style.indent_title_4}> 收件人：{handleEmpty(detail.email.receiver)}</p>

              <p className={style.indent_title}>
                威胁告警SysLog输出：{detail.syslog.is_enable ? 'Yes' : 'No'}
              </p>
              <p className={style.indent_title_4}>
                关联ip：
                {detail.syslog.log_trigger_ip ? detail.syslog.log_trigger_ip.toString() : '-'}
              </p>
              <p className={style.indent_title_4}> Syslog服务器IP：{handleEmpty(detail.syslog.syslog_server)}</p>
              <p className={style.indent_title_4}> Syslog服务器端口：{handleEmpty(detail.syslog.server_port)}</p>
              <p className={style.indent_title}>
                <span>
                  联动阻断
                  <Tooltip title="仅支持与威胁情报网关进行联动阻断">
                    <QuestionCircleOutlined style={{fontSize: '16px', display: 'inline'}}/>
                  </Tooltip>
                </span>：{detail.threat_gw.is_enable ? 'Yes' : 'No'}
              </p>
              <p className={style.indent_title_4}> 威胁情报网关ip：{handleEmpty(detail.threat_gw.server)}</p>
              <p className={style.indent_title_4}> token：{handleEmpty(detail.threat_gw.token)}</p>
            </div>
          </div>
        ) : null}
      </Modal>
    </div>
  );
};
export default Index;
