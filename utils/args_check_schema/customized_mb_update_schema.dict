{
    "type": "object",
    "required": ["ip"],
    "properties": {
        "ip": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,128}$"
        },
        "domain": {
            "type": "string",
            "default": ""
        },
        "assetsName": {
            "type": "string",
            "default": ""
        },
        "assetsType": {
            "type": "string",
            "default": ""
        },
        "organisation": {
            "type": "string",
            "default": ""
        },
        "industry": {
            "type": "string",
            "default": ""
        },
        "country": {
            "type": "string",
            "default": ""
        },
        "address": {
            "type": "string",
            "default": ""
        },
        "components": {
            "type": "string",
            "default": ""
        },
        "other": {
            "type": "string",
            "default": ""
        }
    }
}