import { useHistory } from '@/hooks/global';
import { <PERSON>ton, Tooltip, Table } from 'antd';
import React, { useState, useEffect } from 'react';
import { isEmpty, get, pick } from 'lodash';
import { getFileReport, getFileExReport } from '@/services/collect';
import moment from 'moment';
import cn from 'classnames';
import s from './style.less';
import TablePart from './tablePart';
import { THREAT_LEVEL_FILE, FILE_COLOR } from '@/utils/constants';
import { Link } from 'umi';

interface FileListProps {
  className?: string;
  params: any;
}

const defaultParams = { sort: 'desc', page: 1, pageSize: 10 }

const FileList = ({ className, params }: FileListProps) => {
  const history = useHistory();
  const [filter, setFilter] = useState(defaultParams);
  const [tableData, setTableData] = useState({ data: [], total: 0 });
  const handleChange = (pagination: any, filters: any, sorter: any) => {
    const { current, pageSize } = pagination;
    const sort = sorter.order === 'descend' ? 'desc' : 'asc';
    setFilter({ ...filter, sort, pageSize, page: current });
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'observedTime',
      key: 'observedTime',
      showSorterTooltip: false,
      sorter: true,
      sortOrder: filter.sort === 'desc' ? 'descend' : 'ascend',
      render(value, row) {
        const observedTime = get(row, '_source.observedTime');
        return observedTime ? moment(observedTime).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '应用协议',
      dataIndex: 'proto',
      key: 'proto',
      render(value, row) {
        const proto = get(row, '_source.flow.proto');
        return proto;
      },
    },
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      render(value, row) {
        // const filename = get(row, '_source.filename', '').split('__');
        const filename = get(row, '_source.filename', '');
        return (
          <Tooltip title={filename}>
            <p className={cn(s.tOver, 'w-200px')}>{filename}</p>
          </Tooltip>
        );
      },
    },
    {
      title: '文件类型',
      dataIndex: 'filetype',
      key: 'filetype',
      render(value, row) {
        const filetype = get(row, '_source.filetype');
        return filetype;
      },
    },
    {
      title: '目的IP',
      dataIndex: 'dst_ip',
      key: 'dst_ip',
      render(value, row) {
        const ip = get(row, '_source.flow.dst_ip');
        return (
          <Tooltip title={ip}>
            <p className={cn(s.tOver, 'w-100px')}>{ip}</p>
          </Tooltip>
        );
      },
    },
    {
      title: '源IP',
      dataIndex: 'src_ip',
      key: 'src_ip',
      render(value, row) {
        const ip = get(row, '_source.flow.src_ip');
        return (
          <Tooltip title={ip}>
            <p className={cn(s.tOver, 'w-100px')}>{ip}</p>
          </Tooltip>
        );
      },
    },
    {
      title: '威胁等级',
      dataIndex: 'threatLevel',
      key: 'threatLevel',
      render(value, row) {
        const threatLevel = get(row, '_source.threatLevel');
        const color = FILE_COLOR[threatLevel];
        return (
          <div
            style={{ color, borderColor: color, backgroundColor: `${color}1A` }}
            className="inline-block bg-op-20 b b-solid rd !text-center w-15 lh-6"
          >
            {THREAT_LEVEL_FILE[threatLevel]}
          </div>
        );
      },
    },
    {
      title: '文件报告',
      dataIndex: 'threatLevel',
      key: 'threatLevel',
      render(e, row) {
        const source = get(row, '_source');
        const obj = pick(source, [
          'pcap_filename',
          'labels',
          'reportPath',
          'md5',
          'filename',
          'file_path',
          'filesize',
          'crc32',
          'sha256',
          'killchain',
          'filetype',
          'threatLevel',
          'flow.src_ip',
          'flow.dst_ip',
          'dstIpGeoInfo.latitude',
          'dstIpGeoInfo.longitude',
          'dstIpGeoInfo.city_name',
          'srcIpGeoInfo.latitude',
          'srcIpGeoInfo.longitude',
          'srcIpGeoInfo.city_name',
          'virus_name.knownsec',
          'virus_name.clamav',
          'threatName',
        ]);
        return obj.threatLevel === 'Safe' ? (
          <Tooltip title={'安全文件报告未保存'}>
            <span className="c-t4 cursor-not-allowed">查看报告</span>
          </Tooltip>
        ) : (
          <Link to={`/app/mica/collect/fileDetail?params=${JSON.stringify(obj)}`} target="_blank" className="!px-0" >
            <span>查看报告</span>
          </Link>
        );
      },
    },
  ];
  const getData = async () => {
    const res = await getFileReport({ ...params, ...filter });
    if (res.flag) {
      const { detail, total } = res.data;
      const data = detail.map(e => {
        e.key = e._id;
        return e;
      });
      setTableData({ data, total });
    }
  };
  const downFun = async () => {
    const info = { ...params, ...filter };
    const url = getFileExReport(info);
    window.location.href = `${window.location.origin}${url}`;
  };
  useEffect(() => {
    setFilter({ ...params, ...defaultParams });
  }, [JSON.stringify(params)]);
  useEffect(() => {
    !isEmpty(filter) && getData();
  }, [JSON.stringify(filter)]);
  return (
    <div className={cn('bg-d0 pa-2', s.fileList, className)}>
      <p className="c-t4 flex justify-between ">
        <span>文件报告列表</span>
        <Button
          onClick={() => {
            downFun();
          }}
        >
          导出
        </Button>
      </p>
      <Table
        expandable={{
          expandedRowRender: row => <TablePart row={row} />,
        }}
        columns={columns}
        dataSource={tableData.data}
        onChange={handleChange}
        pagination={{
          size: 'small',
          current: filter.page,
          pageSize: filter.pageSize,
          total: tableData.total,
          showQuickJumper: true,
          showSizeChanger: true
        }}
      />
    </div>
  );
};
export default FileList;
