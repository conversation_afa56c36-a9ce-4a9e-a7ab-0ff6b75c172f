import redis
import psutil
from celery import Celery
from config.config import RedisConfig
from concurrent.futures import Thread<PERSON>oolExecutor
from multiprocessing import Queue as MutiQueue


# init redis replay virtual card queue
def init_virtual_card():
    """
    :return:
    """
    redis_pool = redis.ConnectionPool(host=RedisConfig.Host, port=RedisConfig.Port, db=5, password=RedisConfig.Password)
    client = redis.Redis(connection_pool=redis_pool)
    info = psutil.net_if_addrs()
    client.delete("card_name")
    for key, value in info.items():
        if "tap" in key:
            client.lpush("card_name", key)


# 线程池数量的设置跟宿主机分配的lcore资源有关，目前是4-5个，为了以后拓展资源时能充分利用，所以暂时设置为8
# 由于在host宿主机会做资源限制（并发限制），所以在celery任务没有获取到执行结果之前会阻塞
hdp_task_thread = ThreadPoolExecutor(8)
hdp_task_queue = MutiQueue(maxsize=8)

assets_queue = MutiQueue(maxsize=1)
app = Celery('demo')
app.config_from_object("celery_tasks.config")
init_virtual_card()
