/*
 * @Author: tianh
 * @Date: 2021-09-30 14:15:01
 * @LastEditors: tianh
 * @LastEditTime: 2022-02-18 16:19:49
 * @Descripttion:
 */
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export const getInfoData = (data: object) => {
  return request(`customize/ioc${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const addInfo = (data: object) => {
  return request(`customize/ioc`, {
    method: 'post',
    data,
  });
};

export const delData = (data: any) => {
  return request(`customize/ioc/single`, {
    method: 'delete',
    data,
  });
};

export const delAllData = () => {
  return request(`customize/ioc`, {
    method: 'delete',
  });
};

export const editInfo = (data: object) => {
  return request(`customize/ioc/single`, {
    method: 'put',
    data,
  });
};

export const getThreatenTypes = () => {
  return request(`customize/ioc/threat_type`, {
    method: 'get',
  });
};

export const getModelThreatTypes = () => {
  return request(`model/threat_type`, {
    method: 'get',
  });
};
// 自定义情报模板导出API
export const getInfoModel = () => {
  return request(`customize/ioc/import`, {
    method: 'get',
    responseType: 'blob',
  });
};