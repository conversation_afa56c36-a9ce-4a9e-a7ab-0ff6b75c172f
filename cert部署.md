## 前端部署

### 数据库更新

进入mongo容器并更新boss数据库

```bash
docker exec -it mongo bash
mongo --host 127.0.0.1 --port 27017 -u ksbox -p FBgxC5PWAre
use boss
db.auth_modules.insertOne(
    {
        "_id": {
            "$oid": "6756642bada9b0001ede64f9"
        },
        "isMenu": true,
        "sort": 1,
        "name": "邮件分析",
        "uri": "app.custom.emailAnalysis",
        "url": "/app/mica/customized/emailAnalysis",
        "icon": "",
        "describe": "",
        "parentId": "app.custom",
        "createdAt": {
            "$date": "2024-12-09T03:29:47.530Z"
        },
        "updatedAt": {
            "$date": "2024-12-09T03:29:47.530Z"
        },
        "position": "side",
        "app": "mica"
    }
)
db.auth_modules.insertOne(
    {
      "_id": {
        "$oid": "689327001d0bf7eec1450617"
      },
      "isMenu": false,
      "sort": 1,
      "name": "邮件分析详情",
      "uri": "app.custom.emailAnalysis.emailDetail",
      "url": "/app/mica/customized/emailAnalysis/emailDetail",
      "icon": "",
      "describe": "",
      "parentId": "app.custom",
      "createdAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "updatedAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "position": "side",
      "app": "mica"
    }
)
db.auth_modules.insertOne(
    {
      "_id": {
        "$oid": "67513541b398f5009f7f7ca9"
      },
      "isMenu": true,
      "sort": 2,
      "name": "文件告警",
      "uri": "app.alarm.file",
      "url": "/app/mica/alarm/File",
      "icon": "gateway",
      "describe": "",
      "parentId": "app.alarm",
      "createdAt": {
        "$date": "2024-12-05T05:08:17.107Z"
      },
      "updatedAt": {
        "$date": "2024-12-05T05:08:17.107Z"
      },
      "position": "side",
      "app": "mica"
    }
)

db.auth_modules.insertOne(
    {
      "_id": {
        "$oid": "68931eb31d0bf7eec1450615"
      },
      "isMenu": true,
      "sort": 1,
      "name": "文件检测日志",
      "uri": "app.collect.fileAnalysis",
      "url": "/app/mica/collect/fileAnalysis",
      "icon": "",
      "describe": "",
      "parentId": "app.collect",
      "createdAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "updatedAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "position": "side",
      "app": "mica"
    }
)
db.auth_modules.insertOne(
    {
      "_id": {
        "$oid": "6892be031d0bf7eec1450611"
      },
      "isMenu": true,
      "sort": 2,
      "name": "异常行为分析",
      "uri": "app.custom.anomalyDetection",
      "url": "/app/mica/customized/anomalyDetection",
      "icon": "exclamation",
      "describe": "",
      "parentId": "app.custom",
      "createdAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "updatedAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "position": "side",
      "app": "mica"
    }
)
db.auth_modules.insertOne(
    {
      "_id": {
        "$oid": "689327001d0bf7eec1450617"
      },
      "isMenu": false,
      "sort": 1,
      "name": "文件检测日志详情",
      "uri": "app.collect.fileAnalysis.fileDetail",
      "url": "/app/mica/collect/fileAnalysis/fileDetail",
      "icon": "",
      "describe": "",
      "parentId": "app.collect",
      "createdAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "updatedAt": {
        "$date": "2024-12-09T03:29:47.530Z"
      },
      "position": "side",
      "app": "mica"
    }
)

db.auth_groups.updateOne({ "name" : "管理员用户" }, {$addToSet: {modules: "app.custom.emailAnalysis"}})
db.auth_groups.updateOne({ "name" : "管理员用户" }, {$addToSet: {modules: "app.custom.emailAnalysis.emailDetail"}})
db.auth_groups.updateOne({ "name" : "管理员用户" }, {$addToSet: {modules: "app.alarm.file"}})
db.auth_groups.updateOne({ "name" : "管理员用户" }, {$addToSet: {modules: "app.collect.fileAnalysis"}})
db.auth_groups.updateOne({ "name" : "管理员用户" }, {$addToSet: {modules: "app.custom.anomalyDetection"}})

```


到此为止，数据库更新完成！

### 前端页面更新


在当前包中，解压dist.tar.gz，并替换已有的前端代码

```bash
# 备份前端代码
cp -fr /opt/mica-app /opt/mica-app-bak
# 删除前端代码
sudo rm -rf /opt/mica-app
# 解压前端代码
tar -zxvf dist.tar.gz --strip-components=1 -C /opt/mica-app/
```

点击前端页面，应该能够看到页面已更新。



## 后端升级：
2，	涉及的文件如下：
   new-file:   /opt/ndr/api_1_0/mail_analysis/mail_alarm_agg.py
   new-file:   /opt/ndr/api_1_0/mail_analysis/mail_analysis.py
   modified:   /opt/ndr/api_1_0/router.py
   new-file:   /opt/ndr/utils/args_check_schema/mail_analysis_list_schema.dict
   说明：新文件直接拷贝到对应的路径，/opt/ndr/api_1_0/mail_analysis这么目录需要新建。router.py 这个文件最好不要覆盖，直接编辑修改：

主要增加一下内容(共8行)：
大约在92行from api_1_0 import cluster的后面插入下面四行
from api_1_0.mail_analysis.mail_analysis import MailAnalysisList
from api_1_0.mail_analysis.mail_analysis import MailAnalysisDetail
from api_1_0.mail_analysis.mail_analysis import MailAnalysisAttachList
from api_1_0.mail_analysis.mail_analysis import MailAnalysisRoute
 

大约在362行插入下列5行
### 邮件分析
api.add_resource(MailAnalysisList, '/api/v1/mail_analysis/email_list')
api.add_resource(MailAnalysisDetail, '/api/v1/mail_analysis/email_detail')
api.add_resource(MailAnalysisAttachList, '/api/v1/mail_analysis/attachments')
api.add_resource(MailAnalysisRoute, '/api/v1/mail_analysis/send_rsv_info')
 


文件复制更新完了以后，重启后端容器：
docker stop mica-api
docker rm mica-api
docker-compose up -d mica-api  #去/root/ndr-deploy/目录执行，如果我没记错的话，这个目录下有一个docker-compose文件


3，样例数据导入
时间来得及就导一下，安装包中有个sample目录，里面有需要导入的样本数据，和导入方法：
导入前先找到ES集群的IP地址，修改相关命令后在执行
```
kslab@kslab-baseos:~/update_20250806$ ls
ndr_backend  sample
kslab@kslab-baseos:~/update_20250806$ ls sample/
bulk_sample_data.ndjson  mail_analysis.map  readme.txt
kslab@kslab-baseos:~/update_20250806$ cat sample/readme.txt 
# usage of the data import, 
# befor action, please modify the ES user/pwd and ip-addr

# 1, crete index
curl -X PUT "localhost:9200/mail_analysis" -H 'Content-Type: application/json' -d '{
  "settings": { "number_of_shards": 2 , "number_of_replicas": 1},
  "mappings": { "dynamic": true }
}'

# 2, import the sample data
curl -X POST "**********:9200/_bulk" -H 'Content-Type: application/x-ndjson'  --data-binary "@bulk_sample_data.ndjson"

# 3, check the import result
curl -X GET "**********:9200/mail_analysis/_search?pretty" -H 'Content-Type: application/json' -d '{"query": {"match_all": {} }}'

kslab@kslab-baseos:~/update_20250806$ 
```