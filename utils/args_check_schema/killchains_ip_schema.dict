{
    "type": "object",
    "required": ["startTime", "stopTime"],
    "properties": {
        "ip": {
            "type": "string",
            "default": "",
            "format": "ipv4"
        },
        "attackIp": {
            "type": "string",
            "default": "",
            "format": "ipv4"
        },
        "victimIp": {
            "type": "string",
            "default": "",
            "format": "ipv4"
        },
        "startTime": {
            "type": "string",
            "pattern": r"^\d{10}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{10}$"
        },
        "count": {
            "type": "string",
            "default": "0",
            "pattern": r"^([0-9]|[1-9][0-9]|100)$"
        },
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}