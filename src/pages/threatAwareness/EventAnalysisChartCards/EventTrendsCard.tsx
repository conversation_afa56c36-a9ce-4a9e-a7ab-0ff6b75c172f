import React, { useEffect } from 'react';
import moment from 'moment';

import { DashCard } from '../DashCard';
import ReactECharts from 'echarts-for-react';
import { useEventTrendsData, useLocalStorage } from '../hooks';
import { Props } from '../UnSettledThreat/HighRiskHostCard';

function EventTrendsCard({ timeRange, onStaticData }: Props): JSX.Element {
  const { loading, data, setTimeRange } = useEventTrendsData(timeRange, onStaticData)
  const attackStatus = useLocalStorage().get('threatAwareness.attack_status') || [];

  useEffect(() => {
    setTimeRange(timeRange);
  }, [timeRange]);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['总数'].concat(attackStatus.map((item: { nameCn: string; }) => item.nameCn)),
      icon: 'circle',
      right: 'center',
      top: '5%',
      itemWidth: 10,
      itemHeight: 10,
    },
    grid: {
      left: '6%',
      right: '8%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.timestamp.map((t) => moment(t).format('YYYY-MM-DD HH:mm:ss'))
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '总数',
        type: 'line',
        showSymbol: false,
        smooth: true,
        data: data.total,
      },
      {
        name: '成功',
        type: 'line',
        showSymbol: false,
        smooth: true,
        data: data.success,
      },
      {
        name: '可疑',
        type: 'line',
        showSymbol: false,
        smooth: true,
        data: data.suspicious,
      },
      {
        name: '尝试',
        type: 'line',
        showSymbol: false,
        smooth: true,
        data: data.attempt,
      },
    ]
  };


  return (
    <DashCard title="事件趋势">
      <ReactECharts
        showLoading={loading}
        option={option}
        notMerge={true}
        style={{ width: '100%', height: 400 }}
        opts={{ renderer: 'svg' }}
      />
    </DashCard>
  );
}

export default EventTrendsCard;
