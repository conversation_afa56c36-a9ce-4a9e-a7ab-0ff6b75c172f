# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : anomaly_result_manager.py
# @Software: PyCharm

"""异常检测结果管理API"""

import json
import time
from datetime import datetime, timedelta
from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from utils.database import MongoDB
from utils.param_check import Validator, check_flask_args
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class AnomalyResultList(Resource):
    """异常检测结果列表"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'anomaly_results'
    
    
    def get(self):
        """获取异常检测结果列表"""
        try:
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 10))
            task_id = request.args.get('task_id')
            model_type = request.args.get('model_type')
            severity = request.args.get('severity')
            start_time = request.args.get('start_time')
            end_time = request.args.get('end_time')
            target_value = request.args.get('target_value')
            
            # 构建查询条件
            query = {}
            if task_id:
                query['task_id'] = task_id
            if model_type:
                query['model_type'] = model_type
            if severity:
                query['severity'] = severity
            if target_value:
                query['target_value'] = {'$regex': target_value, '$options': 'i'}
            
            # 时间范围查询
            if start_time and end_time:
                start_dt = datetime.fromtimestamp(int(start_time) / 1000)
                end_dt = datetime.fromtimestamp(int(end_time) / 1000)
                query['detection_time'] = {'$gte': start_dt, '$lte': end_dt}
            
            # 分页查询
            skip = (page - 1) * page_size
            results = list(self.mongodb.find(
                self.collection, 
                query, 
                {'_id': 0}
            ).sort('detection_time', -1).skip(skip).limit(page_size))
            
            total = self.mongodb.count(self.collection, query)
            
            # 格式化时间戳
            for result in results:
                if 'detection_time' in result:
                    result['detection_time'] = int(result['detection_time'].timestamp() * 1000)
                if 'create_time' in result:
                    result['create_time'] = int(result['create_time'].timestamp() * 1000)
            
            result_data = {
                'results': results,
                'total': total,
                'page': page,
                'page_size': page_size
            }
            
            return flask_response('获取检测结果成功', True, result_data)
            
        except Exception as e:
            LOG.error(f"获取检测结果失败: {str(e)}")
            return flask_response(f'获取检测结果失败: {str(e)}', False, {})


class AnomalyResultDetail(Resource):
    """异常检测结果详情"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'anomaly_results'
    
    
    def get(self, result_id):
        """获取检测结果详情"""
        try:
            result = self.mongodb.find_one(self.collection, {'result_id': result_id}, {'_id': 0})
            
            if not result:
                return flask_response('检测结果不存在', False, {})
            
            # 格式化时间戳
            if 'detection_time' in result:
                result['detection_time'] = int(result['detection_time'].timestamp() * 1000)
            if 'create_time' in result:
                result['create_time'] = int(result['create_time'].timestamp() * 1000)
            
            # 获取相关任务信息
            task = self.mongodb.find_one('anomaly_tasks', {'task_id': result['task_id']}, {'_id': 0, 'name': 1, 'description': 1})
            if task:
                result['task_info'] = task
            
            return flask_response('获取检测结果详情成功', True, result)
            
        except Exception as e:
            LOG.error(f"获取检测结果详情失败: {str(e)}")
            return flask_response(f'获取检测结果详情失败: {str(e)}', False, {})
    
    
    def put(self, result_id):
        """更新检测结果状态"""
        try:
            data = request.get_json()
            
            # 检查结果是否存在
            existing_result = self.mongodb.find_one(self.collection, {'result_id': result_id})
            if not existing_result:
                return flask_response('检测结果不存在', False, {})
            
            # 更新数据
            update_data = {}
            
            # 允许更新的字段
            updatable_fields = ['status', 'confirmed', 'notes', 'handled_by']
            for field in updatable_fields:
                if field in data:
                    update_data[field] = data[field]
            
            if update_data:
                update_data['update_time'] = datetime.now()
                self.mongodb.update_one(self.collection, {'result_id': result_id}, {'$set': update_data})
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"更新异常检测结果: {result_id}")
            LOG.info(f"更新异常检测结果成功: {result_id}")
            
            return flask_response('更新检测结果成功', True, {})
            
        except Exception as e:
            LOG.error(f"更新检测结果失败: {str(e)}")
            return flask_response(f'更新检测结果失败: {str(e)}', False, {})


class AnomalyResultStats(Resource):
    """异常检测结果统计"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'anomaly_results'
    
    
    def get(self):
        """获取检测结果统计信息"""
        try:
            start_time = request.args.get('start_time')
            end_time = request.args.get('end_time')
            
            # 构建时间查询条件
            query = {}
            if start_time and end_time:
                start_dt = datetime.fromtimestamp(int(start_time) / 1000)
                end_dt = datetime.fromtimestamp(int(end_time) / 1000)
                query['detection_time'] = {'$gte': start_dt, '$lte': end_dt}
            
            # 统计总数
            total_count = self.mongodb.count(self.collection, query)
            
            # 按严重程度统计
            severity_pipeline = [
                {'$match': query},
                {'$group': {'_id': '$severity', 'count': {'$sum': 1}}},
                {'$sort': {'_id': 1}}
            ]
            severity_stats = list(self.mongodb.aggs(self.collection, severity_pipeline))
            
            # 按模型类型统计
            model_pipeline = [
                {'$match': query},
                {'$group': {'_id': '$model_type', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}}
            ]
            model_stats = list(self.mongodb.aggs(self.collection, model_pipeline))
            
            # 按时间统计（最近7天）
            time_pipeline = [
                {'$match': query},
                {'$group': {
                    '_id': {
                        'year': {'$year': '$detection_time'},
                        'month': {'$month': '$detection_time'},
                        'day': {'$dayOfMonth': '$detection_time'}
                    },
                    'count': {'$sum': 1}
                }},
                {'$sort': {'_id': 1}},
                {'$limit': 7}
            ]
            time_stats = list(self.mongodb.aggs(self.collection, time_pipeline))
            
            # 格式化时间统计数据
            formatted_time_stats = []
            for stat in time_stats:
                date_str = f"{stat['_id']['year']}-{stat['_id']['month']:02d}-{stat['_id']['day']:02d}"
                formatted_time_stats.append({
                    'date': date_str,
                    'count': stat['count']
                })
            
            stats_data = {
                'total_count': total_count,
                'severity_stats': severity_stats,
                'model_stats': model_stats,
                'time_stats': formatted_time_stats
            }
            
            return flask_response('获取统计信息成功', True, stats_data)
            
        except Exception as e:
            LOG.error(f"获取统计信息失败: {str(e)}")
            return flask_response(f'获取统计信息失败: {str(e)}', False, {})
