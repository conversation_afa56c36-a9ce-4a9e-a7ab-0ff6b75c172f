#!/usr/bin/env python
# coding=utf-8

import os

"""通用集群模块依赖变量设置."""

"""集群服务基本配置默认值 -- 外部不可见"""
__DEFAULT_CLUSTER_NAME = "KSPCLUSTER"
__DEFAULT_SERVER_PORT = 18916
__DEFAULT_REGISTRY_PORT = 18912
__DEFAULT_CLUSTER_CFG = "/etc/ksp/cluster/cluster.json"
__DEFAULT_CLUSTER_HOOK_SCRIPT = "/opt/cluster/hook.sh"
__DEFAULT_ROLE_FILE_PATH = "/etc/ksp/cluster/role"
__DEFAULT_CLUSTER_SSL_SERVER_KEY = '/etc/ksp/cluster/credentials/server.key'
__DEFAULT_CLUSTER_SSL_SERVER_CERT = '/etc/ksp/cluster/credentials/server.crt'
__DEFAULT_CLUSTER_SSL_CA_CERT = '/etc/ksp/cluster/credentials/ca.crt'
__DEFAULT_CLUSTER_SSL_CLIENT_KEY = '/etc/ksp/cluster/credentials/client.key'
__DEFAULT_CLUSTER_SSL_CLIENT_CERT = '/etc/ksp/cluster/credentials/client.crt'
__DEFAULT_CLUSTER_PRE_SHARED_KEY = 'some-preshared-key'
__DEFAULT_KSP_NETWORK_TOOL_PATH = "/opt/kspython3.7/bin/ksp-network"

"""集群服务基本配置运行值"""
CLUSTER_NAME = os.environ.get('CLUSTER_NAME', __DEFAULT_CLUSTER_NAME)
CLUSTER_SERVER_PORT = int(os.environ.get('CLUSTER_SERVER_PORT', __DEFAULT_SERVER_PORT))
CLUSTER_REGISTRY_PORT = int(os.environ.get('CLUSTER_REGISTRY_PORT', __DEFAULT_REGISTRY_PORT))
CLUSTER_CFG = os.environ.get('CLUSTER_CFG', __DEFAULT_CLUSTER_CFG)
CLUSTER_HOOK_SCRIPT = os.environ.get('CLUSTER_HOOK_SCRIPT', __DEFAULT_CLUSTER_HOOK_SCRIPT)
CLUSTER_ROLE_FILE_PATH = os.environ.get('CLUSTER_ROLE_FILE_PATH', __DEFAULT_ROLE_FILE_PATH)
CLUSTER_KSP_NETWORK_TOOL_PATH = os.environ.get('CLUSTER_KSP_NETWORK_TOOL_PATH', __DEFAULT_KSP_NETWORK_TOOL_PATH)

"""集群服务安全配置运行值"""
CLUSTER_SSL_CLIENT_KEY = os.environ.get('CLUSTER_SSL_CLIENT_KEY', __DEFAULT_CLUSTER_SSL_CLIENT_KEY)
CLUSTER_SSL_CLIENT_CERT = os.environ.get('CLUSTER_SSL_CLIENT_CERT', __DEFAULT_CLUSTER_SSL_CLIENT_CERT)
CLUSTER_SSL_SERVER_KEY = os.environ.get('CLUSTER_SSL_SERVER_KEY', __DEFAULT_CLUSTER_SSL_SERVER_KEY)
CLUSTER_SSL_SERVER_CERT = os.environ.get('CLUSTER_SSL_SERVER_CERT', __DEFAULT_CLUSTER_SSL_SERVER_CERT)
CLUSTER_SSL_CA_CERT = os.environ.get('CLUSTER_SSL_CA_CERT', __DEFAULT_CLUSTER_SSL_CA_CERT)
CLUSTER_PRE_SHARED_KEY = os.environ.get('CLUSTER_PRE_SHARED_KEY', __DEFAULT_CLUSTER_PRE_SHARED_KEY)

"""集群服务外部依赖变量 -- 业务调度相关"""
CLUSTER_SCHE_CFG = os.environ.get('CLUSTER_SCHE_CFG', '/opt/config/schedule.yaml')
CLUSTER_DATA_SRC_CFG = os.environ.get('CLUSTER_DATA_SRC_CFG', '/opt/config/datasource.yaml')
CLUSTER_SYSUPDATE_SYNCFILE = os.environ.get('CLUSTER_SYSUPDATE_SYNCFILE', '/opt/config/sync_list.lst')
CLUSTER_UPDATE_SYSTEMD_ENV_FILE_PATH = os.environ.get('CLUSTER_UPDATE_SYSTEMD_ENV_FILE_PATH', '/etc/ksp/cluster/systemd_env')
