#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_event_metadata.py
# @time: 2019/07/18
# @software: PyCharm


import time

import requests

from celery_tasks import app
from config.config import NdrLog
from utils.database import BaseMongoDB
from utils.database import get_es_client
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)
db = BaseMongoDB("ndr")
es_client = get_es_client()


@app.task()
def rule_deal():
    present_time = int(time.time() * 1000)
    past_time = int(present_time - 24 * 60 * 60 * 1000)
    es_template = ES_Template().read_template('rule_list')
    es_template["query"]["bool"]["must"][0]["range"]["observedTime"]["gte"] = past_time
    es_template["query"]["bool"]["must"][0]["range"]["observedTime"]["lte"] = present_time
    try:
        data = es_client.search(index="rule-eve", body=es_template)
        data_buckets = data["aggregations"]["groups"]["buckets"]
        sid_list = []
        if data_buckets:
            for i in data_buckets:
                if i["doc_count"] < 2000:
                    break
                else:
                    sid_list.append(str(i["key"]))

        if sid_list:
            request_arg = {"action": "disable", "sidList": sid_list, "control": "auto"}
            requests.post("http://localhost:7001/api/v1/feature/enable_status", json=request_arg,
                          headers={"Upgrade": "True"})
    except Exception as e:
        LOG.error('reason[{0}]'.format(str(e)))
