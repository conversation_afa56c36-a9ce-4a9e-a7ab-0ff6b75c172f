[
    {
        "$match": {
            "observedAtTimestamp": {
                "$gte": 1504527700099,
                "$lte": 1578430872367
            }
        }
    },
    {
        "$group": {
            "_id": "$attackIp",
            "threatScores": {
                "$addToSet": "$threatScore"
            },
            "attackIsSuccesss": {
                "$addToSet": "$attackIsSuccesss"
            },
            "threatLevels": {
                "$addToSet": "$threatLevelInt"
            },
            "iocs": {
                "$addToSet": "$ioc"
            },
     ,       "eventTypes": {
                "$addToSet": "$eventType"
            },
            "victimIpsList": {
                "$addToSet": "$victimIps"
            },
            "attackIpList": {
                "$addToSet": "$attackIp"
            },
            "lockheedKillchainENs": {
                "$addToSet": "$lockheedKillchainENs"
            },
            "threatFlags": {
                "$addToSet": "$threatFlags"
            },
            "occurredAt": {
                "$addToSet": "$occurredAt"
            },
            "observedAt": {
                "$addToSet": "$observedAt"
            }
        }
    },
    {
        "$project": {
            "_id": 0,
            "attackIp": "$_id",
            "victimIps": {
                "$reduce": {
                    "input": "$victimIpsList",
                    "initialValue": [],
                    "in": {
                        "$setUnion": [
                            "$$value",
                            "$$this"
                        ]
                    }
                }
            },
            "threatFlags": {
                "$reduce": {
                    "input": "$threatFlags",
                    "initialValue": [],
                    "in": {
                        "$setUnion": [
                            "$$value",
                            "$$this"
                        ]
                    }
                }
            },
            "lockheedKillchainENs": {
                "$reduce": {
                    "input": "$lockheedKillchainENs",
                    "initialValue": [],
                    "in": {
                        "$setUnion": [
                            "$$value",
                            "$$this"
                        ]
                    }
                }
            },
            "attackIsSuccesss": {
                "$reduce": {
                    "input": "$attackIsSuccesss",
                    "initialValue": [],
                    "in": {
                        "$setUnion": [
                            "$$value",
                            "$$this"
                        ]
                    }
                }
            },
            "threatScores": "$threatScores",
            "occurredAt": {
                "$reduce": {
                    "input": "$occurredAt",
                    "initialValue": [],
                    "in": {
                        "$setUnion": [
                            "$$value",
                            "$$this"
                        ]
                    }
                }
            },
            "observedAt": {
                "$reduce": {
                    "input": "$observedAt",
                    "initialValue": [],
                    "in": {
                        "$setUnion": [
                            "$$value",
                            "$$this"
                        ]
                    }
                }
            },
            "eventType": {
                "$max": "$eventTypes"
            },
            "threatLevel": {
                "$max": "$threatLevels"
            },
            "threatScore": {
                "$max": "$threatScores"
            },
            "ioc": {
                "$max": "$iocs"
            },
            "ips": {
                "$reduce": {
                    "input": "$victimIpsList",
                    "initialValue": "$attackIpList",
                    "in": {
                        "$setUnion": [
                            "$$value",
                            "$$this"
                        ]
                    }
                }
            }
        }
    },
    {
        "$project": {
            "attackIp": "$attackIp",
            "victimIps": "$victimIps",
            "threatFlags": "$threatFlags",
            "lockheedKillchainENs": "$lockheedKillchainENs",
            "eventType": "$eventType",
            "attackIsSuccesss": "$attackIsSuccesss",
            "threatScores": "$threatScores",
            "occurredAt": "$occurredAt",
            "observedAt": "$observedAt",
            "threatLevel": "$threatLevel",
            "firstOccurredAtTimestamp": {
                "$min": "$occurredAt"
            },
            "lastOccurredAtTimestamp": {
                "$max": "$occurredAt"
            },
            "firstobservedAtTimestamp": {
                "$min": "$observedAt"
            },
            "threatScore": "$threatScore",
            "ioc": "$ioc",
            "ips": "$ips"
        }
    }
]