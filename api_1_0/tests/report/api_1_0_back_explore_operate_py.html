


<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    
    
    <meta http-equiv="X-UA-Compatible" content="IE=emulateIE7" />
    <title>Coverage for api_1_0/back_explore/operate.py: 84%</title>
    <link rel="stylesheet" href="style.css" type="text/css">
    
    <script type="text/javascript" src="jquery.min.js"></script>
    <script type="text/javascript" src="jquery.hotkeys.js"></script>
    <script type="text/javascript" src="jquery.isonscreen.js"></script>
    <script type="text/javascript" src="coverage_html.js"></script>
    <script type="text/javascript">
        jQuery(document).ready(coverage.pyfile_ready);
    </script>
</head>
<body class="pyfile">

<div id="header">
    <div class="content">
        <h1>Coverage for <b>api_1_0/back_explore/operate.py</b> :
            <span class="pc_cov">84%</span>
        </h1>

        <img id="keyboard_icon" src="keybd_closed.png" alt="Show keyboard shortcuts" />

        <h2 class="stats">
            279 statements &nbsp;
            <span class="run hide_run shortkey_r button_toggle_run">234 run</span>
            <span class="mis shortkey_m button_toggle_mis">45 missing</span>
            <span class="exc shortkey_x button_toggle_exc">0 excluded</span>

            
        </h2>
    </div>
</div>

<div class="help_panel">
    <img id="panel_icon" src="keybd_open.png" alt="Hide keyboard shortcuts" />
    <p class="legend">Hot-keys on this page</p>
    <div>
    <p class="keyhelp">
        <span class="key">r</span>
        <span class="key">m</span>
        <span class="key">x</span>
        <span class="key">p</span> &nbsp; toggle line displays
    </p>
    <p class="keyhelp">
        <span class="key">j</span>
        <span class="key">k</span> &nbsp; next/prev highlighted chunk
    </p>
    <p class="keyhelp">
        <span class="key">0</span> &nbsp; (zero) top of page
    </p>
    <p class="keyhelp">
        <span class="key">1</span> &nbsp; (one) first highlighted chunk
    </p>
    </div>
</div>

<div id="source">
    <table>
        <tr>
            <td class="linenos">
<p id="n1" class="pln"><a href="#n1">1</a></p>
<p id="n2" class="pln"><a href="#n2">2</a></p>
<p id="n3" class="pln"><a href="#n3">3</a></p>
<p id="n4" class="pln"><a href="#n4">4</a></p>
<p id="n5" class="pln"><a href="#n5">5</a></p>
<p id="n6" class="pln"><a href="#n6">6</a></p>
<p id="n7" class="stm run hide_run"><a href="#n7">7</a></p>
<p id="n8" class="pln"><a href="#n8">8</a></p>
<p id="n9" class="stm run hide_run"><a href="#n9">9</a></p>
<p id="n10" class="stm run hide_run"><a href="#n10">10</a></p>
<p id="n11" class="stm run hide_run"><a href="#n11">11</a></p>
<p id="n12" class="stm run hide_run"><a href="#n12">12</a></p>
<p id="n13" class="stm run hide_run"><a href="#n13">13</a></p>
<p id="n14" class="stm run hide_run"><a href="#n14">14</a></p>
<p id="n15" class="stm run hide_run"><a href="#n15">15</a></p>
<p id="n16" class="stm run hide_run"><a href="#n16">16</a></p>
<p id="n17" class="stm run hide_run"><a href="#n17">17</a></p>
<p id="n18" class="stm run hide_run"><a href="#n18">18</a></p>
<p id="n19" class="stm run hide_run"><a href="#n19">19</a></p>
<p id="n20" class="stm run hide_run"><a href="#n20">20</a></p>
<p id="n21" class="pln"><a href="#n21">21</a></p>
<p id="n22" class="pln"><a href="#n22">22</a></p>
<p id="n23" class="stm run hide_run"><a href="#n23">23</a></p>
<p id="n24" class="pln"><a href="#n24">24</a></p>
<p id="n25" class="pln"><a href="#n25">25</a></p>
<p id="n26" class="stm run hide_run"><a href="#n26">26</a></p>
<p id="n27" class="pln"><a href="#n27">27</a></p>
<p id="n28" class="stm run hide_run"><a href="#n28">28</a></p>
<p id="n29" class="stm run hide_run"><a href="#n29">29</a></p>
<p id="n30" class="pln"><a href="#n30">30</a></p>
<p id="n31" class="pln"><a href="#n31">31</a></p>
<p id="n32" class="pln"><a href="#n32">32</a></p>
<p id="n33" class="stm run hide_run"><a href="#n33">33</a></p>
<p id="n34" class="pln"><a href="#n34">34</a></p>
<p id="n35" class="pln"><a href="#n35">35</a></p>
<p id="n36" class="pln"><a href="#n36">36</a></p>
<p id="n37" class="pln"><a href="#n37">37</a></p>
<p id="n38" class="pln"><a href="#n38">38</a></p>
<p id="n39" class="pln"><a href="#n39">39</a></p>
<p id="n40" class="stm run hide_run"><a href="#n40">40</a></p>
<p id="n41" class="stm run hide_run"><a href="#n41">41</a></p>
<p id="n42" class="stm run hide_run"><a href="#n42">42</a></p>
<p id="n43" class="stm run hide_run"><a href="#n43">43</a></p>
<p id="n44" class="stm run hide_run"><a href="#n44">44</a></p>
<p id="n45" class="stm run hide_run"><a href="#n45">45</a></p>
<p id="n46" class="pln"><a href="#n46">46</a></p>
<p id="n47" class="pln"><a href="#n47">47</a></p>
<p id="n48" class="stm run hide_run"><a href="#n48">48</a></p>
<p id="n49" class="stm run hide_run"><a href="#n49">49</a></p>
<p id="n50" class="stm run hide_run"><a href="#n50">50</a></p>
<p id="n51" class="pln"><a href="#n51">51</a></p>
<p id="n52" class="stm run hide_run"><a href="#n52">52</a></p>
<p id="n53" class="stm mis"><a href="#n53">53</a></p>
<p id="n54" class="pln"><a href="#n54">54</a></p>
<p id="n55" class="pln"><a href="#n55">55</a></p>
<p id="n56" class="pln"><a href="#n56">56</a></p>
<p id="n57" class="stm run hide_run"><a href="#n57">57</a></p>
<p id="n58" class="pln"><a href="#n58">58</a></p>
<p id="n59" class="pln"><a href="#n59">59</a></p>
<p id="n60" class="pln"><a href="#n60">60</a></p>
<p id="n61" class="pln"><a href="#n61">61</a></p>
<p id="n62" class="pln"><a href="#n62">62</a></p>
<p id="n63" class="pln"><a href="#n63">63</a></p>
<p id="n64" class="pln"><a href="#n64">64</a></p>
<p id="n65" class="pln"><a href="#n65">65</a></p>
<p id="n66" class="pln"><a href="#n66">66</a></p>
<p id="n67" class="stm run hide_run"><a href="#n67">67</a></p>
<p id="n68" class="stm run hide_run"><a href="#n68">68</a></p>
<p id="n69" class="stm mis"><a href="#n69">69</a></p>
<p id="n70" class="stm run hide_run"><a href="#n70">70</a></p>
<p id="n71" class="stm mis"><a href="#n71">71</a></p>
<p id="n72" class="stm run hide_run"><a href="#n72">72</a></p>
<p id="n73" class="stm run hide_run"><a href="#n73">73</a></p>
<p id="n74" class="stm run hide_run"><a href="#n74">74</a></p>
<p id="n75" class="stm run hide_run"><a href="#n75">75</a></p>
<p id="n76" class="stm run hide_run"><a href="#n76">76</a></p>
<p id="n77" class="stm mis"><a href="#n77">77</a></p>
<p id="n78" class="pln"><a href="#n78">78</a></p>
<p id="n79" class="stm mis"><a href="#n79">79</a></p>
<p id="n80" class="stm mis"><a href="#n80">80</a></p>
<p id="n81" class="stm mis"><a href="#n81">81</a></p>
<p id="n82" class="pln"><a href="#n82">82</a></p>
<p id="n83" class="stm run hide_run"><a href="#n83">83</a></p>
<p id="n84" class="stm run hide_run"><a href="#n84">84</a></p>
<p id="n85" class="stm run hide_run"><a href="#n85">85</a></p>
<p id="n86" class="stm run hide_run"><a href="#n86">86</a></p>
<p id="n87" class="stm run hide_run"><a href="#n87">87</a></p>
<p id="n88" class="stm run hide_run"><a href="#n88">88</a></p>
<p id="n89" class="stm run hide_run"><a href="#n89">89</a></p>
<p id="n90" class="stm run hide_run"><a href="#n90">90</a></p>
<p id="n91" class="stm run hide_run"><a href="#n91">91</a></p>
<p id="n92" class="stm run hide_run"><a href="#n92">92</a></p>
<p id="n93" class="stm mis"><a href="#n93">93</a></p>
<p id="n94" class="pln"><a href="#n94">94</a></p>
<p id="n95" class="stm mis"><a href="#n95">95</a></p>
<p id="n96" class="stm run hide_run"><a href="#n96">96</a></p>
<p id="n97" class="stm mis"><a href="#n97">97</a></p>
<p id="n98" class="pln"><a href="#n98">98</a></p>
<p id="n99" class="stm mis"><a href="#n99">99</a></p>
<p id="n100" class="pln"><a href="#n100">100</a></p>
<p id="n101" class="stm run hide_run"><a href="#n101">101</a></p>
<p id="n102" class="pln"><a href="#n102">102</a></p>
<p id="n103" class="pln"><a href="#n103">103</a></p>
<p id="n104" class="stm run hide_run"><a href="#n104">104</a></p>
<p id="n105" class="pln"><a href="#n105">105</a></p>
<p id="n106" class="pln"><a href="#n106">106</a></p>
<p id="n107" class="pln"><a href="#n107">107</a></p>
<p id="n108" class="pln"><a href="#n108">108</a></p>
<p id="n109" class="pln"><a href="#n109">109</a></p>
<p id="n110" class="stm run hide_run"><a href="#n110">110</a></p>
<p id="n111" class="stm run hide_run"><a href="#n111">111</a></p>
<p id="n112" class="stm run hide_run"><a href="#n112">112</a></p>
<p id="n113" class="stm run hide_run"><a href="#n113">113</a></p>
<p id="n114" class="stm run hide_run"><a href="#n114">114</a></p>
<p id="n115" class="stm run hide_run"><a href="#n115">115</a></p>
<p id="n116" class="stm run hide_run"><a href="#n116">116</a></p>
<p id="n117" class="pln"><a href="#n117">117</a></p>
<p id="n118" class="pln"><a href="#n118">118</a></p>
<p id="n119" class="stm run hide_run"><a href="#n119">119</a></p>
<p id="n120" class="pln"><a href="#n120">120</a></p>
<p id="n121" class="pln"><a href="#n121">121</a></p>
<p id="n122" class="pln"><a href="#n122">122</a></p>
<p id="n123" class="pln"><a href="#n123">123</a></p>
<p id="n124" class="pln"><a href="#n124">124</a></p>
<p id="n125" class="stm run hide_run"><a href="#n125">125</a></p>
<p id="n126" class="stm mis"><a href="#n126">126</a></p>
<p id="n127" class="stm run hide_run"><a href="#n127">127</a></p>
<p id="n128" class="stm run hide_run"><a href="#n128">128</a></p>
<p id="n129" class="stm mis"><a href="#n129">129</a></p>
<p id="n130" class="pln"><a href="#n130">130</a></p>
<p id="n131" class="stm run hide_run"><a href="#n131">131</a></p>
<p id="n132" class="stm run hide_run"><a href="#n132">132</a></p>
<p id="n133" class="pln"><a href="#n133">133</a></p>
<p id="n134" class="pln"><a href="#n134">134</a></p>
<p id="n135" class="stm run hide_run"><a href="#n135">135</a></p>
<p id="n136" class="pln"><a href="#n136">136</a></p>
<p id="n137" class="pln"><a href="#n137">137</a></p>
<p id="n138" class="pln"><a href="#n138">138</a></p>
<p id="n139" class="pln"><a href="#n139">139</a></p>
<p id="n140" class="pln"><a href="#n140">140</a></p>
<p id="n141" class="pln"><a href="#n141">141</a></p>
<p id="n142" class="stm run hide_run"><a href="#n142">142</a></p>
<p id="n143" class="stm run hide_run"><a href="#n143">143</a></p>
<p id="n144" class="pln"><a href="#n144">144</a></p>
<p id="n145" class="pln"><a href="#n145">145</a></p>
<p id="n146" class="stm run hide_run"><a href="#n146">146</a></p>
<p id="n147" class="pln"><a href="#n147">147</a></p>
<p id="n148" class="pln"><a href="#n148">148</a></p>
<p id="n149" class="pln"><a href="#n149">149</a></p>
<p id="n150" class="pln"><a href="#n150">150</a></p>
<p id="n151" class="stm run hide_run"><a href="#n151">151</a></p>
<p id="n152" class="pln"><a href="#n152">152</a></p>
<p id="n153" class="pln"><a href="#n153">153</a></p>
<p id="n154" class="pln"><a href="#n154">154</a></p>
<p id="n155" class="pln"><a href="#n155">155</a></p>
<p id="n156" class="pln"><a href="#n156">156</a></p>
<p id="n157" class="pln"><a href="#n157">157</a></p>
<p id="n158" class="pln"><a href="#n158">158</a></p>
<p id="n159" class="pln"><a href="#n159">159</a></p>
<p id="n160" class="pln"><a href="#n160">160</a></p>
<p id="n161" class="pln"><a href="#n161">161</a></p>
<p id="n162" class="pln"><a href="#n162">162</a></p>
<p id="n163" class="pln"><a href="#n163">163</a></p>
<p id="n164" class="pln"><a href="#n164">164</a></p>
<p id="n165" class="pln"><a href="#n165">165</a></p>
<p id="n166" class="pln"><a href="#n166">166</a></p>
<p id="n167" class="pln"><a href="#n167">167</a></p>
<p id="n168" class="pln"><a href="#n168">168</a></p>
<p id="n169" class="stm run hide_run"><a href="#n169">169</a></p>
<p id="n170" class="stm run hide_run"><a href="#n170">170</a></p>
<p id="n171" class="pln"><a href="#n171">171</a></p>
<p id="n172" class="pln"><a href="#n172">172</a></p>
<p id="n173" class="stm run hide_run"><a href="#n173">173</a></p>
<p id="n174" class="pln"><a href="#n174">174</a></p>
<p id="n175" class="stm run hide_run"><a href="#n175">175</a></p>
<p id="n176" class="stm run hide_run"><a href="#n176">176</a></p>
<p id="n177" class="pln"><a href="#n177">177</a></p>
<p id="n178" class="stm run hide_run"><a href="#n178">178</a></p>
<p id="n179" class="stm run hide_run"><a href="#n179">179</a></p>
<p id="n180" class="stm run hide_run"><a href="#n180">180</a></p>
<p id="n181" class="stm run hide_run"><a href="#n181">181</a></p>
<p id="n182" class="pln"><a href="#n182">182</a></p>
<p id="n183" class="pln"><a href="#n183">183</a></p>
<p id="n184" class="stm run hide_run"><a href="#n184">184</a></p>
<p id="n185" class="pln"><a href="#n185">185</a></p>
<p id="n186" class="pln"><a href="#n186">186</a></p>
<p id="n187" class="pln"><a href="#n187">187</a></p>
<p id="n188" class="pln"><a href="#n188">188</a></p>
<p id="n189" class="stm run hide_run"><a href="#n189">189</a></p>
<p id="n190" class="pln"><a href="#n190">190</a></p>
<p id="n191" class="pln"><a href="#n191">191</a></p>
<p id="n192" class="stm run hide_run"><a href="#n192">192</a></p>
<p id="n193" class="stm run hide_run"><a href="#n193">193</a></p>
<p id="n194" class="stm run hide_run"><a href="#n194">194</a></p>
<p id="n195" class="stm run hide_run"><a href="#n195">195</a></p>
<p id="n196" class="pln"><a href="#n196">196</a></p>
<p id="n197" class="stm run hide_run"><a href="#n197">197</a></p>
<p id="n198" class="stm mis"><a href="#n198">198</a></p>
<p id="n199" class="stm mis"><a href="#n199">199</a></p>
<p id="n200" class="pln"><a href="#n200">200</a></p>
<p id="n201" class="stm mis"><a href="#n201">201</a></p>
<p id="n202" class="pln"><a href="#n202">202</a></p>
<p id="n203" class="stm run hide_run"><a href="#n203">203</a></p>
<p id="n204" class="stm run hide_run"><a href="#n204">204</a></p>
<p id="n205" class="stm run hide_run"><a href="#n205">205</a></p>
<p id="n206" class="stm run hide_run"><a href="#n206">206</a></p>
<p id="n207" class="stm run hide_run"><a href="#n207">207</a></p>
<p id="n208" class="stm run hide_run"><a href="#n208">208</a></p>
<p id="n209" class="stm run hide_run"><a href="#n209">209</a></p>
<p id="n210" class="pln"><a href="#n210">210</a></p>
<p id="n211" class="stm run hide_run"><a href="#n211">211</a></p>
<p id="n212" class="pln"><a href="#n212">212</a></p>
<p id="n213" class="pln"><a href="#n213">213</a></p>
<p id="n214" class="pln"><a href="#n214">214</a></p>
<p id="n215" class="pln"><a href="#n215">215</a></p>
<p id="n216" class="pln"><a href="#n216">216</a></p>
<p id="n217" class="pln"><a href="#n217">217</a></p>
<p id="n218" class="stm run hide_run"><a href="#n218">218</a></p>
<p id="n219" class="stm run hide_run"><a href="#n219">219</a></p>
<p id="n220" class="stm mis"><a href="#n220">220</a></p>
<p id="n221" class="stm mis"><a href="#n221">221</a></p>
<p id="n222" class="stm run hide_run"><a href="#n222">222</a></p>
<p id="n223" class="stm run hide_run"><a href="#n223">223</a></p>
<p id="n224" class="stm run hide_run"><a href="#n224">224</a></p>
<p id="n225" class="stm run hide_run"><a href="#n225">225</a></p>
<p id="n226" class="stm run hide_run"><a href="#n226">226</a></p>
<p id="n227" class="stm run hide_run"><a href="#n227">227</a></p>
<p id="n228" class="stm run hide_run"><a href="#n228">228</a></p>
<p id="n229" class="pln"><a href="#n229">229</a></p>
<p id="n230" class="stm run hide_run"><a href="#n230">230</a></p>
<p id="n231" class="stm run hide_run"><a href="#n231">231</a></p>
<p id="n232" class="stm run hide_run"><a href="#n232">232</a></p>
<p id="n233" class="stm run hide_run"><a href="#n233">233</a></p>
<p id="n234" class="stm mis"><a href="#n234">234</a></p>
<p id="n235" class="stm run hide_run"><a href="#n235">235</a></p>
<p id="n236" class="stm run hide_run"><a href="#n236">236</a></p>
<p id="n237" class="stm run hide_run"><a href="#n237">237</a></p>
<p id="n238" class="stm run hide_run"><a href="#n238">238</a></p>
<p id="n239" class="stm run hide_run"><a href="#n239">239</a></p>
<p id="n240" class="stm run hide_run"><a href="#n240">240</a></p>
<p id="n241" class="stm mis"><a href="#n241">241</a></p>
<p id="n242" class="stm run hide_run"><a href="#n242">242</a></p>
<p id="n243" class="stm run hide_run"><a href="#n243">243</a></p>
<p id="n244" class="stm run hide_run"><a href="#n244">244</a></p>
<p id="n245" class="stm run hide_run"><a href="#n245">245</a></p>
<p id="n246" class="stm run hide_run"><a href="#n246">246</a></p>
<p id="n247" class="stm run hide_run"><a href="#n247">247</a></p>
<p id="n248" class="stm run hide_run"><a href="#n248">248</a></p>
<p id="n249" class="stm run hide_run"><a href="#n249">249</a></p>
<p id="n250" class="stm run hide_run"><a href="#n250">250</a></p>
<p id="n251" class="stm run hide_run"><a href="#n251">251</a></p>
<p id="n252" class="stm run hide_run"><a href="#n252">252</a></p>
<p id="n253" class="stm mis"><a href="#n253">253</a></p>
<p id="n254" class="stm mis"><a href="#n254">254</a></p>
<p id="n255" class="pln"><a href="#n255">255</a></p>
<p id="n256" class="pln"><a href="#n256">256</a></p>
<p id="n257" class="stm run hide_run"><a href="#n257">257</a></p>
<p id="n258" class="pln"><a href="#n258">258</a></p>
<p id="n259" class="pln"><a href="#n259">259</a></p>
<p id="n260" class="pln"><a href="#n260">260</a></p>
<p id="n261" class="pln"><a href="#n261">261</a></p>
<p id="n262" class="pln"><a href="#n262">262</a></p>
<p id="n263" class="stm run hide_run"><a href="#n263">263</a></p>
<p id="n264" class="stm run hide_run"><a href="#n264">264</a></p>
<p id="n265" class="stm run hide_run"><a href="#n265">265</a></p>
<p id="n266" class="stm run hide_run"><a href="#n266">266</a></p>
<p id="n267" class="pln"><a href="#n267">267</a></p>
<p id="n268" class="stm run hide_run"><a href="#n268">268</a></p>
<p id="n269" class="stm run hide_run"><a href="#n269">269</a></p>
<p id="n270" class="stm run hide_run"><a href="#n270">270</a></p>
<p id="n271" class="stm mis"><a href="#n271">271</a></p>
<p id="n272" class="stm mis"><a href="#n272">272</a></p>
<p id="n273" class="pln"><a href="#n273">273</a></p>
<p id="n274" class="stm run hide_run"><a href="#n274">274</a></p>
<p id="n275" class="pln"><a href="#n275">275</a></p>
<p id="n276" class="stm run hide_run"><a href="#n276">276</a></p>
<p id="n277" class="pln"><a href="#n277">277</a></p>
<p id="n278" class="stm run hide_run"><a href="#n278">278</a></p>
<p id="n279" class="pln"><a href="#n279">279</a></p>
<p id="n280" class="stm run hide_run"><a href="#n280">280</a></p>
<p id="n281" class="stm run hide_run"><a href="#n281">281</a></p>
<p id="n282" class="stm run hide_run"><a href="#n282">282</a></p>
<p id="n283" class="stm mis"><a href="#n283">283</a></p>
<p id="n284" class="stm mis"><a href="#n284">284</a></p>
<p id="n285" class="pln"><a href="#n285">285</a></p>
<p id="n286" class="stm run hide_run"><a href="#n286">286</a></p>
<p id="n287" class="pln"><a href="#n287">287</a></p>
<p id="n288" class="pln"><a href="#n288">288</a></p>
<p id="n289" class="pln"><a href="#n289">289</a></p>
<p id="n290" class="pln"><a href="#n290">290</a></p>
<p id="n291" class="pln"><a href="#n291">291</a></p>
<p id="n292" class="stm run hide_run"><a href="#n292">292</a></p>
<p id="n293" class="stm run hide_run"><a href="#n293">293</a></p>
<p id="n294" class="stm mis"><a href="#n294">294</a></p>
<p id="n295" class="stm run hide_run"><a href="#n295">295</a></p>
<p id="n296" class="stm run hide_run"><a href="#n296">296</a></p>
<p id="n297" class="stm mis"><a href="#n297">297</a></p>
<p id="n298" class="stm run hide_run"><a href="#n298">298</a></p>
<p id="n299" class="stm run hide_run"><a href="#n299">299</a></p>
<p id="n300" class="stm run hide_run"><a href="#n300">300</a></p>
<p id="n301" class="pln"><a href="#n301">301</a></p>
<p id="n302" class="stm run hide_run"><a href="#n302">302</a></p>
<p id="n303" class="pln"><a href="#n303">303</a></p>
<p id="n304" class="pln"><a href="#n304">304</a></p>
<p id="n305" class="stm run hide_run"><a href="#n305">305</a></p>
<p id="n306" class="pln"><a href="#n306">306</a></p>
<p id="n307" class="pln"><a href="#n307">307</a></p>
<p id="n308" class="pln"><a href="#n308">308</a></p>
<p id="n309" class="pln"><a href="#n309">309</a></p>
<p id="n310" class="pln"><a href="#n310">310</a></p>
<p id="n311" class="stm run hide_run"><a href="#n311">311</a></p>
<p id="n312" class="stm run hide_run"><a href="#n312">312</a></p>
<p id="n313" class="pln"><a href="#n313">313</a></p>
<p id="n314" class="pln"><a href="#n314">314</a></p>
<p id="n315" class="stm run hide_run"><a href="#n315">315</a></p>
<p id="n316" class="pln"><a href="#n316">316</a></p>
<p id="n317" class="pln"><a href="#n317">317</a></p>
<p id="n318" class="pln"><a href="#n318">318</a></p>
<p id="n319" class="pln"><a href="#n319">319</a></p>
<p id="n320" class="stm run hide_run"><a href="#n320">320</a></p>
<p id="n321" class="stm run hide_run"><a href="#n321">321</a></p>
<p id="n322" class="stm run hide_run"><a href="#n322">322</a></p>
<p id="n323" class="stm run hide_run"><a href="#n323">323</a></p>
<p id="n324" class="stm run hide_run"><a href="#n324">324</a></p>
<p id="n325" class="stm run hide_run"><a href="#n325">325</a></p>
<p id="n326" class="pln"><a href="#n326">326</a></p>
<p id="n327" class="stm run hide_run"><a href="#n327">327</a></p>
<p id="n328" class="stm run hide_run"><a href="#n328">328</a></p>
<p id="n329" class="stm run hide_run"><a href="#n329">329</a></p>
<p id="n330" class="pln"><a href="#n330">330</a></p>
<p id="n331" class="pln"><a href="#n331">331</a></p>
<p id="n332" class="stm run hide_run"><a href="#n332">332</a></p>
<p id="n333" class="stm run hide_run"><a href="#n333">333</a></p>
<p id="n334" class="stm mis"><a href="#n334">334</a></p>
<p id="n335" class="stm mis"><a href="#n335">335</a></p>
<p id="n336" class="pln"><a href="#n336">336</a></p>
<p id="n337" class="stm run hide_run"><a href="#n337">337</a></p>
<p id="n338" class="stm run hide_run"><a href="#n338">338</a></p>
<p id="n339" class="stm run hide_run"><a href="#n339">339</a></p>
<p id="n340" class="stm run hide_run"><a href="#n340">340</a></p>
<p id="n341" class="stm run hide_run"><a href="#n341">341</a></p>
<p id="n342" class="stm run hide_run"><a href="#n342">342</a></p>
<p id="n343" class="pln"><a href="#n343">343</a></p>
<p id="n344" class="stm run hide_run"><a href="#n344">344</a></p>
<p id="n345" class="pln"><a href="#n345">345</a></p>
<p id="n346" class="pln"><a href="#n346">346</a></p>
<p id="n347" class="pln"><a href="#n347">347</a></p>
<p id="n348" class="pln"><a href="#n348">348</a></p>
<p id="n349" class="pln"><a href="#n349">349</a></p>
<p id="n350" class="pln"><a href="#n350">350</a></p>
<p id="n351" class="pln"><a href="#n351">351</a></p>
<p id="n352" class="pln"><a href="#n352">352</a></p>
<p id="n353" class="pln"><a href="#n353">353</a></p>
<p id="n354" class="pln"><a href="#n354">354</a></p>
<p id="n355" class="pln"><a href="#n355">355</a></p>
<p id="n356" class="pln"><a href="#n356">356</a></p>
<p id="n357" class="pln"><a href="#n357">357</a></p>
<p id="n358" class="pln"><a href="#n358">358</a></p>
<p id="n359" class="pln"><a href="#n359">359</a></p>
<p id="n360" class="pln"><a href="#n360">360</a></p>
<p id="n361" class="pln"><a href="#n361">361</a></p>
<p id="n362" class="pln"><a href="#n362">362</a></p>
<p id="n363" class="pln"><a href="#n363">363</a></p>
<p id="n364" class="pln"><a href="#n364">364</a></p>
<p id="n365" class="pln"><a href="#n365">365</a></p>
<p id="n366" class="pln"><a href="#n366">366</a></p>
<p id="n367" class="pln"><a href="#n367">367</a></p>
<p id="n368" class="pln"><a href="#n368">368</a></p>
<p id="n369" class="pln"><a href="#n369">369</a></p>
<p id="n370" class="pln"><a href="#n370">370</a></p>
<p id="n371" class="pln"><a href="#n371">371</a></p>
<p id="n372" class="pln"><a href="#n372">372</a></p>
<p id="n373" class="pln"><a href="#n373">373</a></p>
<p id="n374" class="pln"><a href="#n374">374</a></p>
<p id="n375" class="pln"><a href="#n375">375</a></p>
<p id="n376" class="pln"><a href="#n376">376</a></p>
<p id="n377" class="stm run hide_run"><a href="#n377">377</a></p>
<p id="n378" class="stm run hide_run"><a href="#n378">378</a></p>
<p id="n379" class="stm run hide_run"><a href="#n379">379</a></p>
<p id="n380" class="stm run hide_run"><a href="#n380">380</a></p>
<p id="n381" class="stm run hide_run"><a href="#n381">381</a></p>
<p id="n382" class="stm run hide_run"><a href="#n382">382</a></p>
<p id="n383" class="stm run hide_run"><a href="#n383">383</a></p>
<p id="n384" class="pln"><a href="#n384">384</a></p>
<p id="n385" class="pln"><a href="#n385">385</a></p>
<p id="n386" class="stm run hide_run"><a href="#n386">386</a></p>
<p id="n387" class="pln"><a href="#n387">387</a></p>
<p id="n388" class="pln"><a href="#n388">388</a></p>
<p id="n389" class="pln"><a href="#n389">389</a></p>
<p id="n390" class="pln"><a href="#n390">390</a></p>
<p id="n391" class="pln"><a href="#n391">391</a></p>
<p id="n392" class="pln"><a href="#n392">392</a></p>
<p id="n393" class="pln"><a href="#n393">393</a></p>
<p id="n394" class="pln"><a href="#n394">394</a></p>
<p id="n395" class="pln"><a href="#n395">395</a></p>
<p id="n396" class="pln"><a href="#n396">396</a></p>
<p id="n397" class="pln"><a href="#n397">397</a></p>
<p id="n398" class="pln"><a href="#n398">398</a></p>
<p id="n399" class="pln"><a href="#n399">399</a></p>
<p id="n400" class="stm run hide_run"><a href="#n400">400</a></p>
<p id="n401" class="pln"><a href="#n401">401</a></p>
<p id="n402" class="stm run hide_run"><a href="#n402">402</a></p>
<p id="n403" class="stm run hide_run"><a href="#n403">403</a></p>
<p id="n404" class="stm run hide_run"><a href="#n404">404</a></p>
<p id="n405" class="stm mis"><a href="#n405">405</a></p>
<p id="n406" class="stm mis"><a href="#n406">406</a></p>
<p id="n407" class="stm mis"><a href="#n407">407</a></p>
<p id="n408" class="stm mis"><a href="#n408">408</a></p>
<p id="n409" class="pln"><a href="#n409">409</a></p>
<p id="n410" class="stm run hide_run"><a href="#n410">410</a></p>
<p id="n411" class="stm mis"><a href="#n411">411</a></p>
<p id="n412" class="stm mis"><a href="#n412">412</a></p>
<p id="n413" class="stm run hide_run"><a href="#n413">413</a></p>
<p id="n414" class="stm run hide_run"><a href="#n414">414</a></p>
<p id="n415" class="pln"><a href="#n415">415</a></p>
<p id="n416" class="stm run hide_run"><a href="#n416">416</a></p>
<p id="n417" class="stm mis"><a href="#n417">417</a></p>
<p id="n418" class="pln"><a href="#n418">418</a></p>
<p id="n419" class="stm mis"><a href="#n419">419</a></p>
<p id="n420" class="stm run hide_run"><a href="#n420">420</a></p>
<p id="n421" class="stm run hide_run"><a href="#n421">421</a></p>
<p id="n422" class="pln"><a href="#n422">422</a></p>
<p id="n423" class="stm run hide_run"><a href="#n423">423</a></p>
<p id="n424" class="stm mis"><a href="#n424">424</a></p>
<p id="n425" class="stm mis"><a href="#n425">425</a></p>
<p id="n426" class="stm run hide_run"><a href="#n426">426</a></p>
<p id="n427" class="stm run hide_run"><a href="#n427">427</a></p>
<p id="n428" class="pln"><a href="#n428">428</a></p>
<p id="n429" class="stm run hide_run"><a href="#n429">429</a></p>
<p id="n430" class="stm run hide_run"><a href="#n430">430</a></p>
<p id="n431" class="stm run hide_run"><a href="#n431">431</a></p>
<p id="n432" class="pln"><a href="#n432">432</a></p>
<p id="n433" class="stm run hide_run"><a href="#n433">433</a></p>
<p id="n434" class="stm run hide_run"><a href="#n434">434</a></p>
<p id="n435" class="stm run hide_run"><a href="#n435">435</a></p>
<p id="n436" class="stm run hide_run"><a href="#n436">436</a></p>
<p id="n437" class="pln"><a href="#n437">437</a></p>
<p id="n438" class="stm run hide_run"><a href="#n438">438</a></p>
<p id="n439" class="pln"><a href="#n439">439</a></p>
<p id="n440" class="pln"><a href="#n440">440</a></p>
<p id="n441" class="stm run hide_run"><a href="#n441">441</a></p>
<p id="n442" class="pln"><a href="#n442">442</a></p>
<p id="n443" class="pln"><a href="#n443">443</a></p>
<p id="n444" class="pln"><a href="#n444">444</a></p>
<p id="n445" class="pln"><a href="#n445">445</a></p>
<p id="n446" class="stm run hide_run"><a href="#n446">446</a></p>
<p id="n447" class="stm run hide_run"><a href="#n447">447</a></p>
<p id="n448" class="stm run hide_run"><a href="#n448">448</a></p>
<p id="n449" class="stm run hide_run"><a href="#n449">449</a></p>
<p id="n450" class="stm run hide_run"><a href="#n450">450</a></p>
<p id="n451" class="stm run hide_run"><a href="#n451">451</a></p>
<p id="n452" class="stm run hide_run"><a href="#n452">452</a></p>
<p id="n453" class="stm run hide_run"><a href="#n453">453</a></p>
<p id="n454" class="stm run hide_run"><a href="#n454">454</a></p>
<p id="n455" class="stm run hide_run"><a href="#n455">455</a></p>
<p id="n456" class="stm run hide_run"><a href="#n456">456</a></p>
<p id="n457" class="stm run hide_run"><a href="#n457">457</a></p>
<p id="n458" class="stm run hide_run"><a href="#n458">458</a></p>
<p id="n459" class="stm run hide_run"><a href="#n459">459</a></p>
<p id="n460" class="stm run hide_run"><a href="#n460">460</a></p>
<p id="n461" class="pln"><a href="#n461">461</a></p>
<p id="n462" class="stm run hide_run"><a href="#n462">462</a></p>
<p id="n463" class="stm run hide_run"><a href="#n463">463</a></p>
<p id="n464" class="stm run hide_run"><a href="#n464">464</a></p>
<p id="n465" class="pln"><a href="#n465">465</a></p>
<p id="n466" class="stm run hide_run"><a href="#n466">466</a></p>
<p id="n467" class="stm mis"><a href="#n467">467</a></p>
<p id="n468" class="stm mis"><a href="#n468">468</a></p>
<p id="n469" class="stm mis"><a href="#n469">469</a></p>
<p id="n470" class="pln"><a href="#n470">470</a></p>
<p id="n471" class="stm run hide_run"><a href="#n471">471</a></p>
<p id="n472" class="pln"><a href="#n472">472</a></p>
<p id="n473" class="stm run hide_run"><a href="#n473">473</a></p>
<p id="n474" class="pln"><a href="#n474">474</a></p>
<p id="n475" class="pln"><a href="#n475">475</a></p>
<p id="n476" class="stm run hide_run"><a href="#n476">476</a></p>
<p id="n477" class="stm run hide_run"><a href="#n477">477</a></p>
<p id="n478" class="stm mis"><a href="#n478">478</a></p>
<p id="n479" class="pln"><a href="#n479">479</a></p>
<p id="n480" class="stm run hide_run"><a href="#n480">480</a></p>
<p id="n481" class="stm run hide_run"><a href="#n481">481</a></p>
<p id="n482" class="stm run hide_run"><a href="#n482">482</a></p>
<p id="n483" class="pln"><a href="#n483">483</a></p>
<p id="n484" class="pln"><a href="#n484">484</a></p>
<p id="n485" class="pln"><a href="#n485">485</a></p>
<p id="n486" class="pln"><a href="#n486">486</a></p>
<p id="n487" class="pln"><a href="#n487">487</a></p>
<p id="n488" class="pln"><a href="#n488">488</a></p>
<p id="n489" class="pln"><a href="#n489">489</a></p>
<p id="n490" class="pln"><a href="#n490">490</a></p>
<p id="n491" class="pln"><a href="#n491">491</a></p>
<p id="n492" class="pln"><a href="#n492">492</a></p>
<p id="n493" class="pln"><a href="#n493">493</a></p>
<p id="n494" class="pln"><a href="#n494">494</a></p>
<p id="n495" class="pln"><a href="#n495">495</a></p>
<p id="n496" class="pln"><a href="#n496">496</a></p>
<p id="n497" class="pln"><a href="#n497">497</a></p>
<p id="n498" class="pln"><a href="#n498">498</a></p>
<p id="n499" class="pln"><a href="#n499">499</a></p>
<p id="n500" class="pln"><a href="#n500">500</a></p>
<p id="n501" class="stm run hide_run"><a href="#n501">501</a></p>
<p id="n502" class="stm mis"><a href="#n502">502</a></p>
<p id="n503" class="pln"><a href="#n503">503</a></p>
<p id="n504" class="stm run hide_run"><a href="#n504">504</a></p>
<p id="n505" class="stm run hide_run"><a href="#n505">505</a></p>
<p id="n506" class="pln"><a href="#n506">506</a></p>
<p id="n507" class="stm run hide_run"><a href="#n507">507</a></p>
<p id="n508" class="stm run hide_run"><a href="#n508">508</a></p>
<p id="n509" class="stm run hide_run"><a href="#n509">509</a></p>
<p id="n510" class="stm run hide_run"><a href="#n510">510</a></p>
<p id="n511" class="stm run hide_run"><a href="#n511">511</a></p>
<p id="n512" class="stm run hide_run"><a href="#n512">512</a></p>
<p id="n513" class="stm run hide_run"><a href="#n513">513</a></p>
<p id="n514" class="stm run hide_run"><a href="#n514">514</a></p>

            </td>
            <td class="text">
<p id="t1" class="pln"><span class="com"># -*- coding: utf-8 -*-</span><span class="strut">&nbsp;</span></p>
<p id="t2" class="pln"><span class="com"># @Time    : 2019-05-31 14:10</span><span class="strut">&nbsp;</span></p>
<p id="t3" class="pln"><span class="com"># <AUTHOR> wu</span><span class="strut">&nbsp;</span></p>
<p id="t4" class="pln"><span class="com"># @File    : operate.py</span><span class="strut">&nbsp;</span></p>
<p id="t5" class="pln"><span class="com"># @Software: PyCharm</span><span class="strut">&nbsp;</span></p>
<p id="t6" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t7" class="stm run hide_run"><span class="str">"""module explore"""</span><span class="strut">&nbsp;</span></p>
<p id="t8" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t9" class="stm run hide_run"><span class="key">import</span> <span class="nam">datetime</span><span class="strut">&nbsp;</span></p>
<p id="t10" class="stm run hide_run"><span class="key">import</span> <span class="nam">json</span><span class="strut">&nbsp;</span></p>
<p id="t11" class="stm run hide_run"><span class="key">import</span> <span class="nam">time</span><span class="strut">&nbsp;</span></p>
<p id="t12" class="stm run hide_run"><span class="key">import</span> <span class="nam">hashlib</span><span class="strut">&nbsp;</span></p>
<p id="t13" class="stm run hide_run"><span class="key">from</span> <span class="nam">flask</span> <span class="key">import</span> <span class="nam">request</span><span class="strut">&nbsp;</span></p>
<p id="t14" class="stm run hide_run"><span class="key">from</span> <span class="nam">flask_restful</span> <span class="key">import</span> <span class="nam">Resource</span><span class="strut">&nbsp;</span></p>
<p id="t15" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span> <span class="key">import</span> <span class="nam">logger</span><span class="strut">&nbsp;</span></p>
<p id="t16" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">utils</span> <span class="key">import</span> <span class="nam">flask_response</span><span class="strut">&nbsp;</span></p>
<p id="t17" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">database</span> <span class="key">import</span> <span class="nam">MongoDB</span><span class="strut">&nbsp;</span></p>
<p id="t18" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">json_format</span> <span class="key">import</span> <span class="nam">JSONEncoder</span><span class="strut">&nbsp;</span></p>
<p id="t19" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">param_check</span> <span class="key">import</span> <span class="nam">Validator</span><span class="op">,</span> <span class="nam">check_flask_args</span><span class="strut">&nbsp;</span></p>
<p id="t20" class="stm run hide_run"><span class="key">from</span> <span class="nam">celery_tasks</span> <span class="key">import</span> <span class="nam">explore_task</span><span class="strut">&nbsp;</span></p>
<p id="t21" class="pln"><span class="com"># from api_1_0.back_alert.explore_task_stats import ExploreTaskStats</span><span class="strut">&nbsp;</span></p>
<p id="t22" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t23" class="stm run hide_run"><span class="nam">LOG</span> <span class="op">=</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">Logger</span><span class="op">(</span><span class="str">"debug"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t24" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t25" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t26" class="stm run hide_run"><span class="key">def</span> <span class="nam">create_task_id</span><span class="op">(</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t27" class="pln">    <span class="str">"""create task id"""</span><span class="strut">&nbsp;</span></p>
<p id="t28" class="stm run hide_run">    <span class="nam">md5_str</span> <span class="op">=</span> <span class="nam">hashlib</span><span class="op">.</span><span class="nam">md5</span><span class="op">(</span><span class="nam">str</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">clock</span><span class="op">(</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">encode</span><span class="op">(</span><span class="str">'utf-8'</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t29" class="stm run hide_run">    <span class="key">return</span> <span class="nam">md5_str</span><span class="op">.</span><span class="nam">hexdigest</span><span class="op">(</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t30" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t31" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t32" class="pln"><span class="com"># &#26356;&#26032;&#29305;&#24449;&#32452;&#20013;&#20219;&#21153;&#21015;&#34920;</span><span class="strut">&nbsp;</span></p>
<p id="t33" class="stm run hide_run"><span class="key">def</span> <span class="nam">feature_group_task_process</span><span class="op">(</span><span class="nam">feature_grp_id</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">,</span> <span class="nam">action</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t34" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t35" class="pln"><span class="str">    :param feature_grp_id:</span><span class="strut">&nbsp;</span></p>
<p id="t36" class="pln"><span class="str">    :param task_id:</span><span class="strut">&nbsp;</span></p>
<p id="t37" class="pln"><span class="str">    :param action:</span><span class="strut">&nbsp;</span></p>
<p id="t38" class="pln"><span class="str">    :return:</span><span class="strut">&nbsp;</span></p>
<p id="t39" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t40" class="stm run hide_run">    <span class="nam">mongodb</span> <span class="op">=</span> <span class="nam">MongoDB</span><span class="op">(</span><span class="str">"ndr"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t41" class="stm run hide_run">    <span class="nam">group</span> <span class="op">=</span> <span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">feature_grp_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t42" class="stm run hide_run">    <span class="key">if</span> <span class="nam">group</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t43" class="stm run hide_run">        <span class="key">if</span> <span class="nam">action</span> <span class="op">==</span> <span class="str">'add'</span> <span class="key">and</span> <span class="nam">task_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t44" class="stm run hide_run">            <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t45" class="stm run hide_run">            <span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">feature_grp_id</span><span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t46" class="pln">                           <span class="op">{</span><span class="str">'taskList'</span><span class="op">:</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t47" class="pln">        <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t48" class="stm run hide_run">            <span class="key">if</span> <span class="nam">task_id</span> <span class="key">in</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t49" class="stm run hide_run">                <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">.</span><span class="nam">remove</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t50" class="stm run hide_run">                <span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">feature_grp_id</span><span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t51" class="pln">                               <span class="op">{</span><span class="str">'taskList'</span><span class="op">:</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t52" class="stm run hide_run">        <span class="key">return</span> <span class="key">True</span><span class="strut">&nbsp;</span></p>
<p id="t53" class="stm mis">    <span class="key">return</span> <span class="key">False</span><span class="strut">&nbsp;</span></p>
<p id="t54" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t55" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t56" class="pln"><span class="com"># parameter check</span><span class="strut">&nbsp;</span></p>
<p id="t57" class="stm run hide_run"><span class="key">def</span> <span class="nam">param_check</span><span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">start_date</span><span class="op">,</span> <span class="nam">end_date</span><span class="op">,</span> <span class="nam">feature_grp_list</span><span class="op">,</span> <span class="nam">mongodb</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t58" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t59" class="pln"><span class="str">    :param name:</span><span class="strut">&nbsp;</span></p>
<p id="t60" class="pln"><span class="str">    :param start_date:</span><span class="strut">&nbsp;</span></p>
<p id="t61" class="pln"><span class="str">    :param end_date:</span><span class="strut">&nbsp;</span></p>
<p id="t62" class="pln"><span class="str">    :param feature_grp_list:</span><span class="strut">&nbsp;</span></p>
<p id="t63" class="pln"><span class="str">    :param mongodb:</span><span class="strut">&nbsp;</span></p>
<p id="t64" class="pln"><span class="str">    :return:</span><span class="strut">&nbsp;</span></p>
<p id="t65" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t66" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t67" class="stm run hide_run">    <span class="nam">rst</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t68" class="stm run hide_run">    <span class="key">if</span> <span class="nam">name</span> <span class="op">==</span> <span class="str">""</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t69" class="stm mis">        <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">'The task name cannot be empty'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t70" class="stm run hide_run">    <span class="key">elif</span> <span class="nam">len</span><span class="op">(</span><span class="nam">name</span><span class="op">)</span> <span class="op">></span> <span class="num">255</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t71" class="stm mis">        <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">'Task name cannot exceed 255 characters in length'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t72" class="stm run hide_run">    <span class="key">try</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t73" class="stm run hide_run">        <span class="key">if</span> <span class="nam">start_date</span> <span class="op">!=</span> <span class="str">""</span> <span class="key">and</span> <span class="nam">end_date</span> <span class="op">!=</span> <span class="str">""</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t74" class="stm run hide_run">            <span class="nam">s_time</span> <span class="op">=</span> <span class="nam">time</span><span class="op">.</span><span class="nam">mktime</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">strptime</span><span class="op">(</span><span class="nam">start_date</span><span class="op">,</span> <span class="str">'%Y-%m-%d'</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t75" class="stm run hide_run">            <span class="nam">e_time</span> <span class="op">=</span> <span class="nam">time</span><span class="op">.</span><span class="nam">mktime</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">strptime</span><span class="op">(</span><span class="nam">end_date</span><span class="op">,</span> <span class="str">'%Y-%m-%d'</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t76" class="stm run hide_run">            <span class="key">if</span> <span class="nam">s_time</span> <span class="op">-</span> <span class="nam">e_time</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t77" class="stm mis">                <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"Start date must be earlier than end date"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t78" class="pln">        <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t79" class="stm mis">            <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"Start time or end time cannot be empty"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t80" class="stm mis">    <span class="key">except</span> <span class="nam">ValueError</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t81" class="stm mis">        <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"Start time or end time format error"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t82" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t83" class="stm run hide_run">    <span class="key">if</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t84" class="stm run hide_run">        <span class="nam">tmp_sid</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t85" class="stm run hide_run">        <span class="nam">sid_list</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t86" class="stm run hide_run">        <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t87" class="stm run hide_run">            <span class="nam">item</span> <span class="op">=</span> <span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">grp_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t88" class="stm run hide_run">            <span class="key">if</span> <span class="nam">item</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t89" class="stm run hide_run">                <span class="nam">tmp_sid</span> <span class="op">+=</span> <span class="nam">item</span><span class="op">[</span><span class="str">'sidList'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t90" class="stm run hide_run">                <span class="key">for</span> <span class="nam">sid</span> <span class="key">in</span> <span class="nam">tmp_sid</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t91" class="stm run hide_run">                    <span class="nam">sid_in_mongo</span> <span class="op">=</span> <span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'rules'</span><span class="op">,</span> <span class="op">{</span><span class="str">'sid'</span><span class="op">:</span> <span class="nam">sid</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t92" class="stm run hide_run">                    <span class="key">if</span> <span class="nam">sid_in_mongo</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t93" class="stm mis">                        <span class="nam">sid_list</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="nam">grp_id</span><span class="op">:</span> <span class="nam">sid</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t94" class="pln">            <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t95" class="stm mis">                <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"Feature group [ %s ] is not exist"</span> <span class="op">%</span> <span class="nam">grp_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t96" class="stm run hide_run">        <span class="key">if</span> <span class="nam">sid_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t97" class="stm mis">            <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"feature group %s  is not exist"</span> <span class="op">%</span> <span class="nam">sid_list</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t98" class="pln">    <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t99" class="stm mis">        <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"Feature group list cannot be empty"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t100" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t101" class="stm run hide_run">    <span class="key">return</span> <span class="nam">rst</span><span class="strut">&nbsp;</span></p>
<p id="t102" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t103" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t104" class="stm run hide_run"><span class="key">def</span> <span class="nam">get_feature_count</span><span class="op">(</span><span class="nam">feature_grp_list</span><span class="op">,</span> <span class="nam">mongodb</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t105" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t106" class="pln"><span class="str">    :param feature_grp_list:</span><span class="strut">&nbsp;</span></p>
<p id="t107" class="pln"><span class="str">    :param mongodb:</span><span class="strut">&nbsp;</span></p>
<p id="t108" class="pln"><span class="str">    :return:  &#21333;&#20010;&#20219;&#21153;&#20013;&#23454;&#38469;&#30340;&#29305;&#24449;&#25968;&#37327;</span><span class="strut">&nbsp;</span></p>
<p id="t109" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t110" class="stm run hide_run">    <span class="nam">sid_list</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t111" class="stm run hide_run">    <span class="key">if</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t112" class="stm run hide_run">        <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t113" class="stm run hide_run">            <span class="nam">item</span> <span class="op">=</span> <span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">grp_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t114" class="stm run hide_run">            <span class="key">if</span> <span class="nam">item</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t115" class="stm run hide_run">                <span class="nam">sid_list</span> <span class="op">+=</span> <span class="nam">item</span><span class="op">[</span><span class="str">'sidList'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t116" class="stm run hide_run">    <span class="key">return</span> <span class="nam">len</span><span class="op">(</span><span class="nam">list</span><span class="op">(</span><span class="nam">set</span><span class="op">(</span><span class="nam">sid_list</span><span class="op">)</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t117" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t118" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t119" class="stm run hide_run"><span class="key">def</span> <span class="nam">get_duration_time</span><span class="op">(</span><span class="nam">s_time</span><span class="op">,</span> <span class="nam">e_time</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t120" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t121" class="pln"><span class="str">    :param s_time: &#24320;&#22987;&#26102;&#38388;</span><span class="strut">&nbsp;</span></p>
<p id="t122" class="pln"><span class="str">    :param e_time: &#32467;&#26463;&#26102;&#38388;</span><span class="strut">&nbsp;</span></p>
<p id="t123" class="pln"><span class="str">    :return: &#26102;&#38388;&#38388;&#38548;(&#31186;)</span><span class="strut">&nbsp;</span></p>
<p id="t124" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t125" class="stm run hide_run">    <span class="key">if</span> <span class="nam">s_time</span> <span class="op">==</span> <span class="str">""</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t126" class="stm mis">        <span class="key">return</span> <span class="num">0</span><span class="strut">&nbsp;</span></p>
<p id="t127" class="stm run hide_run">    <span class="nam">start</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">mktime</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">strptime</span><span class="op">(</span><span class="nam">s_time</span><span class="op">,</span> <span class="str">"%Y-%m-%d %H:%M:%S"</span><span class="op">)</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t128" class="stm run hide_run">    <span class="key">if</span> <span class="nam">e_time</span> <span class="op">!=</span> <span class="str">""</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t129" class="stm mis">        <span class="nam">end</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">mktime</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">strptime</span><span class="op">(</span><span class="nam">e_time</span><span class="op">,</span> <span class="str">"%Y-%m-%d %H:%M:%S"</span><span class="op">)</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t130" class="pln">    <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t131" class="stm run hide_run">        <span class="nam">end</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">time</span><span class="op">(</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t132" class="stm run hide_run">    <span class="key">return</span> <span class="nam">end</span> <span class="op">-</span> <span class="nam">start</span><span class="strut">&nbsp;</span></p>
<p id="t133" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t134" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t135" class="stm run hide_run"><span class="key">class</span> <span class="nam">ExploreTaskWithId</span><span class="op">(</span><span class="nam">Resource</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t136" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t137" class="pln"><span class="str">        &#21333;&#20010;&#25506;&#32034;&#20219;&#21153;&#30340;&#33719;&#21462;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t138" class="pln"><span class="str">        &#21333;&#20010;&#25506;&#32034;&#20219;&#21153;&#30340;&#21024;&#38500;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t139" class="pln"><span class="str">        &#21333;&#20010;&#25506;&#32034;&#20219;&#21153;&#30340;&#20462;&#25913;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t140" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t141" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t142" class="stm run hide_run">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t143" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span> <span class="op">=</span> <span class="nam">MongoDB</span><span class="op">(</span><span class="str">"ndr"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t144" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t145" class="pln">    <span class="com"># get one task</span><span class="strut">&nbsp;</span></p>
<p id="t146" class="stm run hide_run">    <span class="key">def</span> <span class="nam">get</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t147" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t148" class="pln"><span class="str">        :param task_id:</span><span class="strut">&nbsp;</span></p>
<p id="t149" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t150" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t151" class="stm run hide_run">        <span class="nam">rst</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t152" class="pln">                                    <span class="op">{</span><span class="str">"_id"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t153" class="pln">                                     <span class="str">"taskName"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t154" class="pln">                                     <span class="str">"taskId"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t155" class="pln">                                     <span class="str">"curFeatureGrpList"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t156" class="pln">                                     <span class="str">"createTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t157" class="pln">                                     <span class="str">"exploreStartDate"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t158" class="pln">                                     <span class="str">"exploreEndDate"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t159" class="pln">                                     <span class="str">"sidList"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t160" class="pln">                                     <span class="str">"startTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t161" class="pln">                                     <span class="str">"endTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t162" class="pln">                                     <span class="str">"status"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t163" class="pln">                                     <span class="str">"process"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t164" class="pln">                                     <span class="str">"remainingTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t165" class="pln">                                     <span class="str">"flowSize"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t166" class="pln">                                     <span class="str">"celeryId"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t167" class="pln">                                     <span class="str">"statisticsInfo"</span><span class="op">:</span> <span class="num">1</span><span class="strut">&nbsp;</span></p>
<p id="t168" class="pln">                                     <span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t169" class="stm run hide_run">        <span class="key">if</span> <span class="nam">rst</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t170" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">"Not found"</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t171" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t172" class="pln">        <span class="com"># &#33719;&#21462;&#32479;&#35745;&#20449;&#24687;&#25968;&#25454;</span><span class="strut">&nbsp;</span></p>
<p id="t173" class="stm run hide_run">        <span class="nam">rst</span><span class="op">[</span><span class="str">"statisticsInfo"</span><span class="op">]</span><span class="op">[</span><span class="str">"curFeatureCount"</span><span class="op">]</span> <span class="op">=</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t174" class="pln">            <span class="nam">get_feature_count</span><span class="op">(</span><span class="nam">rst</span><span class="op">[</span><span class="str">'curFeatureGrpList'</span><span class="op">]</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t175" class="stm run hide_run">        <span class="nam">rst</span><span class="op">[</span><span class="str">"statisticsInfo"</span><span class="op">]</span><span class="op">[</span><span class="str">"flowSize"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">rst</span><span class="op">[</span><span class="str">"flowSize"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t176" class="stm run hide_run">        <span class="nam">rst</span><span class="op">[</span><span class="str">"statisticsInfo"</span><span class="op">]</span><span class="op">[</span><span class="str">"durationTime"</span><span class="op">]</span> <span class="op">=</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t177" class="pln">            <span class="nam">get_duration_time</span><span class="op">(</span><span class="nam">rst</span><span class="op">[</span><span class="str">'startTime'</span><span class="op">]</span><span class="op">,</span> <span class="nam">rst</span><span class="op">[</span><span class="str">'endTime'</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t178" class="stm run hide_run">        <span class="nam">rst</span><span class="op">.</span><span class="nam">pop</span><span class="op">(</span><span class="str">"flowSize"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t179" class="stm run hide_run">        <span class="nam">col_fmt</span> <span class="op">=</span> <span class="nam">JSONEncoder</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">encode</span><span class="op">(</span><span class="nam">rst</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t180" class="stm run hide_run">        <span class="nam">col_rst</span> <span class="op">=</span> <span class="nam">json</span><span class="op">.</span><span class="nam">loads</span><span class="op">(</span><span class="nam">col_fmt</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t181" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="nam">col_rst</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t182" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t183" class="pln">    <span class="com"># delete task</span><span class="strut">&nbsp;</span></p>
<p id="t184" class="stm run hide_run">    <span class="key">def</span> <span class="nam">delete</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t185" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t186" class="pln"><span class="str">        :param task_id:</span><span class="strut">&nbsp;</span></p>
<p id="t187" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t188" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t189" class="stm run hide_run">        <span class="nam">task</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">"back_explore"</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t190" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t191" class="pln">        <span class="com"># scheduler delete process</span><span class="strut">&nbsp;</span></p>
<p id="t192" class="stm run hide_run">        <span class="key">if</span> <span class="nam">task</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t193" class="stm run hide_run">            <span class="nam">message</span> <span class="op">=</span> <span class="str">'Task does not exist.'</span><span class="strut">&nbsp;</span></p>
<p id="t194" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"task: %s delete failed.%s"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">task_id</span><span class="op">,</span> <span class="nam">message</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t195" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t196" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t197" class="stm run hide_run">        <span class="key">if</span> <span class="nam">task</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span> <span class="key">in</span> <span class="op">[</span><span class="str">'pause'</span><span class="op">,</span> <span class="str">'running'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t198" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">"Status is %s,can not delete."</span> <span class="op">%</span> <span class="nam">task</span><span class="op">[</span><span class="str">"status"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t199" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"task: %s, %s deleted failed. Status: %s"</span> <span class="op">%</span><span class="strut">&nbsp;</span></p>
<p id="t200" class="pln">                      <span class="op">(</span><span class="nam">task</span><span class="op">[</span><span class="str">"taskName"</span><span class="op">]</span><span class="op">,</span> <span class="nam">task</span><span class="op">[</span><span class="str">"taskId"</span><span class="op">]</span><span class="op">,</span> <span class="nam">task</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t201" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t202" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t203" class="stm run hide_run">        <span class="nam">explore_task</span><span class="op">.</span><span class="nam">stop_task</span><span class="op">(</span><span class="nam">task</span><span class="op">[</span><span class="str">'taskId'</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t204" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">delete</span><span class="op">(</span><span class="str">"back_explore"</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t205" class="stm run hide_run">        <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">task</span><span class="op">[</span><span class="str">'curFeatureGrpList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t206" class="stm run hide_run">            <span class="nam">feature_group_task_process</span><span class="op">(</span><span class="nam">grp_id</span><span class="op">,</span> <span class="nam">task</span><span class="op">[</span><span class="str">'taskId'</span><span class="op">]</span><span class="op">,</span> <span class="str">'del'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t207" class="stm run hide_run">        <span class="nam">explore_task</span><span class="op">.</span><span class="nam">start_task</span><span class="op">.</span><span class="nam">delay</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t208" class="stm run hide_run">        <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"task: %s, %s deleted success."</span> <span class="op">%</span> <span class="op">(</span><span class="nam">task</span><span class="op">[</span><span class="str">"taskName"</span><span class="op">]</span><span class="op">,</span> <span class="nam">task</span><span class="op">[</span><span class="str">"taskId"</span><span class="op">]</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t209" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t210" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t211" class="stm run hide_run">    <span class="key">def</span> <span class="nam">task_modify</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">old_task</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">,</span> <span class="nam">action</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t212" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t213" class="pln"><span class="str">        :param old_task:</span><span class="strut">&nbsp;</span></p>
<p id="t214" class="pln"><span class="str">        :param task_id:</span><span class="strut">&nbsp;</span></p>
<p id="t215" class="pln"><span class="str">        :param action:</span><span class="strut">&nbsp;</span></p>
<p id="t216" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t217" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t218" class="stm run hide_run">        <span class="key">if</span> <span class="nam">action</span> <span class="op">==</span> <span class="str">'restart'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t219" class="stm run hide_run">            <span class="key">if</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span> <span class="key">in</span> <span class="op">[</span><span class="str">'running'</span><span class="op">,</span> <span class="str">'pause'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t220" class="stm mis">                <span class="nam">message</span> <span class="op">=</span> <span class="str">"Status is %s,can not restart"</span> <span class="op">%</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">"status"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t221" class="stm mis">                <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t222" class="stm run hide_run">            <span class="nam">pre_grp_list</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t223" class="stm run hide_run">            <span class="key">if</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">"curFeatureGrpList"</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t224" class="stm run hide_run">                <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">"curFeatureGrpList"</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t225" class="stm run hide_run">                    <span class="nam">item</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">grp_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t226" class="stm run hide_run">                    <span class="nam">pre_grp_list</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="nam">item</span><span class="op">[</span><span class="str">'groupName'</span><span class="op">]</span><span class="op">:</span> <span class="nam">item</span><span class="op">[</span><span class="str">'sidList'</span><span class="op">]</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t227" class="stm run hide_run">            <span class="nam">explore_task</span><span class="op">.</span><span class="nam">start_task</span><span class="op">.</span><span class="nam">delay</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t228" class="stm run hide_run">            <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t229" class="pln">                                <span class="op">{</span><span class="str">'action'</span><span class="op">:</span> <span class="nam">action</span><span class="op">,</span> <span class="str">'preFeatureGrpList'</span><span class="op">:</span> <span class="nam">pre_grp_list</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t230" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"task: %s restart success."</span> <span class="op">%</span> <span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t231" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t232" class="stm run hide_run">        <span class="key">if</span> <span class="nam">action</span> <span class="op">==</span> <span class="str">'pause'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t233" class="stm run hide_run">            <span class="key">if</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span> <span class="op">!=</span> <span class="str">'running'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t234" class="stm mis">                <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">"Status is %s, can not pause"</span> <span class="op">%</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">"status"</span><span class="op">]</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t235" class="stm run hide_run">            <span class="nam">explore_task</span><span class="op">.</span><span class="nam">pause_task</span><span class="op">.</span><span class="nam">delay</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t236" class="stm run hide_run">            <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">,</span> <span class="op">{</span><span class="str">'action'</span><span class="op">:</span> <span class="nam">action</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t237" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"task: %s pause success."</span> <span class="op">%</span> <span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t238" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t239" class="stm run hide_run">        <span class="key">if</span> <span class="nam">action</span> <span class="op">==</span> <span class="str">'resume'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t240" class="stm run hide_run">            <span class="key">if</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span> <span class="op">!=</span> <span class="str">'pause'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t241" class="stm mis">                <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">"Status is %s,can not resume"</span> <span class="op">%</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">"status"</span><span class="op">]</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t242" class="stm run hide_run">            <span class="nam">explore_task</span><span class="op">.</span><span class="nam">resume_task</span><span class="op">.</span><span class="nam">delay</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t243" class="stm run hide_run">            <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">,</span> <span class="op">{</span><span class="str">'action'</span><span class="op">:</span> <span class="nam">action</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t244" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"task: %s resume success."</span> <span class="op">%</span> <span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t245" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t246" class="stm run hide_run">        <span class="key">if</span> <span class="nam">action</span> <span class="op">==</span> <span class="str">'stop'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t247" class="stm run hide_run">            <span class="key">if</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span> <span class="key">in</span> <span class="op">[</span><span class="str">'stop'</span><span class="op">,</span> <span class="str">'finish'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t248" class="stm run hide_run">                <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">"Status is %s, can not stop"</span> <span class="op">%</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">"status"</span><span class="op">]</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t249" class="stm run hide_run">            <span class="nam">explore_task</span><span class="op">.</span><span class="nam">stop_task</span><span class="op">.</span><span class="nam">delay</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t250" class="stm run hide_run">            <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">,</span> <span class="op">{</span><span class="str">'action'</span><span class="op">:</span> <span class="nam">action</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t251" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"task: %s stop success."</span> <span class="op">%</span> <span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t252" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t253" class="stm mis">        <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"task: %s %s failed.Reason: No such operation"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">task_id</span><span class="op">,</span> <span class="nam">action</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t254" class="stm mis">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">'No such operation'</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t255" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t256" class="pln">    <span class="com"># Modify task</span><span class="strut">&nbsp;</span></p>
<p id="t257" class="stm run hide_run">    <span class="op">@</span><span class="nam">check_flask_args</span><span class="op">(</span><span class="nam">Validator</span><span class="op">(</span><span class="str">"explore_update_schema"</span><span class="op">)</span><span class="op">,</span> <span class="nam">request</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t258" class="pln">    <span class="key">def</span> <span class="nam">put</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t259" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t260" class="pln"><span class="str">        :param task_id:</span><span class="strut">&nbsp;</span></p>
<p id="t261" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t262" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t263" class="stm run hide_run">        <span class="nam">old_task</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t264" class="stm run hide_run">        <span class="key">if</span> <span class="nam">old_task</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t265" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"task: %s update failed. The task does not exist."</span> <span class="op">%</span> <span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t266" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">"The task does not exist."</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t267" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t268" class="stm run hide_run">        <span class="nam">action</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"action"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t269" class="stm run hide_run">        <span class="key">if</span> <span class="nam">action</span> <span class="op">==</span> <span class="str">'update'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t270" class="stm run hide_run">            <span class="key">if</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'status'</span><span class="op">]</span> <span class="key">in</span> <span class="op">[</span><span class="str">'running'</span><span class="op">,</span> <span class="str">'pause'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t271" class="stm mis">                <span class="nam">message</span> <span class="op">=</span> <span class="str">"Status is %s, can not update"</span> <span class="op">%</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">"status"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t272" class="stm mis">                <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t273" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t274" class="stm run hide_run">            <span class="nam">feature_grp_list</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"featureGroupList"</span><span class="op">]</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t275" class="pln">                <span class="key">if</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"featureGroupList"</span><span class="op">]</span> <span class="key">else</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'curFeatureGrpList'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t276" class="stm run hide_run">            <span class="nam">start_date</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"exploreStartDate"</span><span class="op">]</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t277" class="pln">                <span class="key">if</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"exploreStartDate"</span><span class="op">]</span> <span class="key">else</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'exploreStartDate'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t278" class="stm run hide_run">            <span class="nam">end_date</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"exploreEndDate"</span><span class="op">]</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t279" class="pln">                <span class="key">if</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"exploreEndDate"</span><span class="op">]</span> <span class="key">else</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'exploreEndDate'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t280" class="stm run hide_run">            <span class="nam">name</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"name"</span><span class="op">]</span> <span class="key">if</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"name"</span><span class="op">]</span> <span class="key">else</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'name'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t281" class="stm run hide_run">            <span class="nam">rst</span> <span class="op">=</span> <span class="nam">param_check</span><span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">start_date</span><span class="op">,</span> <span class="nam">end_date</span><span class="op">,</span> <span class="nam">feature_grp_list</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t282" class="stm run hide_run">            <span class="key">if</span> <span class="nam">rst</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t283" class="stm mis">                <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"task: %s %s failed.Reason: %s"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">task_id</span><span class="op">,</span> <span class="nam">action</span><span class="op">,</span> <span class="nam">rst</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t284" class="stm mis">                <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">rst</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t285" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t286" class="stm run hide_run">            <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskId"</span><span class="op">:</span> <span class="nam">task_id</span><span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t287" class="pln">                                <span class="op">{</span><span class="str">'action'</span><span class="op">:</span> <span class="nam">action</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t288" class="pln">                                 <span class="str">'taskName'</span><span class="op">:</span> <span class="nam">name</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t289" class="pln">                                 <span class="str">"exploreStartDate"</span><span class="op">:</span> <span class="nam">start_date</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t290" class="pln">                                 <span class="str">"exploreEndDate"</span><span class="op">:</span> <span class="nam">end_date</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t291" class="pln">                                 <span class="str">"curFeatureGrpList"</span><span class="op">:</span> <span class="nam">feature_grp_list</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t292" class="stm run hide_run">            <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t293" class="stm run hide_run">                <span class="key">if</span> <span class="nam">grp_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'curFeatureGrpList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t294" class="stm mis">                    <span class="nam">feature_group_task_process</span><span class="op">(</span><span class="nam">grp_id</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">,</span> <span class="str">'add'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t295" class="stm run hide_run">            <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">old_task</span><span class="op">[</span><span class="str">'curFeatureGrpList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t296" class="stm run hide_run">                <span class="key">if</span> <span class="nam">grp_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t297" class="stm mis">                    <span class="nam">feature_group_task_process</span><span class="op">(</span><span class="nam">grp_id</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">,</span> <span class="str">'del'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t298" class="stm run hide_run">            <span class="nam">explore_task</span><span class="op">.</span><span class="nam">start_task</span><span class="op">.</span><span class="nam">delay</span><span class="op">(</span><span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t299" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"task: %s update success."</span> <span class="op">%</span> <span class="nam">task_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t300" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t301" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t302" class="stm run hide_run">        <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">task_modify</span><span class="op">(</span><span class="nam">old_task</span><span class="op">,</span> <span class="nam">task_id</span><span class="op">,</span> <span class="nam">action</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t303" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t304" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t305" class="stm run hide_run"><span class="key">class</span> <span class="nam">ExploreTask</span><span class="op">(</span><span class="nam">Resource</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t306" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t307" class="pln"><span class="str">        &#21019;&#24314;&#25506;&#32034;&#20219;&#21153;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t308" class="pln"><span class="str">        &#33719;&#21462;&#25152;&#26377;&#25506;&#32034;&#20219;&#21153;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t309" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t310" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t311" class="stm run hide_run">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t312" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span> <span class="op">=</span> <span class="nam">MongoDB</span><span class="op">(</span><span class="str">"ndr"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t313" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t314" class="pln">    <span class="com"># create task</span><span class="strut">&nbsp;</span></p>
<p id="t315" class="stm run hide_run">    <span class="op">@</span><span class="nam">check_flask_args</span><span class="op">(</span><span class="nam">Validator</span><span class="op">(</span><span class="str">"explore_create_schema"</span><span class="op">)</span><span class="op">,</span> <span class="nam">request</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t316" class="pln">    <span class="key">def</span> <span class="nam">post</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t317" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t318" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t319" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t320" class="stm run hide_run">        <span class="nam">name</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"name"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t321" class="stm run hide_run">        <span class="nam">task_name</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="op">{</span><span class="str">"taskName"</span><span class="op">:</span> <span class="nam">name</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t322" class="stm run hide_run">        <span class="key">if</span> <span class="nam">task_name</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t323" class="stm run hide_run">            <span class="nam">message</span> <span class="op">=</span> <span class="str">'The explore task [%s] already exists.'</span> <span class="op">%</span> <span class="nam">name</span><span class="strut">&nbsp;</span></p>
<p id="t324" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"task: created failed.Reason: %s"</span> <span class="op">%</span> <span class="nam">message</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t325" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t326" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t327" class="stm run hide_run">        <span class="nam">start_date</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"exploreStartDate"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t328" class="stm run hide_run">        <span class="nam">end_date</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"exploreEndDate"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t329" class="stm run hide_run">        <span class="nam">feature_grp_list</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"featureGroupList"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t330" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t331" class="pln">        <span class="com"># &#21442;&#25968;&#26816;&#26597;</span><span class="strut">&nbsp;</span></p>
<p id="t332" class="stm run hide_run">        <span class="nam">rst</span> <span class="op">=</span> <span class="nam">param_check</span><span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">start_date</span><span class="op">,</span> <span class="nam">end_date</span><span class="op">,</span> <span class="nam">feature_grp_list</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t333" class="stm run hide_run">        <span class="key">if</span> <span class="nam">rst</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t334" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"task: %s created failed.Reason: %s"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">rst</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t335" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">rst</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t336" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t337" class="stm run hide_run">        <span class="nam">pre_grp_list</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t338" class="stm run hide_run">        <span class="key">if</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t339" class="stm run hide_run">            <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t340" class="stm run hide_run">                <span class="nam">item</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">grp_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t341" class="stm run hide_run">                <span class="key">if</span> <span class="nam">item</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t342" class="stm run hide_run">                    <span class="nam">pre_grp_list</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="nam">item</span><span class="op">[</span><span class="str">'groupName'</span><span class="op">]</span><span class="op">:</span> <span class="nam">item</span><span class="op">[</span><span class="str">'sidList'</span><span class="op">]</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t343" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t344" class="stm run hide_run">        <span class="nam">body</span> <span class="op">=</span> <span class="op">{</span><span class="strut">&nbsp;</span></p>
<p id="t345" class="pln">            <span class="str">"taskName"</span><span class="op">:</span> <span class="nam">name</span><span class="op">,</span>  <span class="com"># &#20219;&#21153;&#21517;&#31216;</span><span class="strut">&nbsp;</span></p>
<p id="t346" class="pln">            <span class="str">"taskId"</span><span class="op">:</span> <span class="nam">create_task_id</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>  <span class="com"># &#20219;&#21153; ID</span><span class="strut">&nbsp;</span></p>
<p id="t347" class="pln">            <span class="str">"action"</span><span class="op">:</span> <span class="str">"start"</span><span class="op">,</span>  <span class="com"># &#20219;&#21153;&#25805;&#20316;&#21160;&#20316;</span><span class="strut">&nbsp;</span></p>
<p id="t348" class="pln">            <span class="str">"createTime"</span><span class="op">:</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">strftime</span><span class="op">(</span><span class="str">'%Y-%m-%d %H:%M:%S'</span><span class="op">)</span><span class="op">,</span>  <span class="com"># &#20219;&#21153;&#21019;&#24314;&#26102;&#38388;</span><span class="strut">&nbsp;</span></p>
<p id="t349" class="pln">            <span class="str">"exploreStartDate"</span><span class="op">:</span> <span class="nam">start_date</span><span class="op">,</span>  <span class="com"># &#22238;&#25918;&#24320;&#22987;&#26085;&#26399;</span><span class="strut">&nbsp;</span></p>
<p id="t350" class="pln">            <span class="str">"exploreEndDate"</span><span class="op">:</span> <span class="nam">end_date</span><span class="op">,</span>  <span class="com"># &#22238;&#25918;&#32467;&#26463;&#26085;&#26399;</span><span class="strut">&nbsp;</span></p>
<p id="t351" class="pln">            <span class="str">"curFeatureGrpList"</span><span class="op">:</span> <span class="nam">feature_grp_list</span><span class="op">,</span>  <span class="com"># &#35268;&#21017;&#21015;&#34920;</span><span class="strut">&nbsp;</span></p>
<p id="t352" class="pln">            <span class="str">"preFeatureGrpList"</span><span class="op">:</span> <span class="nam">pre_grp_list</span><span class="op">,</span>  <span class="com"># &#19978;&#19968;&#27425;&#22238;&#25918;&#20351;&#29992;&#30340;&#29305;&#24449;&#32452;&#21450;&#29305;&#24449;</span><span class="strut">&nbsp;</span></p>
<p id="t353" class="pln">            <span class="str">"allPcapList"</span><span class="op">:</span> <span class="op">[</span><span class="op">]</span><span class="op">,</span>  <span class="com"># &#22238;&#25918;&#21608;&#26399;&#20869;&#30340; pcap &#25991;&#20214;&#21015;&#34920;</span><span class="strut">&nbsp;</span></p>
<p id="t354" class="pln">            <span class="str">"flowSize"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span>  <span class="com"># &#22238;&#25918;&#21608;&#26399;&#20869;&#30340; pcap &#25991;&#20214;&#24635;&#22823;&#23567;(&#23383;&#33410;)</span><span class="strut">&nbsp;</span></p>
<p id="t355" class="pln">            <span class="str">"exploredPcapList"</span><span class="op">:</span> <span class="op">[</span><span class="op">]</span><span class="op">,</span>  <span class="com"># &#24050;&#32463;&#22238;&#25918;&#23436;&#25104;&#30340; pcap &#25991;&#20214;&#21015;&#34920;</span><span class="strut">&nbsp;</span></p>
<p id="t356" class="pln">            <span class="str">"celeryId"</span><span class="op">:</span> <span class="str">""</span><span class="op">,</span>  <span class="com"># celery id</span><span class="strut">&nbsp;</span></p>
<p id="t357" class="pln">            <span class="str">"startTime"</span><span class="op">:</span> <span class="str">""</span><span class="op">,</span>  <span class="com"># &#20219;&#21153;&#24320;&#22987;&#26102;&#38388;</span><span class="strut">&nbsp;</span></p>
<p id="t358" class="pln">            <span class="str">"endTime"</span><span class="op">:</span> <span class="str">""</span><span class="op">,</span>  <span class="com"># &#20219;&#21153;&#32467;&#26463;&#26102;&#38388;</span><span class="strut">&nbsp;</span></p>
<p id="t359" class="pln">            <span class="str">"status"</span><span class="op">:</span> <span class="str">""</span><span class="op">,</span>  <span class="com"># &#20219;&#21153;&#24403;&#21069;&#30340;&#29366;&#24577;&#65306;running, pause, stop ,finish, failed</span><span class="strut">&nbsp;</span></p>
<p id="t360" class="pln">            <span class="str">"process"</span><span class="op">:</span> <span class="num">0.0</span><span class="op">,</span>  <span class="com"># &#20219;&#21153;&#25191;&#34892;&#36827;&#24230;</span><span class="strut">&nbsp;</span></p>
<p id="t361" class="pln">            <span class="str">"remainingTime"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span>  <span class="com"># &#21097;&#20313;&#26102;&#38388;</span><span class="strut">&nbsp;</span></p>
<p id="t362" class="pln">            <span class="str">"statisticsInfo"</span><span class="op">:</span> <span class="op">{</span>  <span class="com"># &#32479;&#35745;&#20449;&#24687;</span><span class="strut">&nbsp;</span></p>
<p id="t363" class="pln">                <span class="str">"killChains"</span><span class="op">:</span> <span class="op">{</span>  <span class="com"># &#26432;&#20260;&#38142;&#38454;&#27573;</span><span class="strut">&nbsp;</span></p>
<p id="t364" class="pln">                    <span class="str">"Recon"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t365" class="pln">                    <span class="str">"Weaponization"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t366" class="pln">                    <span class="str">"Delivery"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t367" class="pln">                    <span class="str">"Exploitation"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t368" class="pln">                    <span class="str">"Beacon"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t369" class="pln">                    <span class="str">"CnC"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t370" class="pln">                    <span class="str">"Actions on Objective"</span><span class="op">:</span> <span class="num">0</span><span class="strut">&nbsp;</span></p>
<p id="t371" class="pln">                <span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t372" class="pln">                <span class="str">"logCount"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span>  <span class="com"># &#26085;&#24535;&#24635;&#25968;</span><span class="strut">&nbsp;</span></p>
<p id="t373" class="pln">                <span class="str">"threatScore"</span><span class="op">:</span> <span class="num">0</span>  <span class="com"># &#23041;&#32961;&#24471;&#20998;</span><span class="strut">&nbsp;</span></p>
<p id="t374" class="pln">            <span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t375" class="pln">        <span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t376" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t377" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">insert_one</span><span class="op">(</span><span class="str">"back_explore"</span><span class="op">,</span> <span class="nam">body</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t378" class="stm run hide_run">        <span class="key">for</span> <span class="nam">grp_id</span> <span class="key">in</span> <span class="nam">feature_grp_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t379" class="stm run hide_run">            <span class="nam">feature_group_task_process</span><span class="op">(</span><span class="nam">grp_id</span><span class="op">,</span> <span class="nam">body</span><span class="op">[</span><span class="str">'taskId'</span><span class="op">]</span><span class="op">,</span> <span class="str">'add'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t380" class="stm run hide_run">        <span class="nam">celery_id</span> <span class="op">=</span> <span class="nam">explore_task</span><span class="op">.</span><span class="nam">start_task</span><span class="op">.</span><span class="nam">delay</span><span class="op">(</span><span class="nam">body</span><span class="op">[</span><span class="str">'taskId'</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t381" class="stm run hide_run">        <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"task: %s, %s created success"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">body</span><span class="op">[</span><span class="str">"taskName"</span><span class="op">]</span><span class="op">,</span> <span class="nam">body</span><span class="op">[</span><span class="str">"taskId"</span><span class="op">]</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t382" class="stm run hide_run">        <span class="nam">data</span> <span class="op">=</span> <span class="op">{</span><span class="str">'celeryId'</span><span class="op">:</span> <span class="nam">str</span><span class="op">(</span><span class="nam">celery_id</span><span class="op">)</span><span class="op">,</span> <span class="str">'taskId'</span><span class="op">:</span> <span class="nam">body</span><span class="op">[</span><span class="str">"taskId"</span><span class="op">]</span><span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t383" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="nam">data</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t384" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t385" class="pln">    <span class="com"># &#26597;&#35810;&#26465;&#20214;&#35299;&#26512;</span><span class="strut">&nbsp;</span></p>
<p id="t386" class="stm run hide_run">    <span class="op">@</span><span class="nam">staticmethod</span><span class="strut">&nbsp;</span></p>
<p id="t387" class="pln">    <span class="key">def</span> <span class="nam">query_condition</span><span class="op">(</span><span class="nam">create_start_time</span><span class="op">,</span> <span class="nam">create_end_time</span><span class="op">,</span> <span class="nam">task_name</span><span class="op">,</span> <span class="nam">kill_chains</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t388" class="pln">                        <span class="nam">vul_min_count</span><span class="op">,</span> <span class="nam">vul_max_count</span><span class="op">,</span> <span class="nam">threat_min_score</span><span class="op">,</span> <span class="nam">threat_max_score</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t389" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t390" class="pln"><span class="str">        :param create_start_time:</span><span class="strut">&nbsp;</span></p>
<p id="t391" class="pln"><span class="str">        :param create_end_time:</span><span class="strut">&nbsp;</span></p>
<p id="t392" class="pln"><span class="str">        :param task_name:</span><span class="strut">&nbsp;</span></p>
<p id="t393" class="pln"><span class="str">        :param kill_chains:</span><span class="strut">&nbsp;</span></p>
<p id="t394" class="pln"><span class="str">        :param vul_min_count:</span><span class="strut">&nbsp;</span></p>
<p id="t395" class="pln"><span class="str">        :param vul_max_count:</span><span class="strut">&nbsp;</span></p>
<p id="t396" class="pln"><span class="str">        :param threat_min_score:</span><span class="strut">&nbsp;</span></p>
<p id="t397" class="pln"><span class="str">        :param threat_max_score:</span><span class="strut">&nbsp;</span></p>
<p id="t398" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t399" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t400" class="stm run hide_run">        <span class="nam">condition</span> <span class="op">=</span> <span class="op">{</span><span class="str">"message"</span><span class="op">:</span> <span class="str">""</span><span class="op">,</span> <span class="str">"flag"</span><span class="op">:</span> <span class="key">False</span><span class="op">,</span> <span class="str">"data"</span><span class="op">:</span> <span class="op">{</span><span class="str">"$and"</span><span class="op">:</span> <span class="op">[</span><span class="op">]</span><span class="op">}</span><span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t401" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t402" class="stm run hide_run">        <span class="key">try</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t403" class="stm run hide_run">            <span class="nam">start</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">mktime</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">strptime</span><span class="op">(</span><span class="nam">create_start_time</span><span class="op">,</span> <span class="str">"%Y-%m-%d %H:%M:%S"</span><span class="op">)</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t404" class="stm run hide_run">            <span class="nam">end</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">mktime</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">strptime</span><span class="op">(</span><span class="nam">create_end_time</span><span class="op">,</span> <span class="str">"%Y-%m-%d %H:%M:%S"</span><span class="op">)</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t405" class="stm mis">        <span class="key">except</span> <span class="nam">ValueError</span> <span class="key">as</span> <span class="nam">msg</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t406" class="stm mis">            <span class="nam">condition</span><span class="op">[</span><span class="str">"message"</span><span class="op">]</span> <span class="op">=</span> <span class="str">"Date or time format error"</span><span class="strut">&nbsp;</span></p>
<p id="t407" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="nam">msg</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t408" class="stm mis">            <span class="key">return</span> <span class="nam">condition</span><span class="strut">&nbsp;</span></p>
<p id="t409" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t410" class="stm run hide_run">        <span class="key">if</span> <span class="nam">start</span> <span class="op">></span> <span class="nam">end</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t411" class="stm mis">            <span class="nam">condition</span><span class="op">[</span><span class="str">"message"</span><span class="op">]</span> <span class="op">=</span> <span class="str">"End time cannot be earlier than start time"</span><span class="strut">&nbsp;</span></p>
<p id="t412" class="stm mis">            <span class="key">return</span> <span class="nam">condition</span><span class="strut">&nbsp;</span></p>
<p id="t413" class="stm run hide_run">        <span class="nam">condition</span><span class="op">[</span><span class="str">"flag"</span><span class="op">]</span> <span class="op">=</span> <span class="key">True</span><span class="strut">&nbsp;</span></p>
<p id="t414" class="stm run hide_run">        <span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">[</span><span class="str">"$and"</span><span class="op">]</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"createTime"</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t415" class="pln">                                          <span class="op">{</span><span class="str">'$gt'</span><span class="op">:</span> <span class="nam">create_start_time</span><span class="op">,</span> <span class="str">'$lt'</span><span class="op">:</span> <span class="nam">create_end_time</span><span class="op">}</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t416" class="stm run hide_run">        <span class="key">if</span> <span class="nam">vul_min_count</span> <span class="op">></span> <span class="nam">vul_max_count</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t417" class="stm mis">            <span class="nam">condition</span><span class="op">[</span><span class="str">"message"</span><span class="op">]</span> <span class="op">=</span> <span class="str">"The maximum number of vulnerabilities"</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t418" class="pln">                                   <span class="str">" cannot be less than the minimum number"</span><span class="strut">&nbsp;</span></p>
<p id="t419" class="stm mis">            <span class="key">return</span> <span class="nam">condition</span><span class="strut">&nbsp;</span></p>
<p id="t420" class="stm run hide_run">        <span class="nam">condition</span><span class="op">[</span><span class="str">"flag"</span><span class="op">]</span> <span class="op">=</span> <span class="key">True</span><span class="strut">&nbsp;</span></p>
<p id="t421" class="stm run hide_run">        <span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">[</span><span class="str">"$and"</span><span class="op">]</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"statisticsInfo.logCount"</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t422" class="pln">                                          <span class="op">{</span><span class="str">'$gt'</span><span class="op">:</span> <span class="nam">vul_min_count</span><span class="op">,</span> <span class="str">'$lt'</span><span class="op">:</span> <span class="nam">vul_max_count</span><span class="op">}</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t423" class="stm run hide_run">        <span class="key">if</span> <span class="nam">threat_min_score</span> <span class="op">></span> <span class="nam">threat_max_score</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t424" class="stm mis">            <span class="nam">condition</span><span class="op">[</span><span class="str">"message"</span><span class="op">]</span> <span class="op">=</span> <span class="str">"The maximum score cannot be less than the minimum score"</span><span class="strut">&nbsp;</span></p>
<p id="t425" class="stm mis">            <span class="key">return</span> <span class="nam">condition</span><span class="strut">&nbsp;</span></p>
<p id="t426" class="stm run hide_run">        <span class="nam">condition</span><span class="op">[</span><span class="str">"flag"</span><span class="op">]</span> <span class="op">=</span> <span class="key">True</span><span class="strut">&nbsp;</span></p>
<p id="t427" class="stm run hide_run">        <span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">[</span><span class="str">"$and"</span><span class="op">]</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"statisticsInfo.threatScore"</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t428" class="pln">                                          <span class="op">{</span><span class="str">'$gt'</span><span class="op">:</span> <span class="nam">threat_min_score</span><span class="op">,</span> <span class="str">'$lt'</span><span class="op">:</span> <span class="nam">threat_max_score</span><span class="op">}</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t429" class="stm run hide_run">        <span class="key">if</span> <span class="nam">task_name</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t430" class="stm run hide_run">            <span class="nam">condition</span><span class="op">[</span><span class="str">"flag"</span><span class="op">]</span> <span class="op">=</span> <span class="key">True</span><span class="strut">&nbsp;</span></p>
<p id="t431" class="stm run hide_run">            <span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">[</span><span class="str">"$and"</span><span class="op">]</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"taskName"</span><span class="op">:</span> <span class="op">{</span><span class="str">'$regex'</span><span class="op">:</span> <span class="nam">task_name</span><span class="op">,</span> <span class="str">'$options'</span><span class="op">:</span> <span class="str">'i'</span><span class="op">}</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t432" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t433" class="stm run hide_run">        <span class="key">if</span> <span class="nam">kill_chains</span> <span class="op">!=</span> <span class="str">""</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t434" class="stm run hide_run">            <span class="nam">condition</span><span class="op">[</span><span class="str">"flag"</span><span class="op">]</span> <span class="op">=</span> <span class="key">True</span><span class="strut">&nbsp;</span></p>
<p id="t435" class="stm run hide_run">            <span class="key">for</span> <span class="nam">step</span> <span class="key">in</span> <span class="nam">kill_chains</span><span class="op">.</span><span class="nam">split</span><span class="op">(</span><span class="str">','</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t436" class="stm run hide_run">                <span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">[</span><span class="str">"$and"</span><span class="op">]</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"statisticsInfo.killChains.%s"</span><span class="strut">&nbsp;</span></p>
<p id="t437" class="pln">                                                  <span class="op">%</span> <span class="nam">step</span><span class="op">:</span> <span class="op">{</span><span class="str">'$gt'</span><span class="op">:</span> <span class="num">0</span><span class="op">}</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t438" class="stm run hide_run">        <span class="key">return</span> <span class="nam">condition</span><span class="strut">&nbsp;</span></p>
<p id="t439" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t440" class="pln">    <span class="com"># get all task</span><span class="strut">&nbsp;</span></p>
<p id="t441" class="stm run hide_run">    <span class="op">@</span><span class="nam">check_flask_args</span><span class="op">(</span><span class="nam">Validator</span><span class="op">(</span><span class="str">"explore_get_all_schema"</span><span class="op">)</span><span class="op">,</span> <span class="nam">request</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t442" class="pln">    <span class="key">def</span> <span class="nam">get</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t443" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t444" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t445" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t446" class="stm run hide_run">        <span class="key">try</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t447" class="stm run hide_run">            <span class="nam">create_start_time</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"createStartTime"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t448" class="stm run hide_run">            <span class="nam">create_end_time</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"createEndTime"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t449" class="stm run hide_run">            <span class="nam">task_name</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"taskName"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t450" class="stm run hide_run">            <span class="nam">kill_chains</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"killChains"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t451" class="stm run hide_run">            <span class="nam">vul_min_count</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"vulMinCount"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t452" class="stm run hide_run">            <span class="nam">vul_max_count</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"vulMaxCount"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t453" class="stm run hide_run">            <span class="nam">threat_min_score</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"threatMinScore"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t454" class="stm run hide_run">            <span class="nam">threat_max_score</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"threatMaxScore"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t455" class="stm run hide_run">            <span class="nam">sort</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"sort"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t456" class="stm run hide_run">            <span class="nam">reverse</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"reverse"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t457" class="stm run hide_run">            <span class="nam">page</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"page"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t458" class="stm run hide_run">            <span class="nam">page_size</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"pageSize"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t459" class="stm run hide_run">            <span class="key">if</span> <span class="nam">sort</span> <span class="op">==</span> <span class="str">'explore'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t460" class="stm run hide_run">                <span class="nam">sort</span> <span class="op">=</span> <span class="str">'exploreStartDate'</span><span class="strut">&nbsp;</span></p>
<p id="t461" class="pln">            <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t462" class="stm run hide_run">                <span class="nam">sort</span> <span class="op">=</span> <span class="str">'createTime'</span><span class="strut">&nbsp;</span></p>
<p id="t463" class="stm run hide_run">            <span class="key">if</span> <span class="nam">reverse</span> <span class="op">==</span> <span class="str">'false'</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t464" class="stm run hide_run">                <span class="nam">reverse</span> <span class="op">=</span> <span class="num">1</span><span class="strut">&nbsp;</span></p>
<p id="t465" class="pln">            <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t466" class="stm run hide_run">                <span class="nam">reverse</span> <span class="op">=</span> <span class="op">-</span><span class="num">1</span><span class="strut">&nbsp;</span></p>
<p id="t467" class="stm mis">        <span class="key">except</span> <span class="nam">TypeError</span> <span class="key">as</span> <span class="nam">err</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t468" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">'Parameter error.Reason: %s'</span> <span class="op">%</span> <span class="nam">err</span><span class="strut">&nbsp;</span></p>
<p id="t469" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t470" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t471" class="stm run hide_run">        <span class="nam">print</span><span class="op">(</span><span class="nam">create_start_time</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t472" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t473" class="stm run hide_run">        <span class="nam">condition</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">query_condition</span><span class="op">(</span><span class="nam">create_start_time</span><span class="op">,</span> <span class="nam">create_end_time</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t474" class="pln">                                         <span class="nam">task_name</span><span class="op">,</span> <span class="nam">kill_chains</span><span class="op">,</span> <span class="nam">vul_min_count</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t475" class="pln">                                         <span class="nam">vul_max_count</span><span class="op">,</span> <span class="nam">threat_min_score</span><span class="op">,</span> <span class="nam">threat_max_score</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t476" class="stm run hide_run">        <span class="nam">print</span><span class="op">(</span><span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t477" class="stm run hide_run">        <span class="key">if</span> <span class="key">not</span> <span class="nam">condition</span><span class="op">[</span><span class="str">'flag'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t478" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">condition</span><span class="op">[</span><span class="str">'message'</span><span class="op">]</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t479" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t480" class="stm run hide_run">        <span class="nam">t_count</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">)</span><span class="op">.</span><span class="nam">count</span><span class="op">(</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t481" class="stm run hide_run">        <span class="nam">data</span> <span class="op">=</span> <span class="op">{</span><span class="str">"count"</span><span class="op">:</span> <span class="nam">t_count</span><span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t482" class="stm run hide_run">        <span class="nam">rst</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find</span><span class="op">(</span><span class="str">'back_explore'</span><span class="op">,</span> <span class="nam">condition</span><span class="op">[</span><span class="str">'data'</span><span class="op">]</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t483" class="pln">                                <span class="op">{</span><span class="str">"_id"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t484" class="pln">                                 <span class="str">"taskName"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t485" class="pln">                                 <span class="str">"taskId"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t486" class="pln">                                 <span class="str">"curFeatureGrpList"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t487" class="pln">                                 <span class="str">"createTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t488" class="pln">                                 <span class="str">"exploreStartDate"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t489" class="pln">                                 <span class="str">"exploreEndDate"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t490" class="pln">                                 <span class="str">"sidList"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t491" class="pln">                                 <span class="str">"startTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t492" class="pln">                                 <span class="str">"endTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t493" class="pln">                                 <span class="str">"status"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t494" class="pln">                                 <span class="str">"process"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t495" class="pln">                                 <span class="str">"remainingTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t496" class="pln">                                 <span class="str">"celeryId"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t497" class="pln">                                 <span class="str">"statisticsInfo"</span><span class="op">:</span> <span class="num">1</span><span class="strut">&nbsp;</span></p>
<p id="t498" class="pln">                                 <span class="op">}</span><span class="op">)</span><span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t499" class="pln">            <span class="op">.</span><span class="nam">sort</span><span class="op">(</span><span class="op">[</span><span class="op">(</span><span class="nam">sort</span><span class="op">,</span> <span class="nam">reverse</span><span class="op">)</span><span class="op">]</span><span class="op">)</span><span class="op">.</span><span class="nam">limit</span><span class="op">(</span><span class="nam">page_size</span><span class="op">)</span><span class="op">.</span><span class="nam">skip</span><span class="op">(</span><span class="op">(</span><span class="nam">page</span> <span class="op">-</span> <span class="num">1</span><span class="op">)</span> <span class="op">*</span> <span class="nam">page_size</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t500" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t501" class="stm run hide_run">        <span class="key">if</span> <span class="nam">rst</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t502" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">'Not found'</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t503" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t504" class="stm run hide_run">        <span class="nam">cols</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t505" class="stm run hide_run">        <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="nam">rst</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t506" class="pln">            <span class="com"># &#32479;&#35745;&#20449;&#24687;&#25968;&#25454;&#22788;&#29702;</span><span class="strut">&nbsp;</span></p>
<p id="t507" class="stm run hide_run">            <span class="key">del</span> <span class="nam">col</span><span class="op">[</span><span class="str">"statisticsInfo"</span><span class="op">]</span><span class="op">[</span><span class="str">"hitFeatureCount"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t508" class="stm run hide_run">            <span class="key">del</span> <span class="nam">col</span><span class="op">[</span><span class="str">"statisticsInfo"</span><span class="op">]</span><span class="op">[</span><span class="str">"threatLevel"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t509" class="stm run hide_run">            <span class="nam">col</span> <span class="op">=</span> <span class="nam">JSONEncoder</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">encode</span><span class="op">(</span><span class="nam">col</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t510" class="stm run hide_run">            <span class="nam">cols</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">json</span><span class="op">.</span><span class="nam">loads</span><span class="op">(</span><span class="nam">col</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t511" class="stm run hide_run">        <span class="nam">data</span><span class="op">[</span><span class="str">"detail"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">cols</span><span class="strut">&nbsp;</span></p>
<p id="t512" class="stm run hide_run">        <span class="nam">data</span><span class="op">[</span><span class="str">"page"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">page</span><span class="strut">&nbsp;</span></p>
<p id="t513" class="stm run hide_run">        <span class="nam">data</span><span class="op">[</span><span class="str">"pageSize"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">page_size</span><span class="strut">&nbsp;</span></p>
<p id="t514" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="nam">data</span><span class="op">)</span><span class="strut">&nbsp;</span></p>

            </td>
        </tr>
    </table>
</div>

<div id="footer">
    <div class="content">
        <p>
            <a class="nav" href="index.html">&#xab; index</a> &nbsp; &nbsp; <a class="nav" href="https://coverage.readthedocs.io">coverage.py v4.5.4</a>,
            created at 2019-09-10 14:11
        </p>
    </div>
</div>

</body>
</html>
