{
    "type":"object",
    "required": ["serverId"],
    "properties":{
        "serverType": {
            "type": "string",
            "default": "",
            "pattern": r"^(ftp|nas)$"
        },
        "host": {
            "type": "string",
            "default": ""
        },
        "port": {
            "type": "number",
            "default": 21
        },
        "filename": {
            "type": "string",
            "default": ""
        },
        "username": {
            "type": "string",
            "default": ""
        },
        "password": {
            "type": "string",
            "default": ""
        },
        "enable": {
            "type": "boolean",
            "default": False,
            "enum": [True, False]
        },
        "aliasName": {
            "type": "string",
            "default": ""
        },
        "serverId": {
            "type": "string",
            "default": ""
        }
    }
}