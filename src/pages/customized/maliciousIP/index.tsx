import React, { useState } from 'react';
import { Tabs } from 'antd';
import Task from './task';
import Datasource from './datasource';
import Kafaka from './kafaka';
const Index = () => {
  const [activeKey, setactiveKey] = useState('1');
  const changeTab = (value: any) => {
    console.log(value);
    setactiveKey(value)
  };
  return (
    <div>
      <Tabs onChange={changeTab} activeKey={activeKey}>
        <Tabs.TabPane tab="分析任务" key="1">
          {activeKey === '1' && <Task />}
        </Tabs.TabPane>
        <Tabs.TabPane tab="数据源(Hive)" key="2">
          {activeKey === '2' && <Datasource />}
        </Tabs.TabPane>
        <Tabs.TabPane tab="数据源(Kafaka)" key="3">
          {activeKey === '3' && <Kafaka />}
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};
export default Index;
