#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import json
from flask import request
from flask_restful import Resource
from utils.param_check import Validator
from utils.utils import flask_response
from utils.es_template.get_es_template import ES_Template
from utils.es_function import *
from utils import param_check
from utils.database import get_es_client
from config.config import RULE_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class StatisticsLevel(Resource):
    '''威胁等级统计汇总'''

    field_template = {
        "observed": "observedTime",
        "occurred": "occurredTime",
    }

    def __init__(self):
        self.es_template = ES_Template().read_template("report_event_level")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["args"]["startTime"])
        # 结束时间往后顺延一天取整，比如选的是23号，那么结束时间是24号的0点
        stop_time = int(kwargs["args"]["stopTime"]) + 24 * 60 * 60 * 1000
        if start_time >= stop_time:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})
        time_mode = self.field_template[kwargs["args"]["timeMode"]]
        self.es_template["query"]["bool"]["must"].append(get_range(time_mode, start_time, stop_time))
        # 连接ES查询
        es_client = get_es_client()
        try:
            res = es_client.search(index=RULE_INDEX, body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, res):
        total = res["hits"]["total"]['value']
        buckets = res["aggregations"]["threatLevel"]["buckets"]
        data_return = {
            "totalCount": total,
            "threatLevels": {
                "High": 0,
                "Medium": 0,
                "Low": 0,
            }
        }
        for bucket in buckets:
            # 获取危险等级
            threat_level = bucket["key"]
            threat_count = bucket["doc_count"]
            data_return["threatLevels"][threat_level] = threat_count
        return data_return
