#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
""" 获取 IOC 告警列表 """
from flask import request
from api_1_0.utils import flask_log
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.es_data_format import es_data_format2, es_data_format
from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils.utils import flask_response
from utils.es_function import *
from utils import param_check
from config.config import NdrLog, FILE_INDEX
from flask_restful import Resource
from utils.logger import get_ndr_logger
from utils.database import MongoDB

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)

field_temp = {
    "killchain": "killchain.keyword",
    "celeryId": "celeryId.keyword",
    "threatLevel": "threatLevel.keyword",
    "confirm": "confirm"
}


def format_es_temp(option_args, es_template):
    """
    :param option_args:
    :return:
    """
    es_must_list = []
    for key, value in option_args.items():
        if value in ["", None]:
            continue
        if key == "mbinfo":
            query = get_mbinfo(option_args["mbinfo"])
            es_template["query"]["bool"]["must"].append(query)
        elif key == "threatScore":
            threat_score_start, threat_score_stop = value.split('-')
            es_template["query"]["bool"]["must"].append(
                get_range("threatScore", int(threat_score_start), int(threat_score_stop)))
        elif key == "threatType":
            query = get_contain(key, value)
            es_template["query"]["bool"]["must"].append(query)
        else:
            search_args = {field_temp[key]: value}
            args_format = {"term": search_args}
            es_must_list.append(args_format)
    es_template["query"]["bool"]["must"].extend(es_must_list)
    return es_template


class FileListResource(NdrResource):

    def __init__(self):
        super(FileListResource, self).__init__(es_template="file_list")
        self.mongodb = MongoDB("ndr")

    @param_check.check_flask_args(Validator('file_list'), request)
    def post(self, **kwargs):
        index = FILE_INDEX
        if kwargs["groupKey"] == "uniqueId":
            index += "-agg"
            kwargs["groupKey"] = ""

        es_index = get_es_index(index, int(kwargs["startTime"]), int(kwargs["stopTime"]))
        es_index_list = []
        es_index_not = []
        for i in es_index:
            # 判断是否存在索引
            if self.es_client.indices.exists(i):
                es_index_list.append(i)
            else:
                es_index_not.append(i)
        if not es_index_list:
            return flask_response('index[%s] not exist' % str(es_index_not), True, {})
        option_args = {
            "celeryId": kwargs["celeryId"],
            "threatLevel": kwargs["threatLevel"],
            "threatScore": kwargs["threatScore"],
            "killchain": kwargs["killchain"],
            "mbinfo": kwargs["mbinfo"],
            "threatType": kwargs["threatType"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        try:
            if not self.es_client.ping():
                flask_log.ndr_log_to_box(NdrLog.Type.RUN, event='Elasticsearch服务连接失败！')
                return flask_response('Elasticsearch服务连接失败！', False, {})
            res = self.es_client.search(index=es_index_list, body=es_template, request_timeout=60)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件告警获取失败")
            return flask_response(message, False, {})
        try:
            data = self.data_format(kwargs["groupKey"], res)
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件告警获取成功")
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件告警获取失败")
            return flask_response(err_info, False, {})

    def data_format(self, group_key, es_data):
        if not group_key:
            total = es_data["hits"]["total"]["value"]
            file_data_list = es_data["hits"]["hits"]
        else:
            total = es_data["aggregations"]["_count"]["value"]
            file_data_list = es_data["aggregations"]["group_id"]["buckets"]
        count = total
        if total > 10000:
            total = 10000
            file_data_list = file_data_list[:total]
        data_return = {
            "total": total,
            "real_count": count,
            "alerts": []
        }
        if not group_key or group_key == "uniqueId":
            for file_info_data in file_data_list:
                each_file_dict = es_data_format2(group_key, file_info_data, self.mongodb)
                data_return["alerts"].append(each_file_dict)
        else:
            for file_info_data in file_data_list:
                each_file_dict = es_data_format(group_key, "file", file_info_data)
                data_return["alerts"].append(each_file_dict)
        return data_return


class FileThreatType(Resource):
    threat_type = ["恶意文件", "恶意软件"]

    @staticmethod
    def get():
        return_data = {"threat_type": []}
        for i in FileThreatType.threat_type:
            return_data["threat_type"].append(i)
        return flask_response("", True, return_data)
