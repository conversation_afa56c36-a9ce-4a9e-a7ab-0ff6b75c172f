{
    "type": "object",
    "required": ["startTime", "stopTime", "data", "format", "exportTime"],
    "properties": {
        "startTime": {
            "type": "integer",
            "default": "",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "integer",
            "default": "",
            "pattern": r"^\d{12,13}$"
        },
        "exportTime": {
            "type": "integer",
            "default": "",
            "pattern": r"^\d{12,13}$"
        },
        "data": {
            "type": "object"
        },
        "format": {
            "type": "string",
            "default": "",
            "pattern": r"^(word|excel|pdf|html)$"
        }
    }
}