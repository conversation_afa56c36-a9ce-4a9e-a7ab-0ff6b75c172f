import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Modal,
  Descriptions,
  Typography,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Progress,
  message,
} from 'antd';
import {
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  SearchOutlined,
  Bar<PERSON>hartOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { getAnomalyResults, updateAnomalyResult, AnomalyResult } from '@/services/anomaly';
import styles from '../style.less';

const { Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const ResultAnalysis: React.FC = () => {
  const [results, setResults] = useState<AnomalyResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    model_type: '',
    severity: '',
    status: '',
    target_value: '',
    start_time: '',
    end_time: '',
  });
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedResult, setSelectedResult] = useState<AnomalyResult | null>(null);

  // 获取检测结果列表
  const fetchResults = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = {
        page,
        page_size: pageSize,
        ...filters,
      };
      const response = await getAnomalyResults(params);
      if (response.flag) {
        setResults(response.data.results);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      message.error('获取检测结果失败');
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   fetchResults();
  // }, [filters]);

  // 查看详情
  const handleViewDetail = (result: AnomalyResult) => {
    setSelectedResult(result);
    setDetailModalVisible(true);
  };

  // 确认异常
  const handleConfirm = async (resultId: string, confirmed: boolean) => {
    try {
      const response = await updateAnomalyResult(resultId, {
        confirmed,
        status: confirmed ? 'confirmed' : 'dismissed',
      });
      if (response.flag) {
        message.success(confirmed ? '已确认异常' : '已忽略异常');
        fetchResults();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    const colors = {
      critical: 'red',
      high: 'orange',
      medium: 'gold',
      low: 'green',
    };
    return colors[severity] || 'default';
  };

  // 获取严重程度文本
  const getSeverityText = (severity: string) => {
    const texts = {
      critical: '严重',
      high: '高危',
      medium: '中危',
      low: '低危',
    };
    return texts[severity] || severity;
  };

  // 获取模型类型名称
  const getModelTypeName = (modelType: string) => {
    const names = {
      time_series: '时间序列趋势',
      baseline: '基线偏移检测',
      repeat_conn: '重复连接检测',
      long_conn: '长连接分析',
      geo_anomaly: '地理/归属异常',
    };
    return names[modelType] || modelType;
  };

  const columns = [
    {
      title: '检测时间',
      dataIndex: 'detection_time',
      key: 'detection_time',
      render: (time: number) => moment(time).format('YYYY-MM-DD HH:mm:ss'),
      sorter: true,
    },
    {
      title: '检测模型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (modelType: string) => (
        <Tag color="blue">{getModelTypeName(modelType)}</Tag>
      ),
    },
    {
      title: '检测目标',
      key: 'target',
      render: (record: AnomalyResult) => (
        <div>
          <Tag color="cyan">{record.target_type.toUpperCase()}</Tag>
          <br />
          <Text code style={{ fontSize: 12 }}>
            {record.target_value}
          </Text>
        </div>
      ),
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)}>
          {getSeverityText(severity)}
        </Tag>
      ),
    },
    {
      title: '异常分数',
      dataIndex: 'anomaly_score',
      key: 'anomaly_score',
      render: (score: number) => (
        <Progress
          percent={Math.round(score * 100)}
          size="small"
          strokeColor={score > 0.7 ? '#ff4d4f' : score > 0.5 ? '#faad14' : '#52c41a'}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: AnomalyResult) => {
        if (record.confirmed === true) {
          return <Tag color="success">已确认</Tag>;
        } else if (record.confirmed === false) {
          return <Tag color="default">已忽略</Tag>;
        } else {
          return <Tag color="processing">待处理</Tag>;
        }
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 300,
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: AnomalyResult) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.confirmed === null && (
            <>
              <Button
                type="text"
                icon={<CheckOutlined />}
                onClick={() => handleConfirm(record.result_id, true)}
                style={{ color: '#52c41a' }}
              >
                确认
              </Button>
              <Button
                type="text"
                icon={<CloseOutlined />}
                onClick={() => handleConfirm(record.result_id, false)}
                style={{ color: '#ff4d4f' }}
              >
                忽略
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 过滤器 */}
      <Card className={styles.filterBar}>
        <Row gutter={16}>
          <Col span={6}>
            <Input
              placeholder="搜索目标值"
              prefix={<SearchOutlined />}
              value={filters.target_value}
              onChange={(e) => setFilters({ ...filters, target_value: e.target.value })}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="检测模型"
              value={filters.model_type}
              onChange={(value) => setFilters({ ...filters, model_type: value })}
              allowClear
            >
              <Option value="time_series">时间序列趋势</Option>
              <Option value="baseline">基线偏移检测</Option>
              <Option value="repeat_conn">重复连接检测</Option>
              <Option value="long_conn">长连接分析</Option>
              <Option value="geo_anomaly">地理/归属异常</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="严重程度"
              value={filters.severity}
              onChange={(value) => setFilters({ ...filters, severity: value })}
              allowClear
            >
              <Option value="critical">严重</Option>
              <Option value="high">高危</Option>
              <Option value="medium">中危</Option>
              <Option value="low">低危</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="处理状态"
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              allowClear
            >
              <Option value="confirmed">已确认</Option>
              <Option value="dismissed">已忽略</Option>
              <Option value="pending">待处理</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              showTime
              onChange={(dates) => {
                if (dates) {
                  setFilters({
                    ...filters,
                    start_time: dates[0]?.valueOf().toString() || '',
                    end_time: dates[1]?.valueOf().toString() || '',
                  });
                } else {
                  setFilters({ ...filters, start_time: '', end_time: '' });
                }
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* 结果列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={results}
          rowKey="result_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: fetchResults,
          }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="异常检测结果详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedResult && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="检测时间" span={2}>
              {moment(selectedResult.detection_time).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="检测模型">
              <Tag color="blue">{getModelTypeName(selectedResult.model_type)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="严重程度">
              <Tag color={getSeverityColor(selectedResult.severity)}>
                {getSeverityText(selectedResult.severity)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="异常分数">
              <Progress
                percent={Math.round(selectedResult.anomaly_score * 100)}
                size="small"
                strokeColor={
                  selectedResult.anomaly_score > 0.7
                    ? '#ff4d4f'
                    : selectedResult.anomaly_score > 0.5
                    ? '#faad14'
                    : '#52c41a'
                }
              />
            </Descriptions.Item>
            <Descriptions.Item label="检测目标">
              <Tag color="cyan">{selectedResult.target_type.toUpperCase()}</Tag>
              <Text code>{selectedResult.target_value}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="描述" span={2}>
              {selectedResult.description}
            </Descriptions.Item>
            <Descriptions.Item label="详细信息" span={2}>
              <pre style={{ fontSize: 12, maxHeight: 200, overflow: 'auto' }}>
                {JSON.stringify(selectedResult.details, null, 2)}
              </pre>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ResultAnalysis;
