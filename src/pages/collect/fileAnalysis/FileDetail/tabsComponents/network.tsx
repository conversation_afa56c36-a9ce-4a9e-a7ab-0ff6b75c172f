import { Collapse, Table, Button } from 'antd';
import React, { useEffect, useState } from 'react';
import cn from 'classnames';
import { toLower } from 'lodash';
import s from './style.less';
import { merge, isEmpty } from 'lodash';
import { TwoKeyMap } from 'unocss';
const { Panel } = Collapse;
interface PartProps {
  info: any;
}
const columnsD = [
  {
    title: '域名',
    dataIndex: 'domain',
  },
  {
    title: 'DGA检测',
    dataIndex: 'dga',
  },
  {
    title: '情报判定',
    dataIndex: 'description',
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
  },
];
const columnsH = [
  {
    title: 'ip',
    dataIndex: 'ip',
  },
];
const columnsDNS = [
  {
    title: '请求',
    dataIndex: 'request',
  },
  {
    title: '应答',
    dataIndex: 'description',
  },
];
const columnsHttp = [
  {
    title: '请求',
    dataIndex: 'name',
  },
  {
    title: '应答',
    dataIndex: 'description',
  },
];
const columnsUDP = [
  {
    title: '源IP',
    dataIndex: 'dst',
  },
  {
    title: '源端口',
    dataIndex: 'dport',
  },
  {
    title: '目的IP',
    dataIndex: 'src',
  },
  {
    title: '目的端口',
    dataIndex: 'description',
  },
];
const columnsTCP = [
  {
    title: '源IP',
    dataIndex: 'name',
  },
  {
    title: '源端口',
    dataIndex: 'description',
  },
  {
    title: '目的IP',
    dataIndex: 'description',
  },
  {
    title: '目的端口',
    dataIndex: 'description',
  },
];
const columnsIC = [
  {
    title: '源IP',
    dataIndex: 'name',
  },
  {
    title: '源端口',
    dataIndex: 'description',
  },
  {
    title: '目的IP',
    dataIndex: 'description',
  },
  {
    title: '目的端口',
    dataIndex: 'description',
  },
];
const Signature = ({ info = {} }: PartProps) => {
  const [btnVal, setDtnVal] = useState('domains');
  const [data, setData] = useState([]);
  const [col, setCol] = useState(columnsD);

  const Obj = {
    domains: { col: columnsD, name: 'Domains' },
    hosts: { col: columnsH, name: 'Hosts' },
    dns_servers: { col: columnsDNS, name: 'DNS' },
    http: { col: columnsHttp, name: 'HTTP' },
    udp: { col: columnsUDP, name: 'UDP' },
    tcp: { col: columnsTCP, name: 'TCP' },
    icmp: { col: columnsIC, name: 'ICMP' },
  };
  useEffect(() => {
    const info_ = info;
    if ('hosts,dns_servers'.includes(btnVal)) {
      info_[btnVal] = info[btnVal].map(e => {
        return {
          ip: e,
        };
      });
    }
    info[btnVal] && setData(info_[btnVal]);
    setCol(Obj[btnVal].col);
  }, [btnVal]);
  return (
    <div className={cn(s.signature, 'pl pr')}>
      <div className="mb-4">
        {Object.entries(Obj).map(([key, val]) => {
          return (
            <Button
              className="mr-4"
              disabled={isEmpty(info[key])}
              onClick={() => {
                setDtnVal(key);
              }}
            >
              {val.name}({info[key]?.length||0})
            </Button>
          );
        })}
      </div>
      <Table className={s.table} columns={col} dataSource={data} pagination={false} />
    </div>
  );
};

export default Signature;
