/*
 * @Author: tianh
 * @Date: 2022-01-17 16:55:17
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-14 15:38:43
 * @Descripttion:
 */

const defaultState = {
  columns: [],
  hidden: [],
  datas: [],
  links: [],
};
export default (state = defaultState, action: { type: string; value: any }) => {
  //就是一个方法函数
  let data = { ...action };
  if (data.type === 'changeColumns') {
    let newState = {
      columns: [],
    };
    newState.columns = data.value.map((item: any) => {
      item = { ...item };
      return item;
    });
    let arr_temp = [];
    arr_temp = data.value.map((item: any) => {
      item = { ...item };
      return item;
    });
    const temp = JSON.stringify(arr_temp);
    sessionStorage.setItem('columns', temp);
    return newState;
  }

  if (action.type === 'changeDatas') {
    let newState = JSON.parse(JSON.stringify(state));
    newState.datas = action.value;
    var obj = {};
    newState.datas = newState.datas.reduce(function(item: any, next: any) {
      obj[next.name] ? '' : (obj[next.name] = true && item.push(next));
      return item;
    }, []);
    return newState;
  }
  if (action.type === 'changeLinks') {
    let newState = JSON.parse(JSON.stringify(state));
    newState.links = action.value;
    return newState;
  }
  return state;
};
