#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_eventType.py
# @time: 2019/09/04
# @software: PyCharm

from utils.base_knowledge import BaseKnowledge


class GetEventType(BaseKnowledge):
    """获取威胁等级字段信息"""

    def __init__(self):
        """初始化参数"""
        super(GetEventType, self).__init__(col="knowledge_event_type", filter_name="eventType")

    @staticmethod
    def get_filed():
        """获取threat_level字段"""
        return {
            "name": "eventType",
            "list": []
        }
