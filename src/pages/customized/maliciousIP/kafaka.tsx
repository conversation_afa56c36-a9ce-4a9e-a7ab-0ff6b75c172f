import React, { useEffect, useState } from 'react';
import {
  Button,
  Upload,
  Collapse,
  Select,
  Form,
  Input,
  Spin,
  Table,
  Modal,
  Row,
  Col,
  message,
  Popconfirm,
  Tooltip,
} from 'antd';
const { Panel } = Collapse;
const { Option } = Select;
import { SearchOutlined, UploadOutlined } from '@ant-design/icons';
import { dataSourceList, addDatasource, getAnalysis, delDatasource } from '@/services/collect';
const Kafaka = () => {
  const [form] = Form.useForm();
  const [taskForm] = Form.useForm();
  const [total, settotal] = useState(0);
  const [loading, setloading] = useState(false);
  const [taskDetail, settaskDetail] = useState<any>({});
  const [params, setparams] = useState<any>({});
  const [tableData, setTableData] = useState<any>([]);
  const [isAdd, setisAdd] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [ruleDetail, setruleDetail] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [action, setaction] = useState('');
  const auditTypeList = [
    {
      label: '普通审批',
      value: 0,
    },
    {
      label: '特殊审批',
      value: 1,
    },
  ];
  const ruleTypeList = [
    {
      label: 'IP规则',
      value: 0,
    },
    {
      label: '简单规则',
      value: 1,
    },
  ];
  const columns = [
    {
      title: '数据源名称',
      dataIndex: 'name',
    },

    {
      title: '规则类型',
      dataIndex: 'rule_type',
      render: (t: any) => {
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">{t === 0 ? 'IP规则' : ''}</div>
            <div className="text-left ws-nowrap">{t === 1 ? '简单规则' : ''}</div>
          </div>
        );
      },
    },
    {
      title: '审批类型',
      dataIndex: 'audit_type',
      render: (t: any) => {
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">{t === 0 ? '普通审批' : ''}</div>
            <div className="text-left ws-nowrap">{t === 1 ? '特殊审批' : ''}</div>
          </div>
        );
      },
    },
    {
      title: '规则概要信息',
      dataIndex: 'rule_list',
    },
    {
      title: '服务器IP地址',
      dataIndex: 'ip_addr',
    },
    {
      title: '账号',
      dataIndex: 'username',
    },
    {
      title: '状态',
      dataIndex: 'task_status',
      render: (t: any) => {
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">{t === -1 ? '未连接' : ''}</div>
            <div className="text-left ws-nowrap">{t === 0 ? '待审核' : ''}</div>
            <div className="text-left ws-nowrap">{t === 1 ? '等待源审核' : ''}</div>
            <div className="text-left ws-nowrap">{t === 2 ? '审核通过' : ''}</div>
            <div className="text-left ws-nowrap">{t === 3 ? '审核失败' : ''}</div>
            <div className="text-left ws-nowrap">{t === 4 ? '等待' : ''}</div>
            <div className="text-left ws-nowrap">{t === 5 ? '运行' : ''}</div>
            <div className="text-left ws-nowrap">{t === 6 ? '完成' : ''}</div>
            <div className="text-left ws-nowrap">{t === 7 ? '失败' : ''}</div>
            <div className="text-left ws-nowrap">{t === 10 ? '不存在' : ''}</div>
          </div>
        );
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      width: 240,
      render: (t: any, record: any) => {
        return (
          <>
            <Button
              onClick={() => {
                handleEdit();
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              编辑
            </Button>
            <Popconfirm title="确定删除吗？" okText="确定" cancelText="取消" onConfirm={() => deleteTask(record.name)}>
              <Tooltip>
                <Button type="link" danger>
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>

            <Button
              onClick={() => {
                executeTask(record.name);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];
  useEffect(() => {
    searchList();
  }, []);

  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const handleEdit = () => {
    setisAdd(false);
    setIsModalOpen(true);
  };
  const handleOk = async () => {
    if (isAdd) {
      setaction('create');
    } else {
      setaction('edit');
    }
    submitAction();
  };
  const submitAction = () => {
    taskForm.validateFields().then((value: any) => {
      value.action = action;

      let formData = new FormData();
      fileList.forEach((file: any) => {
        console.log(file);
        formData.append('fileName', file.originFileObj);
      });

      for (const key in value) {
        if (key !== 'fileName') {
          formData.append(key, value[key]);
        }
      }
      addDatasource(formData).then((res) => {
        if (res.flag) {
          setIsModalOpen(false);
          message.success('操作成功');
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const testLink = () => {
    setaction('link');
    taskForm.validateFields().then((value: any) => {
      value.action = action;

      let formData = new FormData();
      fileList.forEach((file: any) => {
        console.log(file);
        formData.append('fileName', file.originFileObj);
      });

      for (const key in value) {
        if (key !== 'fileName') {
          formData.append(key, value[key]);
        }
      }
      addDatasource(formData).then((res) => {
        if (res.flag) {
          // setIsModalOpen(false);
          message.success('操作成功');
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const handleCancel = () => {
    taskForm.resetFields();
    setIsModalOpen(false);
  };

  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      searchList();
    });
  };
  const deleteTask = (name: any) => {
    delDatasource({ name: name }).then((res) => {
      if (res.flag) {
        onSubmit();
        message.success('操作成功');
      } else {
        message.success(res.message);
      }
    });
  };

  const executeTask = (id: any) => {
    setloading(true);
    getAnalysis(id).then((res) => {
      if (res.flag) {
        message.success('操作成功');
      } else {
        message.error(res.message);
      }
    });
    setloading(false);
  };
  const handleAdd = () => {
    setisAdd(true);
    setIsModalOpen(true);
  };
  const searchList = () => {
    let value = { ...params };
    dataSourceList(value).then((res) => {
      if (res.flag) {
        setloading(false);
        settotal(res.data.total);
        setTableData(res.data.detail);
      } else {
        message.error(res.message);
      }
    });
  };

  const formItemLayout = {
    labelCol: {
      span: 4, // * ≥576px
    },
    wrapperCol: {},
  };
  const uploadChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form {...formItemLayout} layout="inline" initialValues={params} form={form} className="!flex gap-3 !mt-5">
            <Row>
              <Col span={8}>
                <Form.Item label="数据源名称:" name="name">
                  <Input />
                </Form.Item>
              </Col>
              <Col offset={12} span={4}>
                <div style={{ textAlign: 'right', padding: 10 }}>
                  <Button
                    style={{ margin: '0 10px' }}
                    className="searchBtn"
                    type="primary"
                    onClick={onSubmit}
                    icon={<SearchOutlined />}
                  >
                    搜索
                  </Button>
                  <Button style={{ color: '#1890ff' }} type="dashed" onClick={handleAdd}>
                    新建
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <Spin spinning={loading}>
        <Table dataSource={tableData} columns={columns} pagination={pagination} />
      </Spin>
      <Modal
        width={800}
        title={isAdd ? '新建数据源' : '编辑数据源'}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form initialValues={taskDetail} form={taskForm}>
          <Form.Item name="name" label="数据源名称" rules={[{ required: true, message: '请填写!' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="ip_addr" label="数据平台IP地址" rules={[{ required: true, message: '请填写!' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="username" label="账号" rules={[{ required: true, message: '请填写!' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="password" label="密码" rules={[{ required: true, message: '请填写!' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="audit_type" label="审批类型" rules={[{ required: true, message: '请选择!' }]}>
            <Select>
              {auditTypeList.map((item) => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="rule_type" label="规则类型" rules={[{ required: true, message: '请选择!' }]}>
            <Select>
              {ruleTypeList.map((item) => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="fileName" label="筛选规则" rules={[{ required: true, message: '请选择!' }]}>
            <Upload maxCount={1} onChange={uploadChange} accept=".xls, .xlsx">
              <Button type="primary" ghost icon={<UploadOutlined />}>
                选择导入的规则文件
              </Button>
            </Upload>
          </Form.Item>
          <Form.Item>
            <Button onClick={testLink} type="link">
              测试连接
            </Button>
            <Button type="link">模板下载</Button>
          </Form.Item>
        </Form>
      </Modal>
      <Modal width={1200} title="规则详情" visible={ruleDetail} onOk={handleOk} onCancel={handleCancel}></Modal>
    </div>
  );
};
export default Kafaka;
