# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : manual_detection.py
# @Software: PyCharm

"""手动异常检测API"""

from datetime import datetime
from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from utils.param_check import Validator, check_flask_args
from api_1_0.utils.flask_log import ndr_log_to_box
from .detection_executor import DetectionExecutor
from .detection_models.detector_factory import DetectorFactory

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class ManualDetection(Resource):
    """手动异常检测"""
    
    def __init__(self):
        self.executor = DetectionExecutor()
    
    
    def post(self):
        """执行手动异常检测"""
        try:
            data = request.get_json()
            
            # 验证必需参数
            required_fields = ['model_type', 'target_type', 'target_value', 'start_time', 'end_time']
            for field in required_fields:
                if field not in data:
                    return flask_response(f'缺少必需参数: {field}', False, {})
            
            # 构建任务配置
            task_config = {
                'model_type': data['model_type'],
                'target_type': data['target_type'],
                'target_value': data['target_value'],
                'detection_config': data.get('detection_config', {}),
            }
            
            # 验证任务配置
            is_valid, error_msg = DetectorFactory.validate_task_config(task_config)
            if not is_valid:
                return flask_response(f'任务配置无效: {error_msg}', False, {})
            
            # 解析时间参数
            start_time = datetime.fromtimestamp(int(data['start_time']) / 1000)
            end_time = datetime.fromtimestamp(int(data['end_time']) / 1000)
            
            # 执行手动检测
            result = self.executor.execute_manual_detection(task_config, start_time, end_time)
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, 
                         f"手动异常检测: {data['model_type']} - {data['target_value']}")
            
            if result['success']:
                LOG.info(f"手动异常检测成功: 检测到{result['detection_count']}个异常")
                return flask_response('手动检测完成', True, result)
            else:
                LOG.error(f"手动异常检测失败: {result.get('error', '未知错误')}")
                return flask_response(f"手动检测失败: {result.get('error', '未知错误')}", False, {})
            
        except Exception as e:
            LOG.error(f"手动异常检测失败: {str(e)}")
            return flask_response(f'手动检测失败: {str(e)}', False, {})


class SupportedModels(Resource):
    """获取支持的检测模型"""
    
    
    def get(self):
        """获取支持的检测模型列表"""
        try:
            models = DetectorFactory.get_supported_models()
            return flask_response('获取支持的模型成功', True, {'models': models})
            
        except Exception as e:
            LOG.error(f"获取支持的模型失败: {str(e)}")
            return flask_response(f'获取支持的模型失败: {str(e)}', False, {})


class ModelDefaultConfig(Resource):
    """获取模型默认配置"""
    
    
    def get(self, model_type):
        """获取指定模型的默认配置"""
        try:
            config = DetectorFactory.get_default_config(model_type)
            if config:
                return flask_response('获取模型默认配置成功', True, {'config': config})
            else:
                return flask_response('不支持的模型类型', False, {})
            
        except Exception as e:
            LOG.error(f"获取模型默认配置失败: {str(e)}")
            return flask_response(f'获取模型默认配置失败: {str(e)}', False, {})


class DetectionStatistics(Resource):
    """检测统计信息"""
    
    def __init__(self):
        self.executor = DetectionExecutor()
    
    
    def get(self):
        """获取检测统计信息"""
        try:
            days = int(request.args.get('days', 7))
            stats = self.executor.get_detection_statistics(days)
            
            if 'error' in stats:
                return flask_response(f'获取统计信息失败: {stats["error"]}', False, {})
            else:
                return flask_response('获取统计信息成功', True, stats)
            
        except Exception as e:
            LOG.error(f"获取检测统计信息失败: {str(e)}")
            return flask_response(f'获取检测统计信息失败: {str(e)}', False, {})
