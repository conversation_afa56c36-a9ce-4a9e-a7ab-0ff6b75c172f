import React from 'react';
import { Modal } from 'antd';

import { TimeRange } from '../UnSettledThreat/HighRiskHostCard';
import ModalTabs from '@/pages/workbench/components/eventCom/modalTabs';

export type Props = {
  show: boolean;
  onClose: () => void;
  timeRange: TimeRange;
  type: string;
  currentThreat?: Record<string, string>
}

function ThreatDetailModal({ show, onClose, timeRange, currentThreat, type}: Props): JSX.Element {
  return (
    <>
      {
        show && currentThreat && (
          <Modal
            visible={show}
            width={1000}
            centered
            title="事件详情"
            footer={null}
            bodyStyle={{ maxHeight: 850, overflow: 'auto', padding: '0 24px 10px' }}
            onCancel={onClose}
          >
            <ModalTabs
              selRow={currentThreat}
              filter={{
                start_time: timeRange.start_time.toString(),
                end_time: timeRange.end_time.toString()
              }}
              type={type}
              defaultActiveKey="2"
            />
          </Modal>
        )
      }
    </>
  );
}

export default ThreatDetailModal;
