syntax = "proto3";

import "google/protobuf/empty.proto";

package forward;

// The forward service definition.
service Forward {
  rpc GetStats (google.protobuf.Empty) returns (StatsReply) {}
  rpc ClearStats (google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc GetPortStats (PortStatsRequest) returns (PortStatsReply) {}
  rpc ClearPortStats (ClearPortStatsRequest) returns (google.protobuf.Empty) {}
  rpc GetDebug (google.protobuf.Empty) returns (GetDebugReply) {}
  rpc SetDebug (SetDebugRequest) returns (google.protobuf.Empty) {}
  rpc Acl4 (stream Acl4Request) returns (Acl4Reply) {}
}

message StatsReply {
  uint64 ipv4_in_packets = 1;
  uint64 ipv4_out_packets = 2;
  uint64 ipv4_consume_packets = 3;
  uint64 ipv4_drop_packets = 4;
  uint64 ipv4_blackhole_packets = 5;
  uint64 ipv4_err_packets = 6;
  uint64 ipv6_in_packets = 7;
  uint64 ipv6_out_packets = 8;
  uint64 other_in_packets = 9;
  uint64 other_out_packets = 10;
  uint64 ipv4_deliver_packets = 11;
}

message PortStatsRequest {
  uint32 port = 1;
}

message PortStatsReply {
  uint64 in_packets = 1;
  uint64 in_bytes = 2;
  uint64 out_packets = 3;
  uint64 out_bytes = 4;
}

message ClearPortStatsRequest {
  uint32 port = 1;
}

message GetDebugReply {
  uint32 debug = 1;
}

message SetDebugRequest {
  bool enable = 1;
  uint32 type = 2;
}

enum ForwardMode {
  FORWARD_MODE_BLACK_HOLE = 0;
  FORWARD_MODE_INLINE = 1;
  FORWARD_MODE_ACL = 2;
}

message Acl4Request {
  uint32 port = 1;
  uint32 port_mask = 2;
  uint32 proto = 3;
  uint32 proto_mask = 4;
  uint32 ip_src = 5;
  uint32 ip_src_mask = 6;
  uint32 ip_dst = 7;
  uint32 ip_dst_mask = 8;
  uint32 port_src = 9;
  uint32 port_src_max = 10;
  uint32 port_dst = 11;
  uint32 port_dst_max = 12;
  uint32 priority = 13;
  uint32 action = 14;
}

message Acl4Reply {
  uint32 code = 1;
  string message = 2;
}