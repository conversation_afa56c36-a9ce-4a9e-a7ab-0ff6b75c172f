# -*- coding: utf-8 -*-
# @Time    : 2019-08-27 13:31
# <AUTHOR> Shark
# @File    : es_function.py
# @Software: PyCharm
import copy
import json
from concurrent.futures import ThreadPoolExecutor

import requests

from utils.cert_ip import search_unit
from utils.logger import get_ndr_logger
from utils.utils import get_country_image
from utils.database import MongoDB
import time

ES_LOG = get_ndr_logger('es_log', __file__)


def es_data_export(group_key, es_data, mongodb):
    if not group_key:
        alert_data_list = es_data["hits"]["hits"]
    else:
        alert_data_list = es_data["aggregations"]["group_id"]["buckets"]

    thread_num = len(alert_data_list) // 600 + 1

    task_thread = ThreadPoolExecutor(thread_num)

    total_alert = []
    for i in range(thread_num):
        total_alert.append(alert_data_list[i * 600:(i + 1) * 600])

    result_task = []
    for j in total_alert:
        result_task.append(task_thread.submit(export_format_data, group_key, j, mongodb))

    task_thread.shutdown(wait=True)

    data_return = []
    for x in result_task:
        data_return += x.result()
    return data_return

    # alert_data_list1 = alert_data_list[0:200]
    # alert_data_list2 = alert_data_list[200:400]
    # alert_data_list3 = alert_data_list[400:600]
    # alert_data_list4 = alert_data_list[600:800]
    # alert_data_list5 = alert_data_list[800:1000]
    # alert_data_list6 = alert_data_list[1000:1200]
    # alert_data_list7 = alert_data_list[1200:1400]
    # alert_data_list8 = alert_data_list[1400:1600]
    # alert_data_list9 = alert_data_list[1600:-1]
    #
    # fu1 = task_thread.submit(export_format_data, group_key, alert_data_list1, mongodb)
    # fu2 = task_thread.submit(export_format_data, group_key, alert_data_list2, mongodb)
    # fu3 = task_thread.submit(export_format_data, group_key, alert_data_list3, mongodb)
    # fu4 = task_thread.submit(export_format_data, group_key, alert_data_list4, mongodb)
    # fu5 = task_thread.submit(export_format_data, group_key, alert_data_list5, mongodb)
    # fu6 = task_thread.submit(export_format_data, group_key, alert_data_list6, mongodb)
    # fu7 = task_thread.submit(export_format_data, group_key, alert_data_list7, mongodb)
    # fu8 = task_thread.submit(export_format_data, group_key, alert_data_list8, mongodb)
    # fu9 = task_thread.submit(export_format_data, group_key, alert_data_list9, mongodb)
    #
    # task_thread.shutdown(wait=True)
    # res1 = fu1.result()
    # res2 = fu2.result()
    # res3 = fu3.result()
    # res4 = fu4.result()
    # res5 = fu5.result()
    # res6 = fu6.result()
    # res7 = fu7.result()
    # res8 = fu8.result()
    # res9 = fu9.result()
    # data_return = res1 + res2 + res3 + res4 + res5 + res6 + res7 + res8 + res9
    # return data_return


def export_format_data(group_key, alert_data_list, mongodb):
    data_return = []
    data_list = []
    for alert_info_data in alert_data_list:
        if not group_key:
            each_alert_dict = alert_info_data["_source"]
            # 调接口获取单位：
            src_city_name = cert_alert_get_ipc_info(each_alert_dict["flow"]["src_ip"])
            dst_city_name = cert_alert_get_ipc_info(each_alert_dict["flow"]["dst_ip"])
            if src_city_name:
                each_alert_dict['srcIpGeoInfo']['city_name'] += ' ' + src_city_name
            if dst_city_name:
                each_alert_dict['dstIpGeoInfo']['city_name'] += ' ' + dst_city_name
            data_list.append(each_alert_dict)
        elif group_key == "uniqueId":
            alert_info = alert_info_data["doc_ids"]["hits"]["hits"][0]
            each_alert_dict = alert_info["_source"]
            each_alert_dict["doc_count"] = alert_info_data["doc_count"]
            # 调接口获取单位：
            src_city_name = cert_alert_get_ipc_info(each_alert_dict["flow"]["src_ip"])
            dst_city_name = cert_alert_get_ipc_info(each_alert_dict["flow"]["dst_ip"])
            if src_city_name:
                each_alert_dict['srcIpGeoInfo']['city_name'] += ' ' + src_city_name
            if dst_city_name:
                each_alert_dict['dstIpGeoInfo']['city_name'] += ' ' + dst_city_name
            each_alert_dict['start_time'] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(int(alert_info_data["start_time"]['value']) / 1000))
            each_alert_dict['end_time'] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(int(alert_info_data["sort_id"]['value']) / 1000))

            data_list.append(each_alert_dict)
        else:
            alert_info_list = alert_info_data["doc_ids"]["hits"]["hits"]
            for alert_info in alert_info_list:
                each_alert_dict = alert_info["_source"]
                data_list.append(each_alert_dict)
    for data in data_list:
        if group_key == "uniqueId":  # ▒~[▒▒~M▒为start_time▒~R~Lend_time
            data.pop('occurredTime')
            data.pop("observedTime")
        else:
            data["occurredTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(data["occurredTime"] / 1000))
            data["observedTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(data["observedTime"] / 1000))

        data["taskName"] = ""
        celery_id = data["celeryId"]
        rst = mongodb.find_one('back_explore', {"celeryId": celery_id})
        if rst:
            data["taskName"] = rst['taskName']
        data["src_ip"] = data["flow"]["src_ip"]
        data["src_port"] = data["flow"]["src_port"]
        data["dst_ip"] = data["flow"]["dst_ip"]
        data["dst_port"] = data["flow"]["dst_port"]
        data.pop("flow")
        data_return.append(data)
    return data_return


def es_data_format2(group_key, data_info, mongodb):
    if group_key:
        ioc_info = data_info["doc_ids"]["hits"]["hits"][0]
        data_dict = ioc_info["_source"]
        data_dict["detailTotal"] = data_info["doc_ids"]["hits"]["total"]["value"]
    else:
        data_dict = data_info["_source"]
    data_dict["describe"] = ""
    data_dict["advice"] = ""
    data_dict["taskName"] = ""
    celery_id = data_dict["celeryId"]
    rst = mongodb.find_one('back_explore', {"celeryId": celery_id})
    if rst:
        data_dict["taskName"] = rst['taskName']

    # 调接口获取单位：
    src_city_name = cert_alert_get_ipc_info(data_dict["flow"]["src_ip"])
    dst_city_name = cert_alert_get_ipc_info(data_dict["flow"]["dst_ip"])
    if src_city_name:
        data_dict['srcIpGeoInfo']['city_name'] += ' ' + src_city_name
    if dst_city_name:
        data_dict['dstIpGeoInfo']['city_name'] += ' ' + dst_city_name

    # 获取国家图片地址
    country_src = data_dict["srcIpGeoInfo"]["country_name"]
    country_dst = data_dict["dstIpGeoInfo"]["country_name"]
    if country_src:
        data_dict["srcIpGeoInfo"]["image"] = get_country_image(
            data_dict["srcIpGeoInfo"]["areacode"])
    if country_dst:
        data_dict["dstIpGeoInfo"]["image"] = get_country_image(
            data_dict["dstIpGeoInfo"]["areacode"])
    return data_dict


def es_data_format(group_key, es_type, data_info):
    data_dict = dict()
    data_dict[group_key] = data_info["key"]
    data_dict["end_time"] = data_info["doc_ids"]["hits"]["hits"][0]["_source"]["observedTime"]
    data_dict["first_time"] = data_info["doc_ids"]["hits"]["hits"][-1]["_source"]["observedTime"]
    data_dict["alarm_count"] = data_info["doc_count"]
    alarm_name_dict = dict()
    ip_list = set()
    unique_id_set = set()
    if group_key == "src_ip":
        data_dict["city_name"] = \
            data_info["doc_ids"]["hits"]["hits"][0]["_source"]["srcIpGeoInfo"]["city_name"]
        data_dict["country_name"] = \
            data_info["doc_ids"]["hits"]["hits"][0]["_source"]["srcIpGeoInfo"]["country_name"]
        data_dict["organisation"] = "重点资产" if \
            data_info["doc_ids"]["hits"]["hits"][0]["_source"]["srcIpMbInfo"]["assets"] else ""
        data_dict["uniqueId"] = data_info["doc_ids"]["hits"]["hits"][0]["_source"]['uniqueId']
        for alert_info in data_info["doc_ids"]["hits"]["hits"]:
            if es_type == "ioc":
                alarm_name = str(alert_info["_source"]["tools"] + " " + alert_info["_source"]["labels"]
                                 + " " + alert_info["_source"]["aptOrganization"]).strip()
            elif es_type == "model":
                alarm_name = alert_info["_source"]["modelName"]
            else:
                alarm_name = alert_info["_source"]["vulName"]
            threat_level = alert_info["_source"]["threatLevel"]
            threat_level_num = 2 if threat_level == "High" else 1 if threat_level == "Medium" else 0
            if alarm_name in alarm_name_dict.keys():
                if alarm_name_dict[alarm_name] > threat_level_num:
                    alarm_name_dict[alarm_name] = threat_level_num
            else:
                alarm_name_dict[alarm_name] = threat_level_num
            if alert_info["_source"]["dstIpMbInfo"]["assets"]:
                ip_list.add(alert_info["_source"]["flow"]["dst_ip"] + ",重点资产")
            else:
                ip_list.add(alert_info["_source"]["flow"]["dst_ip"])
            unique_id_set.add(alert_info["_source"]['uniqueId'])
    else:
        data_dict["city_name"] = \
            data_info["doc_ids"]["hits"]["hits"][0]["_source"]["dstIpGeoInfo"]["city_name"]
        data_dict["country_name"] = \
            data_info["doc_ids"]["hits"]["hits"][0]["_source"]["dstIpGeoInfo"]["country_name"]
        data_dict["organisation"] = "重点资产" if \
            data_info["doc_ids"]["hits"]["hits"][0]["_source"]["dstIpMbInfo"]["assets"] else ""
        for alert_info in data_info["doc_ids"]["hits"]["hits"]:
            if es_type == "ioc":
                alarm_name = str(alert_info["_source"]["tools"] + " " + alert_info["_source"]["labels"]
                                 + " " + alert_info["_source"]["aptOrganization"]).strip()
            elif es_type == "model":
                alarm_name = alert_info["_source"]["modelName"]
            else:
                alarm_name = alert_info["_source"]["vulName"]
            threat_level = alert_info["_source"]["threatLevel"]
            threat_level_num = 2 if threat_level == "High" else 1 if threat_level == "Medium" else 0
            if alarm_name in alarm_name_dict.keys():
                if alarm_name_dict[alarm_name] > threat_level_num:
                    alarm_name_dict[alarm_name] = threat_level_num
            else:
                alarm_name_dict[alarm_name] = threat_level_num
            if alert_info["_source"]["srcIpMbInfo"]["assets"]:
                ip_list.add(alert_info["_source"]["flow"]["src_ip"] + ",重点资产")
            else:
                ip_list.add(alert_info["_source"]["flow"]["src_ip"])
            unique_id_set.add(alert_info["_source"]['uniqueId'])
    data_dict["alarm_name"] = alarm_name_dict
    data_dict["ip_list"] = list(ip_list)
    data_dict["uniqueId"] = list(unique_id_set)

    return data_dict


def cert_alert_get_ipc_info(ip):
    # url = 'http://xxx:7070'  ## http://xxx:7070/?ipLocation=*******&token=xxx

    token = "10cd8a73aa5d46bb920e85a1b34c8674"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36"
    }
    try:
        mg = MongoDB('ndr')
        ret_data = mg.find_one('ipc_info', {'ip': ip})
        if ret_data and ret_data.get('ipc', None):
            city_name = ret_data['ipc']
        else:
            unit = search_unit(ip)
            if unit != "":
                city_name = unit
                mg.update('ipc_info', {'ip': ip}, {'ip': ip, 'ipc': unit}, insert=True)
            else:
                url = "http://************:7070/?ipLocation=%s&token=%s" % (ip, token)
                try:
                    req = requests.get(url=url, headers=headers, verify=False)
                    json_data = json.loads(req.text)
                    if "Error" in json_data:
                        ES_LOG.error(json_data["Error"])
                    city_name = json_data["User"]
                    mg.update('ipc_info', {'ip': ip}, {'ip': ip, 'ipc': str(json_data["User"])}, insert=True)
                except Exception as e:
                    ES_LOG.error("error：", e)
                    return ''
        return city_name
    except Exception as e:
        ES_LOG.error("error：", e)
        return ''
