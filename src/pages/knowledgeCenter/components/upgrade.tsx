import React, { useState } from "react";
import { Upload, Table, Modal, Progress, message as Message } from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';
import type { RcFile, UploadProps } from 'antd/es/upload/interface';
import { CloudUploadOutlined, InboxOutlined } from "@ant-design/icons";
import styles from "../style.less";

const { Dragger } = Upload;

interface InterProps {
  title: string;
  data: any[];
  total: number;
  currentMsg: { version:string;};
  columns: any[];
  params: {page:number;pageSize: number;}
  setParams: any;
  getUpdateLogs: ()=>void;
  getFeatureMsg: ()=>void;
  updateUrl: string;
  updateFilename: string;
}

const Upgrade = (props: InterProps) => {
  const { title, data, total, currentMsg, columns, setParams, params, getUpdateLogs, getFeatureMsg, updateUrl, updateFilename } = props;

  const [uploadVisible, setuploadVisible] = useState(false);
  const [defaultPercent, setdefaultPercent] = useState(0);
    // 分页change事件
    const pagination = {
      total: total,
      showSizeChanger: true,
      current: params.page || 1,
      pageSize: params.pageSize,
      showTotal: (total: number) => `共${total}条`,
      onChange(page: number, pageSize: any) {
        setParams({ ...params, page, pageSize });
      },
    };

      // 展示进度条
  const toggleVisibleStatusTrue = (percent: number) => {
    setuploadVisible(true);
    setdefaultPercent(percent);
  };

  // 上传页面关闭
  const toggleVisibleStatusFalse = () => {
    setuploadVisible(false);
  };

  // 上传
  const uploads: UploadProps = {
    accept: '.zip',
    name: updateFilename,
    action: updateUrl,
    multiple: false,
    showUploadList: false,
    onChange: (info: UploadChangeParam) => {
      if (info.file.status === 'uploading') {
        toggleVisibleStatusTrue(Math.floor(info.file.percent));
      } else if (!info.file.response.flag) {
        toggleVisibleStatusFalse();
        Message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
        getUpdateLogs();
        getFeatureMsg();
        return;
      } else if (info.file.status === 'done') {
        setTimeout(() => {
          Message.success('文件上传成功');
          toggleVisibleStatusFalse();
          getUpdateLogs();
          getFeatureMsg();
        }, 3000);
      } else if (info.file.status === 'error') {
        toggleVisibleStatusFalse();
        Message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
        getUpdateLogs();
        getFeatureMsg();
      }
    },
    beforeUpload: (file: RcFile) => {
      let pos = file.name.lastIndexOf('.');
      let result = file.name.substring(pos + 1, file.name.length);
      if (result !== 'zip') {
        Message.error('仅支持上传zip格式的文件');
        return Upload.LIST_IGNORE;
      }
      return true
    },
  };

  return <div className={styles.upgrade}>
    <div className={styles.upgradeTop}>
      <div className={styles.upgradeTitle}>
        <CloudUploadOutlined style={{ color: "rgb(100,195,79)", fontSize: '100px' }} />
        <div className={styles.upgradeTitleMsg}>
          <p className={styles.textTitle}>{title}</p>
          <p>当前版本号：{currentMsg.version} </p>
        </div>
      </div>
      <Dragger {...uploads}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域完成上传</p>
        <p className="ant-upload-hint">仅支持zip文件</p>
      </Dragger>
    </div>
    <Table columns={columns} pagination={pagination} dataSource={data} className={styles.tables} rowKey='_id' />
    <Modal
      destroyOnClose={true}
      title="文件正在上传..."
      visible={uploadVisible}
      maskClosable={false}
      keyboard={false}
      footer={null}
      closable={false}
    >
      <Progress percent={defaultPercent} status="active" />
    </Modal>
  </div>
}

export default Upgrade;