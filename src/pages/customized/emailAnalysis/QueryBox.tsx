import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import cs from 'classnames';
import { Form, Input, Button } from 'antd';
import SelectorWithCheckAll from '../SelectorWithCheckAll';

import { useConstantOptions } from '@/pages/threatAwareness/hooks';

import style from '../style.less';
import { useRecvOrgOptions } from './hooks';
import { TimeRange } from '../types';

const { Item } = Form;

export type Props = {
  listType: string;
  timeRange: TimeRange;
  initValue?: any;
  onSearch?: (values?: any) => void;
  className?: string;
};

function QueryBox(
  { onSearch, listType, initValue, timeRange }: Props,
  ref: React.Ref<{ search: (params: any) => void }>,
): JSX.Element {
  const [form] = Form.useForm();
  const { store } = useConstantOptions();

  const { loading, data: recvOrgOptions, setRange } = useRecvOrgOptions(timeRange);

  const handleSerach = (params: any) => {
    const values = {};
    Object.keys(params)?.forEach((key) => {
      if (!params[key]) return;
      if (
        (typeof params[key] === 'string' && !!params[key]?.trim()) ||
        (Array.isArray(params[key]) && params[key]?.length)
      ) {
        values[key] = params[key];
      }
    });
    onSearch?.(values);
  };

  const handleResetQuery = () => {
    form.resetFields();
    onSearch?.({});
  };

  const searchWithValues = (params: any) => {
    const prevValues = form.getFieldsValue();
    form.setFieldsValue(params);
    handleSerach({ ...prevValues, ...params });
  };

  useEffect(() => {
    setRange(timeRange);
  }, [timeRange]);

  useImperativeHandle(ref, () => ({
    search: searchWithValues,
  }));

  return (
    <div className={cs('flex w-full', style.query_box)}>
      <Form
        form={form}
        layout="inline"
        onFinish={handleSerach}
        initialValues={initValue}
        className={style.threat_file_analysis_query_form}
      >
        {['emailList', 'emailAttachmentList'].includes(listType) && (
          <>
            <Item label="邮件ID" name="msg_id">
              <Input allowClear />
            </Item>
            <Item label="威胁等级" name="severity">
              {store?.serverirty && store?.serverirty.length ? (
                <div>
                  <SelectorWithCheckAll
                    options={store?.serverirty
                      .filter(({ value }: any) => value)
                      .map(({ nameCn, value }: any) => ({ label: nameCn, value }))}
                  />
                </div>
              ) : null}
            </Item>
          </>
        )}
        {['emailAttack', 'emailList'].includes(listType) && (
          <Item label={`${listType === 'emailList' ? '收件' : '被攻击'}组织`} name="rsv_org">
            <SelectorWithCheckAll loading={loading} options={recvOrgOptions} />
          </Item>
        )}
        {listType === 'emailAttachmentList' && (
          <>
            <Item label="文件名" name="filename">
              <Input allowClear />
            </Item>
            <Item label="MD5" name="md5">
              <Input allowClear />
            </Item>
          </>
        )}
        <Item>
          <Button style={{ marginRight: 10 }} type="primary" htmlType="submit">
            搜索
          </Button>
          <Button onClick={handleResetQuery}>清除条件</Button>
        </Item>
      </Form>
    </div>
  );
}

export default forwardRef(QueryBox);
