import React from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import { useIntl } from 'umi';
import { ConfigProvider } from 'antd';
import { withRouter } from 'react-router';
import {
  BasicLayout as ProLayoutComponents,
  BasicLayoutProps as ProLayoutComponentsProps,
  MenuDataItem,
} from '@ant-design/pro-layout';
import globalStore from '@/store/global';
import { DefaultSettings } from '../../config/defaultSettings';

export interface BasicLayoutProps extends ProLayoutComponentsProps {
  sideBar: MenuDataItem[];
  breadcrumbNameMap: {
    [path: string]: MenuDataItem;
  };
  settings: DefaultSettings;
}

export type BasicLayoutContext = { [K in 'location']: BasicLayoutProps[K] } & {
  breadcrumbNameMap: {
    [path: string]: MenuDataItem;
  };
};

const BasicLayout: React.FC<BasicLayoutProps> = props => {
  const collapsed = globalStore.useState('collapsed');
  const settings = globalStore.useState('settings');

  const { formatMessage } = useIntl();
  const { children } = props;

  const handleMenuCollapse = (payload: boolean) => {
    globalStore.setState({ collapsed: payload });
  };

  return (
    <ConfigProvider>
      <ProLayoutComponents
        collapsed={collapsed}
        breakpoint={false}
        onCollapse={handleMenuCollapse}
        formatMessage={formatMessage}
        {...props}
        {...settings}
      >
        {children}
      </ProLayoutComponents>
    </ConfigProvider>
  );
};

const SubApp: React.FC = ({ children }) => (
  <ConfigProvider>{children}</ConfigProvider>
);

export default withRouter(
  // eslint-disable-next-line
  window && window.__POWERED_BY_QIANKUN__ ? SubApp : BasicLayout,
);
