# -*- coding: utf-8 -*-
# @Time    : 2020-05-14 10:10
# <AUTHOR> wu
# @File    : flask_log.py
# @Software: PyCharm


""" Flask log process"""
import requests
from datetime import datetime
from flask import request
from utils.database import MongoDB
import queue
import select
from utils.utils import PollableQueue


# 初始化一个消息队列用于推送日志
log_queue = queue.Queue()


def ndr_log_to_box(log_type='run', event=""):
    """
    :param log_type:
    :param event:
    :return:
    """
    try:
        current_user = 'admin'
        mongodb = MongoDB('boss')
        user = mongodb.find_one('auth_users', {'account': current_user})

        if not user:
            print("ERROR: User admin not found.")
            return

        data = {
            'code': 0,
            'data': "",
            'ip': request.headers.getlist("X-Forwarded-For")[0] if request.headers.get('X-Forwarded-For', '') else '',
            'method': request.method,
            'msg': "",
            'operationContent': event,
            'operationType': '运行日志' if log_type == 'run' else '操作日志',
            'path': request.url,
            'starttime': datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ"),  # 盒子需要给utc时间，前端展示时自动转为北京时间
            'ua': "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                  "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36",
            'userId': str(user['_id']),
            'userName': current_user
        }

        log_queue.put_nowait(data)
    except Exception as e:
        pass


def ndr_log_to_box_thread(q):
    while True:
        try:
            data = q.get()
            if not data:
                continue

            requests.post('http://boss-api-v2-prd:7001/api/auditLog', data=data, timeout=3)  # 最好设置超时，否则会阻塞影响其他输出
        except Exception as e:
            print('PollQueueServer Error: %s' % str(e))
            continue

        # can_read, _, _ = select.select([queue, ], [], [])
        # for r in can_read:
        #     data = r.get()
        #     if not data:
        #         continue
        #     try:
        #         requests.post('http://boss-api-v2-prd:7001/api/auditLog', data=data, timeout=3)  # 最好设置超时，否则会阻塞影响其他输出
        #     except Exception as e:
        #         print('PollQueueServer Error: %s' % str(e))
        #         continue
