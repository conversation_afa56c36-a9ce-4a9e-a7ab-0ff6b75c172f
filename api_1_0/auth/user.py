
# -*- coding: utf-8 -*-
# @Time    : 2019-06-13 18:10
# <AUTHOR> wu
# @File    : user.py
# @Software: PyCharm

"""module users"""
import time
import hashlib
import re
import json
from flask import request
from flask import make_response
from flask import jsonify
from flask_restful import Resource
from werkzeug.security import generate_password_hash
from config.config import UserConfig, NdrLog
from utils.database import MongoDB
from utils.param_check import Validator, check_flask_args
from utils.json_format import JSONEncoder
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from api_1_0.auth.auths import check_password, Authenticate
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def assign_id():
    """ assign user id """
    return hashlib.md5(str(time.clock()).encode('utf-8')).hexdigest()


def password_complexity_check(password):
    """ check password complexity """
    return re.match(r'(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,64}', password)


def init_admin():
    """create admin """
    mongodb = MongoDB('ndr')
    user = mongodb.find_one('user', {'name': 'admin'})
    if user is not None:
        LOG.warning("User admin already exist.")
        return 0
    user = {'name': 'admin',
            'id': assign_id(),
            'password': UserConfig.Password,
            'group': 'administrator',
            'active': True,
            'email': '',
            'phone': '',
            'department': '',
            'create_time': int(time.time() * 1000),
            'status': 'offline',
            'user_ip': ''}
    LOG.info("Create admin success")
    return mongodb.insert_one('user', user)


def init_auditor():
    """create auditor """
    mongodb = MongoDB('ndr')
    user = mongodb.find_one('user', {'name': 'auditor'})
    if user is not None:
        LOG.warning("User auditor already exist.")
        return 0
    user = {'name': 'auditor',
            'id': assign_id(),
            'password': UserConfig.Password,
            'group': 'auditor',
            'active': True,
            'email': '',
            'phone': '',
            'department': '',
            'create_time': int(time.time() * 1000),
            'status': 'offline',
            'user_ip': ''}
    LOG.info("Create auditor success")
    return mongodb.insert_one('user', user)


class Users(Resource):
    """User"""
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @check_flask_args(Validator("user_modify_schema"), request)
    def put(self, **kwargs):
        """change password"""
        user_id = kwargs['userId']
        old_password = kwargs['oldPassword']
        new_password = kwargs['newPassword']
        active = kwargs['active']
        email = kwargs['email']
        phone = kwargs['phone']
        department = kwargs['department']
        user = self.mongodb.find_one('user', {'id': user_id})
        is_password_change = False
        if user is None:
            ndr_log_to_box(NdrLog.Type.OPERATE, "修改密码失败：用户不存在。")
            return flask_response('User does not exist', False, {})

        name, _, _ = Authenticate().get_current_user()
        if name == 'admin' and user['name'] != 'admin' and new_password != "":
            if not password_complexity_check(new_password):
                ndr_log_to_box(NdrLog.Type.OPERATE, "修改[%s]密码失败：新密码必须包含字母、数字和特殊字符。" % user['name'])
                return flask_response('The new password must contain letters, numbers and special characters!', False, {})
            password = generate_password_hash(new_password)
            user['status'] = 'offline'
        elif old_password != "" and new_password != "":
            if not password_complexity_check(new_password):
                ndr_log_to_box(NdrLog.Type.OPERATE, "修改[%s]密码失败：新密码必须包含字母、数字和特殊字符。" % user['name'])
                return flask_response('The new password must contain letters, numbers and special characters!', False, {})
            ret, info = check_password(user['name'], old_password)
            if not ret:
                ndr_log_to_box(NdrLog.Type.OPERATE, "用户[admin]修改密码失败：%s" % info)
                LOG.error("Password change failed.info: %s" % info)
                return flask_response("Wrong old password.", False, {})
            password = generate_password_hash(new_password)
            is_password_change = True
        elif old_password == "" and new_password == "":
            password = user['password']
        else:
            ndr_log_to_box(NdrLog.Type.OPERATE, "修改[%s]密码失败：旧密码和新密码都必须输入。" % user['name'])
            return flask_response('Old password and new password must be entered.', False, {})
        msg = ''
        body = Authenticate().identify()
        if body['flag'] and name == user['name'] and is_password_change:
            Authenticate().encode_auth_token(user['id'], expired_time=1)
            LOG.info("User [%s] logout successfully." % user['name'])
            msg = 'User information modified successfully, please log in again.'
            data = make_response(jsonify(body))
            data.delete_cookie('token')
        if is_password_change:
            ndr_log_to_box(NdrLog.Type.OPERATE, "修改[%s]密码成功。" % user["name"])
            LOG.info("User [%s] modified password successfully" % user['name'])
        else:
            ndr_log_to_box(NdrLog.Type.OPERATE, "修改[%s]资料成功。" % user["name"])
            LOG.info("User [%s] modified info successfully" % user['name'])
        if user['name'] == 'admin':
            active = True
        self.mongodb.update('user', {'id': user_id},
                            {'password': password,
                             'active': active,
                             'email': email,
                             'phone': phone,
                             'department': department,
                             'status': user['status']})
        return flask_response(msg, True, {})

    @check_flask_args(Validator("user_get_all_schema"), request)
    def get(self, **kwargs):
        """获取所有用户"""
        group = kwargs['type']
        name = kwargs['name']
        page_size = int(kwargs['pageSize'])
        page = int(kwargs['page'])
        condition = {"$and": []}
        if group != '':
            condition["$and"].append({"group": group})
        if name != '':
            condition["$and"].append({"name": {'$regex': name, '$options': 'i'}})
        if not condition["$and"]:
            condition = {}
        data = {"count": self.mongodb.find('user', condition).count()}
        rst = self.mongodb.find('user', condition,
                                {"_id": 0,
                                 "name": 1,
                                 "id": 1,
                                 "group": 1,
                                 "active": 1,
                                 "email": 1,
                                 "phone": 1,
                                 "department": 1,
                                 "create_time": 1,
                                 "last_login_time": 1,
                                 "status": 1
                                 }).sort([('create_time', -1)]).limit(page_size).skip((page - 1) * page_size)
        if rst is None:
            ndr_log_to_box(NdrLog.Type.OPERATE, "获取用户信息失败：不存在。")
            return flask_response('Not found', False, {})
        cols = []
        for col in rst:
            col = JSONEncoder().encode(col)
            cols.append(json.loads(col))
        data["detail"] = cols
        data["page"] = page
        data["pageSize"] = page_size
        return flask_response("", True, data)

    @check_flask_args(Validator("user_create_schema"), request)
    def post(self, **kwargs):
        """创建用户"""
        if self.mongodb.find('user', {}).count() >= 18:
            return flask_response("The number of users reaches the maximum", False, {})
        name = kwargs['username']
        password = kwargs['newPassword']
        active = kwargs['active']
        email = kwargs['email']
        phone = kwargs['phone']
        department = kwargs['department']

        user = self.mongodb.find_one('user', {'name': name})
        if user is not None:
            ndr_log_to_box(NdrLog.Type.OPERATE, "创建用户失败：[%s]已存在。" % name)
            LOG.error("User [%s] already exist." % name)
            return flask_response('User [%s] already exists' % name, False, {})

        if password_complexity_check(password):
            user = {'name': name,
                    'id': assign_id(),
                    'password': generate_password_hash(password),
                    'group': 'operator',
                    'active': active,
                    'email': email,
                    'phone': phone,
                    'department': department,
                    'create_time': int(time.time() * 1000),
                    'status': 'offline',
                    'user_ip': ''}
            self.mongodb.insert_one('user', user)
            ndr_log_to_box(NdrLog.Type.OPERATE, "创建用户[%s]成功。" % name)
            LOG.info("User [%s] created successfully." % name)
            return flask_response('', True, {})
        ndr_log_to_box(NdrLog.Type.OPERATE, "创建用户[%s]失败：密码必须办好字母、数字和特殊字符。" % name)
        return flask_response('The password must contain letters, numbers and special characters!', False, {})

    @check_flask_args(Validator("user_delete_schema"), request)
    def delete(self, **kwargs):
        """删除用户"""
        user_list = kwargs['userId']
        rst = []
        for user_id in user_list:
            user = self.mongodb.find_one('user', {'id': user_id})
            if user is None:
                ndr_log_to_box(NdrLog.Type.OPERATE, "删除用户失败：[%s]不存在。" % user_id)
                LOG.error("User [%s] does not exist." % user_id)
                rst.append("User [%s] does not exist." % user_id)
                continue
            if user['name'] in ['admin', 'auditor']:
                ndr_log_to_box(NdrLog.Type.OPERATE, "删除用户失败：预定义用户[%s]不允许删除。" % user['name'])
                rst.append('The preset user [%s] cannot be deleted' % user['name'])
                continue
            body = Authenticate().identify()
            if body['flag']:
                Authenticate().encode_auth_token(user['id'], expired_time=1)
                ndr_log_to_box(NdrLog.Type.OPERATE, "删除用户[%s]成功。" % user['name'])
                LOG.info("User [%s] deleted successfully." % user['name'])
                data = make_response(jsonify(body))
                data.delete_cookie('token')
            self.mongodb.delete("user", {"id": user_id})
        return flask_response('', True, rst)


class UserGetOne(Resource):
    """get one user"""
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    def get(self, user_id):
        """获取单个用户"""
        user = self.mongodb.find_one('user', {'id': user_id}, {"_id": 0,
                                                               "name": 1,
                                                               "id": 1,
                                                               "group": 1,
                                                               "active": 1,
                                                               "email": 1,
                                                               "phone": 1,
                                                               "department": 1,
                                                               "create_time": 1,
                                                               "last_login_time": 1,
                                                               "status": 1
                                                               })
        if user is None:
            return flask_response('User does not exist', False, {})
        return flask_response('', True, json.loads(JSONEncoder().encode(user)))
