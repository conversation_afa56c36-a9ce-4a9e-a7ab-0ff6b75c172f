#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import json
import time
import traceback

from api_1_0.utils.flask_log import ndr_log_to_box
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator
from config.config import NdrLog
from utils import param_check
from flask import request
from bson import ObjectId

from utils.cluster import *

TABLE_NAME = 'cluster'

NODE_STATUS_OFFLINE = 'offline'
NODE_STATUS_ONLINE = 'online'
NODE_STATUS_REMOVED = 'removed'

FIELDS = {
    "node_name": "",
    "node_status": "",
    "node_role": "",
    "node_ip": "",
    "description": ""
}

AUTO_FIELDS = [
    "created_at",
    "updated_at",
]


def data_from_args(args):
    data = {}
    for k in FIELDS:
        if k in args:
            data[k] = args.get(k)

    return data


def timestamp():
    return int(time.time() * 1000)


LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class Nodes(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    @param_check.check_flask_args(Validator("cluster_node_get_schema"), request)
    def get(self, **kwargs):
        """
        获取节点列表
        """
        args = kwargs.get("args")
        page = int(args.get("page"))
        page_size = int(args.get("pageSize"))
        try:
            total = self.mongodb.find(self.table_name, {}).count()
            result = self.mongodb.find(self.table_name, {}).sort([("node_name", 1)]).limit(page_size).skip(
                (page - 1) * page_size)

            cols = []
            for r in result:
                r["_id"] = str(r["_id"])
                cols.append(r)

            data = {"total": total,
                    "cols": cols,
                    "page": page,
                    "pageSize": page_size}
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Get cluster nodes ERROR: %s' % e)
            return flask_response('failed', False, {})

    @param_check.check_flask_args(Validator("cluster_node_schema"), request)
    def post(self, **kwargs):
        """
        添加节点
        """
        args = kwargs['args']
        data = data_from_args(args)
        data['node_status'] = NODE_STATUS_ONLINE

        try:
            old = self.mongodb.find_one(self.table_name, {"node_name": data['node_name']})
            if old is not None:
                return flask_response('节点名与其他节点冲突', False, {})

            old = self.mongodb.find_one(self.table_name, {"node_ip": data['node_ip']})
            if old is not None:
                return flask_response('节点IP与其他节点冲突', False, {})

            if data.get('node_role') == 'master':
                old = self.mongodb.find(self.table_name, {"node_role": 'master'})
                if old is None or old.count() > 0:
                    return flask_response('主节点已存在', False, {})

            inserted_id = self.mongodb.insert_one(self.table_name, data)
            if inserted_id == "":
                raise ValueError("insert failed")

            master = self.mongodb.find_one(self.table_name, {"node_role": 'master'})
            mgmt_node_ip = master['node_ip'] if master else ''

            r = cluster_add_node({
                "node_name": data['node_name'],
                "node_ip": data['node_ip'],
                "node_role": data['node_role'],
                "node_status": data['node_status'],
                "mgmt_node_ip": mgmt_node_ip,
            })
            if r:
                LOG.error(f'add node to cluster failed, failed nodes: {r}')

            data['_id'] = str(inserted_id)
            ndr_log_to_box(NdrLog.Type.OPERATE, "集群节点添加成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Create cluster nodes ERROR: %s\n%s' % (str(e), traceback.format_exc()))
            ndr_log_to_box(NdrLog.Type.OPERATE, "集群节点添加失败")
            return flask_response('create failed', False, {})

class Node(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def delete(self, **kwargs):
        """
        删除节点
        """
        LOG.error("%s" % json.dumps(kwargs))
        condition = {'node_name': kwargs['node_name']}
        try:
            r = self.mongodb.delete(self.table_name, condition)
            if r == 0:
                raise ValueError("delete failed")

            r = cluster_delete_node(r['node_ip'])
            if r:
                LOG.error(f'delete node failed, failed nodes: {r}')

            ndr_log_to_box(NdrLog.Type.OPERATE, "集群节点删除成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error('Delete cluster node ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "集群节点删除失败")
            return flask_response('delete failed', False, {})

class NodeLeave(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def post(self, **kwargs):
        """
        下线节点
        """
        node_name = kwargs['node_name']
        try:
            doc = self.mongodb.find_one(self.table_name, {"node_name": node_name})
            if doc is None:
                return flask_response('node does not exist', False, {})

            if doc['node_role'] == 'master':
                return flask_response('master node can not leave from cluster', False, {})

            r = cluster_delete_node(doc['node_ip'])
            if r:
                LOG.error(f'leave node failed, failed nodes: {r}')

            self.mongodb.update_one(self.table_name, {"node_name": node_name},{'$set': {"node_status": NODE_STATUS_REMOVED, "node_role": 'single'}})

            ndr_log_to_box(NdrLog.Type.OPERATE, "下线节点成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error('Leave node ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "下线节点失败")
            return flask_response('leave node failed', False, {})

class NodeJoin(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def post(self, **kwargs):
        """
        上线节点
        """
        node_name = kwargs['node_name']
        try:
            doc = self.mongodb.find_one(self.table_name, {"node_name": node_name})
            if doc is None:
                return flask_response('node does not exist', False, {})

            if doc['node_role'] == 'single':
                doc['node_role'] = 'slave'
            r = cluster_add_node({
                "node_name": node_name,
                "node_ip": doc['node_ip'],
                "node_role": doc['node_role'],
                "node_status": NODE_STATUS_ONLINE,
                "mgmt_node_ip": doc.get('mgmt_node_ip', ''),
            })
            if r:
                LOG.error(f'join node failed, failed nodes: {r}')

            self.mongodb.update_one(self.table_name, {"node_name": node_name},{'$set': {"node_status": NODE_STATUS_ONLINE, "node_role": doc['node_role']}})

            ndr_log_to_box(NdrLog.Type.OPERATE, "上线节点成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error('Join nodes ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "上线节点失败")
            return flask_response('join node failed', False, {})


class NodeBeMaster(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def post(self, **kwargs):
        """
        设置master节点
        """
        node_name = kwargs['node_name']
        try:
            doc = self.mongodb.find_one(self.table_name, {"node_name": node_name})
            if doc is None:
                return flask_response('节点不存在', False, {})

            if doc['node_role'] == 'single':
                return flask_response('single node can not be master', False, {})

            r = cluster_set_mgmt_node_ip(doc['node_ip'])
            if r:
                LOG.error(f'set master node failed, failed nodes: {r}')

            self.mongodb.update_one(self.table_name, {"node_role": 'master'},
                                    {'$set': {"node_role": 'slave'}})
            self.mongodb.update_one(self.table_name, {"node_name": node_name},
                                    {'$set': {"node_role": 'master'}})

            ndr_log_to_box(NdrLog.Type.OPERATE, "设置集群主节点成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error('set cluster master ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "设置集群主节点失败")
            return flask_response('set cluster master failed', False, {})