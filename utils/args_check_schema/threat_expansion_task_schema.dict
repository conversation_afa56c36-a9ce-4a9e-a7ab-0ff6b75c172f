{"type": "object", "required": ["alias", "engine", "dock"], "properties": {"alias": {"type": "string"}, "engine": {"type": "string", "enum": ["zoomeye", "zoomEye", "fofa", "shodan", "censys", "other"]}, "type": {"type": "string"}, "org": {"type": "string"}, "tool": {"type": "string"}, "tag": {"type": "string"}, "author": {"type": "string"}, "sig_type": {"type": "string"}, "sig_status": {"type": "string", "enum": ["active", "inactive"]}, "dock": {"type": "string"}, "description": {"type": "string"}}}