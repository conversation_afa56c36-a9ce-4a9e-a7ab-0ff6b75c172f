# -*- coding: utf-8 -*-
# @Time    : 2019-09-06 14:10
# <AUTHOR> wu
# @File    : utils.py
# @Software: PyCharm

"""module explore"""
import json
from utils.logger import get_ndr_logger
from utils.database import MongoDB
from utils.json_format import JSONEncoder

LOG = get_ndr_logger('mongo_log', __file__)


def explore_get_all_name_and_id():
    """
    :return: [{'taskName':'name','taskId':'id'},{},...]
    """
    try:
        mongodb = MongoDB("ndr")
    except TypeError as err:
        LOG.error('mongodb access failed.Reason: %s' % err)
        return None

    rst = mongodb.find('back_explore', {}, {"_id": 0,
                                            "taskName": 1,
                                            "taskId": 1,
                                            })
    cols = []
    for col in rst:
        col = JSONEncoder().encode(col)
        cols.append(json.loads(col))
    return cols
