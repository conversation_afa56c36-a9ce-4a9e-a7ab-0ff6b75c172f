#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import json
import time
import traceback

from api_1_0.utils.flask_log import ndr_log_to_box
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator
from config.config import NdrLog
from utils import param_check
from flask import request
from bson import ObjectId

from .engine import run_task

TABLE_NAME = 'apt_org_fingerprint'

APTORG_FIELDS = {
    "name": "",
    "fingerprint": "",
}


def data_from_args(args):
    data = {}
    for k in APTORG_FIELDS:
        if k in args:
            data[k] = args.get(k)

    return data


def timestamp():
    return int(time.time() * 1000)


LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class AptOrgs(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    @staticmethod
    def query_condition(args):
        condition = {}

        if args.get("start_time") or args.get("stop_time"):
            condition["update_time"] = {
                "$gte": int(args.get("start_time", 0)),
                "$lte": int(args.get("stop_time", 2 ** 63 - 1))
            }

        keys = ["name", "fingerprint"]
        for k, v in args.items():
            if k in keys and v:
                condition[k] = v

        return condition

    @param_check.check_flask_args(Validator("threat_expansion_search_aptorg"), request)
    def get(self, **kwargs):
        """
        获取任务列表
        """
        args = kwargs.get("args")
        page = int(args.get("page", 1))
        page_size = int(args.get("pageSize", 10))
        try:
            condition = self.query_condition(args)
            total = self.mongodb.find(self.table_name, condition).count()
            result = self.mongodb.find(self.table_name, condition).sort([("name", 1)]).limit(page_size).skip(
                (page - 1) * page_size)

            cols = []
            for r in result:
                r["_id"] = str(r["_id"])
                cols.append(r)

            data = {"total": total,
                    "cols": cols,
                    "page": page,
                    "pageSize": page_size}
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Get threat expansion apt org ERROR: %s' % e)
            return flask_response('failed', False, {})


class AptOrg(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def get(self, _id):
        """
        获取APT组织指纹
        """
        try:
            condition = {'_id': ObjectId(_id)}
            data = self.mongodb.find_one(self.table_name, condition)
            data['_id'] = str(data['_id'])
            return data
        except Exception as e:
            LOG.error('Create apt org ERROR: %s' % e)
            return flask_response('', False, {})

    def put(self, _id, name, fingerprint):
        """
        更新APT组织指纹
        """
        condition = {'_id': ObjectId(_id)}
        data = {
            "_id": ObjectId(_id),
            "name": name,
            "fingerprint": fingerprint,
            "update_time": timestamp()
        }

        try:
            fingerprint = self.mongodb.find_one(self.table_name, condition)
            if not fingerprint:
                r = self.mongodb.insert_one(self.table_name, data)
                if r == "":
                    raise ValueError("create failed")
            else:
                r = self.mongodb.update_one(self.table_name, condition, {"$set": data})
                if r == 0:
                    raise ValueError("update failed")
            return
        except Exception as e:
            LOG.error('Update apt org fingerprint ERROR: %s' % e)
            return

    def delete(self, _id):
        """
        删除APT组织指纹
        """
        condition = {'_id': ObjectId(_id)}
        try:
            r = self.mongodb.delete(self.table_name, condition)
            if r == 0:
                raise ValueError("delete failed")
            return
        except Exception as e:
            LOG.error('Delete apt org fingerprint ERROR: %s' % e)
            return