# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: die.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='die.proto',
  package='die',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\tdie.proto\x12\x03\x64ie\",\n\x0bReplyHeader\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\"Q\n\x11\x44IEFeatureRequest\x12!\n\x08metadata\x18\x01 \x01(\x0b\x32\r.die.MetadataH\x00\x12\x0e\n\x04\x66ile\x18\x02 \x01(\x0cH\x00\x42\t\n\x07request\"i\n\x14\x44IEAddFeatureRequest\x12!\n\x08metadata\x18\x01 \x01(\x0b\x32\r.die.MetadataH\x00\x12\x0e\n\x04rule\x18\x02 \x01(\x0cH\x00\x12\x13\n\trule_info\x18\x03 \x01(\x0cH\x00\x42\t\n\x07request\"(\n\x08Metadata\x12\x0e\n\x06\x61\x63tion\x18\x01 \x01(\t\x12\x0c\n\x04size\x18\x02 \x01(\x03\"7\n\x18\x44IEOperateFeatureRequest\x12\x0b\n\x03sid\x18\x01 \x03(\r\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\"+\n\x19\x44IENewSsnLogSwitchRequest\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\"&\n\x15\x46ileRestoreAppRequest\x12\r\n\x05\x61ppid\x18\x01 \x03(\r\"&\n\x16\x46ileRestoreTypeRequest\x12\x0c\n\x04type\x18\x01 \x01(\x04\"2\n\x16\x46ileRestoreSizeRequest\x12\x0b\n\x03min\x18\x01 \x01(\r\x12\x0b\n\x03max\x18\x02 \x01(\r2\xdf\x03\n\nDIEService\x12\x37\n\x07\x46\x65\x61ture\x12\x16.die.DIEFeatureRequest\x1a\x10.die.ReplyHeader\"\x00(\x01\x12=\n\nAddFeature\x12\x19.die.DIEAddFeatureRequest\x1a\x10.die.ReplyHeader\"\x00(\x01\x12\x43\n\x0eOperateFeature\x12\x1d.die.DIEOperateFeatureRequest\x1a\x10.die.ReplyHeader\"\x00\x12\x45\n\x0fNewSsnLogSwitch\x12\x1e.die.DIENewSsnLogSwitchRequest\x1a\x10.die.ReplyHeader\"\x00\x12\x45\n\x13\x46ileRestoreProtocol\x12\x1a.die.FileRestoreAppRequest\x1a\x10.die.ReplyHeader\"\x00\x12\x42\n\x0f\x46ileRestoreType\x12\x1b.die.FileRestoreTypeRequest\x1a\x10.die.ReplyHeader\"\x00\x12\x42\n\x0f\x46ileRestoreSize\x12\x1b.die.FileRestoreSizeRequest\x1a\x10.die.ReplyHeader\"\x00\x62\x06proto3'
)




_REPLYHEADER = _descriptor.Descriptor(
  name='ReplyHeader',
  full_name='die.ReplyHeader',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='die.ReplyHeader.code', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='message', full_name='die.ReplyHeader.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18,
  serialized_end=62,
)


_DIEFEATUREREQUEST = _descriptor.Descriptor(
  name='DIEFeatureRequest',
  full_name='die.DIEFeatureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='metadata', full_name='die.DIEFeatureRequest.metadata', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='file', full_name='die.DIEFeatureRequest.file', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='die.DIEFeatureRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=64,
  serialized_end=145,
)


_DIEADDFEATUREREQUEST = _descriptor.Descriptor(
  name='DIEAddFeatureRequest',
  full_name='die.DIEAddFeatureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='metadata', full_name='die.DIEAddFeatureRequest.metadata', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rule', full_name='die.DIEAddFeatureRequest.rule', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rule_info', full_name='die.DIEAddFeatureRequest.rule_info', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='die.DIEAddFeatureRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=147,
  serialized_end=252,
)


_METADATA = _descriptor.Descriptor(
  name='Metadata',
  full_name='die.Metadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='action', full_name='die.Metadata.action', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size', full_name='die.Metadata.size', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=254,
  serialized_end=294,
)


_DIEOPERATEFEATUREREQUEST = _descriptor.Descriptor(
  name='DIEOperateFeatureRequest',
  full_name='die.DIEOperateFeatureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sid', full_name='die.DIEOperateFeatureRequest.sid', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='action', full_name='die.DIEOperateFeatureRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=296,
  serialized_end=351,
)


_DIENEWSSNLOGSWITCHREQUEST = _descriptor.Descriptor(
  name='DIENewSsnLogSwitchRequest',
  full_name='die.DIENewSsnLogSwitchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enable', full_name='die.DIENewSsnLogSwitchRequest.enable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=353,
  serialized_end=396,
)


_FILERESTOREAPPREQUEST = _descriptor.Descriptor(
  name='FileRestoreAppRequest',
  full_name='die.FileRestoreAppRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='appid', full_name='die.FileRestoreAppRequest.appid', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=398,
  serialized_end=436,
)


_FILERESTORETYPEREQUEST = _descriptor.Descriptor(
  name='FileRestoreTypeRequest',
  full_name='die.FileRestoreTypeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='die.FileRestoreTypeRequest.type', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=438,
  serialized_end=476,
)


_FILERESTORESIZEREQUEST = _descriptor.Descriptor(
  name='FileRestoreSizeRequest',
  full_name='die.FileRestoreSizeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min', full_name='die.FileRestoreSizeRequest.min', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max', full_name='die.FileRestoreSizeRequest.max', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=478,
  serialized_end=528,
)

_DIEFEATUREREQUEST.fields_by_name['metadata'].message_type = _METADATA
_DIEFEATUREREQUEST.oneofs_by_name['request'].fields.append(
  _DIEFEATUREREQUEST.fields_by_name['metadata'])
_DIEFEATUREREQUEST.fields_by_name['metadata'].containing_oneof = _DIEFEATUREREQUEST.oneofs_by_name['request']
_DIEFEATUREREQUEST.oneofs_by_name['request'].fields.append(
  _DIEFEATUREREQUEST.fields_by_name['file'])
_DIEFEATUREREQUEST.fields_by_name['file'].containing_oneof = _DIEFEATUREREQUEST.oneofs_by_name['request']
_DIEADDFEATUREREQUEST.fields_by_name['metadata'].message_type = _METADATA
_DIEADDFEATUREREQUEST.oneofs_by_name['request'].fields.append(
  _DIEADDFEATUREREQUEST.fields_by_name['metadata'])
_DIEADDFEATUREREQUEST.fields_by_name['metadata'].containing_oneof = _DIEADDFEATUREREQUEST.oneofs_by_name['request']
_DIEADDFEATUREREQUEST.oneofs_by_name['request'].fields.append(
  _DIEADDFEATUREREQUEST.fields_by_name['rule'])
_DIEADDFEATUREREQUEST.fields_by_name['rule'].containing_oneof = _DIEADDFEATUREREQUEST.oneofs_by_name['request']
_DIEADDFEATUREREQUEST.oneofs_by_name['request'].fields.append(
  _DIEADDFEATUREREQUEST.fields_by_name['rule_info'])
_DIEADDFEATUREREQUEST.fields_by_name['rule_info'].containing_oneof = _DIEADDFEATUREREQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['ReplyHeader'] = _REPLYHEADER
DESCRIPTOR.message_types_by_name['DIEFeatureRequest'] = _DIEFEATUREREQUEST
DESCRIPTOR.message_types_by_name['DIEAddFeatureRequest'] = _DIEADDFEATUREREQUEST
DESCRIPTOR.message_types_by_name['Metadata'] = _METADATA
DESCRIPTOR.message_types_by_name['DIEOperateFeatureRequest'] = _DIEOPERATEFEATUREREQUEST
DESCRIPTOR.message_types_by_name['DIENewSsnLogSwitchRequest'] = _DIENEWSSNLOGSWITCHREQUEST
DESCRIPTOR.message_types_by_name['FileRestoreAppRequest'] = _FILERESTOREAPPREQUEST
DESCRIPTOR.message_types_by_name['FileRestoreTypeRequest'] = _FILERESTORETYPEREQUEST
DESCRIPTOR.message_types_by_name['FileRestoreSizeRequest'] = _FILERESTORESIZEREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ReplyHeader = _reflection.GeneratedProtocolMessageType('ReplyHeader', (_message.Message,), {
  'DESCRIPTOR' : _REPLYHEADER,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.ReplyHeader)
  })
_sym_db.RegisterMessage(ReplyHeader)

DIEFeatureRequest = _reflection.GeneratedProtocolMessageType('DIEFeatureRequest', (_message.Message,), {
  'DESCRIPTOR' : _DIEFEATUREREQUEST,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.DIEFeatureRequest)
  })
_sym_db.RegisterMessage(DIEFeatureRequest)

DIEAddFeatureRequest = _reflection.GeneratedProtocolMessageType('DIEAddFeatureRequest', (_message.Message,), {
  'DESCRIPTOR' : _DIEADDFEATUREREQUEST,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.DIEAddFeatureRequest)
  })
_sym_db.RegisterMessage(DIEAddFeatureRequest)

Metadata = _reflection.GeneratedProtocolMessageType('Metadata', (_message.Message,), {
  'DESCRIPTOR' : _METADATA,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.Metadata)
  })
_sym_db.RegisterMessage(Metadata)

DIEOperateFeatureRequest = _reflection.GeneratedProtocolMessageType('DIEOperateFeatureRequest', (_message.Message,), {
  'DESCRIPTOR' : _DIEOPERATEFEATUREREQUEST,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.DIEOperateFeatureRequest)
  })
_sym_db.RegisterMessage(DIEOperateFeatureRequest)

DIENewSsnLogSwitchRequest = _reflection.GeneratedProtocolMessageType('DIENewSsnLogSwitchRequest', (_message.Message,), {
  'DESCRIPTOR' : _DIENEWSSNLOGSWITCHREQUEST,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.DIENewSsnLogSwitchRequest)
  })
_sym_db.RegisterMessage(DIENewSsnLogSwitchRequest)

FileRestoreAppRequest = _reflection.GeneratedProtocolMessageType('FileRestoreAppRequest', (_message.Message,), {
  'DESCRIPTOR' : _FILERESTOREAPPREQUEST,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.FileRestoreAppRequest)
  })
_sym_db.RegisterMessage(FileRestoreAppRequest)

FileRestoreTypeRequest = _reflection.GeneratedProtocolMessageType('FileRestoreTypeRequest', (_message.Message,), {
  'DESCRIPTOR' : _FILERESTORETYPEREQUEST,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.FileRestoreTypeRequest)
  })
_sym_db.RegisterMessage(FileRestoreTypeRequest)

FileRestoreSizeRequest = _reflection.GeneratedProtocolMessageType('FileRestoreSizeRequest', (_message.Message,), {
  'DESCRIPTOR' : _FILERESTORESIZEREQUEST,
  '__module__' : 'die_pb2'
  # @@protoc_insertion_point(class_scope:die.FileRestoreSizeRequest)
  })
_sym_db.RegisterMessage(FileRestoreSizeRequest)



_DIESERVICE = _descriptor.ServiceDescriptor(
  name='DIEService',
  full_name='die.DIEService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=531,
  serialized_end=1010,
  methods=[
  _descriptor.MethodDescriptor(
    name='Feature',
    full_name='die.DIEService.Feature',
    index=0,
    containing_service=None,
    input_type=_DIEFEATUREREQUEST,
    output_type=_REPLYHEADER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AddFeature',
    full_name='die.DIEService.AddFeature',
    index=1,
    containing_service=None,
    input_type=_DIEADDFEATUREREQUEST,
    output_type=_REPLYHEADER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='OperateFeature',
    full_name='die.DIEService.OperateFeature',
    index=2,
    containing_service=None,
    input_type=_DIEOPERATEFEATUREREQUEST,
    output_type=_REPLYHEADER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='NewSsnLogSwitch',
    full_name='die.DIEService.NewSsnLogSwitch',
    index=3,
    containing_service=None,
    input_type=_DIENEWSSNLOGSWITCHREQUEST,
    output_type=_REPLYHEADER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FileRestoreProtocol',
    full_name='die.DIEService.FileRestoreProtocol',
    index=4,
    containing_service=None,
    input_type=_FILERESTOREAPPREQUEST,
    output_type=_REPLYHEADER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FileRestoreType',
    full_name='die.DIEService.FileRestoreType',
    index=5,
    containing_service=None,
    input_type=_FILERESTORETYPEREQUEST,
    output_type=_REPLYHEADER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FileRestoreSize',
    full_name='die.DIEService.FileRestoreSize',
    index=6,
    containing_service=None,
    input_type=_FILERESTORESIZEREQUEST,
    output_type=_REPLYHEADER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DIESERVICE)

DESCRIPTOR.services_by_name['DIEService'] = _DIESERVICE

# @@protoc_insertion_point(module_scope)
