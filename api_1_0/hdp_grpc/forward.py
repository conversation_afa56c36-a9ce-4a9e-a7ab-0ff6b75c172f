#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import grpc

from api_1_0.hdp_grpc.die import grpc_newssnlog_switch
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator
from config.config import GrpcCfg
from flask import request
from config.config import NdrLog
from utils import param_check
import protos_pb.forward_pb2 as forward_pb2
import protos_pb.forward_pb2_grpc as forward_pb2_grpc

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


# Grpc API #
def grpc_get_port_stats(port):
    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = forward_pb2_grpc.ForwardStub(channel)
        response = stub.GetPortStats(forward_pb2.PortStatsRequest(port=port))

        return response


# Rest API #
class GrpcForword(Resource):
    @param_check.check_flask_args(Validator('grpc_get_stats_schema'), request)
    def get(self, **kwargs):
        try:
            response = grpc_get_port_stats(int(kwargs['port']))
            if not response:
                return flask_response('Get port %s stats failed.' % kwargs['port'], False, {})

            data = {
                'in_packets': response.in_packets,
                'in_bytes': response.in_bytes,
                'out_packets': response.out_packets,
                'out_bytes': response.out_bytes,
            }

            return flask_response('', True, data)
        except Exception as e:
            print('Grpc get port %s failed. Reason: %s' % (kwargs['port'], str(e)), flush=True)
            return flask_response('Get port %s stats failed.' % kwargs['port'], False, {})
