import React, { useEffect, useState } from 'react';
import { Form, Card, Input, Button, message, Select, Tree, Spin } from 'antd';
import style from './style.less';
import { Base64 } from 'js-base64';
import { getFTPDir, getFTPTree, openConnect, getFTPstatus } from '@/services/explore';
import { ellipsis } from '@/utils/utils';

const { Option } = Select;
const { TreeNode } = Tree;

const FTP = (props: any) => {
  const { getDownLoadDir, getChooseFile } = props;
  const [form] = Form.useForm();
  const [loading, setloading] = useState(false);
  const [dirList, setdirList] = useState([]);
  const [treeData, settreeData] = useState([]);
  const [initialValue, setInitialValue] = useState({
    host: '',
    port: '21',
    username: '',
    password: '',
  });
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 5 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 12 },
    },
  };
  const getDir = () => {
    getFTPDir().then(res => {
      if (res.flag) {
        setdirList(res.data.detail);
      } else {
        message.error(res.message);
      }
    });
  };
  const getTree = () => {
    setloading(true);
    getFTPTree().then(res => {
      if (res.flag) {
        settreeData(res.data);
        setloading(false);
      } else {
        message.error(res.message);
      }
    });
  };

  const onCheck = (checkedKeys: any, e: any) => {
    let isTrue = true;
    if (e.checkedNodes) {
      for (let item of e.checkedNodes) {
        if (item.size.charAt(item.size.length - 1) === 'G') {
          let size = item.size.substring(0, item.size.length - 1);
          size = parseFloat(size);
          if (size >= 2.0) {
            isTrue = false;
            message.error('文件大小不能超过2G');
            break;
          }
        }
      }
      if (!isTrue) {
        return;
      }
      getChooseFile(checkedKeys);
    }
  };
  // const ftpCheckedKeys = () => {};
  const handleConnect = () => {
    form.validateFields().then((values: any) => {
      values.port = parseInt(values.port);
      if (initialValue.password !== values.password) {
        values.password = Base64.encode(values.password);
      }
      openConnect(values).then(res => {
        if (res.flag) {
          getTree();
          getDir();
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const changeDir = (value: any) => {
    getDownLoadDir(value);
  };
  const renderTree = (data: any) => {
    return data.map((item: any) => {
      const arr = item.fileName.split('/');
      const title = arr[arr.length - 1];
      if (item.children) {
        return (
          <TreeNode title={title} disabled={!item.downloadable} key={item.fileName} dataRef={item}>
            {renderTree(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          disabled={!item.downloadable}
          title={
            <div>
              {ellipsis(title, 25)} {' ' + item.size}
            </div>
          }
          key={item.fileName}
          {...item}
        />
      );
    });
  };
  const getStatus = () => {
    getFTPstatus().then(res => {
      if (res.flag) {
        if (JSON.stringify(res.data) !== '{}') {
          const ftpMsg = res.data;
          let data = {
            host: ftpMsg.host,
            port: ftpMsg.port,
            username: ftpMsg.username,
            password: ftpMsg.password,
          };
          form.setFieldsValue(data);
          setInitialValue(data);
        }
      } else {
        message.error(res.message);
      }
    });
  };
  useEffect(() => {
    getStatus();
  }, []);

  return (
    <div>
      <Form form={form} {...formItemLayout} className={style.ftpBox}>
        <Card style={{ width: '50%' }}>
          <Form.Item
            label="IP"
            name="host"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input placeholder="请输入IP" />
          </Form.Item>
          <Form.Item
            label="PORT"
            name="port"
            initialValue="21"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input placeholder="请输入端口" />
          </Form.Item>
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item label="密码" name="password">
            <Input type="password" placeholder="请输入密码" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={handleConnect} style={{ marginLeft: '50px' }}>
              连接
            </Button>
          </Form.Item>
        </Card>
        <Card style={{ width: '50%' }}>
          <Form.Item label="上传目录" name="dir">
            <Select onChange={changeDir}>
              {dirList.map((item: any) => {
                return (
                  <Option key={item.dirName} value={item.dirName}>
                    {item.dirName}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <div className={style.catalogBox}>
            <p>2G以上的数据包无法上传</p>
            <Spin spinning={loading}>
              <Tree checkable showLine onCheck={onCheck} selectable={false}>
                {renderTree(treeData)}
              </Tree>
            </Spin>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default FTP;
