# NDR系统整体架构说明

[TOC]

* 组件说明 | [DONE]
* 组件的架构图 | [DONE]
* 组件的安装说明  |  [oth]
* 组件封装为功能模块的架构图  | [DONE]
* 功能模块的说明 | [DONE]
* 功能模块对外提供的API说明 |  to conf & conf 2 jira
* 每个API的详细数据结构 
* 业务场景来说明如何调用一组API来完成实验局工作

<br/>

| 版本号 | 日期      | 作者 |
| ------ | --------- | ---- |
| v0.1   | 2019/5/18 | 李林 |

<br/>

------------------------------------------

## 0x0 基础组件说明

### 0x01 概述
* 每个组件的详细说明（组件提供什么样的功能？解决什么问题？）
* 组件的输入是什么
* 组件的输出是什么
* 该组件是否对外暴露服务（我们是否使用了该服务）
* 该组件的配置文件是否有调优（调优的配置文件和逐行解释说明）

### 0x02 PF\_RING

* 组件说明：PF\_RING 是 Luca 研究出来的基于 Linux 内核级的高效数据包捕获技术。简单来说 PF\_RING 是一个高速数据包捕获库。当前主要采用 PF\_RING 实现流量抓取的 ZC（Zero Copy） 模式，提高设备的抓包稳定性，降低丢包率；
* 输入：来自NDR设备的网卡流量；
* 输出：将流量通过ZC模式直接同步到 Suricata 和 Moloch；
* 无服务暴露；
* 配置文件路径：/etc/pf_ring
* 配置文件修改参照「[NDR系统组件安装使用说明文档](NDR系统组件安装使用说明文档.md)」
* 备注：PF\_RING 在当前 NDR 系统替代了 Suricata 和 Moloch 原生的抓包功能。

### 0x03 Suricata
* 组件说明：Suricata 专注于大规模网络部署，免费的开源，成熟，快速和强大的高性能网络威胁监测引擎，并支持 IPS（入侵防御）与 NSM（网络安全监控）模式，用来替代原有的 Snort 入侵监测系统，完全兼容 Snort 规则语法和支持 Lua 脚本。在 NDR 系统中，我们主要使用 Suricata 的 IDS 功能，通过规则来完成流量识别告警，包括识别 0day 和 1day 漏洞以及 APT 攻击；
* 输入：PF\_RING的ZC模式数据同步（通过配置文件对接）；
* 输出：eve.json（备注：eve.json用于 Filebeat 组件读取 ； eve.json 同时用于 Moloch 组件读取）； 
* 无服务暴露；
* 配置文件位置：/etc/suricata/suricata.yaml ；
* 配置文件修改参照「[NDR系统组件安装使用说明文档](NDR系统组件安装使用说明文档.md)」；
* 备注：Suricata 需要通过配置减少无关告警的日志输出（减少系统负载）；
* 备注2：<mark> Suricata 支持 CPU 优化，当前采用默认设置，后期考虑调参实现优化问题；


### 0x04 Filebeat
* 组件说明：filebeat 是一个轻量级的 logstash，当需要收集信息的机器配置或资源并不是特别多时，使用 filebeat 来收集日志。现阶段我们通过 Filebeat 直接实现将来自 Suricata 的 eve.json格式日志直接传递给ES；
* 输入：Suricata 产生的 eve.json；
* 输出：ES；
* 无服务暴露；
* 配置文件位置：/opt/ndr/conf/filebeat.yaml；  <mark> (需要验证)
* 配置文件修改：

```bash
xxx #指定输入 Sruicata 的 eve.json文件路径
xxx #指定输出 ES 索引名称
xxx #指定输出 ES 地址路径
```


### 0x05 Moloch

* 组件说明： Moloch 是一个开源，大规模，完整的数据包捕获，索引和数据库系统。 Moloch 扩展了当前的安全基础架构，以标准PCAP格式存储和索引网络流量，提供快速的索引访问。为 PCAP 浏览，搜索和导出提供了直观简单的Web界面。 Moloch公开了 API，允许直接下载和使用 PCAP 数据及 JSON 格式的会话数据。 Moloch 以标准 PCAP 格式存储和导出所有数据包，可用 Wireshark 分析。当前系统中需要基于 Moloch 实现攻击会话流的索引创建，以及基于索引的 PCAP 文件导出；
* 输入：PF\_RING的ZC模式数据同步（通过配置文件对接）；
* 输出1：部分协议元数据发送到 ES ；
* 输出2：全流量的 Pcap 文件存储到本地硬盘；
* 有服务暴露：默认提供 Web 和 API 接口服务，端口号：8005 ；
* 配置文件位置：/data/moloch/etc/config.ini
* 配置文件修改：

```bash
pcapDir = /data/moloch/raw #pcap文件夹
maxFileSizeG = 12  #pcap数据包最大的大小，单位G
freeSpaceG = 5% #允许磁盘的最大剩余空间，如果超过，则删除pcap数据包
viewHost = localhost #设置web接口监听的host
spiDataMaxIndices=8 #spi数据查询的最远日期
dropUser=ndr;  dropGroup=ndr #用户和组具有pcapDir的访问权限，最好设置与pcapDir相同用户
packetThreads=2 # moloch处理数据的线程数
  
```
* 其他参数列表： <https://github.com/aol/moloch/wiki/Settings>


### 0x06 ElasticSearch

* 组件说明： ElasticSearch是一个基于Lucene的搜索服务器。它提供了一个分布式多用户能力的全文搜索引擎，基于RESTful web接口。在当前 NDR 系统中主要存储告警日志和部分Moloch 数据包的元数据；
* 输入：来自 Filebeat 、Moloch 主动提交的日志类数据；
* 输出：Nginx 业务层代码；
* 有服务暴露：默认提供 Web 和 API 接口服务，端口号：9200 ；
* 配置文件位置：/opt/ndr/conf/es.yaml；
* 配置文件修改：<mark> 后期考虑调参实现优化问题。

### 0x07 MongoDB

* 组件说明： MongoDB作为 NDR 系统的业务层数据库；
* 输入1：Nginx 业务层代码；
* 输入2：后期可能后台组件或 Deamon 进程主动写入状态等信息到 MongoDB ；
* 输出：Nginx 业务层代码；
* 有服务暴露：默认提供 Web 和 API 接口服务，端口号：9200 ；
* 配置文件位置：/opt/ndr/conf/mongo.conf；
* 配置文件修改：<mark> 后期考虑调参实现优化问题。


<br/>

------------------------------------------

## 0x01 NDR系统组件数据流图

![NDR系统组件数据流图](./NDR系统组件数据流图.png)

网络出口流量经过 NDR 设备底层，通过 PF\_RING ZC 技术，经过规则引擎 Suricata，触发安全事件告警，同时另一方面经过 Moloch 进行全包留存。最后告警日志经 Filebeat 采集入 ES，Moloch 解析的数据包部分元数据索引日志入 ES。

<br/>

------------------------------------------

## 0x02 系统组件安装说明

* 详见「[NDR系统组件安装使用说明文档](NDR系统组件安装使用说明文档.md)」

<br/>

------------------------------------------


## 0x03 NDR系统功能模块图

![NDR系统功能模块图](./NDR系统功能模块图.png)

* 「用户管理」功能依据当前实验局场景，只实现最基本的登入、登出、及认证功能
* 「BackIDS」功能模块封装了 Suricata 相关组件的底层实现
* 「BackStream」功能模块封装了 Moloch 相关组件的底层实现
* 「系统管理」功能 BackIDS、BackStream底层组件的维护入口，以及系统网卡基础配置入口、日志导出等

<br/>

------------------------------------------

## 0x04 NDR系统「用户管理」功能模块说明

* 用户登录
* 用户登出
* 用户密码修改

<br/>

------------------------------------------

## 0x05 NDR系统「BackIDS」功能模块说明

### 0x0501 规则管理

  * 概述：确定规则的数据结构，对当前规则和未来生产规则进行人工分类，依赖 killchains ，入库，上线规则的增删改查，允许或者禁止某些规则生效；
  * 监测规则汇总查询：当前NDR系统加载的规则总数、规则的启用条目数、规则的禁用条目数量；
  * 查询失效的监测规则：查询当前失效的监测规则详情列表；
  * 监测规则详情查询：跟进SID查询指定的监测规则详细信息；
  * 监测规则添加：管理员单条手工添加监测规则；
  * 监测规则批量添加：管理员批量导入文件形式的监测规则列表；<mark>（导入文件为固定的数据结构格式）；
  * 监测规则修改（更新）：更新指定SID的监测规则（主要更新：规则体、杀伤链标签、威胁评级、威胁分类）；
  * 监测规则删除：删除指定SID的监测规则；
  * 立即启用监测规则：启用指定SID的监测规则；
  * 定时启用监测规则：指定时间点生效监测规则；
  * 监测规则禁用：禁用指定SID的监测规则； 

### 0x0502 安全事件查询

  * 概述：根据当前系统告警事件的发生，根据用户的条件进行事件查询，例如需要查询某个时间段关于某个源 IP 的安全事件等基本的查询；
  *  根据不同条件查询安全事件：开始时间、结束时间、应用层协议、源 IP 地址、目的 IP 地址、源端口号、目的端口号、查询数量。（例如：startTime=1557475221&stopTime=1557907236&count=20&appProto=ftp&dstIp=*************&srcIp&srcPort&dstPort）；


### 0x0503 安全事件聚合查询

  * 概述：根据当前系统过去某个时间段的安全事件的统计聚合分析，例如需要查看某个时间段 Weblogic 这个安全事件发生的Top 20 的 IP
  * 根据不同条件聚合安全事件：keyword 取值： eventName（按照攻击事件聚合）、sid（按照攻击规则聚合）、srcIp（按照攻击源 IP ）、dstIp（按照攻击目的 IP），keyword=eventName&topNum=20&startTime=1557475221&stopTime=1557907236


<br/>

------------------------------------------


## 0x06 NDR系统「BackStream」功能模块说明

### 0x0601 全流量查询
  * 概述：根据源IP、目的IP、源端口、目的端口、协议、flowId、起止时间获取数据包	
  * 根据不同条件（srcIp=&dstIp=&srcPort=&dstPort=&appProto=&flowId=&startTime=&stopTime=）

备注：如果根据告警事件中的 FlowID 查询全流量，就可以获取告警事件的会话还原


### 0x0602 统计当前 IP 的通联情况

  * 概述：根据 IP、起止时间获取 IP 通联情况 	
  * 根据不同条件（start=&num=&startTime=&stopTime=&ip=）

### 0x0603 查询当前流量中的会话概要
  * 概述：统计当前协议、当前流量中前十的源IP、目的IP、当前流量域名的会话概要
  * 根据不同条件 spi=&startTime=&stopTime=&num= （spi 取值可选  srcIp, dstIp, protocol, dns.host）

<br/>

------------------------------------------

## 0x07 NDR系统「系统管理」功能模块说明

### 0x0701 系统管理
* 关闭系统
* 重启系统
### 0x0702 系统组件管理
* 查看系统各组件 （suricata、moloch）状态
* 启动、停止、重启所有组件
* 启动、停止、重启某组件

  



















 
