# -*- coding: utf-8 -*-
# @Time    : 2019-07-18 15:30
# <AUTHOR> Shark
# @File    : top_threat_score.py
# @Software: PyCharm
from datetime import datetime
from flask import request
from flask_restful import Resource
from utils.utils import flask_response
from utils import param_check, database
from utils.param_check import Validator
from utils.mongo_template.get_mongo_template import MongoTemplate
from utils.logger import get_ndr_logger

MONGO_LOG = get_ndr_logger('mongo_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class TopScore(Resource):
    """按漏洞触发次数获取漏洞列表"""

    def __init__(self):
        """初始化参数"""
        # 初始化 ES 查询模板
        self.mongo_template = MongoTemplate().read_template('top_score')
        # 初始化 ES client
        self.mongo_client = database.MongoDB("ndr")

    @param_check.check_flask_args(Validator("top_score"), request)
    def get(self, **kwargs):
        """处理get请求"""

        # 参数赋值并做格式转换
        args = {
            "page_size": int(kwargs["args"]["pageSize"]),
            "start_time": int(kwargs["args"]["startTime"]),
            "stop_time": int(kwargs["args"]["stopTime"])
        }

        if args["start_time"] >= args["stop_time"]:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})

        self.format_mongo_template(args)

        try:
            res = self.mongo_client.aggs(collection="event_outline_minutes", pipeline=self.mongo_template)
        except Exception as e:
            err_info = "error: " + str(e)
            MONGO_LOG.error('params[{0}],reason[{1}]'.format(self.mongo_template, str(e)))
            return flask_response(err_info, True, [])
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, True, [])

    def format_mongo_template(self, args):
        """通过传入参数填充ES查询聚合语句"""

        self.mongo_template[0]["$match"]["$and"][0]["lastOccurredAtTimestamp"]["$gte"] = args["start_time"]
        self.mongo_template[0]["$match"]["$and"][1]["firstOccurredAtTimestamp"]["$lte"] = args["stop_time"]
        self.mongo_template[1]["$match"]["occurredAt"]["$elemMatch"]["$gte"] = args["start_time"]
        self.mongo_template[1]["$match"]["occurredAt"]["$elemMatch"]["$lte"] = args["stop_time"]
        self.mongo_template[4]["$limit"] = args["page_size"]

    @staticmethod
    def data_format(data):
        """将es输出结果格式化成前端结构"""

        res_list = []

        for res_one in data:
            res_one.pop("_id")
            res_list.append(res_one)

        return res_list
