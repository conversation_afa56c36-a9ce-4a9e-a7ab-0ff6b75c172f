#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: test_rulesListResource.py
# @time: 2019/07/10
# @software: PyCharm
import json
import random
import unittest
from api_1_0.router import app, api
from api_1_0.back_rules.rules_content_manager import RulesListResource
from utils.database import MongoDB


class TestRulesListResource(unittest.TestCase):

    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        self.query_string = {
            "threatScore": 80,
            "author": "Xiaofu",
            "vulName": "Drupalgeddon2 <8.3.9 <8.4.6 <8.5.1 代码执行漏洞",
            "vulType": "代码执行",
            "cve": "CVE-2018-7600",
            "lockheedKillchainCN": "载荷投递",
            "lockheedKillchainStage": 3,
            "lockheedKillchainEN": "Delivery",
            "threatFlag": "Exploits and Attacks",
            "alterInfo": "",
            "submitTime": "2019-06-18",
            "is0day": 1,
            "ruleContent": "alert http any any -> any any (msg: \"ATTACK [PTsecurity] Drupalgeddon2 <8.3.9 <8.4.6 <8.5.1 RCE through registration form (CVE-2018-7600)\"; flow: established, to_client; flowbits:isset,30000014; content: \"200\";http_stat_code; content:\"|22|command|22 3A 22|insert|22|\"; http_server_body; content:\"|22|data|22|\"; reference: cve, 2018-7600; reference: url, research.checkpoint.com/uncovering-drupalgeddon-2; classtype: attempted-admin; reference: url, github.com/ptresearch/AttackDetection; metadata: Open Ptsecurity.com ruleset; sid: 30000020; rev: 2; )",
            "attackIp": "src_ip",
            "victimIp": "dst_ip",
            "sid": "30000020",
            "appProto": "http"
        }

    def tearDown(self):
        pass

    def test_get(self):
        resp_dict = self.get(RulesListResource)
        self.assertEqual(resp_dict["flag"], True)

    # 缺少必需字段
    def test_lack_of_required_parameters(self):
        required_list = ["lockheedKillchainEN", "ruleContent", "threatFlag", "threatScore", "sid", "attackIp",
                         "victimIp", "appProto", "lockheedKillchainCN"]
        self.query_string.pop(random.sample(required_list, 1)[0])
        resp_dict = self.post(RulesListResource, self.query_string)
        self.assertEqual(resp_dict["flag"], False)

    # 非法 threatScore
    def test_post_illegal_of_threatScore(self):
        illegal_score = [-1, -1.5, 101, 150.5]
        for score in illegal_score:
            self.query_string["threatScore"] = score
            resp_dict = self.post(RulesListResource, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    # 合法 threatScore
    def test_post_legal_of_threatScore(self):
        db = MongoDB("ndr")
        db.drop("rules")
        legal_score = [0, 10, 80, 100]
        for score in legal_score:
            self.query_string["threatScore"] = score
            resp_dict = self.post(RulesListResource, self.query_string)
            db.drop("rules")
            self.assertEqual(resp_dict["flag"], True)

    # 添加已存在 sid
    def test_post_exist_of_sid(self):
        db = MongoDB("ndr")
        db.drop("rules")
        self.post(RulesListResource, self.query_string)
        resp_dict = self.post(RulesListResource, self.query_string)
        self.assertEqual(resp_dict["flag"], False)

    # 非法的 appProto
    def test_post_illegal_of_appProto(self):
        illegal_list = ["", "10010", "##$$./", "sadasdasd", "  ", "   sss"]
        for line in illegal_list:
            self.query_string["appProto"] = line
            resp_dict = self.post(RulesListResource, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    # 合法的 appProto
    def test_post_legal_of_appProto(self):
        db = MongoDB("ndr")
        db.drop("rules")
        legal_list = ["tcp", "http", "ftp", "ssh", "tls", "smb", "dns", "udp", "icmp", "smtp"]
        for line in legal_list:
            self.query_string["appProto"] = line
            resp_dict = self.post(RulesListResource, self.query_string)
            db.drop("rules")
            self.assertEqual(resp_dict["flag"], True)

    # 非法的 ruleContent
    def test_post_illegal_of_ruleContent(self):
        db = MongoDB("ndr")
        db.drop("rules")
        illegal_list = ["", "10010", "##$$./", "sadasdasd", "  ", "   sss"]
        for line in illegal_list:
            self.query_string["ruleContent"] = line
            resp_dict = self.post(RulesListResource, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    # 非法 author
    def test_post_illegal_of_author(self):
        db = MongoDB("ndr")
        db.drop("rules")
        illegal_list = ["##$$./", "sadasdasd_/&*", "  ", "   ss", "123456789012345678901"]
        for line in illegal_list:
            self.query_string["author"] = line
            resp_dict = self.post(RulesListResource, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    # 非法 sid
    def test_post_illegal_of_sid(self):
        db = MongoDB("ndr")
        db.drop("rules")
        illegal_list = ["-1", "4294967297", "  ", "   ss", "sadasda", "123456789012345678901", '0']
        for line in illegal_list:
            self.query_string["sid"] = line
            resp_dict = self.post(RulesListResource, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    # 非法 submitTime
    def test_post_illegal_of_submitTime(self):
        db = MongoDB("ndr")
        db.drop("rules")
        illegal_list = ["##$$./", "2019-0-1", "", "2019-10-1", "2019-10-34", "2019-10-21 ", " 2019-10-21",
                        "2019 -10-21"]
        for line in illegal_list:
            self.query_string["submitTime"] = line
            resp_dict = self.post(RulesListResource, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    # 非法 cve
    def test_post_illegal_of_sid(self):
        db = MongoDB("ndr")
        db.drop("rules")
        illegal_list = ["##$$./", "cve-2019-0-1", "", "cve-20194-10111", "cve-2019-1234567", "CVE-2019-1234567",
                        "cve-2019-123456 "]
        for line in illegal_list:
            self.query_string["cve"] = line
            resp_dict = self.post(RulesListResource, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    def post(self, url, data):
        response = self.client.post(api.url_for(url), json=data)
        resp_dict = json.loads(response.data)
        return resp_dict

    def get(self, url):
        response = self.client.get(api.url_for(url))
        resp_dict = json.loads(response.data)
        return resp_dict


if __name__ == '__main__':
    suite = unittest.TestSuite()
    suite.addTest(TestRulesListResource.test_get())
    # 执行测试
    runner = unittest.TextTestRunner()
    runner.run(suite)
