import React, { useRef, useState } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  Button,
  Modal,
  Pagination,
  Popover,
  Table,
} from 'antd';
import moment from 'moment';

import CreateReportForm from './createReportForm';
import { ReportContent } from './reportContentSelector';
import { reportStatus } from '@/utils/enumList';
import { ReportListItem, useThreatReportList } from './hooks';

const { confirm } = Modal;

const defaultCheckedReportContent: ReportContent = {
  overview: true,
  suggest: true,
  stat: true,
  high_risk: [1, 2, 3, 4],
  risk: [1, 2, 3, 4],
};

const defaultCreateFromValues = {
  name: '',
  timeRange: null,
  type: [],
  reportContent: defaultCheckedReportContent,
  waterMark: '',
  customCompany: ''
};

const Index = () => {
  const formRef = useRef<any | null>();
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [formValue, setformValue] = useState(defaultCreateFromValues);

  const [selectedRowKeys, setselectedRowKeys] = useState([]);

  const {
    data,
    loading,
    pagination: paginationData,
    total,
    createListData,
    changePage,
    setSorter,
    deleteListData,
    downloadReportZip,
  } = useThreatReportList();

  const pageHeight = window.innerHeight; // 获取页面高度

  const downloadContentRender = (report: Record<string, string>) => {
    const reportDownloadData = Object.entries(report);
    return (
      <div className="flex justify-center">
        {
          reportDownloadData.map(([type, name]) => (
            <Button
              key={name}
              type="link"
              onClick={() => downloadReportZip(name)}
            >
              {type}
            </Button>
          ))
        }
      </div>
    )

  }

  const groupColumns = [
    {
      title: '生成时间',
      width: 180,
      dataIndex: 'timestamp',
      sorter: (a: any, b: any) => moment(a.createTime).unix() - moment(b.createTime).unix(),
      render: (t: string) => {
        return moment(t).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '报告名称',
      width: 150,
      dataIndex: 'name',
    },
    {
      title: '报告类型',
      width: 120,
      dataIndex: 'report_type',
    },
    {
      title: '报告周期',
      dataIndex: 'period',
      width: 100,
    },
    {
      title: '生成方式',
      dataIndex: 'gen_method',
      width: 100,
    },
    {
      title: '时间范围',
      key: 'timeRange',
      width: 350,
      render: (_: any, record: any) => {
        const { start_time, stop_time } = record
        return <>
          {moment.unix(start_time).format('YYYY-MM-DD HH:mm:ss')} 至 {moment.unix(stop_time).format('YYYY-MM-DD HH:mm:ss')}
        </>;
      },
    },
    {
      title: '创建人',
      width: 120,
      dataIndex: 'user',
      render: (t: string) => {
        return t || '--'
      },
    },
    {
      title: '状态',
      width: 80,
      dataIndex: 'status',
      render: (t: string) => {
        return reportStatus[t];
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 180,
      render: (_: any, { _id, report }: ReportListItem) => {
        return (
          <>
            <Popover
              content={downloadContentRender(report)}
              title="选择报告格式"
              trigger="focus"
            >
              <Button
                type="link"
              >
                下载报告
              </Button>
            </Popover>
            <Button
              type="link"
              danger
              onClick={() => showDeleteConfirm(_id)}
            >
              删除
            </Button>
          </>
        );
      },
    },
  ];

  const rowSelection = {
    fixed: true,
    columnWidth: 60,
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    getCheckboxProps(record: any) {
      return {
        disabled: record.featureStatus === 'deprecated',
      };
    },
    selectedRowKeys,
  };

  const pagination = {
    total: total,
    showSizeChanger: true,
    showQuickJumper: true,
    current: paginationData.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: paginationData.pageSize,
    onChange: changePage,
  };

  const handleCreate = () => {
    // todo handle create report
    formRef.current?.validateFields()
      .then((values: any) => {
        const {
          timeRange,
          reportContent,
          ...restValues
        } = values;

        const start_time = timeRange?.[0].unix();
        const stop_time = timeRange?.[1].unix();

        const content = Object.keys(reportContent).filter(key => {
          return reportContent[key];
        })

        const { high_risk, risk } = reportContent;

        const payload = {
          ...restValues,
          start_time,
          stop_time,
          content,
          high_risk,
          risk,
        };

        setConfirmLoading(true);
        return createListData(payload);

      }).then(() => {
        closeCreateModal();
        setConfirmLoading(false);
      })
      .catch(() => { })
  }

  const openCreateModal = () => {
    setformValue(defaultCreateFromValues);
    setVisible(true);
  }

  const closeCreateModal = () => {
    setVisible(false);
  }

  const showDeleteConfirm = (id?: string) => {
    confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除吗？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        if (id) {
          return deleteListData([id]);
        }
        return deleteListData(selectedRowKeys);
      },
    });
  };

  const onTableChange = (_: any, __: any, sorter: any) => {
    const { order } = sorter;
    if (!order) {
      setSorter('desc');
      return;
    }
    setSorter(order.replace(/end$/, ""));
  }

  return (
    <div>
      <div className="mt-2 flex justify-between items-center">
        <div className='flex gap-3'>
          <Button type="primary" onClick={openCreateModal}>
            新建报告
          </Button>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            danger
            onClick={() => showDeleteConfirm()}
          >
            删除
          </Button>
          {
            !!selectedRowKeys.length && (
              <span className='flex items-center'>
                已经选择 {selectedRowKeys.length} 条数据
              </span>
            )
          }
        </div>
        <Pagination {...pagination} />
      </div>
      <Table
        loading={loading}
        className="mt-4"
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        rowKey="_id"
        scroll={{ x: 1200, y: pageHeight - 224 }}
        pagination={pagination}
        dataSource={data}
        columns={groupColumns}
        onChange={onTableChange}
      />
      <Modal
        destroyOnClose={true}
        title="新建报告"
        visible={visible}
        centered
        width={800}
        onOk={handleCreate}
        onCancel={closeCreateModal}
        confirmLoading={confirmLoading}
      >
        <CreateReportForm
          ref={formRef}
          value={formValue}
        />
      </Modal>
    </div>
  );
};
export default Index;

