#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: test_eventGroupByResource.py
# @time: 2019/08/15
# @software: PyCharm
import json
import random
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_event.get_event_list import EventGroupByResource


class TestEventGroupByResource(TestCase):
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_get_1_start_time_error(self):
        startTime = 1
        stopTime = 2
        groupBy = ["threatFlag", "threatLevel", "killchains", "country", "ip"]
        response = self.client.get(
            api.url_for(EventGroupByResource, startTime=startTime, stopTime=stopTime, groupBy=random.choice(groupBy)))
        resp_dict = json.loads(response.data)
        self.assertEqual(resp_dict["flag"], False)

    def test_get_start_time_gt_stop_time(self):
        startTime = 1578430872367
        stopTime = 1504527700099
        groupBy = ["threatFlag", "threatLevel", "killchains", "country", "ip"]
        response = self.client.get(
            api.url_for(EventGroupByResource, startTime=startTime, stopTime=stopTime, groupBy=random.choice(groupBy)))
        resp_dict = json.loads(response.data)
        self.assertEqual(resp_dict["flag"], False)

    def test_get_start_time_over_now_time(self):
        startTime = 1597480532000
        stopTime = 1697480532000
        groupBy = ["threatFlag", "threatLevel", "killchains", "country", "ip"]
        response = self.client.get(
            api.url_for(EventGroupByResource, startTime=startTime, stopTime=stopTime, groupBy=random.choice(groupBy)))
        resp_dict = json.loads(response.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)
