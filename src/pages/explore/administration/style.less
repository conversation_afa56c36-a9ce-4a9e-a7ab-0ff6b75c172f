.searchBtn {
  margin-top: 4px !important;
}

.dirName {
  display: flex;
  align-items: center;
  margin: 4px 0 10px 0;
}

.pcapUploadBtn {
  color: #003793;
  cursor: pointer;
}

.pcapLabel {
  position: relative;

  .pcapInput {
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 1px;
    visibility: hidden;
  }
}

.update_box {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}

.isConnect {
  color: rgb(82, 196, 26);
}

.isBreak {
  color: rgb(245, 34, 45);
}

.ftpBox {
  display: flex;
}

.catalogBox {
  height: 400px;
  padding-top: 20px;
  overflow: auto;
}

.handle {
  padding: 20px;
}

.goBackBtn {
  margin: 0 10px;
}