import React, { useState, useEffect } from 'react';
import { message, Table, Button, Modal, Input } from 'antd';
import { getQueryVariable } from '@/utils/utils';
import { getOffLineDetail, ediltTag, delOffLineDetail } from '@/services/explore';

import style from './style.less';
import { useHistory } from '@/hooks/global';
import moment from 'moment';
const { confirm } = Modal;
const Detail = () => {
  const history = useHistory();
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox');
  const [total, settotal] = useState(0);
  const [params, setparams] = useState({ page: 1, pageSize: 10, dir: '' });
  const [tableData, settableData] = useState([]);
  const [isModalVisible, setisModalVisible] = useState(false);
  const [inputValue, setinputValue] = useState('');
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const [fileName, setfileName] = useState('');
  const columns = [
    {
      title: '文件名称',
      dataIndex: 'fileName',
    },
    {
      title: '文件大小',
      dataIndex: 'humanSize',
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      render: (t: any) => {
        return moment(t).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '用户',
      dataIndex: 'createByUserName',
    },
    {
      title: '来源',
      dataIndex: 'source',
      render: (t: string) => (t === 'upload' ? '本地上传' : 'FTP服务器下载'),
    },
    {
      title: '文件tag',
      dataIndex: 'tag',
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: string, record: object) => {
        return (
          <Button
            type="link"
            onClick={() => {
              handleEdit(record);
            }}
          >
            编辑Tag
          </Button>
        );
      },
    },
  ];
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: params.pageSize,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const rowSelection = {
    onChange: (selectedRowKeys: any) => {
      setselectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys,
  };
  useEffect(() => {
    if (getQueryVariable('dir')) {
      params.dir = decodeURI(getQueryVariable('dir'));
      setparams(params);
    }
  }, []);

  useEffect(() => {
    getList();
  }, [params]);

  const getList = () => {
    getOffLineDetail(params).then(res => {
      if (res.flag) {
        settableData(res.data.detail);
        setselectedRowKeys([]);
        settotal(res.data.count);
      } else {
        message.error(res.message);
      }
    });
  };

  const handleEdit = (value: any) => {
    setisModalVisible(true);
    setinputValue('');
    setfileName(value.fileName);
  };

  const handleOk = () => {
    ediltTag({
      fileName: fileName,
      tag: inputValue,
      dir: decodeURI(getQueryVariable('dir')),
    }).then(res => {
      if (res.flag) {
        message.success('操作成功');
        setisModalVisible(false);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleCancel = () => {
    setinputValue('');
    setisModalVisible(false);
  };
  const handleDelete = () => {
    if (!selectedRowKeys || !selectedRowKeys.length) {
      return message.error('请选择数据');
    }
    confirm({
      title: '确定删除所选离线数据包?',
      okType: 'danger',
      onOk() {
        let params = {
          all: '0',
          dir: decodeURI(getQueryVariable('dir')),
          fileList: [],
        };
        selectedRowKeys.forEach((item: any) => {
          params.fileList.push({ fileName: tableData[item].fileName });
        });
        delOffLineDetail(params).then(res => {
          if (res.flag) {
            message.success('操作成功');
            getList();
          } else {
            message.error(res.message);
          }
        });
      },
    });
  };
  const changeInput = (e: any) => {
    setinputValue(e.target.value);
  };
  const deleteAll = () => {
    confirm({
      title: '确定删除所有离线数据包?',
      okType: 'danger',
      onOk() {
        let _parmas = {
          all: '1',
          dir: decodeURI(getQueryVariable('dir')),
          fileList: [],
        };
        delOffLineDetail(_parmas).then(res => {
          if (res.flag) {
            message.success('操作成功');
            getList();
          } else {
            message.error(res.message);
          }
        });
      },
    });
  };
  return (
    <div>
       <div className={style.handle}>
        <Button type="primary" onClick={handleDelete}>
          删除
        </Button>
        <Button
          className={style.goBackBtn}
          type="primary"
          onClick={() => {
            history.go(-1);
          }}
        >
          返回
        </Button>
        <Button type="primary" onClick={deleteAll}>
          全部删除
        </Button>
      </div>
      <Table
        rowSelection={{
          type: selectionType,
          ...rowSelection,
        }}
        rowKey={(r, i) => i}
        pagination={pagination}
        dataSource={tableData}
        columns={columns}
      ></Table>
      <Modal title="编辑tag" visible={isModalVisible} onOk={handleOk} onCancel={handleCancel}>
        <Input onChange={changeInput} value={inputValue} />
      </Modal>
    </div>
  );
};
export default Detail;
