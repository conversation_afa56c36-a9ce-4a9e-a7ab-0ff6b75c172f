import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  DatePicker,
  Card,
  Row,
  Col,
  message,
  Checkbox,
  InputNumber,
  Radio,
  Typography,
} from 'antd';
import moment from 'moment';
import { createAnomalyTask } from '@/services/anomaly';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Text } = Typography;

interface CreateTaskModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [taskType, setTaskType] = useState<string>('scheduled');

  const handleCancel = () => {
    form.resetFields();
    setTaskType('scheduled');
    onCancel();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 构建任务数据
      const taskData: any = {
        name: values.name,
        description: values.description || '',
        model_types: values.model_types,
        target_type: values.target_type,
        target_value: values.target_value,
        task_type: values.task_type,
        alert_config: {
          enabled: values.alert_enabled || false,
          severity_threshold: values.severity_threshold || 'medium',
        },
      };

      // 根据任务类型添加时间配置
      if (values.task_type === 'historical') {
        // 历史回溯任务
        const [startTime, endTime] = values.time_range || [];
        taskData.start_time = startTime?.valueOf();
        taskData.end_time = endTime?.valueOf();
        taskData.time_window_hours = values.time_window_hours || 1;
      } else {
        // 定时任务
        taskData.schedule_type = values.schedule_type || 'daily';
        taskData.interval_hours = values.interval_hours || 24;
        taskData.time_range_hours = values.time_range_hours || 1;
        
        if (values.schedule_start_date) {
          taskData.start_date = values.schedule_start_date.valueOf();
        }
        if (values.schedule_end_date) {
          taskData.end_date = values.schedule_end_date.valueOf();
        }
        
        taskData.enabled_days = values.enabled_days || [1,2,3,4,5,6,7];
      }

      const response = await createAnomalyTask(taskData);
      if (response.flag) {
        onSuccess();
        message.success('任务创建成功');
      } else {
        message.error(response.message || '创建失败');
      }
    } catch (error) {
      message.error('创建任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 模型选项
  const modelOptions = [
    { label: '📈 时间序列趋势', value: 'time_series' },
    { label: '📊 基线偏移检测', value: 'baseline' },
    { label: '🔁 重复连接检测', value: 'repeat_conn' },
    { label: '⛓️ 长连接分析', value: 'long_conn' },
    { label: '📍 地理/归属异常', value: 'geo_anomaly' },
  ];

  return (
    <Modal
      title="创建异常检测任务"
      visible={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          model_types: ['time_series', 'baseline', 'repeat_conn', 'long_conn', 'geo_anomaly'],
          task_type: 'scheduled',
          target_type: 'ip',
          alert_enabled: true,
          severity_threshold: 'medium',
          schedule_type: 'daily',
          interval_hours: 24,
          time_range_hours: 1,
          time_window_hours: 1,
          enabled_days: [1,2,3,4,5,6,7],
        }}
      >
        {/* 基本信息 */}
        <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="任务名称"
                rules={[{ required: true, message: '请输入任务名称' }]}
              >
                <Input placeholder="请输入任务名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="description" label="任务描述">
                <Input placeholder="请输入任务描述" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 检测配置 */}
        <Card title="检测配置" size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="model_types"
            label="检测模型（支持多选）"
            rules={[{ required: true, message: '请选择至少一个检测模型' }]}
          >
            <Checkbox.Group options={modelOptions} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="target_type"
                label="检测目标类型"
                rules={[{ required: true, message: '请选择目标类型' }]}
              >
                <Select>
                  <Option value="ip">IP地址</Option>
                  <Option value="domain">域名</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="target_value"
                label="检测目标值"
                rules={[{ required: true, message: '请输入检测目标值' }]}
              >
                <Input placeholder="请输入IP地址或域名" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 任务类型 */}
        <Card title="任务类型" size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="task_type"
            label="选择任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Radio.Group onChange={(e) => setTaskType(e.target.value)}>
              <Radio value="historical">📅 历史回溯检测</Radio>
              <Radio value="scheduled">⏰ 定时检测任务</Radio>
            </Radio.Group>
          </Form.Item>

          {taskType === 'historical' ? (
            // 历史回溯配置
            <div>
              <Form.Item
                name="time_range"
                label="检测时间范围"
                rules={[{ required: true, message: '请选择检测时间范围' }]}
              >
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder={['开始时间', '结束时间']}
                />
              </Form.Item>
              <Form.Item name="time_window_hours" label="检测窗口（小时）">
                <InputNumber min={1} max={24} />
              </Form.Item>
            </div>
          ) : (
            // 定时任务配置
            <div>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="schedule_type" label="调度类型">
                    <Select>
                      <Option value="daily">每天</Option>
                      <Option value="weekly">每周</Option>
                      <Option value="monthly">每月</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="interval_hours" label="执行间隔（小时）">
                    <InputNumber min={1} max={168} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="time_range_hours" label="检测窗口（小时）">
                    <InputNumber min={1} max={24} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="schedule_start_date" label="开始日期">
                    <DatePicker placeholder="选择开始日期" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="schedule_end_date" label="结束日期（最多一年）">
                    <DatePicker 
                      placeholder="选择结束日期"
                      disabledDate={(current) => {
                        const startDate = form.getFieldValue('schedule_start_date');
                        if (startDate) {
                          return current && current > moment(startDate).add(1, 'year');
                        }
                        return current && current > moment().add(1, 'year');
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="enabled_days" label="启用日期">
                <Checkbox.Group
                  options={[
                    { label: '周一', value: 1 },
                    { label: '周二', value: 2 },
                    { label: '周三', value: 3 },
                    { label: '周四', value: 4 },
                    { label: '周五', value: 5 },
                    { label: '周六', value: 6 },
                    { label: '周日', value: 7 },
                  ]}
                />
              </Form.Item>
            </div>
          )}
        </Card>

        {/* 告警配置 */}
        <Card title="告警配置" size="small">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="alert_enabled" label="启用告警" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="severity_threshold" label="告警阈值">
                <Select>
                  <Option value="low">低危及以上</Option>
                  <Option value="medium">中危及以上</Option>
                  <Option value="high">高危及以上</Option>
                  <Option value="critical">仅严重</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </Modal>
  );
};

export default CreateTaskModal;
