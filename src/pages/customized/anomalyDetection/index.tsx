import React, { useState, useEffect } from 'react';
import { <PERSON>, Tabs, Button, message, Space, Statistic, Row, Col } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { PlusOutlined, AlertOutlined, Bar<PERSON><PERSON>Outlined, SettingOutlined } from '@ant-design/icons';
import TaskManagement from './components/TaskManagement';
import ResultAnalysis from './components/ResultAnalysis';
import AlertCenter from './components/AlertCenter';
import Statistics from './components/Statistics';
import ModelConfigManagement from './components/ModelConfigManagement';
import { getAnomalyStats } from '@/services/anomaly';
import styles from './style.less';

const { TabPane } = Tabs;

const AnomalyDetection: React.FC = () => {
  const [activeTab, setActiveTab] = useState('tasks');
  const [stats, setStats] = useState<any>({});
  const [loading, setLoading] = useState(false);

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await getAnomalyStats();
      if (response.flag) {
        setStats(response.data);
      }
    } catch (error) {
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    // 定时刷新统计数据
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const tabItems = [
    {
      key: 'tasks',
      label: (
        <span>
          <PlusOutlined />
          任务管理
        </span>
      ),
      children: <TaskManagement onStatsChange={fetchStats} />
    },
    {
      key: 'models',
      label: (
        <span>
          <SettingOutlined />
          模型配置
        </span>
      ),
      children: <ModelConfigManagement />
    },
    {
      key: 'results',
      label: (
        <span>
          <BarChartOutlined />
          结果分析
        </span>
      ),
      children: <ResultAnalysis />
    },
    {
      key: 'alerts',
      label: (
        <span>
          <AlertOutlined />
          告警中心
        </span>
      ),
      children: <AlertCenter onStatsChange={fetchStats} />
    },
    {
      key: 'statistics',
      label: (
        <span>
          <BarChartOutlined />
          统计分析
        </span>
      ),
      children: <Statistics />
    }
  ];

  return (
    <PageContainer>
      {/* 统计概览 */}
      <Card className={styles.statsCard} loading={loading}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="活跃任务"
              value={stats.active_tasks || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="今日检测"
              value={stats.today_detections || 0}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="未处理告警"
              value={stats.pending_alerts || 0}
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="高危告警"
              value={stats.high_severity_alerts || 0}
              valueStyle={{ color: '#f5222d' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 主要内容区域 */}
      <Card className={styles.mainCard}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>
    </PageContainer>
  );
};

export default AnomalyDetection;
