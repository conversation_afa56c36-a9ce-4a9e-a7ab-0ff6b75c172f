import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Card,
  Row,
  Col,
} from 'antd';
import { updateAnomalyTask, AnomalyTask } from '@/services/anomaly';

const { Option } = Select;

interface EditTaskModalProps {
  visible: boolean;
  task: AnomalyTask | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const EditTaskModal: React.FC<EditTaskModalProps> = ({
  visible,
  task,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (task && visible) {
      form.setFieldsValue({
        name: task.name,
        description: task.description,
        status: task.status,
        alert_enabled: task.alert_config?.enabled || false,
        severity_threshold: task.alert_config?.severity_threshold || 'medium',
      });
    }
  }, [task, visible, form]);

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleSubmit = async () => {
    if (!task) return;
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const updateData = {
        name: values.name,
        description: values.description,
        status: values.status,
        alert_config: {
          enabled: values.alert_enabled,
          severity_threshold: values.severity_threshold,
        },
      };
      
      const response = await updateAnomalyTask(task.task_id, updateData);
      if (response.flag) {
        onSuccess();
        message.success('任务更新成功');
      } else {
        message.error(response.message || '更新失败');
      }
    } catch (error) {
      message.error('更新任务失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="编辑异常检测任务"
      visible={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="任务名称"
                rules={[{ required: true, message: '请输入任务名称' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="description" label="任务描述">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item name="status" label="任务状态">
            <Select>
              <Option value="active">运行中</Option>
              <Option value="inactive">已停止</Option>
            </Select>
          </Form.Item>
        </Card>

        <Card title="告警配置" size="small">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="alert_enabled" label="启用告警" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="severity_threshold" label="告警阈值">
                <Select>
                  <Option value="low">低危及以上</Option>
                  <Option value="medium">中危及以上</Option>
                  <Option value="high">高危及以上</Option>
                  <Option value="critical">仅严重</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </Modal>
  );
};

export default EditTaskModal;
