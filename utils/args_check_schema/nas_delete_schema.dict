{
    "type":"object",
    "required":["fileList"],
    "properties":{
        "fileList": {
            "type":"array",
            "items":{
                "type":"object",
                "properties":{
                    "serverId":{
                        "type":"string",
                        "pattern": r"^.{1,512}"
                    },
                    "userId":{
                        "type":"string",
                        "pattern":r"^[0-9a-fA-F]{32}$"
                    }
                }
            }
        }
    }
}