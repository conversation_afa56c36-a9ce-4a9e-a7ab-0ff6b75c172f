#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
""" 获取单个 IOC 告警详情 """
from flask import request

from api_1_0.back_event.get_ioc_list import format_es_temp
from utils.database import MongoDB
from utils.es_function import get_es_template
from utils.ndr_base import NdrResource
from utils.utils import flask_response
from utils.param_check import Validator, check_flask_args
from config.config import IOC_INDEX, FILE_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class FileDetailResource(NdrResource):
    """ 获取file数据详情 """

    def __init__(self):
        """初始化参数"""
        self.mongodb = MongoDB("ndr")
        super(FileDetailResource, self).__init__(es_template="file_detail")

    @check_flask_args(Validator("file_list"), request)
    def post(self, **kwargs):

        option_args = {
            "celeryId": kwargs["celeryId"],
            "threatLevel": kwargs["threatLevel"],
            "threatScore": kwargs["threatScore"],
            "killchain": kwargs["killchain"],
            "mbinfo": kwargs["mbinfo"],
            "threatType": kwargs["threatType"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        group = kwargs["groupKey"]
        if group != "uniqueId":
            group = "flow." + group
        group = group + ".keyword"
        unique_filter = {
            "term": {
                group: kwargs["value"]
            }
        }
        es_template["query"]["bool"]["must"].append(unique_filter)
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])
        es_template['from'] = (page - 1) * page_size
        es_template['size'] = page_size
        try:
            res = self.es_client.search(index=FILE_INDEX, body=es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(kwargs["groupKey"], res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, group_key, es_data):
        total = es_data["hits"]["total"]['value']
        data_return = {
            "total": total,
            "alters": []
        }
        file_detail_list = es_data["hits"]["hits"]
        if group_key == "uniqueId":
            for file_detail in file_detail_list:
                file_dict = dict()
                file_dict["taskType"] = file_detail["_source"]["taskType"]
                file_dict["occurredTime"] = file_detail["_source"]["occurredTime"]
                file_dict["observedTime"] = file_detail["_source"]["observedTime"]
                file_dict["src_mac"] = file_detail["_source"]["flow"]["src_mac"]
                file_dict["dst_mac"] = file_detail["_source"]["flow"]["dst_mac"]
                file_dict["src_ip"] = file_detail["_source"]["flow"]["src_ip"]
                file_dict["dst_ip"] = file_detail["_source"]["flow"]["dst_ip"]
                file_dict["src_port"] = file_detail["_source"]["flow"]["src_port"]
                file_dict["dst_port"] = file_detail["_source"]["flow"]["dst_port"]
                file_dict["proto"] = file_detail["_source"]["flow"]["proto"]
                file_dict["pcap_filename"] = file_detail["_source"]["pcap_filename"]
                file_dict["not_fss_pcapname"] = file_detail["_source"]["not_fss_pcapname"]
                data_return['alters'].append(file_dict)
        else:
            for file_detail in file_detail_list:
                data_dict = file_detail["_source"]
                data_dict["taskName"] = ""
                celery_id = data_dict["celeryId"]
                rst = self.mongodb.find_one('back_explore', {"celeryId": celery_id})
                if rst:
                    data_dict["taskName"] = rst['taskName']
                data_return['alters'].append(data_dict)
        return data_return
