#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_event_metadata.py
# @time: 2019/07/18
# @software: PyCharm


import datetime
import time
import ipaddress

from celery_tasks.country_code import country_map
from celery_tasks import app
from celery_tasks.utils import <PERSON><PERSON><PERSON>ouse
from config.config import NdrLog
from utils.database import BaseMongoDB
from utils.database import get_es_client
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)
db = BaseMongoDB("ndr")
assets_up = 100000
try:
    assets_up = db.find_one("system_config", {})["assets_up"]
except Exception as e:
    LOG.error(e)
ch_client = ClickHouse()
es_client = get_es_client()


@app.task()
def get_assets_info():
    present_time = int(time.mktime(datetime.datetime.now().timetuple())) * 1000
    past_min_time = int(time.mktime((datetime.datetime.now() - datetime.timedelta(minutes=3)).timetuple())) * 1000
    past_day_time = int(time.mktime((datetime.datetime.now() - datetime.timedelta(minutes=10)).timetuple())) * 1000

    es_template = ES_Template().read_template('get_assets_info')
    es_template["query"]["bool"]["must"][0]["range"]["observedTime"]["gte"] = past_min_time
    es_template["query"]["bool"]["must"][0]["range"]["observedTime"]["lte"] = present_time
    try:
        data = es_client.search(index="rule-eve,ioc-eve,model-eve,file-eve", body=es_template)
        format_data = data_format(data)
        ip_list = list(format_data.keys())
        ip_list_old = []
        for ip in ip_list:
            rst = db.find_one("assets_info", {"ip_addr": ip})
            if rst:
                ip_list_old.append(ip)
        ip_list_new = list(set(ip_list) - set(ip_list_old))
        # 资产数据超过一定数量后，删除部分资产数据
        data_count = db.count("assets_info", {})
        if data_count > assets_up:
            popular = db.find("assets_info", {}).sort([("popular", -1)]).skip(assets_up).limit(1)[0]["popular"]
            db.delete("assets_info",
                      {"$and": [{"is_outer": True},
                                {"is_importent": False},
                                {"create_time": {"$lte": (int(time.time()) - 24 * 60 * 60) * 1000}},
                                {"popular": {"$lte": popular}}]})
        insert_data = []
        for ip in ip_list_new:
            body = {
                "name": "",  # 资产名称
                "ip_addr": ip,  # IP地址
                "is_outer": False if ipaddress.ip_address(ip).is_private else True,  # 是否外网
                "mac_addr": "",  # MAC地址
                "enable_port": {},  # 启用的端口
                "type": "",  # 资产类型
                "assets_num": "",  # 资产编号
                "is_sercret": False,  # 是否涉密
                "is_importent": False,  # 是否重点资产
                "location": "",  # 所在位置
                "country": "",  # 所在国家
                "country_code": "",  # 国家代码
                "department": "",  # 所属部门
                "industry": "",  # 所属行业
                "organisation": "",  # 所属组织
                "components": "",  # 组件列表
                "domain": "",  # 域名
                "other": "",  # 其他
                "person": "",  # 责任人
                "os_info": "",  # 操作系统信息
                "labels": [],  # 标签列表
                "send_count": 0,  # 发起的链接数
                "accept_count": 0,  # 接收的链接数
                "app_total_traffic": 0,  # 交互的流量总量
                "app_traffic": {},  # 应用流量Top10
                "send_traffic": {},  # 发送流量Top5
                "access_sample": {},  # 访问抽样
                "popular": 5,  # 每次在告警中发现加5，资产画像查询的时候就加1
                "latest_week_stream": [],  # 周流量
                "create_time": int(time.time() * 1000),  # 资产信息创建时间
                "update_time": int(time.time() * 1000)  # 资产信息更新时间
            }
            # 先执行初始化插入操作，在将ck查询的结果更新到mongodb
            # db.insert_one("assets_info", body)
            insert_data.append(body)
        if insert_data:
            db.insert_many("assets_info", insert_data)
        LOG.info("总共new  ip数量 %s" % (len(ip_list_new)))
        LOG.info("总共old  ip数量 %s" % (len(ip_list_old)))
        # 更新旧资产
        for ip, value in format_data.items():
            if ip in ip_list_old:
                rst = db.find_one("assets_info", {"ip_addr": ip})
                popular = rst["popular"] + 5
                labels = set(rst["labels"])
                if value["labels"]:
                    labels.add(value["labels"])
                body_update = {
                    "location": value["location"],  # 所在位置
                    "country": value["country"],  # 所在国家
                    "country_code": country_map[value["country"]] if value["country"] in country_map.keys() else "",
                    "labels": list(labels),  # 标签
                    "popular": popular,  # 每次在告警中发现加5，资产画像查询的时候就加1
                    "update_time": int(time.time() * 1000)
                }
                db.update_many("assets_info", {"ip_addr": ip}, body_update)
        # 更新新资产
        for ip, value in format_data.items():
            if ip in ip_list_new:
                send_count, accept_count, enable_port, app_traffic, total_traffic, send_traffic, access_sample, week_stream \
                    = ch_client.get_clickhouse_data(ip, past_day_time // 1000, present_time // 1000)
                labels = set()
                db_set = {1433, 3306, 27017, 6379, 9083}
                total_count = send_count + accept_count
                if total_count > 10000 and send_count / total_count > 0.99:
                    labels.add("PC")
                if 80 in enable_port or 443 in enable_port:
                    labels.add("WebServer")
                if 21 in enable_port:
                    labels.add("FTP服务器")
                if 25 in enable_port:
                    labels.add("邮件服务器")
                if 53 in enable_port:
                    labels.add("DNS服务器")
                if db_set & set(enable_port):
                    labels.add("数据库")
                if value["labels"]:
                    labels.add(value["labels"])
                enable_port_data = {}
                for port in enable_port:
                    enable_port_data[str(port)] = int(time.time())
                body_update = {
                    "is_importent": True if value["from"] else False,  # 是否重点资产
                    "location": value["location"],  # 所在位置
                    "country": value["country"],  # 所在国家
                    "country_code": country_map[value["country"]] if value["country"] in country_map.keys() else "",
                    "industry": value["industry"],  # 所属行业
                    "organisation": value["organisation"],  # 所属组织
                    # "components": value["components"],  # 组件列表
                    # "domain": value["domain"],  # 域名
                    # "other": value["other"],  # 其他
                    # "name": value["assetsName"],  # 资产名称
                    # "type": value["assetsType"],  # 资产类型
                    "labels": list(labels),
                    "send_count": send_count,
                    "accept_count": accept_count,
                    "enable_port": enable_port_data,
                    "app_total_traffic": total_traffic,
                    "app_traffic": app_traffic,
                    "send_traffic": send_traffic,
                    "access_sample": access_sample,
                    "latest_week_stream": [week_stream],
                    "update_time": int(time.time() * 1000)
                }
                db.update_many("assets_info", {"ip_addr": ip}, body_update)
            end_time = int(time.mktime(datetime.datetime.now().timetuple()))
            if end_time - present_time // 1000 >= 180:
                return
    except Exception as e:
        LOG.error('reason[{0}]'.format(str(e)))
        return


def data_format(data):
    format_data = {}
    for bucket in data["aggregations"]["group_id"]["buckets"]:
        data_info = bucket["doc_ids"]["hits"]["hits"][0]["_source"]
        src = {}
        src_ip = data_info["flow"]["src_ip"].strip()
        src["assetsType"] = data_info["srcIpMbInfo"]["assetsType"]
        src["country"] = data_info["srcIpMbInfo"]["country"]
        src["components"] = data_info["srcIpMbInfo"]["components"]
        src["other"] = data_info["srcIpMbInfo"]["other"]
        src["location"] = data_info["srcIpMbInfo"]["address"]
        src["domain"] = data_info["srcIpMbInfo"]["domain"]
        src["assetsName"] = data_info["srcIpMbInfo"]["assetsName"]
        src["organisation"] = data_info["srcIpMbInfo"]["organisation"]
        src["industry"] = data_info["srcIpMbInfo"]["industry"]
        src["from"] = data_info["srcIpMbInfo"]["from"]
        if not src["country"]:
            src["country"] = data_info["srcIpGeoInfo"]["country_name"]
        if not src["location"]:
            src["location"] = data_info["srcIpGeoInfo"]["longitude"] + "-" + data_info["srcIpGeoInfo"]["latitude"]
        src["labels"] = ""
        if data_info["eve_type"] != "ioc-eve":
            src["labels"] = "攻击设施"
        format_data[src_ip] = src
        dst = {}
        dst_ip = data_info["flow"]["dst_ip"].strip()
        dst["assetsType"] = data_info["dstIpMbInfo"]["assetsType"]
        dst["country"] = data_info["dstIpMbInfo"]["country"]
        dst["components"] = data_info["dstIpMbInfo"]["components"]
        dst["other"] = data_info["dstIpMbInfo"]["other"]
        dst["location"] = data_info["dstIpMbInfo"]["address"]
        dst["domain"] = data_info["dstIpMbInfo"]["domain"]
        dst["assetsName"] = data_info["dstIpMbInfo"]["assetsName"]
        dst["organisation"] = data_info["dstIpMbInfo"]["organisation"]
        dst["industry"] = data_info["dstIpMbInfo"]["industry"]
        dst["from"] = data_info["dstIpMbInfo"]["from"]
        if not dst["country"]:
            dst["country"] = data_info["dstIpGeoInfo"]["country_name"]
        if not dst["location"]:
            dst["location"] = data_info["dstIpGeoInfo"]["longitude"] + "-" + data_info["dstIpGeoInfo"]["latitude"]
        dst["labels"] = ""
        format_data[dst_ip] = dst
    return format_data
