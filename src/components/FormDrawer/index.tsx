import React from 'react';
import { Drawer, But<PERSON> } from 'antd';
import { DrawerProps } from 'antd/lib/drawer';
import styles from './index.less';

interface Props extends DrawerProps {
  hideFooter?: boolean;
  loading?: boolean;
  onSubmit?: () => void;
}

class FormDrawer extends React.PureComponent<Props> {
  static defaultProps = {
    width: 640,
  };

  render() {
    const { onClose, onSubmit, hideFooter, loading = false } = this.props;
    return (
      <Drawer className={styles.drawer} {...this.props}>
        {this.props.children}
        {!hideFooter && (
          <div className={styles.footer}>
            <Button style={{ marginRight: 8 }} onClick={onClose}>
              取消
            </Button>
            <Button type="primary" loading={loading} onClick={onSubmit}>
              确认
            </Button>
          </div>
        )}
      </Drawer>
    );
  }
}

export default FormDrawer;
