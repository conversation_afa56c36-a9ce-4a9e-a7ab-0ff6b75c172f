#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import copy
from itertools import combinations

from flask import request
from flask_restful import Resource

from api_1_0.knowledge.get_killchains import GetKillchains
from api_1_0.utils.flask_log import ndr_log_to_box
from config.config import NdrLog
from utils.utils import flask_response
from utils import param_check, database
from utils.param_check import Validator
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)


class AtkChRecvResource(Resource):
    """
    说明：ES数据库根据时间、ip、回放/实时的条件取3种告警，每种告警最多100条，理论上最多300个数据；
        每种告警根据时间+阶段聚合，有的同时间点并且同阶段有多个数据，但是只取一条；
        三种告警在同时间点有同阶段概率会出现数据，但是都要展示出来；
    """

    def __init__(self):
        """初始化参数"""
        # 初始化 ES 查询模板
        self.es_template = ES_Template().read_template('attack_chains_recv')
        self.es_suricata_tmp = {}
        self.es_ioc_tmp = {}
        self.es_model_tmp = {}
        self.es_file_tmp = {}
        # 初始化 ES client
        self.es_client = database.get_es_client()
        # formate chart data
        self.chart_data = {
            "xAxis": list(GetKillchains.killchain_map.values()),
            "yAxis": [],  # 时间参数，需要手动添加
            "scatter": [],  # 描点的数据
            "line": [],  # 描最长路径折线的数据
        }

    def format_es_template(self, args):
        time_range = {
            "range": {
                args["timeType"]: {
                    "gte": int(args["startTime"]),
                    "lte": int(args["stopTime"])
                }
            }
        }
        self.es_template["query"]["bool"]["must"][0] = time_range
        self.es_template["query"]["bool"]["must"][2]["bool"]["should"][0]["term"]["flow.src_ip.keyword"] = args["ip"]
        self.es_template["query"]["bool"]["must"][2]["bool"]["should"][1]["term"]["flow.dst_ip.keyword"] = args["ip"]
        self.es_template["query"]["bool"]["must"][1]["term"]["taskType.keyword"] = 'replay' if args[
            "replay"] else 'realTime'
        # 每个index所包含的字段不一样，根据index提取所需要的字段
        self.es_suricata_tmp = copy.deepcopy(self.es_template)
        self.es_ioc_tmp = copy.deepcopy(self.es_template)
        self.es_model_tmp = copy.deepcopy(self.es_template)
        self.es_file_tmp = copy.deepcopy(self.es_template)
        self.es_suricata_tmp["aggs"]["grp_by_time_chain"]["aggs"]["org_data"]["top_hits"]["_source"]["includes"] += [
            "cve",
            "vulName",
            "threatFlag"
        ]
        self.es_ioc_tmp["aggs"]["grp_by_time_chain"]["aggs"]["org_data"]["top_hits"]["_source"]["includes"] += [
            "threat_type",
            "aptOrganization",
            "ioc"
        ]
        self.es_model_tmp["aggs"]["grp_by_time_chain"]["aggs"]["org_data"]["top_hits"]["_source"]["includes"] += [
            "modelName",
        ]

    def caculate_longest_path_from_stage(self, chains_map, time_type):
        """
        计算攻击链最长路径
        1. 找到最早时间的最早阶段，即最左上角的点，以此为基点计算此基点的最长路径。
            因为在此基点同阶段之下的不会有比此基点更长的路径。唯一可能比基点长的路径的点，必然处于其他阶段并且比基点时间更早。
        2. 基点后的遍历规则：组合基点之后的chains列表，如C55，根据新的列表计算最长路径。
        3. 找到基点下一阶段的最早时间，如果小于等于基点，则不再计算，因为必然不会比基点的路径长。
        4. 依次查找其他阶段比基点时间更早的点计算最长路径，如果之后的阶段最长路径小于基点路径，则之后的阶段不再计算。
        """

        def get_path_by_spot(spot, killchain_list):
            """
            给定列表和spot，返回路径列表.
            """
            if not killchain_list:
                return []
            # 只循环第一列
            for i in killchain_list[0]:
                # 如果此阶段找到了时间比基点大的点，变更基点再往下个阶段找，并结束当前循环
                if i[time_type] > spot[time_type]:
                    ret_list = get_path_by_spot(i, killchain_list[1:])
                    ret_list.append(i)

                    return ret_list

            # 如果此阶段没有找到时间比基点小的点，继续以基点往下个阶段找，直到结束
            return get_path_by_spot(spot, killchain_list[1:])

        max_step = len(chains_map)
        caculate_step = 0
        for idx, killchain in enumerate(chains_map):
            # 如果后面所能找到的最长路径小于已找到的路径长度，则不再查找
            if max_step - idx - 1 < caculate_step:
                break
            # 找到每个阶段最小时间的点，并求最长路径，如果最小时间比基点最小时间更大，则不需要查找
            if not killchain or (self.chart_data['line'] and killchain[0][time_type] >= self.chart_data['line'][0][
                time_type]):
                continue

            # 组合所有可能的列表，注意：stage是有序的，所以是组合，而不是排列！
            # 排列的列表大小取决于max_step，因为过于小的step不必再计算
            for comb_num in reversed(range(1, max_step - idx)):
                for comb_list in combinations(chains_map[idx + 1:], comb_num):
                    if caculate_step > comb_num:
                        break
                    # print(idx, comb_num, caculate_step, comb_list)
                    tmp_list = get_path_by_spot(killchain[0], comb_list)
                    tmp_list.append(killchain[0])  # 由于递归，此时列表路径为倒序
                    if caculate_step < len(tmp_list):
                        caculate_step = len(tmp_list)
                        self.chart_data['line'] = copy.deepcopy(tmp_list)

            # print("The longest path is : ", [i['observedTime'] for i in self.chart_data['line']])

    def formate_data(self, res_suricata, res_ioc, res_model, res_file, time_type):
        # 合并3种告警数据
        suricata_buckets = res_suricata["aggregations"]['grp_by_time_chain']['buckets']
        ioc_buckets = res_ioc["aggregations"]['grp_by_time_chain']['buckets']
        model_buckets = res_model["aggregations"]['grp_by_time_chain']['buckets']
        file_buckets = res_file["aggregations"]['grp_by_time_chain']['buckets']
        all_buckets = suricata_buckets + ioc_buckets + model_buckets + file_buckets
        all_buckets = sorted(all_buckets, key=lambda i: i['org_data']['hits']['hits'][0]['_source'][time_type])
        chains_map = [[], [], [], [], [], [], []]  # 建立一个二维数组，第一下标是攻击阶段，第二下标是dict数据
        # 为方便后面的数据处理，先进行排序
        for raw_data in all_buckets:
            org_data = raw_data['org_data']['hits']['hits'][0]['_source']
            org_data['xAxis'] = GetKillchains.killchain_map.get(org_data['killchain'])

            org_data['yAxis'] = org_data[time_type]

            self.chart_data['scatter'].append(org_data)
            self.chart_data['yAxis'].append(org_data[time_type])
            # 填入chains_map数据，即使有重复的时间点数据，也不必去重，只取其一即可
            chains_map[self.chart_data['xAxis'].index(org_data['xAxis'])].append(org_data)

        self.chart_data['yAxis'] = list(set(self.chart_data['yAxis']))  # 去重
        self.chart_data['yAxis'].sort()  # 顺序排序
        self.chart_data['yAxis'].reverse()  # 反向（前端要求）

        self.caculate_longest_path_from_stage(chains_map, time_type)

        for spot in self.chart_data['scatter']:
            if spot in self.chart_data['line']:
                spot['spotType'] = 'line'
            else:
                spot['spotType'] = 'scatter'

        return self.chart_data

    @param_check.check_flask_args(Validator("attack_chains_recv"), request)
    def post(self, **kwargs):
        try:
            time_type = kwargs["timeType"]
            self.format_es_template(kwargs)
            res_suricata = self.es_client.search(index="rule-eve", body=self.es_suricata_tmp)
            res_ioc = self.es_client.search(index="ioc-eve", body=self.es_ioc_tmp)
            res_model = self.es_client.search(index="model-eve", body=self.es_model_tmp)
            res_file = self.es_client.search(index="file-eve", body=self.es_file_tmp)
            self.chart_data = self.formate_data(res_suricata, res_ioc, res_model, res_file, time_type)
        except Exception as e:
            err_info = "error: " + str(e)
            ES_LOG.error('params[{0}], reason[{1}]'.format(self.es_template, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "ip攻击链获取失败")
            return flask_response(err_info, False, {})

        ndr_log_to_box(NdrLog.Type.OPERATE, "ip攻击链获取成功")
        return flask_response("", True, self.chart_data)
