import os
import time
import logging
from datetime import datetime, timedelta
from elasticsearch import Elasticsearch, helpers
from clickhouse_driver import Client as ClickhouseClient

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler("mail_analysis.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

Config = {
    'ES': {
        'hosts': 'http://127.0.0.1:9200',
        'alarm_index': 'file-eve-agg-20250801',
        'new_index': 'mail_analysis'
    },
    'ClickHouse': {
        'host': '127.0.0.1',
        'port': 9000,
        'user': 'default',
        'password': 'ndr@ck++',
        'database': 'ndr',
        'table': 'dpilog_mail'
    },
    'DEFAULT': {
        'interval': 60,  # 处理间隔(秒)
        'time_window': 3000,  # 时间窗口(秒)
        'state_file': 'mail_analysis_state.txt',
    }
}


class MailAnalysisProcessor:
    def __init__(self):
        self.config = Config

        # 初始化ES连接
        es_hosts = self.config['ES']['hosts'].split(',')
        self.es = Elasticsearch(es_hosts)
        self.alarm_index = self.config['ES']['alarm_index']
        self.new_index = self.config['ES']['new_index']

        # 初始化ClickHouse连接
        self.ch_client = ClickhouseClient(
            host=self.config['ClickHouse']['host'],
            port=self.config['ClickHouse']['port'],
            user=self.config['ClickHouse']['user'],
            password=self.config['ClickHouse']['password'],
            database=self.config['ClickHouse']['database'],
        )
        self.ch_table = self.config['ClickHouse']['table']

        # 状态管理
        self.state_file = self.config['DEFAULT']['state_file']
        self.last_processed_ts = self._load_state()
        self.time_window = self.config['DEFAULT']['time_window']

    def _load_state(self):
        """加载上次处理的时间戳"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r') as f:
                    return float(f.read().strip())
            except Exception as e:
                logger.error(f"加载状态文件失败: {e}")
        # 默认返回1小时前的时间戳
        return int((datetime.utcnow() - timedelta(hours=1)).timestamp())

    def _save_state(self, timestamp):
        """保存最后处理的时间戳"""
        try:
            with open(self.state_file, 'w') as f:
                f.write(str(timestamp))
            logger.info(f"状态已更新: {timestamp}")
        except Exception as e:
            logger.error(f"保存状态失败: {e}")

    def _query_es_alarms(self):
        """查询ES中新增的文件告警，只取邮件协议相关并去重"""
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"range": {"observedTime": {"gt": self.last_processed_ts}}},
                        {"terms": {"application": ["pop3", "imap", "smtp"]}}  # 新增邮件协议过滤
                    ]
                }
            },
            "sort": [{"ts": {"order": "asc"}}],
            "size": 1000,
            "aggs": {  # 使用聚合去重
                "unique_files": {
                    "terms": {
                        "field": "md5.keyword",
                        "size": 1000
                    },
                    "aggs": {
                        "latest_alarm": {
                            "top_hits": {
                                "size": 1,
                                "sort": [{"observedTime": {"order": "desc"}}]  # 取最新的告警
                            }
                        }
                    }
                }
            }
        }

        try:
            # 使用聚合查询获取去重结果
            resp = self.es.search(index=self.alarm_index, body=query)
            unique_alarms = []

            # 从聚合结果中提取数据
            for bucket in resp['aggregations']['unique_files']['buckets']:
                hit = bucket['latest_alarm']['hits']['hits'][0]
                unique_alarms.append(hit['_source'])

            return unique_alarms
        except Exception as e:
            logger.error(f"ES查询失败: {e}")
            return []

    def _query_clickhouse_mail(self, filename, alarm_ts):
        """查询ClickHouse中的邮件信息，对同一msg_id的记录进行聚合"""
        start_time = datetime.fromtimestamp(alarm_ts - self.time_window)
        end_time = datetime.fromtimestamp(alarm_ts + self.time_window)
        file_name = filename.split("__")[1]

        # 使用聚合查询，对同一msg_id的记录进行合并
        query = f"""
        SELECT 
            any(`from`) as `from`, 
            any(to) as to, 
            any(cc) as cc, 
            any(bcc) as bcc, 
            any(subject) as subject, 
            any(body) as body, 
            msg_id,
            groupArray(service) as services
        FROM {self.ch_table}
        WHERE 
            create_time >= %(start_time)s AND
            create_time <= %(end_time)s AND
            file_names LIKE %(pattern)s
        GROUP BY msg_id
        """

        try:
            #打印三个参数的信息
            logger.info(f"查询ClickHouse参数信息: {start_time}, {end_time}, {file_name}")
            return self.ch_client.execute(
                query,
                {
                    'start_time': start_time,
                    'end_time': end_time,
                    'pattern': f'%{file_name}%'
                }
            )
        except Exception as e:
            logger.error(f"ClickHouse查询失败: {e}")
            return []

    def _process_alarms(self):
        """处理告警并生成新文档"""
        alarms = self._query_es_alarms()
        if not alarms:
            logger.info("未发现新的告警记录")
            return []

        max_ts = self.last_processed_ts
        actions = []

        # 处理收件人字段，按逗号拆分为数组
        def split_recipients(recipient_str):
            if ';' in recipient_str:
                char = ';'
            elif ',' in recipient_str:
                char = ','
            else:
                char = ' '
            if recipient_str:
                return [r.strip() for r in recipient_str.split(char) if r.strip()]
            return []

        # 处理告警
        for alarm in alarms:
            try:
                alarm_ts = alarm['observedTime']
                filename = alarm['filename']

                # 更新最后处理时间戳
                if alarm_ts > max_ts:
                    max_ts = alarm_ts

                # 查询关联的邮件信息
                mail_records = self._query_clickhouse_mail(filename, alarm_ts/1000)

                if not mail_records:
                    logger.warning(f"未找到文件 {filename} 的邮件信息 (时间: {alarm_ts})")
                    continue

                # 创建基础文档
                base_doc = {
                    'timestamp': alarm_ts,
                    'filename': filename,
                    'md5': alarm['md5'],
                    "filetype": alarm['filetype'],
                    "filesize": alarm['filesize'],
                    'threatName': alarm['threatName'],
                    'threatLevel': alarm['threatLevel'],
                    "threatType": alarm['threatType'],
                    'labels': alarm.get('labels', ''),
                    'reportPath': alarm.get('reportPath', '')
                }

                # 为每个邮件记录创建新文档
                for mail in mail_records:
                    services = mail[7] if mail[7] else []  # services数组
                    if 'POP3' in services or 'IMAP' in services:
                        received = True
                    else:
                        received = False

                    doc = base_doc.copy()
                    doc.update({
                        'from': mail[0] or '',     # from
                        'to': split_recipients(mail[1]),       # to
                        'cc':split_recipients(mail[2]),        # cc
                        'bcc': split_recipients(mail[3]),      # bcc
                        'subject': mail[4] or '',  # subject
                        'body': mail[5] or '',     # body
                        'msg_id': mail[6] or '',   # msg_id
                        'fetch': received
                    })
                    actions.append({
                        '_op_type': 'index',
                        '_index': self.new_index,
                        '_source': doc
                    })

                logger.info(f"处理文件 {filename}, 找到 {len(mail_records)} 条邮件记录")

            except KeyError as e:
                logger.error(f"告警记录缺少必要字段: {e}, 数据: {alarm}")
            except Exception as e:
                logger.error(f"处理告警时出错: {e}")

        # 更新最后处理时间
        if max_ts > self.last_processed_ts:
            self.last_processed_ts = max_ts
            self._save_state(max_ts)

        return actions

    def run(self):
        """主处理循环"""
        logger.info("邮件分析处理器已启动")
        logger.info(f"最后处理时间: {datetime.fromtimestamp(self.last_processed_ts/1000).isoformat()}")

        interval = self.config['DEFAULT']['interval']

        while True:
            try:
                start_time = time.time()
                logger.info("开始处理周期...")

                actions = self._process_alarms()
                if actions:
                    success, errors = helpers.bulk(self.es, actions)
                    logger.info(f"成功插入 {success} 条文档, 错误 {len(errors)} 条")
                    if errors:
                        logger.error(f"插入错误: {errors[:3]}")  # 只显示前3个错误

                elapsed = time.time() - start_time
                sleep_time = max(1, interval - elapsed)
                logger.info(f"周期处理完成, 耗时 {elapsed:.2f}秒, 休眠 {sleep_time:.2f}秒")
                time.sleep(sleep_time)

            except KeyboardInterrupt:
                logger.info("用户中断, 退出程序")
                break
            except Exception as e:
                logger.critical(f"处理过程中发生严重错误: {e}", exc_info=True)
                logger.info("程序将在10秒后重试...")
                time.sleep(10)


if __name__ == "__main__":
    processor = MailAnalysisProcessor()
    processor.run()