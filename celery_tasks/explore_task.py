# -*- coding: utf-8 -*-
# @Time    : 2019-08-26 14:10
# <AUTHOR> wu
# @File    : explore_task.py
# @Software: PyCharm
"""
celery 探索任务处理
"""
import ftplib
import logging
import time
import os
import json
import hashlib
import zlib
import filetype

from croniter import croniter
from datetime import datetime, timed<PERSON><PERSON>
from celery import current_task
from kafka import KafkaProducer
from celery_tasks import app, hdp_task_thread, hdp_task_queue
from config.config import ReplayFeaturePath, ClickHouseConfig
from config.config import DetectProg
from config.config import NdrLog, FtpPcapPath, NASConfig, CommandTimeOut
from utils.utils import ftp_server_reconnect, ftp_server_disconnect, run_command, ftp_server_connect,\
    pcap_fix, ftp_server_recurse_pcap, get_hdp_feature_str_from_query
from utils.database import BaseMongoDB
from utils.logger import get_ndr_logger, LogToDb
from celery_tasks.explore_task_status import ExploreTaskStatus, FileTaskResultUpdate
from api_1_0.back_explore.nas import check_nas_server_online
from celery.result import AsyncResult
from api_1_0.dpilog.dpilog import DpilogResource

LOG = get_ndr_logger(NdrLog.Type.RUN, __file__)

logging.getLogger("elasticsearch").setLevel(logging.WARNING)


def connect_ftp_server():
    """
    :return:
    """
    mongodb = BaseMongoDB("ndr")
    ftp_server = mongodb.find_one('ftp_server', {}, {"serverId": 1,
                                                     "host": 1,
                                                     "port": 1,
                                                     "username": 1,
                                                     "password": 1,
                                                     "enable": 1})
    if not ftp_server:
        return "FTP server not configured.", False
    if not ftp_server["enable"]:
        return "FTP server is not enabled.", False
    ftp, rst = ftp_server_reconnect(ftp_server['host'], ftp_server['port'],
                                    ftp_server['username'], ftp_server['password'])
    return ftp, rst


def clean_task_tmp_files(task_id):
    if os.path.exists(ReplayFeaturePath.RULES_PATH_HDP + task_id + ".dat"):
        os.system("rm -rf %s" % (ReplayFeaturePath.RULES_PATH_HDP + task_id + ".dat"))
    if os.path.exists(ReplayFeaturePath.RULES_PATH_HDP + task_id + ".rules"):
        os.system("rm -rf %s" % (ReplayFeaturePath.RULES_PATH_HDP + task_id + ".rules"))


# celery task for explore
@app.task()
def start_task(task_id, file_list):
    """执行或重新执行回放任务"""
    if not file_list:
        LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='任务开始执行失败，文件列表为空。')
        return
    mongo_client = BaseMongoDB("ndr")
    # 从 mongo 查询 task_id 对应的参数详情
    task_info = mongo_client.find_one("back_explore", {"taskId": task_id})

    celery_id = current_task.request.id

    LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]开始执行。' % task_info['taskName'])
    LOG.info("Explore task [%s] is started." % task_id)
    flag = hdp_rule_compile(task_id, task_info, mongo_client)
    if not flag:
        LOG.error("Explore task [%s] compile rules failed." % task_id)
        return
    LOG.info("Explore task [%s] compile rules success." % task_id)
    file_info = []
    total_size = 0

    for file in file_list:
        try:
            file_size = os.path.getsize(file)
            if file_size:
                file_info.append({'pcapName': file, 'pcapSize': file_size, 'pcapStatus': 'ready'})
                total_size += file_size
        except Exception as error:
            LogToDb().write_to_db(log_type=NdrLog.Type.RUN,
                                  event='探索任务[%s]获取 pcap 文件信息失败：%s。' % (task_info['taskName'], error))
            LOG.error("Get pcap info error.Reason: ", error)

    # # 更新任务初始状态，，all pcap list
    mongo_client.update("back_explore", {"taskId": task_id}, {
        "celeryId": celery_id,
        "status": "running",
        'process': 0,
        "replayInterface": "0",
        "startTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "pcapInfo": file_info,
        "endTime": "",
        'replay_endTime': '',
        "flowSize": total_size
    })

    # pcap 文件处理, 不必再等待线程结束，只提供执行信息，剩下的由回调函数处理
    for pcap_info in file_info:
        hdp_task_thread.submit(hdp_pcap_process, pcap_info['pcapName'], task_id, celery_id)


@app.task()
def stop_task(task_id):
    """停止正在执行的回放任务"""
    mongo_client = BaseMongoDB("ndr")
    celery_task = mongo_client.find_one("back_explore", {"taskId": task_id})
    if not celery_task:
        LOG.error("Explore task [%s] does not exist." % task_id)
        LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]停止失败：不存在。' % task_id)
        return
    celery_id = celery_task['celeryId'] if celery_task['taskType'] == 'singleTask' else celery_task['timedCeleryId']
    app.control.revoke(celery_id, terminate=True)
    LOG.info("Explore task [%s] is stopped." % task_id)
    mongo_client.update("back_explore",
                        {"taskId": task_id},
                        {"status": "finish",
                         "process": 1,
                         "endTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S',),
                         'replay_endTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S',)
                         })
    clean_task_tmp_files(task_id)
    LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]停止执行。' % celery_task["taskName"])


def hdp_rule_compile(task_id, raw_data, mongo_client):
    all_feat = mongo_client.find('feature', {
        "$and": [
            {"featureGroup": {"$in": raw_data["curFeatureGrpList"]}},
            {"featureStatus": 'enable'}
        ]})

    feature = ReplayFeaturePath.RULES_PATH_HDP + task_id + ".rules"
    with open(feature, "w") as fd:
        for feat in all_feat:
            feat_str = get_hdp_feature_str_from_query(feat)
            if not feat_str:
                LOG.error("Feature data error: %s" % str(feat))
                return False
            fd.write(feat_str)

    sig_pack_path = ReplayFeaturePath.RULES_PATH_HDP + task_id + ".dat"
    if os.path.getsize(feature):
        command = DetectProg.Hdp_SigCompile + " --featurePath {0} --sigPackPath {1} "
        args = command.format(feature, sig_pack_path)
        run_command(args)
        # os.system(args)
        LOG.info("Execute command: %s" % args)
    else:
        LOG.info("No hdp rules in file: %s" % feature)
        return False
    return True


def hdp_pcap_process(pcap, task_id, celery_id):
    sig_pack_path = ReplayFeaturePath.RULES_PATH_HDP + task_id + ".dat"
    if os.path.isfile(sig_pack_path):
        command = DetectProg.Hdp_Prog + " --ips-sigpack '{0}' --pcap '{1}'" \
                                        " --taskid '{2}' --celeryid '{3}'"
        args = command.format(sig_pack_path, pcap, task_id, celery_id)
        # 将执行命令发送给宿主机的celery进程，由宿主机执行
        hdp_task_queue.put(None)
        app.send_task('host_celery.tasks.hdp_pcap_task', args=[args, task_id],
                      link=update_pcap_status.s(celery_id, pcap),
                      queue='host_task', routing_key='host_task')
    else:
        LOG.error("Hdp sigpack file not exist.")


@app.task()
def update_pcap_status(hdp_ret, celery_id, pcap_f):
    """
    回放完成回调函数，做最后的状态更新，清理工作
    """
    mg = BaseMongoDB("ndr")
    task_info = mg.find_one('back_explore', {'celeryId': celery_id}, {
        'taskType': 1, 'taskId': 1, 'sourceData': 1, 'dir_name': 1})
    if not task_info:
        hdp_task_queue.get()
        return

    if task_info['taskType'] == 'timedTask':
        # 删除下载的文件，节省磁盘空间
        run_command('rm -f "%s"' % pcap_f, timeout=20)
        nas_info = mg.find_one('nas_manager', {'serverId': task_info['sourceData']})
        if not nas_info:
            hdp_task_queue.get()
            return
        if nas_info['serverType'] == 'ftp':
            pcap_name = pcap_f[len(os.path.join(NASConfig.PcapPath, task_info['taskId'])):]
        else:
            pcap_name = os.path.join(NASConfig.Path, task_info['dir_name']) + \
                                     pcap_f[len(os.path.join(NASConfig.PcapPath, task_info['taskId'])):]
    else:
        pcap_name = pcap_f

    # 这里不再计算并更新进度条process字段，因为先读后写，多进程情况下会导致共享资源互斥问题，统一放在任务刷新定时任务中计算
    mg.update_one("back_explore", {"celeryId": celery_id, 'pcapInfo.pcapName': pcap_name}, {
        "$set": {
            "statisticsInfo": get_es_statistics_info(celery_id, "celeryId"),
            "pcapInfo.$.pcapStatus": hdp_ret
        },
        "$addToSet": {
            "exploredPcapList": pcap_name
        }
    })

    hdp_task_queue.get()


def get_es_statistics_info(explore_id, id_type):
    rst_data = ExploreTaskStatus().get(explore_id=explore_id, id_type=id_type)
    kill_chains = {"vul": [], "ioc": [], "model": []}
    for item in rst_data["vul"]["killChains"].keys():
        if rst_data["vul"]["killChains"][item] > 0:
            kill_chains["vul"].append(item)
    for item in rst_data["ioc"]["killChains"].keys():
        if rst_data["ioc"]["killChains"][item] > 0:
            kill_chains["ioc"].append(item)
    for item in rst_data["model"]["killChains"].keys():
        if rst_data["model"]["killChains"][item] > 0:
            kill_chains["model"].append(item)
    statistics = {
        "vul": {
            "killChains": kill_chains["vul"],  # 杀伤链阶段
            "logCount": rst_data["vul"]['alertCount'],  # 日志数量
            "hitCount": rst_data["vul"]['hitCount'],  # 规则命中数
            "threatScore": rst_data["vul"]['score'],  # 威胁得分数
            "threatLevel": {
                "high": rst_data["vul"]['level']['High'],  # 高危漏洞数
                "medium": rst_data["vul"]['level']['Medium'],  # 中危漏洞数
                "low": rst_data["vul"]['level']['Low'],  # 低危漏洞数
            }
        },
        "ioc": {
            "killChains": kill_chains["ioc"],  # 杀伤链阶段
            "logCount": rst_data["ioc"]['alertCount'],  # 日志数量
            "hitCount": rst_data["ioc"]['hitCount'],  # 规则命中数
            "threatScore": rst_data["ioc"]['score'],  # 威胁得分数
            "threatLevel": {
                "high": rst_data["ioc"]['level']['High'],  # 高危漏洞数
                "medium": rst_data["ioc"]['level']['Medium'],  # 中危漏洞数
                "low": rst_data["ioc"]['level']['Low'],  # 低危漏洞数
            }
        },
        "model": {
            "killChains": kill_chains["model"],  # 杀伤链阶段
            "logCount": rst_data["model"]['alertCount'],  # 日志数量
            "hitCount": rst_data["model"]['hitCount'],  # 规则命中数
            "threatScore": rst_data["model"]['score'],  # 威胁得分数
            "threatLevel": {
                "high": rst_data["model"]['level']['High'],  # 高危漏洞数
                "medium": rst_data["model"]['level']['Medium'],  # 中危漏洞数
                "low": rst_data["model"]['level']['Low'],  # 低危漏洞数
            }
        },
        "file_log": rst_data["file_log"]
    }

    return statistics


def hdp_timed_pcap_process(pcap_file, task_id, celery_id):
    """
    下载文件并发送hdp任务
    """
    mg = BaseMongoDB("ndr")
    task_data = mg.find_one('back_explore', {'taskId': task_id}, {'sourceData': 1, 'status': 1})
    if not task_data or task_data['status'] != 'running':
        LOG.error('定时任务 %s 执行失败，未找到该任务' % task_id)
        mg.update(
            "back_explore",
            {"taskId": task_id, 'pcapInfo.pcapName': pcap_file},
            {"pcapInfo.$.pcapStatus": 'download failed'})
        return
    nas_data = mg.find_one('nas_manager', {'serverId': task_data['sourceData']})
    if not nas_data:
        mg.update(
            "back_explore",
            {"taskId": task_id, 'pcapInfo.pcapName': pcap_file},
            {"pcapInfo.$.pcapStatus": 'download failed'})
        LOG.error('定时任务 %s 执行失败，未找到该数据源' % task_id)
        return
    if not os.path.exists(NASConfig.PcapPath):
        os.system('mkdir -p %s' % NASConfig.PcapPath)

    local_path = os.path.join(NASConfig.PcapPath, task_id)
    if not os.path.exists(local_path):
        os.mkdir(local_path)
    status = ''
    dl_pcap = ''
    try:
        if nas_data['default_status']:
            if nas_data['serverType'] == 'ftp':
                ftp_handler, rst = ftp_server_connect(
                    nas_data['host'], nas_data['port'], nas_data['username'], nas_data["password"])
                if not rst:
                    LOG.error("nas pcap file download failed. Reason: %s" % str(ftp_handler))
                    status = 'download failed'
                else:
                    local_dir = os.path.join(local_path, os.path.dirname(pcap_file)[1:])
                    if not os.path.exists(local_dir):
                        os.makedirs(local_dir)
                    local_pcap_file = os.path.join(local_path, pcap_file[1:])
                    with open(local_pcap_file, 'wb') as write_file:
                        ftp_handler.retrbinary('RETR {0}'.format(pcap_file), write_file.write, 1024)
                    pcap_fix(local_pcap_file)
                    LOG.info("ftp pcap file [%s] download success." % pcap_file)
                    ftp_server_disconnect(ftp_handler)
                    dl_pcap = os.path.join(NASConfig.PcapPath, task_id, pcap_file[1:])  # 去掉最开始的'/'
                    status = 'download success'
            elif nas_data['serverType'] == 'nas':
                file_path = os.path.join(NASConfig.Path, nas_data['aliasName'])
                # 判断文件是否存在，有可能在下载的过程中断开连接
                ret, msg = run_command('ls %s' % pcap_file, timeout=CommandTimeOut.timeout)
                if not ret:
                    LOG.error("nas pcap file download failed. Reason: %s" % msg)
                    status = 'download failed'
                else:
                    local_dir = os.path.join(local_path, os.path.dirname(pcap_file[len(file_path) + 1:]))
                    if not os.path.exists(local_dir):
                        os.makedirs(local_dir)
                    cmd = 'cp %s %s' % (pcap_file, local_dir)
                    ret, msg = run_command(cmd, timeout=CommandTimeOut.timeout)
                    if not ret:
                        LOG.error("nas pcap file download failed. Reason: %s" % msg)
                        status = 'download failed'
                    else:
                        pcap_fix(os.path.join(local_dir, pcap_file))
                        # 去掉/mnt/nas/aliasName前缀路径
                        dl_pcap = os.path.join(NASConfig.PcapPath, task_id,
                                               pcap_file[len(os.path.join(NASConfig.Path, nas_data['aliasName'])) + 1:])
                        status = 'download success'
        else:
            LOG.error("nas pcap file download failed. Reason: %s" % '未连接')
            status = 'download failed'
    except Exception as error:
        LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='pcap [%s]文件下载失败: %s' % (pcap_file, error))
        status = 'download failed'

    mg.update(
        "back_explore",
        {"taskId": task_id, 'pcapInfo.pcapName': pcap_file},
        {"pcapInfo.$.pcapStatus": status})

    if status == 'download success' and dl_pcap:
        LOG.info('timed hdp pcap: %s' % dl_pcap)
        hdp_pcap_process(dl_pcap, task_id, celery_id)


def update_nas_incremental_pcap(task_data, nas_data, file_list):
    total_size = 0
    if nas_data['serverType'] == 'nas':
        try:
            for monitor_path in nas_data['monitor_dirs']:
                ret, _ = run_command('ls %s' % monitor_path, timeout=3)
                if not ret:
                    LOG.error("Explore timed task [%s] monitor dir [%s] is not exists!" % (
                        task_data['taskName'], monitor_path))
                    continue
                for dir_path, _, files in os.walk(monitor_path):
                    for file in files:
                        file_name = os.path.join(dir_path, file)
                        file_size = os.path.getsize(file_name)
                        if file.endswith(('.pcap', '.cap', '.pcapng')) \
                                and file_name not in task_data['exploredPcapList'] \
                                and file_size != 0:
                            total_size += file_size
                            file_list.append({
                                'pcapName': file_name,
                                'pcapSize': file_size,
                                'pcapStatus': 'ready'})
        except Exception as error:
            LOG.error("Explore timed task [%s] nas error: %s" % (task_data['taskName'], str(error)))
            return 0
    elif nas_data['serverType'] == 'ftp':
        try:
            ftp, rst = ftp_server_connect(
                nas_data['host'], nas_data['port'], nas_data['username'], nas_data["password"])
            if not rst:
                LOG.error("Explore timed task [%s] can not conncet ftp server!" % task_data['taskName'])
                return 0

            for monitor_dir in nas_data['monitor_dirs']:
                ftp_pcap_list = ftp_server_recurse_pcap(ftp, monitor_dir)
                for file in ftp_pcap_list:
                    file_size = ftp.size(file)
                    if file not in task_data['exploredPcapList'] and file_size != 0:
                        total_size += file_size
                        file_list.append({
                            'pcapName': file,
                            'pcapSize': file_size,
                            'pcapStatus': 'ready'})
        except ftplib.error_perm as error:
            LOG.error("Explore timed task [%s] ftp error: %s" % (task_data['taskName'], str(error)))
            return 0

    return total_size


@app.task()
def start_timed_task(task_id):
    mongo_client = BaseMongoDB("ndr")
    raw_data = mongo_client.find_one('back_explore', {'taskId': task_id})

    if not raw_data:
        LOG.error('定时任务 %s 执行失败，未找到该任务' % task_id)
        return
    celery_id = current_task.request.id if raw_data['celeryId'] == '' else raw_data['celeryId']

    LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]开始执行。' % raw_data['taskName'])
    LOG.info("Explore task [%s] is started." % raw_data['taskName'])

    nas_data = mongo_client.find_one('nas_manager', {'serverId': raw_data['sourceData']})

    if not nas_data:
        return

    if not check_nas_server_online(nas_data['serverId']):
        LOG.info("Explore timed task [%s] is stop because server is not online!" % raw_data['taskName'])
        return

    file_list = []
    total_size = update_nas_incremental_pcap(raw_data, nas_data, file_list)

    # 距上次执行任务没有数据包更新，直接返回
    if total_size == 0 or not file_list:
        mongo_client.update("back_explore", {"taskId": task_id}, {
            'process': 0.99,
            'replay_endTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S', ),
        })

        return

    hdp_rule_compile(task_id, raw_data, mongo_client)

    # 存入mongo，all pcap list, 定时任务只保存最近一次回放的pcap列表
    mongo_client.update("back_explore", {"taskId": task_id}, {
        "pcapInfo": file_list,
        "flowSize": total_size,
        "replayInterface": "0",
        # 第一次启动定时任务，需要更新celeryId
        # 由于定时任务一直执行，不能根据每次调用的任务生成celeryId，
        # 否则会造成启动一个定时任务，但是有多个celeryId，而取证页面是根据celeryId来进行任务的结果搜索
        # 与想要查询的结果不符，即定时任务在执行阶段celeryId不能改变，当手动重启定时任务时需要更新celeryId
        'celeryId': celery_id,
        'process': 0,
        'timedCeleryId': current_task.request.id,
        'startTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S') if raw_data['startTime'] == '' else raw_data[
            'startTime'],
        'endTime': ''
    })
    # 不必再等待线程结束，只提供执行信息，剩下的由回调函数处理
    for pcap_info in file_list:
        hdp_task_thread.submit(hdp_timed_pcap_process, pcap_info['pcapName'], task_id, celery_id)


# 定时任务轮询，检查是否启动定时任务，
@app.task()
def timed_task_poll():
    mongodb = BaseMongoDB("ndr")
    raw_data = mongodb.find(
        'back_explore',
        {'taskType': 'timedTask', "status": "running"},
        {
            'schedule_expr': 1, 'taskDelay': 1, 'preTime': 1, 'type': 1,
            'nextTime': 1, 'taskId': 1, 'timedCeleryId': 1, 'taskName': 1
        })

    if not raw_data:
        return

    # 更新定时任务上次执行时间和下次执行时间
    def update_timed_task_time(task, date):
        pre_time = task['nextTime']
        cron = croniter(task['schedule_expr'], date)
        next_time = cron.get_next(datetime) + timedelta(seconds=int(task['taskDelay']))

        mongodb.update(
            'back_explore',
            {'taskId': task['taskId']},
            {'preTime': pre_time, 'nextTime': next_time})

        LOG.info('\ntimed task [%s] update current time: %s, next task time: %s\n' % (
            task['taskName'], date.strftime("%Y-%m-%d %H:%M:%S"), next_time.strftime("%Y-%m-%d %H:%M:%S")
        ))

        return next_time

    current_time = datetime.now()

    for task in raw_data:
        next_excute_time = task['nextTime']
        # 如果当前时间大于下次执行时间，需要更新下次执行时间，否则任务永远不会执行
        if current_time - next_excute_time >= timedelta(seconds=1):
            next_excute_time = update_timed_task_time(task, current_time)

        if next_excute_time - current_time < timedelta(seconds=59):
            # 判断上次任务是否执行完成，未完成则需要停止
            if AsyncResult(task['timedCeleryId']).status != 'SUCCESS':
                app.control.revoke(task['timedCeleryId'], terminate=True)

            # 启动定时任务
            if task['type'] == 'pcap':
                start_timed_task.apply_async(args=[task['taskId']], countdown=int(task['taskDelay']))
            elif task['type'] == 'other':
                start_timed_file_task.apply_async(args=[task['taskId']], countdown=int(task['taskDelay']))
            update_timed_task_time(task, next_excute_time)

        # 启动定时任务
        # start_timed_task.apply_async(args=[task['taskId']], countdown=int(task['taskDelay']))


def get_cl_statistics_info(celery_id, start_time, end_time):
    def get_timestamp_func(ts):
        return int(time.time()) if ts == '' else int(time.mktime(time.strptime(ts, "%Y-%m-%d %H:%M:%S")))

    statistics = {}
    try:
        cl_client = DpilogResource()
        cond_sql = "where create_time>='%s' AND create_time<='%s' AND celeryId='%s'" % (
            get_timestamp_func(start_time), get_timestamp_func(end_time), celery_id)

        for proto in ClickHouseConfig.CH_ALL_PROTO:
            proto_sql = "select count(ts) as logCount from %s %s" % ('dpilog_' + proto, cond_sql)
            _, ch_data = cl_client.get_val_from_database(proto_sql)
            if ch_data and ch_data[0]['logCount'] != 0:
                statistics[proto] = ch_data[0]['logCount']

        # 统计总数据
        statistics['logCount'] = sum(statistics.values())
    except Exception as e:
        logging.error('get clickhouse statistics info failed! %s' % str(e))
        statistics = {}

    return statistics


# 所有探索任务结果刷新任务
@app.task()
def file_task_result_update():
    mongodb = BaseMongoDB("ndr")
    raw_data = mongodb.find('back_explore', {'status': 'running', 'type': 'other'},
                            {'pcapList': 0, 'exploredPcapList': 0})
    if not raw_data:
        return
    for replay_task in raw_data:
        es_return_datas = FileTaskResultUpdate().get(replay_task['celeryId'], len(replay_task['pcapInfo']))
        for es_return_data in es_return_datas['hits']['hits']:
            file_path = es_return_data['_source']['file_path'] + '/' + es_return_data['_source']['filename']
            if replay_task['taskType'] == 'timedTask':
                file_path = file_path.split(replay_task['taskId'])[-1]
            LOG.info('file_task_result_update, file_path : %s', file_path)
            # /var/ndr_datasource/tmp/ebee57222aef38df7875991e924e96a5/init_test_data/http_chunked_gzip.pcap
            mongodb.update_one("back_explore", {"taskId": replay_task['taskId'], 'pcapInfo.pcapName': file_path}, {
                "$set": {
                    "statisticsInfo": get_es_statistics_info(replay_task['celeryId'], "celeryId"),
                    "pcapInfo.$.pcapStatus": 'success'
                },
                "$addToSet": {
                    "exploredPcapList": file_path
                }
            })



# 所有探索任务结果刷新任务
@app.task()
def replay_task_result_update():
    mongodb = BaseMongoDB("ndr")
    # 以process来表示进度条百分比，以status判断ES是否刷新结束
    # process通过控制不会到100%，只能在此处设置为1，原因是因为还需要等待spark处理结束，因此回放完成不意味着任务完成；
    # status表示是否还需要刷新ES数据，可能spark处理数据会很多，在process等待过程中并不能处理结束，为了不漏数据需要更长的时间刷新；
    raw_data = mongodb.find('back_explore', {'status': 'running'}, {'pcapList': 0, 'exploredPcapList': 0})

    if not raw_data:
        return

    def update_database_statistics(celery_id, update_cl=True, update_es=True):
        update_data = {}
        if not update_cl and not update_es:
            return

        if update_es:
            update_data['statisticsInfo'] = get_es_statistics_info(replay_task['celeryId'], "celeryId")

        if update_cl:
            update_data['pcapStatisticsInfo'] = get_cl_statistics_info(replay_task['celeryId'],
                                                                       replay_task['startTime'], replay_task['endTime'])

        mongodb.update("back_explore", {"celeryId": celery_id}, update_data)

    for replay_task in raw_data:
        # 回放未结束 ,刷新ES和clickhouse，刷新进度条
        if not replay_task['replay_endTime']:
            update_database_statistics(replay_task['celeryId'])
            if len(replay_task['pcapInfo']):
                process = round(
                    len(list(filter(
                        lambda x: x['pcapStatus'] in ['success', 'failed', 'download failed'],  # 这三种状态都计入进度条
                        replay_task['pcapInfo']))) / len(replay_task['pcapInfo']), 2)
                mongodb.update("back_explore", {"celeryId": replay_task['celeryId']}, {
                    "process": 0.99 if process == 1 else process,
                    'replay_endTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S') if process == 1 else ''
                })
            continue

        # 回放已结束
        end_time = int(time.mktime(time.strptime(replay_task['replay_endTime'], "%Y-%m-%d %H:%M:%S")))
        now_time = int(time.time())
        # spark是2min处理一批数据（标准0min/2min），因此process等待时间是2min向后取整并加1min
        stop_task_time = end_time + 60 if end_time % 120 == 0 else (end_time // 120 + 1) * 120 + 60
        # ES刷新时间，任务结束后等待6min，之后不再刷新
        stop_update_time = end_time + 360
        # 单次任务处理
        if replay_task['taskType'] == 'singleTask':
            if replay_task['type'] == 'other':  # 文件回放不再等待刷新，直接完成
                if replay_task['process'] != 1:
                    mongodb.update('back_explore', {'celeryId': replay_task['celeryId']}, {
                        'process': 1, 'status': 'finish', "endTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                    LOG.info("Explore single task [%s] status is finish." % replay_task['taskName'])
                else:
                    continue

            if replay_task['process'] != 1:
                update_database_statistics(replay_task['celeryId'])
                if now_time >= stop_task_time:
                    mongodb.update('back_explore', {'celeryId': replay_task['celeryId']}, {
                        'process': 1, "endTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                    clean_task_tmp_files(replay_task['taskId'])
                    LOG.info("Explore single task [%s] process is completed." % replay_task['taskName'])
                continue
            # 此时进度已经完成，可以不再刷新clickhouse，但是继续刷新es
            if now_time < stop_update_time:
                update_database_statistics(replay_task['celeryId'], update_cl=False)
            else:
                mongodb.update('back_explore', {'celeryId': replay_task['celeryId']}, {'status': 'finish'})
                LOG.info("Explore single task [%s] status is finish." % replay_task['taskName'])
        # 定时任务不用设置process，一直是99%，也不用设置endTime，定时任务一直运行，因此status不能finish
        else:
            if now_time < stop_task_time:
                update_database_statistics(replay_task['celeryId'])
            elif now_time < stop_update_time:
                update_database_statistics(replay_task['celeryId'], update_cl=False)
            else:  # 超出时间则不再刷新数据
                LOG.info("Explore timed task [%s] update data is finish." % replay_task['taskName'])


# 在设备关机或后端重启后用于离线任务恢复
@app.task()
def recovery_single_task():
    mongo_client = BaseMongoDB("ndr")
    raw_data = mongo_client.find('back_explore', {'replay_endTime': '', 'taskType': 'singleTask'},
                                 {'pcapList': 0, 'exploredPcapList': 0})

    if not raw_data:
        return

    file_info = []
    explore_size = 0
    for task_info in raw_data:
        for pcap_info in task_info['pcapInfo']:
            if pcap_info['pcapStatus'] != 'completed':
                file_info.append(pcap_info)
            else:
                explore_size += pcap_info['pcapSize']

        if file_info:
            # pcap 文件处理
            LOG.info("Explore task [%s] is recovering..." % task_info['taskName'])
            LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]恢复中......' % task_info['taskName'])
            # 不必再等待线程结束，只提供执行信息，剩下的由回调函数处理
            for pcap_info in file_info:
                hdp_task_thread.submit(
                    hdp_pcap_process, pcap_info['pcapName'], task_info['taskId'], task_info['celeryId'])


# 启动文件分析任务
@app.task()
def start_file_task(task_id, file_list):
    if not file_list:
        LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='任务开始执行失败，文件列表为空。')
        return
    mongo_client = BaseMongoDB("ndr")
    # 从 mongo 查询 task_id 对应的参数详情
    task_info = mongo_client.find_one("back_explore", {"taskId": task_id})
    file_info = []
    total_size = 0
    
    celery_id = current_task.request.id

    for file in file_list:
        try:
            file_size = os.path.getsize(file)
            if file_size:
                file_info.append({'pcapName': file, 'pcapSize': file_size, 'pcapStatus': 'ready'})
                total_size += file_size
        except Exception as error:
            LogToDb().write_to_db(log_type=NdrLog.Type.RUN,
                                  event='探索任务[%s]获取 file 文件信息失败：%s。' % (task_info['taskName'], error))
            LOG.error("Get pcap info error.Reason: ", error)

    # 更新任务初始状态, all file list
    mongo_client.update("back_explore", {"taskId": task_id}, {
        "celeryId": celery_id,
        "status": "running",
        'process': 0,
        "replayInterface": "0",
        "startTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "pcapInfo": file_info,
        "endTime": "",
        'replay_endTime': '',
        "flowSize": total_size
    })

    for pcap_info in file_info:
        file_detection(task_id, pcap_info['pcapName'], celery_id)


# 停止文件分析任务
@app.task()
def stop_file_task(task_id):
    mongo_client = BaseMongoDB("ndr")
    celery_task = mongo_client.find_one("back_explore", {"taskId": task_id})
    if not celery_task:
        LOG.error("Explore task [%s] does not exist." % task_id)
        LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]停止失败：不存在。' % task_id)
        return
    celery_id = celery_task['celeryId'] if celery_task['taskType'] == 'singleTask' else celery_task['timedCeleryId']
    app.control.revoke(celery_id, terminate=True)
    LOG.info("Explore task [%s] is stopped." % task_id)
    mongo_client.update("back_explore",
                        {"taskId": task_id},
                        {"status": "finish",
                         "process": 1,
                         "endTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S',),
                         'replay_endTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S',)
                         })
    # TODO: 清除kafka中数据，采用什么方式？？？
    LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]停止执行。' % celery_task["taskName"])


# 立即启动文件分析任务
@app.task()
def start_timed_file_task(task_id):
    mongo_client = BaseMongoDB("ndr")
    raw_data = mongo_client.find_one('back_explore', {'taskId': task_id})

    if not raw_data:
        LOG.error('定时任务 %s 执行失败，未找到该任务' % task_id)
        return
    celery_id = current_task.request.id if raw_data['celeryId'] == '' else raw_data['celeryId']

    LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='探索任务[%s]开始执行。' % raw_data['taskName'])
    LOG.info("Explore task [%s] is started." % raw_data['taskName'])

    nas_data = mongo_client.find_one('nas_manager', {'serverId': raw_data['sourceData']})

    if not nas_data:
        return

    if not check_nas_server_online(nas_data['serverId']):
        LOG.info("Explore timed task [%s] is stop because server is not online!" % raw_data['taskName'])
        return

    file_list = []

    total_size = update_nas_incremental_file(raw_data, nas_data, file_list)

    # 距上次执行任务没有数据包更新，直接返回
    if total_size == 0 or not file_list:
        mongo_client.update("back_explore", {"taskId": task_id}, {
            'process': 0.99,
            'replay_endTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S', ),
        })

        return

    # 存入mongo，all pcap list, 定时任务只保存最近一次回放的pcap列表
    mongo_client.update("back_explore", {"taskId": task_id}, {
        "pcapInfo": file_list,
        "flowSize": total_size,
        "replayInterface": "0",
        # 第一次启动定时任务，需要更新celeryId
        # 由于定时任务一直执行，不能根据每次调用的任务生成celeryId，
        # 否则会造成启动一个定时任务，但是有多个celeryId，而取证页面是根据celeryId来进行任务的结果搜索
        # 与想要查询的结果不符，即定时任务在执行阶段celeryId不能改变，当手动重启定时任务时需要更新celeryId
        'celeryId': celery_id,
        'process': 0,
        'timedCeleryId': current_task.request.id,
        'startTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S') if raw_data['startTime'] == '' else raw_data[
            'startTime'],
        'endTime': ''
    })

    for pcap_info in file_list:
        timed_file_download(pcap_info['pcapName'], task_id, celery_id)


def encrypt(fpath, algorithm):
    with open(fpath, 'rb') as f:
        return hashlib.new(algorithm, f.read()).hexdigest()


def crc(fpath):
    hash = 0
    for eachLine in open(fpath, 'rb'):
        hash = zlib.crc32(eachLine, hash)
    return hash


# 文件检测推送到 kafka
def file_detection(task_id, file, celery_id):
    # 连接kafka
    producer = KafkaProducer(bootstrap_servers='kafka:9092', value_serializer=lambda m: json.dumps(m).encode())
    filePath, fileName = os.path.split(file)
    md5 = encrypt(file, 'md5')
    sha1 = encrypt(file, 'sha1')
    sha256 = encrypt(file, 'sha256')
    crc32 = crc(file)
    size = os.stat(file).st_size
    _, msg = run_command("file -b " + file + "| awk '{print $1}'", timeout=3)
    file_type = msg.replace('\n', '') if msg else "unknown"
    nowTime = int(time.time())
    data = {
        "ts": nowTime,
        "conn_id": "",
        "net_ext_flag": "",
        "src_mac": "00:0E:1E:80:13:00",
        "dst_mac": "00:0E:1E:80:13:01",
        "orig_ip": "0.0.0.0",
        "orig_ipv4": "0.0.0.0",
        "orig_ipv6": "",
        "orig_port": 0,
        "resp_ip": "0.0.0.0",
        "resp_ipv4": "0.0.0.0",
        "resp_ipv6": "",
        "resp_port": 0,
        "taskId": task_id,
        "celeryId": celery_id,
        "topic": "file",
        "intf": 222,
        "pcap_filename": "",
        "proto": "other",
        "application": "",
        "file_origin": "api_submit",
        "file_path": filePath,
        "fuid": "",
        "filename": fileName,
        "filetype": file_type,
        "filesize": size,
        "md5" : md5,
        "crc32": str(crc32),
        "sha1": sha1,
        "sha256": sha256,
        "create_time": nowTime,
    }
    producer.send('file', data)


def update_nas_incremental_file(task_data, nas_data, file_list):
    total_size = 0
    if nas_data['serverType'] == 'nas':
        try:
            for monitor_path in nas_data['monitor_dirs']:
                ret, _ = run_command('ls %s' % monitor_path, timeout=3)
                if not ret:
                    LOG.error("Explore timed task [%s] monitor dir [%s] is not exists!" % (
                        task_data['taskName'], monitor_path))
                    continue
                for dir_path, _, files in os.walk(monitor_path):
                    for file in files:
                        file_name = os.path.join(dir_path, file)
                        file_size = os.path.getsize(file_name)
                        if file_name not in task_data['exploredPcapList'] \
                                and file_size != 0:
                            total_size += file_size
                            file_list.append({
                                'pcapName': file_name,
                                'pcapSize': file_size,
                                'pcapStatus': 'ready'})
        except Exception as error:
            LOG.error("Explore timed task [%s] nas error: %s" % (task_data['taskName'], str(error)))
            return 0
    elif nas_data['serverType'] == 'ftp':
        LOG.info("ftp")
        try:
            ftp, rst = ftp_server_connect(
                nas_data['host'], nas_data['port'], nas_data['username'], nas_data["password"])
            if not rst:
                LOG.error("Explore timed task [%s] can not conncet ftp server!" % task_data['taskName'])
                return 0

            for monitor_dir in nas_data['monitor_dirs']:
                ftp_pcap_list = ftp_server_recurse_file(ftp, monitor_dir)
                for file in ftp_pcap_list:
                    file_size = ftp.size(file)
                    if file not in task_data['exploredPcapList'] and file_size != 0:
                        total_size += file_size
                        file_list.append({
                            'pcapName': file,
                            'pcapSize': file_size,
                            'pcapStatus': 'ready'})
        except ftplib.error_perm as error:
            LOG.error("Explore timed task [%s] ftp error: %s" % (task_data['taskName'], str(error)))
            return 0

    return total_size


def ftp_server_recurse_file(ftp, start_dir):
    recurse_file_list = []
    ftp.cwd(start_dir)
    dir_res = []
    ftp.dir('.', dir_res.append)  # 将当前目录下所有文件加入到dir_res
    for file_info in dir_res:
        file = file_info.split(" ")[-1]
        file_path = os.path.join(ftp.pwd(), file)
        if file_info.startswith("d"):
            recurse_file_list += ftp_server_recurse_file(ftp, file_path)
            ftp.cwd('..')
        else:
            recurse_file_list.append(file_path)
    return recurse_file_list


def timed_file_download(pcap_file, task_id, celery_id):
    """
    下载文件到宿主机
    """
    mg = BaseMongoDB("ndr")
    task_data = mg.find_one('back_explore', {'taskId': task_id}, {'sourceData': 1, 'status': 1})
    if not task_data or task_data['status'] != 'running':
        LOG.error('定时任务 %s 执行失败，未找到该任务' % task_id)
        mg.update(
            "back_explore",
            {"taskId": task_id, 'pcapInfo.pcapName': pcap_file},
            {"pcapInfo.$.pcapStatus": 'download failed'})
        return
    nas_data = mg.find_one('nas_manager', {'serverId': task_data['sourceData']})
    if not nas_data:
        mg.update(
            "back_explore",
            {"taskId": task_id, 'pcapInfo.pcapName': pcap_file},
            {"pcapInfo.$.pcapStatus": 'download failed'})
        LOG.error('定时任务 %s 执行失败，未找到该数据源' % task_id)
        return
    if not os.path.exists(NASConfig.PcapPath):
        os.system('mkdir -p %s' % NASConfig.PcapPath)

    local_path = os.path.join(NASConfig.PcapPath, task_id)
    if not os.path.exists(local_path):
        os.mkdir(local_path)
    status = ''
    dl_pcap = ''
    try:
        if nas_data['default_status']:
            if nas_data['serverType'] == 'ftp':
                ftp_handler, rst = ftp_server_connect(
                    nas_data['host'], nas_data['port'], nas_data['username'], nas_data["password"])
                if not rst:
                    LOG.error("nas file download failed. Reason: %s" % str(ftp_handler))
                    status = 'download failed'
                else:
                    local_dir = os.path.join(local_path, os.path.dirname(pcap_file)[1:])
                    if not os.path.exists(local_dir):
                        os.makedirs(local_dir)
                    local_pcap_file = os.path.join(local_path, pcap_file[1:])
                    with open(local_pcap_file, 'wb') as write_file:
                        ftp_handler.retrbinary('RETR {0}'.format(pcap_file), write_file.write, 1024)
                    if local_pcap_file.endswith(".pcap"):
                        pcap_fix(local_pcap_file)
                    LOG.info("ftp file [%s] download success." % pcap_file)
                    ftp_server_disconnect(ftp_handler)
                    dl_pcap = os.path.join(NASConfig.PcapPath, task_id, pcap_file[1:])  # 去掉最开始的'/'
                    status = 'download success'
            elif nas_data['serverType'] == 'nas':
                file_path = os.path.join(NASConfig.Path, nas_data['aliasName'])
                # 判断文件是否存在，有可能在下载的过程中断开连接
                ret, msg = run_command('ls %s' % pcap_file, timeout=CommandTimeOut.timeout)
                if not ret:
                    LOG.error("nas file download failed. Reason: %s" % msg)
                    status = 'download failed'
                else:
                    local_dir = os.path.join(local_path, os.path.dirname(pcap_file[len(file_path) + 1:]))
                    if not os.path.exists(local_dir):
                        os.makedirs(local_dir)
                    cmd = 'cp %s %s' % (pcap_file, local_dir)
                    ret, msg = run_command(cmd, timeout=CommandTimeOut.timeout)
                    if not ret:
                        LOG.error("nas file download failed. Reason: %s" % msg)
                        status = 'download failed'
                    else:
                        if pcap_file.endswith(".pcap"):
                            pcap_fix(os.path.join(local_dir, pcap_file))
                        # 去掉/mnt/nas/aliasName前缀路径
                        dl_pcap = os.path.join(NASConfig.PcapPath, task_id,
                                               pcap_file[len(os.path.join(NASConfig.Path, nas_data['aliasName'])) + 1:])
                        status = 'download success'
        else:
            LOG.error("nas file download failed. Reason: %s" % '未连接')
            status = 'download failed'
    except Exception as error:
        LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='file [%s]文件下载失败: %s' % (pcap_file, error))
        status = 'download failed'

    mg.update(
        "back_explore",
        {"taskId": task_id, 'pcapInfo.pcapName': pcap_file},
        {"pcapInfo.$.pcapStatus": status})

    if status == 'download success' and dl_pcap:
        file_detection(task_id, dl_pcap, celery_id)
