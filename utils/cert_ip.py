# -*- coding: utf-8 -*-

import pandas as pd
import ipaddress


def search_unit(ip):
    try:
        # 单位信息文件
        src_xlsx_file = "/opt/ndr/utils/unit-1225.xlsx"
        xlsx_ip_units = get_xlsx_data(src_xlsx_file)
        unit = get_ip_unit(xlsx_ip_units, ip)
        return unit
    except Exception as e:
        print(e)
        return ""


def get_ip_unit(xlsx_ip_units, ip):
    for unit, networks in xlsx_ip_units.items():
        for network in networks:
            if check_ip_in_network(ip, network):
                return unit
    return ""


def get_xlsx_data(xlsxfile):
    # 读取XLSX文件
    xlsxdata = pd.read_excel(xlsxfile)
    xlsx_ip_units = {}
    for index, row in xlsxdata.iterrows():
        unit = row.iloc[0]
        networks = row.iloc[1]
        if pd.isna(unit) or pd.isna(networks):  # 跳过空行
            continue
        # ip_pool = []
        # for net in networks.split():
        #     ip_pool += get_ip_addresses(net, unit)
        xlsx_ip_units[unit] = networks.split()
        # break
    # print(xlsx_ip_units)

    return xlsx_ip_units


def check_ip_in_network(ip_address, network):
    try:
        ip = ipaddress.ip_address(ip_address)
        if "/" in network:
            net = ipaddress.ip_network(network)
        elif "-" in network:
            start_ip, end_ip = network.split("-")
            if '.' not in end_ip:
                end_ip = f"{start_ip.rsplit('.', 1)[0]}.{end_ip}"
            net = ipaddress.ip_network(f"{start_ip}-{end_ip}")
        else:
            return False
        return ip in net
    except ValueError:
        return False


if __name__ == '__main__':
    main()


