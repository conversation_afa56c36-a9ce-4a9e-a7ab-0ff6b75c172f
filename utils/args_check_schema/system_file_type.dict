{"type": "object", "required": ["file_type"], "properties": {"file_type": {"type": "object", "properties": {"office": {"type": "number", "default": 1, "enum": [1, 0]}, "pdf": {"type": "number", "default": 1, "enum": [1, 0]}, "rtf": {"type": "number", "default": 0, "enum": [1, 0]}, "pe": {"type": "number", "default": 0, "enum": [1, 0]}, "elf": {"type": "number", "default": 1, "enum": [1, 0]}, "zip": {"type": "number", "default": 0, "enum": [1, 0]}, "gzip": {"type": "number", "default": 0, "enum": [1, 0]}, "7z": {"type": "number", "default": 0, "enum": [1, 0]}, "bz2": {"type": "number", "default": 0, "enum": [1, 0]}, "rar": {"type": "number", "default": 0, "enum": [1, 0]}, "tar": {"type": "number", "default": 0, "enum": [1, 0]}, "xz": {"type": "number", "default": 0, "enum": [1, 0]}, "jsp": {"type": "number", "default": 0, "enum": [1, 0]}, "asp": {"type": "number", "default": 0, "enum": [1, 0]}, "php": {"type": "number", "default": 0, "enum": [1, 0]}, "shell": {"type": "number", "default": 0, "enum": [1, 0]}, "python": {"type": "number", "default": 0, "enum": [1, 0]}, "flash": {"type": "number", "default": 0, "enum": [1, 0]}, "gif": {"type": "number", "default": 0, "enum": [1, 0]}, "bpg": {"type": "number", "default": 0, "enum": [1, 0]}, "png": {"type": "number", "default": 0, "enum": [1, 0]}, "jpeg": {"type": "number", "default": 0, "enum": [1, 0]}, "apk": {"type": "number", "default": 0, "enum": [1, 0]}, "java": {"type": "number", "default": 0, "enum": [1, 0]}, "lnk": {"type": "number", "default": 0, "enum": [1, 0]}, "html": {"type": "number", "default": 0, "enum": [1, 0]}, "xml": {"type": "number", "default": 0, "enum": [1, 0]}}}}}