# -*- coding: utf-8 -*-
# @Time    : 2019-09-02 18:19
# <AUTHOR> Shark
# @File    : test_alert_detail.py
# @Software: PyCharm
import json
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_alert.alert_detail import AlertDetail


class TestAlertDetail(TestCase):
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_alertId_not_exists(self):
        resp = self.client.get(
            api.url_for(AlertDetail)
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_alertId_not_exist_in_es(self):
        resp = self.client.get(
            api.url_for(AlertDetail,
                        alertId="MotKzWwBDckSjmX-xS90"
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)

    def test_all_true(self):
        resp = self.client.get(
            api.url_for(AlertDetail,
                        alertId="UOlG8WwBTPAsp3nawJCP"
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)
