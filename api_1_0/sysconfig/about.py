
# -*- coding: utf-8 -*-
# @Time    : 2020-03-23 08:10
# <AUTHOR> wu
# @File    : about.py
# @Software: PyCharm

"""about"""
from os import system
from flask_restful import Resource
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from utils.database import MongoDB

LOG = get_ndr_logger('api_log', __file__)


class About(Resource):
    """关于"""
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    def get(self):
        """about"""
        about = self.mongodb.find_one('about', {}, {"_id": 0, "type": 0})
        if about:
            return flask_response("", True, about)
        return flask_response("Get about info failed.", False, {})

    def set(self):
        """ 系统初始化时，获取相关参数写入 mongo ，部分信息需要从 license 中获取"""
        about = {
            "basic_function": "基础流量检测",
            "certificate_num": "D80FE1B5E57F4D5A9AAF5AFC671EC0B9",
            "certificate_type": "正版授权",
            "certifications": "XXXXXX",
            "company_name_cn": "北京知道创宇信息技术有限公司",
            "company_name_en": "knownsec",
            "device_id": "D0AC-5C98-702F-1EFA-3FC5",
            "product_form": "标准版",
            "product_name_cn": "网络检测响应系统",
            "product_name_en": "NDR",
            "product_type": "ks-P100",
            "release_num": "1R0F0B0"
        }

        self.mongodb.insert_one("about", about)
