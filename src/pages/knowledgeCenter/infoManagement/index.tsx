import React, { useState } from 'react';
import { Tabs, Form } from 'antd';
import Info from './info';
import Upgrade from "./upgrade";
const { TabPane } = Tabs;
const Index = () => {  
  const [form] = Form.useForm();
  const [activeKey, setactiveKey] = useState('1');
  const onChange = (value: React.SetStateAction<string>) => {
    setactiveKey(value);
  };
  return (
    <Tabs activeKey={activeKey} onChange={onChange}>
      <TabPane tab="情报管理" key="1">
        {activeKey === '1' ? <Info form={form}/> : ''}
      </TabPane>
      <TabPane tab="情报库升级" key="2">
        {activeKey === '2' ? <Upgrade /> : ''}
      </TabPane>
    </Tabs>
  );
};
export default Index;
