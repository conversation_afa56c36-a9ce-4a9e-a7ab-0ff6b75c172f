import React, { CSSProperties, Fragment } from 'react';
import moment from 'moment';
import cs from 'classnames';

import { Divider, Empty, List, Modal, Skeleton, Tooltip } from 'antd';
import { useEmailSendRecvData } from './hooks';

import { EmailSendRecvInfo } from '../types';
import { ArrowDownOutlined, ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';

import { ipReg, dateReg } from '../constants';

import styleModule from './style.less'

import type { Props } from './EmailDetailContent'

function EmailSendReciveModal({ data, open, onCancel }: Props & {
  open: boolean;
  onCancel: () => void
}): JSX.Element {

  return (
    <Modal
      open={open}
      width={1100}
      title="收发信息"
      onCancel={onCancel}
      destroyOnClose
      footer={null}
      bodyStyle={{ maxHeight: 750, overflow: 'auto' }}
    >
      {
        !!data.msg_id && <EmailSendReciveContent data={data} />
      }
    </ Modal>
  )
}

export default EmailSendReciveModal;

function EmailSendReciveContent({ data }: Props) {
  const { data: sendRecvData, loading } = useEmailSendRecvData(data)

  const renderPop3 = (popArr: EmailSendRecvInfo['pop3']) => {
    if (!popArr.length) return null
    return (
      <>
        <div className='flex flex-col gap-4'>
          <span className='font-size-8 font-500'>POP3:</span>
          {
            popArr.map(({ from, pop3_ip, src_ip, date, to }, i) => {
              return (
                <div key={i} className='flex items-center justify-between gap-10'>
                  <span className={styleModule.email_ip_block}>{moment(date || new Date()).format('YYYY-MM-DD HH:mm:ss')}</span>
                  <Block className={styleModule.email_ip_block} title="收件人IP" content={src_ip} />
                  <ArrowLeftOutlined />
                  <Block className={styleModule.email_sender_block} title="收件人" content={to[i]} />
                  <ArrowLeftOutlined />
                  <Block className={styleModule.email_ip_block} title="POP3服务器" content={pop3_ip} />
                </div>
              )
            })
          }
        </div>
        <Divider />
      </>

    )
  }

  const renderImap = (imapArr: EmailSendRecvInfo['imap']) => {
    if (!imapArr.length) return null
    return (
      <div className='flex flex-col gap-4'>
        <span className='font-size-8 font-500'>IMAP:</span>
        {
          imapArr.map(({ from, src_ip, imap_ip, date }, i) => {
            return (
              <div key={i} className='flex items-center justify-between gap-10'>
                <span className={styleModule.email_ip_block}>{moment(date || new Date()).format('YYYY-MM-DD HH:mm:ss')}</span>
                <Block className={styleModule.email_ip_block} title="收件人IP" content={src_ip} />
                <ArrowLeftOutlined />
                <Block className={styleModule.email_sender_block} title="收件人" content={from} />
                <ArrowLeftOutlined />
                <Block className={styleModule.email_ip_block} title="IMAP服务器" content={imap_ip} />
              </div>
            )
          })
        }
      </div>
    )
  }

  const renderSmtp = (smtpArr: EmailSendRecvInfo['smtp']) => {
    if (!smtpArr.length) return null;
    const { received, from, src_ip, date, smtp_ip } = smtpArr[0];
    const restReceivedArr = received.split('||').reverse().slice(1).map((reveivedString) => {
      const ip = reveivedString.match(ipReg)?.[0]
      if (!ip) {
        return false
      }
      const date = reveivedString.match(dateReg)?.[0] || '未知日期';
      return { ip, date }
    }).filter(Boolean)

    return (
      <>
        <div className='flex flex-col gap-2'>
          <span className='font-size-8 font-500'>SMTP:</span>
          <div className='flex items-center gap-10 justify-between'>
            <span className={styleModule.email_ip_block}>{moment(new Date(date)).format('YYYY-MM-DD HH:mm:ss')}</span>
            <Block title="发件人源IP" content={src_ip} />
            <ArrowRightOutlined />
            <Block className={styleModule.email_sender_block} title="发件人" content={from} />
            {
              smtp_ip && (
                <>
                  <ArrowRightOutlined />
                  <Block className={styleModule.email_ip_block} title="SMTP服务器1" content={smtp_ip} />
                </>
              )
            } 
          </div>
          {
            restReceivedArr?.map(({ ip, date }: any, index) => (
              <Fragment key={index}>
                <div className='flex items-center justify-end' >
                  <Block className={styleModule.email_ip_block} title={<ArrowDownOutlined />} />
                </div>
                <div className='flex items-center justify-between'>
                  <span className={styleModule.email_ip_block}>{moment(date).format('YYYY-MM-DD HH:mm:ss')}</span>
                  <Block className={styleModule.email_ip_block} title={`SMTP服务器${index + 2}`} content={ip} />
                </div>
              </Fragment>

            ))
          }
        </div>
        <Divider />
      </>
    )
  }

  if (loading && !sendRecvData) return <Skeleton />

  if (Object.keys(sendRecvData!).every((key) => !sendRecvData?.[key]?.length)) {
    return <Empty />
  }

  return (
    <div className='flex justify-between'>
      <div style={{ width: 750 }}>
        {renderSmtp(sendRecvData?.smtp || [])}
        {renderPop3(sendRecvData?.pop3 || [])}
        {renderImap(sendRecvData?.imap || [])}
      </div>
      <List
        size="small"
        header="收件人列表"
        bordered
        style={{ width: 230, maxHeight: 500 }}
        dataSource={sendRecvData?.totalTo}
        renderItem={item => <Tooltip placement="top" title={item}>
          <List.Item>
            <span className='truncate'>{item}</span>
          </List.Item>
        </Tooltip>
        }
      />
    </div>
  )
}

const Block = (props: { title: any, content?: any, className?: string, style?: CSSProperties }) => {
  return (
    <div style={props.style} className={cs('flex flex-col items-center', props?.className)}>
      <div className='w-full font-500 text-center'>{props.title}</div>
      <div className='w-full text-center' title={props?.content} >{props?.content}</div>
    </div>
  )
}