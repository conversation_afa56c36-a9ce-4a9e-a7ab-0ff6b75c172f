
.threat_file_analysis_query_form {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  row-gap: 10px;
  width: 100%;
  padding: 10px 0;
  :global{
    .ant-row {
      width: auto;
      padding: 0;
    }
    .ant-form-item-with-help{
      margin-bottom: 0;
    }
    .ant-form-item-label {
      min-width: 85px;
    }
    .ant-form-item-control {
      width: 0;
      text-wrap: nowrap;
    }
  }
}

.threat_file_card {
  width: 0;
  display: flex;
  flex-direction: column;
  height: 350px;
  flex: 1;
}

.file_analysis_wrap{
  height: calc(100vh - 73px);
  overflow: auto;
  display: flex;
  flex-direction: column;

  .query_box {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #f0f2f5;
  }

  .pieCharts {
    display: flex;
    gap: 5px;

  }

}

@media (max-width: 1200px) {
  .file_analysis_wrap{
      .pieCharts {
        flex-direction: column;

        .threat_file_card{
          width: auto;
        }
      }
    
  }
}

.severity_tag{
  // border-radius: 16px;
  // padding: 2px 10px;
  // font-weight: bold;
}