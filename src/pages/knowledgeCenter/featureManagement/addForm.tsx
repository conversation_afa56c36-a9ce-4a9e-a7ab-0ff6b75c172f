import React, { forwardRef, useEffect, useState } from 'react';
import { Form, Input, Select, InputNumber } from 'antd';
import { vulTypeOptions, ipOption } from '@/utils/enumList';
import { threatClassify } from '@/services/alarm';
const { Option } = Select;
const { TextArea } = Input;
const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const AddForm = forwardRef((props: any, ref: any) => {
  const { value, killchainList } = props;
  const [form] = Form.useForm();
  const [threatClassifyList, setthreatClassifyList] = useState([]);
  // 威胁分类
  const getThreatClassify = () => {
    threatClassify().then(res => {
      setthreatClassifyList(res.data);
    });
  };
  useEffect(() => {
    getThreatClassify();
  }, []);

  return (
    <Form ref={ref} form={form} initialValues={value} {...layout}>
      <div>
        <div className="grid grid-cols-2 pr-4">
          <Form.Item label="创建者" name="author">
            <Input />
          </Form.Item>
          <Form.Item
            label="威胁得分"
            name="threatScore"
            rules={[
              {
                required: true,
                message: '请输入威胁得分',
              },
            ]}
          >
            <InputNumber max={100} min={0} />
          </Form.Item>
          <Form.Item label="协议" name="appProto">
            <Input />
          </Form.Item>
          <Form.Item label="漏洞信息" name="alterInfo">
            <Input />
          </Form.Item>
          <Form.Item
            label="告警名称"
            name="vulName"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="0day漏洞"
            name="is0day"
            rules={[
              {
                required: true,
                message: '请选择0day漏洞',
              },
            ]}
          >
            <Select>
              <Option value={true}>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="漏洞类型"
            name="vulType"
            rules={[
              {
                required: true,
                message: '请填写漏洞类型',
              },
            ]}
          >
            <Select>
              {vulTypeOptions.map(item => {
                return (
                  <Option value={item.key} key={item.key}>
                    {item.key}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="攻击者IP"
            name="attackIp"
            rules={[
              {
                required: true,
                message: '请选择攻击者IP',
              },
            ]}
          >
            <Select>
              {ipOption.map(item => {
                return (
                  <Option key={item.key} value={item.value}>
                    {item.key}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item label="CVE编号" name="cve">
            <Input placeholder="CVE-XXXX-XX.." />
          </Form.Item>
          <Form.Item
            label="受害者IP"
            name="victimIp"
            rules={[
              {
                required: true,
                message: '请选择受害者IP',
              },
            ]}
          >
            <Select>
              {ipOption.map(item => {
                return (
                  <Option key={item.key} value={item.value}>
                    {item.key}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="杀伤链阶段"
            name="lockheedKillchainEN"
            rules={[
              {
                required: true,
                message: '请选择杀伤链阶段',
              },
            ]}
          >
            <Select>
              {killchainList.map((item: any) => {
                return (
                  <Option
                    value={item.lockheedKillchainEN}
                    key={item.lockheedKillchainEN}
                  >
                    {item.lockheedKillchainCN}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="威胁分类"
            name="threatFlag"
            rules={[
              {
                required: true,
                message: '请选择威胁分类',
              },
            ]}
          >
            <Select>
              {threatClassifyList.map((item: any) => {
                return (
                  <Option value={item.threatFlag} key={item.threatFlag}>
                    {item.threatFlagCN}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </div>
        <Form.Item
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          label="特征内容"
          name="featureContent"
          className="!pr-4"
          rules={[
            {
              required: true,
              message: '请输入特征内容',
            },
          ]}
        >
          <TextArea
            autoSize={{
              minRows: 8,
            }}
            placeholder="请勿填写特征编号(sid)自动生成"
          />
        </Form.Item>
      </div>
    </Form>
  );
});

export default AddForm;
