import { Button, Toolt<PERSON>, Table, ConfigProvider, Empty } from 'antd';
import React, { useEffect, useState } from 'react';
import { DownloadOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { getFileSecIP, getFileDstIP, getFileUrl, getFileEmail, getFileIP } from '@/services/collect';
import { parseQueryString } from '@/utils/utils';

import cn from 'classnames';
import s from './style.less';

interface FileListProps {
  className?: string;
  type: string;
  params: any;
}
const FileList = ({ className, type, params }: FileListProps) => {
  const [sort, setSort] = useState('desc');
  const [data, setData] = useState([]);
  const tableType = {
    srcIP: {
      title: '文件下载源IP',
      curl: getFileIP,
      down: (query: any) => `file/export/ip${parseQueryString({ ...query, ip: 'src_ip' })}`,
      columns: [
        {
          title: '源IP',
          width: '80%',
          dataIndex: 'ip',
          key: 'ip',
          render(value) {
            return (
              <Tooltip title={value} key={value}>
                {value}
              </Tooltip>
            );
          },
        },
        {
          title: '数量',
          dataIndex: 'total',
          key: 'total',
          showSorterTooltip: false,
          sorter: true,
          sortOrder: sort === 'desc' ? 'descend' : 'ascend',
        },
      ],
    },
    url: {
      title: '文件相关URL',
      curl: getFileUrl,
      down: (query: any) => `file/export/url${parseQueryString(query)}`,
      columns: [
        {
          title: 'HTTP下载URL',
          width: '80%',
          dataIndex: 'url',
          key: 'url',
          render(value) {
            return (
              <Tooltip title={value} key={value}>
                {value}
              </Tooltip>
            );
          },
        },
        {
          title: '数量',
          dataIndex: 'total',
          key: 'total',
          showSorterTooltip: false,
          sorter: true,
          sortOrder: sort === 'desc' ? 'descend' : 'ascend',
        },
      ],
    },
    dstIP: {
      title: '文件下载目的IP',
      curl: getFileIP,
      down: (query: any) => `file/export/ip${parseQueryString({ ...query, ip: 'dst_ip' })}`,
      columns: [
        {
          title: '目的IP',
          width: '80%',
          dataIndex: 'ip',
          key: 'ip',
          render(value) {
            return (
              <Tooltip title={value} key={value}>
                {value}
              </Tooltip>
            );
          },
        },
        {
          title: '数量',
          dataIndex: 'total',
          key: 'total',
          showSorterTooltip: false,
          sorter: true,
          sortOrder: sort === 'desc' ? 'descend' : 'ascend',
        },
      ],
    },
    email: {
      title: '文件相关邮箱',
      curl: getFileEmail,
      down: (query: any) => `file/export/email${parseQueryString(query)}`,
      columns: [
        {
          title: '发件人',
          dataIndex: 'sender',
          key: 'sender',
          render(value) {
            return (
              <Tooltip title={value} key={value}>
                <span>{value}</span>
              </Tooltip>
            );
          },
        },
        {
          title: '收件人',
          dataIndex: 'receiver',
          key: 'receiver',
          render(value) {
            return (
              <Tooltip title={value} key={value}>
                <span>{value}</span>
              </Tooltip>
            );
          },
        },
        {
          title: '数量',
          dataIndex: 'total',
          key: 'total',
          showSorterTooltip: false,
          sorter: true,
          sortOrder: sort === 'desc' ? 'descend' : 'ascend',
        },
      ],
    },
  };
  const handleChange = (pagination, filters, sorter) => {
    const sort_ = sorter.order === 'descend' ? 'desc' : 'asc';
    setSort(sort_);
  };
  const getData = async () => {
    const params_ = { ...params, sort };
    const arr = { dstIP: 'dst_ip', srcIP: 'src_ip' };
    arr[type] && (params_.ip = arr[type]);
    const res = await tableType[type].curl(params_);
    if (res.flag) {
      setData(res.data);
    }
  };
  const downFile = async (format: number) => {
    const query = { ...params, sort, type: format };
    const url = tableType[type].down(query);
    window.location.href = `${window.location.origin}/mica-api/api/v1/${url}`;
  };
  useEffect(() => {
    !isEmpty(params) && getData();
  }, [params, sort]);
  return (
    <div className={cn('bg-d0 pa-1', s.fileList, className)}>
      <p className="c-t4">{tableType[type].title}</p>
      <ConfigProvider renderEmpty={Empty}>
        <Table
          className="h-13rem overflow-y-auto"
          columns={tableType[type].columns}
          dataSource={data}
          onChange={handleChange}
          pagination={false}
        />
      </ConfigProvider>
      {data[0] && (
        <p className="mt-2">
          导出：
          <Button
            type="link"
            className="!px-0"
            onClick={() => {
              downFile(1);
            }}
          >
            原始数据
            <DownloadOutlined className="!mx-0" />
          </Button>
          <Button
            type="link"
            className="!px-0 ml-2"
            onClick={() => {
              downFile(2);
            }}
          >
            格式化后的数据
            <DownloadOutlined className="!mx-0" />
          </Button>
        </p>
      )}
    </div>
  );
};
export default FileList;
