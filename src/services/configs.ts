/*
 * @Author: 田浩
 * @Date: 2021-07-19 15:47:54
 * @LastEditors: fangh3
 * @LastEditTime: 2022-06-30 10:41:53
 * @Descripttion:
 */
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';
// 保存任务配置
export const saveForm = (data: any) => {
  return request(`system/system_config`, {
    method: 'POST',
    data,
  });
};

// 获取基础配置
export const getTaskData = () => {
  return request(`system/system_config`, {
    method: 'GET',
  });
};


// 设置风暴抑制基础配置
export const setAlermData = (data: { performance: string; alarmFilter: boolean, alarmMerge: string, alarmOut: string }) => {
  return request(`system/system_alarm`, {
    method: 'POST',
    data
  });
};

export const getSysConfig = () => {
  return request(`ip/update`, {
    method: 'GET',
  });
};

export const saveSysConfig = (data: any) => {
  return request(`ip/update`, {
    method: 'POST',
    data,
  });
};

export const whiteList = (data: any) => {
  return request(`self_signature_white${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const editWhite = (data: any) => {
  return request(`self_signature_white`, {
    method: 'put',
    data,
  });
};

export const addWthite = (data: any) => {
  return request(`self_signature_white`, {
    method: 'post',
    data,
  });
};

export const deleteWhite = (data: any) => {
  return request(`self_signature_white`, {
    method: 'delete',
    data,
  });
};

export const getWork = () => {
  return request(`system/interface`, {
    method: 'get',
  });
};

export const saveWorkStatus = (data: any) => {
  return request(`system/interface`, {
    method: 'put',
    data,
  });
};

export const getPolicy = () => {
  return request(`system/fss_policy/config`, {
    method: 'get',
  });
};
export const submitPolicy = (data: any) => {
  return request(`system/fss_policy/config`, {
    method: 'post',
    data,
  });
};
export const addPolicyList = (data: any) => {
  return request(`system/fss_policy`, {
    method: 'post',
    data,
  });
};

export const getPolicyList = (data: any) => {
  return request(`system/fss_policy${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const delPolicyData = (data: any) => {
  return request(`system/fss_policy`, {
    method: 'delete',
    data,
  });
};



export const clearData = () => {
  return request('system/delete_data', {
    method: 'post',
  });
};

export const getInterfaceList = (data: any) => {
  if (data) {
    return request(`system/interface_forword/${data}`);
  } else {
    return request(`system/interface_forword`);
  }
};

export const addInterface = (data: any) => {
  return request(`system/interface_forword`, {
    method: 'post',
    data,
  });
};

export const deleteInterface = (data: any) => {
  return request(`system/interface_forword/${data}`, {
    method: 'delete',
    data,
  });
};

export const changeInterfaceStatus = (data: any) => {
  return request(`system/interface_forword`, {
    method: 'put',
    data,
  });
};

export const getsecurityPolicyList = (data: any) => {
  return request(`security_policy/single${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const addsecurityPolicy = (data: any) => {
  return request(`security_policy/single`, {
    method: 'post',
    data,
  });
};

export const deletesecurityPolicy = (data: any) => {
  return request(`security_policy/single`, {
    method: 'delete',
    data,
  });
};

export const editsecurityPolicy = (data: any) => {
  return request(`security_policy/single`, {
    method: 'post',
    data,
  });
};

export const sortsecurityPolicy = (data: any) => {
  return request(`security_policy/single`, {
    method: 'put',
    data,
  });
};

export const exportSecurityPolicy = () => {
  return request(`security_policy/export`, {
    method: 'post',
  });
};

export const testEmail = async (data: any) => {
  return request(`security_policy/mail_conn`, {
    method: 'post',
    data,
  });
};

export const securityPolicyInterface = () => {
  return request(`security_policy/interface`, {
    method: 'get',
  });
};


export const clusterList  = (data:any)=>{
  return request(`cluster/nodes`,{method:'get',data})
}

export const addCluster = (data:any)=>{
  return request(`cluster/nodes`,{method:'post',data:data})
}

export const delNodes = (data:any)=>{
  return request(`cluster/nodes/:node_name`,{method:'delete',data:data})
}

export const remvoeNode = (data:any)=>{
  return request(`cluster/nodes/${data.node_name}/leave`,{method:'post',data:data})
}

export const setMainNode = (data:any)=>{
  return request(`cluster/nodes/${data.node_name}/bemaster`,{method:'post',data:data})
}

export const joinGroup = (data:any)=>{
  return request(`cluster/nodes/${data.node_name}/join`,{method:'post',data:data})
}