.email_detail_info {
  border: 1px solid #e8e8e8;
  padding: 10px 10px 0 10px;
}

.email_content {
  min-height: 220px;
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  padding: 10px;
  background-color: #f5f5f5;
}

.fileContent{
  .contentD1{
    display: grid;
    grid-template-columns: 30% 70%;
    grid-template-rows: 22rem;
    grid-gap: 0.5rem;
  }
}
.detailBox{
  height: calc(100vh - 74px);
  display: grid;
  grid-template-rows: 22rem;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    'a b'
    'c c';
  grid-gap: 10px;
  overflow: scroll;
  p{margin-bottom:0}
  &>div{
    background: #fff;
  }
  &>div:nth-child(3){
    grid-area: c;
  }
}

.email_ip_block {
  width: 120px;
}

.email_sender_block {
  width: 160px;

  & > div {
    text-wrap: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}