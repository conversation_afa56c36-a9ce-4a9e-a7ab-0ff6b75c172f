# @license: Apache Licence
# @file: test_feature_group_manager.py
# @time: 2019/08/27
# @software: PyCharm
"""
特征组单元测试用例
"""
import json
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_feature.feature_group_manager import FeatureGroupWithId, FeatureGroup


class TestFeatureGroupWithId(TestCase):
    """
    带 ID 的特征组单元测试用例
    """
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

        parm = {
            "name": "group_for_unittest_put11",
            "sidList": ["30000001", "30000002"]
        }
        rst = self.client.post(api.url_for(FeatureGroup), json=parm)
        self.group = json.loads(rst.data)
        print(self.group)

    def tearDown(self):
        pass

    def group_delete(self):
        """
        :return:
        """
        self.client.delete(api.url_for(FeatureGroupWithId,
                                       group_id=self.group["data"]["groupId"]))

    def test_get_1(self):
        """
        获取已存在的特征组
        :return:
        """
        rst = self.client.get(api.url_for(FeatureGroupWithId,
                                          group_id=self.group["data"]["groupId"]))
        self.group_delete()
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], True)

    def test_get_2(self):
        """
        获取不存在的特征组
        :return:
        """
        rst = self.client.get(api.url_for(FeatureGroupWithId,
                                          group_id="f28c95eb3814cf952fb971bd7b2e84a4545"))
        self.group_delete()
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], False)

    def test_put_1(self):
        """
        已存在特征组修改
        :return:
        """
        self.client.put(api.url_for(FeatureGroupWithId,
                                    group_id=self.group["data"]["groupId"]),
                        json={"action": "stop"})
        parm = {
            "name": "group_for_unit_test_put1",
            "sidList": ["30000001", "30000002"]
        }

        rst = self.client.put(api.url_for(FeatureGroupWithId,
                                          group_id=self.group["data"]["groupId"]), json=parm)
        rst = json.loads(rst.data)
        print(rst)
        self.group_delete()
        self.assertEqual(rst['flag'], True)

    def test_put_2(self):
        """
        修改不存在的特征组
        :return:
        """
        parm = {
            "name": "group_for_unit_test_put2",
            "sidList": ["30000001", "30000002"]
        }
        rst = self.client.put(api.url_for(FeatureGroupWithId,
                                          group_id="f28c95eb3814cf952fb971bd7b2e84a45"), json=parm)
        rst = json.loads(rst.data)
        print(rst)
        self.group_delete()
        self.assertEqual(rst['flag'], False)

    def test_delete_1(self):
        """
        删除不存在的特征组
        :return:
        """
        rst = self.client.delete(api.url_for(FeatureGroupWithId,
                                             group_id="3bcfac207b00417867798e16401708d62"))
        rst = json.loads(rst.data)
        print(rst)
        self.group_delete()
        self.assertEqual(rst['flag'], False)

    def test_delete_2(self):
        """
        删除存在的特征组
        :return:
        """
        rst = self.client.delete(api.url_for(FeatureGroupWithId,
                                             group_id=self.group["data"]["groupId"]))
        rst = json.loads(rst.data)
        print(rst)
        self.group_delete()
        self.assertEqual(rst['flag'], True)


class TestFeatureGroup(TestCase):
    """
    特征组不带 ID 的单元测试用例
    """
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_post_1(self):
        """
        创建新的特征组
        :return:
        """
        parm = {
            "name": "group_for_unit_test_post1",
            "sidList": ["30000001", "30000002"]
        }

        rst = self.client.post(api.url_for(FeatureGroup), json=parm)
        print(rst.data)
        rst = json.loads(rst.data)
        if rst['flag'] is True:
            self.client.delete(api.url_for(FeatureGroupWithId,
                                           group_id=rst["data"]["groupId"]))
        self.assertEqual(rst['flag'], True)

    def test_post_2(self):
        """
        重复创建特征组
        :return:
        """
        parm = {
            "name": "group_for_unit_test_post2",
            "sidList": ["30000001", "30000002"]
        }

        rst = self.client.post(api.url_for(FeatureGroup), json=parm)
        rst1 = self.client.post(api.url_for(FeatureGroup), json=parm)
        print(rst1.data)
        rst1 = json.loads(rst1.data)
        rst = json.loads(rst.data)
        if rst['flag'] is True:
            self.client.delete(api.url_for(FeatureGroupWithId,
                                           group_id=rst["data"]["groupId"]))
        self.assertEqual(rst1['flag'], False)

    def test_post_3(self):
        """
        创建特征组不存在的探索任务
        :return:
        """
        parm = {
            "name": "group_for_unittest_post3",
            "sidList": ["300000010", "300000020"]
        }
        rst = self.client.post(api.url_for(FeatureGroup), json=parm)
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], False)

    def test_post_4(self):
        """
        创建空名的特征组
        :return:
        """
        parm = {
            "name": "",
            "sidList": ["30000001", "30000002"]
        }
        rst = self.client.post(api.url_for(FeatureGroup), json=parm)
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], False)

    def test_get_1(self):
        """
        获取所有特征组
        :return:
        """
        rst = self.client.get(api.url_for(FeatureGroup,
                                          page=1,
                                          pageSize=10
                                          ))
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], True)
