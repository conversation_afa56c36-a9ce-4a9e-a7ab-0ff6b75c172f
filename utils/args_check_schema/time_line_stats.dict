{
    "type": "object",
    "required": ["startTime", "mode"],
    "properties": {
        "timeSlice": {
            "type": "string",
            "default": "Minute",
            "enum": ["Minute", "Hour", "Day", "Month"],
        },
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "mode": {
            "type": "string",
            "enum": ["killchains", "threatlevel", "threatLevel"],
        },
        "pageSize": {
            "type": "string",
            "default": "5",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}