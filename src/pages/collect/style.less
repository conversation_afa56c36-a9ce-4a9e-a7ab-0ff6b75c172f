// @import '~@/style/index.less'

.keyword {
  width: 400px;
  max-height: 80px;
  overflow: auto;
}

.eventDetail {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  padding: 16px;
  overflow-y: auto;
  background-color: #fff;

  .content {
    display: flex;
    align-items: center;
    width: 50%;
    padding: 5px 15px;
  }
}

.btn_center {
  margin: 0 10px;
}

.meansBox {
  padding: 20px;
}

.header {
  display: flex;
  padding-bottom: 20px;
}

.computer {
  display: block;
  width: 80px;
  height: 80px;
}

.head_ip {
  // padding-right: 20px;
  font-weight: bold;
  font-size: 18px;
}

.head_info {
  padding: 4px 0 0 20px;
}

.labels {
  margin: 0 10px;
  padding: 4px 6px;
  color: #fff;
  background-color: #40a9ff;
  border-radius: 6px;
}

.text_box {
  display: flex;
  padding-top: 4px;
}

.head_text {
  width: 142px;
  height: 21px;
  padding-right: 20px;
}

.top_info {
  height: 27px;
  // padding-bottom: 20px;
}

.head_btn {
  margin-left: auto;
  margin-top: 28px;
}

.card {
  width: 32%;
}

.charts_box {
  display: flex;
  justify-content: space-between;
}

.detail_item {
  width: 358px;
  background: #f2f4f5;
  padding: 10px;
  height: 96px;
  // width: 320px;
  // height: 90px;
  // margin: 10px 0;
}

.purple {
  color: purple;
  font-size: 28px;
  font-weight: bold;
}

.card_title {
  font-weight: bold;
  font-size: 18px;
}

.translate {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20px;
}

.port {
  display: inline-block;
  font-size: 28px;
  font-weight: bold;
  color: #1bc126;
  width: 25%;
}

.application {
  width: 300px;
  height: 200px;
}

.application_title {
  margin-right: 6px;
}

.circular {
  width: 8px;
  height: 8px;
  margin-right: 10px;
  border-radius: 50%;
  display: inline-block;
}

.blue {
  color: #40a9ff;
  padding-right: 16px;
}

.square {
  width: 6px;
  height: 6px;
  display: inline-block;
  transform: translateY(-1px);
  margin-right: 10px;
}

.week_chart_box {
  width: 450px;
  height: 260px;
}

.threat_item {
  width: 358px;
  padding: 2px;
  background: #ebedf0;
  // height: 76px;
}

.timeline_box {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
}

.time_count {
  display: inline-block;
  // width: 100px;
  padding-top: 14px;
  font-size: 18px;
}

.count_color {
  font-size: 24px;
  color: #ff8000;
}

.pointer {
  cursor: pointer;
}

.time_content {
  width: 260px;
  padding-right: 4px;
}

.send_box {
  width: 300px;
  height: 250px;
}

.chart {
  width: 100%;
  height: 150px;
}

.table_box {
  max-height: calc(100vh - 282px);
  width: 800px;
  top: 32px;
  right: 50px;
  background: rgba(255, 255, 255, 0.8);
  position: absolute;
  overflow: auto;

  :global {
    .ant-table {
      max-height: calc(100vh - 412px);
      overflow: auto;
      background-color: rgba(248, 248, 248, 0.5) !important;
    }

    .ant-table-thead > tr > th {
      position: sticky;
      top: 0;
      z-index: 1;
    }

    .ant-table-header {
      position: sticky;
      top: 0;
      z-index: 1;

      .ant-table-thead tr {
        padding: 10px 20px;
      }

      .ant-table-thead tr th {
        background-color: rgba(240, 240, 240, 1);
      }
    }

    .ant-table-tbody > tr:hover > td {
      background-color: rgba(243, 243, 243, 0.5);
    }

    .ant-pagination .ant-table-pagination .ant-table-pagination-right {
      background: rgba(255, 255, 255, 0);
    }

    .ant-table-body {
      .ant-table-cell {
        padding: 10px 20px;
      }
    }
  }
}

.bottom_charts {
  position: relative;

  :global {
    .ant-collapse-content {
      background: rgba(255, 255, 255, 0);
    }

    .ant-collapse-content-box {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}
.form {
  :global {
    .ant-form-item > .ant-form-item-control {
      flex: 1;
      overflow: hidden;
    }

    div.ant-form-item-label {
      min-width: 6em;
    }
  }
}

.ant-form-item {
  margin-bottom: 0;
}

.ant-form-item-label {
  text-align: left;
}
.threatExpansion {
  :global {
    .ant-row {
      padding: 0;
    }
  }
}

.hadnleFrom {
  :global {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td,
    .ant-table tfoot > tr > th,
    .ant-table tfoot > tr > td {
      padding: 0 !important;
    }
  }
}
.changeForm {
  :global {
    .ant-row {
      padding: 0;
    }
  }
}
