import sys
sys.path.append('/opt/ndr')

from .celery import *
from .utils import *

from cluster.api.manager import MyClusterManager
from cluster.api.consistency import Consistency


cluster_mgr = MyClusterManager()

@app.task(queue='host_task', routing_key='host_task')
def get_node_name():
    return cluster_mgr.get_node_name()

@app.task(queue='host_task', routing_key='host_task')
def get_node_ip():
    return cluster_mgr.get_node_ip()

@app.task(queue='host_task', routing_key='host_task')
def add_node(node_info):
    return cluster_mgr.add_node(node_info)

@app.task(queue='host_task', routing_key='host_task')
def delete_node(node_ip):
    cluster_mgr.set_node_role(node_ip, 'single')
    r = cluster_mgr.delete_node(node_ip)
    return r

@app.task(queue='host_task', routing_key='host_task')
def set_mgmt_node_ip(node_ip):
    return cluster_mgr.set_mgmt_node_ip(node_ip)


@app.task(queue='host_task', routing_key='host_task')
def sync_file(src_file_path, dst_file_path):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.sync_file_safe_by_cluster(src_file_path, dst_file_path)

@app.task(queue='host_task', routing_key='host_task')
def read_file_by_node(node_ip, src_file_path, dst_file_path):
    cluster_mgr.read_file_by_node(node_ip, src_file_path, dst_file_path)

@app.task(queue='host_task', routing_key='host_task')
def remove_file(file_path):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.remove_file_by_cluster(file_path)

@app.task(queue='host_task', routing_key='host_task')
def request_pcap(node_ip, query_str, file_path, url_list):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.request_pcap_by_node(node_ip, query_str, file_path, url_list)

@app.task(queue='host_task', routing_key='host_task')
def hdp_feature_upgrade(path):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.sync_file_safe_by_cluster(path)
        cluster_mgr.hdp_feature_upgrade_by_cluster(path)

@app.task(queue='host_task', routing_key='host_task')
def hdp_feature_modify(sid_list:list, rule_path, rule_info_path):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.hdp_feature_operate_by_cluster(sid_list)
        cluster_mgr.hdp_feature_add_by_cluster(rule_path, rule_info_path)

@app.task(queue='host_task', routing_key='host_task')
def hdp_feature_operate(sid_list:list):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.hdp_feature_operate_by_cluster(sid_list)

@app.task(queue='host_task', routing_key='host_task')
def hdp_feature_add(rule_path, rule_info_path):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.hdp_feature_add_by_cluster(rule_path, rule_info_path)

@app.task(queue='host_task', routing_key='host_task')
def hdp_set_file_protocol(file_app_cfg, file_app_list: list):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.hdp_set_file_protocol_by_cluster(file_app_cfg, file_app_list)

@app.task(queue='host_task', routing_key='host_task')
def hdp_set_file_type(file_type_cfg):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.hdp_set_file_type_by_cluster(file_type_cfg)

@app.task(queue='host_task', routing_key='host_task')
def hdp_set_file_size(min_size, max_size):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.hdp_set_file_size_by_cluster(min_size, max_size)

@app.task(queue='host_task', routing_key='host_task')
def sync_file_and_restart_hdp(file_path):
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.sync_file_safe_by_cluster(file_path)
        cluster_mgr.hdp_restart_by_cluster()

@app.task(queue='host_task', routing_key='host_task')
def hdp_update_portmask(node_name, port_mask):
    if cluster_mgr.get_node_name() == node_name:
        cmd = 'sed -i "s/^port_mask.*/port_mask = %s/g" /opt/hdp/config/conf.ini' % port_mask
        ret, msg = run_command(cmd)
        if not ret:
            return "Modify hdp port-mask parameter failed(%s)." % msg
        os.system("supervisorctl -u %s -p %s restart hdp" % (SupervisorConfig.User, SupervisorConfig.Password))
        return ""
    if cluster_mgr.get_role() == 'master':
        node_ip = cluster_mgr.get_node_ip_by_node_name(node_name)
        if not node_ip:
            return "Modify hdp port-mask parameter failed(can't find node ip)."
        r = cluster_mgr.hdp_update_portmask_by_node(node_ip, port_mask)
        if r:
            return "Modify hdp port-mask parameter failed(%s)." % r
        return ""
    else:
        return "Modify hdp port-mask parameter failed(not master)."


@app.task(queue='host_task', routing_key='host_task')
def hdp_restart():
    if cluster_mgr.get_role() == 'master':
        cluster_mgr.hdp_restart_by_cluster()

@app.task(queue='monitor_task', routing_key='monitor_task')
def consistency_check_and_sync():
    consistency = Consistency()
    if consistency.cluster.get_role() == 'master':
        consistency.check_and_sync()

TABLE_NAME = 'cluster'

NODE_STATUS_OFFLINE = 'offline'
NODE_STATUS_ONLINE = 'online'
NODE_STATUS_REMOVED = 'removed'

@app.task(queue='monitor_task', routing_key='monitor_task')
def cluster_check():
    mongodb = MongoDB("ndr")
    nodes_in_db = mongodb.find('cluster')
    nodes_actual = cluster_mgr.get_node_list()
    nodes = {}
    for node in nodes_actual:
        nodes[node['node_name']] = node

    nodes_dict_in_db = {}
    for node in nodes_in_db:
        nodes_dict_in_db[node['node_name']] = node
        if node['node_name'] not in nodes:
            if node['node_status'] != NODE_STATUS_REMOVED:
                cluster_mgr.add_node(node)
        else:
            if node['node_role'] == 'master' and nodes['node_name']['node_role'] != 'master':
                cluster_mgr.set_mgmt_node_ip(node['node_ip'])

    for node in nodes_actual:
        if node['node_name'] not in nodes_dict_in_db:
            cluster_mgr.delete_node(node['node_ip'])

    for node in nodes_in_db:
        if node['node_status'] == NODE_STATUS_REMOVED:
            continue

        try:
            conn, _ = cluster_mgr.connect_node_by_ssl(node['node_ip'])
            conn.close()
            if node['node_status'] != NODE_STATUS_ONLINE:
                mongodb.update(TABLE_NAME, {'node_name': node['node_name']}, {'$set': {'node_status': NODE_STATUS_ONLINE}})
        except:
            if node['node_status'] == NODE_STATUS_ONLINE:
                mongodb.update(TABLE_NAME, {'node_name': node['node_name']}, {'$set': {'node_status': NODE_STATUS_OFFLINE}})




