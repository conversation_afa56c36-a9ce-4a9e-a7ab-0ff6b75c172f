{
    "type": "object",
    "required": [],
    "properties": {
        "target": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,128}$"
        },
        "type": {
	        "type": "string",
	        "enum": ["ip", "domain"],
	        "default": ""
        },
        "tag": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,128}$"
        }
    }
}