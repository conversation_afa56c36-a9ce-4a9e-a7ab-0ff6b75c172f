{
	"type": "object",
	"required": [],
	"properties": {
		"name": {
			"type": "string",
			"pattern": r"^$|^[A-Za-z0-9_\-\u4e00-\u9fa5]{0,64}$",
			"default": ""
		},
		"type": {
			"type": "string",
            "enum": ["", "administrator", "operator", "auditor"],
            "default": ""
		},
		"page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
	}
}