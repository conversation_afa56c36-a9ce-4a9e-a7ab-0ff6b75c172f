import { Image, Card } from 'antd';
import React, { useEffect, useState } from 'react';
import cn from 'classnames';
import s from './style.less';
interface PartProps {
  info: any;
}
const RunImg = ({ info = [] }: PartProps) => {
  useEffect(() => {}, [info]);
  return (
    <div className={cn(s.runImg)}>
      <Card title={`共计${info.length}张图片`}>
        <Image.PreviewGroup>
          {info.map(e => {
            return (
              <div className="mr-2 inline-block">
                <Image width={150} height={150} src={`data:image/gif;base64,${e.img_stream}`} />
              </div>
            );
          })}
        </Image.PreviewGroup>
      </Card>
    </div>
  );
};

export default RunImg;
