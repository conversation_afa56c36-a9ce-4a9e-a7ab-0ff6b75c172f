#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>

""" log process"""

import os
import time
import gzip
import logging
from logging.handlers import TimedRotatingFileHandler
from config.config import LogPath, MongoDBConfig
from pymongo import MongoClient

# 日志logger_name选用以下
user_log, mongo_log, es_log, api_log, explore_log, tools_log = (
    'user_log', 'mongo_log', 'es_log', 'api_log', 'explore_log', 'tools_log')

'''
    最细化日志文件，对于每一个接口生成一份日志文件，根据接口文件名定义logger名称
'''


def get_ndr_logger(log_type, file, console_confirm=True):
    """
    :param log_type:
    :param file:
    :param console_confirm:
    :return:
    """
    # logger_name = file[:-3].split('/')[-1]
    file_name = file.split('/')[-1]
    log_path = LogPath.Log_PATH
    if not os.path.exists(log_path):
        os.mkdir(log_path)
    log_fn = "{0}/{1}.log".format(log_path, log_type)
    # getLogger 为单例模式
    ndr_logger = logging.getLogger(file_name)
    ndr_logger.setLevel(logging.INFO)
    formatter = logging.Formatter("""[%(asctime)s][%(name)s][line:%(lineno)d]%(levelname)s:  %(message)s""")
    # handler 存在判定，防止添加多个handler，造成日志重复
    if not ndr_logger.handlers:
        # ndr_handler = TimedRotatingFileHandler(log_fn, 'midnight', backupCount=60)
        ndr_handler = GzTimedRotatingFileHandler(log_fn, 'midnight', interval=1, backupCount=5)
        ndr_handler.setFormatter(formatter)
        ndr_logger.addHandler(ndr_handler)
        # 是否输出到控制台
        if console_confirm:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            ndr_logger.addHandler(console_handler)

    return ndr_logger


class GzTimedRotatingFileHandler(TimedRotatingFileHandler):
    """ 日志文件压缩打包 """

    def __init__(self, filename, when, interval, backupCount):
        super(GzTimedRotatingFileHandler, self).__init__(filename, when, interval, backupCount)

    @staticmethod
    def do_gzip(old_log):
        """
        :param old_log:
        :return:
        """
        with open(old_log) as old:
            with gzip.open(old_log + '.gz', 'wb') as comp_log:
                comp_log.writelines(old)
        os.remove(old_log)

    def do_rollover(self):
        """
        :return:
        """
        if self.stream:
            self.stream.close()
            self.stream = None
        current_time = int(time.time())
        dst_now = time.localtime(current_time)[-1]
        t_interval = self.rolloverAt - self.interval
        if self.utc:
            time_tuple = time.gmtime(t_interval)
        else:
            time_tuple = time.localtime(t_interval)
            dst_then = time_tuple[-1]
            if dst_now != dst_then:
                if dst_now:
                    addend = 3600
                else:
                    addend = -3600
                time_tuple = time.localtime(t_interval + addend)
        dfn = self.baseFilename + "." + time.strftime(self.suffix, time_tuple)
        if os.path.exists(dfn):
            os.remove(dfn)
        if os.path.exists(self.baseFilename):
            os.rename(self.baseFilename, dfn)
            self.do_gzip(dfn)
        if self.backupCount > 0:
            for s_file in self.getFilesToDelete():
                os.remove(s_file)
        if not self.delay:
            self.stream = self._open()
        new_rollover_at = self.computeRollover(current_time)
        while new_rollover_at <= current_time:
            new_rollover_at = new_rollover_at + self.interval
        if (self.when == 'MIDNIGHT' or self.when.startswith('W')) and not self.utc:
            dst_at_rollover = time.localtime(new_rollover_at)[-1]
            if dst_now != dst_at_rollover:
                if not dst_now:
                    addend = -3600
                else:
                    addend = 3600
                new_rollover_at += addend
        self.rolloverAt = new_rollover_at


class LogToDb():
    """write log to mongodb """

    def __init__(self):
        self.conn = MongoClient(
            host=MongoDBConfig.MONGO_HOST,
            port=MongoDBConfig.MONGO_PORT,
            username=MongoDBConfig.MONGO_USERNAME,
            password=MongoDBConfig.MONGO_PASSWORD,
            authSource=MongoDBConfig.MONGO_AuthSource)
        self.db = self.conn["ndr"]

    def write_to_db(self, username="", log_type='run', event="", user_ip=""):
        """
        :param username:
        :param log_type:
        :param event:
        :param user_ip:
        :return:
        """
        body = {
            "user": username,
            "user_ip": user_ip if user_ip else '',
            "type": log_type,
            "event": event,
            "writeTime": int(time.time() * 1000)
        }
        self.db["log"].insert_one(body)

    def log_to_db(self, collect, username="", update_mode="", start_time=0, stop_time=0, first_version="",
                  end_version="", status=""):
        body = {
            "user": username,
            "update_mode": update_mode,
            "start_time": start_time,
            "stop_time": stop_time,
            "first_version": first_version,
            "end_version": end_version,
            "status": status
        }
        self.db[collect].insert_one(body)
