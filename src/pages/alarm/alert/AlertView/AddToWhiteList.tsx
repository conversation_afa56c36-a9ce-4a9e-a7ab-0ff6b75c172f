import { handleaddWhiteList } from '@/services/alarm';
import { Button, Form, Input, Modal, Select, message } from 'antd';
import React from 'react';


export default function AddToWhiteList({ value, filter, children, onChange }) {
    const [modalVisible, setModalVisible] = React.useState(false);
    const [form] = Form.useForm();
    React.useEffect(() => {
        form.setFieldsValue({
            type: 'ip',
            src_ip: value?.flow?.src_ip,
            dst_ip: value?.flow?.dst_ip,
        });
    }, [value]);

    const handleSubmit = (values) => {
        return handleaddWhiteList({
            target: [values.src_ip, values.dst_ip].filter(Boolean),
            type: values.type,
        }).then(res => {
            if (!res.flag) {
                return message.error(res.message);
            }
            form.resetFields();
            setModalVisible(false);
            message.success(res.message);
            onChange({ ...filter });
        });
    };

    return (
        <div onClick={(e) => e.stopPropagation()}>
            <Button
                type="link"
                onClick={(e) => {
                    e.stopPropagation();
                    setModalVisible(true);
                }}
            >{children}</Button>
            <Modal
                centered
                visible={modalVisible}
                title="添加白名单"
                footer={null}
                onCancel={() => setModalVisible(false)}
            >
                <Form
                    form={form}
                    onFinish={handleSubmit}
                >
                    <Form.Item name="type" label="添加类型">
                        <Select>
                            <Select.Option value="ip">告警白名单</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item name="src_ip" label="源IP">
                        <Input />
                    </Form.Item>
                    <Form.Item name="dst_ip" label="目的IP">
                        <Input />
                    </Form.Item>
                    <div className="flex justify-center">
                        <Button type="primary" htmlType="submit">添加</Button>
                    </div>
                </Form>
            </Modal>
        </div>
    )
}
