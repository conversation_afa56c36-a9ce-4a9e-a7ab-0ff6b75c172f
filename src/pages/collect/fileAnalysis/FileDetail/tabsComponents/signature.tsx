import { Collapse, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import s from './style.less';
import { merge, isEmpty } from 'lodash';
const { Panel } = Collapse;
interface PartProps {
  info: any;
}
const Signature = ({ info = [] }: PartProps) => {
  const [data, setData] = useState([]);
  const columns1 = [
    {
      title: '签名类别',
      dataIndex: 'name',
    },
    {
      title: '签名描述',
      dataIndex: 'description',
    },
  ];
  const columns2 = [
    {
      title: '类型',
      dataIndex: 'type',
    },
    {
      title: '进程ID',
      dataIndex: 'pid',
    },
    {
      title: '时间',
      dataIndex: 'time',
    },
    {
      title: '种类',
      dataIndex: 'category',
    },
    {
      title: 'API',
      dataIndex: 'api',
    },
    {
      title: '标识',
      dataIndex: 'flags',
    },
    {
      title: '返回值',
      dataIndex: 'return_value',
    },
    {
      title: '状态',
      dataIndex: 'nt_status',
    },
    {
      title: '操作',
      dataIndex: 'offset',
    },
  ];
  const TablePart = row => {
    const marks = isEmpty(row.marks)
      ? []
      : row.marks.map(e => {
          const obj = e ? merge(e, e.call) : e;
          return obj;
        });
    return (
      <Table
        className={s.table}
        columns={columns2}
        dataSource={marks}
        pagination={{
          size: 'small',
          showQuickJumper: true,
        }}
      />
    );
  };
  useEffect(() => {
    if (!isEmpty(info)) {
      const data_ = info.map((e, i) => {
        e.key = i + Math.random();
        return e;
      });
      setData(data_);
    }
  }, [info]);
  return (
    <div className={s.signature}>
      <Table
        className={s.table}
        expandable={{
          expandedRowRender: row => TablePart(row),
        }}
        columns={columns1}
        dataSource={data}
        pagination={false}
      />
    </div>
  );
};

export default Signature;
