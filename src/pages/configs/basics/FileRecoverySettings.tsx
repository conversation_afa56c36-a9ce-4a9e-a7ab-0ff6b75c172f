import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  <PERSON>ton,
  Card,
  Checkbox,
  Col,
  InputNumber,
  Popover,
  Row,
  Select,
  Space,
  Tooltip,
  Typography,
  message,
  notification,
} from 'antd';
import cn from 'classnames';
import React from 'react';

interface FileRecoverySettingsProps {
  className: string;
}

export default function FileRecoverySettings({ className }: FileRecoverySettingsProps) {
  return (
    <div className={cn(className, 'mt-4')}>
      <Card title={<span className="font-bold">文件还原配置</span>} bordered className="w-100%">
        <div className="flex flex-col items-start">
          <SizeSettings />
          <ProtocolSettings className="mt-8" />
          <FileTypeSettings className="mt-8" />
        </div>
      </Card>
    </div>
  );
}

const formatBytes = (bytes: number, decimals = 2) => {
  if (!+bytes) return { value: 0, unit: 'byte' };

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['byte', 'kb', 'mb', 'gb', 'tb', 'pb', 'EiB', 'ZiB', 'YiB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return { value: parseFloat((bytes / Math.pow(k, i)).toFixed(dm)), unit: sizes[i] };
};

const convertBytesToAny = (value: number) => {
  if (!value || value < 0) return { value: 0, unit: 'kb' };
  if (value >= 1024) {
    return formatBytes(value, 2);
  }
  return { value, unit: 'byte' };
};

function SizeSettings({ className }: { className?: string }) {
  const [submiting, setSubmiting] = React.useState(false);
  const [state, setState] = React.useState({ min: 0, max: 0, minUnit: 'kb', maxUnit: 'kb' });
  const prev = React.useRef({});

  React.useEffect(() => {
    fetch('/mica-api/api/v1/system/file_size')
      .then(x => x.json())
      .then(resp => {
        const data = resp.data || {};
        const { value: minValue, unit: minUnit } = convertBytesToAny(data.min);
        const { value: maxValue, unit: maxUnit } = convertBytesToAny(data.max);
        prev.current = { min: minValue, max: maxValue, minUnit, maxUnit };
        setState({ ...state, min: minValue, max: maxValue, minUnit, maxUnit });
      })
      .catch(error =>
        notification.error({
          message: '还原的文件大小配置失败',
          description: error.message,
        }),
      );
  }, []);

  const convertToBytes = (value: number, unit: string) => {
    if (!value) return 0;
    if (unit === 'kb') {
      return value * 1024;
    } else if (unit === 'mb') {
      return value * 1024 * 1024;
    }
    return value;
  };

  const handleSave = async () => {
    setSubmiting(true);
    try {
      const min = convertToBytes(state.min, state.minUnit);
      const max = convertToBytes(state.max, state.maxUnit);
      if (min > max) {
        message.error('最小值不能大于最大值');
        return;
      }
      if (max > 20 * 1024 * 1024) {
        message.error('配置的大小不能超过20MB');
        return;
      }
      const resp = await fetch('/mica-api/api/v1/system/file_size', {
        method: 'POST',
        headers: { 'content-type': 'application/json; charset=UTF-8' },
        body: JSON.stringify({
          file_size: { min: convertToBytes(state.min, state.minUnit), max: convertToBytes(state.max, state.maxUnit) },
        }),
      }).then(res => res.json());
      if (!resp.flag) {
        notification.error({
          message: '还原的文件大小保存失败',
          description: '配置的大小不能超过20MB并且最小值不能大于最大值',
        });
      } else {
        message.success('还原的文件大小保存成功');
      }
    } finally {
      setSubmiting(false);
    }
  };

  const sizeChange = (field: string, unit: string, value: number) => {
    setState({ ...state, ...{ [`${field}Unit`]: unit || 'kb', [field]: value } });
  };

  return (
    <div className={className}>
      <div className="mb-2">
        <Space align="center">
          <span>还原的文件大小</span>
          <Tooltip
            title="说明"
            overlay={
              <>
                <Typography.Title level={5} style={{ color: '#fff' }}>
                  配置说明
                </Typography.Title>
                <div className="mb-2">配置的大小不能超过20MB</div>
                <div>正整数且最小值不能大于最大值</div>
              </>
            }
          >
            <QuestionCircleOutlined />
          </Tooltip>
          <span>:</span>
        </Space>
      </div>
      <div className="flex items-center">
        <div className="p-2 b b-solid b-brd5" style={{ width: '590px' }}>
          <Row justify="space-between">
            <Col span={10}>
              <Space align="center">
                <div style={{ width: '50px', textAlign: 'right' }}>最小：</div>
                <InputNumber
                  min={0}
                  precision={0}
                  value={state.min}
                  onChange={value => sizeChange('min', state.minUnit, value)}
                  style={{ width: '170px' }}
                  addonAfter={
                    <Select value={state.minUnit} onChange={value => sizeChange('min', value?.toString(), state.min)}>
                      <Select.Option value="byte">Byte</Select.Option>
                      <Select.Option value="kb">KB</Select.Option>
                      <Select.Option value="mb">MB</Select.Option>
                    </Select>
                  }
                />
              </Space>
            </Col>
            <Col span={10}>
              <Space align="center">
                <div style={{ width: '50px', textAlign: 'right' }}>最大：</div>
                <InputNumber
                  value={state.max}
                  min={0}
                  precision={0}
                  onChange={value => sizeChange('max', state.maxUnit, value)}
                  style={{ width: '170px' }}
                  addonAfter={
                    <Select value={state.maxUnit} onChange={value => sizeChange('max', value?.toString(), state.max)}>
                      <Select.Option value="byte">Byte</Select.Option>
                      <Select.Option value="kb">KB</Select.Option>
                      <Select.Option value="mb">MB</Select.Option>
                    </Select>
                  }
                />
              </Space>
            </Col>
          </Row>
        </div>
        <Button type="primary" className="ml-4" loading={submiting} onClick={handleSave}>
          保存
        </Button>
      </div>
    </div>
  );
}

const protocolOptions = [
  { label: 'HTTP', value: 'HTTP' },
  { label: 'FTP', value: 'FTP' },
  { label: 'SMB', value: 'SMB' },
  { label: 'NFS', value: 'NFS' },
  { label: 'SMTP', value: 'SMTP' },
  { label: 'POP3', value: 'POP3' },
  { label: 'IMAP', value: 'IMAP' },
  { label: 'TFTP', value: 'TFTP' },
];

function ProtocolSettings({ className }: { className?: string }) {
  const [submiting, setSubmiting] = React.useState(false);
  const [state, setState] = React.useState([]);
  const prev = React.useRef({});

  React.useEffect(() => {
    fetch('/mica-api/api/v1/system/file_protocol')
      .then(x => x.json())
      .then(resp => {
        prev.current = resp.data;
        setState(
          Object.keys(resp.data).reduce((arr, key) => {
            resp.data[key] && arr.push(key);
            return arr;
          }, []),
        );
      })
      .catch(error =>
        notification.error({
          message: '还原的协议类型配置失败',
          description: error.message,
        }),
      );
  }, []);

  const handleSave = async () => {
    setSubmiting(true);
    try {
      const resp = await fetch('/mica-api/api/v1/system/file_protocol', {
        method: 'POST',
        headers: { 'content-type': 'application/json; charset=UTF-8' },
        body: JSON.stringify({
          file_protocol: Object.keys(prev.current).reduce((obj, key) => {
            obj[key] = state.includes(key) ? 1 : 0;
            return obj;
          }, {}),
        }),
      }).then(resp => resp.json());
      if (!resp.flag) {
        notification.error({
          message: '还原的协议类型保存失败',
          description: resp.message,
        });
      } else {
        message.success('还原的协议类型保存成功');
      }
    } finally {
      setSubmiting(false);
    }
  };

  return (
    <div className={className}>
      <div className="mb-2">还原的协议类型：</div>
      <div className="flex items-center">
        <div className="p-2 b b-solid b-brd5">
          <Checkbox.Group
            className="flex flex-wrap"
            options={protocolOptions}
            value={state}
            onChange={state => setState(state)}
          />
        </div>
        <Button type="primary" className="ml-4" loading={submiting} onClick={handleSave}>
          保存
        </Button>
      </div>
    </div>
  );
}

const fileTypeOptions = [
  {
    label: '文档文件',
    children: [
      { label: 'OFFICE', value: 'office', tip: '包含 DOC、DOCX、XLS、XLSX、PPT、PPTX' },
      { label: 'PDF', value: 'pdf' },
      { label: 'RTF', value: 'rtf' },
    ],
  },
  {
    label: '可执行文件',
    children: [
      { label: 'PE', value: 'pe' },
      { label: 'ELF', value: 'elf' },
    ],
  },
  {
    label: '压缩文件',
    children: [
      { label: 'ZIP', value: 'zip' },
      { label: 'GZIP', value: 'gzip' },
      { label: '7Z', value: '7z' },
      { label: 'BZ2', value: 'bz2' },
      { label: 'RAR', value: 'rar' },
      { label: 'TAR', value: 'tar' },
      { label: 'XZ', value: 'xz' },
    ],
  },
  {
    label: '脚本文件',
    children: [
      { label: 'JSP', value: 'jsp' },
      { label: 'ASP', value: 'asp' },
      { label: 'PHP', value: 'php' },
      { label: 'SHELL', value: 'shell' },
      { label: 'PYTHON', value: 'python' },
    ],
  },
  {
    label: '多媒体文件',
    children: [
      { label: 'FLASH', value: 'flash' },
      { label: 'GIF', value: 'gif' },
      { label: 'BPG', value: 'bpg' },
      { label: 'PNG', value: 'png' },
      { label: 'JPEG', value: 'jpeg' },
    ],
  },
  {
    label: '其他',
    children: [
      { label: 'APK', value: 'apk' },
      { label: 'JAVA', value: 'java' },
      { label: 'LNK', value: 'lnk' },
      { label: 'HTML', value: 'html' },
      { label: 'XML', value: 'xml' },
    ],
  },
];
function FileTypeSettings({ className }: { className?: string }) {
  const [submiting, setSubmiting] = React.useState(false);
  const [checked, setChecked] = React.useState({});
  const childrenCheckState = React.useMemo(
    () => fileTypeOptions.some(({ children }) => children.some(({ value }) => checked[value])),
    [checked],
  );
  const allChecked = React.useMemo(
    () => fileTypeOptions.every(({ children }) => children.every(({ value }) => checked[value])),
    [checked],
  );

  React.useEffect(() => {
    fetch('/mica-api/api/v1/system/file_type')
      .then(x => x.json())
      .then(resp => {
        setChecked(resp.data);
      })
      .catch(error =>
        notification.error({
          message: '还原的文件类型配置失败',
          description: error.message,
        }),
      );
  }, []);

  const handleSave = async () => {
    setSubmiting(true);
    try {
      const resp = await fetch('/mica-api/api/v1/system/file_type', {
        method: 'POST',
        headers: { 'content-type': 'application/json; charset=UTF-8' },
        body: JSON.stringify({
          file_type: Object.entries(checked).reduce((obj, [key, val]) => {
            obj[key] = val ? 1 : 0;
            return obj;
          }, {}),
        }),
      }).then(resp => resp.json());
      if (!resp.flag) {
        notification.error({
          message: '还原的文件类型保存失败',
          description: resp.message,
        });
      } else {
        message.success('还原的文件类型保存成功');
      }
    } finally {
      setSubmiting(false);
    }
  };

  return (
    <div className={className}>
      <div className="mb-2">还原的文件类型：</div>
      <div className="b b-b-solid b-b-brd5 b-l-solid b-l-brd5 b-r-solid b-r-brd5">
        <div className="b b-t-solid b-t-brd5 p-2">
          <Checkbox
            checked={allChecked}
            indeterminate={childrenCheckState && !allChecked}
            onChange={e =>
              setChecked({
                ...fileTypeOptions.reduce((obj: { [k: string]: Boolean }, { children }) => {
                  children.forEach(({ value }) => {
                    obj[value] = e.target.checked;
                    return obj;
                  }, {});
                  return obj;
                }, {}),
              })
            }
          >
            全选
          </Checkbox>
        </div>
        {React.useMemo(
          () =>
            fileTypeOptions.map(({ label, children }) => {
              const childrenCheckState = children.some(({ value }) => checked[value]);
              const allChecked = children.every(({ value }) => checked[value]);
              return (
                <div key={label} className="b b-t-solid b-t-brd5 p-2">
                  <Checkbox
                    className="w-32"
                    indeterminate={childrenCheckState && !allChecked}
                    checked={allChecked}
                    onChange={e => {
                      setChecked({
                        ...checked,
                        ...children.reduce((obj: { [k: string]: Boolean }, { value }) => {
                          obj[value] = e.target.checked;
                          return obj;
                        }, {}),
                      });
                    }}
                  >
                    {label}：
                  </Checkbox>
                  <span>
                    {children.map(({ label, tip, value }) => (
                      <Checkbox
                        key={value}
                        checked={checked[value]}
                        onChange={e => {
                          setChecked({ ...checked, [value]: e.target.checked });
                        }}
                      >
                        {label}
                        {tip && (
                          <Tooltip overlay={tip}>
                            <QuestionCircleOutlined className="ml-1" />
                          </Tooltip>
                        )}
                      </Checkbox>
                    ))}
                  </span>
                </div>
              );
            }),
          [checked],
        )}
      </div>
      <div className="flex mt-4">
        <Button type="primary" loading={submiting} onClick={handleSave}>
          保存
        </Button>
      </div>
    </div>
  );
}
