import React, { useEffect, useMemo, useState } from 'react';
import { Form, Select, Radio, Checkbox, Input, InputNumber, TimePicker, message, Button, Tree, Table, Modal } from 'antd';
import { exploreTaskType, exploreCycle, backWork } from '@/utils/enumList';
import { getpacpList, getDir, getLastUploadDir, getDataSource, getTaskPacp } from '@/services/explore';

import moment from 'moment';
import style from './index.less';
import store from './store';
import { ellipsis } from '@/utils/utils';
import { addTask, editData } from '@/services/explore';
import { isEmpty } from 'lodash';
import { SelectValue } from 'antd/lib/select';

import SelectorWithCheckAll from '@/components/SelectorWithCheckAll'

const { Option } = Select;
const { TreeNode } = Tree;
let pathParams = {
  dir: '',
  lastest: 0,
};
const Addtask = ({ onhide }) => {
  const [form] = Form.useForm();
  const [pcapFilesList, setpcapFilesList] = useState([]);
  const [allDirList, setAllDirList] = useState([]);
  const [dirList, setdirList] = useState([]);
  const [path, setpath] = useState<any>([]);
  const [alldataSource, setAlldataSource] = useState([]);
  const [dataSourceArray, setdataSourceArray] = useState([]);
  const [formValue, setFormValue] = useState<any>({
    taskType: 'singleTask',
    pcapList: [],
    taskId: '',
    type: '',
    dir_name: '',
    schedule: {
      period: '',
      baseDate: '',
      baseTime: '',
    },
    taskName: '',
    ftpDir: [],
    curFeatureGrpList: [],
    status: '',
    time: null,
    sourceData: '',
  });
  const [actionType, setactionType] = useState(store.getState().actionType);
  const type = form.getFieldValue("type");
  const changeDir = (typeW: SelectValue, all: any, setType: string) => {
    const allData = all;
    const obj = ['pcap', 'other'];
    let arr: React.SetStateAction<never[]> = [];
    if (isEmpty(allData)) {
      arr = [];
    } else {
      arr = allData.filter((e: any) => {
        const str = isNaN(e.type) ? e.type : obj[e.type - 1];
        return typeW === str;
      });
    }
    setType === 'dir' ? setdirList(arr) : setdataSourceArray(arr)
  };

  const getChooseDir = async (value: string) => {
    pathParams.dir = value;
    formValue.pcapList = [];
    setFormValue(formValue);
    getPacpDetail();
  };
  const changeTaskType = (value: any) => {
    // formValue.taskType = value.target.value;
    setFormValue({
      taskType: value.target.value,
      pcapList: [],
      taskId: '',
      type: '',
      dir_name: '',
      schedule: {
        period: '',
        baseDate: '',
        baseTime: '',
      },
      taskName: '',
      ftpDir: [],
      curFeatureGrpList: [],
      status: '',
      time: null,
      sourceData: '',
    });
  };
  // 获取数据源表格
  const getPacpDetail = () => {
    getLastUploadDir(pathParams).then(res => {
      if (res.flag) {
        setpath(res.data.detail);
      }
    });
  };
  const getLast = (e: { target: { checked: any } }) => {
    if (e.target.checked) {
      if (pathParams.dir) {
        pathParams.lastest = 1;
        getPacpDetail();
        formValue.pcapList = [];
        setFormValue({ ...formValue });
      }
    } else {
      pathParams.lastest = 0;
      getPacpDetail();
      formValue.pcapList = [];
      setFormValue({ ...formValue });
    }
  };

  useEffect(() => {
    // 获取定时任务数据源
    getDataSource().then(res => {
      if (res.flag) {
        setAlldataSource(res.data.detail);
        setdataSourceArray(res.data.detail)
      } else {
        message.error(res.message);
      }
    });
    // 如果有taskid 说明是编辑 使用redux的值
    // 获取特征组
    if (formValue.taskType === 'singleTask') {
      getPacpSelect();
      getDirList();
      form.setFieldsValue({ ...formValue });
    }
    if (store.getState().formValue.taskId) {
      form.setFieldsValue(store.getState().formValue);
      setFormValue(store.getState().formValue);
      let data = store.getState().formValue;
      // 如果是单次任务
      if (data.taskType === 'singleTask') {
        pathParams.dir = data.dir_name;
        // 获取目录
        // getDirList();
        // 获取数据源表格
        getPacpDetail();
        getTaskPacp({
          taskName: data.taskName,
        }).then(res => {
          if (res.flag) {
            let detail = res.data.detail.map((item: any) => {
              return item.pcapName.substr(1);
            });
            data.pcapList = detail;
            form.setFieldsValue({ pcapList: detail })
            setFormValue(data);
          }
        });
      } else {
        getDataSource().then(res => {
          setAlldataSource(res.data.detail);
        });
      }
    }
  }, []);
  const getPacpSelect = () => {
    getpacpList({ page: 1 }).then(res => {
      if (res.flag) {
        setpcapFilesList(res.data.detail);
      }
    });
  };
  // 获取目录
  const getDirList = () => {
    getDir().then(res => {
      if (res.flag) {
        setAllDirList(res.data.detail);
        store.getState().formValue?.type && changeDir(store.getState().formValue.type, res.data.detail, 'dir');
      }
    });
  };
  const handleBack = () => {
    sessionStorage.removeItem('actionType');
    sessionStorage.removeItem('formValue');
    onhide();
    // history.go(-1);
  };

  const handleSubmit = () => {
    form.validateFields().then((value: any) => {
      if (value.type === 'date') {
        value.exploreStartDate = `${value.time[0].valueOf()}`;
        value.exploreEndDate = `${value.time[1].valueOf()}`;
        delete value.time;
      }
      if (value.schedule) {
        if (value.schedule.period === 'hour') {
          value.schedule.baseTime = moment(value.schedule.baseTime).format('mm:ss');
        } else {
          value.schedule.baseTime = moment(value.schedule.baseTime).format('HH:mm:ss');
        }
      }
      if (actionType === 'add') {
        let params = Object.assign({}, formValue, value);
        if (params.taskType === 'singleTask') {
          delete params.schedule;
          delete params.sourceData;
          delete params.status;
          delete params.taskId;
        }
        addTask(params).then(res => {
          if (res.flag) {
            message.success('操作成功');
            handleBack();
          } else {
            message.error(res.message);
          }
        });
      } else {
        value.action = 'update';
        value.taskId = formValue.taskId;
        let params = Object.assign({}, formValue, value);
        params.exploreStartDate = `${params.exploreStartDate}`;
        params.exploreEndDate = `${params.exploreEndDate}`;
        if (params.taskType === 'singleTask') {
          delete params.schedule;
        } else {
          delete params.type;
        }
        editData(params).then(res => {
          if (res.flag) {
            message.success('操作成功');
            handleBack();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };
  const changePeriod = (value: any) => {
    form.setFieldsValue({
      ...form.getFieldsValue(),
      schedule: {
        period: value,
      },
      pcapList: [],
      taskId: formValue.taskId,
      process: formValue.process,
    });
    setFormValue({
      ...formValue,
      schedule: {
        period: value,
      },
    });
  };
  const renderTree = (data: any) => {
    return data.map((item: any) => {
      const arr = item.fileName.split('/');
      const title = arr[arr.length - 1];
      if (item.children) {
        return (
          <TreeNode title={title} key={item.fileName} dataRef={item}>
            {renderTree(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          title={
            <div style={{ display: 'flex' }}>
              <div>{ellipsis(title, 25)}</div>
              <div>{item.size}</div>
            </div>
          }
          key={item.fileName}
          {...item}
        />
      );
    });
  };
  const Formlayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 10 },
  };
  return (
    <Form {...Formlayout} form={form} onFinish={handleSubmit}>
      <Form.Item
        label="任务类型"
        name="type"
        rules={[
          {
            required: true,
            message: '请选择任务类型！',
          },
        ]}
      >
        <Select
          disabled={actionType == 'edit' && formValue.process !== 1}
          allowClear
          onChange={e => {
            changeDir(e, allDirList, 'dir');
            changeDir(e, alldataSource, 'source');
            form.setFieldsValue({ dir_name: null });
            form.setFieldsValue({ sourceData: null });
          }}
        >
          <Option value={'pcap'} key={'pcap'} children={'流量分析'} />
          <Option value={'other'} key={'other'} children={'文件分析'} />
        </Select>
      </Form.Item>
      <Form.Item
        label="调度类型"
        name="taskType"
        rules={[
          {
            required: true,
            message: '请选择调度类型!',
          },
        ]}
      >
        <Radio.Group disabled={actionType == 'edit'} onChange={changeTaskType}>
          {exploreTaskType.map(item => {
            return (
              <Radio key={item.key} value={item.key}>
                {item.name}
              </Radio>
            );
          })}
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label="任务名称"
        name="taskName"
        rules={[
          {
            required: true,
            message: '请填写任务名称!',
          },
        ]}
      >
        <Input disabled={actionType == 'edit'} />
      </Form.Item>
      {type === 'pcap' && <Form.Item
        label="特征组"
        name="curFeatureGrpList"
        rules={[
          {
            required: true,
            message: '请选择特征组!',
          },
        ]}
      >
        <Select disabled={actionType == 'edit' && formValue.process !== 1} allowClear mode="multiple">
          {pcapFilesList.map((item: { groupId: string; groupName: string }) => {
            return (
              <Option value={item.groupId} key={item.groupId}>
                {item.groupName}
              </Option>
            );
          })}
        </Select>
      </Form.Item>}
      {formValue.taskType === 'singleTask' ? (
        <Form.Item label="选择目录">
          <Form.Item
            rules={[
              {
                required: true,
                message: '请选择目录!',
              },
            ]}
            name="dir_name"
          >
            <Select disabled={actionType == 'edit' && formValue.process !== 1} onChange={getChooseDir}>
              {dirList.map((item: { dirName: string }) => {
                return (
                  <Option key={item.dirName} value={item.dirName}>
                    {item.dirName}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form.Item>
      ) : (
          <>
          <Form.Item
            label="数据源"
            name="sourceData"
            rules={[
              {
                required: true,
                message: '请选择数据源!',
              },
            ]}
          >
            <Select disabled={actionType == 'edit'}>
              {dataSourceArray.map((item: { serverId: string; aliasName: string }) => {
                return (
                  <Option value={item.serverId} key={item.serverId}>
                    {item.aliasName}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>

          <Form.Item
            label="调度周期"
            name={['schedule', 'period']}
            rules={[
              {
                required: true,
                message: '请选择调度周期!',
              },
            ]}
          >
            <Select onChange={changePeriod} disabled={actionType == 'edit' && formValue.process !== 1}>
              {exploreCycle.map(item => {
                return (
                  <Option value={item.key} key={item.key}>
                    {item.name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          {formValue.schedule.period === 'week' ? (
            <Form.Item
              label="星期"
              name={['schedule', 'baseDate']}
              rules={[
                {
                  required: true,
                  message: '请填写星期!',
                },
              ]}
            >
              <InputNumber disabled={actionType == 'edit' && formValue.process !== 1} min={1} max={7} />
            </Form.Item>
          ) : null}
          {formValue.schedule.period === 'month' ? (
            <Form.Item
              label="日期"
              name={['schedule', 'baseDate']}
              rules={[
                {
                  required: true,
                  message: '请填写日期!',
                },
              ]}
            >
              <InputNumber disabled={actionType == 'edit' && formValue.process !== 1} min={1} max={31} />
            </Form.Item>
          ) : null}
          <Form.Item
            label="时间"
            name={['schedule', 'baseTime']}
            rules={[
              {
                required: true,
                message: '请填写时间!',
              },
            ]}
          >
            <TimePicker
              disabled={actionType == 'edit' && formValue.process !== 1}
              format={formValue.schedule.period === 'hour' ? 'mm:ss' : 'HH:mm:ss'}
            />
          </Form.Item>
          {actionType === 'edit' ? null : (
            <Form.Item
              label="立即触发任务调度"
              name="executeNow"
              rules={[
                {
                  required: true,
                  message: '请选择!',
                },
              ]}
              initialValue={false}
            >
              <Radio.Group>
                {backWork.map((item, index) => {
                  return (
                    <Radio key={item.name} value={item.key}>
                      {item.name}
                    </Radio>
                  );
                })}
              </Radio.Group>
            </Form.Item>
          )}
          {actionType == 'edit' ? (
            <Form.Item
              label="重新处理数据源数据"
              initialValue={false}
              name="dealWithAll"
              rules={[
                {
                  required: true,
                  message: '请选择重新处理数据源数据!',
                },
              ]}
            >
              <Radio.Group>
                {backWork.map((item: { key: any; name: string }) => {
                  return (
                    <Radio disabled={actionType == 'edit' && formValue.process !== 1} value={item.key} key={item.key}>
                      {item.name}
                    </Radio>
                  );
                })}
              </Radio.Group>
            </Form.Item>
          ) : null}
          </>
      )}

      {
        formValue.taskType === 'singleTask' ? (
          <div>
            {path && path.length ? (
              <div>
                <Form.Item
                  name="pcapList"
                  label="选择文件"
                  rules={[{ required: true, message: '请选择文件!' }]}
                >
                  <SelectorWithCheckAll
                    options={path.map((item: any) => ({
                      label: item.fileName,
                      value: item.fileName,
                    }))}
                    disabled={actionType == 'edit' && formValue.process !== 1}
                  />
                </Form.Item>
              </div>
            ) : null}
          </div>
        ) : null
      }

      {actionType === 'edit' ? (
        <Form.Item
          label="立即触发任务调度"
          name="executeNow"
          rules={[
            {
              required: true,
              message: '请选择!',
            },
          ]}
          initialValue={false}
        >
          <Radio.Group disabled={actionType == 'edit' && formValue.process !== 1}>
            {backWork.map((item, index) => {
              return (
                <Radio key={item.name} value={item.key}>
                  {item.name}
                </Radio>
              );
            })}
          </Radio.Group>
        </Form.Item>
      ) : null}
      <div className={style.btnGroup}>
        <Button
          type="primary"
          disabled={actionType == 'edit' && formValue.process !== 1}
          htmlType="submit"
        >
          确定
        </Button>
        <Button
          className={style.determine}
          onClick={handleBack}
        >
          返回
        </Button>
      </div>
    </Form>
  );
};
export default Addtask;
