# -*-coding:utf-8-*-
# @File    : system_monitor.data.py
# @Software: PyCharm


from utils.param_check import Validator
from flask import request
from flask_restful import Resource
from utils import param_check
from utils.logger import get_ndr_logger
from config.config import Ndr<PERSON>og
from celery_tasks import app
from utils.utils import flask_response
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class GetServiceData(Resource):

    def get(self):
        try:
            res = app.send_task('host_celery.tasks.get_service_data', args=[], queue='monitor_task',
                                routing_key='monitor_task')
            return_data = res.get(timeout=5)
            for service_data in return_data:
                if service_data['status'] == 'stop':
                    ndr_log_to_box(NdrLog.Type.RUN, event='%s服务 already stop' % service_data['name'])
            return flask_response("ServiceData get successfull", True, return_data)
        except Exception as error:
            LOG.error("ServiceData get failed.Reason：%s" % str(error))
            return flask_response("ServiceData get failed", False, {})

    @param_check.check_flask_args(Validator('service_monitor_schema'), request)
    def put(self, **kwargs):
        try:
            server_name = kwargs['serverName']
            action = kwargs['action']
            if server_name and action:
                app.send_task('host_celery.tasks.handle_service', args=[server_name, action], queue='monitor_task',
                              routing_key='monitor_task')
                ndr_log_to_box(NdrLog.Type.OPERATE, event='%s服务 is %sing' % (server_name, action))
            return flask_response("handle successfull", True, {})
        except Exception as error:
            LOG.error("Service handle failed: failed.Reason：%s" % str(error))
            return flask_response("handle failed", False, {})
