/*
 * @Author: tianh
 * @Date: 2021-11-18 18:06:41
 * @LastEditors: tianh
 * @LastEditTime: 2021-11-19 14:23:49
 * @Descripttion:
 */
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export const getModelList = (data: any) => {
  return request(`model/manage${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const changeModel = (data: any) => {
  return request(`model/manage`, {
    method: 'put',
    data,
  });
};

// 获取特征库升级日志
export const getFeatureLogs = (params:{page:number;pageSize:number}) => {
  return request(`feature/update_log`, {
    method: 'get',
    params
  });
};

// 获取特征库版本信息
export const getFeatureVersion = () => {
  return request(`feature/db_version`, {
    method: 'get',
  });
};

// 获取情报库库升级日志
export const getInfoLogs = (params:{page:number;pageSize:number}) => {
  return request(`ioc/update_log`, {
    method: 'get',
    params
  });
};

// 获取情报库版本信息
export const getInfoVersion = () => {
  return request(`ioc/db_version`, {
    method: 'get',
  });
};
