#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import json
import os
import time
import traceback
import hashlib
import xlrd

from flask import request, send_file, make_response
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator
from config.config import NdrLog
from utils import param_check
from bson import ObjectId
from werkzeug.utils import secure_filename
from celery_tasks import app

TABLE_NAME = 'cert_hive_source'

DATA_TYPES = {'conn', 'url', 'tls', 'dns', 'ljc'}

HIVE_STATUS_IDLE = 'idle'
HIVE_STATUS_RUNNING = 'running'
HIVE_STATUS_FAILED = 'failed'
HIVE_STATUS_SUCCESS = 'success'

#HIVE_RULE_FILE_DIR = '/tmp/hive_rule_files'
HIVE_RULE_FILE_DIR = '/tmp'
HIVE_TEMPLATE_FILE_DIR = '/opt/ndr/api_1_0/thirdparty/cert'

HIVE_FIELDS = {
    "source_name": "",
    "data_types": "",
    "start_time": "",
    "end_time": "",
}

AUTO_HIVE_FIELDS = [
    "update_id",
    "status",
    "created_at",
    "updated_at",
]


def data_from_args(args):
    data = {}
    for k in HIVE_FIELDS:
        if k in args:
            data[k] = args.get(k)

    return data


def timestamp():
    return int(time.time() * 1000)


def parse_file(file_obj):
    file_path = HIVE_RULE_FILE_DIR + '/' + secure_filename(file_obj.filename)
    file_obj.save(file_path)
    try:
        data = xlrd.open_workbook(file_path)
        table = data.sheet_by_index(0)
        rules = []
        for num in range(1, table.nrows):
            parm = []
            parm += table.row_values(num)
            ip, ports = parm[0].strip(), str(parm[1])
            if not ip:
                continue
            if not ip.split('.')[0].isdigit() and not ip.startswith('{'):
                raise Exception('Invalid ip format: %s' % ip)
            ports = ','.join([str(int(float(i.strip()))) if i.strip() else '' for i in ports.split(',')])
            if ports.find('.') != -1:
                raise Exception('Invalid ports: %s' % ports)
            rules.append({"ip": ip, "ports": ports})
        os.remove(file_path)
        return rules
    except Exception as ex:
        traceback.print_exc()
        os.remove(file_path)
        return []


def update_id(data):
    value = '-'.join([data.get(key, '') for key in ['source_name', 'data_types']])
    value += '-' + str(data.get('start_time', 0)) + '-' + str(data.get('end_time', 0))
    value += '-' + json.dumps(data.get('rules', ''))
    value += '_' + str(time.time())
    return hashlib.md5(value.encode()).hexdigest()


LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class HiveRuleTemplate(Resource):
    def get(self):
        file_path = os.path.join(HIVE_TEMPLATE_FILE_DIR, 'hive_rule_template.xlsx')
        if not os.path.exists(file_path):
            return flask_response('hive rule template file not found!', True, {})

        ret = send_file(file_path, as_attachment=True)
        return make_response(ret)


class Hives(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    @staticmethod
    def query_condition(args):
        condition = {}

        if args.get("start_time") or args.get("stop_time"):
            condition["created_at"] = {
                "$gte": int(args.get("start_time", 0)),
                "$lte": int(args.get("stop_time", 2 ** 63 - 1))
            }

        keys = ["source_name", "data_types"]
        for k, v in args.items():
            if k in keys and v:
                condition[k] = v

        return condition

    @param_check.check_flask_args(Validator("cert_hive_source_search_schema"), request)
    def get(self, **kwargs):
        """
        获取数据源列表
        """
        args = kwargs.get("args")
        page = int(args.get("page"))
        page_size = int(args.get("pageSize"))
        try:
            condition = self.query_condition(args)
            total = self.mongodb.find(self.table_name, condition).count()
            result = self.mongodb.find(self.table_name, condition).sort([("updated_at", -1)]).limit(page_size).skip(
                (page - 1) * page_size)

            cols = []
            for r in result:
                r["_id"] = str(r["_id"])
                r['statistics'] = ' '.join([f'{k}({v})' for k, v in r['statistics'].items() if k in r['data_types']])
                cols.append(r)

            data = {"total": total,
                    "cols": cols,
                    "page": page,
                    "pageSize": page_size}
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Get hive sources ERROR: %s' % e)
            return flask_response('failed', False, {})

    def post(self):
        """
        创建数据源
        """
        if 'file' not in request.files:
            return flask_response("no file part.", False, {})
        file = request.files['file']
        if file.filename == '':
            return flask_response("no selected file.", False, {})
        rules = parse_file(file)
        if not rules:
            return flask_response("file data invalid.", False, {})

        source_name = request.form.get('source_name')
        if not source_name:
            return flask_response("no source name.", False, {})

        data_types = request.form.get('data_types')
        if not data_types:
            return flask_response("no data types.", False, {})
        data_types_list = []
        for t in data_types.split(','):
            v = t.strip()
            if v not in DATA_TYPES:
                return flask_response("data type error.", False, {})
            data_types_list.append(v)

        data = dict(source_name=source_name, data_types=','.join(data_types_list),
                    start_time=request.form.get('start_time'), end_time=request.form.get('end_time'),
                    rules=rules)
        data['update_id'] = update_id(data)
        data['status'] = HIVE_STATUS_IDLE
        data['created_at'] = timestamp()
        data['updated_at'] = data['created_at']
        data['statistics'] = {'conn': 0, 'url': 0, 'dns': 0, 'tls': 0, 'ljc': 0}

        try:
            inserted_id = self.mongodb.insert_one(self.table_name, data)
            if inserted_id == "":
                raise ValueError("insert failed")

            data['_id'] = str(inserted_id)
            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源创建成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Create hive sources ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源创建失败")
            return flask_response('create failed', False, {})


class Hive(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def get(self, **kwargs):
        """
        获取数据源
        """
        try:
            condition = {'_id': ObjectId(kwargs['_id'])}
            data = self.mongodb.find_one(self.table_name, condition)
            data['_id'] = str(data['_id'])
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Create hive source ERROR: %s' % e)
            return flask_response('', False, {})

    def put(self, **kwargs):
        """
        更新数据源
        """
        condition = {'_id': ObjectId(kwargs['_id'])}

        try:
            r = self.mongodb.find_one(self.table_name, condition)
            if not r:
                raise ValueError("not found")

            data = {}
            if 'file' in request.files:
                file = request.files['file']
                if file.filename == '':
                    return flask_response("no selected file.", False, {})
                rules = parse_file(file)
                if not rules:
                    return flask_response("file data invalid.", False, {})
                data['rules'] = rules

            # source_name = request.form.get('source_name')
            # if source_name:
            #     data['source_name'] = source_name

            data_types = request.form.get('data_types')
            if data_types:
                data_types_list = []
                for t in data_types.split(','):
                    v = t.strip()
                    if v not in DATA_TYPES:
                        return flask_response("data type error.", False, {})
                    data_types_list.append(v)

                data['data_types'] = ','.join(data_types_list)

            start_time = request.form.get('start_time')
            if start_time:
                data['start_time'] = request.form.get('start_time')

            end_time = request.form.get('end_time')
            if end_time:
                data['end_time'] = request.form.get('end_time')

            for k in ['source_name', 'data_types', 'start_time', 'end_time', 'rules']:
                if k not in data:
                    data[k] = r[k]

            data['update_id'] = update_id(data)
            data['updated_at'] = timestamp()
            r = self.mongodb.update_one(self.table_name, condition, {"$set": data})
            if r == 0:
                raise ValueError("update failed")

            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源更新成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Update hive sources ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源更新失败")
            return flask_response('update failed', False, {})

    def delete(self, **kwargs):
        """
        创建数据源
        """
        condition = {'_id': ObjectId(kwargs['_id'])}
        try:
            r = self.mongodb.delete(self.table_name, condition)
            if r == 0:
                raise ValueError("delete failed")

            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源删除成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error('Delete hive sources ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源删除失败")
            return flask_response('delete failed', False, {})


class HiveRun(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def post(self, **kwargs):
        """
        启动数据源数据获取任务
        """
        condition = {'_id': ObjectId(kwargs['_id'])}
        try:
            doc = self.mongodb.find_one(self.table_name, condition)
            if not doc:
                raise ValueError("source not found")

            if doc['status'] == HIVE_STATUS_RUNNING:
                return flask_response('already running', True, {})

            result = run_task(doc['source_name'])
            status = HIVE_STATUS_RUNNING if len(result) == 0 else HIVE_STATUS_FAILED
            self.mongodb.update_one(self.table_name, condition, {
                "$set": {'status': status, 'update_id': update_id(doc),
                         'statistics': {'conn': 0, 'url': 0, 'dns': 0, 'tls': 0, 'ljc': 0}}})

            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源获取数据任务启动成功")
            return flask_response('', True, result)
        except Exception as e:
            LOG.error('Run hive source ERROR: %s %s' % (e, traceback.format_exc()))
            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源获取任务启动失败")
            return flask_response(str(e), False, {})


class HiveStop(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def post(self, **kwargs):
        """
        停止数据源数据获取任务
        """
        condition = {'_id': ObjectId(kwargs['_id'])}
        try:
            doc = self.mongodb.find_one(self.table_name, condition)
            if not doc:
                raise ValueError("source not found")

            if doc['status'] != HIVE_STATUS_RUNNING:
                return flask_response('not running', True, {})

            result = stop_task(doc['source_name'])
            if len(result) != 0:
                raise ValueError(json.dumps(result))

            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源获取任务停止成功")
            return flask_response('', True, result)
        except Exception as e:
            LOG.error('Run hive source ERROR: %s %s' % (e, traceback.format_exc()))
            ndr_log_to_box(NdrLog.Type.OPERATE, "数据源获取任务停止失败")
            return flask_response(str(e), False, {})


def run_task(source_name):
    app.send_task('host_celery.cert.hive_source_run', args=[source_name], queue='host_task', routing_key='host_task')
    return ""


def stop_task(source_name):
    r = app.send_task('host_celery.cert.hive_source_stop', args=[source_name], queue='host_task', routing_key='host_task')
    return r.get(timeout=5)
