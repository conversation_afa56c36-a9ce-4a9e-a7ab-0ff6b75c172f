import React, { useEffect } from 'react';

// 获取当前host
const host = window.location.host;
const agreement = window.location.protocol

export default () => {
    useEffect(() => {
        // 新窗口打开大屏
        window.open(`${agreement}//${host}/screen`);
        // 跳转到规则告警页面
        window.location.href = `${agreement}//${host}/app/mica/alarm/loopHole`
        // window.location.href = `${agreement}//${host}/screen`
    }, [])
    return (
        <></>
    )
}