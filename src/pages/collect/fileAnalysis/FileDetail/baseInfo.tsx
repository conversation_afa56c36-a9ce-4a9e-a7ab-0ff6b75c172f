import React, { useEffect, useMemo, useState } from 'react';
import cn from 'classnames';
import { Button, List, Tag } from 'antd';
import { THREAT_LEVEL_FILE, FILE_COLOR } from '@/utils/constants';
import { useHistory } from '@/hooks/global';
import computer from '@/assets/images/fileType.png';
import { getFileReportBin } from '@/services/collect';
import s from './style.less';
interface BaseInfoProps {
  base: any;
}
const BaseInfo = ({ base }: BaseInfoProps) => {
  const history = useHistory();
  const params = JSON.parse(history.location.query.params);
  const downFun = () => {
    const filePath = `${params.file_path}/${params.filename}`
    const url = getFileReportBin({ filePath, md5: base.md5 });
    window.open(`${window.location.origin}/mica-api/api/v1/${url}`);
  };

  const data = useMemo(() => {
    return [
      {
        title: 'MD5',
        key: 'md5',
        content: base.md5 || '--',
      },
      {
        title: '病毒家族',
        key: 'threatName',
        content: base['threatName'] || '--',
      },
      {
        title: '信誉名单',
        content: '--',
      },
      {
        title: '开源情报',
        content: '--',
      },
      {
        title: '标签',
        key: 'labels',
        render: () => {
          const str = base['labels'] || '';
          const arr = str.split(',');
          if (arr.length) {
            return (
              <div className="a123">
                {arr.map(e => (
                  <Tag className="!mb-1">{e}</Tag>
                ))}
              </div>
            );
          }
          return '- -';
        },
      },
    ];
  }, [base]);
  return (
    <div className={cn('h-22rem of-hidden pa-2', s.base_info)}>
      <p className={s.title}>
        <span>基本信息</span>
        <Button
          onClick={() => {
            downFun();
          }}
          className={s.downBtn}
        >
          下载
        </Button>
      </p>
      <div className="flex flex-1">
        <div className="w-30% flex flex-col justify-center">
          <div className="pos-relative flex justify-center">
            <img width={100} src={computer} />
            <p className="pos-absolute bottom-4 ">{base.filetype}</p>
          </div>
          <p className="mt-4 ml-2rem">
            等级：
            <span className={s.file_level} style={{ background: FILE_COLOR[base.threatLevel] }}>
              {THREAT_LEVEL_FILE[base.threatLevel]}
            </span>
          </p>
          <p className="mt-2 ml-2rem">类型：{base.filetype}</p>
        </div>
        <div className={cn('w-70%', s.listBox)}>
          {data.map((item, i) => {
            return (
              <div className={cn('flex p-1 items-center', i % 2 && s.f4f5f6)}>
                <div className="w-4.5rem flex-shrink-0">{item.title}</div>
                <div>{!item.render ? item.content : item.render()}</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
export default BaseInfo;
