{
	"type": "object",
	"required": ["lockheedKillchainEN", "featureContent", "threatFlag", "threatScore", "sid", "attackIp", "victimIp", "appProto", "lockheedKillchainCN"],
	"properties": {
		"lockheedKillchainEN": {
			"type": "string",
			"enum": ["Reconnaissance", "Weaponization", "Delivery", "Exploitation", "Installation", "Command and Control", "Actions on Objective"]
		},
		"featureContent": {
			"type": "string",
			"pattern": r'''alert(.*?)sid:(.*?)'''
		},
		"threatFlag": {
			"type": "string",
			"default": "",
            "enum": ["Exploits and Attacks", "APT", "Malicious Host", "Suspicious", "Botnet", "Phishing",
				"Scanning", "Malware", "DOS", "Trojan", "Mining", "Ransomware", "Spyware", "Webshell", "URL_malware",
				"Brute force"]
		},
		"author": {
			"type": "string",
			"pattern": r'''^([\w-]{0,20})$'''
		},
		"vulType": {
			"type": "string",
			"pattern": r'''^([\s*\w-]{0,20})$'''
		},
		"lockheedKillchainCN": {
			"type": "string",
			"enum": ["侦查跟踪", "武器构建", "载荷投递", "漏洞利用", "安装植入", "命令控制", "目标达成"]
		},
		"alterInfo": {
			"type": "string",
			"pattern": r'''^([\w]{0,150})$'''
		},
		"threatScore": {
			"type": "integer",
			"minimum": 0,
			"maximum": 100
		},
		"lockheedKillchainStage": {
			"type": "integer",
			"minimum": 1,
			"maximum": 7
		},
		"sid": {
			"type": "string",
			"pattern": r'''(^[1-9]\d{0,8}$)|(^[1-3]\d{9}$)|(^4[0-1]\d{8}$)|(^42[0-8]\d{7}$)|(^429[0-3]\d{6}$)|(^4294[0-8]\d{5}$)|(^42949[0-5]\d{4}$)|(^429496[0-6]\d{3}$)|(^4294967[0-1]\d{2}$)|(^42949672[0-8]\d$)|(^429496729[0-5]$)'''
		},
		"attackIp": {
			"type": "string",
			"enum": ["src_ip", "dst_ip"]
		},
		"victimIp": {
			"type": "string",
			"enum": ["src_ip", "dst_ip"]
		},
		"appProto": {
			"type": "string",
			"enum": ["tcp", "http", "ftp", "ssh", "tls", "smb", "dns", "udp", "icmp", "smtp"]
		},
		"submitTime": {
		    "type": "string",
		    "pattern": r'''^((?:19|20)\d\d)-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$'''
		},
		"cve": {
			"type": "string",
			"pattern": r'''^$|^[cC][vV][eE]-\d{4}-\d{3,9}$'''
		},
		"is0day":{
	        "type": "string",
	        "enum": ["False", "True"]
		},
		"featureStatus":{
		    "type": "string",
	        "enum": ["disable", "enable", "deprecated"]
		}
	}
}