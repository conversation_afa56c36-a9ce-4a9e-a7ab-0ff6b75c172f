#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import json
from flask import request
from api_1_0.report.base_map import BaseMap
from utils.param_check import Validator
from utils.utils import flask_response
from utils import param_check
from utils.logger import get_ndr_logger
from utils.best_show_size import size_format

API_LOG = get_ndr_logger('api_log', __file__)


class ThreatLevelMap(BaseMap):
    '''威胁等级流量图'''

    def __init__(self):
        super(ThreatLevelMap, self).__init__("threatLevel.keyword")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        es_return = self.get_es_data(**kwargs)
        message = es_return[1]
        if not es_return[0]:
            return flask_response(message, False, {})

        try:
            data = self.data_format(message)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(message, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, message):
        docs = []
        total_threat = {
            "Low": 0,
            "Medium": 0,
            "High": 0
        }
        total = {}
        data_list = message["aggregations"]["group_by"]["buckets"]
        for data in data_list:
            threat_dict = dict()
            time_stamp = data["key"]
            threatFlow = dict()
            for threat in data["group_by"]["buckets"]:
                # 单位，返回为Mb
                flow_value = threat["totalFlow"]["value"]
                total_threat[threat["key"]] += flow_value
                threatFlow[threat["key"]] = {
                    "actualSize": flow_value,
                    "showSize": size_format(flow_value)
                }
            threat_dict["time"] = time_stamp
            threat_dict["threatFlow"] = threatFlow
            docs.append(threat_dict)
        threats = sorted(total_threat.items(), key=lambda x: ["High", "Medium", "Low"].index(x[0]))
        for key, value in threats:
            total[key] = {
                "actualSize": value,
                "showSize": size_format(value)
            }
        reslut_docs = self.slice_time_data(docs, "threatFlow")
        data_return = {
            "total": total,
            "slice_flow": reslut_docs
        }
        return data_return
