import React, { useState, useEffect } from 'react';
import { Tabs } from 'antd';
import Http from './logs/http';
import Dns from './logs/dns';
import Ftp from './logs/ftp';
import Ssl from './logs/ssl';
import Icmp from './logs/icmp';
import Files from './logs/files';
import Mail from './logs/mail';
import Conn from './logs/conn';
import Mysql from './logs/mysql';
import Login from './logs/login';
import Dhcp from './logs/dhcp';
import Modbus from './logs/modbus';
import Nfs from './logs/nfs';
import Telnet from './logs/telnet';
import Snmp from './logs/snmp';
import Rip from './logs/rip';
import Netbios from './logs/netbios';
import Tftp from './logs/tftp';
import Mssql from './logs/mssql';
import Igmp from './logs/igmp';
import Smb from './logs/smb';
import { getQueryVariable } from '@/utils/utils';
import { logTypes } from './constant';
import { getTaskName } from '@/services/common';
import moment from 'moment';
import store from './store';

const { TabPane } = Tabs;

const Index = () => {
  const [activeKey, setactiveKey] = useState('http');
  const [taskList, settaskList] = useState([]);
  const [params, setparams] = useState<any>({
    time: [],
    startTime: null,
    stopTime: null,
    ip1: '',
    ip2: '',
    page: 1,
    celeryId: '',
    isAccurate: false,
    dpilogType: 'dpilog_http',
    pcap_filename: '',
    pageSize: 10,
  });

  useEffect(() => {
    if (getQueryVariable('celeryId')) {
      params.celeryId = getQueryVariable('celeryId');
    }
    if (getQueryVariable('startTime') && getQueryVariable('endTime')) {
      params.startTime = parseInt(getQueryVariable('startTime'));
      params.stopTime = parseInt(getQueryVariable('endTime'));
      params.time = [moment(params.startTime), moment(params.stopTime)];
    }
    if (getQueryVariable('ip') || getQueryVariable('ip2') || getQueryVariable('pcap_filename')) {
      params.ip1 = getQueryVariable('ip');
      params.ip2 = getQueryVariable('ip2');
      // 判断是否包含中文进行转码
      if (getQueryVariable('includeCN')) {
        params.pcap_filename = decodeURI(getQueryVariable('pcap_filename'));
      } else {
        params.pcap_filename = getQueryVariable('pcap_filename');
      }
    }
    if (getQueryVariable('application')) {
      params.application = getQueryVariable('application');
    }
    const logType = getQueryVariable('dpilogType');
    if (logType) {
      if (logTypes.includes(logType)) {
        params.dpilogType = `dpilog_${logType}`;
        setactiveKey(getQueryVariable('dpilogType'));
      } else {
        params.dpilogType = 'dpilog_conn';
        setactiveKey('conn');
      }
    }
    const saveColumns = {
      type: 'actionParams',
      value: { ...params },
    };
    store.dispatch(saveColumns);

    setparams({ ...params });
    return () => {
      const saveColumns = {
        type: 'changeColumns',
        value: [],
      };
      store.dispatch(saveColumns);
    };
  }, []);

  // 改变日志类型
  const onChange = (value: any) => {
    if (logTypes.includes(value)) {
      params.dpilogType = `dpilog_${value}`;
    } else {
      params.dpilogType = 'dpilog_conn';
    }
    setactiveKey(value);
    setparams({ ...params, page: 1, pageSize: 10 });
  };

  // 获取任务名字
  const getSelectTaskName = () => {
    getTaskName({}).then((res) => {
      if (res.flag) {
        res.data.push({
          taskName: '实时任务',
          celeryId: '00',
        });
        settaskList(res.data);
      }
    });
  };

  useEffect(() => {
    getSelectTaskName();
  }, []);

  return (
    <div>
      <Tabs type="card" activeKey={activeKey} onChange={onChange}>
        <TabPane style={{ position: 'relative' }} tab="http" key="http">
          {activeKey === 'http' ? (
            <Http params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="dns" key="dns">
          {activeKey === 'dns' ? (
            <Dns params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="ftp" key="ftp">
          {activeKey === 'ftp' ? (
            <Ftp params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="ssl" key="ssl">
          {activeKey === 'ssl' ? (
            <Ssl params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="icmp" key="icmp">
          {activeKey === 'icmp' ? (
            <Icmp params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="files" key="files">
          {activeKey === 'files' ? (
            <Files params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="mail" key="mail">
          {activeKey === 'mail' ? (
            <Mail params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="conn" key="conn">
          {activeKey === 'conn' ? (
            <Conn params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="mysql" key="mysql">
          {activeKey === 'mysql' ? (
            <Mysql params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="login" key="login">
          {activeKey === 'login' ? (
            <Login params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="telnet" key="telnet">
          {activeKey === 'telnet' ? (
            <Telnet params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="nfs" key="nfs">
          {activeKey === 'nfs' ? (
            <Nfs params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="modbus" key="modbus">
          {activeKey === 'modbus' ? (
            <Modbus params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="dhcp" key="dhcp">
          {activeKey === 'dhcp' ? (
            <Dhcp params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="snmp" key="snmp">
          {activeKey === 'snmp' ? (
            <Snmp params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="rip" key="rip">
          {activeKey === 'rip' ? (
            <Rip params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="tftp" key="tftp">
          {activeKey === 'tftp' ? (
            <Tftp params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="netbios" key="netbios">
          {activeKey === 'netbios' ? (
            <Netbios params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="mssql" key="mssql">
          {activeKey === 'mssql' ? (
            <Mssql params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="igmp" key="igmp">
          {activeKey === 'igmp' ? (
            <Igmp params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
        <TabPane tab="smb" key="smb">
          {activeKey === 'smb' ? (
            <Smb params={params} setparams={setparams} activeKey={activeKey} taskList={taskList} />
          ) : null}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Index;
