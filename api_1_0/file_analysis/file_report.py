#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import base64
import os

import json
from flask import request, send_file, make_response

from api_1_0.utils.flask_log import ndr_log_to_box
from api_1_0.file_analysis.file_statistics import FileResource
from config.config import FileReport, NdrLog
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class FileAnalysisReport(FileResource):
    """
    文件分析报告
    """

    def __init__(self):
        super(FileAnalysisReport, self).__init__()

    @param_check.check_flask_args(Validator("file_analysis_report_schema"), request)
    def get(self, **kwargs):
        """
        文件报告内容
        :return:
        """
        file_path = kwargs["args"]["filePath"]

        try:
            with open(file_path, 'r', encoding='utf-8') as fcc_file:
                return_data = json.load(fcc_file)
            name = file_path.split('/')[-1][:-7]
            path_data = []
            for path_object in return_data.get("screenshots", []):
                file_name = os.path.join(FileReport.Path, 'image', name, path_object["path"].split('/')[-1])
                with open(file_name, 'rb') as img_f:
                    img_stream = img_f.read()
                    img_stream = base64.b64encode(img_stream)
                    path_object["img_stream"] = str(img_stream, encoding='utf-8')
                path_data.append(path_object)
            return_data["screenshots"] = path_data

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "获取文件分析报告失败")
            return flask_response(err_info, False, {})
        ndr_log_to_box(NdrLog.Type.OPERATE, "获取文件分析报告成功")
        return flask_response('获取成功', True, return_data)


class FileAnalysisReportExport(FileResource):
    """
    文件分析-源文件导出
    """

    def __init__(self):
        super(FileAnalysisReportExport, self).__init__()

    @param_check.check_flask_args(Validator("file_analysis_report_schema"), request)
    def get(self, **kwargs):
        """
        源文件导出
        :return:
        """
        file_path = kwargs["args"]["filePath"]

        try:
            if os.path.exists(file_path):
                ndr_log_to_box(NdrLog.Type.OPERATE, "导出文件分析报告成功")
                if kwargs['md5'] != '':
                    return make_response(
                        send_file(file_path, as_attachment=True, attachment_filename=f'{kwargs["md5"]}.bin'))
                else:
                    return make_response(send_file(file_path, as_attachment=True))
            else:
                ndr_log_to_box(NdrLog.Type.OPERATE, "导出文件分析报告失败, 文件不存在")
                return flask_response("Find bin file failed!", False, {})
        except Exception as e:
            LOG.error('reason: %s' % str(e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "导出文件分析报告失败")
            return flask_response("Download bin file failed!", False, {'error msg': str(e)})
