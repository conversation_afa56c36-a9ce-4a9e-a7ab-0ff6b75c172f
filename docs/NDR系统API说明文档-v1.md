# NDR系统API说明文档

[TOC]

## 0x00 NDR系统「用户管理」

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | Sprint 未完成 | 下个Sprint开发 |

* 用户登录

  目前用户登陆获取 token

  postman 或浏览器登录后，会根据认证生成的 cookie 做鉴权处理，第三方在程序中进行 API 调用时需要在 headers 中指定认证通过的 token（有效时长为 24 小时），指定格式如下：

  | key           | vaule    |
  | ------------- | -------- |
  | Authorization | $(token) |

  **请求参数：**

  | 参数名   | 必须 | 类型   | 说明   |
  | :------- | :--- | :----- | :----- |
  | username | True | String | 用户名 |
  | password | True | String | 密码   |

  ```bash
  /api/v1/login POST
  {
  		"username": "$(username)",
  		"password": "$(password)
  }
  
  response:
  # 登录成功
  {
      "message": "success",
      "data": {
          "id": "a1b1d6818aa067c05b6f5a6953d1ce7b",
          "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE1NjA5MzQ1NjAsImlhdCI6MTU2MDkzMDk2MCwiZGF0YSI6eyJpZCI6ImExYjFkNjgxOGFhMDY3YzA1YjZmNWE2OTUzZDFjZTdiIiwibG9naW5fdGltZSI6IjIwMTktMDYtMTkgMTU6NTU6NTkiLCJzdGF0dXMiOiJvbmxpbmUifX0.4oDvwyXW18Fs1bfd0by6YnOjAxrbdIi_I_VujwZD7Y8"
      }
  }
  
  # 用户不存在
  {
      "message": "failed",
      "data": "The user does not exist"
  }
  
  # 密码错误
  {
      "message": "failed",
      "data": "Wrong password"
  }
  ```

* 用户登出

  **请求参数：**

  | 参数名 | 必须 | 类型   | 说明    |
  | :----- | :--- | :----- | :------ |
  | id     | True | String | 用户 ID |

  ```bash
  /api/v1/logout post
  {
  	"id":"$(id)"
  }
  
  response:
  # 登出成功
  {
      "message": "success",
      "data": ""
  }
  
  # 用户未登录或 token 不正确
  {
      "message": "failed",
      "data": "Not logged in"
  }
  
  # 用户不存在
  {
      "message": "failed",
      "data": "User does not exist"
  }
  
  # 其它 API 未正确使用 token
  {
      "message": "failed",
      "data": "Token error"
  }
  ```

* 用户密码修改

  **请求参数：**
  
  | 参数名       | 必须 | 类型   | 说明   |
  | :----------- | :--- | :----- | :----- |
  | username     | True | String | 用户名 |
  | old_password | True | String | 旧密码 |
  | new_password | True | String | 新密码 |
  
  ```bash
  /api/v1/change_password PUT
  {
  	"username":"$(username)",
  	"oldPassword":"$(old_password)",
  	"newPassword":"$(new_password)"
  	
  }
  response:
  # 修改成功
  {
      "message": "success",
      "data": {}
  }
  # 旧密码不正确
  {
      "message": "failed",
      "data": "Wrong old password"
  }
  ```

<br/>

------------------------------------------

## 0x01 NDR系统「BackIDS」

### 0x0101 「漏洞」攻击行为统计（高优先级）

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 漏洞知识库 | 鲁悦提供：规则详情、sid、threatFlag、attacker、 lockheedkillchainsCn、lockheedkillchainsEn、  victimtor、vulName、cve、threatScore |
  

* 类似一个Dashboard模块，没有具体调查工作时，作为日常的统一浏览入口；

* 根据漏洞聚合：查看 top n 触发的漏洞攻击行为，包括：漏洞的名称、触发次数、对应的每一条攻击日志（时间、五元组、payload、命中规则、killchains）；

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | vulName为空或为多个如何处理 |  |
  | 5.22-6.10 | 输出结果按API文档格式化 |  |

  **请求参数：**

  | 参数名    | 必须  | 类型   | 说明     |
  | :-------- | :---- | :----- | :------- |
  | keyword   | True  | String | 关键字   |
  | topNum    | True  | String | 排行     |
  | startTime | False | String | 开始时间 |
  | stopTime  | False | String | 结束时间 |
  | page      | False | String | 页数     |
  | pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/group GET （默认按照 24 小时聚合）
  ?keyword=vulName&topNum=20&startTime=1557475221&stopTime=1557907236&page=&pagesize=
  
  startTime: 查询的session起始时间，可为空，unix时间戳格式
  stopTime: 查询的session结束时间，可为空，unix时间戳格式
  
  response:
  {
    "message": "",
    "data": [
        {
            "vulName": "weblogic 漏洞",
            "triggerCount": 292844,
            "alertRecords": [
                {
                    "src_ip": "**************",
                    "src_port": 449,
                    "rulesInfo": {
                        "attackIp": "************",
                        "cve": "cve-2019-006",
                        "victimIp": "**************",
                        "vulName": "weblogic 漏洞",
                        "killchains": "Actions On Objective",
                        "threatScore": 61,
                        "threatFlag": "Malicious Host"
                    },
                    "alert": {
                        "severity": 3,
                        "signature_id": 2011540,
                        "rev": 6,
                        "metadata": {
                            "updated_at": [
                                "2017_11_27"
                            ],
                            "former_category": [
                                "POLICY"
                            ],
                            "created_at": [
                                "2010_09_27"
                            ]
                        },
                        "gid": 1,
                        "signature": "ET POLICY OpenSSL Demo CA - Internet Widgits Pty (O)",
                        "action": "allowed",
                        "category": "Not Suspicious Traffic"
                    },
                    "payload": "FgMBAFkCGM19hHA/oEA4AAAA=",
                    "flow_id": 1613069999816203,
                    "dest_ip": "************",
                    "flow": {
                        "pkts_toserver": 4,
                        "start": "2019-01-12T07:01:42.409099+0800",
                        "bytes_toclient": 1539,
                        "bytes_toserver": 457,
                        "pkts_toclient": 4
                    },
                    "dest_port": 49212
                }, {},{}
            ]
        },{},{}
    ]
}
  ```

* 根据攻击者地址聚合：查看 top n 攻击者的 IP 地址，包括：IP 地址、触发次数、对应的每一条攻击日志（时间、五元组、使用什么漏洞、payload、命中规则、killchains）；
- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 聚合结果攻击者地址不可为空 |  |
  | 5.22-6.10 | 输出结果按API文档格式化 |  |

  **请求参数：**

| 参数名    | 必须  | 类型   | 说明     |
| :-------- | :---- | :----- | :------- |
| keyword   | True  | String | 关键字   |
| topNum    | True  | String | 排行     |
| startTime | False | String | 开始时间 |
| stopTime  | False | String | 结束时间 |
| page      | False | String | 页数     |
| pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/group GET （默认按照 24 小时聚合）
  ?keyword=attackIp&topNum=20&startTime=1557475221&stopTime=1557907236&page=&pagesize=
  
  keyword: 取值 attackIp（按照攻击IP）
  startTime: 查询的session起始时间，可为空，unix时间戳格式
  stopTime: 查询的session结束时间，可为空，unix时间戳格式
  
  response:
  {
    "message": "Get restult By attackIp Success",
    "data": [{
    "attackIP": "*******",
    "triggerCount": 20,
    "alertRecords": [{
      "src_ip": "**********",
      "src_port": 49213,
      "@timestamp": "2019-05-15T08:54:26.223Z",
      "killchains": "beacon",
      "attackIp": "************",
      "victimIp": "************",
        "vulInfo": {
      "vulName": "weblogic RCE 漏洞",
      "CVE":"cve-2019-0117"
      },
      "payload": "xxxxxx=",
      "alert": {
        "severity": 1,
        "signature_id": 2020410,
        "rev": 4,
        "metadata": {
          "updated_at": [
            "2015_02_11"
          ],
          "created_at": [
            "2015_02_11"
          ]
        },
        "gid": 1,
        "signature": "ET TROJAN HawkEye Keylogger FTP",
        "action": "allowed",
        "category": "A Network Trojan was detected"
      },
      "flow_id": 598934946687717,
      "dest_ip": "*************",
      "flow": {
        "pkts_toserver": 19,
        "start": "2019-05-02T22:01:33.174821+0000",
        "bytes_toclient": 2262,
        "bytes_toserver": 1244,
        "pkts_toclient": 28
      },
      "dest_port": 21
    }, {}, {}]
  }]
  }
  ```

* 根据被攻击地址聚合：查看 top n 被攻击的 IP 地址，包括：IP 地址、触发次数、对应的每一条攻击日志（时间、五元组、使用什么漏洞、payload、命中规则、killchains）；
- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 聚合结果被攻击者地址不可为空 |  |
  | 5.22-6.10 | 输出结果按API文档格式化 | |

  **请求参数：**

| 参数名    | 必须  | 类型   | 说明     |
| :-------- | :---- | :----- | :------- |
| keyword   | True  | String | 关键字   |
| topNum    | False | String | 排行     |
| startTime | False | String | 开始时间 |
| stopTime  | False | String | 结束时间 |
| page      | False | String | 页数     |
| pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/group GET （默认按照 24 小时聚合）
  ?keyword=victimIp&topNum=20&startTime=1557475221&stopTime=1557907236&page=&pagesize=
  
  #keyword: 取值 victimIp（按照攻击IP）
  #startTime: 查询的session起始时间，可为空，unix时间戳格式
  #stopTime: 查询的session结束时间，可为空，unix时间戳格式
  
  response:
  {
    "message": "",
    "data": [
        {
            "attackIp": "***********",
            "triggerCount": 53360,
            "alertRecords": [
                {
                    "src_ip": "*********",
                    "src_port": 445,
                    "rulesInfo": {
                        "attackIp": "***********",
                        "cve": "cve-2019-035",
                        "victimIp": "*********",
                        "vulName": "DEDECMS 漏洞",
                        "killchains": "Actions On Objective",
                        "threatScore": 38,
                        "threatFlag": "Suspicious"
                    },
                    "alert": {
                        "severity": 1,
                        "signature_id": 2025650,
                        "rev": 2,
                        "metadata": {
                            "affected_product": [
                                "Windows_XP_Vista_7_8_10_Server_32_64_Bit"
                            ],
                            "attack_target": [
                                "Client_Endpoint"
                            ],
                            "updated_at": [
                                "2018_08_15"
                            ],
                            "former_category": [
                                "EXPLOIT"
                            ],
                            "created_at": [
                                "2018_07_11"
                            ],
                            "tag": [
                                "ETERNALBLUE",
                                "Metasploit"
                            ],
                            "signature_severity": [
                                "Major"
                            ],
                            "deployment": [
                                "Internal"
                            ]
                        },
                        "gid": 1,
                        "signature": "ET EXPLOIT ETERNALBLUE Probe Vulnerable System Response MS17-010",
                        "action": "allowed",
                        "category": "A Network Trojan was detected"
                    },
                    "payload": "AAAAI/9TTUIlBQIAwJgBSAAAAI0AoACAAAAAAA==",
                    "flow_id": 1408563461811997,
                    "dest_ip": "***********",
                    "flow": {
                        "pkts_toserver": 6,
                        "start": "2019-04-27T10:22:00.978717+0800",
                        "bytes_toclient": 726,
                        "bytes_toserver": 612,
                        "pkts_toclient": 5
                    },
                    "dest_port": 49214
                }, {}, {}
            ]
        }, {}, {}
    ]
}
  ```

### 0x0102 漏洞攻击调查（高优先级）

* 概述：用户可以根据漏洞的名称作为关键字、或者漏洞的CVE编号作为关键字，查看流量中是否存在该漏洞的攻击行为，以及该漏洞攻击的实际影响范围；

* 根据漏洞名称聚合调查：什么时间、该漏洞、从哪里发起攻击、攻击哪个目的地址、使用的攻击payload、攻击是否成功、处于killchains的哪个阶段、命中了哪条监测规则；（vulname=xxxx&topNum=20&startTime=1557475221&stopTime=1557907236）

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 参数校验不完整，边界问题 |  |
  | 5.22-6.10 | 输出结果格式化 |  |
  | 5.22-6.10 | 输出结果添加是否成功字段 |  |

  **请求参数：**

| 参数名    | 必须  | 类型   | 说明       |
| :-------- | :---- | :----- | :--------- |
| vulName   | True  | String | 漏洞名称   |
| count     | False | String | 数量       |
| startTime | False | String | 开始时间   |
| stopTime  | False | String | 结束时间   |
| ip        | False | String | IP 地址    |
| killchains | False | String | 杀伤链阶段 |
| cve       | False | String | 漏洞编号   |
| victimIp  | False | String | 被攻击者IP |
| attackIp  | False | String | 攻击者IP   |
| page      | False | String | 页数     |
| pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/vul GET(默认 10 条)时间跨度默认 24 小时
  startTime=1557475221&stopTime=1557907236&count=20&vulName=weblogic&ip=************&killchains=&cve=cve-2019-0117&victimIp=************&attackIp=&page=&pagesize=
  
  response:
  {
    "message": "",
    "data": [
        {
            "victimIp": "***********",
            "triggerCount": 53360,
            "alertRecords": [
                {
                    "src_ip": "*********",
                    "src_port": 445,
                    "rulesInfo": {
                        "attackIp": "***********",
                        "cve": "cve-2019-035",
                        "victimIp": "*********",
                        "vulName": "DEDECMS 漏洞",
                        "killchains": "Actions On Objective",
                        "threatScore": 38,
                        "threatFlag": "Suspicious"
                    },
                    "alert": {
                        "severity": 1,
                        "signature_id": 2025650,
                        "rev": 2,
                        "metadata": {
                            "affected_product": [
                                "Windows_XP_Vista_7_8_10_Server_32_64_Bit"
                            ],
                            "attack_target": [
                                "Client_Endpoint"
                            ],
                            "updated_at": [
                                "2018_08_15"
                            ],
                            "former_category": [
                                "EXPLOIT"
                            ],
                            "created_at": [
                                "2018_07_11"
                            ],
                            "tag": [
                                "ETERNALBLUE",
                                "Metasploit"
                            ],
                            "signature_severity": [
                                "Major"
                            ],
                            "deployment": [
                                "Internal"
                            ]
                        },
                        "gid": 1,
                        "signature": "ET EXPLOIT ETERNALBLUE Probe Vulnerable System Response MS17-010",
                        "action": "allowed",
                        "category": "A Network Trojan was detected"
                    },
                    "payload": "AAAAI/9TTUIlBQIAwJgBSAAAAI0AoACAAAAAAA==",
                    "flow_id": 1408563461811997,
                    "dest_ip": "***********",
                    "flow": {
                        "pkts_toserver": 6,
                        "start": "2019-04-27T10:22:00.978717+0800",
                        "bytes_toclient": 726,
                        "bytes_toserver": 612,
                        "pkts_toclient": 5
                    },
                    "dest_port": 49214
                }, {}, {}
            ]
        }, {}, {}
    ]
}
  ```

* 根据CVE编号聚合调查：什么时间、该CVE漏洞、从哪里发起攻击、攻击哪个目的地址、使用的攻击payload、攻击是否成功、处于killchains的哪个阶段、命中了哪条监测规则；（cve=cve-xxxx-xxxx&topNum=20&startTime=1557475221&stopTime=1557907236）
- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 参数校验不完整 |  |
  | 5.22-6.10 | 输出结果格式化 |  |
  | 5.22-6.10 | 输出结果添加是否成功字段 |  |


  **请求参数：**

| 参数名    | 必须  | 类型   | 说明       |
| :-------- | :---- | :----- | :--------- |
| cve       | True  | String | 漏洞编号   |
| count     | False | String | 数量       |
| startTime | False | String | 开始时间   |
| stopTime  | False | String | 结束时间   |
| ip        | False | String | IP 地址    |
| killchains | False | String | 杀伤链阶段 |
| vulName   | False | String | 漏洞名称   |
| victimIp  | False | String | 被攻击者IP |
| attackIp  | False | String | 攻击者IP   |
| page      | False | String | 页数     |
| pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/vul GET(默认 10 条)时间跨度默认 12 小时
  startTime=1557475221&stopTime=1557907236&count=20&vulName=weblogic&ip=************&killchains=&cve=cve-2019-0117&victimIp=************&attackIp=&page=&pagesize=

  response:
  {
    "message": "",
    "data": [{
      "src_ip": "************",
      "src_port": 49213,
      "@timestamp": "2019-05-15T08:54:26.223Z",
      "killchains": "beacon",
      "attackIp": "************",
      "victimIp": "************",
      "vulInfo": {
      "vulName": "weblogic RCE 漏洞",
      "CVE":"cve-2019-0117"
      },
      "payload": "xxxxxx=",
      "alert": {
        "severity": 1,
        "signature_id": 2020410,
        "rev": 4,
        "metadata": {
          "updated_at": [
            "2015_02_11"
          ],
          "created_at": [
            "2015_02_11"
          ]
        },
        "gid": 1,
        "signature": "ET TROJAN HawkEye Keylogger FTP",
        "action": "allowed",
        "category": "A Network Trojan was detected"
      },
      "flow_id": 598934946687717,
      "dest_ip": "*************",
      "flow": {
        "pkts_toserver": 19,
        "start": "2019-05-02T22:01:33.174821+0000",
        "bytes_toclient": 2262,
        "bytes_toserver": 1244,
        "pkts_toclient": 28
      },
      "dest_port": 21
    }, {}, {}]
  }
  ```

* 根据被攻击地址聚合调查：<mark>某段时间范围</mark>、被调查的地址、<mark>被哪里攻击</mark>、遭遇什么漏洞攻击、使用的攻击payload、攻击是否成功、<mark>处于killchains的哪个阶段</mark>、命中了哪条监测规则；通过时间维度+攻击来源+killchains阶段，能够拼凑出时间线索关系，则认为有可能构造出一个完整的攻击事件；

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 参数校验不完整 |  |
  | 5.22-6.10 | 输出结果格式化 |  |
  | 5.22-6.10 | 输出结果添加是否成功字段 |  |
  | 5.22-6.10 | 是否根据IP地址段聚合 | 查看可行性、考虑放到调查模块(异步) |

  **请求参数：**

| 参数名    | 必须  | 类型   | 说明       |
| :-------- | :---- | :----- | :--------- |
| vulName   | False | String | 漏洞名称   |
| count     | False | String | 数量       |
| startTime | False | String | 开始时间   |
| stopTime  | False | String | 结束时间   |
| ip        | False | String | IP 地址    |
| killchains | False | String | 杀伤链阶段 |
| cve       | False | String | 漏洞编号   |
| victimIp  | True  | String | 被攻击者IP |
| attackIp  | False | String | 攻击者IP   |
| page      | False | String | 页数     |
| pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/vul GET(默认 10 条)时间跨度默认 12 小时
  startTime=1557475221&stopTime=1557907236&count=20&vulName=weblogic&ip=************&killchains=&cve=cve-2019-0117&victimIp=************&attackIp=&page=&pagesize=
  
  response:
  {
    "message": "",
    "data": [{
      "src_ip": "************",
      "src_port": 49213,
      "@timestamp": "2019-05-15T08:54:26.223Z",
      "killchains": "beacon",
      "attackIp": "************",
      "victimIp": "************",
      "vulInfo": {
      "vulName": "weblogic RCE 漏洞",
      "CVE":"cve-2019-0117"
      },
      "payload": "xxxxxx=",
      "alert": {
        "severity": 1,
        "signature_id": 2020410,
        "rev": 4,
        "metadata": {
          "updated_at": [
            "2015_02_11"
          ],
          "created_at": [
            "2015_02_11"
          ]
        },
        "gid": 1,
        "signature": "ET TROJAN HawkEye Keylogger FTP",
        "action": "allowed",
        "category": "A Network Trojan was detected"
      },
      "flow_id": 598934946687717,
      "dest_ip": "*************",
      "flow": {
        "pkts_toserver": 19,
        "start": "2019-05-02T22:01:33.174821+0000",
        "bytes_toclient": 2262,
        "bytes_toserver": 1244,
        "pkts_toclient": 28
      },
      "dest_port": 21
    }, {}, {}]
  }
  ```

* 根据攻击者地址聚合调查：<mark>某段时间范围</mark>、被调查的地址、发起了什么漏洞攻击、使用的攻击payload、攻击是否成功、<mark>处于killchains的哪个阶段</mark>、命中了哪条监测规则；通过时间维度+攻击目的+killchains阶段，能够拼凑出时间线索关系，则认为有可能构造出一个完整的攻击事件；同时，结合「0x0601 攻击者（被攻击者）流量回溯」实现 PCAP 数据包导出，方便开发更详细的安全专家分析；

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 参数校验不完整 |  |
  | 5.22-6.10 | 输出结果格式化 |  |
  | 5.22-6.10 | 输出结果添加是否成功字段 |  |
  | 5.22-6.10 | 是否根据IP地址段聚合 | 查看可行性、考虑放到调查模块(异步) |
  | 5.22-6.10 | 将孤立的攻击通过时间维度和killchains结合形成安全事件 |killchains阶段展示数量的同时展示涉及的时间范围|

  **请求参数：**

| 参数名    | 必须  | 类型   | 说明       |
| :-------- | :---- | :----- | :--------- |
| vulName   | True  | String | 漏洞名称   |
| count     | False | String | 数量       |
| startTime | False | String | 开始时间   |
| stopTime  | False | String | 结束时间   |
| ip        | False | String | IP 地址    |
| killchains | False | String | 杀伤链阶段 |
| cve       | False | String | 漏洞编号   |
| victimIp  | False | String | 被攻击者IP |
| attackIp  | True  | String | 攻击者IP   |
| page      | False | String | 页数     |
| pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/vul GET(默认 10 条)时间跨度默认 12 小时
  startTime=1557475221&stopTime=1557907236&count=20&vulName=weblogic&ip=************&killchains=&cve=cve-2019-0117&victimIp=&attackIp=************&page=&pagesize=

  response:
  {
    "message": "",
    "data": [{
      "src_ip": "************",
      "src_port": 49213,
      "@timestamp": "2019-05-15T08:54:26.223Z",
      "killchains": "beacon",
      "attackIp": "************",
      "victimIp": "************",
      "vulInfo": {
        "vulName": "weblogic RCE 漏洞",
        "CVE": "cve-2019-0117"
      },
      "payload": "xxxxxx=",
      "alert": {
        "severity": 1,
        "signature_id": 2020410,
        "rev": 4,
        "metadata": {
          "updated_at": [
            "2015_02_11"
          ],
          "created_at": [
            "2015_02_11"
          ]
        },
        "gid": 1,
        "signature": "ET TROJAN HawkEye Keylogger FTP",
        "action": "allowed",
        "category": "A Network Trojan was detected"
    },
      "flow_id": 598934946687717,
      "dest_ip": "*************",
      "flow": {
        "pkts_toserver": 19,
        "start": "2019-05-02T22:01:33.174821+0000",
        "bytes_toclient": 2262,
        "bytes_toserver": 1244,
        "pkts_toclient": 28
      },
      "dest_port": 21
    }, {}, {}]
  }
  ```

* 根据IP地址聚合killchains：<mark>某段时间范围</mark>、攻击地址、被调查的地址<mark>处于killchains的哪些阶段</mark>、命中了哪条监测规则；通过时间维度+攻击目的+killchains阶段，能够拼凑出时间线索关系，则认为有可能构造出一个完整的攻击事件；同时，结合「0x0601 攻击者（被攻击者）流量回溯」实现 PCAP 数据包导出，方便开发更详细的安全专家分析；

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 参数校验不完整 |  |
  | 5.22-6.10 | 输出结果格式化 |  |
  | 5.22-6.10 | 是否根据IP地址段聚合 | |

  **请求参数：**

| 参数名    | 必须  | 类型   | 说明       |
| :-------- | :---- | :----- | :--------- |
| startTime | False | String | 开始时间   |
| stopTime  | False | String | 结束时间   |
| ip        | False | String | IP 地址    |
| victimIp  | False | String | 被攻击者IP |
| attackIp  | True  | String | 攻击者IP   |
| page      | False | String | 页数     |
| pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/killchains GET(默认 10 条)时间跨度默认 12 小时
  startTime=1557475221&stopTime=1557907236&ip=************&victimIp=&attackIp=************&page=&pagesize=

  response:
  {
    "message": "",
    "data": {
        [
                    {
                        "key": "[***********]-[************]",
                        "doc_count": 2121,
                        "killchains_count": {
                            "value": 5
                        },
                        "killchains": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {
                                    "key": "Beacon",
                                    "doc_count": 606
                                },
                                {
                                    "key": "Recon",
                                    "doc_count": 606
                                },
                                {
                                    "key": "Actions On Objective",
                                    "doc_count": 303
                                },
                                {
                                    "key": "Delivery",
                                    "doc_count": 303
                                },
                                {
                                    "key": "Lateral Movement",
                                    "doc_count": 303
                                }
                            ]
                        }
                    }
                ]
            }
        }
    }
}
  ```

### 0x0103 killchains攻击调查（威胁知识迭代）（高优先级）

* 概述：专注围绕某一个杀伤链环节，通过一个环节做上下游行为拓展的方式，来尝试做整个攻击事件调查的模块

* 根据killchains的阶段名称，例如：deliver和exploit，查询处于这个环节的所有安全事件，包含：攻击者 IP 地址、被攻击 IP 地址、使用的漏洞、是否利用成功；

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 参数校验不完整 |  |
  | 5.22-6.10 | 输出结果格式化 |  |


  **请求参数：**

| 参数名    | 必须  | 类型   | 说明       |
| :-------- | :---- | :----- | :--------- |
| vulName   | False | String | 漏洞名称   |
| count     | False | String | 数量       |
| startTime | False | String | 开始时间   |
| stopTime  | False | String | 结束时间   |
| ip        | False | String | IP 地址    |
| killchains | True  | String | 杀伤链阶段 |
| cve       | False | String | 漏洞编号   |
| victimIp  | False | String | 被攻击者IP |
| attackIp  | False | String | 攻击者IP   |
| page      | False | String | 页数     |
| pageSize  | False | String | 页大小   |

  ```bash
  /api/v1/event/vul GET(默认 10 条)时间跨度默认 12 小时
  startTime=1557475221&stopTime=1557907236&count=20&vulName=weblogic&ip=************&killchains=deliver,exploit&cve=cve-2019-0117&victimIp=&attackIp=&page=&pagesize=
  
  response:
  {
    "message": "",
    "data": [{
      "src_ip": "************",
      "src_port": 49213,
      "@timestamp": "2019-05-15T08:54:26.223Z",
      "killchains": "beacon",
      "attackIp": "************",
      "victimIp": "************",
      "vulInfo": {
      "vulName": "weblogic RCE 漏洞",
      "CVE":"cve-2019-0117"
      },
      "payload": "xxxxxx=",
      "alert": {
        "severity": 1,
        "signature_id": 2020410,
        "rev": 4,
        "metadata": {
          "updated_at": [
            "2015_02_11"
          ],
          "created_at": [
            "2015_02_11"
          ]
        },
        "gid": 1,
        "signature": "ET TROJAN HawkEye Keylogger FTP",
        "action": "allowed",
        "category": "A Network Trojan was detected"
      },
      "flow_id": 598934946687717,
      "dest_ip": "*************",
      "flow": {
        "pkts_toserver": 19,
        "start": "2019-05-02T22:01:33.174821+0000",
        "bytes_toclient": 2262,
        "bytes_toserver": 1244,
        "pkts_toclient": 28
      },
      "dest_port": 21
    }, {}, {}]
  }
  ```

* 根据某 IP 确定其 killchains 详情

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--------: |
  | 5.22-6.10 | 参数校验不完整 |  |
  | 5.22-6.10 | 输出结果格式化 |  |

  **请求参数：**

| 参数名   | 必须  | 类型   | 说明       |
| :------- | :---- | :----- | :--------- |
| attackIp | False | string | 攻击者IP   |
| victimIp | False | string | 被攻击者IP |
| interval | False | int    | 间隔时间   |
| count    | False | int    | 每个killchains展示的日志数量|
| page     | False | Int    | 页数       |
| pagesize | False | Int    | 页大小     |

  ```bash
  /api/v1/killchains?attackIp=*******&victimIp=&interval=7&page=1&pagesize=1&count=1 GET(默认按照过去七天碰撞)
  
  # count可为0，此时不展示日志数量，只展示两个IP涉及的killchains和数量

  response:
  {
    "message": "et killchains success",
    "data": {
      "count": 50,
      "detail": [{
        "killchains": ["Recon", "Weaponization", "Delivery", "Exploitation"],
        "attackIp": "*******",
        "victimIp": "xx.xx.xx.xx",
        "alertRecords": {
          "Recon": [{}],
          "Weaponization": [{}, {}],
          "Delivery": [{}, {}],
          "Exploitation": [{}, {}]
        }
      }, {
        "killchains": ["Weaponization", "Delivery", "Exploitation"],
        "attackIp": "*******",
        "victimIp": "xx.xx.xx.xx",
        "alertRecords": {
          "Weaponization": [{}, {}],
          "Delivery": [{}, {}],
          "Exploitation": [{}, {}]
        }
      }, {}, {}]
    }
  }
  ```

### 0x0104 漏洞攻击探索（高优先级）

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--: |
  | 5.22-6.10 | 5.22-6.10  Sprint 未联调 |  |

* 概述：围绕新近披露的1Day漏洞，尝试针对历史流量进行该漏洞的回溯规则匹配和行为挖掘，澄清一个目标：在过往的历史流量中，是否已经遭受过该漏洞的攻击；

  * 新增漏洞攻击特征「规则」更新：
    * 「参考」监测规则添加：管理员单条手工添加监测规则；
    * 「参考」监测规则批量添加：管理员批量导入文件形式的监测规则列表；<mark>（导入文件为固定的数据结构格式）；
  * 选取漏洞攻击的规则SID；
  * 根据时间跨度选取计划回放的流量包：选取条件为时间跨度范围；

* 触发开始执行漏洞攻击探索：下发一个攻击探索任务；
  **请求参数：**

  | 参数名    | 必须 | 类型   | 说明     |
  | :-------- | :--- | :----- | :------- |
  | name      | True | String | 任务名   |
  | sidList   | True | List   | SID 列表 |
  | startDate | True | String | 开始时间 |
  | endDate   | True | String | 结束时间 |

  ```bash
  /api/v1/explore POST
  {
    "name": "task1"
    "sidList": ["2009201", "2009202", "2009203"],
    "startDate": "2019/05/10",
    "endDate": "2019/05/15"
  }

  response:
  {
      "message": "success",
      "data": {
          "celeryId": "a6b9297d-bb39-4f91-8476-aaf4769843f8",
          "taskId": "e84703f23cfaeddc17fa63d6330548cc"
    }
  }
  ```
  
* 查看当前所有回放任务列表

  **请求参数：**

  | 参数名   | 必须 | 类型 | 说明   |
  | :------- | :--- | :--- | :----- |
  | page     | True | Int  | 页数   |
  | pagesize | True | Int  | 页大小 |

  ```bash
  /api/v1/explore?page=1&pagesize=1 GET
  
  response:
  {
      "message": "Success",
      "data": {
          "count": 116,
          "detail": [
              {
                  "taskName": "task1",
                  "sidList": [
                      "2009201",
                      "2009202",
                      "2009203"
                  ],
                  "taskId": "5e88f7c3-8dee-4554-a574-0a2bd29becc6",
                  "status": "",
                  "process": ""
              },
              {
                  "taskName": "task1",
                  "sidList": [
                      "2009201",
                      "2009202",
                      "2009203"
                  ],
                  "taskId": "bed000b4-f6bb-4009-989a-48bb6c458014",
                  "status": "",
                  "process": ""
              },
              ...
              {},{}
          ],
          "page": 1,
          "pagesize": 2
      }
  }
  ```

* 获取单条回放任务信息

  ```bash
  /api/v1/explore/557fc8432253bf0f1f4f6cf0 GET
  
  response:
  {
      "message": "success",
      "data": {
          "taskName": "task139",
          "taskId": "7bab38782ad7d97e93f15c22982c9949",
          "createAt": "2019-06-18 10:23:26",
          "exploreStartDate": "2019-05-15",
          "exploreEndDate": "2019-08-15",
          "sidList": [
              "30000001"
          ],
          "startTime": "2019-06-18 14:14:18",
          "endTime": "2019-06-18 14:15:39",
          "status": "finish",
          "process": 1
      }
  }
  ```

* 回放任务删除

  ```bash
  /api/v1/explore/4114002e586f931d47fa306a6026432f DELETE
  
  response:
  # 删除成功
  {
      "message": "success",
      "data": {}
  }
  
  # 任务不存在
  {
      "message": "failed",
      "data": "Task does not exist."
  }
  
  # 删除正在执行的任务
  {
      "message": "failed",
      "data": "Status is running,can not delete."
  }
  
  # 删除暂停任务
  {
      "message": "failed",
      "data": "Status is pause,can not delete."
  }
  ```

* 回放任务更新

  **请求参数**

  | 参数名           | 必须 | 类型   | 说明                           |
  | ---------------- | ---- | ------ | ------------------------------ |
  | action           | True | string | 取值为 update                  |
  | sidList          | True | string | 若不修改，需要重传原来的参数值 |
  | exploreStartDate | True | string | 若不修改，需要重传原来的参数值 |
  | exploreEndDate   | True | string | 若不修改，需要重传原来的参数值 |

  ```bash
  /api/v1/explore/557fc8432253bf0f1f4f6cf0 PUT
  {
  	"action":"update",
  	"sidList": ["30000001"],
      "exploreStartDate": "2019-05-15",
      "exploreEndDate": "2019-08-15"
  }
  
  response:
  # 更新成功
  {
      "message": "success",
      "data": {}
  }
  
  # 更新正在执行的任务
  {
      "message": "failed",
      "data": "Status is running,can not update."
  }
  
  # 更新暂停任务
  {
      "message": "failed",
      "data": "Status is pause,can not update."
  }
  ```

* 回放任务暂停

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  | action   | True | String  |  取值为 pause  |

  ```bash
  /api/v1/explore/557fc8432253bf0f1f4f6cf0 PUT
  {
  	"action":"pause"
  }
  
  response:
  # 暂停成功
  {
      "message": "success",
      "data": {}
  }
  
  # 暂停已完成的任务
  {
      "message": "failed",
      "data": "Status is finish, can not pause"
  }
  
  # 暂停已停止的任务
  {
      "message": "failed",
      "data": "Status is stop, can not pause"
  }
  
  # 暂停已暂停的任务
  {
      "message": "failed",
      "data": "Status is pause, can not pause"
  }
  ```

* 回放任务恢复

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  | action   | True | String  |  操作类型  |

  ```bash
  /api/v1/explore/557fc8432253bf0f1f4f6cf0 PUT
  {
  	"action":"resume"
  }
  
  response:
  # 恢复成功
  {
      "message": "success",
      "data": {}
  }
  
  # 恢复已完成的任务
  {
      "message": "failed",
      "data": "Status is finish, can not resume"
  }
  
  # 恢复已停止的任务
  {
      "message": "failed",
      "data": "Status is stop, can not resume"
  }
  
  # 恢复正运行的任务
  {
      "message": "failed",
      "data": "Status is running, can not resume"
  }
  ```

* 回放任务重新执行

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  | action   | True | String  |  取值为 restart  |

  ```bash
  /api/v1/explore/557fc8432253bf0f1f4f6cf0  PUT
  {
  	"action":"restart"
  }
  
  response:
  # 重新执行成功
  {
      "message": "success",
      "data": {}
  }
  
  # 重新执行已暂停的任务
  {
      "message": "failed",
      "data": "Status is pause, can not restart"
  }
  
  # 重新正运行的任务
  {
      "message": "failed",
      "data": "Status is running, can not restart"
  }
  ```

* 探索结果呈现：什么时间点（不超过选取的时间范围）、哪些漏洞、从哪里发起攻击、攻击哪个目的地址、使用的攻击payload、攻击是否成功、处于killchains的哪个阶段、命中了哪条监测规则；

  **请求参数：**

  | 参数名    | 必须  | 类型   | 说明       |
  | :-------- | :---- | :----- | :--------- |
  | vulName   | False | String | 漏洞名称   |
  | count     | False | String | 数量       |
  | startTime | False | String | 开始时间   |
  | stopTime  | False | String | 结束时间   |
  | ip        | False | String | IP 地址    |
  | killchains | True  | String | 杀伤链阶段 |
  | cve       | False | String | 漏洞编号   |
  | victimIp  | False | String | 被攻击者IP |
  | attackIp  | False | String | 攻击者IP   |
  | page      | False | String | 页数     |
  | pagesize  | False | String | 页大小   |

  ```bash
  /api/v1/event/replay GET(默认 10 条)时间跨度默认 12 小时
  startTime=1557475221&stopTime=1557907236&count=20&vulName=weblogic&ip=************&killchains=beacon&cve=cve-2019-0117&victimIp=&attackIp=&page=1&pagesize=1
  
  response:
  {
    "message": "",
    "data": [{
      "src_ip": "************",
      "src_port": 49213,
      "@timestamp": "2019-05-15T08:54:26.223Z",
      "killchains": "beacon",
      "attackIp": "************",
      "victimIp": "************",
      "vulInfo": {
      "vulName": "weblogic RCE 漏洞",
      "CVE":"cve-2019-0117"
      },
      "payload": "xxxxxx=",
      "alert": {
        "severity": 1,
        "signature_id": 2020410,
        "rev": 4,
        "metadata": {
          "updated_at": [
            "2015_02_11"
          ],
          "created_at": [
            "2015_02_11"
          ]
        },
        "gid": 1,
        "signature": "ET TROJAN HawkEye Keylogger FTP",
        "action": "allowed",
        "category": "A Network Trojan was detected"
      },
      "flow_id": 598934946687717,
      "dest_ip": "*************",
      "flow": {
        "pkts_toserver": 19,
        "start": "2019-05-02T22:01:33.174821+0000",
        "bytes_toclient": 2262,
        "bytes_toserver": 1244,
        "pkts_toclient": 28
      },
      "dest_port": 21
    }, {}, {}]
  }
  ```

* 探索结果标记：可以选取部分或者全部采信的攻击结果，将其批量标记或导入至「漏洞攻击调查」的 ES 库；

### 0x0105 规则管理


* 概述：确定规则的数据结构，对当前规则和未来生产规则进行人工分类，依赖 killchains ，入库，上线规则的增删改查，允许或者禁止某些规则生效；

* 监测规则添加：管理员单条手工添加监测规则；「DB」

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--: |
  | 5.22-6.10 | 规则状态使用字符串表示 |  |

  **请求参数：**

  | 参数名    | 必须  | 类型   | 说明       |
  | :-------- | :---- | :----- | :--------- |
  | lockheedKillchainEN | True  | String | 杀伤链阶段，可取值 ["Recon", "Weaponization", "Delivery", "Exploitation", "Beacon", "CnC", "Actions on Objective"] } |
  | ruleContent | True  | String | 规则字段详情 |
  | threatFlag | True  | String | 威胁分类，可取值 ["Exploits and Attacks", "DoS", "Malware", "Scanning", "Botnet", "Phishing", "Suspicious",    "Malicious host", "APT" ] |
  | threatScore | True  | int | 威胁打分，取值范围 1-100 |
  | vulName   | False | String | 漏洞名称   |
  | cve       | False | String | CVE编号   |
  | sid       | True | String | 规则id，可取值 |
  | attackIp       | True | String | 攻击者标记，可取值 ["src_ip", "dst_ip"] |
  | victimIp       | True | String | 受害者标记，可取值 ["src_ip", "dst_ip"] |
  | appProto       | True | String | 协议，可取值范围 ["tcp", "http", "ftp", "ssh", "tls", "smb", "dns", "udp", "icmp", "smtp"] |
| lockheedKillchainCN | False | String | 杀伤链阶段，可取值["侦查跟踪", "武器构建", "载荷投递", "漏洞利用", "安装植入", "命令控制", "目标达成"] |
  | vulType | False | String | 漏洞类型 |
  | lockheedKillchainStage | False | int | 漏洞阶段，可取值 1-7 |
  | submitTime | False | string | 规则首次编写时间 |
  | alterInfo | False | string | 漏洞详细信息 |
  | is0day | False | string | 是否是0day漏洞，可取值 0、1 |
  | author | False | string | 规则编写作者 |
  
  
  
  
  
  ```bash
  /api/v1/rules POST
  
  {
    "threatFlag": "dos",
    "threatScore": 80,
  	"author" : "Xiaofu",
  	"vulName" : "Drupalgeddon2 <8.3.9 <8.4.6 <8.5.1 代码执行漏洞",
  	"vulType" : "代码执行",
  	"cve" : "CVE-2018-7600",
	"lockheedKillchainStage" : 3,
  	"lockheedKillchainCN" : "载荷投递",
  	"lockheedKillchainEN" : "Delivery",
  	"threatFlag" : "Exploits and Attacks",
  	"alterInfo" : "",
  	"submitTime" : "2019-06-18",
  	"is0day" : 1,
  	"ruleContent" : "alert http any any -> any any (msg: \"ATTACK [PTsecurity] Drupalgeddon2 <8.3.9 <8.4.6 <8.5.1 RCE through registration form (CVE-2018-7600)\"; flow: established, to_client; flowbits:isset,30000014; content: \"200\";http_stat_code; content:\"|22|command|22 3A 22|insert|22|\"; http_server_body; content:\"|22|data|22|\"; reference: cve, 2018-7600; reference: url, research.checkpoint.com/uncovering-drupalgeddon-2; classtype: attempted-admin; reference: url, github.com/ptresearch/AttackDetection; metadata: Open Ptsecurity.com ruleset; sid: 30000019; rev: 2; )",
  	"attackIp" : "src_ip",
  	"victimIp" : "dst_ip",
  	"sid": "30000019",
  	"appProto": "http"
  }
  response:
  {
    "message": "rule add success",
    "data": {}
  }
  
  ```

* 监测规则 DB 方式更新

  ```bash
  /api/v1/rules/update POST
  
  response:
  {
    "message": "",
    "data": {
      "addSuccessCount": 10,
      "invalidSidList": ["2009201", "2009202"]
    }
  }
  ```

  

* 检测规则回滚

  ```bash
  /api/v1/rules/rollback POST
  
  response:
  {}
  ```
  
* 监测规则禁用：禁用指定SID的监测规则；(传递List则自动支持批量)「DB」

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  | action   | True | String  |  操作  |
  | sid   | True | List  |  规则id  |

  ```bash
  /api/v1/rules/status POST
  {
    "action":"disable",
    "sid":["2009201", "2009202"]
  }
  
  response:
  {
    "message": "rule disable success",
    "data": {}
  }
  ```

* 监测规则删除：删除指定SID的监测规则；（API实现伪删除）「DB」

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  | sidList   | True | List  |  规则id  |

  ```bash
  /api/v1/rules DEL
  {
    "sidList":["2009201", "2009202"]
  }
  
  response:
  {
      "message": "",
      "data": {
          "delSuccessCount": 0,
          "deleteFail": {
              "delNotExistSIDList": [
                  "2009201",
                  "2009202"
              ],
              "delActiveSIDList": [
                  "30000002"
              ]
          }
      }
  }
  ```

* 监测规则修改（更新）：更新指定SID的监测规则（主要更新：规则体、杀伤链标签、威胁评级、威胁分类）；「DB」

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--: |
  | 5.22-6.10 | 启用状态是否可以更新 | 确保数据一致性 |

  **请求参数：**

| 参数名    | 必须  | 类型   | 说明       |
| :-------- | :---- | :----- | :--------- |
| killchains | True  | String | 杀伤链阶段 |
| rule | True  | String | 规则字段详情 |
| threatFlag | True  | String | 威胁分类 |
| threatScore | True  | int | 威胁打分 |
| vulName   | False | String | 漏洞名称   |
| cve       | False | String | CVE编号   |
| sid       | True | String | 规则id   |
| attackIp       | True | String | 攻击者   |
| victimIp       | True | String | 受害者   |
| appProto       | True | String | 协议   |

  ```bash
  /api/v1/rules/sid PUT
  
  {
      "killchains": "beacon",
      "rule": "alert tcp $EXTERNAL_NET any -> $HOME_NET 445 (msg:\"ET TROJAN Conficker.b Shellcode\"; flow:established,to_server; content:\"|e8 ff ff ff ff c2|_|8d|O|10 80|1|c4|Af|81|9MSu|f5|8|ae c6 9d a0|O|85 ea|O|84 c8|O|84 d8|O|c4|O|9c cc|Ise|c4 c4 c4|,|ed c4 c4 c4 94|&<O8|92|\\;|d3|WG|02 c3|,|dc c4 c4 c4 f7 16 96 96|O|08 a2 03 c5 bc ea 95|\\;|b3 c0 96 96 95 92 96|\\;|f3|\\;|24 |i|95 92|QO|8f f8|O|88 cf bc c7 0f f7|2I|d0|w|c7 95 e4|O|d6 c7 17 cb c4 04 cb|{|04 05 04 c3 f6 c6 86|D|fe c4 b1|1|ff 01 b0 c2 82 ff b5 dc b6 1f|O|95 e0 c7 17 cb|s|d0 b6|O|85 d8 c7 07|O|c0|T|c7 07 9a 9d 07 a4|fN|b2 e2|Dh|0c b1 b6 a8 a9 ab aa c4|]|e7 99 1d ac b0 b0 b4 fe eb eb|\"; reference:url,www.honeynet.org/node/388; reference:url,doc.emergingthreats.net/2009201; classtype:trojan-activity; sid:2009201; rev:6; metadata:created_at 2010_07_30, updated_at 2010_07_30;)",
      "threatFlag": "dos",
      "threatScore": 80,
      "vulName": "weblogic RCE 漏洞",
      "cve":"cve-2019-0117",
      "sid": "2009201",
      "attackIp": "srcIp",
      "victimIp": "dstIp",
      "appProto": "tcp"
  }
  
  
  response:
  {
    "message": "rule modify success",
    "data": {}
  }
  ```

* 监测规则汇总查询：当前NDR系统加载的规则总数、规则的启用条目数（和SID列表）、规则的禁用条目数量（和SID列表）；「DB」

  ```bash
  /api/v1/rules/ GET
  
  reponse:
  {
    "message": "Get rules successfully",
    "data": {
      "allRulesCount": 5538,
      "enableRules": {
        "count": 5537,
        "sidList": ["2009201", "2009202"]
      },
      "disableRules": {
        "count": 2,
        "sidList": ["2009201", "2009202"]
      }
    }
  }
  ```

* 监测规则详情查询：跟进SID查询指定的监测规则详细信息；「DB」

  ```bash
  /api/v1/rules/sid  GET
  
  response:
  {
    "message": "Get rules successfully",
    "data": {
      "killchains": "beacon",
      "rule": "alert tcp $EXTERNAL_NET any -> $HOME_NET 445 (msg:\"ET TROJAN Conficker.b Shellcode\"; flow:established,to_server; content:\"|e8 ff ff ff ff c2|_|8d|O|10 80|1|c4|Af|81|9MSu|f5|8|ae c6 9d a0|O|85 ea|O|84 c8|O|84 d8|O|c4|O|9c cc|Ise|c4 c4 c4|,|ed c4 c4 c4 94|&<O8|92|\\;|d3|WG|02 c3|,|dc c4 c4 c4 f7 16 96 96|O|08 a2 03 c5 bc ea 95|\\;|b3 c0 96 96 95 92 96|\\;|f3|\\;|24 |i|95 92|QO|8f f8|O|88 cf bc c7 0f f7|2I|d0|w|c7 95 e4|O|d6 c7 17 cb c4 04 cb|{|04 05 04 c3 f6 c6 86|D|fe c4 b1|1|ff 01 b0 c2 82 ff b5 dc b6 1f|O|95 e0 c7 17 cb|s|d0 b6|O|85 d8 c7 07|O|c0|T|c7 07 9a 9d 07 a4|fN|b2 e2|Dh|0c b1 b6 a8 a9 ab aa c4|]|e7 99 1d ac b0 b0 b4 fe eb eb|\"; reference:url,www.honeynet.org/node/388; reference:url,doc.emergingthreats.net/2009201; classtype:trojan-activity; sid:2009201; rev:6; metadata:created_at 2010_07_30, updated_at 2010_07_30;)",
      "rulesStatus": 1,
      "threatFlag": "dos",
      "threatScore": 80,
      "vulInfo": {
        "vulName": "weblogic RCE 漏洞",
        "CVE": "cve-2019-0117"
      },
      "sid": "2009201",
      "attackIp": "srcIp",
      "victimIp": "dstIp",
      "appProto": "tcp"
  
    }
  }
  
  ```

* 启用监测规则：启用指定SID的监测规则；(传递List则自动支持批量)「DB」

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  | action   | True | String  |  操作  |
  | sid   | True | List  |  规则id  |

  ```bash
  /api/v1/rules/status POST
  {
  "action":"enable",
  "sidList":["2009201", "2009202"] 
  }
  
  
  response:
  {
    "message": "rule enable success",
    "data": {}
  }
  
  ```

* 定时生效规则：指定时间点生效监测规则；「Suricata」

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  |  enableTime  | False | String  |  预生效时间  |
  |  enableFlag  | False | int  |  标识位  |

  ```bash
  /api/v1/rules/activation POST
  {
  	"taskName":"test11",
  	"enableTime":"2019-06-05 13:38:00",
    "enableFlag":0
  }
  
  # "enableFlag":0 定时生效 1 即时生效
  
  response:
  {
    "message": "rule active success",
    "data": {}
  }
  ```

* 定时生效规则所有任务查询

  ```bash
/api/v1/rules/activation GET
  
  response:
  {
      "message": "Get task list success",
    "data": {
          "count": 5,
          "detail": [
              {
                  "activate_time": "2019-06-04 12:33:00",
                  "task_name": "test",
                  "created_at": "2019-06-04 12:29:01",
                  "task_id": "cf1fac8f-88a0-469d-b350-c79a56f764f4",
                  "task_status": "SUCCESS"
              },
              {
                  "activate_time": "2019-06-04 12:34:00",
                  "task_name": "test",
                  "created_at": "2019-06-04 12:29:07",
                  "task_id": "8772c8f6-8924-4bda-9631-96c6ff60a0ed",
                  "task_status": "SUCCESS"
              },
              {
                  "activate_time": "2019-06-04 12:37:00",
                  "task_name": "test",
                  "created_at": "2019-06-04 12:29:12",
                  "task_id": "c7f69e01-cca1-441d-b742-cef9c604859b",
                  "task_status": "SUCCESS"
              },
              {
                  "activate_time": "2019-06-04 12:38:00",
                  "task_name": "test",
                  "created_at": "2019-06-04 12:29:17",
                  "task_id": "79345abf-71db-4e71-86f5-2a8b13f44f28",
                  "task_status": "SUCCESS"
              },
              {
                  "activate_time": "2019-06-06 13:38:00",
                  "task_name": "test11",
                  "created_at": "2019-06-04 14:19:57",
                  "task_id": "abb747dd-b0ae-41d5-939a-abf7b5266ddc",
                  "task_status": "PENDING"
              }
          ]
      }
  }
  ```
  
* 定时生效规则任务单个查询

  ```bash
  /api/v1/rules/activation/cf1fac8f-88a0-469d-b350-c79a56f764f4 GET
  
  response:
  {
      "message": "",
      "data": {
          "activate_time": "2019-06-04 12:33:00",
          "task_name": "test",
          "created_at": "2019-06-04 12:29:01",
          "task_id": "cf1fac8f-88a0-469d-b350-c79a56f764f4",
          "task_status": "SUCCESS"
      }
  }
  
  ```

* 定时生效规则任务修改
  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  |  enableTime  | True | String  |  预生效时间  |
  ```bash
  /api/v1/rules/activation/cf1fac8f-88a0-469d-b350-c79a56f764f4 PUT
  {
  	"enableTime":"2019-06-06 13:38:00"
  }
  
  ```

response:
  {
    "message": "rule active success",
      "data": {
          "active_time": "2019-06-06 13:38:00"
      }
  }
  ```
  

* 定时生效规则任务删除

  ```bash
/api/v1/rules/activation/cf1fac8f-88a0-469d-b350-c79a56f764f4 DELETE
  
  response:
  {
      "message": "task delete success",
    "data": {}
  }
  
  ```

  

* 立即生效规则：立即同步DB中的“启用状态”规则到规则栈；「Suricata」

  **请求参数：**

  | 参数名   | 必须  | 类型 | 说明   |
  | :------- | :---- | :--- | :----- |
  |  enableTime  | False | String  |  预生效时间  |
  |  enableFlag  | False | int  |  标识位  |

  ```bash
  /api/v1/rules/activation POST
  {
  	"taskName":"test11",
  	"enableTime":"2019-06-05 13:38:00",
    "enableFlag":1
  }
  
  # "enable_flag":0 定时生效 1 即时生效
  
  response:
  {
    "message": "rule active success",
    "data": {}
  }
  ```

<br/>





------------------------------------------

## 0x02 NDR系统「BackStream」

### 0x0201 攻击者（被攻击者）流量回溯（高优先级）

* 概述：能够根据攻击者（被攻击者）的 IP 地址，全量提取其 PCAP 数据包流量；
* 根据 IP 地址：源地址or目的地址，提取 PCAP 流量导出；

- 问题记录

  | Sprint | 事件 | 备注 |
  | :----: | :--: | :--: |
  | 5.22-6.10 | pcap download error | 寻找更好解决办法（1. 可下载所以pcap，打包。2. hack 新的下载接口。3. 对比 google stenographer ，根据情况切换） |

  ```bash
  /api/v1/pcap/dowoload GET
  srcIp=&dstIp=&srcPort=&dstPort=&appProto=&flowId=&sessionId=&startTime=&stopTime=

  response:
  {
    "message": "",
    "data": {
      "downUrl": "http://*********:8005/sessions.pcap?fields=suricata&expression=ip.src==*******&&host.dns.all==app-pc&startTime=1234567890&stopTime=1234567890&length=100&start=1"
    }
  }

  srcIp: 源IP，可为空，IP标准格式
  dstIp: 目的IP，可为空，IP标准格式
  srcPort: 源端口，可为空，1-65536的整数
  dstPort: 目的端口，可为空，1-65536的整数
  appProto: 协议，可为空
  flowId: suricata日志会话的唯一标识符，可为空
  sessionId: moloch流量session的唯一标识符，可为空
  startTime: 查询的session起始时间，可为空，unix时间戳格式
  stopTime: 查询的session结束时间，可为空，unix时间戳格式
  ```

### 0x0202 全流量查询

* 概述：根据源IP、目的IP、源端口、目的端口、协议、flowId、起止时间获取数据包；
* 查询条件包括：（srcIp=&dstIp=&srcPort=&dstPort=&appProto=&flowId=&startTime=&stopTime=）；
* 备注：如果根据告警事件中的 FlowID 查询全流量，就可以获取告警事件的会话还原，Suricata的触发规则与 Moloch 的 PCAP 包，通过 FlowID 实现关联；

### 0x0203 统计当前 IP 的通联情况

* 概述：根据 IP、起止时间获取 IP 通联情况；
* 根据不同条件（start=&num=&startTime=&stopTime=&ip=）；

### 0x0204 查询当前流量中的会话概要

* 概述：统计当前协议、当前流量中前十的源IP、目的IP、当前流量域名的会话概要；
* 根据不同条件 spi=&startTime=&stopTime=&num= （spi 取值可选  srcIp, dstIp, protocol, dns.host）；
* 类似Wireshark的Summary功能；

<br/>

------------------------------------------

## 0x03 NDR系统「系统管理」

### 0x0301 系统管理

* 配置网卡

* 关闭和重启系统

  | 参数名  | 必须 | 类型   | 说明                         |
  | ------- | ---- | ------ | ---------------------------- |
  | command | True | string | 取值范围：reboot 或 poweroff |

  

  ```bash
  api/v1/systemConfig/ POST
  {
  		"command": "$(command)" # command 取值为reboot 或 poweroff
  }
  
  response
  # 操作成功
  {
      "message": "success",
      "data": {}
  }
  
  # command参数错误
  {
      "message": {
          "command": "$(command) is not a valid choice"
      }
  }
  
  # 操作失败
  {
      "message": "failed",
      "data": "OS error code: 1" # 对应的系统错误码：1 表示没权限,其它错误码可参考 Linux 命令标准
  }
  
  ```

<br/>

------------------------------------------

## 0x04 讨论归档

- 规则更新(通过 SID 范围判断规则自生产问题)

  - 通过文件批量上传【1】

    ```bash
    /api/v1/rules/ POST
    
    response
    {
     "rules_status": "rule add success",
     "rules_count": xxxx
    }
    文件每条记录格式如单条记录
    ```

  - 通过文本单条上传【2】

    ```bash
    /api/v1/rules/sid POST
    
    {
        "attck_flag": "beacon",
        "rule": "alert tcp $EXTERNAL_NET any -> $HOME_NET 445 (msg:\"ET TROJAN Conficker.b Shellcode\"; flow:established,to_server; content:\"|e8 ff ff ff ff c2|_|8d|O|10 80|1|c4|Af|81|9MSu|f5|8|ae c6 9d a0|O|85 ea|O|84 c8|O|84 d8|O|c4|O|9c cc|Ise|c4 c4 c4|,|ed c4 c4 c4 94|&<O8|92|\\;|d3|WG|02 c3|,|dc c4 c4 c4 f7 16 96 96|O|08 a2 03 c5 bc ea 95|\\;|b3 c0 96 96 95 92 96|\\;|f3|\\;|24 |i|95 92|QO|8f f8|O|88 cf bc c7 0f f7|2I|d0|w|c7 95 e4|O|d6 c7 17 cb c4 04 cb|{|04 05 04 c3 f6 c6 86|D|fe c4 b1|1|ff 01 b0 c2 82 ff b5 dc b6 1f|O|95 e0 c7 17 cb|s|d0 b6|O|85 d8 c7 07|O|c0|T|c7 07 9a 9d 07 a4|fN|b2 e2|Dh|0c b1 b6 a8 a9 ab aa c4|]|e7 99 1d ac b0 b0 b4 fe eb eb|\"; reference:url,www.honeynet.org/node/388; reference:url,doc.emergingthreats.net/2009201; classtype:trojan-activity; sid:2009201; rev:6; metadata:created_at 2010_07_30, updated_at 2010_07_30;)",
        "rules_status":1，
        "threat_flag": "dos",
        "threat_score": 80
    }
    
    
    response
    {
     "rules_status": "rule add success",
     "rules_count": xxxx
    }
    
    attck_flag: att&ck 攻击阶段
    rules_status: 0 代表 disable 1 代表 enable
    rule: rule 详细内容
    sid: 规则唯一标识符
    threat_flag: 威胁分类，可取值 exploits and attacks，dos，malware，scanning，botnet，           phishing，suspicious，malicious host，apt
    threat_score: 规则威胁程度打分
    
    ```

  

- 规则删除【3】

  ```bash
  /api/v1/rules/sid DEL
  
  sid: 规则唯一标识符
  
  response
  {
   "rules_status": "rule delete success",
   "rules_count": 5538
  }
  
  ```

- 规则更改【4】

  ```bash
  /api/v1/rules/sid PUT
  {
      "attck_flag": "beacon",
      "rule": "alert tcp $EXTERNAL_NET any -> $HOME_NET 445 (msg:\"ET TROJAN Conficker.b Shellcode\"; flow:established,to_server; content:\"|e8 ff ff ff ff c2|_|8d|O|10 80|1|c4|Af|81|9MSu|f5|8|ae c6 9d a0|O|85 ea|O|84 c8|O|84 d8|O|c4|O|9c cc|Ise|c4 c4 c4|,|ed c4 c4 c4 94|&<O8|92|\\;|d3|WG|02 c3|,|dc c4 c4 c4 f7 16 96 96|O|08 a2 03 c5 bc ea 95|\\;|b3 c0 96 96 95 92 96|\\;|f3|\\;|24 |i|95 92|QO|8f f8|O|88 cf bc c7 0f f7|2I|d0|w|c7 95 e4|O|d6 c7 17 cb c4 04 cb|{|04 05 04 c3 f6 c6 86|D|fe c4 b1|1|ff 01 b0 c2 82 ff b5 dc b6 1f|O|95 e0 c7 17 cb|s|d0 b6|O|85 d8 c7 07|O|c0|T|c7 07 9a 9d 07 a4|fN|b2 e2|Dh|0c b1 b6 a8 a9 ab aa c4|]|e7 99 1d ac b0 b0 b4 fe eb eb|\"; reference:url,www.honeynet.org/node/388; reference:url,doc.emergingthreats.net/2009201; classtype:trojan-activity; sid:2009201; rev:6; metadata:created_at 2010_07_30, updated_at 2010_07_30;)",
      "rules_status":1,
      "threat_flag": "dos",
      "threat_score": 80,
  }
  ```

  

- 规则查询(存储 mongoDB)规则标签「威胁分类、杀伤链分类（ATT&CK）、威胁打分」【5】

  ```bash
  /api/v1/rules/sid  GET
  
  response:
  {
      "attck_flag": "beacon",
      "rule": "alert tcp $EXTERNAL_NET any -> $HOME_NET 445 (msg:\"ET TROJAN Conficker.b Shellcode\"; flow:established,to_server; content:\"|e8 ff ff ff ff c2|_|8d|O|10 80|1|c4|Af|81|9MSu|f5|8|ae c6 9d a0|O|85 ea|O|84 c8|O|84 d8|O|c4|O|9c cc|Ise|c4 c4 c4|,|ed c4 c4 c4 94|&<O8|92|\\;|d3|WG|02 c3|,|dc c4 c4 c4 f7 16 96 96|O|08 a2 03 c5 bc ea 95|\\;|b3 c0 96 96 95 92 96|\\;|f3|\\;|24 |i|95 92|QO|8f f8|O|88 cf bc c7 0f f7|2I|d0|w|c7 95 e4|O|d6 c7 17 cb c4 04 cb|{|04 05 04 c3 f6 c6 86|D|fe c4 b1|1|ff 01 b0 c2 82 ff b5 dc b6 1f|O|95 e0 c7 17 cb|s|d0 b6|O|85 d8 c7 07|O|c0|T|c7 07 9a 9d 07 a4|fN|b2 e2|Dh|0c b1 b6 a8 a9 ab aa c4|]|e7 99 1d ac b0 b0 b4 fe eb eb|\"; reference:url,www.honeynet.org/node/388; reference:url,doc.emergingthreats.net/2009201; classtype:trojan-activity; sid:2009201; rev:6; metadata:created_at 2010_07_30, updated_at 2010_07_30;)",
      "rules_status": 0,
      "sid": 2009201,
      "threat_flag": "dos",
      "threat_score": 80,
      "update_time": "2019050711"
  }
  
  ```

- 规则生效【6】即时/定时生效

  ```bash
  /api/v1/rules/enable POST
  {
   "enable_time":"2019/5/10/08/20",
   "enable_flag":0   
  }
  
  "enable_flag":0 定时生效 1 即时生效
  
  response:
  {
      "enable_rules_count": 5538,
      "rules_status": "Rules enable success"
  }
  
  ```

  

- 针对某条规则失效【7】

  ```bash
  /api/v1/rules/disable POST
  {
   "sid":xxxx
  }
  
  response:
  {
      "now_enable_rules_count": 5537,
      "old_enable_rules_count": 5538,
      "rules_status": "Rules disable success"
  }
  ```



- 获取当前总规则，生效规则【8】

  ```bash
  /api/v1/rules/ GET
  
  reponse:
  {
      "message": "Get rules successfully",
      "data": {
          "all_rules_count": 5538,
          "enable_rules_count": 5537
      }
  }
  
  
  ```

  

- 安全事件查询

  - 根据不同条件查询安全事件【9】

    ```bash
    /api/v1/event? GET(默认 10 条)时间跨度默认 12 小时
    startTime=1557475221&stopTime=1557907236&count=20&appProto=ftp&dstIp=*************&srcIp&srcPort&dstPort
      
    response:
    
    {
        "message": "",
        "data": {
            "took": 0,
            "timed_out": false,
            "_shards": {
                "total": 5,
                "successful": 5,
                "skipped": 0,
                "failed": 0
            },
            "hits": {
                "total": 3,
              "max_score": null,
                "hits": [
                  {
                        "_index": "rule-eve",
                        "_type": "rule-eve",
                        "_id": "VGyzumoBFlqIbVTyM4kH",
                        "_score": null,
                      "_source": {
                            "src_ip": "**********",
                          "src_port": 49213,
                            "@timestamp": "2019-05-15T08:54:26.223Z",
                            "payload": "VVNFUiBzbmlmZmVyemV0dA0KUEFTUyB0cmlibGUyMg0KT1BUUyB1dGY4IG9uDQpQV0QNCkNXRCAvDQpUWVBFIEkNClBBU1YNClNUT1IgSGF3a0V5ZV9LZXlsb2dnZXJfS2V5bG9nX1JlY29yZHNfM19CUkVBVVgtV0lONy1QQyA1LjIuMjAxOSAxMDowMToyOCBQTS50eHQNClRZUEUgSQ0KUEFTVg0KU1RPUiBzY3JlZW5zaG90Ml9CUkVBVVgtV0lONy1QQy5qcGVnDQo=",
                            "alert": {
                                "severity": 1,
                              "signature_id": 2020410,
                                "rev": 4,
                              "metadata": {
                                    "updated_at": [
                                        "2015_02_11"
                                    ],
                                    "created_at": [
                                        "2015_02_11"
                                    ]
                              },
                                "gid": 1,
                              "signature": "ET TROJAN HawkEye Keylogger FTP",
                                "action": "allowed",
                                "category": "A Network Trojan was detected"
                            },
                            "flow_id": 598934946687717,
                          "dest_ip": "*************",
                            "flow": {
                              "pkts_toserver": 19,
                                "start": "2019-05-02T22:01:33.174821+0000",
                                "bytes_toclient": 2262,
                                "bytes_toserver": 1244,
                                "pkts_toclient": 28
                          },
                            "dest_port": 21
                      },
                        "sort": [
                            1557910466223
                        ]
                    },
                  {
                        "_index": "rule-eve",
                      "_type": "rule-eve",
                        "_id": "ZGtgsGoBFlqIbVTy_u7V",
                      "_score": null,
                        "_source": {
                          "src_ip": "**********",
                            "src_port": 49213,
                          "@timestamp": "2019-05-13T08:48:26.746Z",
                            "payload": "VVNFUiBzbmlmZmVyemV0dA0KUEFTUyB0cmlibGUyMg0KT1BUUyB1dGY4IG9uDQpQV0QNCkNXRCAvDQpUWVBFIEkNClBBU1YNClNUT1IgSGF3a0V5ZV9LZXlsb2dnZXJfS2V5bG9nX1JlY29yZHNfM19CUkVBVVgtV0lONy1QQyA1LjIuMjAxOSAxMDowMToyOCBQTS50eHQNClRZUEUgSQ0KUEFTVg0KU1RPUiBzY3JlZW5zaG90Ml9CUkVBVVgtV0lONy1QQy5qcGVnDQo=",
                          "alert": {
                                "severity": 1,
                                "signature_id": 2020410,
                                "rev": 4,
                                "metadata": {
                                    "updated_at": [
                                        "2015_02_11"
                                    ],
                                  "created_at": [
                                        "2015_02_11"
                                  ]
                                },
                              "gid": 1,
                                "signature": "ET TROJAN HawkEye Keylogger FTP",
                                "action": "allowed",
                                "category": "A Network Trojan was detected"
                            },
                            "flow_id": 795534927178469,
                            "dest_ip": "*************",
                          "flow": {
                                "pkts_toserver": 19,
                              "start": "2019-05-02T22:01:33.174821+0000",
                                "bytes_toclient": 2262,
                              "bytes_toserver": 1244,
                                "pkts_toclient": 28
                          },
                            "dest_port": 21
                      },
                        "sort": [
                          1557737306746
                        ]
                    },
                    {
                        "_index": "rule-eve",
                        "_type": "rule-eve",
                        "_id": "PmtgsGoBFlqIbVTyeO5k",
                        "_score": null,
                        "_source": {
                            "src_ip": "**********",
                            "src_port": 49213,
                            "@timestamp": "2019-05-13T08:47:51.745Z",
                            "payload": "VVNFUiBzbmlmZmVyemV0dA0KUEFTUyB0cmlibGUyMg0KT1BUUyB1dGY4IG9uDQpQV0QNCkNXRCAvDQpUWVBFIEkNClBBU1YNClNUT1IgSGF3a0V5ZV9LZXlsb2dnZXJfS2V5bG9nX1JlY29yZHNfM19CUkVBVVgtV0lONy1QQyA1LjIuMjAxOSAxMDowMToyOCBQTS50eHQNClRZUEUgSQ0KUEFTVg0KU1RPUiBzY3JlZW5zaG90Ml9CUkVBVVgtV0lONy1QQy5qcGVnDQo=",
                            "alert": {
                                "severity": 1,
                                "signature_id": 2020410,
                                "rev": 4,
                                "metadata": {
                                    "updated_at": [
                                        "2015_02_11"
                                    ],
                                    "created_at": [
                                        "2015_02_11"
                                    ]
                                },
                                "gid": 1,
                                "signature": "ET TROJAN HawkEye Keylogger FTP",
                                "action": "allowed",
                                "category": "A Network Trojan was detected"
                            },
                            "flow_id": 1004478643677925,
                            "dest_ip": "*************",
                            "flow": {
                                "pkts_toserver": 19,
                                "start": "2019-05-02T22:01:33.174821+0000",
                                "bytes_toclient": 2262,
                                "bytes_toserver": 1244,
                                "pkts_toclient": 28
                            },
                            "dest_port": 21
                        },
                        "sort": [
                            1557737271745
                        ]
                    }
                ]
            }
        }
    }
      
    
    ```

  - 根据 killchains 分类获取【10 todo】

  - 根据威胁分类获取【11 todo】

  - 根据威胁打分降序排列【12 todo】

  - Top 安全事件聚合【13】

    ```bash
    /api/v1/event/group GET （默认按照 24 小时聚合）
    ?keyword=eventName&topNum=20&startTime=1557475221&stopTime=1557907236
    
    keyword: 取值 eventName（按照攻击事件聚合）、sid（按照攻击规则聚合）、srcIp（按照攻击源 IP ）、dstIp（按照攻击目的 IP）
    startTime: 查询的session起始时间，可为空，unix时间戳格式
    stopTime: 查询的session结束时间，可为空，unix时间戳格式
    
    ```

  - Top killchains 阶段聚合【14 todo】

  - Top 威胁分类聚合【15 todo】

  - Top 威胁打分降序聚合【16 todo】

    

## 0x01 BackStream

- 根据源IP、目的IP、源端口、目的端口、协议、flowId、sessionId、起止时间获取数据包【13】

  ```bash
  /api/v1/pcap/dowoload GET
  srcIp=&dstIp=&srcPort=&dstPort=&appProto=&flowId=&sessionId=&startTime=&stopTime=
  
  response:
  {
    "message": "",
    "data": {
    "downUrl": "http://*********:8005/sessions.pcap?fields=suricata&expression=ip.src==*******&&host.dns.all==app-pc&startTime=1234567890&stopTime=1234567890&length=100&start=1"
    }
  }
  
  srcIp: 源IP，可为空，IP标准格式
  dstIp: 目的IP，可为空，IP标准格式
  srcPort: 源端口，可为空，1-65536的整数
  dstPort: 目的端口，可为空，1-65536的整数
  appProto: 协议，可为空
  flowId: suricata日志会话的唯一标识符，可为空
  sessionId: moloch流量session的唯一标识符，可为空
  startTime: 查询的session起始时间，可为空，unix时间戳格式
  stopTime: 查询的session结束时间，可为空，unix时间戳格式
  
  
  ```

```
  
  
- 根据 sessionID 查询对应 Ascii、Hex、Natural、UTF-8【21】

  ```shell
  /api/v1/pcap/session GET
  sessionID=&base=&startTime=&stopTime=
  
  responese:
  {
      "message": "",
      "data": `<div class=\"row\" id=\"textpacket\"><div class=\"col-md-6\"><h4><span class=\"srccol\">Source<span class=\"src-col-tip\"></span></span></h4></div><div class=\"col-md-6\"><h4><span class=\"dstcol\">Destination<span class=\"dst-col-tip\"></span></span></h4></div></div><div class=\"row\"><div class=\"col-md-6 sessionsrc\"><div class=\"session-detail-ts\"`
  }
  
  base: 展示的数据样式，如果为空默认值为ascii，值范围为[ascii, hex, natural, utf8]
  sessionId: moloch流量session的唯一标识符，可为空
  startTime: 查询的session起始时间，可为空，unix时间戳格式
  stopTime: 查询的session结束时间，可为空，unix时间戳格式

```

- 统计当前协议、当前流量中前十的源IP、目的IP、当前流量域名的会话概要【22】

  ```shell
  /api/v1/pcap/spi
  spi=&startTime=&stopTime=&num=
  
  response:
  {
    "message": "",
    "data": {
        "protocol": {
            "doc_count_error_upper_bound": 0,
            "sum_other_doc_count": 18903,
            "buckets": [
                {
                    "key": "tcp",
                    "doc_count": 24171
                },
                {
                    "key": "http",
                    "doc_count": 23603
                },
                {
                    "key": "udp",
                    "doc_count": 18338
                }
            ]
        }
    }
  }
  
  spi: 统计的数据名称，值范围为[srcIp, dstIp, protocol, dns.host]
  num: 查询的top数量
  startTime: 查询的session起始时间，可为空，unix时间戳格式
  stopTime: 查询的session结束时间，可为空，unix时间戳格式
  
  ```

- 统计当前某个IP通联情况【23】

  ```bash
  /api/v1/pcap/connect
  start=&num=&startTime=&stopTime=&ip=
  
  response:
  {
      "message": "",
      "data": [
          {
              "id": "***********",
              "cnt": 1,
              "sessions": 2,
              "type": 1,
              "totBytes": 4078,
              "totDataBytes": 2254,
              "totPackets": 28,
              "pos": 0
          },
          {
              "id": "*********",
              "cnt": 1,
              "sessions": 2,
              "type": 2,
              "totBytes": 4078,
              "totDataBytes": 2254,
              "totPackets": 28,
              "pos": 1
          }
      ]
  }
  
  ip: 统计的ip
  startTime: 查询的session起始时间，可为空，unix时间戳格式
  stopTime: 查询的session结束时间，可为空，unix时间戳格式
  start: 从第start个开始的session开始统计，如果为空默认值为1
  stop: 统计到第stop个session结束，如果为空则不限制
  
  ```

  

- 有可能在攻击分片流量有些统计的需求，例如在协议的角度，包括有哪些TCP、SSL、HTTP流量等等

- 根据 目的 IP 查找对应 pcap【33】
- 联合查询【34】



## 0x02 用户管理

目前用户登陆获取 token

```bash
/api/v1/user/login POST
{
"username": "ndr",
"password": "1qaz@WSXndr"
}

response:
{
  "message": "Login successfully",
  "data": {
  "token": "xxxxx"
  }
}

```



## 0x03 系统管理

- 获取系统组件状态

  ```bash
  /api/v1/component/status GET
  
  response:
  {
    "message": "",
    "data": {
    "backDPI_status": "xxxx",
    "backIDS_status" "xxxx",
    "backStream_status" "xxxx"
    }
  }
  
  backDPI_status: pf_ring
  backIDS_status: suricata
  backStream_status: moloch
  
  ```

- 重启系统组件

  ```bash
  /api/v1/component/restart  POST
  keyword=backDPI
  
  keyword 取值：backDPI、backIDS、backStream、all
  
  {
    "message": "restart success",
    "data": {}
  }
  
  
  ```

- 关闭系统组件

  ```bash
  /api/v1/component/stop  POST
  keyword=backDPI
  
  keyword 取值：backDPI、backIDS、backStream、all
  
  {
    "message": "stop success",
    "data": {}
  }
  
  
  
  ```

- 启动系统组件

  ```bash
  /api/v1/component/start  POST
  keyword=backDPI
  
  keyword 取值：backDPI、backIDS、backStream、all
  
  {
    "message": "start success",
    "data": {}
  }
  
  
  
  ```

- 关闭系统

  ```bash
  /api/v1/system/poweroff  POST
  
  
  {
    "message": "System poweroff success",
    "data": {}
  }
  
  
  
  ```

- 重启系统

  ```bash
  /api/v1/system/reboot  POST
  keyword=backDPI
  
  keyword 取值：backDPI、backIDS、backStream、all
  
  {
    "message": "System reboot success",
    "data": {}
  }
  ```
  
  
  <br/>

------------------------------------------

## 0x08 讨论归档