#!/usr/bin/env python3
# coding:utf-8

import sys
sys.path.append('/opt/ndr')

from os import environ
from os import path
import json
import traceback

from ksp import config
from ksp.dataupdate.pkgmanager import PkgManager
from cluster.utils.logger import get_logger
from cluster.utils.upgrade import Upgrade

from cluster.api.manager import MyClusterManager

logger = get_logger(__name__)


def get_pkg_list(cluster_mgr, src_pkginfo_file_path, mgmt_node_ip):
    # 读取升级包信息文件内容
    pkginfo_file_content = cluster_mgr.read_file_content_by_node(
        mgmt_node_ip, src_pkginfo_file_path
    )
    if pkginfo_file_content is None:
        logger.error('Failed to read pkginfo file from node:%s.' % str(mgmt_node_ip))
        return []

    try:
        pkginfo = json.loads(pkginfo_file_content)
        pkg_list = pkginfo['pkg_list']
        return pkg_list
    except Exception as ex:
        logger.error('Failed to parse pkginfo %s.' % pkginfo_file_content)
        return []


def download_and_install_pkg(
        cluster_mgr, pkg_mgr, mgmt_node_ip,
        src_data_pkg_base_dir, pkg_name, pkg_version,
        update_controller, upgrade_log_id):
    try:
        local_data_pkg_home = config.get('data_update.home')
        remote_pkg_path = path.join(src_data_pkg_base_dir, pkg_name)
        # FIXME: read_file_content_by_node非常慢，2G数据需要花1个小时读取
        pkg_file_content = cluster_mgr.read_file_content_by_node(
            mgmt_node_ip, remote_pkg_path)
        if pkg_file_content is None:
            raise Exception(f'Failed to download {remote_pkg_path}!')
        local_pkg_file_path = path.join(local_data_pkg_home, pkg_name)
        local_pkg_file = open(local_pkg_file_path, 'wb')
        local_pkg_file.write(pkg_file_content)
        local_pkg_file.close()
        ret, _, _, _ = pkg_mgr.install_pkg(pkg_name)
        if ret == 0:
            config.set('prod.data_version', pkg_version)
        else:
            logger.error(f'Failed to install pkg {pkg_name} errcode {ret}.')
            conn, _ = cluster_mgr.connect_node_by_ssl(mgmt_node_ip)
            update_controller.record_upgrade_log_end(conn, {
                'id': upgrade_log_id,
                'status': 'failure',
                'process_log': f'Failed to install package {pkg_name}'
            })
            conn.close()
    except Exception as ex:
        logger.error('Failed to download pkg %s.' % (pkg_name, str(ex)))
        conn, _ = cluster_mgr.connect_node_by_ssl(mgmt_node_ip)
        update_controller.record_upgrade_log_end(conn, {
            'id': upgrade_log_id,
            'status': 'failure',
            'process_log': f'Failed to download package {pkg_name} from master.'
        })
        conn.close()
        return


def main():
    src_pkginfo_file_path = environ.get('DATAUPDATE_PKG_INFO_FILEPATH')
    src_data_pkg_base_dir = environ.get('DATAUPDATE_PKG_BASEDIR')
    mgmt_node_ip = config.get("cluster.mgmt_node_ip")

    cluster_mgr = MyClusterManager()
    pkg_mgr = PkgManager()
    update_controller = Upgrade()
    conn, _ = cluster_mgr.connect_node_by_ssl(mgmt_node_ip)
    upgrade_log_id = update_controller.record_upgrade_log_start(conn, "update_mode", "data")
    conn.close()

    # 读取升级包信息文件内容
    pkg_list = get_pkg_list(cluster_mgr, src_pkginfo_file_path, mgmt_node_ip)
    # 遍历所有升级包，下载并安装
    try:
        cur_pkg_version = config.get("prod.data_version")
        for pkg_info in pkg_list:
            if pkg_info['version'] > cur_pkg_version:
                # pkg_name 不带 .pkg 后缀，直接以版本号作为文件名
                pkg_name = pkg_info["version"]
                download_and_install_pkg(
                    cluster_mgr, pkg_mgr, mgmt_node_ip,
                    src_data_pkg_base_dir, pkg_name, pkg_info["version"],
                    update_controller, upgrade_log_id)
    except Exception as ex:
        logger.error('Failed to install pkg %s.' % str(ex))
        traceback.print_exc()
        return


if __name__ == "__main__":
    main()
