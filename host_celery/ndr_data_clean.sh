#!/bin/bash

# 1.清除所有DPI协议解析日志数据
if [ $1 = "true" ];then
  echo "deleteing stream parse log from ck..."
  docker stop clickhouse
  docker rm -f clickhouse
  sudo rm -rf /var/lib/clickhouse/*
  sudo docker-compose -f /root/ndr-deploy/docker-compose.yml up -d clickhouse
  sleep 10
  docker exec -d clickhouse bash -c 'clickhouse-client -u default --password ndr@ck++ --multiquery < /etc/clickhouse-server/init_table.sql'
  echo "deleteing stream parse log finished."
fi

# 停止流量处理
supervisorctl --user ndr --password 1qaz@WSXndr stop hdp

# 2.清楚流量解析的文件
if [ $2 = "true" ];then
  sudo rm -rf /var/log/hdp/files/*
fi

# 3.清除所有全流量留存数据
if [ $3 = "true" ];then
  echo "delete the raw pkt of stream..."
  if [ -d "/data/disk6" ]; then
      sudo rm -rf /data/disk1/realtime_pkts1/
      sudo rm -rf /data/disk2/realtime_pkts2/
      sudo rm -rf /data/disk3/realtime_pkts3/
      sudo rm -rf /data/disk4/realtime_pkts4/
      sudo rm -rf /data/disk5/realtime_pkts5/
      sudo rm -rf /data/disk6/realtime_index1/
      sudo rm -rf /data/disk6/realtime_index2/
      sudo rm -rf /data/disk6/realtime_index3/
      sudo rm -rf /data/disk6/realtime_index4/
      sudo rm -rf /data/disk6/realtime_index5/

      sudo mkdir -p /data/disk1/realtime_pkts1/
      sudo mkdir -p /data/disk2/realtime_pkts2/
      sudo mkdir -p /data/disk3/realtime_pkts3/
      sudo mkdir -p /data/disk4/realtime_pkts4/
      sudo mkdir -p /data/disk5/realtime_pkts5/
      sudo mkdir -p /data/disk6/realtime_index1/
      sudo mkdir -p /data/disk6/realtime_index2/
      sudo mkdir -p /data/disk6/realtime_index3/
      sudo mkdir -p /data/disk6/realtime_index4/
      sudo mkdir -p /data/disk6/realtime_index5/

      sudo rm -rf /data/disk1/replay_pkts1/
      sudo rm -rf /data/disk2/replay_pkts2/
      sudo rm -rf /data/disk3/replay_pkts3/
      sudo rm -rf /data/disk4/replay_pkts4/
      sudo rm -rf /data/disk5/replay_pkts5/
      sudo rm -rf /data/disk6/replay_index1/
      sudo rm -rf /data/disk6/replay_index2/
      sudo rm -rf /data/disk6/replay_index3/
      sudo rm -rf /data/disk6/replay_index4/
      sudo rm -rf /data/disk6/replay_index5/

      sudo mkdir -p /data/disk1/replay_pkts1/
      sudo mkdir -p /data/disk2/replay_pkts2/
      sudo mkdir -p /data/disk3/replay_pkts3/
      sudo mkdir -p /data/disk4/replay_pkts4/
      sudo mkdir -p /data/disk5/replay_pkts5/
      sudo mkdir -p /data/disk6/replay_index1/
      sudo mkdir -p /data/disk6/replay_index2/
      sudo mkdir -p /data/disk6/replay_index3/
      sudo mkdir -p /data/disk6/replay_index4/
      sudo mkdir -p /data/disk6/replay_index5/
  else
      sudo rm -rf /data/disk1/realtime_pkts1/
      sudo rm -rf /data/disk1/realtime_index1/
      sudo rm -rf /data/disk2/realtime_pkts2/
      sudo rm -rf /data/disk2/realtime_index2/
      sudo rm -rf /data/disk3/realtime_pkts3/
      sudo rm -rf /data/disk3/realtime_index3/
      sudo rm -rf /data/disk4/realtime_pkts4/
      sudo rm -rf /data/disk4/realtime_index4/

      sudo mkdir -p /data/disk1/realtime_pkts1/
      sudo mkdir -p /data/disk1/realtime_index1/
      sudo mkdir -p /data/disk2/realtime_pkts2/
      sudo mkdir -p /data/disk2/realtime_index2/
      sudo mkdir -p /data/disk3/realtime_pkts3/
      sudo mkdir -p /data/disk3/realtime_index3/
      sudo mkdir -p /data/disk4/realtime_pkts4/
      sudo mkdir -p /data/disk4/realtime_index4/

      sudo rm -rf /data/disk1/replay_pkts1/
      sudo rm -rf /data/disk1/replay_index1/
      sudo rm -rf /data/disk2/replay_pkts2/
      sudo rm -rf /data/disk2/replay_index2/
      sudo rm -rf /data/disk3/replay_pkts3/
      sudo rm -rf /data/disk3/replay_index3/
      sudo rm -rf /data/disk4/replay_pkts4/
      sudo rm -rf /data/disk4/replay_index4/

      sudo mkdir -p /data/disk1/replay_pkts1/
      sudo mkdir -p /data/disk1/replay_index1/
      sudo mkdir -p /data/disk2/replay_pkts2/
      sudo mkdir -p /data/disk2/replay_index2/
      sudo mkdir -p /data/disk3/replay_pkts3/
      sudo mkdir -p /data/disk3/replay_index3/
      sudo mkdir -p /data/disk4/replay_pkts4/
      sudo mkdir -p /data/disk4/replay_index4/

      if  [ -d "/data/disk5" ]; then
          sudo rm -rf /data/disk5/realtime_pkts5/
          sudo rm -rf /data/disk5/realtime_index5/
          sudo mkdir -p /data/disk5/realtime_pkts5/
          sudo mkdir -p /data/disk5/realtime_index5/
          sudo rm -rf /data/disk5/replay_pkts5/
          sudo rm -rf /data/disk5/replay_index5/
          sudo mkdir -p /data/disk5/replay_pkts5/
          sudo mkdir -p /data/disk5/replay_index5/
      fi
  fi
  echo "delete the raw pkt of stream finished."
fi
supervisorctl --user ndr --password 1qaz@WSXndr start hdp
#if [ -d "/tmp/ndr_ftp_pcap_cache/" ]; then
#    sudo rm -rf /tmp/ndr_ftp_pcap_cache/*
#fi

#if [ -d "/var/ndr_datasource/tmp" ]; then
#    sudo rm -rf /var/ndr_datasource/tmp/*
#fi

#sudo rm -rf /tmp/*.pcap

echo ""
echo "CLean complited!"
