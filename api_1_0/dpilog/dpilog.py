#!/usr/bin/env python3
# -*- coding:utf-8 -*-
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator
from config.config import Ndr<PERSON>og
from utils import param_check
from flask import request, make_response, send_file
import time
import os
from clickhouse_driver import Client as CH_client
from clickhouse_driver import errors as CH_errors
from config.config import ClickHouseConfig
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class DpilogResource(Resource):
    def __init__(self):
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)
        self.get_src_ip_cond = lambda \
            ip: "(src_ipv6=toIPv6('%s'))" % ip if ':' in ip else "(src_ipv4=toIPv4('%s'))" % ip
        self.get_dst_ip_cond = lambda \
            ip: "(dst_ipv6=toIPv6('%s'))" % ip if ':' in ip else "(dst_ipv4=toIPv4('%s'))" % ip

    def __del__(self):
        self.ch_client.disconnect()

    def get_val_from_database(self, sql):
        ret_list = list()
        err_msg = ""
        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except CH_errors.NetworkError:
            err_msg = "ClickHouse数据库连接失败！"
        except CH_errors.ServerException as e:
            if e.code == CH_errors.ErrorCodes.TOO_SLOW:
                err_msg = "Clickhouse查询超时！"
            else:
                err_msg = "Clickhouse查询错误，Code：%d" % e.code
        except Exception as e:
            LOG.error("Clickhouse excute ERRORS: %s" % str(e))
            err_msg = 'Clickhouse未知错误！'

        if err_msg:
            ndr_log_to_box(NdrLog.Type.RUN, err_msg)
            LOG.error('Clickhouse query Failed! [%s]' % sql)
            return err_msg, ret_list

        if raw is None or not raw[0]:
            return 'Not found!', []
        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]
        unexpect_para = ['src_ipv4', 'src_ipv6', 'dst_ipv4', 'dst_ipv6']
        for i in range(len(raw_val)):
            data = {}
            for idx, item in enumerate(raw_val[i]):
                if raw_key[idx][0] in unexpect_para:
                    continue
                if isinstance(item, bytes):
                    item = str(item)

                data[raw_key[idx][0]] = item
            ret_list.append(data)
        return "", ret_list

    def get_ip_sql(self, args):
        ip_sql = []
        accurate_flag = args['isAccurate']
        if accurate_flag == "true":  # 解析精确匹配ip
            if args["ip1"]:
                ip_sql.append(self.get_src_ip_cond(args["ip1"]))
            if args["ip2"]:
                ip_sql.append(self.get_dst_ip_cond(args["ip2"]))
        else:  # 解析非精确匹配ip
            ip_list = []
            if args['ip1']:
                ip_list.append(args['ip1'])
            if args['ip2']:
                ip_list.append(args['ip2'])
            if len(ip_list) == 1:
                ip_sql.append(
                    "(%s OR %s)" % (self.get_src_ip_cond(ip_list[0]), self.get_dst_ip_cond(ip_list[0])))
            elif len(ip_list) == 2:
                ip_sql.append("((%s AND %s) OR (%s AND %s))" % (
                    self.get_src_ip_cond(ip_list[0]), self.get_dst_ip_cond(ip_list[1]),
                    self.get_src_ip_cond(ip_list[1]), self.get_dst_ip_cond(ip_list[0])))
        return (' AND ' + ' AND '.join(ip_sql)) if ip_sql else ''


class DpilogListInfo(DpilogResource):
    def __init__(self):
        super(DpilogListInfo, self).__init__()
        self.keyword_dict = {
            "dpilog_conn": "",
            "dpilog_http": "uri,host,referrer,user_agent",
            "dpilog_dns": "query,result",
            "dpilog_ftp": "arg,conn_id",
            "dpilog_mail": "from,to,subject",
            "dpilog_ssl": "server_name,ja3,ja3s",
            "dpilog_files": "filename,md5",
            "dpilog_icmp": "",
            "dpilog_mysql": "",
            "dpilog_login": "",
            "dpilog_dhcp": "",
            "dpilog_telnet": "",
            "dpilog_nfs": "",
            "dpilog_modbus": "",
            "dpilog_tftp": "type",
            "dpilog_rip": "",
            "dpilog_netbios": "",
            "dpilog_snmp": "kv_list",
            "dpilog_igmp": "",
            "dpilog_smb": "",
            "dpilog_mssql": "",
        }
        self.cond_dict = {
            "startTime": "(create_time>='%s')",
            "stopTime": "(create_time<='%s')",
            "srcPort": "(src_port='%s')",
            "dstPort": "(dst_port='%s')",
            "celeryId": "(celeryId='%s')",
            "pcap_filename": "(pcap_filename like '%%%s%%')"
        }

    def __del__(self):
        super(DpilogListInfo, self).__del__()

    def get_mail_sql(self, args):
        cond_dict = {
            "from": "(from='%s')",
            "recipient": "(to='%s')",
            "file_names": "(file_names='%s')",
            "service": "(service='%s')"
        }
        cond_sql = []
        if args["body"]:
            cond_sql.append("(body like '%" + args["body"] + "%')")
        if args["subject"]:
            cond_sql.append("(subject like '%" + args["subject"] + "%')")
        for key, val in cond_dict.items():
            if args[key]:
                cond_sql.append(val % args[key])
        return (' AND ' + ' AND '.join(cond_sql)) if cond_sql else ''

    def get_keyword_cond_sql(self, args, dpilog_type):
        if not args['keyword'] or dpilog_type not in self.keyword_dict or self.keyword_dict[dpilog_type] == "":
            return ""
        keyword_list = list()
        for i in self.keyword_dict[dpilog_type].split(','):
            keyword_list.append(i + " like '%" + args['keyword'] + "%'")
        keyword_sql = ' OR '.join(keyword_list)
        # 如果对应日志表没有搜索关键字，则使用1=0过滤掉整个日志表
        return ' AND (' + keyword_sql + ')'

    def get_basic_sql(self, args):
        cond_sql = []
        for key, val in self.cond_dict.items():
            if args[key]:
                cond_sql.append(val % args[key])
        ip_sql_str = self.get_ip_sql(args)
        return (' AND '.join(cond_sql) if cond_sql else '') + ip_sql_str

    @param_check.check_flask_args(Validator('dpilog_list_info_schema'), request)
    def get(self, **kwargs):
        if not kwargs['startTime'] or not kwargs['stopTime']:
            return flask_response("not Found", True, [])
        kwargs['startTime'] = kwargs['startTime'][:-3]
        kwargs['stopTime'] = kwargs['stopTime'][:-3]
        limit_start = int(kwargs['pageSize']) * (int(kwargs['page']) - 1)
        limit_offset = int(kwargs['pageSize'])
        limit_sql = " order by create_time desc limit %s, %s" % (limit_start, limit_offset)
        dpilog_type = kwargs['dpilogType']
        application = kwargs['application']
        sql_count = "select count(*) as total from %s where %s " % \
                    (dpilog_type, self.get_basic_sql(kwargs) + self.get_keyword_cond_sql(kwargs, dpilog_type))
        sql_str = "select * from %s where %s " % \
                  (dpilog_type, self.get_basic_sql(kwargs) + self.get_keyword_cond_sql(kwargs, dpilog_type))
        if dpilog_type == 'dpilog_conn' and application:
            sql_count = sql_count + " AND (application='%s') " % application
            sql_str = sql_str + " AND (application='%s') " % application
        if dpilog_type == 'dpilog_mail':
            sql_count = sql_count + self.get_mail_sql(kwargs)
            sql_str = sql_str + self.get_mail_sql(kwargs)
        sql_str = sql_str + limit_sql
        data = dict()

        msg, ret_list = self.get_val_from_database(sql_count)
        if not ret_list:
            LOG.info("获取日志数量统计失败：%s" % msg)
            return flask_response(msg, True, [])
        data['total'] = ret_list[0]['total']
        if ret_list[0]['total'] > 20000000:
            return flask_response("您搜索的结果已超过2000万条数据，请缩小搜索范围！", False, [])
        msg, ret_list = self.get_val_from_database(sql_str)
        if not ret_list:
            LOG.info("获取日志列表信息失败：%s" % msg)
            return flask_response(msg, True, [])
        data['dpilog'] = ret_list
        for dpilog in data['dpilog']:
            dpilog['uuid'] = str(dpilog['uuid'])
            dpilog['create_time'] = int(dpilog['create_time'].timestamp()) * 1000
            dpilog['ts'] = int(dpilog['ts'].timestamp()) * 1000
        ndr_log_to_box(NdrLog.Type.OPERATE, "获取DPI统计信息成功")
        return flask_response(msg, True, data)


class DpilogStatisticsInfo(DpilogResource):
    def __init__(self):
        super(DpilogStatisticsInfo, self).__init__()
        self.current_time = int(time.time())
        # 最大记录时间为6个月
        self.max_log_time = 6 * 30 * 24 * 3600
        # 数据库记录的最大过去时间
        self.past_time = self.current_time - self.max_log_time
        # 显示的柱状条数
        self.bars = 120
        # 时间粒度表，单位秒，分别是1min，30min，2h，6h，36h
        # 根据bars为120可以计算出时间范围分别是：
        # 0~2h, 2h~2.5d, 2.5d~10d, 10d~1m, 1m~6m
        self.granularity = [60, 1800, 7200, 21600, 129600]
        # 数据显示单位
        self.unit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
        self.cond_dict = {
            "srcPort": "(src_port='%s')",
            "dstPort": "(dst_port='%s')",
            "celeryId": "(celeryId='%s')",
            "pcap_filename": "(pcap_filename like '%%%s%%')"
        }

    def __del__(self):
        super(DpilogStatisticsInfo, self).__del__()

    def get_time_interval(self, args):
        start_time = int(args['startTime'][:-3])
        stop_time = int(args['stopTime'][:-3])
        time_granularity = self.granularity[-1]
        # 如果结束时间比当前时间大，则取当前时间为结束时间
        if stop_time > self.current_time:
            stop_time = self.current_time
        time_gap = stop_time - start_time
        # 根据提供的时间范围计算合适的时间粒度
        tmp = int(time_gap / self.bars)
        for granularity in self.granularity:
            if tmp <= granularity:
                time_granularity = granularity
                break
            else:
                continue
        # 计算显示的时间范围
        # (start_time + stop_time) / 2 表示获取中间时间点
        # (self.bars / 2) * time_granularity 表示中间点往后偏移一半的柱状条数的时间
        stop_interval = (start_time + stop_time) / 2 + (self.bars / 2) * time_granularity
        # 如果计算的结束范围大于当前时间，则取当前时间为终止时间
        if stop_interval > self.current_time:
            stop_interval = self.current_time
        start_interval = stop_interval - self.bars * time_granularity
        return time_granularity, (int(start_interval), int(stop_interval))

    def get_unit_index(self, num):
        unit_idx = 0

        while num > 1024:
            num = num >> 10
            unit_idx += 1

        if unit_idx > len(self.unit) - 1:
            unit_idx = len(self.unit) - 1

        return unit_idx

    def get_size(self, num, unit_idx, ndig):
        return str(round(num / pow(1024, unit_idx), ndig)) + self.unit[unit_idx]

    @param_check.check_flask_args(Validator('dpilog_statistics_info_schema'), request)
    def get(self, **kwargs):
        if not kwargs['startTime'] or not kwargs['stopTime']:
            return flask_response("not Found", True, [])
        if int(kwargs['startTime'][:-3]) > self.current_time:
            ndr_log_to_box(NdrLog.Type.OPERATE, "获取DPI统计信息失败：不存在。")
            return flask_response('Not found', False, [])
        time_granularity, time_interval = self.get_time_interval(kwargs)
        cond_sql = "where create_time >= '%s' AND  create_time <= '%s'" % (str(time_interval[0]), str(time_interval[1]))
        application = kwargs['dpilogType'].split('_')[1]
        if application == "conn":
            pass
        elif application == "mail":
            cond_sql = cond_sql + " AND application in ('smtp','pop3','imap') "
        elif application == "ssl":
            cond_sql = cond_sql + " AND application='https' "
        elif application == "icmp":
            cond_sql = cond_sql + " AND application='ICMP' "
        elif application == "igmp":
            cond_sql = cond_sql + " AND application='IGMP' "
        else:
            cond_sql = cond_sql + " AND application='%s' " % application
        sql_basic_list = []
        for key, val in self.cond_dict.items():
            if kwargs[key]:
                sql_basic_list.append(val % kwargs[key])
        sql_basic_str = (' AND ' + ' AND '.join(sql_basic_list)) if sql_basic_list else ''
        ip_sql_str = self.get_ip_sql(kwargs)
        cond_sql = cond_sql + sql_basic_str + ip_sql_str
        sql = "select toStartOfInterval(create_time, INTERVAL " \
              + str(int(time_granularity / 60)) + " minute) as time, " \
              + "sum(orig_ip_bytes)+sum(resp_ip_bytes) as total_bytes " \
              + "from dpilog_conn " \
              + cond_sql \
              + " group by time order by time"
        msg, ret_list = self.get_val_from_database(sql)
        if not ret_list:
            LOG.info("获取流量统计失败：%s" % msg)
            return flask_response(msg, True, [])
        data = list()
        # 找到数据最大值，并对所有返回的数据库数据进行转换
        max_num = 0
        for ret_data in ret_list:
            num = int(ret_data['total_bytes'])
            ret_data['total_bytes'] = num
            if num > max_num:
                max_num = num

        # 根据最大值计算数据显示单位编号
        unit_idx = self.get_unit_index(max_num)

        # 确定起始时间，由于toStartOfInterval不会输出区间记录为0的值，需要手动添加数据
        start_time = time_interval[0] - (time_interval[0] % time_granularity)
        j = 0
        total_ret_list = len(ret_list)
        for i in range(self.bars):
            bar_time = start_time + i * time_granularity
            # 如果计算的时间大于stop_time，结束循环
            if bar_time > time_interval[1]:
                break
            if j < total_ret_list and bar_time == int(
                    ret_list[j]['time'].timestamp()):
                # 转换时间
                ret_list[j]['time'] = int(
                    ret_list[j]['time'].timestamp()) * 1000

                actualSize = ret_list[j]['total_bytes'] / pow(1024, unit_idx)
                # 换算最小显示
                if actualSize > 0 and actualSize < 0.01:
                    actualSize = 0.01
                else:
                    actualSize = round(actualSize, 2)

                # 修改total_bytes数据结构，
                # ex：total_bytes:{'actualSize': '1024.0B', 'showSize': '1.0K'}
                ret_list[j]['total_bytes'] = {
                    'actualSize': str(actualSize) + self.unit[unit_idx],
                    'showSize': self.get_size(
                        ret_list[j]['total_bytes'],
                        self.get_unit_index(ret_list[j]['total_bytes']), 1)
                }
                data.append(ret_list[j])
                j += 1
            # 没有或不足的数据添加0值
            else:
                data.append({
                    'time': bar_time * 1000,
                    'total_bytes': {
                        'actualSize': self.get_size(0, unit_idx, 2),
                        'showSize': "0B"}})

        ndr_log_to_box(NdrLog.Type.OPERATE, "获取DPI统计信息成功")
        return flask_response(msg, True, data)


# 邮件下载
class DpilogMailDown(DpilogResource):
    @param_check.check_flask_args(Validator('dpilog_mail_down_schema'), request)
    def get(self, **kwargs):
        try:
            # is_exist 是为了先判断文件是否存在，主要是解决前端解析返回数据问题
            is_exist = kwargs['is_exist']
            stamp = kwargs['filename'][:10]
            if not stamp.isdigit():
                return flask_response("文件名参数错误：%s" % kwargs['filename'], False, {})
            dir_name = time.strftime("%Y%m%d", time.localtime(int(stamp)))
            filepath = os.path.join("/var/log/hdp/files/", dir_name, kwargs['filename'])
            if not os.path.exists(filepath):
                return flask_response("%s 该文件类型不支持下载或者文件已被清理" % kwargs['filename'], False, {})

            if is_exist == "true":
                return flask_response("", True, {})
            else:
                return make_response(send_file(filepath, as_attachment=True))
        except Exception as e:
            return flask_response("下载失败！", False, {'msg': str(e)})


# files下载
class DpilogFileDown(DpilogResource):
    @param_check.check_flask_args(Validator('dpilog_mail_down_schema'), request)
    def get(self, **kwargs):
        try:
            # is_exist 是为了先判断文件是否存在，主要是解决前端解析返回数据问题
            is_exist = kwargs['is_exist']
            stamp = kwargs['filename'][:10]
            if not stamp.isdigit():
                return flask_response("文件名参数错误：%s" % kwargs['filename'], False, {})
            dir_name = time.strftime("%Y%m%d", time.localtime(int(stamp)))
            filepath = os.path.join("/var/log/hdp/files/", dir_name, kwargs['filename'])
            if not os.path.exists(filepath):
                return flask_response("%s 该文件类型不支持下载或者文件已被清理" % kwargs['filename'], False, {})

            if is_exist == "true":
                return flask_response("", True, {})
            else:
                return make_response(send_file(filepath, as_attachment=True))
        except Exception as e:
            return flask_response("下载失败！", False, {'msg': str(e)})
