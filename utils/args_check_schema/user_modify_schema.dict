{
	"type": "object",
	"required": ["userId"],
	"properties": {
		"userId": {
			"type": "string",
			"pattern": r"^[0-9a-fA-F]{32}$"
		},
		"oldPassword": {
			"type": "string",
			"pattern": r"^$|^.{8,64}$",
			"default": ""
		},
		"newPassword": {
			"type": "string",
			"pattern": r"^$|^.{8,64}$",
			"default": ""
		},
		"active": {
			"type": "boolean",
			"default": True
		},
		"email": {
			"type": "string",
			"pattern": r"^$|^[A-Za-z0-9]+([_\.][A-Za-z0-9]+)*@([A-Za-z0-9\-]+\.)+[A-Za-z]{2,16}$",
			"default": ""
		},
		"phone": {
			"type": "string",
			"pattern": r"^$|^[0-9]{1,32}$",
			"default": ""
		},
		"department": {
			"type": "string",
			"pattern": r"^$|^[A-Za-z0-9_\-\u4e00-\u9fa5]{1,128}$",
			"default": ""
		}
	}
}