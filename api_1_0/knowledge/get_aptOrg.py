#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import re
from flask import request
from flask_restful import Resource
from utils.utils import flask_response
from utils.es_template.get_es_template import ES_Template
from utils import param_check
from utils.param_check import Validator
from utils.database import get_es_client
from config.config import IOC_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class GetAptName(Resource):
    '''获取apt攻击组织'''

    def __init__(self):
        self.es_template = ES_Template().read_template("knowledge_apt_org")

    @param_check.check_flask_args(Validator("apt_org"), request)
    def get(self, **kwargs):
        keyword = kwargs["args"].get("aptOrganization", None)
        if keyword:
            re_word_en = re.findall(r'[A-Za-z\d]+', keyword)
            re_word_cn = re.findall(r'[\u4e00-\u9fa5]+', keyword)
            result_en = ["*{0}*".format(word) for word in re_word_en]
            result = " ".join(result_en + re_word_cn)
            args = {
                "query_string": {
                    "query": result.lower(),
                    "default_field": "iocInfo.aptOrganization",
                    "analyzer": "ik_max_word",
                    "analyze_wildcard": "true",
                    "default_operator": "and",
                    "lowercase_expanded_terms": "true"
                }
            }
            self.es_template["query"]["bool"]["must"].append(args)

        # 连接ES查询
        es_client = get_es_client()
        # return self.es_template
        try:
            res = es_client.search(index=IOC_INDEX, body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, {})

        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    @staticmethod
    def data_format(es_data):
        data_return = []
        apt_data_list = es_data["aggregations"]["groupBy"]["buckets"]
        for apt_info in apt_data_list:
            each_apt_dict = {
                "aptOrganization": apt_info["key"] if apt_info["key"] else "未知"
            }
            data_return.append(each_apt_dict)

        return data_return
