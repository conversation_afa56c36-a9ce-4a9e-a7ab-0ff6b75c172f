#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from flask_restful import Resource
from utils.database import MongoDB
from utils.utils import flask_response
from utils.logger import get_ndr_logger

MONGO_LOG = get_ndr_logger("mongo_log", __file__)


class BaseKnowledge(Resource):
    '''知识库基类'''

    def __init__(self, col, filter_name):
        self.mongo_client = MongoDB("ndr")
        self.col = col
        self.filter_name = filter_name

    def get(self):
        """从mongoDB中查询filter_name字段信息返回"""
        data = self.mongo_client.find_one(self.col, {"name": self.filter_name})

        if not data:
            return flask_response("MongoDB return data error", False, [])

        return flask_response("", True, data["list"])

    def push_to_db(self):
        '''讲所需字段添加到mongo库中'''
        filed = self.get_filed()
        self.mongo_client.drop(self.col)
        is_insert = self.mongo_client.insert_one(self.col, filed)
        if not is_insert:
            MONGO_LOG.info("MongoDB 插入[{0}] 失败".format(filed))

    @staticmethod
    def get_filed():
        pass
