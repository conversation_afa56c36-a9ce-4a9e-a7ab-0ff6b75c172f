import React from 'react';

import { Tooltip } from 'antd';
import { IntlComponent } from 'Components/Common';
import { levelColor } from '@/utils/enumList';
import style from './index.less';

const LevelColor = () => {
  const renderTitle = (level: {
    High: React.ReactNode;
    Medium: React.ReactNode;
    Low: React.ReactNode;
  }) => (
    <div>
      <div>High: {level.High}</div>
      <div>Medium: {level.Medium}</div>
      <div>Low: {level.Low}</div>
    </div>
  );
  return (
    <Tooltip title={renderTitle(level)}>
      <div className={style.wrapper}>
        <div className={style.count}>{count}</div>
        <div
          className={style.high}
          style={{ width: widthH, backgroundColor: levelColor.High }}
        />
        <div
          className={style.middle}
          style={{ width: widthM, backgroundColor: levelColor.Medium }}
        />
        <div
          className={style.low}
          style={{ width: widthL, backgroundColor: levelColor.Low }}
        />
      </div>
    </Tooltip>
  );
};
export default LevelColor;
