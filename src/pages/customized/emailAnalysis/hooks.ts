import { useState, useEffect } from "react";
import { message } from "antd";
import { LabeledValue } from "antd/lib/select";
import {
  fetchEmailAttachmentDetail,
  fetchEmailAttachmentList,
  fetchEmailDetail,
  fetchEmailList,
  fetchEmailSendRecvInfo,
  fetchEmailTargetedAttackList,
  fetchFileDetail,
  fetchRecvOrgList,
  getExportFileUrl,
  getFileAnalysisOptions
} from "@/services/threatAnalysis";

import { useLoadingData, usePaginationData } from "../hooks";

import { useTimeRange } from "@/pages/threatAwareness/hooks";

import type {
  EmailAttachmentReportDetail,
  EmailAttachmentItem,
  EmailDetail,
  EmailListItem,
  EmailListParams,
  EmailSendRecvInfo,
  EmailTargetedAttackListItem,
  EmailTargetedAttackParams,
  ReportDetail
} from "../types";

export type TimeRange = {
  start_time: number;
  end_time: number;
}

export function useFileAnalysisQueryOptions() {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<Record<string, LabeledValue[]>>({
    labels: [],
    threatName: [],
    application: [],
    source: [],
    platform: [],
    family: [],
  });

  const getOptions = async () => {
    try {
      setLoading(true)
      const { data: res, flag } = await getFileAnalysisOptions();
      if (flag) {
        const _options = {}
        Object.keys(res).forEach((key) => {
          const { data: { buckets } } = res[key];
          if (_options[key]) {
            _options[key] = buckets?.map(({ key }: any) => ({ label: key, value: key }))
          }
        })
        setOptions(_options)
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getOptions()
  }, [])

  return {
    options,
    loading,
  }
}

export function useEmailAnalysisListData(timeRange: TimeRange, abortCallback?: (c: AbortController) => void) {
  const {
    data: list,
    loading,
    setLoading,
    setData,
  } = useLoadingData<EmailListItem[]>([], true);

  const controller = new AbortController();
  const { signal } = controller;

  const { pagination, updatePagination } = usePaginationData()

  const [sort, setSort] = useState('');

  const [queryParams, setQueryParams] = useState<Omit<EmailListParams, 'start_time' | 'end_time' | 'page' | 'pageSize'>>({});

  const [range, setTimeRange] = useTimeRange(timeRange)

  const getEmailList = async () => {
    try {
      setLoading(true)
      const params = {
        ...range,
        ...queryParams,
        sort,
        page: pagination.page,
        pageSize: pagination.pageSize
      }

      // Object.keys(params).forEach(key => {
      //   if (typeof params[key] === 'number') {
      //     params[key] = params[key].toString()
      //   }
      // })

      abortCallback?.(controller)

      const { data: res, flag } = await fetchEmailList(params, signal)
      if (flag) {
        const { total, detail } = res;
        setData(detail);
        updatePagination({ total })
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return;
      }
      else {
        message.error(error.message)
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getEmailList()
  }, [pagination.page, pagination.pageSize, range, sort, queryParams])

  return {
    list,
    loading,
    pagination,
    setSort,
    updatePagination,
    setTimeRange,
    setQueryParams,
  }
}

export function useEmailAttachmentListData(timeRange: TimeRange, abortCallback?: (c: AbortController) => void) {
  const {
    data: list,
    loading,
    setLoading,
    setData,
  } = useLoadingData<EmailAttachmentItem[]>([], true);

  const controller = new AbortController();
  const { signal } = controller;

  const { pagination, updatePagination } = usePaginationData()

  const [sort, setSort] = useState('');

  const [queryParams, setQueryParams] = useState<Omit<EmailListParams, 'start_time' | 'end_time' | 'page' | 'pageSize'>>({});

  const [range, setTimeRange] = useTimeRange(timeRange)

  const getEmailList = async () => {
    try {
      setLoading(true)
      const params = {
        ...range,
        ...queryParams,
        sort,
        page: pagination.page,
        pageSize: pagination.pageSize
      }

      // Object.keys(params).forEach(key => {
      //   if (typeof params[key] === 'number') {
      //     params[key] = params[key].toString()
      //   }
      // })
      abortCallback?.(controller)

      const { data: res, flag } = await fetchEmailAttachmentList(params, signal)

      if (flag) {
        const { total, detail } = res;
        setData(detail);
        updatePagination({ total })
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return;
      }
      else {
        message.error(error.message)
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getEmailList()
  }, [pagination.page, pagination.pageSize, queryParams, range, sort])

  return {
    list,
    loading,
    pagination,
    setSort,
    updatePagination,
    setTimeRange,
    setQueryParams,
  }
}

export function useEmailTargetedAttackListData(timeRange: TimeRange, abortCallback?: (c: AbortController) => void) {
  const {
    data: list,
    loading,
    setLoading,
    setData,
  } = useLoadingData<EmailTargetedAttackListItem[]>([], true);

  const controller = new AbortController();
  const { signal } = controller;

  const { pagination, updatePagination } = usePaginationData()

  const [queryParams, setQueryParams] = useState<Omit<EmailTargetedAttackParams, 'start_time' | 'end_time' | 'page' | 'pageSize'>>({});

  const [range, setTimeRange] = useTimeRange(timeRange)

  const getEmailAttackList = async () => {
    try {
      setLoading(true)
      const params = {
        ...range,
        ...queryParams,
        page: pagination.page,
        pageSize: pagination.pageSize
      }

      Object.keys(params).forEach(key => {
        if (typeof params[key] === 'number') {
          params[key] = params[key].toString()
        }
      })
      abortCallback?.(controller)

      // const { data: res, flag } = await fetchEmailTargetedAttackList(params, signal)

      // if (flag) {
      //   const { total, detail } = res;
      //   setData(detail);
      //   updatePagination({ total })
      // }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return;
      }
      else {
        message.error(error.message)
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getEmailAttackList()
  }, [pagination.page, pagination.pageSize, queryParams, range])

  return {
    list,
    loading,
    pagination,
    updatePagination,
    setTimeRange,
    setQueryParams,
  }
}


export function useEmailDetailData(params: { msg_id: string } & TimeRange) {
  const {
    data,
    loading,
    setLoading,
    setData,
  } = useLoadingData<EmailDetail | null>(null, true);

  const getEmailDetail = async () => {
    try {
      setLoading(true)
      const { data: { detail }, flag } = await fetchEmailDetail(params)
      if (flag) {
        setData(detail);
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  const downloadFile = async (fileid: string) => {
    try {
      const { data, flag: fileFlag } = await fetchEmailAttachmentDetail({
        uuid: fileid,
        start_time: params.start_time,
        end_time: params.end_time
      })

      const [fileData] = data;
      if (fileFlag && fileData._source.report_path) {
        const filePath = `${fileData._source.file.file_path}/${fileData._source.file.fuid}`
        const url = await getExportFileUrl({ filePath })
        window.open(`${window.location.origin}/mica-api/api/v3/${url}`, '_self')
        return true
      }
    } catch (error: any) {
      message.error(error.message)
    }
    return false
  }

  useEffect(() => {
    getEmailDetail()
  }, [])

  return {
    data,
    loading,
    downloadFile,
  }
}

export function useEmailSendRecvData(params: { msg_id: string } & TimeRange) {
  const {
    data,
    loading,
    setLoading,
    setData,
  } = useLoadingData<EmailSendRecvInfo | null>(null, true);

  const getEmailList = async () => {
    try {
      setLoading(true)
      const { data: res, flag } = await fetchEmailSendRecvInfo(params)
      const { total, detail } = res;
      if (flag) {
        setData(detail);
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getEmailList()
  }, [])

  return {
    data,
    loading,
  }
}


export function useEmailAttachmentDetailData(params: { uuid: string } & TimeRange) {
  const {
    data: fileBaseData,
    loading,
    setLoading,
    setData,
  } = useLoadingData<EmailAttachmentReportDetail | null>(null, true);

  const {
    data: fileDetailData,
    setData: setDetail,
  } = useLoadingData<ReportDetail | null>(null, true);

  const getReportDetail = async () => {
    try {
      setLoading(true)
      const { data, flag: fileFlag } = await fetchEmailAttachmentDetail(params)
      const [fileData] = data
      if (fileFlag && fileData) {
        setData(fileData);
        const { report_path } = fileData?._source
        if (report_path) {
          const { data, flag } = await fetchFileDetail({ filePath: report_path });
          if (flag) {
            setDetail(data)
          }
        }
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getReportDetail()
  }, [])

  return {
    fileBaseData,
    fileDetailData,
    loading,
  }
}

export function useRecvOrgOptions(timeRange: TimeRange) {
  const {
    data,
    loading,
    setLoading,
    setData,
  } = useLoadingData<any>([], true);
  const [range, setRange] = useTimeRange(timeRange)

  const getRecvOrgList = async () => {
    try {
      setLoading(true)
      const { data: res, flag } = await fetchRecvOrgList(range)
      if (flag) {
        setData(res?.map((value) => ({ label: value, value })) || []);
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  // useEffect(() => {
  //   getRecvOrgList()
  // }, [range])

  return {
    loading,
    data,
    setRange
  }
}