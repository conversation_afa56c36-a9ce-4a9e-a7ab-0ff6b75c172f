# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import forward_pb2 as forward__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


class ForwardStub(object):
    """The forward service definition.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetStats = channel.unary_unary(
                '/forward.Forward/GetStats',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=forward__pb2.StatsReply.FromString,
                )
        self.ClearStats = channel.unary_unary(
                '/forward.Forward/ClearStats',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.GetPortStats = channel.unary_unary(
                '/forward.Forward/GetPortStats',
                request_serializer=forward__pb2.PortStatsRequest.SerializeToString,
                response_deserializer=forward__pb2.PortStatsReply.FromString,
                )
        self.ClearPortStats = channel.unary_unary(
                '/forward.Forward/ClearPortStats',
                request_serializer=forward__pb2.ClearPortStatsRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.GetDebug = channel.unary_unary(
                '/forward.Forward/GetDebug',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=forward__pb2.GetDebugReply.FromString,
                )
        self.SetDebug = channel.unary_unary(
                '/forward.Forward/SetDebug',
                request_serializer=forward__pb2.SetDebugRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.Acl4 = channel.stream_unary(
                '/forward.Forward/Acl4',
                request_serializer=forward__pb2.Acl4Request.SerializeToString,
                response_deserializer=forward__pb2.Acl4Reply.FromString,
                )


class ForwardServicer(object):
    """The forward service definition.
    """

    def GetStats(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearStats(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPortStats(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearPortStats(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDebug(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetDebug(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Acl4(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ForwardServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetStats': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStats,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=forward__pb2.StatsReply.SerializeToString,
            ),
            'ClearStats': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearStats,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'GetPortStats': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPortStats,
                    request_deserializer=forward__pb2.PortStatsRequest.FromString,
                    response_serializer=forward__pb2.PortStatsReply.SerializeToString,
            ),
            'ClearPortStats': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearPortStats,
                    request_deserializer=forward__pb2.ClearPortStatsRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'GetDebug': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDebug,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=forward__pb2.GetDebugReply.SerializeToString,
            ),
            'SetDebug': grpc.unary_unary_rpc_method_handler(
                    servicer.SetDebug,
                    request_deserializer=forward__pb2.SetDebugRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'Acl4': grpc.stream_unary_rpc_method_handler(
                    servicer.Acl4,
                    request_deserializer=forward__pb2.Acl4Request.FromString,
                    response_serializer=forward__pb2.Acl4Reply.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'forward.Forward', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Forward(object):
    """The forward service definition.
    """

    @staticmethod
    def GetStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/forward.Forward/GetStats',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            forward__pb2.StatsReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClearStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/forward.Forward/ClearStats',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPortStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/forward.Forward/GetPortStats',
            forward__pb2.PortStatsRequest.SerializeToString,
            forward__pb2.PortStatsReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClearPortStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/forward.Forward/ClearPortStats',
            forward__pb2.ClearPortStatsRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDebug(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/forward.Forward/GetDebug',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            forward__pb2.GetDebugReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetDebug(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/forward.Forward/SetDebug',
            forward__pb2.SetDebugRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Acl4(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(request_iterator, target, '/forward.Forward/Acl4',
            forward__pb2.Acl4Request.SerializeToString,
            forward__pb2.Acl4Reply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
