import React, { useEffect, useState } from 'react';
import cs from 'classnames';
import { DashCard } from '../DashCard';
import { Button, Radio, RadioChangeEvent, Select, Tag } from 'antd';
import ThreatGridTable from './ThreatGridTable';

import { useConstantOptions, useThreatData } from '../hooks';
import { Props } from './HighRiskHostCard';
import { SECURITY_COLORS } from '../constant';
import WeekTrendsModal from '../Modal/WeekTrendsModal';

import style from './grid-table.less'
import ThreatDetailModal from '../Modal/ThreatDetailModal';

function AttackerCard({ timeRange, onStaticData }: Props): JSX.Element {
  const { disposeOptions, typeOptions, serverirtyMap } = useConstantOptions()

  const [showWeekTrends, setShow] = useState(false);
  const [showDetail, setShowDetail] = useState(false);

  const {
    loading,
    list,
    currentThreat,
    setCurrentThreat,
    updateQueryParams,
    updateTimeRange,
    updateThreat,
    total,
    page,
    setPage
  } = useThreatData(
    {
    sort_agg_field: 'attacker',
    status: '1',
    all_host: 1,
    ...timeRange
    },
    onStaticData
  )

  useEffect(() => {
    updateTimeRange(timeRange)
  }, [timeRange])

  const titleRender = () => {
    return (
      <div className='flex justify-between items-center flex-1 pr-6'>
        <span>攻击者
          <Button className='!c-blue' type='text' onClick={() => setShow(true)}>本周趋势</Button>
        </span>
        <Radio.Group
          size='small'
          optionType='button'
          defaultValue="1"
          onChange={handleTypeChange}
          options={typeOptions}
        />
        <div>
          处置状态：
          <Select
            size='small'
            defaultValue="1"
            style={{ width: 120 }}
            onChange={handleStatusChange}
            options={disposeOptions}
          />
        </div>
      </div>
    )
  }

  const handleTypeChange = (v: RadioChangeEvent) => {
    const { value } = v.target
    updateQueryParams({
      all_host: value
    })
  }

  const handleStatusChange = (value: string) => {
    updateQueryParams({
      status: value
    })
  }

  const handleThreatUpdate = (ip: string, isDispose: boolean) => {
    updateThreat({ type: 'attacker', ip_addr: ip, action: isDispose ? 'restore' : 'process' })
  }

  const handleDetail = (ip: string, suggestion: string) => {
    setCurrentThreat(() => {
      setShowDetail(true);
      return { ip, suggestion }
    })
  }

  const itemRender = (record: any) => {

    const { ip, flags_lst, is_process_lst, severity_lst, threat_desc_lst } = record;

    // key_as_string 都是 true ，就是已处置的状态
    const isDispose = is_process_lst?.every(({ key_as_string }: any) => key_as_string === 'true')

    const isAssets = flags_lst?.some(({ key }: any) => key === 'assets');

    const severityKeys = severity_lst?.map(({ key }: any) => key);

    const serverirtyTags = severityKeys?.slice(-1)?.map((key: string) => ({
      name: serverirtyMap[key],
      color: SECURITY_COLORS[key],
      key,
    }))

    return (
      <div className={cs('flex flex-col', style.table_item)}>
        <div className='flex flex-col p-3 gap-2 flex-1 justify-around'>
          <div className='flex'>
            <span className='mr-2'>{ip || 'unknown'}</span>
            <Tag color="blue">{!isAssets && '非'}资产</Tag>
          </div>
          <div className={cs('flex flex-wrap items-center gap-1', style.item_tags)}>
            {
              serverirtyTags?.map(({ name, color }: any) =>
                <Tag className='font-500' key={name} color={color}>{name}</Tag>)
            }
            {
              threat_desc_lst?.map(({ key }: any) =>
                <Tag className='font-500' key={key} color='#f50'>{key}</Tag>)
            }
          </div>
        </div>
        <div className={cs('flex justify-evenly items-center', style.item_option)}>
          <Button
            className='!c-blue'
            type='text'
            onClick={() => handleDetail(ip, threat_desc_lst?.[0]?.key || '')}
          >
            详情
          </Button>          <Button
            onClick={() => handleThreatUpdate(ip, isDispose)}
            className='!c-blue'
            type='text'
          >
            {isDispose && '已'}处置
          </Button>
        </div>
      </div>
    )
  }

  return (
    <DashCard title={titleRender()}>
      <ThreatGridTable
        page={page}
        loading={loading}
        total={total}
        className='flex-1'
        data={list}
        itemRender={itemRender}
        onPageChange={setPage}
      />
      {
        showWeekTrends &&
        <WeekTrendsModal
          show={showWeekTrends}
          trendType='attacker'
          onClose={() => setShow(false)}
        />
      } 
      <ThreatDetailModal
        show={showDetail}
        currentThreat={currentThreat}
        onClose={() => setShowDetail(false)}
        timeRange={timeRange}
        type="attacker"
      />
    </DashCard>
  );
}

export default AttackerCard;
