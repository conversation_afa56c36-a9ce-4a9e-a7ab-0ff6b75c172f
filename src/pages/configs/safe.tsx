/*
 * @Author: 田浩
 * @Date: 2021-08-18 11:52:45
 * @LastEditors: tianh
 * @LastEditTime: 2022-03-30 15:27:02
 * @Descripttion:
 */
import React, { useState, useEffect } from 'react';
import { Button, Table, Modal, Form, Input, message } from 'antd';
import {
  whiteList,
  editWhite,
  addWthite,
  deleteWhite,
} from '@/services/configs';
import style from './style.less';
const Safe = (props: any) => {
  const [form] = Form.useForm();
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>(
    'checkbox',
  );
  const [searchValue, setsearchValue] = useState('');
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const [tableData, settableData] = useState([]);
  const [formValue, setformValue] = useState<any>({
    target: '',
    tag: '',
  });
  const [visible, setvisible] = useState(false);
  const [isAdd, setisAdd] = useState(false); //判断是添加还是编辑
  const rowSelection = {
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys,
  };
  const getSearch = (e: any) => {
    setsearchValue(e.target.value);
  };
  const getList = () => {
    whiteList({ target: searchValue }).then(res => {
      if (res.flag) {
        setselectedRowKeys([]);
        settableData(res.data);
      }
    });
  };
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      // 添加时
      if (isAdd) {
        addWthite(value).then(res => {
          if (res.flag) {
            setformValue({
              target: '',
              tag: '',
            });
            message.success(res.message);
            setvisible(false);
            getList();
          } else {
            message.error(res.message);
          }
        });
      } else {
        // 编辑时
        editWhite(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            setvisible(false);
            getList();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };
  /**
   * @name:
   * @description: 单选删除
   * @param {*}
   * @return {*}
   */

  const handleDelete = (value: any) => {
    deleteWhite({ target_list: [value] }).then(res => {
      if (res.flag) {
        message.success(res.message);
        setsearchValue('');
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  /**
   * @name:
   * @description: 编辑
   * @param {*
   * value 从表格中获取到每行的对象
   * }
   * @return {*}
   */

  const handleEdit = (value: any) => {
    setformValue(value);
    setisAdd(false);
    setvisible(true);
  };
  /**
   * @name:
   * @description: 批量删除
   * @param {*}
   * @return {*}
   */

  const seleteDelete = () => {
    deleteWhite({ target_list: selectedRowKeys }).then(res => {
      if (res.flag) {
        message.success(res.message);
        setsearchValue('');
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  useEffect(() => {
    getList();
  }, []);
  const columns = [
    {
      title: '证书序列号',
      dataIndex: 'target',
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
    },
    {
      title: '备注',
      dataIndex: 'tag',
    },
    {
      title: '操作',
      dataIndex: '',
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              type="link"
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                handleDelete(record.target);
              }}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
  return (
    <div>
      <div className={style.top}>
        <span>
          自签名证书白名单：
          <Input style={{ width: 168, marginRight: 10 }} onChange={getSearch} />
          <Button type="primary" onClick={getList}>
            查询
          </Button>
          <Button
            className={style.btn}
            type="primary"
            onClick={() => {
              setisAdd(true);
              setformValue({
                tag: '',
                target: '',
              });
              setvisible(true);
            }}
          >
            添加
          </Button>
          <Button type="primary" onClick={seleteDelete}>
            删除
          </Button>
        </span>
      </div>
      <Table
        pagination={false}
        rowKey={(r, i) => r.target}
        dataSource={tableData}
        columns={columns}
        rowSelection={{
          type: selectionType,
          ...rowSelection,
        }}
      />
      <Modal
        onCancel={() => {
          setformValue({
            target: '',
            tag: '',
          });
          setvisible(false);
        }}
        destroyOnClose={true}
        onOk={onSubmit}
        title={isAdd ? '添加' : '编辑'}
        visible={visible}
      >
        <Form form={form} initialValues={formValue} onFinish={onSubmit}>
          <Form.Item
            label="证书序列号"
            name="target"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="备注"
            name="tag"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Safe;
