{
    "type": "object",
    "required": [],
    "properties": {
        "start_time": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stop_time": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "name": {
            "type": "string",
            "default": ""
        },
        "fingerprint": {
            "type": "string",
            "default": ""
        },
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}