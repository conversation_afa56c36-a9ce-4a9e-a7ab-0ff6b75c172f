import { presetUno } from 'unocss';

const theme = {
  fontSize: {
    xs: ['0.725rem', '0.75rem'],
    sm: ['0.75rem', '1rem'],
    base: ['0.875rem', '1.25rem'],
    md: ['0.875rem', '1.25rem'],
    lg: ['1rem', '1.5rem'],
    xl: ['1.125rem', '1.75rem'],
    '2xl': ['1.25rem', '1.75rem'],
    '3xl': ['1.5rem', '2rem'],
    '4xl': ['1.875rem', '2.25rem'],
    '5xl': ['2.25rem', '2.5rem'],
    '6xl': ['2.5rem', '2.75rem'],
    '7xl': ['3rem', '1'],
    '8xl': ['3.75rem', '1'],
    '9xl': ['4.5rem', '1'],
    '10xl': ['6rem', '1'],
  },
  colors: {
    u: { // uno
      100: '#ecf6ff', // SelectedBackGround
      200: '#d1defe',
      300: '#add3ff', // Seconday
      400: '#19A5FF',
      500: '#0b6fd6', // Primary
      600: '#105dc6', // Hover
      700: '#0d45a7', // Click
      800: '#313645',
      900: '#20232D',
    },
    d: { // dos
      0: '#ffffff', // Background1
      100: '#f7f9fc', // Background2
      150: '#ecf6ff', // Background3
      200: '#E6EAEF', // Border1
      250: '#cbd1da', // Silent
      300: '#b2b2b2',
      400: '#999ea8', // TextSecondary, Border2
      500: '#807878',
      600: '#686c78', // Text, Border3
      700: '#4e566b', // Border4
      800: '#2a2b31', // TextLevelTitle
      900: '#1e1f23', // TextTitle
    },
    success: '#009933',
    notice: '#696C78',
    warn: {
      DEFAULT: '#FAAF2C',
      4: '#FFE58F',
      5: '#FAAF2C',
    },
    error: '#ff0000'
  },
  animation: {
    keyframes: {
      spin: '{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}',
    },
    timingFns: {
      spin: 'linear',
    },
    durations: {
      spin: '1s',
    },
  },
  breakpoints: {
    'sm': '640px',
    'md': '768px',
    'lg': '1024px',
    'xl': '1280px',
    '2xl': '1600px',
  },
};
theme.colors.t = {
  0: theme.colors.d[0],
  3: theme.colors.d[200],
  4: theme.colors.d[400],
  5: theme.colors.d[700],
  6: theme.colors.d[800],
  7: theme.colors.d[900],
};
theme.colors.brd = {
  4: "#fafafa",
  5: "#F0F2F5",
  6: "#E6EAEF",
};

export default {
  theme,
  presets: [
    presetUno(),
  ],
  shortcuts: [
    {
      tag: 'inline-block h-6 lh-6 px-2 bg-d100 b b-solid b-brd6 rd-1 text-xs c-t5 truncate',
      'tag-notice':
        'inline-block h-6 lh-6 px-2 bg-notice bg-op-4 b b-solid rd-1 text-xs truncate c-notice b-notice',
      'tag-warn':
        'inline-block h-6 lh-6 px-2 bg-warn bg-op-4 b b-solid rd-1 text-xs truncate c-warn b-warn',
      'tag-error':
        'inline-block h-6 lh-6 px-2 bg-error bg-op-4 b b-solid rd-1 text-xs truncate c-error b-error',
      'tag-success':
        'inline-block h-6 lh-6 px-2 bg-success bg-op-4 b b-solid rd-1 text-xs truncate c-success b-success',
    },
  ],
  rules: [],
  safelist: [],
};
