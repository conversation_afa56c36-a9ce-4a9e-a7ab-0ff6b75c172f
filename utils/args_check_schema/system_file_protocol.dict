{
	"type": "object",
	"required": ["file_protocol"],
	"properties": {
		"file_protocol": {
		    "type": "object",
			"properties": {
                "HTTP": {
                    "type": "number",
                    "default": 1,
                    "enum": [1, 0]
                },
                "FTP": {
                    "type": "number",
                    "default": 1,
                    "enum": [1, 0]
                },
                "SMB": {
                    "type": "number",
                    "default": 1,
                    "enum": [1, 0]
                },
                "NFS": {
                    "type": "number",
                    "default": 0,
                    "enum": [1, 0]
                },
                "SMTP": {
                    "type": "number",
                    "default": 1,
                    "enum": [1, 0]
                },
                "POP3": {
                    "type": "number",
                    "default": 1,
                    "enum": [1, 0]
                },
                "IMAP": {
                    "type": "number",
                    "default": 1,
                    "enum": [1, 0]
                },
                "TFTP": {
                    "type": "number",
                    "default": 0,
                    "enum": [1, 0]
                },
            }
		}
	}
}