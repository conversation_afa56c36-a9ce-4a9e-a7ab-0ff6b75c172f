#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: test_eventListResource.py
# @time: 2019/07/24
# @software: PyCharm
import json
import random
from unittest import TestCase, TestSuite, TextTestRunner
from api_1_0.router import app, api
from api_1_0.back_event.get_event_list import EventListResource


class TestEventListResource(TestCase):
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_get_query_random_one_key(self):
        # rip = lambda: '.'.join(
        #     [str(int(''.join([str(random.randint(0, 2)), str(random.randint(0, 5)), str(random.randint(0, 5))]))) for _
        #      in range(4)])
        threatLevel = ["High", "Medium", "Low"]
        killchains = ["Recon", "Weaponization", "Delivery", "Exploitation", "Beacon", "CnC", "Actions on Objective"]
        country = ["Australia", "India", "Venezuela", "Switzerland", "Vietnam", "Turkey", "Israel",
                   "Republic of Moldova"]
        threatFlag = ["Exploits and Attacks", "DoS", "Malware", "Scanning", "Botnet", "Phishing", "Suspicious",
                      "Malicious host", "APT"]
        options_args = [{"ip": rip()}, {"threatLevel": random.choice(threatLevel)},
                        {"killchains": random.choice(killchains)}, {"country": random.choice(country)},
                        {"threatFlag": random.choice(threatFlag)}, {"attackIp": rip()}, {"victimIp": rip()}]
        startTime = 1504527700099
        stopTime = 1578430872367
        response = {}
        try:

            for item in options_args:
                for key, value in item.items():
                    response = self.client.get(
                        api.url_for(EventListResource, startTime=startTime, stopTime=stopTime, key=value))
                resp_dict = json.loads(response.data)
                self.assertEqual(resp_dict["flag"], True)

        except Exception as e:
            print(e)

    def test_get_query_random_mul_key(self):

        threatLevel = ["High", "Medium", "Low"]
        killchains = ["Recon", "Weaponization", "Delivery", "Exploitation", "Beacon", "CnC", "Actions on Objective"]
        country = ["Australia", "India", "Venezuela", "Switzerland", "Vietnam", "Turkey", "Israel",
                   "Republic of Moldova"]
        threatFlag = ["Exploits and Attacks", "DoS", "Malware", "Scanning", "Botnet", "Phishing", "Suspicious",
                      "Malicious host", "APT"]

        startTime = 1504527700099
        stopTime = 1578430872367
        try:

            response = self.client.get(api.url_for(EventListResource, startTime=startTime, stopTime=stopTime,
                                                   threatLevel=random.choice(threatLevel),
                                                   country=random.choice(country),
                                                   threatFlag=random.choice(threatFlag)))
        except Exception as e:
            print(e)
        else:
            resp_dict = json.loads(response.data)
            self.assertEqual(resp_dict["flag"], True)


def suite():
    suite = TestSuite()
    suite.addTest(TestEventGroupByResource('test_default_widget_size'))
    suite.addTest(TestEventGroupByResource('test_widget_resize'))
    return suite


if __name__ == '__main__':
    runner = TextTestRunner()
    runner.run(suite())
