import React, { useState, useEffect } from 'react';
import { Tabs } from 'antd';
import Administration from './administration/index';
import Task from './task/index';
import { getQueryVariable } from '@/utils/utils';
const { TabPane } = Tabs;
const Index = () => {
  const [activeKey, setactiveKey] = useState('1');
  const onChange = (value: any) => {
    setactiveKey(value);
  };
  useEffect(() => {
    if (getQueryVariable('goback')) {
      setactiveKey('2');
    }
  }, []);
  return (
    <div>
      <Tabs activeKey={activeKey} onChange={onChange}>
        <TabPane tab="任务" key="1">
          {activeKey === '1' ? <Task /> : null}
        </TabPane>
        <TabPane tab="数据源管理" key="2">
          {activeKey === '2' ? <Administration /> : null}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Index;
