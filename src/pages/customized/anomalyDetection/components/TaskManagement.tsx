import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Popconfirm,
  message,
  Modal,
  Descriptions,
  Typography,
  Input,
  Select,
  DatePicker,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { getAnomalyTasks, deleteAnomalyTask, updateAnomalyTask, AnomalyTask } from '@/services/anomaly';
import EditTaskModal from './EditTaskModal';
import CreateTaskModal from './CreateTaskModal';
import styles from '../style.less';

const { Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface TaskManagementProps {
  onStatsChange: () => void;
}

const TaskManagement: React.FC<TaskManagementProps> = ({ onStatsChange }) => {
  const [tasks, setTasks] = useState<AnomalyTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    status: '',
    model_type: '',
    task_type: '',
    search: '',
  });
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<AnomalyTask | null>(null);

  // 获取任务列表
  const fetchTasks = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = {
        page,
        page_size: pageSize,
        ...filters,
      };
      const response = await getAnomalyTasks(params);
      if (response.flag) {
        setTasks(response.data.tasks);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   fetchTasks();
  // }, [filters]);

  // 删除任务
  const handleDelete = async (taskId: string) => {
    try {
      const response = await deleteAnomalyTask(taskId);
      if (response.flag) {
        message.success('删除成功');
        fetchTasks();
        onStatsChange();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 启用/禁用任务
  const handleToggleStatus = async (task: AnomalyTask) => {
    try {
      const newStatus = task.status === 'active' ? 'inactive' : 'active';
      const response = await updateAnomalyTask(task.task_id, { status: newStatus });
      if (response.flag) {
        message.success(`任务已${newStatus === 'active' ? '启用' : '禁用'}`);
        fetchTasks();
        onStatsChange();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 查看任务详情
  const handleViewDetail = (task: AnomalyTask) => {
    setSelectedTask(task);
    setDetailModalVisible(true);
  };

  // 创建任务
  const handleCreate = () => {
    setCreateModalVisible(true);
  };

  // 创建成功回调
  const handleCreateSuccess = () => {
    setCreateModalVisible(false);
    fetchTasks();
    onStatsChange();
    message.success('任务创建成功');
  };

  // 编辑任务
  const handleEdit = (task: AnomalyTask) => {
    setSelectedTask(task);
    setEditModalVisible(true);
  };

  // 编辑成功回调
  const handleEditSuccess = () => {
    setEditModalVisible(false);
    setSelectedTask(null);
    fetchTasks();
    onStatsChange();
  };

  // 获取模型类型标签颜色
  const getModelTypeColor = (modelType: string) => {
    const colors = {
      time_series: 'blue',
      baseline: 'green',
      repeat_conn: 'orange',
      long_conn: 'purple',
      geo_anomaly: 'red',
    };
    return colors[modelType] || 'default';
  };

  // 获取模型类型名称
  const getModelTypeName = (modelType: string) => {
    const names = {
      time_series: '时间序列趋势',
      baseline: '基线偏移检测',
      repeat_conn: '重复连接检测',
      long_conn: '长连接分析',
      geo_anomaly: '地理/归属异常',
    };
    return names[modelType] || modelType;
  };

  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: AnomalyTask) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.description}
          </Text>
        </div>
      ),
    },
    {
      title: '检测模型',
      dataIndex: 'model_types',
      key: 'model_types',
      render: (modelTypes: string[]) => (
        <div>
          {modelTypes?.map((modelType, index) => (
            <Tag key={index} color={getModelTypeColor(modelType)} style={{ marginBottom: 4 }}>
              {getModelTypeName(modelType)}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '检测目标',
      key: 'target',
      render: (record: AnomalyTask) => (
        <div>
          <Tag color="cyan">{record.target_type.toUpperCase()}</Tag>
          <br />
          <Text code style={{ fontSize: 12 }}>
            {record.target_value}
          </Text>
        </div>
      ),
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (taskType: string) => (
        <Tag color={taskType === 'historical' ? 'orange' : 'blue'}>
          {taskType === 'historical' ? '历史回溯' : '定时检测'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '运行中' : '已停止'}
        </Tag>
      ),
    },
    {
      title: '执行次数',
      dataIndex: 'run_count',
      key: 'run_count',
    },
    {
      title: '最后执行',
      dataIndex: 'last_run_time',
      key: 'last_run_time',
      render: (time: number) =>
        time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '未执行',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (time: number) => moment(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: AnomalyTask) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={() => handleToggleStatus(record)}
          >
            {record.status === 'active' ? '停止' : '启动'}
          </Button>
          <Popconfirm
            title="确定要删除这个任务吗？"
            onConfirm={() => handleDelete(record.task_id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 过滤器 */}
      <Card className={styles.filterBar}>
        <Space wrap>
          <Input
            placeholder="搜索任务名称"
            prefix={<SearchOutlined />}
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            style={{ width: 200 }}
          />
          <Select
            placeholder="选择状态"
            value={filters.status}
            onChange={(value) => setFilters({ ...filters, status: value })}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="active">运行中</Option>
            <Option value="inactive">已停止</Option>
          </Select>
          <Select
            placeholder="选择模型类型"
            value={filters.model_type}
            onChange={(value) => setFilters({ ...filters, model_type: value })}
            style={{ width: 150 }}
            allowClear
          >
            <Option value="time_series">时间序列趋势</Option>
            <Option value="baseline">基线偏移检测</Option>
            <Option value="repeat_conn">重复连接检测</Option>
            <Option value="long_conn">长连接分析</Option>
            <Option value="geo_anomaly">地理/归属异常</Option>
          </Select>
          <Select
            placeholder="任务类型"
            value={filters.task_type}
            onChange={(value) => setFilters({ ...filters, task_type: value })}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="historical">历史回溯</Option>
            <Option value="scheduled">定时检测</Option>
          </Select>
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          创建检测任务
        </Button>
      </Card>

      {/* 任务列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="task_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: fetchTasks,
          }}
        />
      </Card>

      {/* 任务详情弹窗 */}
      <Modal
        title="任务详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTask && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="任务名称" span={2}>
              {selectedTask.name}
            </Descriptions.Item>
            <Descriptions.Item label="描述" span={2}>
              {selectedTask.description}
            </Descriptions.Item>
            <Descriptions.Item label="检测模型" span={2}>
              {selectedTask.model_types?.map((modelType, index) => (
                <Tag key={index} color={getModelTypeColor(modelType)} style={{ marginBottom: 4 }}>
                  {getModelTypeName(modelType)}
                </Tag>
              ))}
            </Descriptions.Item>
            <Descriptions.Item label="检测目标">
              <Tag color="cyan">{selectedTask.target_type.toUpperCase()}</Tag>
              <br />
              <Text code>{selectedTask.target_value}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="任务类型">
              <Tag color={selectedTask.task_type === 'historical' ? 'orange' : 'blue'}>
                {selectedTask.task_type === 'historical' ? '历史回溯' : '定时检测'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={selectedTask.status === 'active' ? 'success' : 'default'}>
                {selectedTask.status === 'active' ? '运行中' : '已停止'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="执行次数">
              {selectedTask.run_count}
            </Descriptions.Item>
            <Descriptions.Item label="最后执行">
              {selectedTask.last_run_time
                ? moment(selectedTask.last_run_time).format('YYYY-MM-DD HH:mm:ss')
                : '未执行'}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {moment(selectedTask.create_time).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="创建者">
              {selectedTask.creator}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* 创建任务弹窗 */}
      <CreateTaskModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={handleCreateSuccess}
      />

      {/* 编辑任务弹窗 */}
      <EditTaskModal
        visible={editModalVisible}
        task={selectedTask}
        onCancel={() => setEditModalVisible(false)}
        onSuccess={handleEditSuccess}
      />
    </div>
  );
};

export default TaskManagement;
