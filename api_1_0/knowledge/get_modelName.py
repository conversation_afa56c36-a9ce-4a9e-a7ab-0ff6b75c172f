#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from flask_restful import Resource
from utils.utils import flask_response
from utils.es_template.get_es_template import ES_Template
from utils.database import get_es_client
from config.config import MODEL_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class GetModelName(Resource):
    '''获取模型名称'''

    def __init__(self):
        self.es_template = ES_Template().read_template("knowledge_model_name")

    def get(self):
        # 连接ES查询
        es_client = get_es_client()
        try:
            res = es_client.search(index=MODEL_INDEX, body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    @staticmethod
    def data_format(es_data):
        data_return = []
        model_data_list = es_data["aggregations"]["groupBy"]["buckets"]
        for model_info in model_data_list:
            each_model_dict = {
                "modelName": model_info["key"] if model_info["key"] else "未知"
            }
            data_return.append(each_model_dict)

        return data_return
