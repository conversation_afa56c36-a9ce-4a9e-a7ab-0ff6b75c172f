# -*- coding: utf-8 -*-
# @Time    : 2019-05-29 18:40
# <AUTHOR> Shark
# @File    : api_get_killchains_loop.py
# @Software: PyCharm

import time
from flask import request
from flask_restful import Resource
from utils import param_check, database
from utils.param_check import Validator
from utils.utils import flask_response
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class EventKillchainsSearch(Resource):
    """根据一项或多项参数查询Killchains闭环"""

    def __init__(self):
        """初始化参数"""

    @param_check.check_flask_args(Validator("killchains_ip_schema"), request)
    def get(self, **kwargs):
        """根据参数组装ES查询语句，送至ES查询并将结果组装返回"""

        # 参数赋值并做格式转换
        args = {
            "ip": kwargs["args"]["ip"],
            "page": int(kwargs["args"]["page"]),
            "page_size": int(kwargs["args"]["pageSize"]),
            "start_time": int(kwargs["args"]["startTime"]),
            "stop_time": int(kwargs["args"]["stopTime"]),
            "attack_ip": kwargs["args"]["attackIp"],
            "victim_ip": kwargs["args"]["victimIp"],
            "count": int(kwargs["args"]["count"])
        }

        if args["start_time"] >= args["stop_time"]:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})

        # query 查询模块
        es_template = get_es_query_code(args)

        # 添加聚合语句
        es_template["aggs"] = get_aggs_killchains_loop(args)

        # 最外层size设置为0，不展示搜索结果，只展示聚合结果
        es_template["size"] = 0

        print(es_template)

        # 连接ES查询
        es_client = database.get_es_client()
        try:
            res = es_client.search(index="rule-eve", body=es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, True, {})
        try:
            data = data_format(res, args["count"])
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})


def data_format(data, count):
    """将es输出结果格式化成前端结构"""
    format_data_list = []
    for vul_data in data["aggregations"]["group_by_IP"]["buckets"]:
        format_data = {
            "GroupIP": vul_data["key"],
            "triggerCount": vul_data["doc_count"],
            "killchainsList": []
        }
        for event_data in vul_data["killchains"]["buckets"]:
            if count > 0:
                event_list = [event["_source"] for event in event_data["top_event_hits"]["hits"]["hits"]]
            else:
                event_list = []
            event = {
                "killchains": event_data["key"],
                "triggerCount": event_data["doc_count"],
                "endTime": event_data["max_time"]["value_as_string"],
                "startTime": event_data["min_time"]["value_as_string"],
                "eventList": event_list
            }
            format_data["killchainsList"].append(event)
        format_data_list.append(format_data)
    return format_data_list


def get_es_query_code(args):
    """填充es查询语句的query部分"""
    query_code = {
        "query": {
            "bool": {
                "must": []
            }
        }
    }
    start_time = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime(args["start_time"]))
    stop_time = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime(args["stop_time"]))
    query_time_range = {
        "range": {
            "@timestamp": {
                "gt": start_time,
                "lt": stop_time
            }
        }
    }
    query_code["query"]["bool"]["must"].append(query_time_range)

    # 通过term精确匹配
    es_term_list = {
        "attack_ip": "rulesInfo.attackIp",
        "victim_ip": "rulesInfo.victimIp"
    }

    for es_arg, field in es_term_list.items():
        if args[es_arg]:
            query = {
                "term": {
                    field: args[es_arg]
                }
            }
            query_code["query"]["bool"]["must"].append(query)

    # 查询IP，不区分攻击者IP、被攻击IP
    if args["ip"]:
        query = {
            "multi_match": {
                "query": args["ip"],
                "fields": [
                    "rulesInfo.attackIp",
                    "rulesInfo.victimIp"
                ]
            }
        }
        query_code["query"]["bool"]["must"].append(query)
    return query_code


def get_aggs_killchains_loop(args):
    """获取聚合语句"""

    # 添加ES查询分页参数
    size_data = args["page_size"]
    from_data = (args["page"] - 1) * args["page_size"]

    killchains_loop_aggs = {
        "group_by_IP": {
            "terms": {
                "script": "doc['rulesInfo.attackIp.keyword'].values +'-'+ doc['rulesInfo.victimIp.keyword'].values",
                "size": 2147483647
            },
            "aggs": {
                "killchains": {
                    "terms": {
                        "field": "rulesInfo.lockheedKillchainEN.keyword"
                    },
                    "aggs": {
                        "max_time": {
                            "max": {
                                "field": "@timestamp"
                            }
                        },
                        "min_time": {
                            "min": {
                                "field": "@timestamp"
                            }
                        }
                    }
                },
                "killchains_count": {
                    "cardinality": {
                        "field": "rulesInfo.lockheedKillchainEN.keyword"
                    }
                },
                "killchains_count_filter": {
                    "bucket_selector": {
                        "buckets_path": {
                            "killchains_count": "killchains_count"
                        },
                        "script": "params.killchains_count>1"
                    }
                },
                "sales_bucket_sort": {
                    "bucket_sort": {
                        "sort": [
                            {
                                "killchains_count": {
                                    "order": "desc"
                                }
                            }
                        ],
                        "from": from_data,
                        "size": size_data,
                    }
                }
            }
        }
    }

    if args["count"] > 0:
        killchains_loop_aggs["group_by_IP"]["aggs"]["killchains"]["aggs"]["top_event_hits"] = get_aggs_top_size(
            args["count"])

    return killchains_loop_aggs


def get_aggs_top_size(count):
    """根据传入的count组合top list查询语句"""
    query = {
        "top_hits": {
            "size": count,
            "_source": {
                "includes": [
                    "src_ip",
                    "src_port",
                    "dest_ip",
                    "dest_port",
                    "flow_id",
                    "payload",
                    "flow",
                    "alert",
                    "rulesInfo"
                ]
            }
        }
    }
    return query
