{
	"type": "object",
	"required": ["threat_gw"],
	"properties": {
		"policy_name": {
			"type": "string",
			"default": ""
		},
		"state": {
			"type": "string",
			"default": "enable",
			"enum": ["enable", "disable"]
		},
		"source_if_list": {
			"type": "array",
			"items": {
                "type": "string",
                "default": ""
              },
            "default": []
		},
		"source_host_ip": {
            "type": "array",
			"items": {
                "type": "string",
                "format": "ipv4|ipv6",
                "default": ""
              },
            "default": []
        },
		"rule_groups": {
			"type": "array",
			"items": {
                "type": "string",
                "default": ""
              },
            "default": []
		},
		"ioc_enable": {
			"type": "boolean",
			"default": False
		},
		"model_enable": {
			"type": "boolean",
			"default": False
		},
		"file_enable": {
			"type": "boolean",
			"default": False
		},
		"threat_log": {
			"type": "boolean",
			"default": True
		},
		 "email": {
            "type": "object",
            "properties": {
                "is_enable": {
                    "type": "boolean",
			        "default": False
                },
                "log_trigger_ip": {
                    "type": "array",
			         "items": {
                          "type": "string",
                          "format": "ipv4|ipv6",
                          "default": ""
                     },
                     "default": []
                },
                "mail_server": {
                    "type": "string",
                    "default": ""
                },
                "server_port": {
                    "type": "number",
                    "default": 25
                },
                "account": {
                    "type": "string",
                    "default": ""
                },
                "password": {
                    "type": "string",
                    "default": ""
                },
                "receiver": {
                    "type": "string",
                    "default": ""
                }
            }
        },
        "syslog": {
            "type": "object",
            "properties": {
                "is_enable": {
                    "type": "boolean",
			        "default": False
                },
                "log_trigger_ip": {
                    "type": "array",
			         "items": {
                          "type": "string",
                          "format": "ipv4|ipv6",
                          "default": ""
                     },
                     "default": []
                },
                "syslog_server": {
                    "type": "string",
                    "default": ""
                },
                "server_port": {
                    "type": "number",
                    "default": 514
                }
            }
        },
        "threat_gw": {
            "type": "object",
            "properties": {
                "is_enable": {
                    "type": "boolean",
			        "default": False
                },
                "server": {
                    "type": "string",
                    "default": ""
                },
                "token": {
                    "type": "string",
                    "default": ""
                }
            }
        }
	}
}