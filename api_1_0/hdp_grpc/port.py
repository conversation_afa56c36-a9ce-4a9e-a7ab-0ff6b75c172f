#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import grpc
# from api_1_0.hdp_grpc.die import grpc_feature_upgrade, grpc_feature_add, grpc_feature_operate
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from config.config import NdrLog, GrpcCfg

import protos_pb.port_pb2 as port_pb2
import protos_pb.port_pb2_grpc as port_pb2_grpc

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


# Grpc API #
def grpc_get_port_links():
    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = port_pb2_grpc.PortServiceStub(channel)
        response = stub.GetPortLinks(port_pb2.PortLinkRequest())
        return response


# Rest API #
# class GrpcPort(Resource):
#     def get(self, **kwargs):
#         response = grpc_get_port_links()
#
#         for status in response.status_list:
#             LOG.info('Received: >> %d  %d' % (status.port_id, status.link_status))
#         x = response.status_list[0]
#         # LOG.info('Received: >> ', x.port_id, x.link_status)
#         # response = grpc_feature_upgrade('/opt/hdp/config/ips-sigpack-en.dat')
#
#         # response = grpc_feature_add('/opt/hdp/1554767167707760156_detect.bin',
#         #                             '/opt/hdp/1554767167707760156_describe.bin')
#
#         # response = grpc_feature_operate([3])
#         return flask_response('', True, {})

