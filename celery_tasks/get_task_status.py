# -*- coding: utf-8 -*-
# @Time    : 2019-05-05 14:10
# <AUTHOR> hachi
# @File    : api_rules_manager.py
# @Software: PyCharm


from celery_tasks import app
from utils.database import BaseMongoDB


@app.task()
def get_activate_task_status():
    """
    更新定时生效规则的 celery 任务状态
    :return:
    """
    db = BaseMongoDB("ndr")
    for task in db.find("rules_activation", {}, {"_id": 0, "taskId": 1}):
        task_status = app.AsyncResult(task["taskId"]).state
        db.update("rules_activation", {"taskId": task["taskId"]}, {"status": task_status})


@app.task()
def get_explore_task_status():
    """
    更新漏洞探索 celery 任务状态
    :return:
    """
    db = BaseMongoDB("ndr")
    for task in db.find("back_explore", {}, {"_id": 0, "taskId": 1}):
        task_status = app.AsyncResult(task["taskId"]).state
        db.update("back_explore", {"taskId": task["taskId"]}, {"status": task_status})
    pass
