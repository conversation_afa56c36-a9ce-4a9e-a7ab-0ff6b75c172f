{
    "type": "object",
    "required": ["attackIp", "victimIp", "startTime", "stopTime"],
    "properties": {
        "attackIp": {
            "type": "string",
            "format": "ipv4"
        },
        "victimIp": {
            "type": "string",
            "format": "ipv4"
        },
        "startTime": {
            "type": "string",
            "pattern": r"^\d{10}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{10}$"
        },
        "count": {
            "type": "string",
            "default": "0",
            "pattern": r"^([0-9]|[1-9][0-9]|100)$",
            "examples": ["hahah"]
        }
    }
}