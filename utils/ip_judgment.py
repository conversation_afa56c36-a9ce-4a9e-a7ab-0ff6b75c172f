#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from functools import reduce


def ip_into_int(ip):
    # 先把 ************* 用map分割'.'成数组，然后转成10进制
    # (((((192 * 256) + 168) * 256) + 31) * 256) + 46
    return reduce(lambda x, y: (x << 8) + y, map(int, ip.split('.')))


# 方法1：掩码对比
def is_internal_ip(ip_str):
    ip_int = ip_into_int(ip_str)
    net_A = ip_into_int('**************') >> 24
    net_B = ip_into_int('**************') >> 20
    net_C = ip_into_int('***************') >> 16
    net_ISP = ip_into_int('***************') >> 22
    net_DHCP = ip_into_int('***************') >> 16
    return ip_int >> 24 == net_A or ip_int >> 20 == net_B or ip_int >> 16 == net_C or ip_int >> 22 == net_ISP or ip_int >> 16 == net_DHCP
