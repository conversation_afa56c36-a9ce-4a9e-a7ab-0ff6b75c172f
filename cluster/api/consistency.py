#!/usr/bin/env python3
# coding:utf-8

import sys
sys.path.append('/opt/ndr')

from cluster.api.manager import MyClusterManager

MOD_HDP = 'hdp'

default_files = {
    '/opt/feature/db/customized_mb_default.db':'',
    '/opt/feature/db/customized_mb.db':'',
    '/opt/feature/db/ipv4_server.ipdb':'',
    '/opt/feature/db/ipv6_server.ipdb':'',
    '/opt/feature/db/KNDR_IOC_default.db':'',
    '/opt/feature/db/KNDR_IOC.db':'',
    '/opt/feature/db/knowledge_feature.db':'',
    '/opt/feature/db/white_list.db':'',
    '/opt/feature/replay/template.yaml':'',
    '/opt/feature/whitelist_default.feature':'',
    '/opt/feature/whitelist.rule':'',
    '/opt/hdp/config/ips-sigpack-en.dat': MOD_HDP,
    '/opt/hdp/config/white-sigpack-en.dat':MOD_HDP,
    # FIXME: 能否直接覆盖文件conf.ini
    '/opt/hdp/config/conf.ini':MOD_HDP
}

class Consistency(object):
    def __init__(self):
        self.files = default_files
        self.cluster = MyClusterManager()

    def get_files(self, files=None):
        if not files:
            return self.files
        elif isinstance(files, list):
            result = {}
            for f in files:
                result[f] = ''
            return result
        else:
            return {}

    def check_files(self, files=None):
        """ 一致性检查
            返回值：和master节点不一致的数据和节点
            {
                "file1": ["node1_ip", "node2_ip"],
                "file1": ["node1_ip", "node3_ip"],
            }
        """
        master = self.cluster.get_master_ip()
        result = {}
        for file in self.get_files(files):
            result[file] = []
            hashes = self.cluster.file_hash_by_cluster(file)
            master_hash = hashes.get(master)
            if not master_hash:
                continue
            for node_ip, hash_value in hashes.items():
                if hash_value != master_hash:
                    result[file].append(node_ip)

        return result

    def check_and_sync_files(self, files=None):
        """ 一致性检查，如果不一致，则同步成一致
            返回值：无
        """
        mods = set()
        master = self.cluster.get_master_ip()
        for file, mod in self.get_files(files).items():
            hashes = self.cluster.file_hash_by_cluster(file)
            master_hash = hashes[master]
            for node_ip, hash_value in hashes.items():
                if hash_value != master_hash:
                    self.cluster.sync_file_safe_by_node(node_ip, file, file)
                    mods.add(mod)

        return mods

    def check_and_sync(self):
        """ 一致性检查，如果不一致，则同步成一致
            返回值：无
        """
        mods = self.check_and_sync_files()
        for mod in mods:
            if mod == MOD_HDP:
                self.cluster.hdp_restart_by_cluster()

