# @license: Apache Licence
# @file: test_operate.py
# @time: 2019/08/27
# @software: PyCharm
"""
探索模块单元测试用例
"""
import json
import time
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_explore.operate import ExploreTaskWithId, ExploreTask


class TestExploreTaskWithId(TestCase):
    """
    探索模块带 ID 的单元测试用例
    """
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

        parm = {
            "name": "task_for_unittest_put13",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba39121"],
            "exploreStartDate": "2019-05-10",
            "exploreEndDate": "2019-08-15"
        }
        rst = self.client.post(api.url_for(ExploreTask), json=parm)
        time.sleep(1)
        self.task = json.loads(rst.data)
        print(self.task)

    def tearDown(self):
        pass

    def stop_delete_task(self):
        """
        :return:
        """
        parm = {
            "action": "stop"
        }
        self.client.put(api.url_for(ExploreTaskWithId,
                                    task_id=self.task["data"]["taskId"]), json=parm)
        self.client.delete(api.url_for(ExploreTaskWithId,
                                       task_id=self.task["data"]["taskId"]))

    def test_get_1(self):
        """
        获取已存在的探索任务
        :return:
        """
        time.sleep(1)
        rst = self.client.get(api.url_for(ExploreTaskWithId,
                                          task_id=self.task["data"]["taskId"]))
        self.stop_delete_task()
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], True)

    def test_get_2(self):
        """
        获取不存在的探索任务
        :return:
        """
        rst = self.client.get(api.url_for(ExploreTaskWithId,
                                          task_id="f28c95eb3814cf952fb971bd7b2e84a4"))
        self.stop_delete_task()
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], False)

    def test_put_1(self):
        """
        探索任务暂停
        :return:
        """
        parm = {
            "action": "pause"
        }
        time.sleep(1)
        rst = self.client.put(api.url_for(ExploreTaskWithId,
                                          task_id=self.task["data"]["taskId"]), json=parm)
        rst = json.loads(rst.data)
        print(rst)
        time.sleep(1)
        self.stop_delete_task()
        self.assertEqual(rst['flag'], True)

    def test_put_2(self):
        """
        探索任务恢复
        :return:
        """
        self.client.put(api.url_for(ExploreTaskWithId,
                                    task_id=self.task["data"]["taskId"]), json={"action": "pause"})
        parm = {
            "action": "resume"
        }
        rst = self.client.put(api.url_for(ExploreTaskWithId,
                                          task_id=self.task["data"]["taskId"]), json=parm)
        rst = json.loads(rst.data)
        print(rst)
        self.stop_delete_task()
        self.assertEqual(rst['flag'], True)

    def test_put_3(self):
        """
        探索任务停止
        :return:
        """
        parm = {
            "action": "stop"
        }
        rst = self.client.put(api.url_for(ExploreTaskWithId,
                                          task_id=self.task["data"]["taskId"]), json=parm)
        rst = json.loads(rst.data)
        print(rst)
        self.test_delete_2()
        self.assertEqual(rst['flag'], True)

    def test_put_4(self):
        """
        探索任务重新执行
        :return:
        """
        self.client.put(api.url_for(ExploreTaskWithId,
                                    task_id=self.task["data"]["taskId"]), json={"action": "stop"})
        time.sleep(1)
        parm = {
            "action": "restart"
        }
        rst = self.client.put(api.url_for(ExploreTaskWithId,
                                          task_id=self.task["data"]["taskId"]), json=parm)
        rst = json.loads(rst.data)
        print(rst)
        self.client.put(api.url_for(ExploreTaskWithId,
                                    task_id=self.task["data"]["taskId"]), json={"action": "stop"})
        self.stop_delete_task()
        self.assertEqual(rst['flag'], True)

    def test_put_5(self):
        """
        探索任务修改
        :return:
        """
        self.client.put(api.url_for(ExploreTaskWithId,
                                    task_id=self.task["data"]["taskId"]), json={"action": "stop"})
        time.sleep(1)
        parm = {
            "action": "update",
            "name": "task_for_unit_test_for_put5",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba39121"],
            "exploreStartDate": "2019-02-10",
            "exploreEndDate": "2019-09-15"
        }

        rst = self.client.put(api.url_for(ExploreTaskWithId,
                                          task_id=self.task["data"]["taskId"]), json=parm)
        rst = json.loads(rst.data)
        print(rst)
        self.stop_delete_task()
        self.assertEqual(rst['flag'], True)

    def test_put_6(self):
        """
        修改不存在的探索任务
        :return:
        """
        parm = {
            "action": "update",
            "name": "task_for_unit_test_for_put6",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba39121"],
            "exploreStartDate": "2019-02-10",
            "exploreEndDate": "2019-09-15"
        }
        rst = self.client.put(api.url_for(ExploreTaskWithId,
                                          task_id="f28c95eb3814cf952fb971bd7b2e84a44455"),
                              json=parm)
        rst = json.loads(rst.data)
        print(rst)
        self.stop_delete_task()
        self.assertEqual(rst['flag'], False)

    def test_delete_1(self):
        """
        删除不存在的探索任务
        :return:
        """
        rst = self.client.delete(api.url_for(ExploreTaskWithId,
                                             task_id="3bcfac207b00417867798e16401708d6"))
        rst = json.loads(rst.data)
        print(rst)
        self.stop_delete_task()
        self.assertEqual(rst['message'], "Task does not exist.")

    def test_delete_2(self):
        """
        删除不存在的探索任务
        :return:
        """
        parm = {
            "action": "stop"
        }
        self.client.put(api.url_for(ExploreTaskWithId,
                                    task_id=self.task["data"]["taskId"]), json=parm)
        rst = self.client.delete(api.url_for(ExploreTaskWithId,
                                             task_id=self.task["data"]["taskId"]))
        rst = json.loads(rst.data)
        print(rst)
        self.assertEqual(rst['flag'], True)


class TestExploreTask(TestCase):
    """
    探索模块不带 ID 的单元测试用例
    """
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_post_1(self):
        """
        创建新的探索任务
        :return:
        """
        parm = {
            "name": "task_for_unit_test_post1",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba39121"],
            "exploreStartDate": "2019-05-10",
            "exploreEndDate": "2019-08-15"
        }

        rst = self.client.post(api.url_for(ExploreTask), json=parm)

        print(rst.data)
        rst = json.loads(rst.data)
        if rst['flag'] is True:
            # 删除任务
            self.client.delete(api.url_for(ExploreTaskWithId,
                                           task_id=rst["data"]["taskId"]))
        self.assertEqual(rst['flag'], True)

    def test_post_2(self):
        """
        重复创建探索任务
        :return:
        """
        parm = {
            "name": "task_for_unit_test4",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba39121"],
            "exploreStartDate": "2019-05-10",
            "exploreEndDate": "2019-08-15"
        }

        rst = self.client.post(api.url_for(ExploreTask), json=parm)
        rst1 = self.client.post(api.url_for(ExploreTask), json=parm)
        print(rst1.data)
        rst1 = json.loads(rst1.data)
        rst = json.loads(rst.data)
        if rst['flag'] is True:
            # 删除任务
            self.client.delete(api.url_for(ExploreTaskWithId,
                                           task_id=rst["data"]["taskId"]))
        self.assertEqual(rst1['flag'], False)

    def test_post_3(self):
        """
        创建特征组不存在的探索任务
        :return:
        """
        parm = {
            "name": "task_for_unittest4",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba3912122"],
            "exploreStartDate": "2019/05/10",
            "exploreEndDate": "2019/08/15"
        }

        rst = self.client.post(api.url_for(ExploreTask), json=parm)
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], False)

    def test_post_4(self):
        """
        创建空名的探索任务
        :return:
        """
        parm = {
            "name": "",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba39121"],
            "exploreStartDate": "2019-05-10",
            "exploreEndDate": "2019-08-15"
        }

        rst = self.client.post(api.url_for(ExploreTask), json=parm)
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], False)

    def test_post_5(self):
        """
        创建日期格式错误的的探索任务
        :return:
        """
        parm = {
            "name": "task_for_unittest_post",
            "featureGroupList": ["bfc10ace21a785a216471f7c8ba39121"],
            "exploreStartDate": "2019/05/10",
            "exploreEndDate": "2019/08/15"
        }

        rst = self.client.post(api.url_for(ExploreTask), json=parm)
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], False)

    def test_get_1(self):
        """
        获取所有探索任务
        :return:
        """
        rst = self.client.get(api.url_for(ExploreTask,
                                          page=1,
                                          pageSize=10
                                          ))
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], True)

    def test_get_2(self):
        """
        获取所有探索任务
        :return:
        """
        rst = self.client.get(api.url_for(ExploreTask,
                                          createStartTime='2019-09-08 15:07:22',
                                          createEndTime='2019-09-09 15:07:28',
                                          taskName='test',
                                          killChains='Recon,Weaponization',
                                          vulMinCount='0',
                                          vulMaxCount='1',
                                          threatMinScore='0',
                                          threatMaxScore='100',
                                          sort='explore',
                                          reverse='false',
                                          page=1,
                                          pageSize=10
                                          ))
        print(rst.data)
        rst = json.loads(rst.data)
        self.assertEqual(rst['flag'], True)
