import React, { useEffect } from 'react';
import CountUp from 'react-countup';

import { DashCard } from './DashCard';
import { useKillChainData } from './hooks';
import { ATTACK_STAGE_ICON_MAP } from './constant';
import CustomIcon, { IconName } from './CustomIcon';
import { Link } from 'umi';
import { Spin } from 'antd';
import { TimeRange } from './UnSettledThreat/HighRiskHostCard';

import style from './index.less';

export type Props = {
  timeRange: TimeRange
  style?: React.CSSProperties;
  className?: string;
}

export function AttackChainCard({ timeRange }: Props): JSX.Element {
  const { loading, data: killChainData, updateTimeRange } = useKillChainData(timeRange);

  useEffect(() => {
    updateTimeRange(timeRange)
  }, [timeRange])

  return (
    <DashCard className={style.card_attack_stage} title="攻击阶段">
      <Spin wrapperClassName='w-full' spinning={loading}>
        <div className='flex flex-1 justify-between items-center p-5 gap-1'>
          {killChainData.map(({ nameCn, value, name }: any, index: number) => {
            const icon = ATTACK_STAGE_ICON_MAP[name];
            return (
              <React.Fragment key={icon}>
                <div className='flex items-center'>
                  <CustomIcon name={icon as IconName} className={style.item_icon} />
                  <span className={style.item_text}>
                    <Link to="/workbench">
                      {`${nameCn} : `}
                      <CountUp
                        className={style.item_text_num}
                        duration={1}
                        delay={0}
                        preserveValue
                        end={value}
                      />
                    </Link>
                  </span>
                </div>

                {index !== killChainData.length - 1 &&
                  <CustomIcon name='rightArrow' style={{ fontSize: 20 }} />}
              </React.Fragment>
            );
          })}
        </div>
      </Spin>
    </DashCard>
  );
}
