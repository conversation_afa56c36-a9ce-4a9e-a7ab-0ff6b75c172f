# -*- coding: utf-8 -*-
# @Time    : 2019-07-18 15:30
# <AUTHOR> Shark
# @File    : top_threat_score.py
# @Software: PyCharm
from flask import request
from flask_restful import Resource
from utils.utils import flask_response
from utils import param_check, database
from utils.param_check import Validator
from utils.es_function import get_threat_level
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class TopThreatScore(Resource):
    """按漏洞触发次数获取漏洞列表"""

    # todo 同级别按最新排序

    def __init__(self):
        """初始化参数"""
        # 初始化 ES 查询模板
        self.es_template = ES_Template().read_template('top_threat_score')
        # 初始化 ES client
        self.es_client = database.get_es_client()

    @param_check.check_flask_args(Validator("top_threat_score"), request)
    def get(self, **kwargs):
        """处理get请求"""

        # 参数赋值并做格式转换
        args = {
            "page_size": int(kwargs["args"]["pageSize"]),
            "start_time": int(kwargs["args"]["startTime"]),
            "stop_time": int(kwargs["args"]["stopTime"])
        }

        if args["start_time"] >= args["stop_time"]:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, [])

        self.format_es_template(args)

        try:
            res = self.es_client.search(index="rule-eve", body=self.es_template)
        except Exception as e:
            err_info = "error: " + str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(err_info, False, [])
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [])

    def format_es_template(self, args):
        """通过传入参数填充ES查询聚合语句"""

        self.es_template["query"]["bool"]["must"][0]["range"]["occurredTime"]["gte"] = args["start_time"]
        self.es_template["query"]["bool"]["must"][0]["range"]["occurredTime"]["lte"] = args["stop_time"]
        self.es_template["aggs"]["top_vul_name"]["terms"]["size"] = args["page_size"]

    @staticmethod
    def data_format(data):
        """将es输出结果格式化成前端结构"""

        res_list = []

        for vul_info in data["aggregations"]["top_vul_name"]["buckets"]:
            res_one = vul_info["top_event_hits"]["hits"]["hits"][0]["_source"]["rulesInfo"]
            res_one["threatLevel"] = get_threat_level(num=res_one["threatLevel"])
            res_one["triggerCount"] = vul_info["doc_count"]
            res_list.append(res_one)

        return res_list
