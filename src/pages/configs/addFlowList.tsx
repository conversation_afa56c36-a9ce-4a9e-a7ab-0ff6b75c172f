/*
 * @Author: 田浩
 * @Date: 2021-09-15 10:57:24
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-06 10:54:07
 * @Descripttion:
 */
import React, { Fragment, forwardRef } from 'react';
import { Form, Input, Select, message, Button } from 'antd';
import { FlowType } from '@/utils/enumList';
const { Option } = Select;

const AddFlowList = forwardRef((props: any, ref: any) => {
  const [form] = Form.useForm();

  const [detail, setDetail] = React.useState([
    {
      tag: '',
      target: '',
      type: '',
    },
  ]);

  const formData = ['target', 'type', 'tag'];

  const deleteOne = (i: any) => {
    let arr = [...detail];
    arr.splice(i, 1);
    form.setFieldsValue({ [`${i}.tag`]: '' });
    form.setFieldsValue({ [`${i}.target`]: '' });
    form.setFieldsValue({ [`${i}.type`]: '' });
    setDetail([...arr]);
  };
  const changeValue = (e: any, index: any) => {
    detail[index].target = e.target.value;
    setDetail([...detail]);
  };
  const changeType = (e: any, index: any) => {
    detail[index].type = e;
    setDetail([...detail]);
  };
  const changeTag = (e: any, index: any) => {
    detail[index].tag = e.target.value;
    setDetail([...detail]);
  };
  const newItem = () => {
    setDetail([
      ...detail,
      {
        tag: '',
        target: '',
        type: '',
      },
    ]);
  };
  const formItemLayout = {
    labelCol: {
      xs: { span: 8 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 10 },
      sm: { span: 10 },
    },
  };
  return (
    <Fragment>
      <Form ref={ref} form={form} {...formItemLayout}>
        {detail.map((item, index) => (
          <div
            key={index}
            style={{
              position: 'relative',
              borderBottom: '1px solid #ccc',
              paddingTop: 20,
            }}
          >
            <Form.Item label="值" name={`${index}.target`} rules={[{ required: true, message: '请填写' }]}>
              <Input />
            </Form.Item>
            <Form.Item label="类型" rules={[{ required: true, message: '请选择' }]} name={`${index}.type`}>
              <Select>
                {FlowType.map(item => {
                  return (
                    <Option value={item.value} key={item.key}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item label="备注" name={`${index}.tag`} rules={[{ required: true, message: '请填写' }]}>
              <Input />
            </Form.Item>
            <div style={{ position: 'absolute', right: 50, top: 58 }}>
              {index === 0 ? (
                <Button type="primary" onClick={newItem}>
                  增加
                </Button>
              ) : null}
              {index === detail.length - 1 && index !== 0 ? (
                <Button type="primary" onClick={() => deleteOne(index)}>
                  删除
                </Button>
              ) : null}
            </div>
          </div>
        ))}
      </Form>
    </Fragment>
  );
});

export default AddFlowList;
