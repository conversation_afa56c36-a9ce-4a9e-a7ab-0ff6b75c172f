#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: test_eventDetailResource.py
# @time: 2019/08/27
# @software: PyCharm
import json
import random
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_event.get_event_detail import EventDetailResource


class TestEventDetailResource(TestCase):
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_get_start_time_gt_stop_time(self):
        startTime = 1578430872367
        stopTime = 1504527700099
        # rip = lambda: '.'.join(
        #     [str(int(''.join([str(random.randint(0, 2)), str(random.randint(0, 5)), str(random.randint(0, 5))]))) for _
        #      in range(4)])
        # attack_ip = rip()
        response = self.client.get(
            api.url_for(EventDetailResource, startTime=startTime, stopTime=stopTime, attackIp=attack_ip))
        resp_dict = json.loads(response.data)
        self.assertEqual(resp_dict["flag"], False)

    def test_get_correct_res(self):
        startTime = 1504527700099
        stopTime = 1578430872367
        # rip = lambda: '.'.join(
        #     [str(int(''.join([str(random.randint(0, 2)), str(random.randint(0, 5)), str(random.randint(0, 5))]))) for _
        #      in range(4)])
        # attack_ip = rip()
        response = self.client.get(
            api.url_for(EventDetailResource, startTime=startTime, stopTime=stopTime, attackIp=attack_ip))
        resp_dict = json.loads(response.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)
