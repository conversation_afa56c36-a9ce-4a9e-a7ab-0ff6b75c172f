import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Link } from 'umi';
import moment from 'moment';
import cs from 'classnames';
import { Badge, Button, Divider, Popover, Space, Table, Tag, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

import styleModule from '../style.less'
import QueryBox from './QueryBox';
import { EmailListItem, TimeRange } from '../types';
import { useEmailAnalysisListData } from './hooks';
import { parseQueryString } from '@/utils/utils';

import { SECURITY_COLORS } from '@/pages/threatAwareness/constant';
// import { useConstantOptions } from '@/pages/threatAwareness/hooks';
import EmailSendReciveModal from './EmailSendReciveModal';

export type Props = {
  timeRange: TimeRange
  onJumpSearch?: (key: string, params: any) => void
  style?: React.CSSProperties;
  className?: string;
}

function EmailList({
  style,
  className,
  timeRange,
  onJumpSearch,
}: Props, ref: React.Ref<{ search?: (params: any) => void }>): JSX.Element {

  // const { serverirtyMap } = useConstantOptions();
  const severityMap: Record<number, string> = {
    1: '低危',
    2: '中危',
    3: '高危',
  }
  const controllerRef = useRef<AbortController>()

  const handleAbort = (controller: AbortController) => {
    controllerRef.current = controller
  }

  const {
    list,
    loading,
    pagination: paginationData,
    setSort,
    updatePagination,
    setTimeRange,
    setQueryParams,
  } = useEmailAnalysisListData(timeRange, handleAbort);

  const [currentID, setID] = useState<string>('')
  const [showSendRecv, setShowSendRecv] = useState(false)
  const searchBoxRef = useRef<{ search: (params: any) => void }>(null)

  useEffect(() => {
    setTimeRange(timeRange)
  }, [timeRange])

  useImperativeHandle(ref, () => ({
    search: (params) => {
      controllerRef.current?.abort();
      searchBoxRef.current?.search(params)
    }
  }));

  const columns = [
    {
      title: "最近检测时间",
      width: 200,
      fixed: 'left',
      dataIndex: "timestamp",
      render: (t: number) => moment(t).format('YYYY-MM-DD HH:mm:ss'),
      sorter: {
        compare: (a: EmailListItem, b: EmailListItem) => a.timestamp - b.timestamp,
      },
    },
    {
      title: "发件人",
      width: 200,
      dataIndex: "from",
      render: (t: string) => t || '-',
    },
    {
      title: "收件人信息",
      width: 260,
      key: "recv_info",
      render: (_: string, record: EmailListItem) => {
        const { cc, to, bcc } = record
        return <Space direction="vertical">
          {!!to.length && <div>收件人: {renderTags(to, 1) || '-'}</div>}
          {!!cc.length && <div>抄送: {renderTags(cc, 1) || '-'}</div>}
          {!!bcc.length && <div>暗送: {renderTags(bcc, 1) || '-'}</div>}
        </Space>
      },
    },
    {
      title: "主题",
      width: 180,
      dataIndex: "subject",
      ellipsis: { showTitle: false },
      render: (t: string) =>
        <Tooltip placement="topLeft" title={t} >{t}</Tooltip> || '-',
    },
    {
      title: "检测结果",
      width: 150,
      dataIndex: "threat",
      render: (threats: string[]) => renderTags(threats, 3),
    },
    {
      title: "邮件ID/邮件状态",
      width: 200,
      key: "status",
      render: (_: string, record: EmailListItem) => {
        const { msg_id, fetch } = record
        const status = fetch ? 'success' : 'error'
        const statusText = fetch ? '已收取' : '未收取'
        return (
          <>
            <Button
              type='link'
              className='w-full'
              style={{ padding: 0 }}
              onClick={() => searchBoxRef.current?.search({ msg_id })}
            >
              <Tooltip placement="topLeft" title={msg_id} >
                <span className='w-full text-left truncate'>{msg_id}</span> 
              </Tooltip>
            </Button>
            <div><Badge status={status} text={statusText} /></div>
          </>
        )
      },
    },
    {
      title: (
        <span>
          威胁等级
          <Popover
            content="显示全部检测结果的最高等级"
            title="说明"
            trigger="hover"
            showArrow={false}
          >
            <InfoCircleOutlined className="pl-2" />
          </Popover>
        </span>
      ),
      width: 110,
      fixed: 'right',
      align: 'center',
      dataIndex: "severity",
      render: (v: number) => <Tag className={styleModule.severity_tag} color={SECURITY_COLORS[v]}>{severityMap[v]}</Tag>,
    },
    {
      title: "操作",
      width: 245,
      align: 'center',
      fixed: 'right',
      key: "action",
      render: (_: never, record: EmailListItem) => {
        const { msg_id: msg_id } = record

        return (
          <Space split={<Divider type="vertical" />}>
            <Link
              className="!px-0"
              target="_blank"
              to={{
                pathname: '/app/mica/customized/emailAnalysis/emailDetail',
                search: `${parseQueryString({ ...timeRange, msg_id, })}`,
              }}
            >
              详情
            </Link>
            <Button
              type='link'
              style={{ padding: 0 }}
              onClick={() => { 
                onJumpSearch?.('emailAttachmentList', { msg_id })
              }}
            >
              附件列表
            </Button>
            <Button
              type='link'
              style={{ padding: 0 }}
              onClick={() => {
                setShowSendRecv(true)
                setID(msg_id)
              }}
            >
              收发信息
            </Button>
          </Space>
        );
      }
    }
  ];

  const totalInfoRender = (total: number) => (
    <span>
      <Popover
        content="最多展示10000条数据"
        title="说明"
        trigger="hover"
        showArrow={false}
      >
        <InfoCircleOutlined className="pr-2" />
      </Popover>
      总计 {total} 条
    </span >
  );

  const pagination = {
    total: paginationData.total,
    showSizeChanger: true,
    showQuickJumper: true,
    current: paginationData.page,
    showTotal: totalInfoRender,
    pageSize: paginationData.pageSize,
    onChange: (page: number, pageSize: number) => updatePagination({ page, pageSize }),
  };

  return (
    <div style={style} className={cs(className)}>
      <QueryBox ref={searchBoxRef} listType="emailList" onSearch={setQueryParams} timeRange={timeRange} />
      <Table
        loading={loading}
        rowKey="id"
        columns={columns as any[]}
        scroll={{ x: 1200, }}
        dataSource={list}
        pagination={pagination}
        onChange={(_, __, sorter) => {
          const { order } = sorter as any
          setSort?.(order?.replace('end', ''))
        }}
      />
      <EmailSendReciveModal
        open={showSendRecv}
        data={{ msg_id: currentID, ...timeRange }}
        onCancel={() => setShowSendRecv(false)}
      />
    </div>
  );
}

export default forwardRef(EmailList);


export const renderTags = (tags?: string[] | string, maxCount = 1) => {
  if (!tags || !tags.length) return ''

  if (typeof tags === 'string') {
    return <Tag>{tags}</Tag>
  }

  if (!maxCount) {
    return tags.map((rule) => <Tag style={{ marginBottom: 4 }} key={rule}>{rule}</Tag>)
  }

  if (!(tags.length - 1)) {
    return <Tag>{tags[0]}</Tag>
  }

  return (
    <Tooltip title={tags.map((rule) => <Tag style={{ marginBottom: 4 }} key={rule}>{rule}</Tag>)} >
      {tags.slice(0, maxCount).map((tag) => <Tag key={tag}>{tag}</Tag>)}
      {tags.length > maxCount && <Tag>...</Tag>} 
    </Tooltip >
  )
}