# -*- coding: utf-8 -*-
# @Time    : 2019-09-02 17:11
# <AUTHOR> Shark
# @File    : test_alert_map.py
# @Software: PyCharm
import json
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_alert.alert_map import AlertMap


class TestAlertMap(TestCase):
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_startTime_not_exists(self):
        resp = self.client.get(
            api.url_for(AlertMap,
                        stopTime=1578430872367,
                        ipSize=10
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_stopTime_not_exists(self):
        resp = self.client.get(
            api.url_for(AlertMap,
                        startTime=1504527700099,
                        ipSize=10
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_ipSize_not_exists(self):
        resp = self.client.get(
            api.url_for(AlertMap,
                        startTime=1504527700099,
                        stopTime=1578430872367
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)

    def test_startTime_lt_stopTime(self):
        resp = self.client.get(
            api.url_for(AlertMap,
                        startTime=1578430872367,
                        stopTime=1504527700099,
                        ipSize=10
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_all_true(self):
        resp = self.client.get(
            api.url_for(AlertMap,
                        startTime=1504527700099,
                        stopTime=1578430872367,
                        ipSize=10
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)
