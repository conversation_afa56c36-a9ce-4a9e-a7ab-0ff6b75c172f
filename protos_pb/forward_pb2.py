# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: forward.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rforward.proto\x12\x07\x66orward\x1a\x1bgoogle/protobuf/empty.proto\"\xb8\x02\n\nStatsReply\x12\x17\n\x0fipv4_in_packets\x18\x01 \x01(\x04\x12\x18\n\x10ipv4_out_packets\x18\x02 \x01(\x04\x12\x1c\n\x14ipv4_consume_packets\x18\x03 \x01(\x04\x12\x19\n\x11ipv4_drop_packets\x18\x04 \x01(\x04\x12\x1e\n\x16ipv4_blackhole_packets\x18\x05 \x01(\x04\x12\x18\n\x10ipv4_err_packets\x18\x06 \x01(\x04\x12\x17\n\x0fipv6_in_packets\x18\x07 \x01(\x04\x12\x18\n\x10ipv6_out_packets\x18\x08 \x01(\x04\x12\x18\n\x10other_in_packets\x18\t \x01(\x04\x12\x19\n\x11other_out_packets\x18\n \x01(\x04\x12\x1c\n\x14ipv4_deliver_packets\x18\x0b \x01(\x04\" \n\x10PortStatsRequest\x12\x0c\n\x04port\x18\x01 \x01(\r\"^\n\x0ePortStatsReply\x12\x12\n\nin_packets\x18\x01 \x01(\x04\x12\x10\n\x08in_bytes\x18\x02 \x01(\x04\x12\x13\n\x0bout_packets\x18\x03 \x01(\x04\x12\x11\n\tout_bytes\x18\x04 \x01(\x04\"%\n\x15\x43learPortStatsRequest\x12\x0c\n\x04port\x18\x01 \x01(\r\"\x1e\n\rGetDebugReply\x12\r\n\x05\x64\x65\x62ug\x18\x01 \x01(\r\"/\n\x0fSetDebugRequest\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\x0c\n\x04type\x18\x02 \x01(\r\"\x8d\x02\n\x0b\x41\x63l4Request\x12\x0c\n\x04port\x18\x01 \x01(\r\x12\x11\n\tport_mask\x18\x02 \x01(\r\x12\r\n\x05proto\x18\x03 \x01(\r\x12\x12\n\nproto_mask\x18\x04 \x01(\r\x12\x0e\n\x06ip_src\x18\x05 \x01(\r\x12\x13\n\x0bip_src_mask\x18\x06 \x01(\r\x12\x0e\n\x06ip_dst\x18\x07 \x01(\r\x12\x13\n\x0bip_dst_mask\x18\x08 \x01(\r\x12\x10\n\x08port_src\x18\t \x01(\r\x12\x14\n\x0cport_src_max\x18\n \x01(\r\x12\x10\n\x08port_dst\x18\x0b \x01(\r\x12\x14\n\x0cport_dst_max\x18\x0c \x01(\r\x12\x10\n\x08priority\x18\r \x01(\r\x12\x0e\n\x06\x61\x63tion\x18\x0e \x01(\r\"*\n\tAcl4Reply\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t*Y\n\x0b\x46orwardMode\x12\x1b\n\x17\x46ORWARD_MODE_BLACK_HOLE\x10\x00\x12\x17\n\x13\x46ORWARD_MODE_INLINE\x10\x01\x12\x14\n\x10\x46ORWARD_MODE_ACL\x10\x02\x32\xca\x03\n\x07\x46orward\x12\x39\n\x08GetStats\x12\x16.google.protobuf.Empty\x1a\x13.forward.StatsReply\"\x00\x12>\n\nClearStats\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x00\x12\x44\n\x0cGetPortStats\x12\x19.forward.PortStatsRequest\x1a\x17.forward.PortStatsReply\"\x00\x12J\n\x0e\x43learPortStats\x12\x1e.forward.ClearPortStatsRequest\x1a\x16.google.protobuf.Empty\"\x00\x12<\n\x08GetDebug\x12\x16.google.protobuf.Empty\x1a\x16.forward.GetDebugReply\"\x00\x12>\n\x08SetDebug\x12\x18.forward.SetDebugRequest\x1a\x16.google.protobuf.Empty\"\x00\x12\x34\n\x04\x41\x63l4\x12\x14.forward.Acl4Request\x1a\x12.forward.Acl4Reply\"\x00(\x01\x62\x06proto3')

_FORWARDMODE = DESCRIPTOR.enum_types_by_name['ForwardMode']
ForwardMode = enum_type_wrapper.EnumTypeWrapper(_FORWARDMODE)
FORWARD_MODE_BLACK_HOLE = 0
FORWARD_MODE_INLINE = 1
FORWARD_MODE_ACL = 2


_STATSREPLY = DESCRIPTOR.message_types_by_name['StatsReply']
_PORTSTATSREQUEST = DESCRIPTOR.message_types_by_name['PortStatsRequest']
_PORTSTATSREPLY = DESCRIPTOR.message_types_by_name['PortStatsReply']
_CLEARPORTSTATSREQUEST = DESCRIPTOR.message_types_by_name['ClearPortStatsRequest']
_GETDEBUGREPLY = DESCRIPTOR.message_types_by_name['GetDebugReply']
_SETDEBUGREQUEST = DESCRIPTOR.message_types_by_name['SetDebugRequest']
_ACL4REQUEST = DESCRIPTOR.message_types_by_name['Acl4Request']
_ACL4REPLY = DESCRIPTOR.message_types_by_name['Acl4Reply']
StatsReply = _reflection.GeneratedProtocolMessageType('StatsReply', (_message.Message,), {
  'DESCRIPTOR' : _STATSREPLY,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.StatsReply)
  })
_sym_db.RegisterMessage(StatsReply)

PortStatsRequest = _reflection.GeneratedProtocolMessageType('PortStatsRequest', (_message.Message,), {
  'DESCRIPTOR' : _PORTSTATSREQUEST,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.PortStatsRequest)
  })
_sym_db.RegisterMessage(PortStatsRequest)

PortStatsReply = _reflection.GeneratedProtocolMessageType('PortStatsReply', (_message.Message,), {
  'DESCRIPTOR' : _PORTSTATSREPLY,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.PortStatsReply)
  })
_sym_db.RegisterMessage(PortStatsReply)

ClearPortStatsRequest = _reflection.GeneratedProtocolMessageType('ClearPortStatsRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLEARPORTSTATSREQUEST,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.ClearPortStatsRequest)
  })
_sym_db.RegisterMessage(ClearPortStatsRequest)

GetDebugReply = _reflection.GeneratedProtocolMessageType('GetDebugReply', (_message.Message,), {
  'DESCRIPTOR' : _GETDEBUGREPLY,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.GetDebugReply)
  })
_sym_db.RegisterMessage(GetDebugReply)

SetDebugRequest = _reflection.GeneratedProtocolMessageType('SetDebugRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETDEBUGREQUEST,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.SetDebugRequest)
  })
_sym_db.RegisterMessage(SetDebugRequest)

Acl4Request = _reflection.GeneratedProtocolMessageType('Acl4Request', (_message.Message,), {
  'DESCRIPTOR' : _ACL4REQUEST,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.Acl4Request)
  })
_sym_db.RegisterMessage(Acl4Request)

Acl4Reply = _reflection.GeneratedProtocolMessageType('Acl4Reply', (_message.Message,), {
  'DESCRIPTOR' : _ACL4REPLY,
  '__module__' : 'forward_pb2'
  # @@protoc_insertion_point(class_scope:forward.Acl4Reply)
  })
_sym_db.RegisterMessage(Acl4Reply)

_FORWARD = DESCRIPTOR.services_by_name['Forward']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _FORWARDMODE._serialized_start=936
  _FORWARDMODE._serialized_end=1025
  _STATSREPLY._serialized_start=56
  _STATSREPLY._serialized_end=368
  _PORTSTATSREQUEST._serialized_start=370
  _PORTSTATSREQUEST._serialized_end=402
  _PORTSTATSREPLY._serialized_start=404
  _PORTSTATSREPLY._serialized_end=498
  _CLEARPORTSTATSREQUEST._serialized_start=500
  _CLEARPORTSTATSREQUEST._serialized_end=537
  _GETDEBUGREPLY._serialized_start=539
  _GETDEBUGREPLY._serialized_end=569
  _SETDEBUGREQUEST._serialized_start=571
  _SETDEBUGREQUEST._serialized_end=618
  _ACL4REQUEST._serialized_start=621
  _ACL4REQUEST._serialized_end=890
  _ACL4REPLY._serialized_start=892
  _ACL4REPLY._serialized_end=934
  _FORWARD._serialized_start=1028
  _FORWARD._serialized_end=1486
# @@protoc_insertion_point(module_scope)
