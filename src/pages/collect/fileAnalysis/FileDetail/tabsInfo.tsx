import { Tabs, Badge, Empty, Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import Virus from './tabsComponents/virus';
import Signature from './tabsComponents/signature';
import StaticInfo from './tabsComponents/staticInfo';
import RunImg from './tabsComponents/runImg';
import ShellCode from './tabsComponents/shellCode';
import Network from './tabsComponents/network';
import s from './style.less';
interface partProps {
  detail: any;
}
const btnArr = [
  '病毒检测',
  '静态信息',
  '文件签名',
  '进程行为',
  '网络行为',
  '启发式检测',
  '流量特征检测',
  'ShellCode',
  '运行截图',
];
const TablePart = ({ detail = {} }: partProps) => {
  const [netNum, setNetNum] = useState(0);
  const [activeKey, setActiveKey] = useState('1');
  const netNumFun = () => {
    const Obj = ['domains', 'hosts', 'dns_servers', 'http', 'udp', 'tcp', 'icmp'];
    let num = 0;
    Obj.map(e => {
      detail.network[e] && (num += detail.network[e].length);
    });
    setNetNum(num);
  };
  const bNum = [0, 0, detail.signatures?.length, 0, netNum, 0, 0, 0, detail.screenshots?.length];
  useEffect(() => {
    !isEmpty(detail) && netNumFun();
  }, [detail]);
  return (
    <div className={s.tabsInfo}>
      <div className={s.btnBox}>
        {btnArr.map((e, i) => {
          return (
            <Badge count={bNum[i]} style={{ right: '26px' }}>
              <Button
                className="mr-4"
                onClick={() => {
                  setActiveKey(`${i + 1}`);
                }}
                disabled={i == activeKey - 1}
              >
                {e}
              </Button>
            </Badge>
          );
        })}
      </div>
      <Tabs type="card" activeKey={activeKey}>
        <Tabs.TabPane tab="" key="1">
          <div className="p-4">
            <Virus info={detail.fileInfo} />
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane tab={<div>静态信息</div>} key="2">
          <div className="p-4">
            <StaticInfo info={detail.static} />
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={
            <Badge count={detail.signatures?.length} offset={[-12, -4]}>
              <div>文件签名</div>
            </Badge>
          }
          key="3"
        >
          <div className="p-4">
            <Signature info={detail.signatures} />
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane tab={<div>进程行为</div>} key="4">
          <div className="p-4">
            <Empty />
            {/* <RrocessInfo info={detail.signatures} /> */}
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={
            <Badge count={netNum} offset={[10, 0]}>
              <div>网络行为</div>
            </Badge>
          }
          key="5"
        >
          <Network info={detail.network} />
        </Tabs.TabPane>
        <Tabs.TabPane tab={<div>启发式检测</div>} key="6">
          <div className="p-4">
            <Empty />
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane tab={<div>流量特征检测</div>} key="7">
          <div className="p-4">
            <ShellCode info={detail.screenshots} />
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane tab={<div>ShellCode</div>} key="8">
          <div className="p-4">
            <ShellCode info={detail.screenshots} />
          </div>
        </Tabs.TabPane>
        <Tabs.TabPane tab={<div>运行截图</div>} key="9">
          <div className="p-4">
            <RunImg info={detail.screenshots} />
          </div>
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};

export default TablePart;
