# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: session.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rsession.proto\x12\x07session\x1a\x1bgoogle/protobuf/empty.proto\",\n\x0bReplyHeader\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\"Q\n\x0fSessionsRequest\x12\x0e\n\x06\x61nchor\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x64\x64r_family\x18\x02 \x01(\r\x12\x19\n\x03key\x18\x03 \x01(\x0b\x32\x0c.session.Key\"i\n\rSessionsReply\x12!\n\x03msg\x18\x01 \x01(\x0b\x32\x14.session.ReplyHeader\x12\x0e\n\x06\x61nchor\x18\x02 \x01(\r\x12%\n\x07session\x18\x03 \x03(\x0b\x32\x14.session.SessionData\"[\n\x03Key\x12\x0e\n\x06src_ip\x18\x01 \x01(\t\x12\x0e\n\x06\x64st_ip\x18\x02 \x01(\t\x12\x10\n\x08src_port\x18\x03 \x01(\r\x12\x10\n\x08\x64st_port\x18\x04 \x01(\r\x12\x10\n\x08protocol\x18\x05 \x01(\r\"W\n\x0bSessionData\x12\x19\n\x03key\x18\x01 \x01(\x0b\x32\x0c.session.Key\x12\r\n\x05\x66lags\x18\x02 \x01(\r\x12\x0e\n\x06\x61pp_id\x18\x03 \x01(\r\x12\x0e\n\x06status\x18\x04 \x01(\r\"+\n\x0eSessionRequest\x12\x19\n\x03key\x18\x01 \x01(\x0b\x32\x0c.session.Key\"X\n\x0cSessionReply\x12!\n\x03msg\x18\x01 \x01(\x0b\x32\x14.session.ReplyHeader\x12%\n\x07session\x18\x02 \x01(\x0b\x32\x14.session.SessionData\"H\n\x0cRelationData\x12\x19\n\x03key\x18\x01 \x01(\x0b\x32\x0c.session.Key\x12\r\n\x05\x66lags\x18\x02 \x01(\r\x12\x0e\n\x06\x61pp_id\x18\x03 \x01(\r\",\n\x0fRelationRequest\x12\x19\n\x03key\x18\x01 \x01(\x0b\x32\x0c.session.Key\"[\n\rRelationReply\x12!\n\x03msg\x18\x01 \x01(\x0b\x32\x14.session.ReplyHeader\x12\'\n\x08relation\x18\x02 \x01(\x0b\x32\x15.session.RelationData\"=\n\x10RelationsRequest\x12\x0e\n\x06\x61nchor\x18\x01 \x01(\r\x12\x19\n\x03key\x18\x02 \x01(\x0b\x32\x0c.session.Key\"l\n\x0eRelationsReply\x12!\n\x03msg\x18\x01 \x01(\x0b\x32\x14.session.ReplyHeader\x12\x0e\n\x06\x61nchor\x18\x02 \x01(\r\x12\'\n\x08relation\x18\x03 \x03(\x0b\x32\x15.session.RelationData\"\x1d\n\x0bStatSetting\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\"\\\n\x10StatSettingReply\x12!\n\x03msg\x18\x01 \x01(\x0b\x32\x14.session.ReplyHeader\x12%\n\x07setting\x18\x02 \x01(\x0b\x32\x14.session.StatSetting\"\x0e\n\x0cStatsRequest\"\x98\x01\n\nStatsReply\x12!\n\x03msg\x18\x01 \x01(\x0b\x32\x14.session.ReplyHeader\x12\x10\n\x08sessions\x18\x02 \x01(\r\x12\x14\n\x0ctcp_sessions\x18\x03 \x01(\r\x12\x14\n\x0cudp_sessions\x18\x04 \x01(\r\x12\x16\n\x0eother_sessions\x18\x05 \x01(\r\x12\x11\n\trelations\x18\x06 \x01(\r2\x9f\x05\n\x07Session\x12>\n\x0eSetStatSetting\x12\x14.session.StatSetting\x1a\x14.session.ReplyHeader\"\x00\x12\x45\n\x0eGetStatSetting\x12\x16.google.protobuf.Empty\x1a\x19.session.StatSettingReply\"\x00\x12\x38\n\x08GetStats\x12\x15.session.StatsRequest\x1a\x13.session.StatsReply\"\x00\x12>\n\nResetStats\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x00\x12\x42\n\x0cListSessions\x12\x18.session.SessionsRequest\x1a\x16.session.SessionsReply\"\x00\x12>\n\nGetSession\x12\x17.session.SessionRequest\x1a\x15.session.SessionReply\"\x00\x12\x41\n\rResetSessions\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x00\x12\x45\n\rListRelations\x12\x19.session.RelationsRequest\x1a\x17.session.RelationsReply\"\x00\x12\x41\n\x0bGetRelation\x12\x18.session.RelationRequest\x1a\x16.session.RelationReply\"\x00\x12\x42\n\x0eResetRelations\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x00\x62\x06proto3')



_REPLYHEADER = DESCRIPTOR.message_types_by_name['ReplyHeader']
_SESSIONSREQUEST = DESCRIPTOR.message_types_by_name['SessionsRequest']
_SESSIONSREPLY = DESCRIPTOR.message_types_by_name['SessionsReply']
_KEY = DESCRIPTOR.message_types_by_name['Key']
_SESSIONDATA = DESCRIPTOR.message_types_by_name['SessionData']
_SESSIONREQUEST = DESCRIPTOR.message_types_by_name['SessionRequest']
_SESSIONREPLY = DESCRIPTOR.message_types_by_name['SessionReply']
_RELATIONDATA = DESCRIPTOR.message_types_by_name['RelationData']
_RELATIONREQUEST = DESCRIPTOR.message_types_by_name['RelationRequest']
_RELATIONREPLY = DESCRIPTOR.message_types_by_name['RelationReply']
_RELATIONSREQUEST = DESCRIPTOR.message_types_by_name['RelationsRequest']
_RELATIONSREPLY = DESCRIPTOR.message_types_by_name['RelationsReply']
_STATSETTING = DESCRIPTOR.message_types_by_name['StatSetting']
_STATSETTINGREPLY = DESCRIPTOR.message_types_by_name['StatSettingReply']
_STATSREQUEST = DESCRIPTOR.message_types_by_name['StatsRequest']
_STATSREPLY = DESCRIPTOR.message_types_by_name['StatsReply']
ReplyHeader = _reflection.GeneratedProtocolMessageType('ReplyHeader', (_message.Message,), {
  'DESCRIPTOR' : _REPLYHEADER,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.ReplyHeader)
  })
_sym_db.RegisterMessage(ReplyHeader)

SessionsRequest = _reflection.GeneratedProtocolMessageType('SessionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONSREQUEST,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.SessionsRequest)
  })
_sym_db.RegisterMessage(SessionsRequest)

SessionsReply = _reflection.GeneratedProtocolMessageType('SessionsReply', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONSREPLY,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.SessionsReply)
  })
_sym_db.RegisterMessage(SessionsReply)

Key = _reflection.GeneratedProtocolMessageType('Key', (_message.Message,), {
  'DESCRIPTOR' : _KEY,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.Key)
  })
_sym_db.RegisterMessage(Key)

SessionData = _reflection.GeneratedProtocolMessageType('SessionData', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONDATA,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.SessionData)
  })
_sym_db.RegisterMessage(SessionData)

SessionRequest = _reflection.GeneratedProtocolMessageType('SessionRequest', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONREQUEST,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.SessionRequest)
  })
_sym_db.RegisterMessage(SessionRequest)

SessionReply = _reflection.GeneratedProtocolMessageType('SessionReply', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONREPLY,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.SessionReply)
  })
_sym_db.RegisterMessage(SessionReply)

RelationData = _reflection.GeneratedProtocolMessageType('RelationData', (_message.Message,), {
  'DESCRIPTOR' : _RELATIONDATA,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.RelationData)
  })
_sym_db.RegisterMessage(RelationData)

RelationRequest = _reflection.GeneratedProtocolMessageType('RelationRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELATIONREQUEST,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.RelationRequest)
  })
_sym_db.RegisterMessage(RelationRequest)

RelationReply = _reflection.GeneratedProtocolMessageType('RelationReply', (_message.Message,), {
  'DESCRIPTOR' : _RELATIONREPLY,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.RelationReply)
  })
_sym_db.RegisterMessage(RelationReply)

RelationsRequest = _reflection.GeneratedProtocolMessageType('RelationsRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELATIONSREQUEST,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.RelationsRequest)
  })
_sym_db.RegisterMessage(RelationsRequest)

RelationsReply = _reflection.GeneratedProtocolMessageType('RelationsReply', (_message.Message,), {
  'DESCRIPTOR' : _RELATIONSREPLY,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.RelationsReply)
  })
_sym_db.RegisterMessage(RelationsReply)

StatSetting = _reflection.GeneratedProtocolMessageType('StatSetting', (_message.Message,), {
  'DESCRIPTOR' : _STATSETTING,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.StatSetting)
  })
_sym_db.RegisterMessage(StatSetting)

StatSettingReply = _reflection.GeneratedProtocolMessageType('StatSettingReply', (_message.Message,), {
  'DESCRIPTOR' : _STATSETTINGREPLY,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.StatSettingReply)
  })
_sym_db.RegisterMessage(StatSettingReply)

StatsRequest = _reflection.GeneratedProtocolMessageType('StatsRequest', (_message.Message,), {
  'DESCRIPTOR' : _STATSREQUEST,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.StatsRequest)
  })
_sym_db.RegisterMessage(StatsRequest)

StatsReply = _reflection.GeneratedProtocolMessageType('StatsReply', (_message.Message,), {
  'DESCRIPTOR' : _STATSREPLY,
  '__module__' : 'session_pb2'
  # @@protoc_insertion_point(class_scope:session.StatsReply)
  })
_sym_db.RegisterMessage(StatsReply)

_SESSION = DESCRIPTOR.services_by_name['Session']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _REPLYHEADER._serialized_start=55
  _REPLYHEADER._serialized_end=99
  _SESSIONSREQUEST._serialized_start=101
  _SESSIONSREQUEST._serialized_end=182
  _SESSIONSREPLY._serialized_start=184
  _SESSIONSREPLY._serialized_end=289
  _KEY._serialized_start=291
  _KEY._serialized_end=382
  _SESSIONDATA._serialized_start=384
  _SESSIONDATA._serialized_end=471
  _SESSIONREQUEST._serialized_start=473
  _SESSIONREQUEST._serialized_end=516
  _SESSIONREPLY._serialized_start=518
  _SESSIONREPLY._serialized_end=606
  _RELATIONDATA._serialized_start=608
  _RELATIONDATA._serialized_end=680
  _RELATIONREQUEST._serialized_start=682
  _RELATIONREQUEST._serialized_end=726
  _RELATIONREPLY._serialized_start=728
  _RELATIONREPLY._serialized_end=819
  _RELATIONSREQUEST._serialized_start=821
  _RELATIONSREQUEST._serialized_end=882
  _RELATIONSREPLY._serialized_start=884
  _RELATIONSREPLY._serialized_end=992
  _STATSETTING._serialized_start=994
  _STATSETTING._serialized_end=1023
  _STATSETTINGREPLY._serialized_start=1025
  _STATSETTINGREPLY._serialized_end=1117
  _STATSREQUEST._serialized_start=1119
  _STATSREQUEST._serialized_end=1133
  _STATSREPLY._serialized_start=1136
  _STATSREPLY._serialized_end=1288
  _SESSION._serialized_start=1291
  _SESSION._serialized_end=1962
# @@protoc_insertion_point(module_scope)
