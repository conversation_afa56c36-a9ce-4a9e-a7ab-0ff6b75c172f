{
    "type": "object",
    "required": ["page"],
    "properties": {
        "createStartTime": {
            "type": "string",
            "default": "31507200000",
            "pattern": r"^\d{12,13}$"
        },
        "createEndTime": {
            "type": "string",
            "default": "253402271999000",
            "pattern": r"^\d{12,13}$"
        },
        "taskName": {
            "type": "string",
            "default": ""
        },
        "taskType": {
            "type": "string",
            "default": "",
            "enum": ["singleTask", "timedTask"]
        },
        "sort": {
            "type": "string",
            "default": "createTime",
            "enum": ["startTime", "createTime"]
        },
        "reverse": {
            "type": "string",
            "enum": ["true", "false"],
            "default": "true"
        },
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}