import { message } from "antd";
import { getPackage } from '@/services/workbench';
import {last} from 'lodash';
 const payloadFun =(type: string, value: any)=>{
    return[
    ['type', type],
    ['occurredTime', value.occurredTime+''],
    ['src_ip', value?.flow?.src_ip || value?.src_ip],
    ['dst_ip', value?.flow?.dst_ip || value?.dst_ip],
    ['replay', `${value.taskType === 'replay' ? true : false}`],
    ['dst_port', `${value?.flow?.dst_port || value?.dst_port}`],
    ['src_port', `${value?.flow?.src_port || value?.src_port}`],
    ['download', 1],
    ['proto', `${value?.flow?.proto || value?.proto}`],
    ['pcap_filename', value.pcap_filename],
    ['not_fss_pcapname', value.not_fss_pcapname],
];
 } 
export const getPackageFun=async(type: string, value: any)=>{
    const payload =payloadFun(type,value).reduce((obj,item)=>{
        obj[item[0]] = item[1]
        return obj;
    },{})
    delete payload.download;
    const res = await getPackage(payload);
    if (res.flag) {
     return res.data?.file_path;
    } else {
      message.error(res.message);
    }
}
export const downloadEvent = (type: string, value: any) => {
    const payload =payloadFun(type,value)
    fetch(`/mica-api/api/v1/package?${new URLSearchParams(payload)}`).then(async resp => {
        const contentType = resp.headers.get('content-type') || '';
        if (!resp.ok || contentType.toLowerCase()?.includes('application/json')) {
            const data = await resp.json().catch(() => ({}));
            message.error(data?.message || '下载失败');
        } else {
            const contentDisposition = resp.headers.get('content-disposition') || '';
            const filename = contentDisposition.match(/attachment; filename="?([^"]+)"?/)?.[1] || value.pcap_filename || value.not_fss_pcapname;
            let blob = await resp.blob();
            download(blob, filename, contentType)
        }
    });
};

export function download(content, filename = '', mime = 'text/plain') {
    const element = document.createElement('a');
    element.target = '_blank';

    let blob = content;
    if (Object.prototype.toString.call(content) !== '[object Blob]') {
        blob = new Blob(['\ufeff' + content]);
    }
    const dataUri = URL.createObjectURL(blob, { type: mime });
    element.setAttribute('href', dataUri);
    element.setAttribute('download', filename);

    element.style.display = 'none';
    document.body.appendChild(element);

    element.click();

    URL.revokeObjectURL(dataUri);
    document.body.removeChild(element);
}