import component from '@/locales/en-US/component';
import { defineConfig, utils } from 'umi';
import unoCSS from '@unocss/webpack';
import defaultSettings from './defaultSettings';

const { winPath } = utils;

const { primaryColor } = defaultSettings;

export default defineConfig({
  publicPath: '/mica/',
  runtimePublicPath: true,
  qiankun: {
    slave: {},
  },
  esbuild: {},
  nodeModulesTransform: {
    type: 'none',
    exclude: [],
  },
  antd: {},
  dva: {
    hmr: true,
  },
  locale: {
    default: 'zh-CN',
    antd: true,
    title: false,
    baseNavigator: true,
    baseSeparator: '-',
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index',
  },
  targets: {
    ie: 11,
  },
  history: { type: 'browser' },
  hash: true,
  // 路由配置
  routes: [
    {
      path: '/screen',
      component: './screen'
    },
    {
      name: 'business-system',
      path: '/',
      component: '../layouts/BasicLayout',
      routes: [
        {
          name: 'alarm',
          path: '/alarm',
          routes: [
            // 威胁告警
            {
              path: '/alarm',
              component: './alarm'
            },
            {
              path: '/alarm/loopHole',
              component: './alarm/alert/Vul',
            },
            {
              path: '/alarm/intelligence',
              component: './alarm/alert/Intel',
            },
            {
              path: '/alarm/modal',
              component: './alarm/alert/ModelView',
            },
            {
              path: '/alarm/file',
              component: './alarm/alert/FileView',
            },
            {
              path: '/alarm/domain',
              component: './alarm/domain',
            },
            {
              path: '/alarm/flow',
              component: './alarm/flow',
            },
          ]
        },
        {
          path: '/collect/download',
          component: './alarm/download',
        },
        // 恶意Ip
        {
          path: '/collect/maliceIP',
          component: './collect/maliceIP'
        },
       
        {
          path :'/customized/threatexpansion',
          component:'./customized/threatexpansion'
        },
        {
          path :'/customized/maliciousIP',
          component:'./customized/maliciousIP'
        },
        {
          path :'/customized/emailAnalysis',
          component:'./customized/emailAnalysis'
        },
        {
          path: '/customized/emailAnalysis/emailDetail',
          component: './customized/emailAnalysis/EmailDetail',
        },
        {
          path :'/customized/anomalyDetection',
          component:'./customized/anomalyDetection'
        },
        {
          path: '/task',
          component: './explore/task/index',
        },
        {
          path: '/dataSource',
          component: './explore/administration/index',
        },
        {
          path: '/exploreForm',
          component: './explore/task/addtask',
        },
        {
          path: '/exploreDetail',
          component: './explore/administration/detail',
        },
        // 分析取证
        {
          path: '/collect/logCollect',
          component: './collect',
        },
        {
          path: '/collect/pathAnalysis',
          component: './collect/pathAnalysis',
        },
        {
          path: '/collect/attacksAnalysis',
          component: './collect/attacksAnalysis',
        },
        {
          path: '/collect/fileAnalysis',
          component: './collect/fileAnalysis',
        },
        {
          path: '/collect/fileDetail',
          component: './collect/fileAnalysis/detail',
        },
        // 威胁报告
        {
          path: '/threatReportList',
          component: './threatReport',
        },
        // 周期报告设置
        {
          path: '/threatReportSetting',
          component: './threatReport/reportSetting',
        },
        // 资产画像
        {
          path: '/meansDetail',
          component: './means/means',
        },
        // 资产管理
        {
          path: '/meansManagement',
          component: './means/meansManagement',
        },

        // 模型管理
        {
          path: '/modelManagement',
          component: './knowledgeCenter/modelManagement',
        },
        // 情报管理
        {
          path: '/informationManagement',
          component: './knowledgeCenter/infoManagement',
        },
        // 规则管理
        {
          path: '/featureManagement',
          component: './knowledgeCenter/featureManagement',
        },
        // 基础配置
        {
          path: '/basics',
          component: './configs/basics',
        },
        // 接口管理
        {
          path: '/interfaceManagement',
          component: './configs/interfaceManagement',
        },
        {
          path: '/securityPolicyForm',
          component: './configs/securityPolicy/editForm',
        },
        // 安全策略
        {
          path: '/securityPolicy',
          component: './configs/securityPolicy',
        },
        // 白名单
        {
          path: '/whiteList',
          component: './configs/white',
        },
        // 硬件监控
        {
          path: '/hardwareMonitoring',
          component: './monitor/hardwareMonitoring',
        },
        // 流量监控
        {
          path: '/flowMonitoring',
          component: './monitor/flowMonitoring',
        },
        // 服务监控
        {
          path: '/serviceMonitoring',
          component: './monitor/serviceMonitoring',
        },
        {
          path: '/clusterManagement',
          component: './configs/clusterManagement',
        },
        // 安全报表
        {
          path: './report',
          component: './report',
        },
     
        {
          path: '/exception/403',
          component: './exception-403',
        },
        {
          path: '/exception/498',
          component: './exception-498',
        },
        {
          path: '/exception/500',
          component: './exception-500',
        },

        {
          component: './exception-404',
        },
      ],
    },
  ],
  // Theme for antd
  // https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': primaryColor,
  },
  ignoreMomentLocale: true,
  lessLoader: {
    javascriptEnabled: true,
    modifyVars: {
      // 'ant-prefix': 'box'
    },
  },
  cssLoader: {
    modules: {
      getLocalIdent: (
        context: {
          resourcePath: string;
        },
        _: string,
        localName: string,
      ) => {
        if (
          context.resourcePath.includes('node_modules') ||
          context.resourcePath.includes('global.less')
        ) {
          return localName;
        }

        const match = context.resourcePath.match(/src(.*)/);
        if (match && match[1]) {
          const antdProPath = match[1].replace('.less', '');
          const arr = winPath(antdProPath)
            .split('/')
            .map((a: string) => a.replace(/([A-Z])/g, '-$1'))
            .map((a: string) => a.toLowerCase());
          return `box-app-${arr.join('-')}-${localName}`.replace(/--/g, '-');
        }
        return localName;
      },
    },
  },
  autoprefixer: {
    flexbox: true,
  },
  manifest: {
    basePath: '/',
  },
  chainWebpack(memo, { env, webpack }) {
    memo.plugin('UnoCSS').use(unoCSS, [{}]);
  },
});
