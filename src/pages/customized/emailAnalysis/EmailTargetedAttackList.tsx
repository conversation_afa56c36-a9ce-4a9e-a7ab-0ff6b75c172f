import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import moment from 'moment';
import cs from 'classnames';
import { Button, Divider, Popover, Space, Table, Tag } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

import QueryBox from './QueryBox';
import { EmailAttachmentItem, EmailTargetedAttackListItem, TimeRange } from '../types';
import { useEmailTargetedAttackListData } from './hooks';
import { renderTags } from './EmailList';

export type Props = {
  timeRange: TimeRange
  onJumpSearch?: (key: string, params: any) => void
  style?: React.CSSProperties;
  className?: string;
}

function EmailTargetedAttackList({
  style,
  className,
  timeRange,
  onJumpSearch,
}: Props, ref: React.Ref<{ search?: (params: any) => void }>): JSX.Element {
  const controllerRef = useRef<AbortController>()

  const handleAbort = (controller: AbortController) => {
    controllerRef.current = controller
  }

  const {
    list,
    loading,
    pagination: paginationData,
    updatePagination,
    setTimeRange,
    setQueryParams,
  } = useEmailTargetedAttackListData(timeRange, handleAbort);

  const searchBoxRef = useRef<{ search: (params: any) => void }>(null)

  useEffect(() => {
    setTimeRange(timeRange)
  }, [timeRange])

  useImperativeHandle(ref, () => ({
    search: (params: any) => {
      controllerRef.current?.abort()
      searchBoxRef.current?.search(params)
    }
  }));

  const columns = [
    {
      title: "时间",
      width: 200,
      fixed: 'left',
      dataIndex: "timestamp",
      render: (t: number) => moment(t).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: "被攻击组织",
      width: 150,
      dataIndex: "rsv_org",
    },
    {
      title: '附件',
      width: 130,
      dataIndex: "filename",
      render: (t: string) => renderTags(t)
    },
    {
      title: "检测结果",
      width: 300,
      dataIndex: "tags",
      render: (tags: string[], { family }: EmailAttachmentItem) => {
        return <Space direction='vertical'>
          <Tag color='red'>{family}</Tag>
          {renderTags(tags, 6)}
        </Space>
      },
    },
    {
      title: "发件邮箱",
      width: 200,
      dataIndex: "from",
      render: renderTags
    },
    {
      title: "收件邮箱",
      width: 300,
      dataIndex: "to",
      render: (to: string[]) => renderTags(to, 4)
    },
    {
      title: "操作",
      width: 180,
      align: 'center',
      fixed: 'right',
      key: "action",
      render: (_: never, record: EmailTargetedAttackListItem) => {
        const { msg_id } = record
        return (
          <Space split={<Divider type="vertical" />}>
            <Button
              type='link'
              style={{ padding: 0 }}
              onClick={() => {
                onJumpSearch?.('emailList', { msg_id })
              }}
            >
              邮件列表
            </Button>
            <Button
              type='link'
              style={{ padding: 0 }}
              onClick={() => {
                onJumpSearch?.('emailAttachmentList', { msg_id })
              }}
            >
              附件列表
            </Button>
          </Space>
        );
      }
    }
  ];

  const totalInfoRender = (total: number) => (
    <span>
      <Popover
        content="最多展示10000条数据"
        title="说明"
        trigger="hover"
      >
        <InfoCircleOutlined className="pr-2" />
      </Popover>
      总计 {total} 条
    </span >
  );

  const pagination = {
    total: paginationData.total,
    showSizeChanger: true,
    showQuickJumper: true,
    current: paginationData.page,
    showTotal: totalInfoRender,
    pageSize: paginationData.pageSize,
    onChange: (page: number, pageSize: number) => updatePagination({ page, pageSize }),
  };

  return (
    <div style={style} className={cs(className)}>
      <QueryBox ref={searchBoxRef} listType="emailAttack" onSearch={setQueryParams} timeRange={timeRange} />
      <Table
        loading={loading}
        rowKey="uuid"
        columns={columns as any[]}
        scroll={{ x: 1200, }}
        dataSource={list}
        pagination={pagination}
      />
    </div>
  );
}

export default forwardRef(EmailTargetedAttackList);
