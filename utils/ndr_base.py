#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from flask_restful import Resource
from utils.database import MongoDB
from utils import database
from utils.mongo_template.get_mongo_template import MongoTemplate
from utils.es_template.get_es_template import ES_Template
from utils.utils import flask_response
from utils.logger import get_ndr_logger

MONGO_LOG = get_ndr_logger("mongo_log", __file__)


class NdrResource(Resource):
    '''NDR系统基类'''

    threat_level_reverse_template = {
        "Low": 0,
        "Medium": 1,
        "High": 2,
        "other": -1
    }
    threat_level_template = {
        0: "Low",
        1: "Medium",
        2: "High",
        -1: "other"
    }
    time_template = {
        "observed": "observedTime",
        "occurred": "occurredTime"
    }
    field_template = {
        "aptOrganization": "iocInfo.aptOrganization.keyword",
        "disclosuretime": "iocInfo.disclosuretime",
        "ip": "ip.attackIp",
        "ioc": "iocInfo.ioc",
        "attackIp": "ip.attackIp",
        "victimIp": "ip.victimIp",
        "taskId": "taskId",
        "sid": "iocInfo.sid",
        "country": "attackIpGeoip.country_name.keyword",
        "fileType": "filetype",
        "observed": "observedTime",
        "occurred": "occurredTime",
    }

    def __init__(self, **kwargs):
        for element in kwargs:
            method = getattr(self, "set_" + element, "")
            if callable(method):
                method(kwargs.get(element))

    # MongoDB数据库
    def set_mongo_db(self, db):
        self.db = MongoDB(db)

    # Mongo查询模板
    def set_mongo_template(self, mongo_template):
        self.mongo_template = MongoTemplate().read_template(mongo_template)

    # ES查询模板 & ES客户端
    def set_es_template(self, es_template):
        self.es_template = ES_Template().read_template(es_template)
        self.es_client = database.get_es_client()

    # 开始时间&结束时间校验
    def time_verify(self, **kwargs):
        start_time = int(kwargs["startTime"])
        stop_time = int(kwargs["stopTime"])
        if start_time >= stop_time:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})
        return True

    def get(self):
        pass

    def post(self):
        pass
