/*
 * @Author: 田浩
 * @Date: 2021-07-19 15:32:01
 * @LastEditors: 田浩
 * @LastEditTime: 2021-08-20 14:01:00
 * @Descripttion:
 */
const defaultState = {
  group: {
    groupId: '',
  },
}; //默认数据
export default (state = defaultState, action: { type: string; value: any }) => {
  //就是一个方法函数
  if (action.type === 'changeGroup') {
    let newState = JSON.parse(JSON.stringify(state));
    newState.group = action.value;
    return newState;
  }
  return state;
};
