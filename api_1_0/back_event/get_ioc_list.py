#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
""" 获取 IOC 告警列表 """
from flask import request
from api_1_0.utils import flask_log
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.es_data_format import es_data_format2, es_data_format
from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils.utils import flask_response
from utils.es_function import *
from utils import param_check
from config.config import IOC_INDEX, NdrLog
from utils.logger import get_ndr_logger
from utils.database import MongoDB

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)

field_temp = {
    "killchain": "killchain.keyword",
    "celeryId": "celeryId.keyword",
    "threatLevel": "threatLevel.keyword",
    "confirm": "confirm"
}


def format_es_temp(option_args, es_template):
    """
    :param option_args:
    :return:
    """
    es_must_list = []
    for key, value in option_args.items():
        if value in ["", None]:
            continue
        if key == "mbinfo":
            query = get_mbinfo(option_args["mbinfo"])
            es_template["query"]["bool"]["must"].append(query)
        elif key == "threatScore":
            threat_score_start, threat_score_stop = value.split('-')
            es_template["query"]["bool"]["must"].append(
                get_range("threatScore", int(threat_score_start), int(threat_score_stop)))
        elif key == "threatType":
            query = get_contain(key, value)
            es_template["query"]["bool"]["must"].append(query)
        else:
            search_args = {field_temp[key]: value}
            args_format = {"term": search_args}
            es_must_list.append(args_format)
    es_template["query"]["bool"]["must"].extend(es_must_list)
    return es_template


class IocListResource(NdrResource):
    """ 获取情报列表 """

    threat_type_list = [
        {
            "threat_type": "APT攻击",
            "describe": "某APT组织对主机展开的持续有效的攻击活动。",
            "advice": "进行事件响应与取证分析，上报监管部门进行封堵。"
        },
        {
            "threat_type": "远控木马",
            "describe": "特洛伊木马（Trojan Horse）简称木马，在计算机领域中指的是一种后门程序，是黑客用来窃取用户资料，"
                        "甚至是远程控制对方的电子设备的恶意程序。和病毒相似，木马程序有很强的隐秘性，会随著操作系统启动而启动。",
            "advice": "根据客户的具体需求判定是否要进行封禁查杀拦截等处理。"
        },
        {
            "threat_type": "挖矿木马",
            "describe": "该IOC是一个矿池地址或者矿池的主站点。由于Minerd等挖矿木马会连接矿池挖矿、或者是部分流氓应用会静默挖矿，"
                        "触发该IOC告警，表示企业内部可能感染了Minerd等挖矿木马，或者是有人利用公司资源进行挖矿。",
            "advice": "先排除是否为内部有员工通过浏览器访问的情况，或者根据自身业务判断是否需要对本次告警进行处理。"
        },
        {
            "threat_type": "网络钓鱼",
            "describe": "通过构造与某一目标网站高度相似的页面诱骗用户的攻击方式。钓 鱼网站是网页仿冒的一种常见形式，"
                        "常以垃圾邮件、即时聊天、手机短信或网页虚 假广告等方式传播，用户访问钓鱼网站后可能泄露账号、密码等个人隐私。",
            "advice": "部署邮件网关+AV用来检测邮件附件以及链接，部署终端安全防护设备，检测下载的附件安全性以及邮件中链接的恶意性，提高员工安全意识"
        },
        {
            "threat_type": "僵尸网络",
            "describe": "僵尸程序是用于构建大规模攻击平台的恶意程序。按照使用的通信协议，僵尸 程序可进一步分为IRC僵尸程序、"
                        "HTTP僵尸程序、P2P僵尸程序和其他僵尸程序 4类。",
            "advice": "关闭不必要的对外端口，及时更新安全补丁，进行员工安全培训，定期进行网络脆弱评估与加固等，在被僵尸网络病"
                      "毒感染前做好事前措施。部署安全设备持续监控受保护的网络，发现潜在的僵尸网络病毒，进行事件响应与取证分析，上报监管部门进行封堵。"
        },
        {
            "threat_type": "后门软件",
            "describe": "主要提供后门软件相关的IP等数据，可以用来在网络中检测和发现可疑的被控端线索，恶意软件通常在被植入后"
                        "需要和服务端进行通信，通过这些线索来发现网络中那台主机可能感染了后门软件。",
            "advice": "杀毒软件查杀。"
        },
        {
            "threat_type": "漏洞利用",
            "describe": "通过网络上已公布的漏洞进行攻击，使用漏洞攻击通常会造成数据窃取，拒绝服务，获取服务器控制权限。"
                        "攻击者通常通过漏洞发布平台（exploit-db，乌云，补天，CVE等）获取漏洞的利用手段进行攻击。",
            "advice": "关注CVE平台，微软官方网站，漏洞发布之后及时更新软件/系统版本，或者安装漏洞补丁"
        },
        {
            "threat_type": "勒索软件",
            "describe": "勒索软件是黑客用来劫持用户资产或资源并以此为条件向用户勒索钱财的一种 恶意软件。勒索软件通常会将用"
                        "户数据或用户设备进行加密操作或更改配置，使之 不可用，然后向用户发出勒索通知，要求用户支付费用以获"
                        "得解密密码或者使系统 恢复正常运行的方法。",
            "advice": "1、停用系统并还原；2、执行恶意软件防护软件来扫瞄并清除勒索软件的相关文件；3、某些勒索软件变种需要一些"
                      "额外的清除步骤，请务必遵照所有必要的步骤来彻底清除您计算机所感染的特定勒索软件。"
        },
        {
            "threat_type": "蠕虫病毒",
            "describe": "蠕虫是指能自我复制和广泛传播，以占用系统和网络资源为主要目的的恶意程 序。按照传播途径，蠕虫可进一步"
                        "分为邮件蠕虫、即时消息蠕虫、U盘蠕虫、漏洞 利用蠕虫和其他蠕虫5类。",
            "advice": "及时修复网络中机器的漏洞，做好安全防御措施。监管部门及时发现，快速阻断。"
        },
        {
            "threat_type": "普通木马",
            "describe": "网络中的设备被植入木马，通过C2通道回连攻击者的主控端，在攻击者的主控端来看，可以通过木马自身的功能对"
                        "植入木马的受害机器进行操作。包括窃取资料、长期监控甚至在判断所控制的机器的价值后，将其作为跳板机器"
                        "在受害者的网络中进行内网横向渗透，扩大攻击战果，这种攻击的影响非常深远。",
            "advice": "部署安全设备（IDS/IPS/防火墙/SIEM/SOC/AV等）持续检测，安全运维人员持续监控并上报异常"
        },
        {
            "threat_type": "中转节点",
            "describe": "匿名网络洋葱路由（The Onion Router）的节点，虚拟专网（Virtual Private Network）节点，指代理软件"
                        "或代理服务器，也可以认为是一种网络访问方式，用来隐藏访问者的身份信息。",
            "advice": "根据客户的具体需求判定是否要进行封禁查杀拦截等处理。"
        },
        {
            "threat_type": "恶意广告",
            "describe": "广告流氓程序主要指在用户未感知的情况下进行静默下载安装推广软件、锁定主页、劫持流量的等等行为，"
                        "并用以获取推广收入分成的恶意代码。",
            "advice": "根据客户的具体需求判定是否要进行封禁查杀拦截等处理。"
        },
        {
            "threat_type": "恶意软件",
            "describe": "恶意软件是一种秘密植入用户系统借以盗取用户机密信息，破坏用户软件和操作系统或是"
                        "造成其它危害的一种网络程序。",
            "advice": "根据客户的具体需求判定是否要进行封禁查杀拦截等处理。"
        }
    ]

    def __init__(self):
        super(IocListResource, self).__init__(es_template="ioc_list")
        self.mongodb = MongoDB("ndr")

    @param_check.check_flask_args(Validator('ioc_list'), request)
    def post(self, **kwargs):
        index = IOC_INDEX
        if kwargs["groupKey"] == "uniqueId":
            index += "-agg"
            kwargs["groupKey"] = ""

        es_index = get_es_index(index, int(kwargs["startTime"]), int(kwargs["stopTime"]))
        es_index_list = []
        es_index_not = []
        for i in es_index:
            # 判断是否存在索引
            if self.es_client.indices.exists(i):
                es_index_list.append(i)
            else:
                es_index_not.append(i)
        if not es_index_list:
            return flask_response('index[%s] not exist' % str(es_index_not), True, {})
        option_args = {
            "celeryId": kwargs["celeryId"],
            "threatLevel": kwargs["threatLevel"],
            "threatScore": kwargs["threatScore"],
            "killchain": kwargs["killchain"],
            "mbinfo": kwargs["mbinfo"],
            "threatType": kwargs["threatType"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        try:
            if not self.es_client.ping():
                flask_log.ndr_log_to_box(NdrLog.Type.RUN, event='Elasticsearch服务连接失败！')
                return flask_response('Elasticsearch服务连接失败！', False, {})
            res = self.es_client.search(index=es_index_list, body=es_template, request_timeout=60)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报告警获取失败")
            return flask_response(message, False, {})
        try:
            data = self.data_format(kwargs["groupKey"], res)
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报告警获取成功")
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报告警获取失败")
            return flask_response(err_info, False, {})

    def data_format(self, group_key, es_data):
        if not group_key:
            total = es_data["hits"]["total"]["value"]
            ioc_data_list = es_data["hits"]["hits"]
        else:
            total = es_data["aggregations"]["_count"]["value"]
            ioc_data_list = es_data["aggregations"]["group_id"]["buckets"]
        count = total
        if total > 10000:
            total = 10000
            ioc_data_list = ioc_data_list[:total]
        data_return = {
            "total": total,
            "real_count": count,
            "alerts": []
        }
        if not group_key or group_key == "uniqueId":
            for ioc_info_data in ioc_data_list:
                each_ioc_dict = es_data_format2(group_key, ioc_info_data, self.mongodb)
                for i in self.threat_type_list:
                    if each_ioc_dict["threatType"].__contains__(i["threat_type"]):
                        if not each_ioc_dict["describe"].__contains__(i["describe"]):
                            each_ioc_dict["describe"] += i["describe"]
                            each_ioc_dict["advice"] += i["advice"]
                data_return["alerts"].append(each_ioc_dict)
        else:
            for ioc_info_data in ioc_data_list:
                each_ioc_dict = es_data_format(group_key, "ioc", ioc_info_data)
                data_return["alerts"].append(each_ioc_dict)
        return data_return


class IocProto(NdrResource):
    """ 获取情报协议列表 """

    def get(self):
        return flask_response("", True, ["http", "dns", "ftp", "ssl", "smtp", "weird", "dpd", "tcp", "udp"])
