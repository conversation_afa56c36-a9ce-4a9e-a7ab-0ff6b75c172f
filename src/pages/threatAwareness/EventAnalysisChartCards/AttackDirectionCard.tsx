import React, { useEffect } from 'react';
import { DashCard } from '../DashCard';
import ReactECharts from 'echarts-for-react';
import { TimeRange } from '../UnSettledThreat/HighRiskHostCard';
import { useAttackDirectionData } from '../hooks';
import { Empty } from 'antd';

export type Props = {
  style?: React.CSSProperties;
  className?: string;
  timeRange: TimeRange;
}

function AttackDirectionCard({ timeRange }: Props): JSX.Element {

  const { loading, data, setTimeRange } = useAttackDirectionData(timeRange)

  const totalCount = data.reduce((acc, { value }) => acc + value, 0)

  useEffect(() => {
    setTimeRange(timeRange);
  }, [timeRange]);

  const option = {
    title: {
      text: totalCount,
      subtext: '事件总量',
      left: 'center',
      top: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      icon: 'circle',
      right: 'center',
      top: '8%',
      itemWidth: 10,
      itemHeight: 10,
      width: '100%',
      selectedMode: false,
      formatter: (n: string) => {
        const item: any = data.find(({ name }) => name === n);
        return `${n} [${((item?.value! / totalCount) * 100).toFixed(2)}%]`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['50%', '55%'],
        data: data,
        label: {
          show: false,
        },
      },
    ]
  };


  return (
    <DashCard title="攻击方向">
      {
        data.length || loading ? (
          <ReactECharts
            showLoading={loading}
            option={option}
            notMerge={true}
            style={{ width: '100%', height: 400 }}
            opts={{ renderer: 'svg' }}
          />
        ) : (<Empty />)
      }

    </DashCard>
  );
}

export default AttackDirectionCard;
