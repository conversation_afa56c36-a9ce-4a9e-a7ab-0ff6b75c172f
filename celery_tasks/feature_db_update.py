# -*- coding: utf-8 -*-
# @Time    : 2019-05-05 14:10
# <AUTHOR> hachi
# @File    : api_feature_manager.py
# @Software: PyCharm

import os
import time
import datetime
import shutil
import sqlite3
import hashlib

import pymongo

from celery_tasks import app
from config.config import SystemFeaturePath, KnowledgeNDR, NdrLog
from celery_tasks.feature_activate_task import periodic_task
from utils.logger import get_ndr_logger, LogToDb
from utils.database import MongoDB
from utils.cluster import *

run_log = get_ndr_logger(NdrLog.Type.RUN, __file__)


def create_group_id():
    """
    :return: group id
    """
    md5_str = hashlib.md5(str(time.clock()).encode('utf-8'))
    return md5_str.hexdigest()[16:24]


@app.task()
def db_update_feature(update_all, name, update_mode, rule_version, version, start_time):
    """
    db 方式更新规则库和知识库
    :return:
    """
    # 获取规则组配置
    default_rule_en = SystemFeaturePath.DEFAULT_RULE_GROUP_EN
    default_rule_cn = SystemFeaturePath.DEFAULT_RULE_GROUP_CN
    try:
        db = MongoDB("ndr")
        user_enable_sid_list = []
        user_disable_sid_list = []
        collections = db.list_collection()
        # 升级保持用户操作的启用/禁用状态
        if "feature" in collections:
            feature_user_disable = db.find("feature", {"isCustomer": False,
                                                       "featureStatus": "disable",
                                                       "control": "user"}, {"sid": 1})
            for user_feature in feature_user_disable:
                user_disable_sid_list.append(user_feature["sid"])
            feature_user_enable = db.find("feature", {"isCustomer": False,
                                                      "featureStatus": "enable",
                                                      "control": "user"}, {"sid": 1})
            for user_feature in feature_user_enable:
                user_enable_sid_list.append(user_feature["sid"])
        if update_all:  # 全量升级
            if os.path.exists(KnowledgeNDR.Path + "knowledge_feature.db"):
                shutil.move(KnowledgeNDR.Path + "knowledge_feature.db", KnowledgeNDR.Path + "old_feature.db")
                shutil.chown(KnowledgeNDR.Path + "old_feature.db", 'kslab', 'kslab')
            shutil.copyfile(KnowledgeNDR.TempPath + "knowledge_feature.db",
                            KnowledgeNDR.Path + "knowledge_feature.db")
            # 保留已有的特征组不变
            old_sid_grp_dict = {}
            old_feature_grp_lis = db.find("feature",
                                          {"$and": [{"isCustomer": False},
                                                    {"$where": "(this.featureGroup.length > 1)"}]},
                                          {"featureGroup": 1, "sid": 1})
            for old_feature_grp in old_feature_grp_lis:
                old_group_list = old_feature_grp["featureGroup"]
                old_sid_grp_dict[old_feature_grp["sid"]] = set(old_group_list)
            conn = sqlite3.connect(KnowledgeNDR.Path + "knowledge_feature.db")
            cursor = conn.cursor()
            res = cursor.execute(
                "SELECT sid, author, vulName, vulType, cve, lockheedKillchainStage, lockheedKillchainCN,"
                " lockheedKillchainEN, threatFlag, alterInfo, submitTime, is0day, ruleContent, attackIp,"
                " victimIp, threatScore, appProto, status from vul_rules")
            insert_mg = data_format(res, default_rule_en, user_enable_sid_list, user_disable_sid_list,
                                    old_sid_grp_dict)

            if insert_mg:
                db.delete("feature", {"isCustomer": False})
                db.insert_many("feature", insert_mg)

            # 将用户自定义的规则插入到新的规则库
            if os.path.exists(KnowledgeNDR.Path + "old_feature.db"):
                conn_old = sqlite3.connect(KnowledgeNDR.Path + "old_feature.db")
                cursor_old = conn_old.cursor()
                custom_res = cursor_old.execute("SELECT * from vul_rules where isCustomer=1")
                for row in custom_res:
                    cursor.execute("insert into vul_rules values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", row)
                conn.commit()
                cursor_old.close()
                conn_old.close()
            cursor.close()
            conn.close()
            os.system("rm " + KnowledgeNDR.Path + "old_feature.db")
        else:  # 增量升级，如果要更新已有的特征（先删除，在插入，只保持用户禁用/启用状态），新增的特征直接插入，
            old_sid_dict = {}
            # 更新已有特征的特征组不变
            add_sid = []
            # 读取增量库数据
            conn = sqlite3.connect(KnowledgeNDR.TempPath + "knowledge_feature.db")
            cursor = conn.cursor()
            res = cursor.execute(
                "SELECT sid, author, vulName, vulType, cve, lockheedKillchainStage, lockheedKillchainCN,"
                " lockheedKillchainEN, threatFlag, alterInfo, submitTime, is0day, ruleContent, attackIp,"
                " victimIp, threatScore, appProto, status from vul_rules")
            res = list(res)
            for row in res:
                add_sid.append(row[0])
            user_feature_grp_list = db.find("feature", {"sid": {"$in": add_sid}}, {"featureGroup": 1, "sid": 1})

            for user_feature_grp in user_feature_grp_list:
                old_sid_dict[user_feature_grp["sid"]] = set(user_feature_grp["featureGroup"])
            insert_mg = data_format(res, default_rule_en, user_enable_sid_list, user_disable_sid_list,
                                    old_sid_dict)
            update_mg = []
            for sid_data in insert_mg:
                update_mg.append(pymongo.ReplaceOne({"sid": sid_data["sid"]}, sid_data, upsert=True))
            if update_mg:  # 批量更新mongo
                db.bulk_write("feature", update_mg)
            default_conn = sqlite3.connect(KnowledgeNDR.Path + "knowledge_feature.db")
            default_cursor = default_conn.cursor()
            res = cursor.execute("SELECT * from vul_rules")
            for row in res:
                default_cursor.execute("replace into vul_rules values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
                                       row)
            default_conn.commit()
            default_conn.close()
            conn.close()

        cluster_sync_file(KnowledgeNDR.Path + "knowledge_feature.db")

        # 更新特征版本
        db.update("knowledge_config", {"rule_version": rule_version}, {"rule_version": version})
        # 生成规则组
        create_rule_grp(db, default_rule_cn, default_rule_en)
        # 执行底层规则文件
        periodic_task.delay(upgrade=True)
        LogToDb().log_to_db("rule_log", name, update_mode, start_time, int(time.time()), rule_version, version,
                            "success")
    except Exception as error:
        run_log.error(error)
        LogToDb().log_to_db("rule_log", name, update_mode, start_time, int(time.time()), rule_version, version,
                            "failed")


def data_format(rows, default_rule_en, user_enable_sid_list, user_disable_sid_list, old_sid_dict):
    insert_mg = []
    for row in rows:
        sid = row[0]
        feature_status = row[17]
        control = "init"
        threat_flag = row[8]
        feature_group = {default_rule_en["default_allRules"]}
        if sid in user_enable_sid_list:
            feature_status = "enable"
            control = "user"
        if sid in user_disable_sid_list:
            feature_status = "disable"
            control = "user"
        if sid in old_sid_dict.keys():
            feature_group = old_sid_dict[sid]
        if threat_flag == "APT":
            feature_group.add(default_rule_en["default_apt_attack"])
        elif threat_flag == "Exploits and Attacks":
            feature_group.add(default_rule_en["default_exploit_attack"])
        elif threat_flag in ["Trojan", "Webshell", "Malicious Host", "Spyware"]:
            feature_group.add(default_rule_en["default_remote_ctrl"])
        elif threat_flag in ["Malware", "Botnet", "Mining", "Ransomware"]:
            feature_group.add(default_rule_en["default_malware"])
        feature = {
            "sid": sid,
            "author": row[1],
            "vulName": row[2],
            "vulType": row[3],
            "cve": row[4],
            "lockheedKillchainStage": row[5],
            "lockheedKillchainCN": row[6],
            "lockheedKillchainEN": row[7],
            "threatFlag": threat_flag,
            "alterInfo": row[9],
            "submitTime": row[10],
            "is0day": row[11],
            "featureContent": row[12],
            "attackIp": row[13],
            "victimIp": row[14],
            "threatScore": row[15],
            "appProto": row[16],
            "updateTime": time.strftime("%Y-%m-%d %H:%M:%S"),
            "featureStatus": feature_status,
            "isCustomer": False,
            "featureGroup": list(feature_group),
            "control": control
        }
        insert_mg.append(feature)
    return insert_mg


def create_rule_grp(db, default_rule_cn, default_rule_en):
    # 生成/更新规则组
    user_id = '8543a91c8a2383dd1327a55c7154e25b'
    user_name = 'admin'
    for grp_name_en, grp_id in default_rule_en.items():
        group = db.find_one('feature_group', {"groupName": grp_name_en})
        if group:
            db.update_many('feature_group', {"groupName": grp_name_en},
                           {"createTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
        else:
            body = {
                "groupName": grp_name_en,  # 特征组名称
                "description": default_rule_cn[grp_name_en],  # 描述
                "attribute": "system",  # 属性
                "groupId": grp_id,  # 特征组 ID
                "createTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 创建时间
                "createByUserId": user_id,
                "createByUserName": user_name
            }
            db.insert_one("feature_group", body)
            run_log.info("group: %s, %s created success" % (body["groupName"], body["groupId"]))
