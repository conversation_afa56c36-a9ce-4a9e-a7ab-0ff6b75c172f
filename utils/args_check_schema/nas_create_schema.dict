{
    "type":"object",
    "required": ["serverType", "host"],
    "properties":{
        "serverType": {
            "type": "string",
            "default": "",
            "pattern": r"^(ftp|nas)$"
        },
        "host": {
            "type": "string",
            "default": ""
        },
        "port": {
            "type": "number",
            "default": ""
        },
        "filename": {
            "type": "string",
            "default": "/"
        },
        "username": {
            "type": "string",
            "pattern": r"^[A-Za-z0-9_\-\u4e00-\u9fa5]{1,64}$"
        },
        "password": {
            "type": "string",
            "default": ""
        },
        "enable": {
            "type": "boolean",
            "default": False,
            "enum": [True, False]
        },
        "aliasName": {
            "type": "string",
            "default": ""
        },
        "serverId": {
            "type": "string",
            "default": ""
        }
    }
}
