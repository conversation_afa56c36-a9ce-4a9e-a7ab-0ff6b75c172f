import React, { useState, useEffect } from 'react';
import { Form, Select, Row, Col, Collapse, message, Button, Spin } from 'antd';
import { getTaskName } from '@/services/common';
import { getFlowList, getAloneChartData } from '@/services/alarm';
import style from './index.less';
const echarts = require('echarts');
const { Panel } = Collapse;
const { Option } = Select;
const Flow = (props: any) => {
  const { form } = props;
  const { getFieldDecorator } = form;
  const [taskList, settaskList] = useState([]);
  const [alone, setalone] = useState(false);
  const [celeryId, setceleryId] = useState('');
  const [loading, setloading] = useState(false);
  const [tableData, settableData] = useState({ ts: '', ip_list: [] });
  const [tableData1, settableData1] = useState({ ts: '', ip_list: [] });
  const [tableData2, settableData2] = useState({ ts: '', ip_list: [] });
  const [tableData3, settableData3] = useState({ ts: '', ip_list: [] });
  const [tableData4, settableData4] = useState({ ts: '', ip_list: [] });
  // 搜索表单
  const onSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    form.validateFields((err: object, value: { celeryId: string }) => {
      if (!err) {
        if (value.celeryId !== undefined) {
          setloading(true);
          getFlowList(value).then(res => {
            if (res.flag) {
              setloading(false);
              setalone(false);
              settableData(res.data[0]);
              settableData1(res.data[1]);
              settableData2(res.data[2]);
              settableData3(res.data[3]);
              settableData4(res.data[4]);
              // initChart();
            } else {
              message.error(res.message);
            }
          });
        }
      }
    });
  };
  useEffect(() => {
    getTaskList();
  }, []);
  useEffect(() => {
    initChart();
  }, [tableData]);
  useEffect(() => {
    initChart1();
  }, [tableData1]);
  useEffect(() => {
    initChart2();
  }, [tableData2]);
  useEffect(() => {
    initChart3();
  }, [tableData3]);
  useEffect(() => {
    initChart4();
  }, [tableData4]);
  const getTaskList = () => {
    getTaskName({}).then(res => {
      if (res.flag) {
        let list = res.data;
        list.push({ taskName: '实时任务', celeryId: '' });
        settaskList(list);
      } else {
        message.error(res.message);
      }
    });
  };
  const initChart = () => {
    const myChart = echarts.init(document.getElementById('main'));
    // if(Object.keys(tableData).length===0) {
    //   return
    // }
    if (tableData) {
      myChart.setOption({
        title: { text: tableData.ts },
        tooltip: {},
        xAxis: {
          data: tableData.ip_list
            ? tableData.ip_list.map((item: any) => {
                return item.ip;
              })
            : [],
        },
        yAxis: {},
        series: [
          {
            name: '流量/M',
            type: 'bar',
            data: tableData.ip_list
              ? tableData.ip_list.map((item: any) => {
                  return item.total;
                })
              : [],
          },
        ],
      });
      myChart.on('click', (params: { name: any }) => {
        getAloneChart(tableData, params.name);
      });
    }
  };
  const initChart1 = () => {
    if (tableData1) {
      const myChart1 = echarts.init(document.getElementById('main1'));
      myChart1.setOption({
        title: { text: tableData1.ts },
        tooltip: {},
        xAxis: {
          data: tableData1.ip_list
            ? tableData1.ip_list.map((item: { ip: any }) => {
                return item.ip;
              })
            : [],
        },
        yAxis: {},
        series: [
          {
            name: '流量/M',
            type: 'bar',
            data: tableData1.ip_list
              ? tableData1.ip_list.map((item: { total: any }) => {
                  return item.total;
                })
              : [],
          },
        ],
      });
      myChart1.on('click', (params: { name: any }) => {
        getAloneChart(tableData1, params.name);
      });
    }
  };
  const initChart2 = () => {
    if (tableData2) {
      const myChart2 = echarts.init(document.getElementById('main2'));
      myChart2.setOption({
        title: { text: tableData2.ts },
        tooltip: {},
        xAxis: {
          data: tableData2.ip_list
            ? tableData2.ip_list.map((item: any) => {
                return item.ip;
              })
            : [],
        },
        yAxis: {},
        series: [
          {
            name: '流量/M',
            type: 'bar',
            data: tableData2.ip_list
              ? tableData2.ip_list.map((item: { total: any }) => {
                  return item.total;
                })
              : [],
          },
        ],
      });
      myChart2.on('click', (params: { name: any }) => {
        getAloneChart(tableData2, params.name);
      });
    }
  };
  const initChart3 = () => {
    if (tableData3) {
      const myChart3 = echarts.init(document.getElementById('main3'));
      myChart3.setOption({
        title: { text: tableData3.ts },
        tooltip: {},
        xAxis: {
          data: tableData3.ip_list
            ? tableData3.ip_list.map((item: any) => {
                return item.ip;
              })
            : [],
        },
        yAxis: {},
        series: [
          {
            name: '流量/M',
            type: 'bar',
            data: tableData3.ip_list
              ? tableData3.ip_list.map((item: any) => {
                  return item.total;
                })
              : [],
          },
        ],
      });
      myChart3.on('click', (params: { name: any }) => {
        getAloneChart(tableData3, params.name);
      });
    }
  };
  const initChart4 = () => {
    if (tableData4) {
      const myChart4 = echarts.init(document.getElementById('main4'));
      myChart4.setOption({
        title: { text: tableData4.ts },
        tooltip: {},
        xAxis: {
          data: tableData4.ip_list
            ? tableData4.ip_list.map((item: any) => {
                return item.ip;
              })
            : [],
        },
        yAxis: {},
        series: [
          {
            name: '流量/M',
            type: 'bar',
            data: tableData4.ip_list
              ? tableData4.ip_list.map((item: any) => {
                  return item.total;
                })
              : [],
          },
        ],
      });
      myChart4.on('click', (params: { name: any }) => {
        getAloneChart(tableData4, params.name);
      });
    }
  };
  const getAloneChart = (data: any, ip: any) => {
    if (data.ts) {
      setalone(true);
      let timeArr = [`${data.ts} 00:00:00`, `${data.ts} 23:59:59`];
      let _params = {
        celeryId: celeryId,
        startTime: timeArr[0],
        endTime: timeArr[1],
        ip: ip,
      };
      getAloneChartData(_params).then(res => {
        if (res.flag) {
          const myChart = echarts.init(document.getElementById('alone'));
          myChart.setOption({
            title: { text: ip },
            tooltip: {},
            xAxis: {
              data:
                res.data.map((item: any) => {
                  return item.ip;
                }) || [],
            },
            yAxis: {},
            series: [
              {
                name: '流量/M',
                type: 'bar',
                data:
                  res.data.map((item: any) => {
                    return item.total;
                  }) || [],
              },
            ],
          });
          //   });
        } else {
          message.error(res.message);
        }
      });
    }
  };
  const changeTask = (value: string) => {
    setceleryId(value);
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form className="searchForm" onSubmit={onSubmit} layout="inline">
            <Row>
              <Col span={6}>
                <Form.Item label="任务名称">
                  {getFieldDecorator('celeryId', {
                    // rules: [
                    //   {
                    //     required: true,
                    //     message: '请选择任务名称!',
                    //   },
                    // ],
                  })(
                    <Select onChange={changeTask}>
                      {taskList.map((item: any) => {
                        return (
                          <Option key={item.celeryId} value={item.celeryId}>
                            {item.taskName}
                          </Option>
                        );
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Button
                  className={style.searchBtn}
                  type="primary"
                  htmlType="submit"
                >
                  搜索
                </Button>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <div className={style.contentBox}>
        {alone ? (
          <div>
            {/* <p className={style.title}>{ip}</p> */}
            <div className={style.chartBox} id="alone"></div>
          </div>
        ) : (
          <Spin tip="加载中" spinning={loading}>
            <div className={style.group}>
              <div className={style.chartBox} id="main"></div>
              <div className={style.chartBox} id="main1"></div>
              <div className={style.chartBox} id="main2"></div>
              <div className={style.chartBox} id="main3"></div>
              <div className={style.chartBox} id="main4"></div>
            </div>
          </Spin>
        )}
      </div>
    </div>
  );
};
export default Form.create()(Flow);
