# -*-coding=utf-8-*-
from __future__ import absolute_import
from datetime import timedelta
from celery.schedules import crontab

from config.config import RedisConfig, MongoDBConfig

CELERY_TIMEZONE = 'Asia/Shanghai'
# BROKER_URL = "redis://" + RedisConfig.Host + ":" + str(RedisConfig.Port) + "/3"
BROKER_URL = "redis://:" + RedisConfig.Password + '@' + RedisConfig.Host + ":" + str(RedisConfig.Port) + "/3"
CELERY_RESULT_BACKEND = "redis://:" + RedisConfig.Password + '@' + RedisConfig.Host + ":" + str(RedisConfig.Port) + "/4"
# CELERY_RESULT_BACKEND = "redis://" + RedisConfig.Host + ":" + str(RedisConfig.Port) + "/4"
RESULT_SERIALIZER = 'json'
ACCEPT_CONTENT = ['json']
CELERY_TASK_RESULT_EXPIRES = 24 * 60 * 60
CELERY_MAX_TASKS_PER_CHILD = 40
# 设置worker并发数，默认为cpu的数量
CELERYD_CONCURRENCY = 16
CELERY_IMPORTS = ("celery_tasks.feature_activate_task",
                  "celery_tasks.explore_task",
                  "celery_tasks.feature_db_update",
                  "celery_tasks.get_replay_package",
                  "celery_tasks.get_assets_discovery",
                  "celery_tasks.assets_update",
                  "celery_tasks.rule_alarm_deal",
                  "celery_tasks.cert_analyis_task"
                  )
CELERYBEAT_SCHEDULE = {
    # 'every-60-seconds': {
    #     'task': 'celery_tasks.get_event_metadata.get_event_meta',
    #     # 配置计划任务的执行时间，这里是每60秒执行一次
    #     'schedule': crontab(minute='*/1'),
    #     'args': ('minutes', '', '')
    # },
    'timed-schedule-task-poll': {
        'task': 'celery_tasks.explore_task.timed_task_poll',
        # 轮询是定时任务是否到执行时间，这里是每60秒轮询一次
        'schedule': crontab(minute='*/1'),
        'args': ()
    },
    # 'every-180-seconds': {
    #     'task': 'celery_tasks.get_assets_discovery.get_assets_info',
    #     # 轮询是定时任务是否到执行时间，这里是每180秒轮询一次
    #     'schedule': 180.0,
    #     'args': ()
    # },
    # 'every-300-seconds': {
    #     'task': 'celery_tasks.assets_update.assets_update_min',
    #     'schedule': 300.0,
    #     'args': ()
    # },
    'rule_alarm_deal': {
        'task': 'celery_tasks.rule_alarm_deal.rule_deal',
        'schedule': crontab(minute=0, hour=0),  # 每天0点0分 执行一次任务
        'args': ()
    },
    # 每10s刷新回放任务结果
    'replay-task-result-update': {
        'task': 'celery_tasks.explore_task.replay_task_result_update',
        'schedule': 10.0,
        'args': ()
    },
    # 每20s刷新文件检测结果
    'file_task_result_update': {
        'task': 'celery_tasks.explore_task.file_task_result_update',
        'schedule': 20.0,
        'args': ()
    }
}
