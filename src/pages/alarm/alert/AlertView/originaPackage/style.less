.originaPackage{
  :global {
    .ant-tabs-nav-wrap {
      justify-content: center;
    }
    .ant-table-thead > tr > th, .ant-table-tbody > tr > td, .ant-table tfoot > tr > th, .ant-table tfoot > tr > td{
      padding: 8px 8px !important;
    }
    .ant-checkbox-wrapper{
      margin: -3px !important;
    }
    tr.ant-table-measure-row{
      visibility: collapse;
    }
  }
}

.dataPack{
  .tableRow0{
    width: 40px;
  }
  .detailContent{
    margin-top: 0.5rem;
    display: flex;
    justify-content: space-between;

    &>div{
      width: 49%;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      overflow: auto;
    }
    .detailBox{
      display: flex;
      padding: 0.5rem;
      overflow: auto;
      .header{
        height: 36px;
        margin-top: 3px;
        color: #000;
        display: flex;
        align-items: center;
      }
      .offset{
        padding: 0 8px;
        color: #999;
        background: #eee;
        margin-right: 1rem;
        height: min-content;
      }
      .hexString{
        font-family: monospace;
        flex-shrink: 0;
        min-width: 400px;
      }
      i{
        display: inline-block;
        font-style: normal;
        text-align: center;
        line-height: 1.5;
      }
      .decode{
        margin-left: 2rem;
        word-break: break-all;
        min-width: 220px;
      }
    }
  }
}
.dataContent{
  code{
    line-height: 1.5;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    word-break: break-all;
    color: #850b0b;
    background: rgb(251,237,237);
  }
  .cStr{
    color: #00007f;
    background: rgb(237,237,251);
  }
}