# -*- coding: utf-8 -*-
# @Time    : 2019-05-06 17:06
# <AUTHOR> hachi
# @File    : utils.py
# @Software: PyCharm
import os
import json
import re
import sqlite3
import time
import zipfile
import jsonschema
import requests
from jsonschema import Draft7Validator
import shutil

from api_1_0.knowledge.get_killchains import GetKillchains
from utils.utils import flask_response
from utils.logger import get_ndr_logger, LogToDb
from utils.args_check_schema.get_scheme import Schema
from config.config import KnowledgeNDR, NdrLog

LOG = get_ndr_logger("tools_log", __file__)
API_LOG = get_ndr_logger('api_log', __file__)
LOG_FEATURE = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class Validator(object):
    def __init__(self, name):
        self.name = name
        self.schema = Schema(self.name).get_schema()
        checker = jsonschema.FormatChecker()
        DefaultValidatingDraft7Validator = extend_with_default(Draft7Validator)
        self.validator = DefaultValidatingDraft7Validator(self.schema,
                                                          format_checker=checker)

    def validate(self, data):
        try:
            self.validator.validate(data)
            return "success", ""
        except jsonschema.ValidationError as ex:
            if len(ex.absolute_path):
                err_info = "{0}: {1} is invalid".format(
                    ex.absolute_path[0], ex.instance)
            else:
                err_info = ex.message.replace("\'", "")
            LOG.error(err_info)
            return "false", err_info


def check_sid_input(request):
    def add_validator(func):
        def wrapper(*args, **kwargs):
            sid = request.url.split("/")[-1]
            sig_id = int(sid)
            if 1 <= sig_id <= 4294967295:
                return func(*args, **kwargs)
            LOG.error("illegal sid %s format" % sid)
            return flask_response("Operate successfully""illegal sid %s format" % sid, False, {})

        return wrapper

    return add_validator


def check_rules(json_data):
    """
    检测规则合法性
    :param json_data: 上传的规则 list
    :return:
    """
    sid_from_rule = re.findall(
        r'\bsid: \d+', json_data["ruleContent"])[0].split(":")[1].strip()

    if json_data["sid"] != sid_from_rule:
        LOG.error("sid %s json value compare sid from rules conflict" %
                  json_data["sid"])
        return '', json_data["sid"], '', "sid %s json value compare sid from rules conflict" % json_data["sid"]

    json_data = {
        "threatFlag": json_data.get('threatFlag'),
        "threatScore": json_data.get('threatScore'),
        "author": json_data.get('author') if json_data.get('author') else "",
        "vulName": json_data.get('vulName') if json_data.get('vulName') else "",
        "vulType": json_data.get('vulType') if json_data.get('vulType') else "",
        "cve": json_data.get('cve') if json_data.get('cve') else "",
        "lockheedKillchainStage": json_data.get('lockheedKillchainStage') if json_data.get(
            'lockheedKillchainStage') else "",
        "lockheedKillchainCN": json_data.get('lockheedKillchainCN') if json_data.get('lockheedKillchainCN') else "",
        "lockheedKillchainEN": json_data.get('lockheedKillchainEN'),
        "alterInfo": json_data.get('alterInfo') if json_data.get('alterInfo') else "",
        "submitTime": json_data.get('submitTime') if json_data.get('submitTime') else time.strftime("%Y-%m-%d %H:%M"),
        "is0day": json_data.get('is0day') if json_data.get('is0day') else 1,
        "ruleContent": json_data.get('ruleContent'),
        "attackIp": json_data.get('attackIp'),
        "victimIp": json_data.get('victimIp'),
        "sid": json_data.get('sid'),
        "appProto": json_data.get('appProto'),
        "updateTime": time.strftime("%Y-%m-%d %H:%M"),
        "rulesStatus": "enable"

    }

    return json_data["sid"], '', json_data, 'success'


def check_flask_args(validator, request, time_verify=False):
    """检查flask参数"""

    def add_validator(func):
        def wrapper(*args, **kwargs):
            data = {}
            if request.json:
                data = request.json
            elif request.values.to_dict():
                data = request.values.to_dict()
            if len(request.view_args):
                data.update(request.view_args)
            res, err_info = validator.validate(data)

            if res != "success":
                return flask_response(err_info, False, {})

            kwargs["args"] = data
            kwargs.update(data)

            if time_verify:
                start_time = int(kwargs["startTime"])
                stop_time = int(kwargs["stopTime"])
                if start_time >= stop_time:
                    message = "时间范围错误，开始时间大于结束时间"
                    return flask_response(message, False, {})

            return func(*args, **kwargs)

        return wrapper

    return add_validator


def extend_with_default(validator_class):
    """添加默认值"""
    validate_properties = validator_class.VALIDATORS["properties"]

    def set_defaults(validator, properties, instance, schema):
        for error in validate_properties(
                validator, properties, instance, schema,
        ):
            yield error
        for property, subschema in properties.items():
            if "default" in subschema:
                instance.setdefault(property, subschema["default"])

    return jsonschema.validators.extend(
        validator_class, {"properties": set_defaults},
    )


def check_killchains(killchains_list):
    if len(killchains_list) == 1 and killchains_list[0] == "":
        return True, ""

    for item in killchains_list:
        if item not in list(GetKillchains.killchain_map.keys()):
            return False, item
    return True, ""


def check_db_file_input(request):
    def add_validator(func):
        def wrapper(*args, **kwargs):
            file = request.files["featureDbFile"]
            try:
                if os.path.exists(KnowledgeNDR.TempPath):
                    shutil.rmtree(KnowledgeNDR.TempPath)
                os.makedirs(KnowledgeNDR.TempPath)
                file.save(KnowledgeNDR.TempPath + "rules.zip")
                os.system("chown -R kslab:kslab %srules.zip" % KnowledgeNDR.TempPath)
                # 解压
                zip_file = zipfile.ZipFile(KnowledgeNDR.TempPath + "rules.zip")
                for file in zip_file.namelist():
                    password = KnowledgeNDR.ZipPwd
                    zip_file.extract(file, KnowledgeNDR.TempPath, password)
                    # shutil.chown(KnowledgeNDR.TempPath + "knowledge_feature.db", 'kslab', 'kslab')
                zip_file.close()
                os.system("chown -R kslab:kslab %s" % KnowledgeNDR.TempPath)
                return func(*args, **kwargs)
            except Exception as e:
                LogToDb().write_to_db('admin', NdrLog.Type.OPERATE, '特征文件检查失败：%s' % str(e))
                LOG_FEATURE.error(e)
                return flask_response(str(e), False, {})

        return wrapper

    return add_validator


def check_license_expired(request):
    def check_license(func):
        def wrapper(*args, **kwargs):
            try:
                box_headers = dict(request.headers)
                # 带有Upgrade字段的头，不进行证书校验，跟不进行登录校验一样，为了方便安装升级。
                if box_headers.get('Upgrade', None):
                    box_headers.pop('Upgrade', None)
                    return func(*args, **kwargs)
                else:
                    box_headers.pop('From-Web', None)  # boss-api需求
                    box_headers.pop('Content-Type', None)  # 有的请求类型带有content-Type，如POST，需要去掉，否则不响应
                    rst = requests.get("http://box-api-v2-prd:7001/api/license/auth", headers=box_headers, timeout=3)
                    ret_data = json.loads(rst.content)
                    if rst.status_code == 200:
                        if ret_data.get('code', None) == 0:
                            return func(*args, **kwargs)
                        else:
                            return flask_response('证书已过期，请更新证书后再升级！', True, {})
                    else:
                        return flask_response('请求获取证书状态失败！', True, {})
            except Exception as e:
                LogToDb().write_to_db('admin', NdrLog.Type.OPERATE, '证书文件检查失败：%s' % str(e))
                return flask_response(str(e), True, {})

        return wrapper

    return check_license


def check_task_id(validator):
    """检查task_id参数"""

    def add_validator(func):
        def wrapper(*args, **kwargs):
            res, err_info = "", ""
            if len(kwargs):
                res, err_info = validator.validate(kwargs)
            if res == "success":
                return func(*args, **kwargs)
            return flask_response(err_info, False, {})

        return wrapper

    return add_validator


def method_check(func):
    def wrapper(*args):
        try:
            result = func(*args)
            return flask_response("", True, result)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(args, str(e)))
            return flask_response(err_info, False, {})

    return wrapper
