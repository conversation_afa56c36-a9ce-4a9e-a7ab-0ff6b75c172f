#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_event_metadata.py
# @time: 2019/07/18
# @software: PyCharm

import pytz
import hashlib
import time
import bson
from datetime import datetime

from api_1_0.knowledge.get_killchains import GetKillchains
from celery_tasks import app
from utils.database import BaseMongoDB
from utils.database import get_es_client
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger
from api_1_0.knowledge.get_eventName import get_event_properties
from config.config import RULE_INDEX


threat_flag_template = ["Exploits and Attacks", "APT", "Malicious Host", "Suspicious", "Botnet", "Phishing",
                        "Scanning", "Malware", "DOS", "Trojan", "Mining", "Ransomware", "Spyware", "Webshell",
                        "URL_malware", "Brute force"]

threat_level_template = {
    0: "Low",
    1: "Medium",
    2: "High",
    -1: "other"
}

LOG = get_ndr_logger('api_log', __file__)
ES_LOG = get_ndr_logger('es_log', __file__)


@app.task()
def get_event_meta(mode, start, stop):
    """
    以每分钟为维度获取安全事件
    :return:
    """

    now_date = datetime.fromtimestamp(int(time.time()))
    now_time = int(now_date.replace(second=0).timestamp())
    db = BaseMongoDB("ndr")
    es_template = ES_Template()
    es_client = get_es_client()
    db_event_list = []

    # 每分钟聚合一次
    if mode == "minutes":
        start_time = (now_time - 60) * 1000
        end_time = now_time * 1000
    # elif mode == "hours":
    #     pre_time = datetime.fromtimestamp(now_time - 3600)
    #     start_time = int(pre_time.timestamp() * 1000)
    #     end_time = int(pre_time.replace(minute=59, second=59).timestamp() * 1000)
    # # 每天聚合一次
    # elif mode == "days":
    #     pre_time = datetime.fromtimestamp(now_time - 3600)
    #     start_time = int(pre_time.replace(hour=0, minute=0, second=0).timestamp() * 1000)
    #     end_time = int(pre_time.replace(hour=23, minute=59, second=59).timestamp() * 1000)
    # # 每周聚合一次
    # elif mode == "weeks":
    #     pre_time = datetime.fromtimestamp(now_date.timestamp() - 604800)
    #     pre_time_plus = datetime.fromtimestamp(now_date.timestamp() - 60)
    #     start_time = int(pre_time.timestamp() * 1000)
    #     end_time = int(pre_time_plus.replace(hour=23, minute=59, second=59).timestamp() * 1000)
    # # 每月聚合一次
    # elif mode == "months":
    #     pre_time = datetime.fromtimestamp(now_date.timestamp() - 60)
    #     start_time = int(pre_time.replace(day=1, hour=0, minute=0, second=0).timestamp() * 1000)
    #     end_time = int(pre_time.replace(hour=23, minute=59, second=59).timestamp() * 1000)
    else:
        start_time = start
        end_time = stop

    # 第一步，确定有哪些新的攻击者IP产生
    es_template_ips = es_template.read_template("get_event_meta")
    es_template_ips["query"]["bool"]["must"][0]["range"]["observedTime"]["gte"] = start_time
    es_template_ips["query"]["bool"]["must"][0]["range"]["observedTime"]["lte"] = end_time

    try:
        data = es_client.search(index=RULE_INDEX, body=es_template_ips)
        ips = data_format_ips(data)
    except Exception as e:
        ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
        return

    # 第二步，获取以每个攻击者为纬度的攻击事件
    for key, values in ips.items():
        es_template_outline = es_template.read_template("get_event_aggs")
        es_template_outline["query"]["bool"]["must"][2]["term"]["rulesInfo.attackIp.keyword"] = key
        # 加载所有受害者IP
        for value in values:
            es_template_outline["query"]["bool"]["should"].append({"term": {"rulesInfo.victimIp.keyword": value}})
        es_template_outline["query"]["bool"]["must"][1]["range"]["observedTime"]["gte"] = start_time
        es_template_outline["query"]["bool"]["must"][1]["range"]["observedTime"]["lte"] = end_time
        data = es_client.search(index=RULE_INDEX, body=es_template_outline)
        event = data_format(data, start_time, end_time)
        db_event_list.append(event)

    # 最后一步，写入到mongo库
    try:
        if mode == "minutes":
            event_list = agg_data(db_event_list, db)
            for event in event_list:
                update_data = {
                    "$set": event,
                }
                db.update_one("event_outline_minutes",
                              {"attackIpStr": event["attackIpStr"]},
                              update_data, insert=True)
        else:
            db.insert_many("event_outline_" + mode, db_event_list)
    except Exception as e:
        LOG.error(e)


def agg_data(data_list, db):
    event_ip_list = []
    for data in data_list:
        event_ip_list.append(data["attackIpStr"])

    # 获取已经存储到mongo的事件数据,加上这次的做重新聚合
    query = [
        {
            "$match": {
                "attackIpStr": {
                    "$in": event_ip_list
                }
            }
        }
    ]
    res = db.aggs("event_outline_minutes", query)
    old_data_dict = dict()
    for data in res:
        old_data_dict[data["attackIpStr"]] = data
    # 开始进行重组
    new_data_list = []
    for data in data_list:
        old_data = old_data_dict.get(data["attackIpStr"])
        # 如果未找到则说明这个事件是新的,直接添加即可
        if not old_data:
            new_data_list.append(data)
        else:
            killchains_list = list(set(old_data["lockheedKillchainENs"] + (data["lockheedKillchainENs"])))
            killchains_list = sorted([i for i in killchains_list],
                                     key=lambda x: list(GetKillchains.killchain_map.keys()).index(x))
            killchains = killchains_list[-1] if killchains_list else ""

            threat_level_list = list(set(old_data["threatLevels"] + (data["threatLevels"])))
            threat_level_list.sort()

            AttVic_ip_list = old_data["attVicIps"] + (data["attVicIps"])
            AttVic_ip_list = [dict(t) for t in set([tuple(dic.items()) for dic in AttVic_ip_list])]

            victim_ip_list = old_data["victimIps"] + (data["victimIps"])
            victim_ip_list = [dict(t) for t in set([tuple(dic.items()) for dic in victim_ip_list])]

            observedAt_list = list(set(old_data["observedAt"] + (data["observedAt"])))
            observedAt_list.sort()

            occurredAt_list = list(set(old_data["occurredAt"] + (data["occurredAt"])))
            occurredAt_list.sort()

            threat_score_list = list(set(old_data["threatScoreList"] + (data["threatScoreList"])))
            threat_score_list.sort()

            triggerCount = old_data["triggerCount"] + data["triggerCount"]
            attackIp = data["attackIp"]
            event_name, event_type = get_event_properties(killchains_list, triggerCount, attackIp["ip"], None)
            event = {
                "id": data["id"],
                "tsBegin": old_data["tsBegin"],
                "tsEnd": data["tsEnd"],
                "observedAtTimestamp": old_data["observedAtTimestamp"],
                "triggerCount": triggerCount,
                "behavior": event_name,
                "eventType": event_type,
                "threatFlags": list(set(old_data["threatFlags"] + (data["threatFlags"]))),
                "lockheedKillchainENs": killchains_list,
                "lockheedKillchainEN": killchains,
                "threatLevels": threat_level_list,
                "threatLevel": threat_level_template[threat_level_list[-1]],
                "threatLevelInt": threat_level_list[-1],
                "attackIp": attackIp,
                "attackIpStr": data["attackIpStr"],
                "attVicIps": AttVic_ip_list,
                "victimIps": victim_ip_list,
                "firstOccurredAt": old_data["firstOccurredAt"],
                "firstOccurredAtTimestamp": old_data["firstOccurredAtTimestamp"],
                "occurredAt": occurredAt_list,
                "attackIsSuccesss": list(set(old_data["attackIsSuccesss"] + (data["attackIsSuccesss"]))),
                "firstObservedAt": old_data["firstObservedAt"],
                "firstObservedAtTimestamp": old_data["firstObservedAtTimestamp"],
                "observedAt": observedAt_list,
                "lastOccurredAt": data["lastOccurredAt"],
                "lastOccurredAtTimestamp": data["lastOccurredAtTimestamp"],
                "lastObservedAtTimestamp": data["lastObservedAtTimestamp"],
                "threatScore": max([old_data["threatScore"], data["threatScore"]]),
                "threatScoreList": threat_score_list
            }
            new_data_list.append(event)
    return new_data_list


def data_format_ips(data):
    ips = {}
    for bucket in data["aggregations"]["attackIpRange"]["buckets"]:
        attackIp = bucket["key"]
        victim_ip_list = [key["key"] for key in bucket["victimIpList"]["buckets"]]
        ips[attackIp] = victim_ip_list

    return ips


def data_format(data, ts_begin, ts_end):
    victim_ip_list = []
    occurredAt_list = []
    observedAt_list = []
    hash = hashlib.md5()
    event_dic = data["aggregations"]
    observations = event_dic["aggs"]["buckets"][0]["doc_count"]
    attackIp = event_dic["aggs"]["buckets"][0]["key"]
    hash.update((attackIp + str(ts_begin)).encode('utf-8'))
    id = hash.hexdigest()
    attack_ip_dic = event_dic["aggs"]["buckets"][0]["attack_ip_hits"]["hits"]["hits"][0]["_source"]["attackIpGeoip"]
    attack_ip_dic["ip"] = attackIp
    attack_ip_dic["mbType"] = data["hits"]["hits"][0]["_source"]["mbip"]["attackIpInfo"]["type"]
    attack_ip_dic["mbName"] = data["hits"]["hits"][0]["_source"]["mbip"]["attackIpInfo"]["name"]
    attack_ip_dic["mbFrom"] = data["hits"]["hits"][0]["_source"]["mbip"]["attackIpInfo"]["from"] if 'from' in \
                                                                                                    data["hits"][
                                                                                                        "hits"][0][
                                                                                                        "_source"][
                                                                                                        "mbip"][
                                                                                                        "attackIpInfo"] else ''
    last_occurred_timestamp = event_dic["lastOccurred"]["value"]
    last_observed_timestamp = event_dic["lastObserved"]["value"]
    attackIsSuccessList = [key["key_as_string"] for key in event_dic["attackIsSuccessList"]["buckets"]]
    threat_level_list = [key["key"] for key in event_dic["threatLevelList"]["buckets"]]
    ioc_list = list(set([key["key"] for key in event_dic["iocList"]["buckets"]]))
    if len(threat_level_list) == 0:
        threat_level_list = [-1]
    try:
        lockheed_killchain_en_count = event_dic["lockheedKillchainENList"]["buckets"]
        lockheed_killchain_en_list = sorted([key["key"] for key in lockheed_killchain_en_count if key["key"]],
                                            key=lambda x: list(GetKillchains.killchain_map.keys()).index(x))
        lockheed_killchain_en = lockheed_killchain_en_list[-1] if lockheed_killchain_en_list else ""
    except Exception as e:
        LOG.error(e)
        lockheed_killchain_en_list = [key["key"] for key in event_dic["lockheedKillchainENList"]["buckets"]]
        lockheed_killchain_en = ""

    try:
        threat_flag_list = sorted([key["key"] for key in event_dic["threatFlagList"]["buckets"] if key["key"]],
                                  key=lambda x: threat_flag_template.index(x))
    except Exception as e:
        LOG.error(e)
        threat_flag_list = [key["key"] for key in event_dic["threatFlagList"]["buckets"]]

    try:
        threat_score_list = sorted([key["key"] for key in event_dic["threatScoreList"]["buckets"]])
        threat_score = threat_score_list[-1] if threat_score_list else ""
    except Exception as e:
        LOG.error(e)
        threat_score = ""
        threat_score_list = [key["key"] for key in event_dic["threatScoreList"]["buckets"]]

    for bucket in event_dic["victimIpList"]["buckets"]:
        data = bucket["victim_ip_hits"]["hits"]["hits"][0]["_source"]["victimIpGeoip"]
        data["ip"] = bucket["key"]
        data["mbType"] = bucket["victim_ip_hits"]["hits"]["hits"][0]["_source"]["mbip"]["victimIpInfo"]["type"]
        data["mbName"] = bucket["victim_ip_hits"]["hits"]["hits"][0]["_source"]["mbip"]["victimIpInfo"]["name"]
        data["mbFrom"] = bucket["victim_ip_hits"]["hits"]["hits"][0]["_source"]["mbip"]["victimIpInfo"][
            "from"] if 'from' in bucket["victim_ip_hits"]["hits"]["hits"][0]["_source"]["mbip"]["victimIpInfo"] else ''
        victim_ip_list.append(data)
        occurredAt_list.append(bucket["victim_ip_hits"]["hits"]["hits"][0]["_source"]["occurredTime"])
        observedAt_list.append(bucket["victim_ip_hits"]["hits"]["hits"][0]["_source"]["observedTime"])

    occurredAt_list.sort()
    observedAt_list.sort()
    threat_level_list.sort()
    ioc_list.sort()

    event_name, event_type = get_event_properties(lockheed_killchain_en_list, observations, attackIp,
                                                  ioc_list[-1] if len(ioc_list) > 0 else None)

    event = {
        "id": id,
        "tsBegin": ts_begin,
        "tsEnd": ts_end,
        "observedAtTimestamp": ts_begin,
        "triggerCount": observations,
        "behavior": event_name,
        "eventType": event_type,
        "threatFlags": threat_flag_list,
        "lockheedKillchainENs": lockheed_killchain_en_list,
        "lockheedKillchainEN": lockheed_killchain_en,
        "threatLevels": threat_level_list,
        "threatLevel": threat_level_template[threat_level_list[-1]],
        "threatLevelInt": threat_level_list[-1],
        "attackIp": attack_ip_dic,
        "attackIpStr": attackIp,
        "victimIps": victim_ip_list,
        "attVicIps": victim_ip_list + [attack_ip_dic],
        "firstOccurredAt": datetime.fromtimestamp(occurredAt_list[0] / 1000, pytz.timezone('Asia/Shanghai')),
        "firstOccurredAtTimestamp": occurredAt_list[0],
        "occurredAt": occurredAt_list,
        "attackIsSuccesss": attackIsSuccessList,
        "firstObservedAt": datetime.fromtimestamp(observedAt_list[0] / 1000, pytz.timezone('Asia/Shanghai')),
        "firstObservedAtTimestamp": observedAt_list[0],
        "observedAt": observedAt_list,
        "lastOccurredAt": datetime.fromtimestamp(last_occurred_timestamp / 1000, pytz.timezone('Asia/Shanghai')),
        "lastOccurredAtTimestamp": bson.int64.Int64(last_occurred_timestamp),
        "lastObservedAtTimestamp": bson.int64.Int64(last_observed_timestamp),
        "threatScore": threat_score,
        "threatScoreList": threat_score_list,
    }
    return event
