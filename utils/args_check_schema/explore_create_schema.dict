{
    "type": "object",
    "required": ["taskName", "curFeatureGrpList"],
    "properties": {
        "taskName": {
            "type": "string",
            "pattern": r"^[A-Za-z0-9_\-\u4e00-\u9fa5]{1,255}$"
        },
        "curFeatureGrpList": {
            "type": "array",
            "items": {
                "type": "string"
              }
        },
        "dir_name": {
            "type": "string",
            "default": ""
        },
        "pcapList": {
            "type": "array",
            "items": {
                "type": "string",
              }
        },
        "sourceData": {
            "type": "string",
            "default": ""
        },
        "schedule": {
            "type": "object",
            "properties": {
                "period": {
                    "type": "string",
                    "default": "",
                    "pattern": r"(hour|day|week|month)"
                },
                "baseTime": {
                    "type": "string",
                    "default": ""
                },
                "baseDate": {
                    "type": "number",
                    "pattern": r"^([12][0-9]|3[0-1]|[1-9])$",
                    "default": 1
                },
            }
        },
        "taskType": {
            "type": "string",
            "default": "",
            "pattern": r"(singleTask|timedTask)"
        },
        "executeNow": {
            "type": "boolean",
			"default": False,
			"enum": [True, False]
        }
    }
}