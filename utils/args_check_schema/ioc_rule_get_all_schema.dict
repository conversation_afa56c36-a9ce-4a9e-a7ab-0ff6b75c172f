{
    "type": "object",
    "required": ["page"],
    "properties": {
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        },
        "ioc": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,64}$"
        },
        "ioc_type": {
	         "type": "string",
	         "enum": ["ip", "domain"],
	         "default": ""
        },
        "labels": {
            "type": "string",
            "default": ""
        }
    }
}