#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import json
import time
import traceback

from api_1_0.utils.flask_log import ndr_log_to_box
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator
from config.config import NdrLog
from utils import param_check
from flask import request
from bson import ObjectId

from .engine import run_task
from .aptorg import AptOrg

TABLE_NAME = 'threat_expansion_tasks'

TASK_STATUS_IDLE = 'idle'
TASK_STATUS_RUNNING = 'running'

TASK_FIELDS = {
    "alias": "",
    "engine": "",
    "type": "",
    "org": "",
    "tool": "",
    "tag": "",
    "author": "",
    "model": "",
    "sig_type": "",
    "sig_status": "",
    "dock": "",
    "description": ""
}

AUTO_TASK_FIELDS = [
    "status",
    "result",
    "created_at",
    "updated_at",
]


def data_from_args(args):
    data = {}
    for k in TASK_FIELDS:
        if k in args:
            data[k] = args.get(k)

    if 'engine' in data:
        data['engine'] = data['engine'].lower()

    return data


def timestamp():
    return int(time.time() * 1000)


LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class Tasks(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME
        self.apt_org = AptOrg()

    @staticmethod
    def query_condition(args):
        condition = {}

        if args.get("start_time") or args.get("stop_time"):
            condition["created_at"] = {
                "$gte": int(args.get("start_time", 0)),
                "$lte": int(args.get("stop_time", 2 ** 63 - 1))
            }

        keys = ["sig_type", "alias", "org", "tool", "tag", "engine", "author", "model"]
        for k, v in args.items():
            if k in keys and v:
                condition[k] = v

        return condition

    @param_check.check_flask_args(Validator("threat_expansion_search_schema"), request)
    def get(self, **kwargs):
        """
        获取任务列表
        """
        args = kwargs.get("args")
        page = int(args.get("page"))
        page_size = int(args.get("pageSize"))
        try:
            condition = self.query_condition(args)
            total = self.mongodb.find(self.table_name, condition).count()
            result = self.mongodb.find(self.table_name, condition).sort([("created_at", -1)]).limit(page_size).skip(
                (page - 1) * page_size)

            cols = []
            for r in result:
                r["_id"] = str(r["_id"])
                r["result"] = ""
                cols.append(r)

            data = {"total": total,
                    "cols": cols,
                    "page": page,
                    "pageSize": page_size}
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Get threat expansion tasks ERROR: %s' % e)
            return flask_response('failed', False, {})

    @param_check.check_flask_args(Validator("threat_expansion_task_schema"), request)
    def post(self, **kwargs):
        """
        创建任务
        """
        args = kwargs['args']
        data = data_from_args(args)
        data['status'] = TASK_STATUS_IDLE
        data['result'] = ''
        data['created_at'] = timestamp()
        data['updated_at'] = data['created_at']

        try:
            inserted_id = self.mongodb.insert_one(self.table_name, data)
            if inserted_id == "":
                raise ValueError("insert failed")

            data['_id'] = str(inserted_id)
            if data['org']:
                self.apt_org.put(data['_id'], data['org'], data['dock'])
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务创建成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Create threat expansion tasks ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务创建失败")
            return flask_response('create failed', False, {})


class Task(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME
        self.apt_org = AptOrg()

    def get(self, **kwargs):
        """
        获取任务
        """
        try:
            condition = {'_id': ObjectId(kwargs['task_id'])}
            data = self.mongodb.find_one(self.table_name, condition)
            data['_id'] = str(data['_id'])
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Create threat expansion task ERROR: %s' % e)
            return flask_response('', False, {})

    @param_check.check_flask_args(Validator("threat_expansion_task_schema"), request)
    def put(self, **kwargs):
        """
        更新任务
        """
        condition = {'_id': ObjectId(kwargs['task_id'])}
        args = kwargs['args']
        data = data_from_args(args)
        data['updated_at'] = timestamp()

        try:
            r = self.mongodb.update_one(self.table_name, condition, {"$set": data})
            if r == 0:
                raise ValueError("update failed")

            if data['org']:
                self.apt_org.put(kwargs['task_id'], data['org'], data['dock'])
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务更新成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Update threat expansion tasks ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务更新失败")
            return flask_response('update failed', False, {})

    @param_check.check_flask_args(Validator("threat_expansion_task_schema"), request)
    def patch(self, **kwargs):
        """
        修改任务
        """
        return self.put(kwargs)

    def delete(self, **kwargs):
        """
        创建任务
        """
        condition = {'_id': ObjectId(kwargs['task_id'])}
        try:
            r = self.mongodb.delete(self.table_name, condition)
            if r == 0:
                raise ValueError("delete failed")

            self.apt_org.delete(kwargs['task_id'])
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务删除成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error('Delete threat expansion tasks ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务删除失败")
            return flask_response('delete failed', False, {})


class TaskRunner(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def post(self, **kwargs):
        """
        运行任务
        """
        condition = {'_id': ObjectId(kwargs['task_id'])}
        try:
            task = self.mongodb.find_one(self.table_name, condition)
            if not task:
                raise ValueError("task not found")

            result = run_task(task['engine'], task['dock'])
            if len(result) != 0:
                r = self.mongodb.update_one(self.table_name, condition, {"$set": {'result': json.dumps(result)}})
                if r == 0:
                    raise ValueError("write task result failed")

            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务运行成功")
            return flask_response('', True, result)
        except Exception as e:
            LOG.error('Run threat expansion task ERROR: %s %s' % (e, traceback.format_exc()))
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析任务运行失败")
            return flask_response(str(e), False, {})
