# -*- coding: utf-8 -*-
import time
from urllib.parse import unquote

from api_1_0.utils.flask_log import ndr_log_to_box
from config.config import NdrLog
from utils.database import MongoDB, get_es_client
from flask import request
from flask_restful import Resource
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class ModelThreatType(Resource):
    threat_type = [
        "APT攻击",
        "可疑行为",
        "恶意网站",
        "僵尸网络",
        "恶意软件",
        "DOS攻击",
        "攻击利用",
        "恶意扫描",
        "爆破",
        "WEBSHELL"]

    @staticmethod
    def get():
        return_data = {"threat_type": []}
        for i in ModelThreatType.threat_type:
            return_data["threat_type"].append(i)
        return flask_response("", True, return_data)


class ModelResource(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.es_client = get_es_client()
        self.collection = 'model_manage'
        self.es_template = {
          "query": {
            "bool": {
              "must": [
                {
                  "range": {
                    "observedTime": {
                      "gte": int((time.time() - 7 * 24 * 3600) * 1000),
                      "lte": int(time.time() * 1000)
                    }
                  }
                }
              ]
            }
          },
          "size": 0,
          "aggs": {
            "modelNameGroup": {
              "terms": {
                "field": "modelName.keyword"
              },
              "aggs": {
                "confirmGroup": {
                  "terms": {
                    "field": "confirm"
                  }
                }
              }
            }
          }
        }

    @param_check.check_flask_args(Validator('model_manager_schema'), request)
    def get(self, **kwargs):
        query_cond = {}
        if kwargs['modelNum']:
            query_cond['model_num'] = int(kwargs['modelNum'])
        if kwargs['modelName']:
            query_cond['model_name'] = unquote(kwargs['modelName'], encoding="GBK")
        if kwargs['modelStatus']:
            query_cond['model_status'] = kwargs['modelStatus']
        try:
            mongo_data = self.mongodb.find(self.collection, query_cond, {'_id': 0}).sort(
                [("model_num", 1)]).limit(int(kwargs['pageSize'])).skip(
                (int(kwargs['page']) - 1) * int(kwargs['pageSize']))

            if not mongo_data:
                return flask_response('', True, {})

            model_data = self.es_client.search(index="model-eve", body=self.es_template)
            buckets_map = {}
            for bucket in model_data["aggregations"]['modelNameGroup']['buckets']:
                total_count = bucket['doc_count']
                false_count = 0
                for confirm in bucket['confirmGroup']["buckets"]:
                    if confirm['key'] == 4:
                        false_count = confirm['doc_count']

                buckets_map[bucket['key']] = {
                    'total_count': total_count, 'false_count': false_count, 'false_persent': false_count / total_count}

            data = []
            for info in mongo_data:
                info['model_evaluation'] = buckets_map.get(
                    info['model_name'], {'total_count': 0, 'false_count': 0, 'false_persent': 0})
                data.append(info)
            ndr_log_to_box(NdrLog.Type.OPERATE, "模型获取成功")
            return flask_response('', True, {'count': self.mongodb.count(self.collection, query_cond), 'detail': data})
        except Exception as e:
            LOG.error("model_manage get failed,reason:%s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "模型获取失败")
            return flask_response('', False, {})

    @param_check.check_flask_args(Validator('model_manager_modify_schema'), request)
    def put(self, **kwargs):
        try:
            self.mongodb.update(self.collection, {'model_num': kwargs['modelNum']},
                                {'model_status': kwargs['modelStatus']})
            ndr_log_to_box(NdrLog.Type.OPERATE, "模型编辑成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error("model_manage update failed,reason:%s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "模型编辑失败")
            return flask_response('', False, {})
