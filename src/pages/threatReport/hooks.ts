import { useState, useEffect } from 'react'
import { message } from 'antd';

import {
  ThreatReportCreateParams,
  createThreatReport,
  deleteThreatReport,
  downloadReport,
  getThreatReportList
} from '@/services/threatReport';

import { encryptData } from './utils';

export type ReportListItem = {
  _id: string;
  name: string;
  start_time: number;
  stop_time: number;
  timestamp: string;
  user: string;
  status: string;
  report_type: string;
  gen_method: string;
  period: string;
  report: Record<string, string>
}

export const SECRET_KEY = '1234567812345678';

export function useThreatReportList() {
  const [data, setData] = useState<ReportListItem[]>([]);
  const [total, setTotal] = useState(0);
  const [sorter, setSorter] = useState('desc');
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
  });

  useEffect(() => {
    fetchListData()
  }, [pagination.page, pagination.pageSize, sorter]);

  const fetchListData = async (page = pagination.page, pageSize = pagination.pageSize, order = sorter) => {
    setLoading(true);
    try {
      const response = await getThreatReportList({ page, pageSize, order });
      const { flag, data, message } = response;
      if (!flag) {
        throw new Error(message);
      }
      const { data: list, total } = data;
      setData(list as ReportListItem[]);
      setTotal(total);
    } catch (error: any) {
      message.error('报告列表请求失败：' + error?.message);
    } finally {
      setLoading(false);
    }
  };

  const refreshList = () => {
    fetchListData(1, 10);
  };

  const createListData = async (payload: ThreatReportCreateParams) => {
    try {
      const { flag, message: msg } = await createThreatReport({
        ...payload,
        ...(payload.encrypt && {
          encrypt: encryptData(payload.encrypt, SECRET_KEY)
        })
      });
      if (flag) {
        message.success('报告创建成功');
        refreshList();
      } else {
        throw new Error(msg);
      }
    } catch (error: any) {
      message.error('报告创建失败：' + error?.message);
    }
  };

  const deleteListData = async (selectRowKeys: string[]) => {
    try {
      const { flag, message: msg } = await deleteThreatReport({ ids: selectRowKeys.join() });
      if (flag) {
        message.success('报告删除成功');
        refreshList();
      } else {
        throw new Error(msg);
      }
    } catch (error: any) {
      message.error('报告删除失败：' + error?.message);
    }
  }

  const downloadReportZip = async (name: string) => {
    const responseBlob = await downloadReport({ name });
    const a = document.createElement('a');
    a.href = URL.createObjectURL(responseBlob);
    a.download = name;
    a.click();

    // 清理 URL 对象
    URL.revokeObjectURL(a.href);
  }

  const changePage = (page: number, pageSize: number) => {
    setPagination({ page, pageSize });
  };

  return {
    data,
    loading,
    pagination,
    total,
    changePage,
    setSorter,
    deleteListData,
    downloadReportZip,
    createListData,
  }
}