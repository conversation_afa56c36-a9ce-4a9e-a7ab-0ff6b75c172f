#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import time, os, copy

from api_1_0.knowledge.get_killchains import GetKillchains
from utils.utils import flask_response
from utils.ndr_base import NdrResource
from utils.param_check import Validator, check_flask_args
from flask import request, send_file, make_response
import pandas as pd
from docx import Document
from docx.shared import Cm
from docx.oxml.ns import qn
from pydocx import PyDocX
import matplotlib.pyplot as plt
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import Paragraph, Frame, TableStyle, Table, Spacer, Image, BaseDocTemplate, PageTemplate
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from reportlab.lib.pagesizes import A4


class Export(NdrResource):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.subTitleFormat = None
        self.excelRowIndex = 1
        # 保存需要导出的报表数据结构
        self.export_data = []
        # 保存报告日期
        self.report_date = ''
        self.word_file_path = ''
        self.excel_file_path = ''
        self.html_file_path = ''
        self.pdf_file_path = ''
        # 保存pdf
        self.story = []
        self.styles = None
        self.export_template = {
            # 告警分析--威胁等级统计
            'statistics_level': {
                'func': self.export_statistics_level,
                'title': '威胁等级统计',
                'columns': ['告警总数', '高危', '中危', '低危'],
                'data': None,
                'word_style': 'Colorful List Accent 6'
            },
            # 告警分析--杀伤链阶段统计
            'statistics_killchains': {
                'func': self.export_statistics_killchains,
                'title': '杀伤链阶段统计',
                'columns': list(GetKillchains.killchain_map.values()),
                'data': None,
                'word_style': 'Colorful List Accent 6'
            },
            # 告警分析--威胁分类统计
            'statistics_threat': {
                'func': self.export_statistics_threat,
                'title': '威胁分类统计',
                'columns': ['攻击利用', 'APT攻击', '恶意主机', '可疑行为', '僵尸网络',
                            '钓鱼邮件', '恶意扫描', '恶意软件', 'DOS攻击', '远控木马',
                            '挖矿木马', '勒索软件', '间谍软件', 'WEBSHELL', '恶意网站', '爆破'],
                'data': None,
            },
            # 流量分析--杀伤链阶段统计
            'killchains_flow_total': {
                'func': self.export_killchains_flow_total,
                'title': '按杀伤链阶段统计的危险流量',
                'columns': list(GetKillchains.killchain_map.values()),
                'data': None,
                'word_style': 'Colorful List Accent 6'
            },
            # 流量分析--按时间和杀伤连阶段统计
            'killchains_flow_slice_flow': {
                'func': self.export_killchains_flow_slice_flow,
                'title': '按时间和杀伤连阶段统计',
                'columns': ['时间'] + list(GetKillchains.killchain_map.values()),
                'data': None,
            },
            # 流量分析--按威胁等级统计的危险流量
            'threat_flow_total': {
                'func': self.export_threat_flow_total,
                'title': '按威胁等级统计的危险流量',
                'columns': ['高危', '中危', '低危'],
                'data': None,
                'word_style': 'Colorful List Accent 6'
            },
            # 流量分析--按时间和按威胁等级统计
            'threat_flow_slice_flow': {
                'func': self.export_threat_flow_slice_flow,
                'title': '按时间和威胁等级统计',
                'columns': ['时间', '高危', '中危', '低危'],
                'data': None,
            },
            # 流量分析--按应用协议统计的危险流量
            'proto_flow_total': {
                'func': self.export_proto_flow_total,
                'title': '按应用协议统计的危险流量',
                'columns': [],
                'data': None,
                'word_style': 'Colorful List Accent 6'
            },
            # 流量分析--按时间和应用协议统计
            'proto_flow_slice_flow': {
                'func': self.export_proto_flow_slice_flow,
                'title': '按时间和应用协议统计',
                'columns': [],
                'data': None,
            },
            # 流量概览统计
            'all_flow': {
                'func': self.export_all_flow,
                'title': '流量概览统计',
                'data': None,
                'columns': ['总流量', '入流量', '出流量', '危险流量'],
                'word_style': 'Colorful List Accent 6'
            },
            # 热门事件
            'event_list': {
                'func': self.export_event_list,
                'title': '热门事件',
                'columns': ['威胁评分', '事件类型', '行为描述', '威胁标签', '杀伤连阶段',
                            '首次发生时间', '发生次数'],
                'data': None,
                'word_style': 'Colorful List Accent 6'
            }
        }

        self.report_sort_list = [
            'statistics_level',
            'statistics_killchains',
            'statistics_threat',
            'all_flow',
            'killchains_flow',
            'threat_flow',
            'proto_flow',
            'event_list'
        ]

    def export_file_path_init(self, export_time):
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(export_time / 1000))
        self.word_file_path = '/tmp/report_%s.docx' % time_str
        self.excel_file_path = '/tmp/report_%s.xlsx' % time_str
        self.html_file_path = '/tmp/report_%s.html' % time_str
        self.pdf_file_path = '/tmp/report_%s.pdf' % time_str

    def word_add_table(self, doc, title, data, columns, style='Colorful List Accent 6'):
        df = pd.DataFrame(data=data, columns=columns)
        self.word_add_heading(doc, title, level=3)
        tab = doc.add_table(rows=df.shape[0] + 1, cols=df.shape[1], style=style)
        tab.style.font.name = u'微软雅黑'
        # header
        for i in range(df.shape[-1]):
            tab.cell(0, i).text = df.columns[i]

        for i, col in enumerate(df):
            for row in range(df.shape[0]):
                tab.cell(row + 1, i).text = str(df[col][row])

    @staticmethod
    def word_add_heading(doc, text, level=1):
        run = doc.add_heading('', level=level).add_run(text)
        run.font.name = u'微软雅黑'
        run.font.bold = False
        run._element.rPr.rFonts.set(qn('w:eastAsia'), u'微软雅黑')

    def pdf_add_heading(self, story, text, style='Heading1'):
        story.append(Paragraph("<b>%s</b>" % text, self.styles[style]))

    def pdf_add_table(self, story, title, data, col_head, colWidths=56):
        self.pdf_add_heading(story, title, 'Heading3')
        # 将行首信息插入到数据中，一起写入表格
        data.insert(0, col_head)
        # 由于表格cell内不会自动换行，需要手动将需要换行的字符串添加\n
        tab_data = []
        for cell_raw in data:
            tab_raw_data = []
            for text in cell_raw:
                tab_raw_data.append(Paragraph(str(text), self.styles['Normal']))
            tab_data.append(tab_raw_data)

        table = Table(tab_data, colWidths=colWidths, hAlign='LEFT')
        # 设置风格
        style = [
            # ("BOX", (0, 0), (-1, -1), 0.25, colors.black),
            # ("INNERGRID", (0, 0), (-1, -1), 0.25, colors.black),
            ("ALIGN", (1, 1), (-1, -1), "LEFT"),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ("BACKGROUND", (0, 0), (-1, 0), colors.cadetblue),
            ("BACKGROUND", (0, 1), (-1, -1), colors.lightyellow),
            ('FONT', (0, 0), (-1, -1), 'simsun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10)
        ]
        # table.setStyle(TableStyle(style))
        # 设置数据风格
        data_len = len(data)
        for each in range(0, data_len):
            table.setStyle(TableStyle(style))

        story.append(table)

    def pdf_add_picture(self, story, title, pic_path):
        self.pdf_add_heading(story, title, 'Heading3')

        if not os.path.exists(pic_path):
            return

        img = Image(pic_path, 5 * inch, 4 * inch, hAlign='LEFT')
        story.append(img)

    def excel_add_level_threat(self, workSheet, data, columns):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '威胁等级统计', cell_format=self.subTitleFormat)
        writePos = "A%d:D%d" % (self.excelRowIndex + 2, self.excelRowIndex + 3)
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "level_threat",
                                       'columns': [{'header': columns[0]},
                                                   {'header': columns[1]},
                                                   {'header': columns[2]},
                                                   {'header': columns[3]}],
                                       'data': data})
        self.excelRowIndex += 4

    def excel_add_killchains_threat(self, workSheet, data, columns):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '杀伤链阶段统计', cell_format=self.subTitleFormat)
        writePos = "A%d:G%d" % (self.excelRowIndex + 2, self.excelRowIndex + 3)
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "killchains_threat",
                                       'columns': [{'header': columns[0]},
                                                   {'header': columns[1]},
                                                   {'header': columns[2]},
                                                   {'header': columns[3]},
                                                   {'header': columns[4]},
                                                   {'header': columns[5]},
                                                   {'header': columns[6]}],
                                       'data': data})
        self.excelRowIndex += 4

    def excel_add_type_threat(self, workSheet, data, columns):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '威胁分类统计', cell_format=self.subTitleFormat)
        writePos = "A%d:P%d" % (self.excelRowIndex + 2, self.excelRowIndex + 3)
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "type_level",
                                       'columns': [{'header': columns[0]},
                                                   {'header': columns[1]},
                                                   {'header': columns[2]},
                                                   {'header': columns[3]},
                                                   {'header': columns[4]},
                                                   {'header': columns[5]},
                                                   {'header': columns[6]},
                                                   {'header': columns[7]},
                                                   {'header': columns[8]},
                                                   {'header': columns[9]},
                                                   {'header': columns[10]},
                                                   {'header': columns[11]},
                                                   {'header': columns[12]},
                                                   {'header': columns[13]},
                                                   {'header': columns[14]},
                                                   {'header': columns[15]}],
                                       'data': data})
        self.excelRowIndex += 4

    def excel_add_all_flow(self, workSheet, data, colunms):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '流量概览统计', cell_format=self.subTitleFormat)
        writePos = "A%d:D%d" % (self.excelRowIndex + 2, self.excelRowIndex + 3)
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "all_flow",
                                       'columns': [{'header': colunms[0]},
                                                   {'header': colunms[1]},
                                                   {'header': colunms[2]},
                                                   {'header': colunms[3]}],
                                       'data': data})
        self.excelRowIndex += 4

    def excel_add_killchains_flow(self, workSheet, data, columns):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '按杀伤链阶段统计的危险流量', cell_format=self.subTitleFormat)
        writePos = "A%d:G%d" % (self.excelRowIndex + 2, self.excelRowIndex + 3)
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "killchains_flow",
                                       'columns': [{'header': columns[0]},
                                                   {'header': columns[1]},
                                                   {'header': columns[2]},
                                                   {'header': columns[3]},
                                                   {'header': columns[4]},
                                                   {'header': columns[5]},
                                                   {'header': columns[6]}],
                                       'data': data})
        self.excelRowIndex += 4

    def excel_add_timed_killchains_flow(self, workSheet, data, colunms):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '按时间和杀伤连阶段统计的流量', cell_format=self.subTitleFormat)
        writePos = "A%d:H%d" % (self.excelRowIndex + 2, self.excelRowIndex + 2 + len(data))
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "timed_killchains_flow",
                                       'columns': [{'header': colunms[0]},
                                                   {'header': colunms[1]},
                                                   {'header': colunms[2]},
                                                   {'header': colunms[3]},
                                                   {'header': colunms[4]},
                                                   {'header': colunms[5]},
                                                   {'header': colunms[6]},
                                                   {'header': colunms[7]}],
                                       'data': data})
        self.excelRowIndex += 2 + len(data) + 1

    def excel_add_threatlevel_flow(self, workSheet, data, colunms):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '按威胁等级统计的流量', cell_format=self.subTitleFormat)
        writePos = "A%d:C%d" % (self.excelRowIndex + 2, self.excelRowIndex + 3)
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "threadlevel_flow",
                                       'columns': [{'header': colunms[0]},
                                                   {'header': colunms[1]},
                                                   {'header': colunms[2]}],
                                       'data': data})
        self.excelRowIndex += 4

    def excel_add_timed_threatlevel_flow(self, workSheet, data, colunms):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '按时间和威胁等级统计的流量', cell_format=self.subTitleFormat)
        writePos = "A%d:D%d" % (self.excelRowIndex + 2, self.excelRowIndex + 2 + len(data))
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "timed_threadlevel_flow",
                                       'columns': [{'header': colunms[0]},
                                                   {'header': colunms[1]},
                                                   {'header': colunms[2]},
                                                   {'header': colunms[3]}],
                                       'data': data})
        self.excelRowIndex += 2 + len(data) + 1

    def excel_add_proto_flow(self, workSheet, data, columns):
        self.excelRowIndex += 1  # set a null row
        columnsInfo = [{'header': item} for item in columns]
        workSheet.write_string(self.excelRowIndex, 0, '按应用协议统计的危险流量', cell_format=self.subTitleFormat)
        writePos = "A%d:%c%d" % (self.excelRowIndex + 2, chr(ord('A') + len(columns) - 1), self.excelRowIndex + 3)
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "proto_flow",
                                       'columns': columnsInfo,
                                       'data': data})
        self.excelRowIndex += 4

    def excel_add_timed_proto_flow(self, workSheet, data, columns):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '按时间和应用协议统计的危险流量', cell_format=self.subTitleFormat)
        writePos = "A%d:%c%d" % (self.excelRowIndex + 2,
                                 chr(ord('A') + len(columns)),
                                 self.excelRowIndex + 2 + len(data))
        columnsInfo = [{'header': '时间'}]
        for proto in columns:
            columnsInfo.append({'header': proto})
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "timed_proto_flow",
                                       'columns': columnsInfo,
                                       'data': data})
        self.excelRowIndex += 2 + len(data) + 1

    def excel_add_hot_event(self, workSheet, data, columns):
        self.excelRowIndex += 1  # set a null row
        workSheet.write_string(self.excelRowIndex, 0, '热门事件', cell_format=self.subTitleFormat)
        writePos = "A%d:H%d" % (self.excelRowIndex + 2, self.excelRowIndex + 2 + len(data))
        workSheet.add_table(writePos, {'style': 'Table Style Medium 9',
                                       'autofilter': False,
                                       'name': "hot_event",
                                       'columns': [{'header': '威胁评分'},
                                                   {'header': '事件类型'},
                                                   {'header': '行为描述'},
                                                   {'header': '威胁标签'},
                                                   {'header': '杀伤连阶段'},
                                                   {'header': '首次发生时间'},
                                                   {'header': '发生次数'},
                                                   {'header': '攻击结果'}],
                                       'data': data})
        self.excelRowIndex += 2 + len(data)

    def export_statistics_level(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']
        dataRow = [dataSrc['totalCount'],
                   dataSrc['threatLevels']['High'],
                   dataSrc['threatLevels']['Medium'],
                   dataSrc['threatLevels']['Low']]
        data = [dataRow]

        if fmt == 'word':
            self.word_add_table(obj, self.export_data[idx]['title'], data, self.export_data[idx]['columns'],
                                style=self.export_data[idx]['word_style'])
        elif fmt == 'excel':
            self.excel_add_level_threat(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_table(self.story, self.export_data[idx]['title'], data, self.export_data[idx]['columns'])

    def export_statistics_killchains(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']
        dataRow = [dataSrc[i] for i in list(GetKillchains.killchain_map.keys())]

        data = [dataRow]

        df = pd.DataFrame({'杀伤链阶段': dataRow}, index=self.export_data[idx]['columns'])
        # 绘制报表并保存
        df.plot.bar(
            rot=0
        ).get_figure().savefig('/tmp/statistics_killchains.png', dpi=400, bbox_inches='tight')

        if fmt == 'word':
            self.word_add_heading(obj, self.export_data[idx]['title'], level=3)
            obj.add_picture('/tmp/statistics_killchains.png')
        elif fmt == 'excel':
            self.excel_add_killchains_threat(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_picture(self.story, self.export_data[idx]['title'], '/tmp/statistics_killchains.png')

    def export_statistics_threat(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']['threatFlags']
        dataRow = [dataSrc['Exploits and Attacks'], dataSrc['APT'], dataSrc['Malicious Host'], dataSrc['Suspicious'],
                   dataSrc['Botnet'], dataSrc['Phishing'], dataSrc['Scanning'], dataSrc['Malware'], dataSrc['DOS'],
                   dataSrc['Trojan'], dataSrc['Mining'], dataSrc['Ransomware'], dataSrc['Spyware'], dataSrc['Webshell'],
                   dataSrc['URL_malware'], dataSrc['Brute force']]
        data = [dataRow]

        df = pd.DataFrame({'威胁分类': dataRow}, index=self.export_data[idx]['columns'])
        # 绘制报表并保存
        df.plot.bar(
            rot=90
        ).get_figure().savefig('/tmp/statistics_threat.png', dpi=400, bbox_inches='tight')

        if fmt == 'word':
            self.word_add_heading(obj, self.export_data[idx]['title'], level=3)
            obj.add_picture('/tmp/statistics_threat.png')
        elif fmt == 'excel':
            self.excel_add_type_threat(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_picture(self.story, self.export_data[idx]['title'], '/tmp/statistics_threat.png')

    def export_killchains_flow_total(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']
        dataRow = [dataSrc[i]['showSize'] for i in list(GetKillchains.killchain_map.keys())]

        data = [dataRow]

        if fmt == 'word':
            self.word_add_table(
                obj, self.export_data[idx]['title'], data, self.export_data[idx]['columns'],
                style=self.export_data[idx]['word_style'])
        elif fmt == 'excel':
            self.excel_add_killchains_flow(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_table(self.story, self.export_data[idx]['title'], data, self.export_data[idx]['columns'])

    def export_killchains_flow_slice_flow(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']
        data = []
        for rowRrc in dataSrc:
            dataRow = [time.strftime("%Y-%m-%d", time.localtime(rowRrc['time'] / 1000))] + [
                rowRrc['killchainsFlow'][i]['actualSize'] for i in list(GetKillchains.killchain_map.keys())
            ]
            data.append(dataRow)

        # 将时间参数提取出来作为index
        index = []
        d = []
        for i in data:
            index.append(i.pop(0))
            d.append(i)
        df = pd.DataFrame(d, index=index, columns=self.export_data[idx]['columns'][1:])
        # 绘制报表并保存
        df.plot.bar(
            ylabel='KB'
        ).get_figure().savefig('/tmp/killchains_flow.png', dpi=400, bbox_inches='tight')

        if fmt == 'word':
            self.word_add_heading(obj, self.export_data[idx]['title'], level=3)
            obj.add_picture('/tmp/killchains_flow.png')
        elif fmt == 'excel':
            self.excel_add_timed_killchains_flow(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_picture(self.story, self.export_data[idx]['title'], '/tmp/killchains_flow.png')

    def export_threat_flow_total(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']
        dataRow = [dataSrc['High']['showSize'], dataSrc['Medium']['showSize'], dataSrc['Low']['showSize']]
        data = [dataRow]

        if fmt == 'word':
            self.word_add_table(
                obj, self.export_data[idx]['title'], data, self.export_data[idx]['columns'],
                style=self.export_data[idx]['word_style'])
        elif fmt == 'excel':
            self.excel_add_threatlevel_flow(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_table(self.story, self.export_data[idx]['title'], data, self.export_data[idx]['columns'])

    def export_threat_flow_slice_flow(self, idx, fmt, obj):
        dataRow = self.export_data[idx]['data']

        data = []
        index = []
        pic_columns = ['高危', '中危', '低危']

        for slice_flow in dataRow:
            data.append([
                round(slice_flow['threatFlow']['High']['actualSize'], 1),
                round(slice_flow['threatFlow']['Medium']['actualSize'], 1),
                round(slice_flow['threatFlow']['Low']['actualSize'], 1)
            ])

            index.append(time.strftime('%Y-%m-%d', time.localtime(int(slice_flow['time'] / 1000))))

        df = pd.DataFrame(data=data, index=index, columns=pic_columns)
        # 绘制报表并保存
        df.plot.bar(
            ylabel='KB'
        ).get_figure().savefig('/tmp/threat_flow.png', dpi=400, bbox_inches='tight')

        if fmt == 'word':
            self.word_add_heading(obj, self.export_data[idx]['title'], level=3)
            obj.add_picture('/tmp/threat_flow.png')
        elif fmt == 'excel':
            # 将时间添加到d
            d = []
            for i, val in enumerate(data):
                val.insert(0, index[i])
                d.append(val)
            self.excel_add_timed_threatlevel_flow(obj, d, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_picture(self.story, self.export_data[idx]['title'], '/tmp/threat_flow.png')

    def export_proto_flow_total(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']
        dataRow = []
        columns = []
        for proto, flow in dataSrc.items():
            dataRow.append(dataSrc[proto]['showSize'])
            columns.append(proto)
        data = [dataRow]

        if fmt == 'word':
            self.word_add_table(
                obj, self.export_data[idx]['title'], data, columns, style=self.export_data[idx]['word_style'])
        elif fmt == 'excel':
            self.excel_add_proto_flow(obj, data, columns)
        elif fmt == 'pdf':
            self.pdf_add_table(self.story, self.export_data[idx]['title'], data, columns)

    def export_proto_flow_slice_flow(self, idx, fmt, obj):
        dataSrc = self.export_data[idx]['data']
        if 0 == len(dataSrc) or not dataSrc[0]['appFlow']:
            return
        data = []
        columns = dataSrc[0]['appFlow'].keys()
        for rowRrc in dataSrc:
            dataRow = [time.strftime("%Y-%m-%d", time.localtime(rowRrc['time'] / 1000))]
            for proto in columns:
                dataRow.append(round(rowRrc['appFlow'][proto]['actualSize'], 1))
            data.append(dataRow)
        org_data = copy.deepcopy(data)
        # 将时间参数提取出来作为index
        index = []
        d = []
        for i in data:
            index.append(i.pop(0))
            d.append(i)

        df = pd.DataFrame(d, index=index, columns=columns)
        # 绘制报表并保存
        df.plot.bar(
            ylabel='KB'
        ).get_figure().savefig('/tmp/proto_flow.png', dpi=400, bbox_inches='tight')

        if fmt == 'word':
            self.word_add_heading(obj, self.export_data[idx]['title'], level=3)
            obj.add_picture('/tmp/proto_flow.png')
        elif fmt == 'excel':
            self.excel_add_timed_proto_flow(obj, org_data, columns)
        elif fmt == 'pdf':
            self.pdf_add_picture(self.story, self.export_data[idx]['title'], '/tmp/proto_flow.png')

    def export_all_flow(self, idx, fmt, obj):
        org_data = self.export_data[idx]['data']

        data = [
            [org_data['totalFlow'], org_data['incomeFlow'], org_data['outgoFlow'], org_data['threatFlow']]
        ]

        if fmt == 'word':
            self.word_add_table(obj, self.export_data[idx]['title'], data, self.export_data[idx]['columns'],
                                style=self.export_data[idx]['word_style'])
        elif fmt == 'excel':
            self.excel_add_all_flow(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_table(self.story, self.export_data[idx]['title'], data, self.export_data[idx]['columns'])

    def export_event_list(self, idx, fmt, obj):
        org_data = self.export_data[idx]['data']

        data = []

        for docs in org_data:
            data.append([
                docs['threatScore'],
                docs['eventType'],
                docs['behaviors'],
                docs['threatFlag'],
                '、'.join([GetKillchains.killchain_map[i] for i in docs['lockheedKillchainENs']]),
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(docs['occurredTime'] / 1000))),
                docs['count']
            ])

        if fmt == 'word':
            self.word_add_table(
                obj, self.export_data[idx]['title'], data, self.export_data[idx]['columns'],
                style=self.export_data[idx]['word_style'])
        elif fmt == 'excel':
            self.excel_add_hot_event(obj, data, self.export_data[idx]['columns'])
        elif fmt == 'pdf':
            self.pdf_add_table(
                self.story, self.export_data[idx]['title'], data,
                self.export_data[idx]['columns'],
                colWidths=[0.5 * inch, 0.6 * inch, 2 * inch, 0.8 * inch, 0.6 * inch, 0.9 * inch, 0.9 * inch, 0.5 * inch,
                           0.5 * inch])

    @check_flask_args(Validator("export_report_schema"), request)
    def post(self, **kwargs):
        start_time = time.strftime("%Y-%m-%d", time.localtime(kwargs['startTime'] / 1000))
        stop_time = time.strftime("%Y-%m-%d", time.localtime(kwargs['stopTime'] / 1000))
        # 记录报告日期区间
        self.report_date = '%s -- %s' % (start_time, stop_time)
        raw_data = kwargs['data']
        self.export_file_path_init(kwargs['exportTime'])
        try:
            # 将数据报表原始数据进行排序，使得按照固定的顺序输出报表
            for item in self.report_sort_list:
                if item in raw_data:
                    single_report_flag = True
                    # 如果包含 total 和 slice_flow ，表示有两个报表，需要拆分一下
                    if 'total' in raw_data[item] and type(raw_data[item]['total']) == dict:
                        if raw_data[item]['total']:
                            self.export_data.append(self.export_template[item + '_' + 'total'])
                            # 将原始数据添加到数据结构中
                            self.export_data[-1]['data'] = raw_data[item]['total']
                        single_report_flag = False
                    if 'slice_flow' in raw_data[item]:
                        if raw_data[item]['slice_flow']:
                            self.export_data.append(self.export_template[item + '_' + 'slice_flow'])
                            # 将原始数据添加到数据结构中
                            self.export_data[-1]['data'] = raw_data[item]['slice_flow']
                        single_report_flag = False
                    # 如果只有单个报表
                    if single_report_flag and raw_data[item]:
                        self.export_data.append(self.export_template[item])
                        # 将原始数据添加到数据结构中
                        self.export_data[-1]['data'] = raw_data[item]

            # 指定默认字体：解决plot不能显示中文问题
            plt.rcParams['font.sans-serif'] = ['simsun']
            plt.rcParams['font.family'] = ['simsun']
            # 禁用科学计数法
            pd.set_option('display.float_format', lambda x: '%u' % x)

            if kwargs['format'] == 'word':
                self.export_word()
            elif kwargs['format'] == 'excel':
                self.export_excel()
            elif kwargs['format'] == 'html':
                self.export_html()
            elif kwargs['format'] == 'pdf':
                self.export_pdf()
            else:
                return flask_response("Not support format: %s" % kwargs['format'], False, {})
        except Exception as e:
            return flask_response("Export %s file failed!" % kwargs['format'], False, {'error msg': str(e)})

        return flask_response("", True, {})

    @check_flask_args(Validator("export_report_file_schema"), request)
    def get(self, **kwargs):
        self.export_file_path_init(int(kwargs['exportTime']))

        if kwargs['format'] == 'word':
            file_path = self.word_file_path
        elif kwargs['format'] == 'excel':
            file_path = self.excel_file_path
        elif kwargs['format'] == 'html':
            file_path = self.html_file_path
        else:
            file_path = self.pdf_file_path

        if not os.path.exists(file_path):
            return make_response("Export failed", 205)

        return make_response(send_file(file_path, as_attachment=True))

    def export_html(self):
        self.export_word()

        html = PyDocX.to_html(self.word_file_path)
        fd = open(self.html_file_path, 'w', encoding="utf-8")
        fd.write(html)
        fd.close()

    def export_word(self):
        doc = Document()

        # 设置页边距margin=1
        sections = doc.sections
        for section in sections:
            section.top_margin = Cm(1)
            section.bottom_margin = Cm(1)
            section.left_margin = Cm(1)
            section.right_margin = Cm(1)

        # 设置字体
        doc.styles['Normal'].font.name = u'微软雅黑'
        doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), u'微软雅黑')

        # 标题
        self.word_add_heading(doc, '网络威胁检测报告', level=1)
        self.word_add_heading(doc, '知道创宇NDR产品团队', level=2)
        self.word_add_heading(doc, '报告日期：%s' % self.report_date, level=2)

        for idx, item in enumerate(self.export_data):
            item['func'](idx, 'word', doc)

        doc.save(self.word_file_path)

    def export_excel(self):
        excelFile = pd.ExcelWriter(self.excel_file_path, engine="xlsxwriter")
        workSheet = excelFile.book.add_worksheet('Sheet1')
        self.subTitleFormat = excelFile.book.add_format({
            'bold': True,  # 字体加粗
            'text_wrap': False,  # 是否自动换行
            'valign': 'top',  # 垂直对齐方式
            'align': 'left',  # 水平对齐方式
            # 'fg_color': '#D7E4BC',  # 单元格背景颜色
            'border': 2})  # 单元格边框宽度
        workSheet.write_string(0, 0, '网络威胁检测报告 (From 知道创宇NDR产品团队, %s)' % self.report_date,
                               cell_format=self.subTitleFormat)

        for idx, item in enumerate(self.export_data):
            item['func'](idx, 'excel', workSheet)

        excelFile.save()

    def export_pdf(self):
        pdfmetrics.registerFont(TTFont('simsun', 'simsun.ttf'))
        self.styles = getSampleStyleSheet()

        self.styles['Normal'].fontName = 'simsun'
        self.styles['Normal'].wordWrap = 'CJK'

        self.styles['Heading1'].fontName = 'simsun'
        self.styles['Heading1'].textColor = colors.cadetblue

        self.styles['Heading2'].fontName = 'simsun'
        self.styles['Heading2'].textColor = colors.cornflowerblue

        self.styles['Heading3'].fontName = 'simsun'
        self.styles['Heading3'].textColor = colors.dodgerblue

        doc = BaseDocTemplate(self.pdf_file_path, pagesize=A4,
                              leftMargin=0.4 * inch, rightMargin=0.4 * inch, topMargin=0.5 * inch,
                              bottomMargin=0.2 * inch)
        frame = Frame(doc.leftMargin, doc.bottomMargin, doc.width, doc.height, id='normal')
        doc.addPageTemplates([PageTemplate(id='test', frames=frame)])

        self.pdf_add_heading(self.story, '网络威胁检测报告', 'Heading1')
        self.pdf_add_heading(self.story, '知道创宇NDR产品团队', 'Heading2')
        self.pdf_add_heading(self.story, '报告日期：%s' % self.report_date, 'Heading2')
        self.story.append(Spacer(1, 0.2 * inch))

        for idx, item in enumerate(self.export_data):
            item['func'](idx, 'pdf', self.story)

        doc.build(self.story)
