import React, { useState } from 'react';
import moment from 'moment';
import { Button, Card, Descriptions, Divider, Space } from 'antd';

import EmailDetailAttachmentList from './EmailDetailAttachmentList';
import { useEmailDetailData } from './hooks';

import { renderTags } from './EmailList';

import styleModule from './style.less'

const { Item } = Descriptions

export type Props = {
  data: {
    end_time: number;
    start_time: number;
    msg_id: string;
  }
}

function EmailDetailContent({ data }: Props): JSX.Element {
  const [showContent, setShowContent] = useState(true);
  const { data: detailData, loading, downloadFile } = useEmailDetailData(data)

  const handleDownloadFile = (fileid: string) => {
    downloadFile(fileid)
  }

  const renderContent = () => {
    return (
      <div>
        <Descriptions
          className={styleModule.email_detail_info}
          labelStyle={{ width: 100, fontWeight: 500 }}
          column={1}
        >
          <Item label="发件人">{detailData?.from}</Item>
          <Item label="发送时间">{moment(detailData?.timestamp || 0).format('YYYY-MM-DD HH:mm:ss')}</Item>
          <Item label="收件人">{renderTags(detailData?.to, 0) || '-'}</Item>
          <Item label="抄送">{renderTags(detailData?.cc, 0) || '-'}</Item>
          <Item label="暗送">{renderTags(detailData?.bcc, 0) || '-'}</Item>
          <Item label="主题"> {detailData?.subject}</Item>
        </Descriptions>
        <Space className='my-3'>
          <Button
            type="primary"
            size='small'
            onClick={() => setShowContent(prev => !prev)}
          >
            隐藏/显示正文
          </Button>
          <Button size='small' disabled>邮件IOC</Button>
        </Space>
        {
          showContent && (
            <div className={styleModule.email_content}>
              <div style={{ whiteSpace: 'pre-wrap' }}>{detailData?.email_body}</div>
              <Divider style={{ marginBottom: 5 }} />
              <span>{detailData?.from}</span>
            </div>
          )
        }
        <div className='my-3 font-500'>附件{detailData?.attachment.length || 0}个:</div>
        <EmailDetailAttachmentList
          loading={loading}
          data={detailData?.attachment || []}
          timeRange={{ start_time: data.start_time, end_time: data.end_time }}
          onDownloadFile={handleDownloadFile}
        />
      </div>
    )
  }

  return (
    <Card
      title="邮件详情"
      loading={loading}
      size="small"
      bodyStyle={{ minHeight: 'calc(100vh - 115px)' }}
    >
      {renderContent()}
    </Card>
  );
}

export default EmailDetailContent;