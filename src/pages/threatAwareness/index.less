@font-face {
  font-family: 'digital';
  src: url('../../assets/fonts/DigifaceWide.ttf');
}

.overview_wrap{
  width: 100%;
  min-height: calc(100vh - 74px);
  display: flex;
  overflow: auto;

  .overview_content{
    flex: 1;
    min-width: 1280px;
  }
}

.customize_overview_card {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
  .title {
    display: flex;
    align-items: center;
    height: 40px;
    background-color: #f9f9f9;
    border-bottom: 1px dashed #e0e0e0;
    color: #5b5b5b;
    padding-left: 20px;
  }

  .content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    background-color: #fff;

    .events-status {
      background-color: red;
    }
  }

}

.overview_section{
  .section_title {
    font-weight: 500;
    margin-left: 8px;
  }
  .section_icon{
    font-size: 18px;
  }
  .section_content{
    display: flex;
  }
}


.static_number{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 50px;
  height: 75px;
  .digital_number {
    transform: scale(.9,1.4) skew(-10deg);
    font-family: 'digital';
    font-size: 25px;
  }

  .number_compare{
    display: flex;
    flex-direction: column;
    color: #bababa;

    .increase {
      .increase_arrow {
        color: red;
      }
      
    }

    .decrease {
      .decrease_arrow {
        color: green;
      }
    }

    .compare_num {
      display: block;
      transform: scale(.9,1.4) skew(-10deg);
      font-family: 'digital';
    }
  }

}

.static_info {
  flex-wrap: wrap;
  gap: 4px
}

.custom_dot{
  display: flex;
  align-items: center;
  gap:5px;
  &::before{
    display: block;
    content:'';
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}

.custom_dot__success {
  &::before{
    background-color: red;
  }
}

.custom_dot__suspend {
  &::before{
    background-color: orange;
  }
}

.custom_dot__try {
  &::before{
    background-color: blue;
  }
}

.threat_event{
  flex-grow: 2;
}

.card_attack_stage{
  flex-basis: 100%;
  margin-top: 10px;

  .item_text {
    border: 1px dashed #ccc;
    border-left: none;
    display: flex;
    align-items: center;
    padding: 5px 5px 5px 10px;
    font-weight: 500;
    text-wrap: nowrap;
    .item_text_num {
      display: inline-block;
      transform: scale(.9,1.4) skew(-10deg);
      font-family: 'digital';
    }
    
  }

  .item_icon {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background-color: #008af3;
    border: 4px solid rgba(208,232,253,.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #fff;
    margin-right: -5px;
  }
}

.pending_threats {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.event_analysis{
  flex: 1;
  display: grid;
  grid-template-columns: 4fr 6fr;
  // grid-template-rows: 300px 300px;
  gap: 10px;
}

.threat_name_dropdown {
  :global {
    .ant-cascader-menus{
      .ant-cascader-menu{
        height: 360px;
      }
    }
  }
}

.scene_form {
  :global {
    .ant-form-item {
      margin-bottom: 0;
      .ant-form-item-label {
        width: 83px;
      }
    }
    
  }
}