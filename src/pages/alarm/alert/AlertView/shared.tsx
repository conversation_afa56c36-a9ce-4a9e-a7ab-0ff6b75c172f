import Link from '@/components/Link';
import { THREAT_LEVEL } from '@/utils/constants';
import { killChainMap, threatFlagImg, threatFlagNameMap, threatLevelColorMap } from '@/utils/enumList';
import moment from 'moment';
import React from 'react';

export interface FilterState {
  groupKey: string;
  dateRange?: [moment.Moment, moment.Moment];
  stopTime?: string;
  threatScore?: [number, number];
  page?: number;
  pageSize?: number;
  confirm?: number;
  sort?: string;
  order?: string;
  celeryId?: string;
  killchain?: string | null;
  mbinfo?: string | null;
  threatFlag?: string | null;
  threatLevel?: string | null;
  threatType?: string | null;
  advanceFilters?: any[];
}

export const TYPE_MAP = {
  vul: '规则告警',
  ioc: '情报告警',
  model: '模型告警',
  file: '文件告警',
};

export const DEFAULT_FILTER_STATE: FilterState = {
  groupKey: 'uniqueId',
  threatScore: [1, 100],
  dateRange: [moment().startOf('day'), moment().endOf('day')],
  page: 1,
  pageSize: 10,
  confirm: undefined,
  sort: 'observedTime',
  order: 'desc',
  celeryId: '',
  killchain: undefined,
  mbinfo: undefined,
  threatFlag: undefined,
  threatLevel: undefined,
  threatType: undefined,
  advanceFilters: [],
};

export const colRenderer = {
  killchain: (killchain: string) => killChainMap[killchain] || killchain,
  threatLevel: (threatLevel: string) => {
    const color = threatLevelColorMap[threatLevel];
    return (
      <div
        style={{ color, borderColor: color, backgroundColor: `${color}1A` }}
        className="inline-block bg-op-20 b b-solid rd !text-center w-15 lh-6"
      >
        {THREAT_LEVEL?.[threatLevel] || threatLevel}
      </div>
    );
  },
  threatScore: (t: number, record: { threatLevel: React.Key }) => {
    return <span style={{ color: threatLevelColorMap[record.threatLevel] }}>{t}</span>;
  },
  threatCategory: (t: string | number) => {
    return (
      <div className="inline-flex items-center lh-1em">
        <img src={threatFlagImg[t]} alt="" className="w-4 h-4" />
        <div className="ml-1">{threatFlagNameMap[t] || '–'}</div>
      </div>
    );
  },
  dateTime: (time: number) => (time ? new Date(time).toLocaleString('swe') : ''),
  area: (srcIpGeoInfo: any) => {
    return [srcIpGeoInfo?.country_name, srcIpGeoInfo?.city_name].filter(Boolean).join(' ');
  },
  alertName: (type: string, record: any) => {
    if (type == 'ioc') {
      return [record?.tools, record?.labels, record?.aptOrganization].filter(Boolean).join(' ');
    } else if (type == 'model') {
      return record?.modelName;
    } else if (type == 'file') {
      return record?.threatName;
    }

    return record?.vulName;
  },
  srcIp: (_: any, record: any) => {
    return (
      <div className="of-hidden">
        <div className="truncate" title={record?.flow?.src_ip}>
          {record?.flow?.src_ip}
        </div>
        <div className="text-sm c-t4 truncate">
          {record.srcIpGeoInfo?.country_name ? (
            <span>
              {record.srcIpGeoInfo.country_name} {record.srcIpGeoInfo.city_name}
            </span>
          ) : (
            '--'
          )}
          {record.srcIpMbInfo?.industry ? (
            <span>
              <span className="px-1">{'/'}</span>
              {record.srcIpMbInfo.industry}
            </span>
          ) : (
            ''
          )}
        </div>
      </div>
    );
  },
  dstIp: (_: any, record: any) => {
    return (
      <div className="of-hidden">
        <div className="truncate" title={record?.flow?.dst_ip}>
          {record?.flow?.dst_ip}
        </div>
        <div className="text-sm c-t4 truncate">
          {record.dstIpGeoInfo?.country_name ? (
            <span>
              {record.dstIpGeoInfo.country_name} {record.dstIpGeoInfo.city_name}
            </span>
          ) : (
            '--'
          )}
          {record.dstIpMbInfo?.industry ? (
            <span>
              <span className="px-1">{'/'}</span>
              {record.dstIpMbInfo.industry}
            </span>
          ) : (
            ''
          )}
        </div>
      </div>
    );
  },
  ioc: (ioc: string) =>
    ioc ? <Link to={`/app/mica/informationManagement?${new URLSearchParams({ ioc })}`}>{ioc}</Link> : '–',
  taskName: (taskName: string, record: any) => {
    if (record.taskType === 'replay') {
      return <Link to={`/app/mica/task?${new URLSearchParams({ taskName })}`}>{taskName}</Link>;
    }
    return '实时任务';
  },
  taskType: (taskType: string) => (taskType === 'replay' ? '回放任务' : '实时任务'),
  ruleId: (sid: string) => (sid ? <Link to={`/app/mica/featureManagement?number=${sid}`}>{`${sid}`}</Link> : '–'),
};

interface SearchPayload {
  startTime?: string;
  stopTime?: string;
  threatScore?: string;
  page?: string;
  pageSize?: string;
  confirm?: string;
  sort?: string;
  order?: string;
  celeryId?: string;
  killchain?: string;
  mbinfo?: string;
  threatFlag?: string;
  threatLevel?: string;
  filter_arg?: any[];
}

export function preparePayload({ dateRange, advanceFilters, threatScore, page = 1, pageSize = 10, ...values }: any) {
  const payload: SearchPayload = { ...values };

  if (dateRange?.length) {
    payload.startTime = (dateRange?.[0]?.unix() * 1000).toString();
    payload.stopTime = (dateRange?.[1]?.unix() * 1000).toString();
  }

  payload.filter_arg = advanceFilters || [];

  if (threatScore.length) {
    payload.threatScore = `${threatScore[0]}-${threatScore[1]}`;
  }

  payload.page = page.toString();
  payload.pageSize = pageSize.toString();

  if (payload.groupKey === 'raw') {
    // 原始
    delete payload.groupKey;
  }

  return payload;
}
