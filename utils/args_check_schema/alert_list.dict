{
    "type": "object",
    "required": ["startTime","stopTime"],
    "properties": {
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "celeryId": {
            "type": "string",
            "default": ""
        },
        "groupKey": {
            "type": "string",
            "default": "",
            "enum": ["uniqueId", "src_ip", "dst_ip"]
        },
        "killchain": {
            "type": "string",
            "default": "",
            "enum": ["Reconnaissance", "Weaponization", "Delivery", "Exploitation", "Installation", "Command and Control", "Actions on Objective"]
        },
        "sid": {
            "type": "string",
            "default": "",
            "pattern": r'''(^[1-9]\d{0,8}$)|(^[1-3]\d{9}$)|(^4[0-1]\d{8}$)|(^42[0-8]\d{7}$)|(^429[0-3]\d{6}$)|(^4294[0-8]\d{5}$)|(^42949[0-5]\d{4}$)|(^429496[0-6]\d{3}$)|(^4294967[0-1]\d{2}$)|(^42949672[0-8]\d$)|(^429496729[0-5]$)'''
        },
        "threatLevel": {
            "type": "string",
            "default": "",
            "enum": ["Low", "Medium", "High"],
        },
        "threatScore": {
            "type": "string",
            "default": "",
            "pattern": r'''^([0-9]|[0-9][0-9]|100|\*)-([0-9]|[0-9][0-9]|100|\*)$'''
        },
        "threatFlag": {
			"type": "string",
			"default": "",
            "enum": ["Exploits and Attacks", "APT", "Malicious Host", "Suspicious", "Botnet", "Phishing",
				"Scanning", "Malware", "DOS", "Trojan", "Mining", "Ransomware", "Spyware", "Webshell", "URL_malware",
				"Brute force"]
		},
        "sort": {
            "type": "string",
            "default": "observedTime",
            "enum": ["observedTime", "threatScore"]
        },
        "order": {
            "type": "string",
            "default": "desc",
            "enum": ["asc", "desc"]
        },
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        },
        "mbinfo": {
            "type": "string",
            "default": ""
        },
        "value": {
            "type": "string",
            "default": ""
        },
        "detail": {
            "type": "boolean",
            "default": False
        },
        "confirm" : {
            "type": "number",
            "enum": [0, 1, 2, 3, 4]
        }
    }
}