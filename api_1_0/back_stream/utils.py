# -*- coding: utf-8 -*-
# @Time    : 2019-05-15 11:11
# <AUTHOR> <PERSON>
# @File    : utils.py
# @Software: PyCharm

import re


def check_args(args):
    """ 根据正则匹配的方式检测每个参数是否有效，
            无效则返回 False 和错误参数
            有效则返回 True"""

    args_map = {'srcIp': check_ip,
                'dstIp': check_ip,
                'srcPort': check_port,
                'dstPort': check_port,
                'appProto': check_protocol,
                'flowId': check_flow_id,
                'sessionId': check_session_id,
                'startTime': check_time,
                'stopTime': check_time,
                'num': check_number,
                'start': check_number,
                'spi': check_spi,
                'ip': check_ip,
                'base': check_base,
                'domain': check_domain}

    for arg, func in args_map.items():
        if arg not in args:
            args[arg] = ""

        if args[arg] and not func(args[arg]):
            return False, "invalid " + arg

    if args['startTime'] and args['stopTime']:
        if int(args['startTime']) > int(args['stopTime']):
            return False, "startTime must less than stopTime"

    return True, ""


def check_domain(domain):
    """eg: **********.in-addr.arpa"""
    compile_domain = re.compile(r'^[a-z0-9\-\.]+$')
    return compile_domain.match(domain)


def check_base(base):
    """check base argument"""
    return base in ['hex', 'ascii', 'natural', 'utf8']


def check_spi(spi):
    """eg: dstIp、protocol、dns.host"""
    compile_spi = re.compile(r'^\w+(\.\w+)?$')
    return compile_spi.match(spi)


def check_ip(ip_addr):
    """check ip addr"""
    compile_ip = re.compile(
        r'''^((1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])\.(1\d{2}|2[0-4]\d|25[
        0-5]|[1-9]\d|\d)\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.(1\d{2}|2[
        0-4]\d|25[0-5]|[1-9]\d|\d)(\/)?([1-9]|[1-2][0-9]|3[0-2])?)$''')
    return compile_ip.match(ip_addr)


def check_port(port):
    """check port"""
    compile_port = re.compile(
        r'''^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}
        |655[0-2][0-9]|6553[0-5])$
        ''')
    return compile_port.match(port)


def check_protocol(protocol):
    """check protocol"""
    compile_protocol = re.compile(r"^[a-zA-Z0-9]+$")
    return compile_protocol.match(protocol)


def check_flow_id(flow_id):
    """eg: 1908583948318982"""
    compile_flow_id = re.compile(r"^\d+$")
    return compile_flow_id.match(flow_id)


def check_session_id(session_id):
    """eg: 190507-J1lj7N_nHV1DXqvDi8ljxu6e"""
    compile_session_id = re.compile(r"^\d{6}-[\w\-_]{24}$")
    return compile_session_id.match(session_id)


def check_time(time):
    """ eg: 1557223391 """
    compile_time = re.compile(r"^\d{10}$")
    return compile_time.match(time)


def check_number(number):
    """check digital"""
    compile_number = re.compile(r"^\d+$")
    return compile_number.match(number)
