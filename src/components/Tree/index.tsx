import React, { useState } from 'react';
import { Tree } from 'antd';

export default props => {
  const { data, onChange } = props;
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const onExpand = expandedKeysValue => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const clickTitle = item => {
    onChange(item);
    const key = item.key;
    setSelectedKeys([key]);
    const tempExpandKeys = [...expandedKeys];
    if (!tempExpandKeys.includes(key)) {
      tempExpandKeys.push(key);
      setExpandedKeys([...tempExpandKeys]);
    } else {
      let i = tempExpandKeys.indexOf(key);
      tempExpandKeys.splice(i, 1);
      setExpandedKeys([...tempExpandKeys]);
    }
  };

  // 自定义节点，将事件添加在节点文字上
  const transfer = data => {
    if (data && data.length) {
      let res = data.map(item => {
        let temp = {
          title: <span onClick={() => clickTitle(item)}>{item.title}</span>,
          key: item.key,
          children: transfer(item.children),
        };
        return temp;
      });

      return res;
    }
  };

  return (
    <Tree
      expandedKeys={expandedKeys}
      autoExpandParent={autoExpandParent}
      selectedKeys={selectedKeys}
      treeData={transfer(data)}
      onExpand={onExpand}
    />
  );
};
