import request, { Response } from '@/utils/request';

export interface UserModule {
  icon: string;
  children: UserModule[];
  system: boolean;
  parentId: string;
  name: string;
  uri: string;
  url: string;
  isMenu: boolean;
  sort: number;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function queryCurrent(): Promise<any> {
  return request('/api/user');
}

// 获取用户的可用模块
export const getUserModule = (url?: string): Promise<Response<UserModule[]>> =>
  request(url || '/api/user/module/list');

// 登出
export const logout = () => request('/api/passport/logout');
