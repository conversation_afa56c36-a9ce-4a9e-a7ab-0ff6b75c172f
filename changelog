# v0.9.0
- 增加：支持异常行为模型流程化
- 增加：支持APT 情报流程化
- 增加：killchains 攻击链
- 增加：支持应用层数据元数据抽取
- 增加：支持全流量存储
- 增加：支持自定义数据包离线处理
- 增加：支持特征管理
- 增加：支持概要报表
- 增加：支持Payload展示
- 增加：支持会话还原

# v1.0.0
- 增加：详细报表模块
- 增加：系统设置模块
- 修复：修复页面时间轴、错误数据导致报表不一致问题
- 修复：Celery 获取系统资源获取bug
- 修复：规则更新无法生效bug
- 优化：调查页面和感知页面的搜索栏和分组查询添加国旗

# v1.0.1
- 增加：添加本地域名解析功能
- 修复：兼容ES数据字段不全导致数据解析错误的问题
- 优化：logstash配置文件替换
- 优化：替换suricata规则文件规则库文件
- 优化：spark行为模型&celery任务的supervisor配置文件更新

# v1.0.2
- 增加：支持用户自定义规则的上传、修改、删除功能
- 优化：添加首页页面攻击者&受害者国旗展示
- 优化：报告页面按顺序返回杀伤链阶段、威胁分类、高中低危预警
