import ftplib
import hashlib
import time
import os
import base64
from flask import request
from config.config import NASConfig, NdrLog
from flask_restful import Resource
from utils import param_check
from utils.logger import get_ndr_logger
from utils.utils import flask_response, ftp_server_connect, ftp_server_disconnect
from utils.utils import run_command
from utils.database import MongoDB
from utils.param_check import Validator, check_flask_args
from api_1_0.auth.auths import Authenticate
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def force_link_nas_server():
    '''
    强制连接客户定义连接的在线数据源
    '''
    mongodb = MongoDB('ndr')
    raw_data = mongodb.find('nas_manager', {})

    if not raw_data:
        return

    for nas_server in raw_data:
        # 由于ftp只在使用时连接，此处不做处理
        if not nas_server['default_status'] or nas_server['serverType'] == 'ftp':
            continue

        filePath = NASConfig.Path + nas_server['aliasName']
        # 已连接的状态强制umount，再mount，并更新状态
        ret, err = run_command('umount -l ' + filePath, timeout=2)

        # 如果存在username和password，则为cifs文件类型
        if nas_server['username'] and nas_server['password']:
            cmd = 'mount -o username=%s,password=%s,nounix,noserverino //%s/%s %s' % (
                nas_server['username'], base64.b64decode(nas_server['password']).decode(),
                nas_server['host'], nas_server['filename'], filePath
            )
        # 否则为nfs文件类型
        else:
            cmd = 'mount %s:%s %s' % (nas_server['host'], nas_server['filename'], filePath)

        ret, out = run_command(cmd, timeout=2)

        # 更新nas服务器连接状态
        mongodb.update(
            'nas_manager',
            {'serverId': nas_server['serverId']},
            {'enable': ret})


def check_nas_server_online(server_id):
    mongodb = MongoDB('ndr')
    raw_data = mongodb.find_one('nas_manager', {'serverId': server_id})

    if not raw_data or not raw_data['default_status']:
        return False

    if 'nas' == raw_data['serverType']:
        file_path = NASConfig.Path + raw_data['aliasName']
        # mount挂载，使用ls检测mount是否断开，断开会触发timeout事件
        ret, out = run_command('ls ' + file_path, timeout=2)

        if not ret or not os.path.exists(file_path):
            return False
    elif 'ftp' == raw_data['serverType']:
        ftp, rst = ftp_server_connect(
            raw_data['host'], raw_data['port'], raw_data['username'], raw_data["password"])
        if not rst:
            return False

        ftp_server_disconnect(ftp)

    return True


class NASResource(Resource):
    def __init__(self):
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()
        self.mongodb = MongoDB("ndr")
        self.collection = 'nas_manager'

    @staticmethod
    def create_nas_server_id():
        """create nas server id"""
        md5_str = hashlib.md5(str(time.clock()).encode('utf-8'))
        return md5_str.hexdigest()

    @staticmethod
    def link_nas_server(kwargs, relink=False):
        if 'nas' == kwargs['serverType']:
            filePath = NASConfig.Path + kwargs['aliasName']

            if not relink and not os.path.exists(filePath):
                # 创建目录用于mount挂载
                os.makedirs(filePath)

            # 如果存在username和password，则为cifs文件类型
            if kwargs['username'] and kwargs['password']:
                cmd = 'mount -o username=%s,password=%s,nounix,noserverino //%s/%s %s' % (
                    kwargs['username'], base64.b64decode(kwargs['password']).decode(),
                    kwargs['host'], kwargs['filename'], filePath
                )
            # 否则为nfs文件类型
            else:
                cmd = 'mount %s:%s %s' % (kwargs['host'], kwargs['filename'], filePath)

            ret, out = run_command(cmd, timeout=3)

            if not ret:
                return False, out

        elif 'ftp' == kwargs['serverType']:
            ftp, rst = ftp_server_connect(
                kwargs['host'], kwargs['port'], kwargs['username'], kwargs["password"])

            if not rst:
                return False, str(ftp)

            ftp_server_disconnect(ftp)

        return True, ''

    @staticmethod
    def get_nas_path_file(path, only_dir, file_list, pcap_list):
        if not os.path.exists(path):
            return

        for file in file_list:
            file_path = os.path.join(path, file)

            if os.path.isdir(file_path):
                pcap_list.append(
                    {
                        "title": file,
                        'key': file_path,
                        "isLeaf": False,
                        # "size": '',
                    }
                )
            elif only_dir == 'true':
                pass
            elif os.path.isfile(file_path):
                # 过滤掉除pcap/cap/pcapng外的文件
                if not file_path.endswith(('.pcap', '.cap', '.pcapng')):
                    continue

                pcap_list.append(
                    {
                        "title": file,
                        "key": file_path,
                        "isLeaf": True,
                        # "size": size_format(os.path.getsize(file_path) / 1024)
                    }
                )
            else:
                continue

    def update_nas_link_status(self, serverId):
        raw_data = self.mongodb.find_one(
            self.collection,
            {'serverId': serverId},
            {'_id': 0})

        status = True
        msg = ''

        if not raw_data:
            return False, 'Not found'

        if not raw_data['default_status']:
            return False, 'not connected'

        if 'nas' == raw_data['serverType']:
            filePath = NASConfig.Path + raw_data['aliasName']

            # mount挂载，使用ls检测mount是否断开，断开会触发timeout事件
            ret, out = run_command('ls ' + filePath, timeout=2)

            if not ret:
                # 尝试连接
                ret, out = self.link_nas_server(raw_data, relink=True)
                if not ret:
                    status, msg = False, '网络文件共享服务器已断开'

        elif 'ftp' == raw_data['serverType']:
            ftp, rst = ftp_server_connect(
                raw_data['host'], raw_data['port'], raw_data['username'], raw_data["password"])

            if not rst:
                status, msg = False, 'FTP服务器已断开'

            ftp_server_disconnect(ftp)

        # 更新nas服务器连接状态
        self.mongodb.update(
            self.collection,
            {'serverId': raw_data['serverId']},
            {'enable': status})

        return status, msg


class NASList(NASResource):
    def __init__(self):
        super(NASList, self).__init__()

    @param_check.check_flask_args(Validator('nas_list_schema'), request)
    def get(self, **kwargs):
        page = int(kwargs['page'])
        pageSize = int(kwargs['pageSize'])
        foldName = kwargs['foldName']
        data = {}
        data['detail'] = []
        if foldName:
            raw_data = self.mongodb.find(self.collection, {"aliasName": foldName}, {'_id': 0})
            for detail in raw_data:
                # 需要先更新状态
                # self.update_nas_link_status(detail['serverId'])
                nas_path = NASConfig.Path + detail['aliasName']
                # 格式化一下
                detail['monitor_dirs'] = [monitor_dir.replace(nas_path, '') for monitor_dir in detail['monitor_dirs']]
                data['detail'].append(detail)
            data['count'] = self.mongodb.find(self.collection, {"aliasName": foldName}).count()

            return flask_response('', True, data)
        else:
            raw_data = self.mongodb.find(self.collection, {}, {'_id': 0}).sort([('createTime', -1)]) \
                .limit(pageSize).skip((page - 1) * pageSize)
            for detail in raw_data:
                # 需要先更新状态
                # self.update_nas_link_status(detail['serverId'])
                nas_path = NASConfig.Path + detail['aliasName']
                # 如果没有监控目录，说明没有配置，不能作为在线数据源使用，删除掉
                if not detail['monitor_dirs']:
                    ret, err = run_command('umount -l ' + nas_path, timeout=2)
                    if ret:
                        run_command('rm -fr ' + nas_path, timeout=2)
                    self.mongodb.delete(self.collection, {"aliasName": detail['aliasName']})
                    continue

                detail['monitor_dirs'] = [monitor_dir.replace(nas_path, '') for monitor_dir in detail['monitor_dirs']]
                data['detail'].append(detail)

            data['count'] = self.mongodb.find(self.collection, {'monitor_dirs': {'$ne': []}}).count()

            return flask_response('', True, data)

    def post(self):
        """
        检测连接
        """
        try:
            server_id = request.json.get('serverId', None)
            if not server_id:
                return flask_response('unknown server id.', True, {'status': 'disconnected'})

            data = self.mongodb.find_one(self.collection, {"serverId": server_id}, {'_id': 0})
            if not data:
                return flask_response('not exists server id.', True, {'status': 'disconnected'})

            status, msg = self.update_nas_link_status(data['serverId'])
            if not status:
                return flask_response(msg, True, {'status': 'disconnected'})
            else:
                return flask_response('', True, {'status': 'connected'})
        except Exception as e:
            return flask_response('', True, {'status': 'disconnected', 'error': str(e)})


class NASOperator(NASResource):
    def __init__(self):
        super(NASOperator, self).__init__()

    @check_flask_args(Validator("nas_create_schema"), request)
    def post(self, **kwargs):
        # 判断是否已存在aliasName，已存在则返回
        raw_data = self.mongodb.find_one(self.collection, {'aliasName': kwargs['aliasName']}, {'_id': 1})

        if raw_data:
            return flask_response(kwargs['aliasName'] + " has exists!!", False, {})

        server_id = self.create_nas_server_id()
        body = {
            'serverId': server_id,
            'serverType': kwargs['serverType'],
            'host': kwargs['host'],
            'username': kwargs['username'],
            'password': kwargs['password'],
            'port': kwargs['port'],
            'filename': kwargs['filename'],
            'default_status': kwargs['enable'],
            'enable': kwargs['enable'],
            'aliasName': kwargs['aliasName'],
            "type": kwargs.get('type', 'pcap'),
            'createByUserName': self.user_name,
            'createByUserId': self.user_id,
            'createTime': int(time.time()) * 1000,
            'monitor_dirs': []
        }

        # 判断是否需要连接，不需要连接则直接保存到数据库，需要连接时挂载成功才会保存到数据库
        if not body['default_status']:
            self.mongodb.insert_one(self.collection, body)

            return flask_response('', True, {})

        ret, out = self.link_nas_server(body)

        if not ret:
            LOG.error("Connect %s server %s failed. Reason: %s" % (
                body['serverType'], body['host'], out))
            ndr_log_to_box(NdrLog.Type.OPERATE, "%s 数据源[%s]添加失败：%s。" % (body['serverType'], body['host'], out))

            return flask_response(out, False, {})

        self.mongodb.insert_one(self.collection, body)

        return flask_response('连接成功，请选择需要监测的目录', True, {'serverId': server_id})

    @check_flask_args(Validator("nas_detail_schema"), request)
    def get(self, **kwargs):
        raw_data = self.mongodb.find_one(self.collection, {'serverId': kwargs['serverId']})
        if not raw_data or not raw_data['default_status']:
            return flask_response('Not connected!', False, {})

        status, ret = self.update_nas_link_status(kwargs['serverId'])

        if not status:
            return flask_response(ret, False, {})

        pcap_list = []

        if not kwargs['path']:
            return flask_response('', True, {'detail': [{'title': '/', 'key': '/', 'isLeaf': False, 'size': ''}]})

        if 'nas' == raw_data['serverType']:
            nas_path = NASConfig.Path + raw_data['aliasName']
            if not os.path.exists(nas_path):
                return flask_response('File not exists', False, {})

            if kwargs['path'] and kwargs['path'] != '/':
                path = os.path.join(nas_path, kwargs['path'])
                if not os.path.exists(path):
                    return flask_response('Path is not exists!', False, {})
            else:
                path = nas_path

            file_list = filter(lambda dir_file: False if dir_file.startswith('.') else True, os.listdir(path))
            self.get_nas_path_file(path, kwargs['onlyDir'], file_list, pcap_list)
        elif 'ftp' == raw_data['serverType']:
            try:
                ftp, rst = ftp_server_connect(
                    raw_data['host'], raw_data['port'], raw_data['username'], raw_data["password"])
                if not rst:
                    return flask_response('FTP can not connect!', False, {})

                path = os.path.join('/', kwargs['path'])

                ftp.cwd(path)
                dir_res = []
                ftp.dir(path, dir_res.append)
                for file in dir_res:
                    # 展示和保存文件或目录
                    if not file.startswith('-') and not file.startswith('d'):
                        continue
                    file_path = file.split(" ")[-1]
                    if file.startswith("d"):
                        pcap_list.append(
                            {
                                'title': file_path,
                                "key": os.path.join(path, file_path),
                                "isLeaf": False,
                                # "size": ''
                            }
                        )
                    elif kwargs['onlyDir'] == 'true':
                        pass
                    else:
                        # file_time = parser.parse(ftp.voidcmd("MDTM %s" % (ftp.pwd() + "/" + file_path))[4:].strip())
                        pcap_list.append(
                            {
                                'title': file_path,
                                "key": os.path.join(path, file_path),
                                "isLeaf": True,
                                # "size": size_format(ftp.size(file_path) / 1024)
                            }
                        )
                ftp_server_disconnect(ftp)
            except ftplib.error_proto:
                return flask_response('FTP proto command error', False, {})
            except Exception as error:
                return flask_response('', False, {'error': str(error)})

        data = dict()
        data['detail'] = pcap_list

        return flask_response('', True, data)

    @check_flask_args(Validator("nas_delete_schema"), request)
    def delete(self, **kwargs):
        delete_list = kwargs['fileList']
        used_source_list = []
        for server in delete_list:
            raw_data = self.mongodb.find_one(
                self.collection,
                {'serverId': server['serverId']},
                {'createByUserId': 1, 'serverType': 1, 'aliasName': 1})

            if not raw_data:
                return flask_response('Not found', False, {})

            task_data = self.mongodb.find_one('back_explore', {'sourceData': server['serverId']}, {'taskName': 1})
            if task_data:
                # 已被任务使用的数据源不允许被删除
                used_source_list.append('数据源[%s]已被任务[%s]使用，不能删除' % (
                    raw_data['aliasName'], task_data['taskName']))
                continue

            if raw_data['createByUserId'] != self.user_id and self.user_name != 'admin':
                LOG.error("Insufficient permissions.")
                return flask_response("Insufficient permissions.", False, {})

            if raw_data['serverType'] == 'nas':
                filePath = NASConfig.Path + raw_data['aliasName']
                ret, err = run_command('umount -l ' + filePath, timeout=2)
                if ret:
                    run_command('rm -fr ' + filePath, timeout=2)

            self.mongodb.delete(self.collection, {'serverId': server['serverId']})

        message = '' if not used_source_list else '、 '.join(used_source_list)

        return flask_response(message, True if not message else False, {})

    @check_flask_args(Validator("nas_modify_schema"), request)
    def put(self, **kwargs):
        raw_data = self.mongodb.find_one(self.collection, {'serverId': kwargs['serverId']})
        if not raw_data:
            return flask_response('Not found', False, {})

        # 如果已经挂载则需要先取消挂载
        if raw_data['enable'] and raw_data['serverType'] == 'nas':
            filePath = NASConfig.Path + raw_data['aliasName']
            ret, err = run_command('umount -l ' + filePath, timeout=2)
            if ret:
                run_command('rm -fr ' + filePath, timeout=2)

        body = {
            'serverType': kwargs['serverType'],
            'username': kwargs['username'],
            'password': kwargs['password'],
            'host': kwargs['host'],
            'port': kwargs['port'] if kwargs['serverType'] == 'ftp' else raw_data['port'],
            'filename': kwargs['filename'] if kwargs['serverType'] == 'nas' else raw_data['filename'],
            'enable': kwargs['enable'],
            'default_status': kwargs['enable'],
            'aliasName': kwargs['aliasName']
        }

        self.mongodb.update(self.collection, {'serverId': kwargs['serverId']}, body)

        if not body['default_status']:
            return flask_response('', True, {})

        ret, out = self.link_nas_server(body)

        if not ret:
            LOG.error("Connect %s server %s failed. Reason: %s" % (
                body['serverType'], body['host'], out))
            ndr_log_to_box(
                NdrLog.Type.OPERATE,
                "%s 数据源[%s]修改失败：%s。" % (
                    body['serverType'], body['host'], out))

            return flask_response(out, False, {})

        return flask_response('', True, {})


class NASMonitorDirs(NASResource):
    def __init__(self):
        super(NASMonitorDirs, self).__init__()

    @check_flask_args(Validator("nas_set_monitor_dirs_schema"), request)
    def post(self, **kwargs):
        raw_data = self.mongodb.find_one(self.collection, {'serverId': kwargs['serverId']})
        if not raw_data or not raw_data['default_status']:
            return flask_response('Not connected!', False, {})

        if not kwargs['monitorDirs']:
            return flask_response('MonitorDirs must not empty!', False, {})

        filter_dirs = []
        child_dirs = []
        # 对选择的监控目录去重，只保留上层目录
        if 'nas' == raw_data['serverType'] and '/' in kwargs['monitorDirs']:
            filter_dirs.append(NASConfig.Path + raw_data['aliasName'] + '/')
        else:
            sort_dirs = sorted([i for i in kwargs['monitorDirs']], key=lambda i: len(i))
            for idx, val in enumerate(sort_dirs):
                if val in child_dirs:
                    continue
                if val not in filter_dirs:
                    filter_dirs.append(val)
                for dirs in sort_dirs[idx + 1:]:
                    if dirs.startswith(val) and dirs[len(val)] == '/':
                        child_dirs.append(dirs)

        self.mongodb.update(self.collection, {'serverId': kwargs['serverId']}, {'monitor_dirs': filter_dirs})

        return flask_response('Config success!', True, {})
