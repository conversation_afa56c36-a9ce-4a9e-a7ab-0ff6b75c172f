/*
 * @Author: 田浩
 * @Date: 2021-09-14 14:40:43
 * @LastEditors: tianh
 * @LastEditTime: 2022-04-13 17:18:47
 * @Descripttion:
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Button,
  Table,
  Popconfirm,
  Upload,
  message,
  Modal,
  Row,
  Col,
} from 'antd';
import { SearchOutlined, UploadOutlined } from "@ant-design/icons";
import {
  addPolicyList,
  getPolicyList,
  delPolicyData,
} from '@/services/configs';
import AddFlowList from './addFlowList';
import style from './style.less';

const Strategy = (props: any) => {
  const [form] = Form.useForm();
  const addForm = useRef<any>(null);
  const [tableData, settableData] = useState([]);
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>(
    'checkbox',
  );
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const [params, setparams] = useState({
    page: 1,
    pageSize: 10,
    target: '',
  });
  const [total, settotal] = useState(0);
  const [visible, setvisible] = useState(false);
  const columns = [
    {
      title: '白名单值',
      dataIndex: 'target',
    },
    {
      title: '类型',
      dataIndex: 'type',
      render: (t: any, record: any) => {
        if (t === 'domain') {
          return '域名';
        } else {
          return 'ipv4';
        }
      },
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
    },
    {
      title: '备注',
      dataIndex: 'tag',
    },
  ];

  const searchList = () => {
    getPolicyList(params).then(res => {
      if (res.flag) {
        settableData(res.data.white_list);
        setselectedRowKeys([]);
        settotal(res.data.total);
      }
    });
  };
  const onsubmit = () => {
    form.validateFields().then((value: any) => {
      setparams({ ...params, ...value });
    });
  };
  const handleSubmit = () => {
    console.log(addForm);
    addForm.current.validateFields().then((value: any) => {
      // console.log(value);
      let arr = Object.values(value);
      let data = [];
      for (let i = 0; i < arr.length; i += 3) {
        let result = [];
        result = arr.slice(i, i + 3);
        let obj = {
          type: result[1],
          tag: result[2],
          target: result[0],
        };
        data.push(obj);
      }
      addPolicyList({ target_list: data }).then(res => {
        if (res.flag) {
          message.success(res.message);
          setvisible(false);
          searchList();
        } else {
          message.error(res.message);
        }
      });
    });
  };

  const deleteAll = () => {
    delPolicyData({ target_list: [] }).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleDelete = () => {
    if (!selectedRowKeys.length) {
      return message.error('请先选择数据');
    }
    let arr: never[] = [];
    selectedRowKeys.forEach(item => {
      arr.push(tableData[item].target);
    });
    delPolicyData({ target_list: arr }).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const rowSelection = {
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys,
  };
  const upload = {
    name: 'fileName',
    action: '/mica-api/api/v1/system/fss_policy/import',
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info: any) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} file uploaded successfully`);
        searchList();
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    beforeUpload: (file: any) => {
      let pos = file.name.lastIndexOf('.');
      let result = file.name.substring(pos + 1, file.name.length);
      if (!['xls', 'xlsx'].includes(result)) {
        message.error('仅支持上传xls/xlsx格式的文件');
        return Upload.LIST_IGNORE;
      }
    },
  };
  // 分页change事件
  const pagination = {
    total: total,

    current: params.page || 1,
    pageSize: params.pageSize,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  useEffect(() => {
    // searchList();
  }, []);
  useEffect(() => {
    searchList();
  }, [params]);

  return (
    <div>
      <Form className="form-inline" form={form} layout="inline">
        <Row>
          <Col span={6}>
            <Form.Item label="白名单值" name="target">
              <Input allowClear placeholder='请输入过滤白名单' />
            </Form.Item>
          </Col>
          <Col span={18} >
            <Form.Item>
              <div className={style.btn_layout}>
                <Button type="primary" onClick={onsubmit} icon={<SearchOutlined />} className='margin_right_10'>
                  查询
                </Button>
                <Upload {...upload} accept=".xls, .xlsx">
                  <Button type="primary" className='margin_right_10' ghost icon={<UploadOutlined />}>导入</Button>
                </Upload>
                <Button
                  type="primary"
                  onClick={() => {
                    setvisible(true);
                  }}
                  className='margin_right_10'
                  ghost
                >
                  添加
                </Button>
                <Popconfirm title="确定删除吗?" onConfirm={handleDelete}>
                  <Button type="primary" className='margin_right_10' ghost danger>
                    删除
                  </Button>
                </Popconfirm>
                <Popconfirm title="确定删除全部吗?" onConfirm={deleteAll}>
                  <Button type="primary" ghost danger>
                    删除全部
                  </Button>
                </Popconfirm>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowSelection={{
          type: selectionType,
          ...rowSelection,
        }}
        rowKey={(r, i) => i}
        pagination={pagination}
        dataSource={tableData}
        columns={columns}
      />
      <Modal
        title="添加"
        getContainer={false}
        width={900}
        destroyOnClose={true}
        visible={visible}
        onCancel={() => {
          setvisible(false);
        }}
        onOk={handleSubmit}
      >
        {visible && <AddFlowList ref={addForm} />}
      </Modal>
    </div>
  );
};
export default Strategy;
