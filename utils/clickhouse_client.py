# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : clickhouse_client.py
# @Software: PyCharm

"""ClickHouse客户端工具类"""

from typing import List, Dict, Any, Optional
from clickhouse_driver import Client as CH_client
from clickhouse_driver import errors as CH_errors
from config.config import ClickHouseConfig, NdrLog
from utils.logger import get_ndr_logger
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class ClickHouseClient:
    """ClickHouse客户端封装类"""
    
    def __init__(self, host: str = None, user: str = None, password: str = None, database: str = None):
        """
        初始化ClickHouse客户端
        
        Args:
            host: ClickHouse主机地址
            user: 用户名
            password: 密码
            database: 数据库名
        """
        self.host = host or ClickHouseConfig.CH_HOST
        self.user = user or ClickHouseConfig.CH_USER
        self.password = password or ClickHouseConfig.CH_PASSWORD
        self.database = database or ClickHouseConfig.CH_DATABASE
        
        self.client = CH_client(
            host=self.host,
            user=self.user,
            password=self.password,
            database=self.database
        )
    
    def execute(self, sql: str, with_column_types: bool = False) -> Optional[Any]:
        """
        执行SQL查询
        
        Args:
            sql: SQL语句
            with_column_types: 是否返回列类型信息
            
        Returns:
            查询结果
        """
        try:
            return self.client.execute(sql, with_column_types=with_column_types)
        except Exception as e:
            LOG.error(f"ClickHouse查询失败: {sql}, 错误: {str(e)}")
            raise e
    
    def execute_with_dict(self, sql: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询并返回字典格式结果
        
        Args:
            sql: SQL语句
            
        Returns:
            字典格式的查询结果列表
        """
        try:
            raw = self.client.execute(sql, with_column_types=True)
            if not raw or not raw[0]:
                return []
            
            # raw[0] 是数据行列表，raw[1] 是列信息列表
            raw_val = raw[0]  # 数据值
            raw_key = raw[1]  # 列名和类型信息
            
            result_list = []
            for row in raw_val:
                data = {}
                for idx, value in enumerate(row):
                    column_name = raw_key[idx][0]
                    # 处理字节类型数据
                    if isinstance(value, bytes):
                        value = value.decode('utf-8', errors='ignore')
                    data[column_name] = value
                result_list.append(data)
            
            return result_list
            
        except CH_errors.NetworkError:
            error_msg = "ClickHouse数据库连接失败！"
            LOG.error(error_msg)
            ndr_log_to_box(NdrLog.Type.RUN, error_msg)
            return []
        except CH_errors.ServerException as e:
            if e.code == CH_errors.ErrorCodes.TOO_SLOW:
                error_msg = "ClickHouse查询超时！"
            else:
                error_msg = f"ClickHouse查询错误，Code：{e.code}"
            LOG.error(f"{error_msg} SQL: {sql}")
            ndr_log_to_box(NdrLog.Type.RUN, error_msg)
            return []
        except Exception as e:
            error_msg = f"ClickHouse未知错误: {str(e)}"
            LOG.error(f"{error_msg} SQL: {sql}")
            ndr_log_to_box(NdrLog.Type.RUN, error_msg)
            return []
    
    def get_dpi_logs(self, table: str, start_time: str, end_time: str, 
                     conditions: str = "", limit: int = 10000) -> List[Dict[str, Any]]:
        """
        获取DPI日志数据
        
        Args:
            table: 表名
            start_time: 开始时间
            end_time: 结束时间
            conditions: 额外的查询条件
            limit: 限制返回行数
            
        Returns:
            DPI日志数据列表
        """
        sql = f"""
            SELECT * FROM {table}
            WHERE ts >= '{start_time}' AND ts <= '{end_time}'
            {conditions}
            ORDER BY ts DESC
            LIMIT {limit}
        """
        
        return self.execute_with_dict(sql)
    
    def get_connection_stats(self, start_time: str, end_time: str, 
                           ip_condition: str = "", group_by: str = "src_ip") -> List[Dict[str, Any]]:
        """
        获取连接统计信息
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            ip_condition: IP条件
            group_by: 分组字段
            
        Returns:
            连接统计数据
        """
        sql = f"""
            SELECT 
                {group_by},
                count(*) as connection_count,
                sum(src_bytes + dst_bytes) as total_bytes,
                sum(src_pkts + dst_pkts) as total_packets,
                avg(duration) as avg_duration,
                max(duration) as max_duration
            FROM dpilog_conn
            WHERE ts >= '{start_time}' AND ts <= '{end_time}'
            {ip_condition}
            GROUP BY {group_by}
            ORDER BY connection_count DESC
            LIMIT 100
        """
        
        return self.execute_with_dict(sql)
    
    def get_dns_queries(self, start_time: str, end_time: str, 
                       domain_pattern: str = "", ip_condition: str = "") -> List[Dict[str, Any]]:
        """
        获取DNS查询数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            domain_pattern: 域名模式
            ip_condition: IP条件
            
        Returns:
            DNS查询数据
        """
        conditions = []
        if domain_pattern:
            conditions.append(f"query LIKE '%{domain_pattern}%'")
        if ip_condition:
            conditions.append(ip_condition)
        
        condition_str = " AND " + " AND ".join(conditions) if conditions else ""
        
        sql = f"""
            SELECT *
            FROM dpilog_dns
            WHERE ts >= '{start_time}' AND ts <= '{end_time}'
            {condition_str}
            ORDER BY ts DESC
            LIMIT 10000
        """
        
        return self.execute_with_dict(sql)
    
    def get_http_requests(self, start_time: str, end_time: str, 
                         host_pattern: str = "", ip_condition: str = "") -> List[Dict[str, Any]]:
        """
        获取HTTP请求数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            host_pattern: 主机名模式
            ip_condition: IP条件
            
        Returns:
            HTTP请求数据
        """
        conditions = []
        if host_pattern:
            conditions.append(f"host LIKE '%{host_pattern}%'")
        if ip_condition:
            conditions.append(ip_condition)
        
        condition_str = " AND " + " AND ".join(conditions) if conditions else ""
        
        sql = f"""
            SELECT *
            FROM dpilog_http
            WHERE ts >= '{start_time}' AND ts <= '{end_time}'
            {condition_str}
            ORDER BY ts DESC
            LIMIT 10000
        """
        
        return self.execute_with_dict(sql)
    
    def get_time_series_data(self, table: str, start_time: str, end_time: str,
                           ip_condition: str = "", time_interval: str = "1h") -> List[Dict[str, Any]]:
        """
        获取时间序列数据
        
        Args:
            table: 表名
            start_time: 开始时间
            end_time: 结束时间
            ip_condition: IP条件
            time_interval: 时间间隔
            
        Returns:
            时间序列数据
        """
        sql = f"""
            SELECT 
                toStartOfInterval(ts, INTERVAL {time_interval}) as time_bucket,
                count(*) as count,
                sum(src_bytes + dst_bytes) as total_bytes,
                sum(src_pkts + dst_pkts) as total_packets
            FROM {table}
            WHERE ts >= '{start_time}' AND ts <= '{end_time}'
            {ip_condition}
            GROUP BY time_bucket
            ORDER BY time_bucket
        """
        
        return self.execute_with_dict(sql)
    
    def disconnect(self):
        """断开连接"""
        try:
            if hasattr(self, 'client'):
                self.client.disconnect()
        except Exception as e:
            LOG.error(f"断开ClickHouse连接失败: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        self.disconnect()


# 创建全局ClickHouse客户端实例
def get_clickhouse_client() -> ClickHouseClient:
    """获取ClickHouse客户端实例"""
    return ClickHouseClient()
