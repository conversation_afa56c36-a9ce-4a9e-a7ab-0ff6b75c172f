# -*- coding: utf-8 -*-
# @Time    : 2019-08-05 14:47
# <AUTHOR> <PERSON>
# @File    : get_threat_flag.py
# @Software: PyCharm
""" 获取威胁分类 """
from utils.ndr_base import NdrResource
from utils.utils import flask_response


def get_threat_flag_cn(threat_flag_en):
    """
    :return:
    """
    for threat_flag in get_threat_flag():
        if threat_flag_en == threat_flag["threatFlag"]:
            return threat_flag["threatFlagCN"]
    return None


def get_threat_flag():
    """
    :return:
    """
    data = [
        {
            "threatFlag": "Exploits and Attacks",
            "threatFlagCN": "攻击利用",
            "imagePath": "/api/v1/image_threatFlag/treat_Exploits.png"
        },
        {
            "threatFlag": "APT",
            "threatFlagCN": "APT攻击",
            "imagePath": "/api/v1/image_threatFlag/treat_APT.png"
        },
        {
            "threatFlag": "Malicious Host",
            "threatFlagCN": "恶意主机",
            "imagePath": "/api/v1/image_threatFlag/treat_Malicious.png"
        },
        {
            "threatFlag": "Suspicious",
            "threatFlagCN": "可疑行为",
            "imagePath": "/api/v1/image_threatFlag/treat_Suspicious.png"
        },
        {
            "threatFlag": "Botnet",
            "threatFlagCN": "僵尸网络",
            "imagePath": "/api/v1/image_threatFlag/treat_Botnet.png"
        },
        {
            "threatFlag": "Phishing",
            "threatFlagCN": "钓鱼邮件",
            "imagePath": "/api/v1/image_threatFlag/treat_Phishing.png"
        },
        {
            "threatFlag": "Scanning",
            "threatFlagCN": "恶意扫描",
            "imagePath": "/api/v1/image_threatFlag/treat_Scanning.png"
        },
        {
            "threatFlag": "Malware",
            "threatFlagCN": "恶意软件",
            "imagePath": "/api/v1/image_threatFlag/treat_Malware.png"
        },
        {
            "threatFlag": "DOS",
            "threatFlagCN": "DOS攻击",
            "imagePath": "/api/v1/image_threatFlag/treat_DoS.png"
        },
        {
            "threatFlag": "Trojan",
            "threatFlagCN": "远控木马",
            "imagePath": "/api/v1/image_threatFlag/treat_Trojan.png"
        },
        {
            "threatFlag": "Mining",
            "threatFlagCN": "挖矿木马",
            "imagePath": "/api/v1/image_threatFlag/treat_Mining.png"
        },
        {
            "threatFlag": "Ransomware",
            "threatFlagCN": "勒索软件",
            "imagePath": "/api/v1/image_threatFlag/treat_Ransomware.png"
        },
        {
            "threatFlag": "Spyware",
            "threatFlagCN": "间谍软件",
            "imagePath": "/api/v1/image_threatFlag/treat_Spyware.png"
        },
        {
            "threatFlag": "Webshell",
            "threatFlagCN": "WEBSHELL",
            "imagePath": "/api/v1/image_threatFlag/treat_Webshell.png"
        },
        {
            "threatFlag": "URL_malware",
            "threatFlagCN": "恶意网站",
            "imagePath": "/api/v1/image_threatFlag/treat_URL_malware.png"
        },
        {
            "threatFlag": "Brute force",
            "threatFlagCN": "爆破",
            "imagePath": "/api/v1/image_threatFlag/treat_Brute.png"
        }
    ]
    return data


class GetThreatFlag(NdrResource):
    """获取威胁分类字段信息"""

    @staticmethod
    def get():
        """
        :return:
        """

        return flask_response("", True, get_threat_flag())
