
syntax = "proto3";


package die;

// The die service definition.
service DIEService {
  // for feature upgrade|recover
  rpc Feature (stream DIEFeatureRequest) returns (ReplyHeader) {}
  // for feature add|modify
  rpc AddFeature (stream DIEAddFeatureRequest) returns (ReplyHeader) {}
  // for feature del|enable|disable
  rpc OperateFeature (DIEOperateFeatureRequest) returns (ReplyHeader) {}
  // for new-session log start|stop
  rpc NewSsnLogSwitch (DIENewSsnLogSwitchRequest) returns (ReplyHeader) {}
  // for file restore protocol
  rpc FileRestoreProtocol (FileRestoreAppRequest) returns (ReplyHeader) {}
  // for file restore type
  rpc FileRestoreType (FileRestoreTypeRequest) returns (ReplyHeader) {}
  // for file restore size
  rpc FileRestoreSize (FileRestoreSizeRequest) returns (ReplyHeader) {}
}

message ReplyHeader {
  uint32 code = 1;
  string message = 2;
}

message DIEFeatureRequest {
  oneof request {
    Metadata metadata = 1;
    bytes file = 2;
  }
}

message DIEAddFeatureRequest {
  oneof request {
    Metadata metadata = 1;
    bytes rule = 2;
    bytes rule_info = 3;
  }
}

message Metadata {
  string action = 1;
  int64 size = 2;
}

message DIEOperateFeatureRequest {
  repeated uint32 sid = 1; // arrary
  string action = 2; // delete|enable|disable
}

message DIENewSsnLogSwitchRequest {
    bool enable = 1;
}

message FileRestoreAppRequest {
  repeated uint32 appid = 1; // arrary
}

message FileRestoreTypeRequest {
  uint64 type = 1;
}

message FileRestoreSizeRequest {
  uint32 min = 1;
  uint32 max = 2;
}
