/*
 * @Author: tianh
 * @Date: 2021-11-02 12:10:27
 * @LastEditors: tianh
 * @LastEditTime: 2021-11-23 14:26:02
 * @Descripttion:
 */
let formValue = sessionStorage.getItem('formValue')
  ? JSON.parse(sessionStorage.getItem('formValue'))
  : {
      taskType: 'singleTask',
      name: '',
      featureGroupList: '',
      pcapFilesList: [],
      status: '',
      curFeatureGrpList: [],
      taskName: '',
      sourceData: '',
      pcapRelayStatusDir: [],
      dir_name: '',
      schedule: {
        period: 'month',
        baseDate: '',
        baseTime: '',
      },
      type: 'pcap',
      taskId: '',
      dir: '',
    };
let actionType = sessionStorage.getItem('actionType')
  ? sessionStorage.getItem('actionType')
  : 'add';

const defaultState = {
  formValue,
  actionType,
}; //默认数据
export default (state = defaultState, action: { type: string; value: any }) => {
  //就是一个方法函数
  if (action.type === 'changeForm') {
    let newState = JSON.parse(JSON.stringify(state));
    newState.formValue = action.value;
    sessionStorage.setItem('formValue', JSON.stringify(action.value));
    return newState;
  }
  if (action.type === 'actionType') {
    let newState = JSON.parse(JSON.stringify(state));
    newState.actionType = action.value;
    sessionStorage.setItem('actionType', action.value);
    return newState;
  }
  return state;
};
