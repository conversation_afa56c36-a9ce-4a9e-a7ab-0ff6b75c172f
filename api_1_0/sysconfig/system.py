# -*- coding: utf-8 -*-
# @Time    : 2019-06-19 17:10
# <AUTHOR> wu
# @File    : system.py
# @Software: PyCharm

"""module system"""
import shutil
import time
import json
import os
import datetime

from api_1_0.hdp_grpc.die import grpc_set_file_protocol, grpc_set_file_type, grpc_set_file_size
from celery_tasks import app
import psutil
from flask import request
from flask_restful import Resource
from config.config import NdrLog, Spark, FileRestoreCfg
from utils import param_check
from utils.ndr_base import NdrResource
from utils.utils import flask_response, supervisorctl_host_service, run_command
from utils.database import MongoDB, get_es_client
from utils.logger import get_ndr_logger
from utils.json_format import JSONEncoder
from utils.param_check import Validator, check_flask_args
from utils.best_show_size import size_format
from utils.cluster import *
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class Reboot(Resource):
    """system reboot"""

    @staticmethod
    def post():
        """reboot server"""
        LOG.info("system reboot now")
        ndr_log_to_box(NdrLog.Type.OPERATE, "系统重启.")
        app.send_task('host_celery.tasks.run_command_in_host', args=['reboot'],
                      queue='host_task', routing_key='host_task')


class PowerOff(Resource):
    """system power off"""

    @staticmethod
    def post():
        """power off server"""
        LOG.info("system shutdown now")
        ndr_log_to_box(NdrLog.Type.OPERATE, "系统关机.")
        app.send_task('host_celery.tasks.run_command_in_host', args=['shutdown -h now'],
                      queue='host_task', routing_key='host_task')


class DeleteEsData(NdrResource):
    """delete data"""

    @param_check.check_flask_args(Validator("es_data_delete"), request)
    def post(self, **kwargs):
        es_template = {
            "query": {
                "bool": {
                    "must": []
                }
            }
        }
        start_time = int(kwargs["startTime"])
        stop_time = int(kwargs["stopTime"])
        query = {
            "range": {
                "occurredTime": {
                    "gte": start_time,
                    "lte": stop_time
                }
            }
        }
        es_template["query"]["bool"]["must"].append(query)
        es_client = get_es_client()
        delete_arg = kwargs["deleteArg"]
        try:
            for key, value in delete_arg.items():
                if value:
                    es_template["query"]["bool"]["must"].append({"term": value})
                    es_client.delete_by_query(index=key, body=es_template, conflicts='proceed',
                                              wait_for_completion=False)
                    es_template["query"]["bool"]["must"].pop(1)
                else:
                    es_client.delete_by_query(index=key, body=es_template, conflicts='proceed',
                                              wait_for_completion=False)
        except Exception as e:
            LOG.info("es data delete failed reason: %s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "es数据清除失败")
            return flask_response("es data delete failed", False, {})
        LOG.info("es data delete succ")
        ndr_log_to_box(NdrLog.Type.OPERATE, "es数据清除成功")
        return flask_response("es data delete success", True, {})


class DeleteSystemData(NdrResource):
    """delete data"""

    @param_check.check_flask_args(Validator("system_data_delete"), request)
    def post(self, **kwargs):
        """power off server"""
        clickhouse = kwargs["clickhouse"]
        all_flow = kwargs["allFlow"]
        flow_file = kwargs["flowFile"]
        assets = kwargs["assets"]
        if assets == "true":
            mongodb = MongoDB("ndr")
            mongodb.delete("assets_info", {})
        ndr_log_to_box(NdrLog.Type.OPERATE, "清除系统数据.")
        app.send_task('host_celery.tasks.run_command_in_host',
                      args=['sh /opt/host_celery/ndr_data_clean.sh %s %s %s' % (clickhouse, flow_file, all_flow)],
                      queue='host_task', routing_key='host_task')
        run_command("supervisorctl restart stenographer stenographer_pcap")


class GetSystemStatus(Resource):
    """get system status"""

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @staticmethod
    def get_net_data(device):
        """
        :param device:
        :return:
        """
        receive = 0
        transmit = 0
        with open('/proc/net/dev', 'r') as file:
            for line in file:
                if line.find(device) >= 0:
                    receive = line.split(':')[1].split()[0]
                    transmit = line.split(':')[1].split()[8]
        return int(receive), int(transmit)

    def speed_monitor(self, device):
        """
        :param device:
        :return:
        """
        receive_old, transmit_old = self.get_net_data(device)
        time.sleep(1)
        receive, transmit = self.get_net_data(device)
        speed = {'rx': size_format((receive - receive_old) / 1024) + "B/S",
                 'tx': size_format((transmit - transmit_old) / 1024) + "B/S"}
        return speed

    def get_net_card_info(self):
        """
        :return:
        """
        net_card_info = []
        n_net = self.mongodb.find_one('network', {'type': "worker"}, {"_id": 0, "workInterfaceList": 1})
        if n_net:
            net_card_info = n_net["workInterfaceList"]
        m_net = self.mongodb.find_one('network', {'type': "manager"}, {"_id": 0, "name": 1})
        if m_net:
            net_card_info.append(m_net["name"])
        return net_card_info

    def get(self):
        """
        :return:
        """
        boot_time = psutil.boot_time()
        cpu_time = psutil.cpu_times_percent()
        cpu_percent = psutil.cpu_percent()
        memory_info = psutil.virtual_memory()
        disk_info = psutil.disk_usage('/')
        net = []
        net_info = psutil.net_io_counters(pernic=True)
        net_card = self.get_net_card_info()
        net_address = psutil.net_if_addrs()
        for card in net_card:
            address = net_address[card][0][1]
            if card in net_address[card][0][1]:
                address = net_address[card][1][1]
            if card in list(net_info.keys()):
                net.append({"netName": card,
                            "address": address,
                            "totalSend": size_format(net_info[card][0] / 1000),
                            "totalRecv": size_format(net_info[card][1] / 1000),
                            "packetsSend": net_info[card][2],
                            "packetsRecv": net_info[card][3],
                            "netSpeed": self.speed_monitor(card)})
        status = {"bootTime": int(boot_time * 1000),
                  "cpuUser": cpu_time[0],
                  "cpuNice": cpu_time[1],
                  "cpuSystem": cpu_time[2],
                  "cpuIdle": cpu_time[3],
                  "cpuPercent": cpu_percent,
                  "diskUsage": disk_info[3],
                  "memPercent": memory_info[2],
                  "memTotal": memory_info[0],
                  "netInfo": net}
        return flask_response("", True, {"status": status})


class GetSystemHistoryStatus(Resource):
    """get system history status"""

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @check_flask_args(Validator('system_history_status'), request)
    def post(self, **kwargs):
        """
        :return:
        """
        his_type = kwargs['args']['type']
        if his_type == "cpu":
            condition = {"_id": 0,
                         "recordTime": 1,
                         "status.cpuIdle": 1,
                         "status.cpuNice": 1,
                         "status.cpuSystem": 1,
                         "status.cpuUser": 1,
                         "status.cpuPercent": 1
                         }
        elif his_type == "memory":
            condition = {"_id": 0,
                         "recordTime": 1,
                         "status.memPercent": 1,
                         "status.memTotal": 1,
                         }
        else:
            condition = {"_id": 0,
                         "recordTime": 1,
                         "status.diskUsage": 1
                         }
        status = self.mongodb.find("system_hours_status", {}, condition).sort([("recordTime", -1)])
        cols = []
        for col in status:
            col["recordTime"] = col["recordTime"] + datetime.timedelta(hours=8)
            col["recordTime"] = int(col["recordTime"].replace().timestamp() * 1000)
            col = JSONEncoder().encode(col)
            cols.append(json.loads(col))
        return flask_response("", True, {"history_status": cols})


class SetSystemConfig(Resource):

    def __init__(self):
        """初始化参数"""
        self.mongodb = MongoDB("ndr")
        self.system_config = 'system_config'

    def get(self):
        data = {}
        obj = self.mongodb.find('system_config', {})
        if obj is None:
            return flask_response('Not found', False, {})
        col = json.loads(JSONEncoder().encode(obj[0]))
        col["software"] = "V2"
        data["detail"] = col
        return flask_response("", True, data)

    @check_flask_args(Validator('system_config'), request)
    def post(self, **kwargs):
        realtime = kwargs['args']['realtime']
        divice = kwargs['args']['divice']

        org_data = self.mongodb.find_one(self.system_config, {'divice': divice})
        if not org_data:
            return flask_response('Database not complete!!', False, {})

        if realtime == org_data['realtime']:
            return flask_response('已配置为 %s!!' % ('偏实时' if realtime else '偏回放'), False, {})

        try:
            shutil.copy(Spark.ReplayPath, Spark.TempPath)
            os.remove(Spark.ReplayPath)
            shutil.copy(Spark.RealtimePath, Spark.ReplayPath)
            os.remove(Spark.RealtimePath)
            shutil.copy(Spark.TempPath, Spark.RealtimePath)
            os.remove(Spark.TempPath)
            os.system("sed -i 's/Streaming/Replay/' %s" % Spark.ReplayPath)
            os.system("sed -i 's/model_stream/model_replay/' %s" % Spark.ReplayPath)
            os.system("sed -i 's/Replay/Streaming/' %s" % Spark.RealtimePath)
            os.system("sed -i 's/model_replay/model_stream/' %s" % Spark.RealtimePath)
            self.mongodb.update(self.system_config, {"divice": divice}, {"realtime": realtime})
        except Exception as e:
            LOG.error("偏实时/回访设置failed:%s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "偏实时/回访设置失败")
            return flask_response("偏实时/回访设置失败 failed", False, {})
        supervisorctl_host_service('restart', 'sparkModelReplay')
        supervisorctl_host_service('restart', 'sparkModelStream')
        msg, ret = self.hdp_resource_update(divice, realtime)
        if not ret:
            return flask_response(msg, False, {})
        ndr_log_to_box(NdrLog.Type.OPERATE, "偏实时/回访设置成功")
        return flask_response("success", True, {})

    def hdp_resource_update(self, divice, realtime):
        # 设备配置信息，TODO 考虑放到Mogo的配置文件中去
        config_map = {
            "D5000": {
                'socket_mem': [9000, 7000],
                'steno_blocks': [128, 64],
                'session_numb': [1024, 256]
            },
            "D5010": {
                'socket_mem': [5120, 3584],
                'steno_blocks': [128, 64],
                'session_numb': [1024, 256]
            },
            "D8000": {
                'socket_mem': [53760, 47616],
                'steno_blocks': [512, 256],
                'session_numb': [4096, 1024]
            },
            "D8010": {
                'socket_mem': [26624, 14336],
                'steno_blocks': [512, 256],
                'session_numb': [8192, 2048]
            },
            "D8600": {
                'socket_mem': [26624, 14336],
                'steno_blocks': [512, 256],
                'session_numb': [8192, 2048]
            }
        }

        index = 0 if realtime else 1  # 获取实时和回放的编号
        for cfg, val_list in config_map[divice].items():
            os.system("sed -i 's/^%s.*$/%s = %d/' /opt/hdp/config/conf.ini" % (cfg, cfg, val_list[index]))

        supervisorctl_host_service('restart', 'hdp')
        app.send_task('host_celery.tasks.reinit_replay_locres_queue',
                      args=[realtime, ], queue='host_task', routing_key='host_task')

        # FIXME: 能否直接覆盖文件conf.ini
        cluster_sync_file_and_restart_hdp('/opt/hdp/config/conf.ini')

        return 'Set model success', True


class SetSystemAlarm(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.system_config = 'system_config'

    @check_flask_args(Validator('system_alarm'), request)
    def post(self, **kwargs):
        alarm_filter = kwargs['args']['alarmFilter']
        alarm_merge = int(kwargs['args']['alarmMerge'])
        alarm_out = int(kwargs['args']['alarmOut'])
        performance = kwargs['args']['performance']
        self.mongodb.update(self.system_config, {"performance": performance},
                            {"alarm_filter": alarm_filter, "alarm_merge": alarm_merge, "alarm_out": alarm_out})
        return flask_response("alarm fliter success", True, {})


class FileProtocol(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    def get(self):
        data = self.mongodb.find_one(FileRestoreCfg.COLLECTION, {})
        if not data:
            return flask_response("get file protocol config failed!", False, {})

        return flask_response("", True, data['file_protocol'])

    @check_flask_args(Validator('system_file_protocol'), request)
    def post(self, **kwargs):
        data = self.mongodb.find_one(FileRestoreCfg.COLLECTION, {})
        if not data:
            return flask_response("set file protocol config failed!", False, {})

        for k, v in kwargs['file_protocol'].items():
            if data.get(k, None) != v:
                file_app_list = [FileRestoreCfg.APPID_MAP[i] for i, j in kwargs['file_protocol'].items() if j == 1]
                file_app_cfg = '(%s)' % ','.join([str(m) for m in file_app_list])
                ret, msg = run_command('sed -i "s/^file_app.*/file_app = %s/g" /opt/hdp/config/conf.ini' % file_app_cfg)
                if not ret:
                    return flask_response("Modify hdp file_app parameter failed(%s)." % msg, False, {})
                self.mongodb.update(FileRestoreCfg.COLLECTION, {}, {'file_protocol': kwargs['file_protocol']})
                rsp = grpc_set_file_protocol(file_app_list)
                print("file protocol get response: ", rsp, flush=True)
                cluster_hdp_set_file_protocol(file_app_cfg, file_app_list)

                return flask_response('set file protocol config success.', True, {})

        return flask_response('file protocol config not changed.', True, {})


class FileType(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    def get(self):
        data = self.mongodb.find_one(FileRestoreCfg.COLLECTION, {})
        if not data:
            return flask_response("get file type config failed!", False, {})

        return flask_response("", True, data['file_type'])

    @check_flask_args(Validator('system_file_type'), request)
    def post(self, **kwargs):
        data = self.mongodb.find_one(FileRestoreCfg.COLLECTION, {})
        if not data:
            return flask_response("set file type config failed!", False, {})

        for k, v in kwargs['file_type'].items():
            if data.get(k, None) != v:
                file_type_cfg = 0
                for i, j in kwargs['file_type'].items():
                    if j == 1:
                        for bit in FileRestoreCfg.TYPEID_MAP[i]:
                            file_type_cfg |= 1 << bit
                ret, msg = run_command(
                    'sed -i "s/^file_type.*/file_type = %s/g" /opt/hdp/config/conf.ini' % hex(file_type_cfg))
                if not ret:
                    return flask_response("Modify hdp file_type parameter failed(%s)." % msg, False, {})
                self.mongodb.update(FileRestoreCfg.COLLECTION, {}, {'file_type': kwargs['file_type']})
                rsp = grpc_set_file_type(file_type_cfg)
                print("file type get response: ", rsp, flush=True)
                cluster_hdp_set_file_type(file_type_cfg)

                return flask_response('set file type config success.', True, {})

        return flask_response('file type config not changed.', True, {})


class FileSize(Resource):
    def __init__(self):
        self.mongodb = MongoDB('ndr')

    def get(self):
        data = self.mongodb.find_one(FileRestoreCfg.COLLECTION, {})
        if not data:
            return flask_response("get file size config failed!", False, {})

        return flask_response("", True, data['file_size'])

    @check_flask_args(Validator('system_file_size'), request)
    def post(self, **kwargs):
        data = self.mongodb.find_one(FileRestoreCfg.COLLECTION, {})
        if not data:
            return flask_response("set file size config failed!", False, {})

        # 参数是KB，统一用byte单位
        min_size = kwargs['file_size']['min']
        max_size = kwargs['file_size']['max']

        if min_size > max_size or max_size > 20 * 1024 * 1024:
            return flask_response('file size config set failed. para is wrong !', False, {})

        if data['file_size']['min'] == min_size and data['file_size']['max'] == max_size:
            return flask_response('file size config not changed.', True, {})

        ret, msg = run_command('sed -i "s/^file_size.*/file_size = (%d,%d)/g" /opt/hdp/config/conf.ini' % (min_size, max_size))
        if not ret:
            return flask_response("Modify hdp file_size parameter failed(%s)." % msg, False, {})

        self.mongodb.update(FileRestoreCfg.COLLECTION, {}, {'file_size': {'min': min_size, 'max': max_size}})

        rsp = grpc_set_file_size(min_size, max_size)
        print('file size get response: ', rsp, flush=True)
        cluster_hdp_set_file_size(min_size, max_size)

        return flask_response('set file size config success.', True, {})
