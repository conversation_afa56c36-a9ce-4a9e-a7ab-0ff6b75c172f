/*
 * @Author: tianh
 * @Date: 2022-01-17 16:55:17
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-14 15:38:43
 * @Descripttion:
 */

const defaultState = {
  columns: [],
  hidden: [],
  datas: [],
  links: [],
};
export default (state = defaultState, action: { type: string; value: any }) => {
  //就是一个方法函数
  let data = { ...action };
  if (data.type === 'changeColumns') {
    let newState = {
      columns: [],
    };
    newState.columns = data.value.map((item: any) => {
      item = { ...item };
      return item;
    });
    let arr_temp = [];
    arr_temp = data.value.map((item: any) => {
      item = { ...item };
      return item;
    });
    const temp = JSON.stringify(arr_temp);
    sessionStorage.setItem('maliciousIP', temp);
    return newState;
  }


  return state;
};
