{
    "type": "object",
    "required": ["startTime", "stopTime"],
    "properties": {
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "ip": {
            "type": "string",
            "default": "",
            "format": "ipv4|ipv6"
        },
        "replay": {
            "type": "boolean",
			"default": False,
			"enum": [True, False]
        },
        "timeType": {
            "type": "string",
            "default": "occurredTime",
            "enum": ["occurredTime", "observedTime"]
        }
    }
}