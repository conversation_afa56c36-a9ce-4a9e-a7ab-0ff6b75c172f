/* eslint-disable import/prefer-default-export */
import { useState, useEffect } from 'react';
import { pcapList, pcapData, pcapContent, loadPcap, pcapBye } from '@/services/workbench';
import { message } from 'antd';

export const useDataList = (pcapPath?: any, filePath?: any) => {
  console.log('[ filePath ]-7', pcapPath,filePath)
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false)
  const [limit, setLimist] = useState(10);
  const [isEmpty, setIsEmpty] = useState(false);

  const updateLimit = () => {
    setLimist((prev) => prev + 10)
  }

  const getDataFun = async () => {
    if (!pcapPath) {
      return false;
    }
    const params = { pcap_name: pcapPath,pcap_filename:filePath };
    setLoading(true)
    const res0 = await loadPcap(params);
    if (!res0.flag) {
      message.error('网络错误');
      return false;
    }

    const res = await pcapList({ pcap_name: pcapPath, limit }).catch(err => err);
    try {
      const data = JSON.parse(res.data);
      if (data.length === listData.length) {
        setIsEmpty(true)
      } else {
        setListData(data);
      }
    } catch (e) {}
    setLoading(false)
  };
  const getDataDetail = async params => {
    const res = await pcapData(params).catch(err => err);
    try {
      const data = JSON.parse(res.data);
      return data;
    } catch (e) {
      return {};
    }
  };
  const getContent = async params => {
    const res = await pcapContent(params).catch(err => err);
    try {
      const data = JSON.parse(res.data);
      return data;
    } catch (e) {
      return {};
    }
  };
  const pcapByeFun = async params => {
    const res = await pcapBye(params).catch(err => err);
    console.log('[ 退出数据包 ]-43', res);
  };
  useEffect(() => {
    getDataFun();
  }, [limit]);
  return {
    listData,
    loading,
    isEmpty,
    setIsEmpty,
    getDataDetail,
    getContent,
    pcapByeFun,
    updateLimit,
  };
};
