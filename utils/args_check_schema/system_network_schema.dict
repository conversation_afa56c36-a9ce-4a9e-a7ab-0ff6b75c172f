{
	"type": "object",
	"required": ["name", "ip", "mask", "gateway", "primaryDNS", "workInterfaceList"],
	"properties": {
		"name": {
			"type": "string",
			"pattern": r"^[A-Za-z0-9_\-]{1,64}$"
		},
		"ip": {
			"type": "string",
			"format": "ipv4"
		},
		"mask": {
			"type": "string",
			"format": "ipv4"
		},
		"gateway": {
			"type": "string",
			"format": "ipv4"
		},
		"primaryDNS": {
			"type": "string",
			"format": "ipv4"
		},
		"secondDNS": {
			"type": "string",
			"format": "ipv4"
		},
		"workInterfaceList": {
		    "type": "array",
			    "items": {
			        "type": "string",
			        "pattern": r"^[A-Za-z0-9_\-]{1,64}$"
			  }
		}
	}
}