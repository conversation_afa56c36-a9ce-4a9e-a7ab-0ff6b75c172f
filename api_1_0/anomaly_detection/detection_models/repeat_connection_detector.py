# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : repeat_connection_detector.py
# @Software: PyCharm

"""重复连接异常检测模型"""

from collections import defaultdict, Counter
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
from .base_detector import BaseDetector
from utils.logger import get_ndr_logger
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class RepeatConnectionDetector(BaseDetector):
    """重复连接异常检测器
    
    异常重复连接行为检测
    比如某IP频繁连接相同域名
    """
    
    def __init__(self, task_config: Dict[str, Any]):
        super().__init__(task_config)
        self.frequency_threshold = self.detection_config.get('frequency_threshold', 100)  # 连接次数阈值
        self.time_window_minutes = self.detection_config.get('time_window_minutes', 60)  # 时间窗口
        self.unique_threshold = self.detection_config.get('unique_threshold', 10)  # 唯一目标数阈值
        self.pattern_types = self.detection_config.get('pattern_types', ['ip_to_domain', 'ip_to_ip', 'port_scan'])
    
    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """执行重复连接异常检测"""
        try:
            results = []
            
            # 获取连接数据
            conn_data = self.get_historical_data('dpilog_conn', start_time, end_time)
            dns_data = self.get_historical_data('dpilog_dns', start_time, end_time)
            http_data = self.get_historical_data('dpilog_http', start_time, end_time)
            
            if not conn_data and not dns_data and not http_data:
                LOG.info(f"当前时段无连接数据: {self.target_value}")
                return results
            
            # 检测不同类型的重复连接模式
            if 'ip_to_domain' in self.pattern_types:
                domain_results = self._detect_ip_to_domain_repeats(dns_data, http_data, start_time, end_time)
                results.extend(domain_results)
            
            if 'ip_to_ip' in self.pattern_types:
                ip_results = self._detect_ip_to_ip_repeats(conn_data, start_time, end_time)
                results.extend(ip_results)
            
            if 'port_scan' in self.pattern_types:
                scan_results = self._detect_port_scan_patterns(conn_data, start_time, end_time)
                results.extend(scan_results)
            
            return results
            
        except Exception as e:
            LOG.error(f"重复连接异常检测失败: {str(e)}")
            return []
    
    def _detect_ip_to_domain_repeats(self, dns_data: List[Dict[str, Any]], 
                                   http_data: List[Dict[str, Any]],
                                   start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测IP到域名的重复连接"""
        results = []
        
        # 统计域名访问频率
        domain_access = defaultdict(list)
        
        # 处理DNS数据
        for record in dns_data:
            if 'query' in record and record['query']:
                key = (record.get('src_ip', ''), record['query'])
                domain_access[key].append(record)
        
        # 处理HTTP数据
        for record in http_data:
            if 'host' in record and record['host']:
                key = (record.get('src_ip', ''), record['host'])
                domain_access[key].append(record)
        
        # 检测异常重复访问
        for (src_ip, domain), records in domain_access.items():
            if len(records) >= self.frequency_threshold:
                # 检查是否在短时间窗口内
                time_windows = self._analyze_time_windows(records)
                
                for window_start, window_records in time_windows.items():
                    if len(window_records) >= self.frequency_threshold:
                        anomaly_result = self._create_repeat_anomaly(
                            'ip_to_domain', src_ip, domain, window_records, 
                            window_start, start_time, end_time
                        )
                        if anomaly_result:
                            results.append(anomaly_result)
        
        return results
    
    def _detect_ip_to_ip_repeats(self, conn_data: List[Dict[str, Any]],
                               start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测IP到IP的重复连接"""
        results = []
        
        # 统计IP连接频率
        ip_connections = defaultdict(list)
        
        for record in conn_data:
            src_ip = record.get('src_ip', '')
            dst_ip = record.get('dst_ip', '')
            if src_ip and dst_ip:
                key = (src_ip, dst_ip)
                ip_connections[key].append(record)
        
        # 检测异常重复连接
        for (src_ip, dst_ip), records in ip_connections.items():
            if len(records) >= self.frequency_threshold:
                time_windows = self._analyze_time_windows(records)
                
                for window_start, window_records in time_windows.items():
                    if len(window_records) >= self.frequency_threshold:
                        anomaly_result = self._create_repeat_anomaly(
                            'ip_to_ip', src_ip, dst_ip, window_records,
                            window_start, start_time, end_time
                        )
                        if anomaly_result:
                            results.append(anomaly_result)
        
        return results
    
    def _detect_port_scan_patterns(self, conn_data: List[Dict[str, Any]],
                                 start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测端口扫描模式"""
        results = []
        
        # 统计每个源IP访问的目标端口
        ip_ports = defaultdict(set)
        ip_records = defaultdict(list)
        
        for record in conn_data:
            src_ip = record.get('src_ip', '')
            dst_port = record.get('dst_port', 0)
            if src_ip and dst_port:
                ip_ports[src_ip].add(dst_port)
                ip_records[src_ip].append(record)
        
        # 检测端口扫描行为
        for src_ip, ports in ip_ports.items():
            if len(ports) >= self.unique_threshold:
                records = ip_records[src_ip]
                time_windows = self._analyze_time_windows(records)
                
                for window_start, window_records in time_windows.items():
                    window_ports = set(r.get('dst_port', 0) for r in window_records)
                    if len(window_ports) >= self.unique_threshold:
                        anomaly_result = self._create_scan_anomaly(
                            src_ip, window_ports, window_records,
                            window_start, start_time, end_time
                        )
                        if anomaly_result:
                            results.append(anomaly_result)
        
        return results
    
    def _analyze_time_windows(self, records: List[Dict[str, Any]]) -> Dict[datetime, List[Dict[str, Any]]]:
        """分析时间窗口内的记录"""
        time_windows = defaultdict(list)
        
        # 按时间窗口分组
        for record in records:
            ts = record.get('ts')
            if ts:
                # 计算时间窗口起始时间（按分钟对齐）
                window_start = ts.replace(second=0, microsecond=0)
                window_start = window_start.replace(minute=(window_start.minute // self.time_window_minutes) * self.time_window_minutes)
                time_windows[window_start].append(record)
        
        return time_windows
    
    def _create_repeat_anomaly(self, pattern_type: str, source: str, target: str,
                             records: List[Dict[str, Any]], window_start: datetime,
                             detection_start: datetime, detection_end: datetime) -> Dict[str, Any]:
        """创建重复连接异常结果"""
        frequency = len(records)
        
        # 计算异常分数
        anomaly_score = min(frequency / (self.frequency_threshold * 5), 1.0)
        
        # 确定严重程度
        severity = self.get_severity_level(anomaly_score)
        
        # 计算时间跨度
        timestamps = [r['ts'] for r in records if 'ts' in r]
        time_span = (max(timestamps) - min(timestamps)).total_seconds() / 60 if timestamps else 0
        
        return {
            'anomaly_score': anomaly_score,
            'severity': severity,
            'pattern_type': pattern_type,
            'source': source,
            'target': target,
            'frequency': frequency,
            'time_span_minutes': time_span,
            'window_start': window_start,
            'time_window': f"{detection_start.strftime('%Y-%m-%d %H:%M')} - {detection_end.strftime('%Y-%m-%d %H:%M')}",
            'description': f"检测到{pattern_type}重复连接异常：{source} -> {target}，"
                         f"在{time_span:.1f}分钟内连接{frequency}次，超过阈值{self.frequency_threshold}",
            'details': {
                'records_sample': records[:10],  # 保存前10条记录作为样本
                'detection_config': self.detection_config
            }
        }
    
    def _create_scan_anomaly(self, source_ip: str, ports: set, records: List[Dict[str, Any]],
                           window_start: datetime, detection_start: datetime, 
                           detection_end: datetime) -> Dict[str, Any]:
        """创建端口扫描异常结果"""
        port_count = len(ports)
        
        # 计算异常分数
        anomaly_score = min(port_count / (self.unique_threshold * 5), 1.0)
        
        # 确定严重程度
        severity = self.get_severity_level(anomaly_score)
        
        # 计算时间跨度
        timestamps = [r['ts'] for r in records if 'ts' in r]
        time_span = (max(timestamps) - min(timestamps)).total_seconds() / 60 if timestamps else 0
        
        # 统计目标IP
        target_ips = set(r.get('dst_ip', '') for r in records if r.get('dst_ip'))
        
        return {
            'anomaly_score': anomaly_score,
            'severity': severity,
            'pattern_type': 'port_scan',
            'source': source_ip,
            'target': f"{len(target_ips)} IPs, {port_count} ports",
            'port_count': port_count,
            'target_ip_count': len(target_ips),
            'time_span_minutes': time_span,
            'window_start': window_start,
            'scanned_ports': sorted(list(ports))[:50],  # 最多显示50个端口
            'time_window': f"{detection_start.strftime('%Y-%m-%d %H:%M')} - {detection_end.strftime('%Y-%m-%d %H:%M')}",
            'description': f"检测到端口扫描异常：{source_ip}在{time_span:.1f}分钟内"
                         f"扫描{len(target_ips)}个IP的{port_count}个端口",
            'details': {
                'target_ips': list(target_ips)[:20],  # 最多显示20个目标IP
                'records_sample': records[:10],
                'detection_config': self.detection_config
            }
        }
