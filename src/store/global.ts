import { getUserModule, UserModule } from '@/services/user';
import { querySysConfig } from '@/services/global';
import defaultSettings, { DefaultSettings } from '../../config/defaultSettings';

import { Rekv } from './rekv';

interface GlobalStore {
  modules: UserModule[];
  sidebar: UserModule[];
  siteConfig: object;
  collapsed: boolean;
  settings: DefaultSettings;
}

const initState: GlobalStore = {
  modules: [],
  sidebar: [],
  siteConfig: {},
  collapsed: false,
  settings: defaultSettings,
};

export interface Tree extends UserModule {
  children: UserModule[];
}

function convert(list: UserModule[]) {
  const res = [];
  const map = list.reduce((listItem, v) => {
    // eslint-disable-next-line no-param-reassign
    listItem[v.uri] = v;
    return listItem;
  }, {});
  // eslint-disable-next-line no-restricted-syntax
  for (const item of list) {
    if (item.parentId === '') {
      res.push(item);
      continue; // eslint-disable-line no-continue
    }
    if (item.parentId in map) {
      const parent = map[item.parentId];
      parent.children = parent.children || [];
      parent.children.push(item);
    }
  }
  return res;
}

function treeSort(tree: Tree[]) {
  tree.sort((a, b) => a.sort - b.sort);
  tree.forEach(item => {
    if (item.children && item.children.length) {
      item.children.sort((a, b) => a.sort - b.sort);
    }
  });

  return tree;
}

/**
 * 用户状态存储
 */
const globalStore = new Rekv({
  initState,
  effects: {
    async getUserModule(store): Promise<Tree[] | undefined> {
      const { code, data } = await getUserModule();
      if (code !== 0) {
        return;
      }

      store.setState({ modules: data });
    },
    async getSidebar(store): Promise<Tree[] | undefined> {
      const { data, code } = await getUserModule();

      if (code !== 0) {
        return;
      }

      const sidebar = treeSort(convert(data.filter(bar => bar.isMenu)));
      store.setState({
        sidebar,
        modules: data,
      });
      // eslint-disable-next-line consistent-return
      return sidebar;
    },
    async getSiteConfig(store) {
      const { code, data } = await querySysConfig('site');

      if (code !== 0) {
        return;
      }

      store.setState({
        siteConfig: data,
      });
    },
  },
});

export default globalStore;
