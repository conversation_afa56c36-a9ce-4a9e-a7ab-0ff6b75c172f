import logging
from datetime import datetime

from cluster.api.clustermgr import ClusterManager
from cluster.utils.file import is_safe_path, get_json_file
from cluster.settings import CLUSTER_CFG

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s -%(levelname)s - %(funcName)s [%(lineno)d]: %(message)s',
    datefmt='%m-%d %H:%M:%S'
)
logger = logging.getLogger("myclusterlogger")

# 扩展 ClusterManager 类
class MyClusterManager(ClusterManager):
    def echo(self, node_ip, msg):
        """向目标节点发送信息，并回显"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.echo(msg)
            conn.close()
            print (replymsg)
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def get_node_list_except_self(self):
        nodes = []
        node_ip = self.get_node_ip()
        node_list = self.get_node_list_from_file()
        for node in node_list:
            if node == node_ip:
                continue
            nodes.append(node)
        return nodes

    def mv_file_by_cluster(self, src_file_path, dst_file_path):
        """ 移动文件，目的路径只能在 /var/ 和 /opt/ 下，防止破坏系统其他文件"""
        assert(is_safe_path(dst_file_path))
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.mv_file_by_node(node, src_file_path, dst_file_path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def mv_file_by_node(self, node_ip, src_file_path, dst_file_path):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            ok = conn.root.mv_file(src_file_path, dst_file_path)
            if not ok:
                raise Exception('mv_file error!')
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def sync_file_safe_by_cluster(self, src_file_path, dst_file_path):
        """ 安全的同步文件，目的路径只能在 /var/ 和 /opt/ 下，防止破坏系统其他文件"""
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.sync_file_safe_by_node(node, src_file_path, dst_file_path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def sync_file_safe_by_node(self, node_ip, src_file_path, dst_file_path):
        """ 安全的同步文件，目的路径只能在 /var/ 和 /opt/ 下，防止破坏系统其他文件"""
        tmp_file_path = dst_file_path + '-' + datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
        failed_node = self.sync_file_by_node(node_ip, src_file_path, tmp_file_path)
        if failed_node:
            return failed_node

        ret = self.mv_file_by_node(node_ip, tmp_file_path, dst_file_path)
        if ret:
            return node_ip

        return ""

    def remove_file_by_cluster(self, file_path):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.remove_file_by_node(node, file_path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def remove_file_by_node(self, node_ip, file_path):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.remove_file(file_path)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def file_hash_by_cluster(self, filename):
        values = {}
        node_list = self.get_node_list_from_file()
        for node in node_list:
            node_id = node
            values[node_id] = self.file_hash_by_node(node_id, filename)
        return values

    def file_hash_by_node(self, node_ip, filename):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            value = conn.root.file_hash(filename)
            conn.close()
            return value
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return None

    def request_pcap_by_node(self, node_ip, query_str, file_path, url_list):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            value = conn.root.request_pcap(query_str, file_path, url_list)
            conn.close()
            return value
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return None

    def hdp_restart_by_cluster(self):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_restart_by_node(node)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def hdp_restart_by_node(self, node_ip):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.hdp_restart()
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_update_portmask_by_node(self, node_ip, port_mask):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.hdp_update_portmask(port_mask)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_status_by_cluster(self):
        status = {}
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_status_by_node(node)
            status[node] = ret
        return status

    def hdp_status_by_node(self, node_ip):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.hdp_status()
            conn.close()
            return replymsg
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return ""

    def hdp_set_file_protocol_by_cluster(self, file_app_cfg, file_app_list: list):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_set_file_protocol_by_node(node, file_app_cfg, file_app_list)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def hdp_set_file_protocol_by_node(self, node_ip, file_app_cfg, file_app_list: list):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.hdp_set_file_protocol(file_app_cfg, file_app_list)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_set_file_type_by_cluster(self, file_type_cfg):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_set_file_type_by_node(node, file_type_cfg)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def hdp_set_file_type_by_node(self, node_ip, file_type_cfg):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.hdp_set_file_type(file_type_cfg)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_set_file_size_by_cluster(self, min_size, max_size):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_set_file_size_by_node(node, min_size, max_size)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def hdp_set_file_size_by_node(self, node_ip, min_size, max_size):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.hdp_set_file_size(min_size, max_size)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_feature_add_by_cluster(self, rule_path, rule_info_path):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_feature_add_by_node(node, rule_path, rule_info_path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes
    def hdp_feature_add_by_node(self, node_ip, rule_path, rule_info_path):
        try:
            self.sync_file_by_node(node_ip, rule_path, rule_path)
            self.sync_file_by_node(node_ip, rule_info_path, rule_info_path)

            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.grpc_feature_add(rule_path, rule_info_path)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_feature_upgrade_by_cluster(self, path):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_feature_upgrade_by_node(node, path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def hdp_feature_upgrade_by_node(self, node_ip, path):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.hdp_feature_upgrade(path)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_feature_operate_by_cluster(self, sid_list: list):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_feature_operate_by_node(node, sid_list)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def hdp_feature_operate_by_node(self, node_ip, sid_list: list):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.hdp_feature_operate(sid_list)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def hdp_newssnlog_switch_by_cluster(self, enable):
        failed_nodes = []
        node_list = self.get_node_list_except_self()
        for node in node_list:
            ret = self.hdp_newssnlog_switch_by_node(node, enable)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def hdp_newssnlog_switch_by_node(self, node_ip, enable):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.hdp_newssnlog_switch(enable)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def service_by_cluster(self, op, services=[]):
        result = ''
        node_list = self.get_node_list_from_file()
        for node in node_list:
            ret = self.service_by_node(node, op, services)
            if ret:
                result += node + ':\n' + ret

        return result

    def service_by_node(self, node_ip, op, services:list):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            replymsg = conn.root.service(op, services)
            conn.close()
            return replymsg
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return 'error'


# # 创建 MyClusterManager 实例
# my_cluster_mgr = MyClusterManager()
#
# # 创建集群
# my_cluster_mgr.create_cluster('**********', ['**********'])
# # 查看集群成员信息
# my_cluster_mgr.get_node_list_except_self()
# # 调用 echo 方法
# my_cluster_mgr.echo('*************', 'hello dude.')