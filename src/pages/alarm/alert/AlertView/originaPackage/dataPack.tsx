import React, { useEffect, useRef, useState } from 'react';
import { Table, Tooltip, Select, Empty, Spin } from 'antd';
import Tree from '@/components/Tree';
import { chunk, isEmpty, drop } from 'lodash';
import s from './style.less';
import { useDataList } from './hook';
const columnsName = ['序号', '时间戳', '源IP', '目的IP', '协议', '大小', '内容'];
const columnswidth = ['80px', '120px', '130px', '130px', '80px', '80px', 'auto'];
const columns = columnsName.map((title, i) => ({
  title,
  dataIndex: 'c',
  width: columnswidth[i],
  render: (e, row) => (
    <Tooltip title={e[i]}>
      <p className="tOver">{e[i]}</p>
    </Tooltip>
  ),
}));
const DataPack = ({ pcapPath, onChange,filePath }: any) => {
  const { listData, getDataDetail, updateLimit, loading, isEmpty: empty } = useDataList(pcapPath, filePath);
  const [selRow, setSelRow] = useState(null);
  const [treeData, setTreeData] = useState([]);
  const [binary, setBinary] = useState(16);
  const [isFull, setIsFull] = useState(false);
  const [offsetArr, setOffsetArr] = useState([0, 0]);
  const [detail, setDetail] = useState({
    bytes: '',
    hexString16: [],
    hexString: [],
  });
  const detailFun = bytes => {
    const bytes_ = bytes || detail.bytes;
    const binaryData = Buffer.from(bytes_, 'base64');
    const decodedString = binaryData.toString('binary');
    const hexString_ = [];
    const decode_ = [];
    for (let i = 0; i < decodedString.length; i++) {
      const pad = binary === 8 ? 8 : 2;
      const tos = binary === 8 ? 2 : 16;
      const hex = decodedString
        .charCodeAt(i)
        .toString(tos * 1)
        .padStart(pad, '0');
      hexString_.push(hex.toUpperCase());
      // 2/16=>10进制
      const to10 = parseInt(hex, tos);
      // 10=>ascii
      const str = to10 < 32 || to10 > 126 ? '⋅' : String.fromCharCode(to10);
      decode_.push(str || '⋅');
    }
    setDetail({
      bytes: bytes_,
      hexString: hexString_,
      hexString16: chunk(hexString_, binary),
      decode: decode_,
    });
  };
  const getDetail = async (record: any) => {
    const res = await getDataDetail({ pcap_name: pcapPath, frame: `${record.num}` });
    const treeData_ = res.tree.map(item => ({
      title: item.l,
      key: item.l,
      h: item.h,
      children: item.n?.map(e => ({
        title: e.l,
        key: e.l,
        h: e.h,
      })),
    }));
    setTreeData(treeData_);

    detailFun(res.bytes);
    onChange({ pcap_name: pcapPath, follow: res.fol[0][0], filter: res.fol[0][1] });
  };

  useEffect(() => {
    detailFun();
    window.addEventListener('fullscreenchange', () => {
      const isFullScreen = document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen;
      if (isFullScreen) {
        setIsFull(true);
      } else {
        setIsFull(false);
      }
    });
    return () => {
      window.removeEventListener('fullscreenchange',()=>{});
    };
  }, [binary]);
  useEffect(() => {
    !isEmpty(listData) && getDetail(listData[0]);
    console.log('listData:', listData);
  }, [listData]);

  const onScrollCapture = () => {
    if(empty) return;
    const tableRef = scrollRef.current as any
    if (Math.round(tableRef?.scrollTop) + tableRef?.clientHeight == tableRef?.scrollHeight) {
      updateLimit()
    }
  };

  const scrollRef = useRef(null)

  return (
    <div className={s.dataPack}>
      <div
        onScrollCapture={onScrollCapture}
        style={{ height: 350, overflowY: 'scroll' }}
        ref={scrollRef}
      >
        <Table
          columns={columns}
          rowClassName={record => {
            if (record.num == selRow) return 'bg-#f0e9e961';
          }}
          sticky
          dataSource={listData}
          pagination={false}
          scroll={{ y: '100%' }}
          onRow={(record: any) => ({
            onClick: () => {
              setOffsetArr([0, 0]);
              setSelRow(record.num);
              getDetail(record);
            },
          })}
        />
        {!!listData.length && loading && !empty && <div className='w-full flex justify-center items-center py-3'>加载中...</div>}
        {empty && !!listData.length && <div className='w-full flex justify-center items-center py-3'>没有更多了</div>}
      </div>
      <div className={s.detailContent}>
        <div className={s.treeBox} style={{ height: isFull ? 'calc(100vh - 476px)' : '250px' }}>
          <Tree
            loading={loading}
            key={JSON.stringify(treeData)}
            data={treeData}
            onChange={e => {
              const h = e.h || [0, 0];
              setOffsetArr(h);
            }}
          />
        </div>
        <div className={s.detailBox} style={{ height: isFull ? 'calc(100vh - 476px)' : '250px' }}>
          {!isEmpty(treeData) && (
            <>
              {' '}
              <div className={s.offset}>
                <p className={s.header}>偏移</p>
                {detail.hexString16.map((e, i) => {
                  return (
                    <>
                      <i>{(i * binary).toString(16).padStart(4, '0')}</i>
                      <br />
                    </>
                  );
                })}
              </div>
              <div className={s.hexString}>
                <p className={s.header}>
                  进制转换：
                  <Select
                    value={binary}
                    size="small"
                    onChange={setBinary}
                    options={[
                      {
                        value: 16,
                        label: '十六进制',
                      },
                      {
                        value: 8,
                        label: '二进制',
                      },
                    ]}
                  />
                </p>
                {detail.hexString.map((e, i) => {
                  let styleObj = {};
                  if (i >= offsetArr[0] && i < offsetArr[0] + offsetArr[1]) {
                    styleObj = { background: '#a7cbfd' };
                  }
                  return (
                    <>
                      <span className="pr-2" style={styleObj}>
                        {e}
                      </span>
                      {(i + 1) % binary === 0 && <br />}
                    </>
                  );
                })}
              </div>
              <div className={s.decode}>
                <p className={s.header}>
                  解码类型：
                  <Select
                    value="ASCII"
                    size="small"
                    options={[
                      {
                        value: 'ASCII',
                        label: 'ASCII',
                      },
                    ]}
                  />
                </p>
                {detail.decode.map((e, i) => {
                  let styleObj = {};
                  if (i >= offsetArr[0] && i < offsetArr[0] + offsetArr[1]) {
                    styleObj = { background: '#a7cbfd' };
                  }
                  return (
                    <>
                      <span className="pr-1" style={styleObj}>
                        {e}
                      </span>
                      {(i + 1) % binary === 0 && <br />}
                    </>
                  );
                })}
                {/* {detail.decode} */}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
export default DataPack;
