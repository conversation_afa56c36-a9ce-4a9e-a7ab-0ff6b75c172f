import React from 'react';

import { useHistory } from 'react-router';
import EmailDetailContent from './EmailDetailContent';

function EmailDetail() {
  const history = useHistory()
  const search = searchToObject(location.search)
  const { end_time, start_time, msg_id } = search

  if (!end_time || !start_time || !msg_id) {
    history.push('/exception-404')
  }

  return <EmailDetailContent data={search as any} />
};

export default EmailDetail;


export function searchToObject(search: string): Record<string, any> {
  const params = {};

  if (search) {
    const searchParams = new URLSearchParams(search);
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
  }

  return params;
}