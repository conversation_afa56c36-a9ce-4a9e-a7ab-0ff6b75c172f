from __future__ import absolute_import, unicode_literals
import sys
import time

sys.path.append('/opt/ndr')

import json
import requests
import traceback
from .utils import *
from .celery import *
from ksp.systoolkit import network_manager
from cluster.api.manager import MyClusterManager

cluster_mgr = MyClusterManager()


@app.task()
def run_command_in_host(cmd):
    ret, msg = run_command(cmd)

    return ret


@app.task(bind=True)
def hdp_pcap_task(self, cmd, task_id):
    mg = MongoDB("ndr")
    task_info = mg.find_one('back_explore', {"taskId": task_id}, {"status": 1})
    if not task_info or task_info['status'] != 'running':
        return 'revoke'
    lcore_id = gl_hdp_lcore_queue.get()
    command = cmd + " --lcoreid '%d'" % lcore_id
    print(command, flush=True)
    ret, msg = run_command(command)
    gl_hdp_lcore_queue.put(lcore_id)

    # print(msg, flush=True)
    # print('=========   get lcore %d   ==========' % lcore_id, flush=True)

    if ret:
        return 'success'
    else:
        return 'failed'


@app.task()
def reinit_replay_locres_queue(realtime):
    if realtime:
        release_num = len(resource_lcores)
        # 释放多余的核，使用循环从gl_hdp_lcore_queue中去get获取核资源
        # 如果是需要释放的核，则不做操作（多进程队列随着get操作推出了该核）
        # 如果不是需要释放的核，则再次将该核推入多进程队列
        while release_num:
            res_lcore = gl_hdp_lcore_queue.get()
            if res_lcore in resource_lcores:
                release_num -= 1
            else:
                gl_hdp_lcore_queue.put(res_lcore)
        print('\n******** MutiQueue release  replay lcores %s ********\n' % str(resource_lcores))
    else:
        # 添加多余的核资源，直接往多进程队列中推入即可
        for res_lcore in resource_lcores:
            gl_hdp_lcore_queue.put(res_lcore)
        print('\n******** MutiQueue add replay lcores %s *********\n' % str(resource_lcores))

    return 'success'


@app.task()
def time_monitor():
    for key, value in HostMonitor.time_server.items():
        run_time = get_cmd_data(HostMonitor.cmd1 + key + HostMonitor.cmd2)
        if not run_time:
            if key == "alert_output":
                os.system("docker exec -d mica-api bash -c '%s'" % value)

@app.task()
def get_service_data():
    return_data = []
    for key, value in HostMonitor.server.items():
        service_data = {}
        run_time = get_cmd_data(HostMonitor.cmd1 + value + HostMonitor.cmd2)
        service_data["name"] = key
        service_data["start"] = "是否启动"
        service_data["restart"] = "是否重启"
        service_data["stop"] = "是否停止"
        if run_time:
            service_data["status"] = "running"
            service_data["runtime"] = run_time
        else:
            service_data["status"] = "stop"
            service_data["runtime"] = "0-00:00:00"
        return_data.append(service_data)
    return return_data


@app.task()
def handle_service(service_name, action):
    if action == "start":
        if service_name == "hdp":
            os.system("supervisorctl -u %s -p %s start hdp" % (SupervisorConfig.User, SupervisorConfig.Password))
        elif service_name == "alert_output":
            os.system("docker exec -d mica-api bash -c '%s'" % HostMonitor.server_action[service_name])
        else:
            os.system("docker stop %s" % HostMonitor.server_action[service_name])
            os.system("docker rm -f %s" % HostMonitor.server_action[service_name])
            os.system("/usr/local/bin/docker-compose -f /root/ndr-deploy/docker-compose.yml up -d %s" %
                      HostMonitor.server_action[service_name])
    elif action == "restart":
        if service_name == "hdp":
            number = get_cmd_data("ps -ef | grep %s | grep -v grep | awk '{print $2}'" % HostMonitor.server["hdp"])
            os.system("kill -9 %s" % number)
            os.system("supervisorctl -u %s -p %s start hdp" % (SupervisorConfig.User, SupervisorConfig.Password))
        elif service_name == "alert_output":
            number = get_cmd_data("ps -ef | grep alert_output.py | grep -v grep | awk '{print $2}'")
            os.system("kill -9 %s" % number)
            os.system("docker exec -d mica-api bash -c '%s'" % HostMonitor.server_action[service_name])
        else:
            os.system("docker stop %s" % HostMonitor.server_action[service_name])
            os.system("docker rm -f %s" % HostMonitor.server_action[service_name])
            os.system("/usr/local/bin/docker-compose -f /root/ndr-deploy/docker-compose.yml up -d %s" %
                      HostMonitor.server_action[service_name])
    else:
        if service_name == "hdp":
            # supervisor 可能存在停止进程不成功，增加一个kill操作，双重停止进程功能。
            os.system("supervisorctl -u %s -p %s stop hdp" % (SupervisorConfig.User, SupervisorConfig.Password))
            number = get_cmd_data("ps -ef | grep %s | grep -v grep | awk '{print $2}'" % HostMonitor.server["hdp"])
            if number:
                os.system("kill -9 %s" % number)
        elif service_name == "alert_output":
            number = get_cmd_data("ps -ef | grep alert_output.py | grep -v grep | awk '{print $2}'")
            os.system("kill -9 %s" % number)
        else:
            os.system("docker stop %s" % HostMonitor.server_action[service_name])
            os.system("docker rm -f %s" % HostMonitor.server_action[service_name])

@app.task()
def get_cpu_mem_data():
    """
    获取cpu，内存数据
    :return:
    """
    mongodb = MongoDB("ndr")
    create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    history_time = (datetime.datetime.now() - datetime.timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")
    # 获取cpu温度
    try:
        cpu_temp_data = 0.0
        if os.path.exists("/usr/bin/sensors"):
            cpu_temp = os.popen("""sensors | grep Core | awk '{sum += $3} END {printf "%3.3f", sum/NR}'""")
            cpu_temp_data = cpu_temp.read()
            cpu_temp_data = round(float(cpu_temp_data), 1)
    except:
        cpu_temp_data = 0.0
    # popen返回文件对象，跟open操作一样
    top_data = os.popen("top -d 1 -bn 2")
    top_str = top_data.read()  # 读文件
    top_list = [i for i in top_str.split("\n") if i.__contains__("%Cpu") or i.__contains__("buff/cache")]
    cpu_data = top_list[2]
    cpu_list = cpu_data.split(",")
    cpu_free = float(cpu_list[3].split()[0]) / 100
    cpu_use = round(1 - cpu_free, 2)

    free_data = os.popen("free")
    free_str = free_data.read()
    free_list = free_str.split("\n")
    mem_data = free_list[1]
    mem_list = mem_data.split()
    mem_total = mem_list[1]
    mem_use = round(float(mem_list[2]) / float(mem_total), 2)
    peak_cpu = 0
    peak_mem = 0

    node_name = cluster_mgr.get_node_name()
    condition = {"node_name": node_name} if node_name else {}
    try:
        peak_data = mongodb.find_one("monitor_peak", condition)
        if peak_data:
            peak_cpu = peak_data.get("cpuPeak", 0.0)
            peak_mem = peak_data.get("memPeak", 0.0)
        # 更新历史峰值
        if peak_cpu < cpu_use:
            mongodb.update("monitor_peak", condition, {"cpuPeak": cpu_use, "cpuPeakTime": create_time}, insert=True)
        if peak_mem < mem_use:
            mongodb.update("monitor_peak", condition, {"memPeak": mem_use, "memPeakTime": create_time}, insert=True)
        # 删除前一个小时以前的所有数据
        condition["createTime"] = {"$lte": history_time}
        mongodb.delete("monitor_cpu", condition)
        mongodb.delete("monitor_mem", condition)
        # 插入当前数据
        data = {"percent": cpu_use, "cpu_temp": cpu_temp_data, "createTime": create_time}
        if node_name:
            data["node_name"] = node_name
        mongodb.insert_one("monitor_cpu", data)

        data = {"percent": mem_use, "createTime": create_time}
        if node_name:
            data["node_name"] = node_name
        mongodb.insert_one("monitor_mem", data)
        top_data.close()
        free_data.close()
    except Exception as e:
        print("cpu_mem__%s\n%s" % (str(e), traceback.format_exc()))


@app.task()
def get_interface_data():
    """
    获取网口数据
    :return:
    """
    # 如果是集群模式，则取该节点IP
    node_name = cluster_mgr.get_node_name()
    condition = {"node_name": node_name} if node_name else {}
    mongodb = MongoDB("ndr")
    create_time = int(time.mktime(datetime.datetime.now().timetuple()))
    # 等到整10秒时才采集数据
    time.sleep(10 - create_time % 10)
    history_time = int(time.mktime((datetime.datetime.now() - datetime.timedelta(hours=1)).timetuple()))
    try:
        try:
            rst = list(mongodb.find("monitor_interface", condition).sort([("createTime", -1)]).limit(1))
        except:
            rst = list()

        body = {}
        for name, port in interface_data.items():
            traffic = 0
            try:
                response = requests.get("http://***********:7001/api/v1/port/stats",
                                        headers={'Upgrade': 'True'}, params={'port': str(int(port))})
                data = json.loads(response.text)
                traffic = int((int(data["data"]["in_bytes"]) << 3) // 1000000)
            except Exception as err:
                print("port stats: %s\n%s" % (str(err), traceback.format_exc()))
            if traffic < 0:
                traffic = 0
            speed = 0

            key_total = name + "_total"
            key_speed = name + "_speed"
            if rst and int(rst[0][key_total]) < traffic:
                last_total_traffic = int(rst[0][key_total])
                speed = (traffic - last_total_traffic) // (create_time - rst[0]["createTime"])
                max_speed = 1000
                if interface_speed[name] == 10:
                    max_speed = 10000
                if speed > max_speed:
                    speed = max_speed
            body[key_total] = 0 if traffic < 0 else traffic
            body[key_speed] = 0 if speed < 0 else speed
            if node_name:
                body['node_name'] = node_name
        body["createTime"] = create_time - create_time % 10
        # 插入当前数据
        condition['createTime'] = {"$lte": history_time}
        mongodb.delete("monitor_interface", condition)
        mongodb.insert_one("monitor_interface", body)
    except Exception as e:
        print("interface__%s\n%s" % (str(e), traceback.format_exc()))


@app.task()
def get_disk_data():
    """
    获取磁盘数据
    :return:
    """
    node_name = cluster_mgr.get_node_name()

    disk_data = os.popen("df -m")
    disk_str = disk_data.read()  # 读文件
    disk_list = [i for i in disk_str.split("\n") if i]
    disk_list.pop(0)
    for disk in disk_list:
        disk_total = int(disk.split()[1])
        disk_use = int(disk.split()[2])
        disk_free = int(disk.split()[3])
        percent = round(float(disk.split()[4].strip('%')) / float(100), 2)
        disk_name = disk.split()[5]
        if disk_name in ["/", "/opt", "/var", "/var/lib/clickhouse", "/var/lib/kafka"]:
            update_disk(node_name, disk_name, disk_total, disk_use, disk_free, percent)
        elif disk_name.startswith("/data"):
            update_disk(node_name, disk_name, disk_total, disk_use, disk_free, percent)
    disk_data.close()


def update_disk(node_name, coll, disk_total, disk_use, disk_free, percent):
    mongodb = MongoDB("ndr")
    create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    condition = {"node_name": node_name} if node_name else {}
    try:
        peak_data = mongodb.find_one("monitor_disk", condition)
        peak = 0
        if peak_data and (coll in peak_data):
            peak = peak_data[coll]["peak"]
            peak_time = peak_data[coll]["peakTime"]
        # 更新历史峰值
        if peak <= percent:
            body = {coll: {
                "peak": percent,
                "peakTime": create_time,
                "total": disk_total,
                "use": disk_use,
                "free": disk_free,
                "percent": percent,
                "createTime": create_time}}
        else:
            body = {coll: {
                "peak": peak,
                "peakTime": peak_time,
                "total": disk_total,
                "use": disk_use,
                "free": disk_free,
                "percent": percent,
                "createTime": create_time}}
        body['node_name'] = node_name
        mongodb.update("monitor_disk", condition, body, insert=True)
    except Exception as e:
        print("disk__%s\n%s" % (str(e), traceback.format_exc()))
    mongodb.close()


def get_cmd_data(cmd):
    """
    获取命令行执行结果
    """
    cmd_data = os.popen(cmd)
    data = cmd_data.read().strip()
    cmd_data.close()
    return data


@app.task()
def hdp_files_auto_delete_task(divice):
    threshold_map = {
        'D5000': 1024 << 10,
        'D5010': 2048 << 10,
        'D8000': 2048 << 10,
        'D8010': 1024 << 10,
        'D8600': 512 << 10
    }  # 单位：MB
    threshold = threshold_map.get(divice, 'D8600')
    files_path = '/var/log/hdp/files'
    collection = 'monitor_hdp_files'
    cls_mg = MongoDB("ndr")
    node_name = cluster_mgr.get_node_name()
    condition = {"node_name": node_name} if node_name else {}
    try:
        # 如果初次运行没有统计信息，则重新统计一遍
        if not cls_mg.count(collection, condition):
            ret, msg = run_command("du %s" % files_path, timeout=1800)
            if not ret:
                print('ERROR: Get hdp files path[%s] size failed, Reason: %s' % (files_path, msg))
                return
            path_size_list = msg.split('\n')[:-1]
            for item in path_size_list:
                # item example: '223364  /var/log/hdp/files/20231021'
                dir_size, obs_path = item.split('\t')
                _, dir_name = os.path.split(obs_path)
                if not dir_size.isdigit() or not dir_name.isdigit():
                    continue
                data = {'dir_name': dir_name, 'dir_size': int(dir_size) >> 10, 'create_time': datetime.datetime.now()}
                if node_name:
                    data["node_name"] = node_name
                cls_mg.insert_one(collection, data)
            return

        # 计算前一整天的对应目录的数据总量，并更新到mongo数据库。
        dir_name = (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime('%Y%m%d')
        if not os.path.exists(os.path.join(files_path, dir_name)):
            return
        ret, msg = run_command("du %s | awk '{print $1}'" % os.path.join(files_path, dir_name), timeout=1800)
        if not ret or not msg.strip().isdigit():
            print('ERROR: Get hdp files dir[%s] size failed, Reason: %s' % (dir_name, msg))
            return
        dir_size = int(msg) >> 10  # 单位：MB
        data = {'dir_name': dir_name, 'dir_size': dir_size, 'create_time': datetime.datetime.now()}
        if node_name:
            data["node_name"] = node_name
        cls_mg.update(collection, condition, data, insert=True)

        # 计算是否需要删除目录
        group = {'total': {"$sum": "$dir_size"}}
        if node_name:
            group["node_name"] = node_name
        raw_data = cls_mg.aggs(collection, [{"$group": group}])
        if not raw_data:
            return
        if raw_data[0]['total'] < threshold:
            print('=== hdp files used %dGB and threshold is %dGB, no need clean...' % (
                raw_data[0]['total'] >> 10, threshold >> 10))
            return

        # 计算需要删除的目录列表
        clean_size = raw_data[0]['total'] - int(threshold * 0.9)  # 删掉阈值的10%
        raw_data = cls_mg.find(collection, condition).sort('dir_name')
        if not raw_data:
            return
        clean_dir_list = []
        cul_size = 0
        for col in raw_data:
            clean_dir_list.append(col['dir_name'])
            cul_size += col['dir_size']
            if cul_size > clean_size:
                break

        del_cond = {'dir_name': {'$in': clean_dir_list}}
        if node_name:
            del_cond["node_name"] = node_name
        cls_mg.delete(collection, del_cond)
        run_command('sudo rm -rf %s' % ' '.join(
            [os.path.join(files_path, dir_name) for dir_name in clean_dir_list]), timeout=3600)
        print('>>> HDP clean dirs[%s] total %dMB hdp files success !!!' % (str(clean_dir_list), cul_size))

    except Exception as e:
        print('ERROR: hdp files clean failed: %s' % str(e))

    return 'success'


@app.task()
def warning_pcap_delete_task():
    # 删掉100天以前的告警pcap文件目录
    pcap_dir = "/var/ndr_warning_pcaps/"
    del_dir = (datetime.datetime.now() + datetime.timedelta(days=-100)).strftime('%Y%m%d')
    del_path = os.path.join(pcap_dir, del_dir)
    run_command('sudo rm -rf %s' % del_path)
    print(">>> Delelted warning pcap dir: %s" % del_path)

