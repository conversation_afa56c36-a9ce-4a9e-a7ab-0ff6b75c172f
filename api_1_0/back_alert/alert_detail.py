# -*- coding: utf-8 -*-
# @Time    : 2019-08-26 15:01
# <AUTHOR> Shark
# @File    : alert_detail.py
# @Software: PyCharm

from flask import request

from api_1_0.back_alert.alert_list import format_es_temp
from utils.database import MongoDB
from utils.es_function import get_es_template
from utils.ndr_base import NdrResource
from utils.utils import flask_response
from utils import param_check
from config.config import RULE_INDEX
from utils.param_check import Validator
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class AlertDetail(NdrResource):
    """按漏洞触发次数获取漏洞列表"""

    def __init__(self):
        """初始化参数"""
        self.mongodb = MongoDB("ndr")
        super(AlertDetail, self).__init__(es_template="alert_detail")

    @param_check.check_flask_args(Validator("alert_list"), request)
    def post(self, **kwargs):
        option_args = {
            "celeryId": kwargs["celeryId"],
            "killchain": kwargs["killchain"],
            "sid": kwargs["sid"],
            "threatLevel": kwargs["threatLevel"],
            "threatScore": kwargs["threatScore"],
            "threatFlag": kwargs["threatFlag"],
            "mbinfo": kwargs["mbinfo"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        group = kwargs["groupKey"]
        if group != "uniqueId":
            group = "flow." + group
        group = group + ".keyword"
        unique_filter = {
            "term": {
                group: kwargs["value"]
            }
        }
        es_template["query"]["bool"]["must"].append(unique_filter)
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])
        es_template['from'] = (page - 1) * page_size
        es_template['size'] = page_size
        try:
            res = self.es_client.search(index=RULE_INDEX, body=es_template)
        except Exception as e:
            err_info = "error: " + str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            return flask_response(err_info, False, {})

        try:
            data = self.data_format(kwargs["groupKey"], res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, group_key, ori_data):
        total = ori_data["hits"]["total"]["value"]
        data_return = {
            "total": total,
            "alters": []
        }
        alert_detail_list = ori_data["hits"]["hits"]
        if group_key == "uniqueId":
            for alert_detail in alert_detail_list:
                alert_dict = dict()
                alert_dict["occurredTime"] = alert_detail["_source"]["occurredTime"]
                alert_dict["observedTime"] = alert_detail["_source"]["observedTime"]
                alert_dict["src_mac"] = alert_detail["_source"]["flow"]["src_mac"]
                alert_dict["dst_mac"] = alert_detail["_source"]["flow"]["dst_mac"]
                alert_dict["responseName"] = alert_detail["_source"]["responseName"]
                alert_dict["src_ip"] = alert_detail["_source"]["flow"]["src_ip"]
                alert_dict["dst_ip"] = alert_detail["_source"]["flow"]["dst_ip"]
                alert_dict["src_port"] = alert_detail["_source"]["flow"]["src_port"]
                alert_dict["dst_port"] = alert_detail["_source"]["flow"]["dst_port"]
                alert_dict["proto"] = alert_detail["_source"]["flow"]["proto"]
                alert_dict["srcIpSend"] = alert_detail["_source"]["flow"]["srcIpSend"]
                alert_dict["dstIpSend"] = alert_detail["_source"]["flow"]["dstIpSend"]
                alert_dict["totalSend"] = alert_detail["_source"]["flow"]["totalSend"]
                alert_dict["payload"] = alert_detail["_source"]["payload"]
                alert_dict["sid"] = alert_detail["_source"]["sid"]
                alert_dict["taskType"] = alert_detail["_source"]["taskType"]
                alert_dict["direction"] = alert_detail["_source"]["direction"]
                alert_dict["pcap_filename"] = alert_detail["_source"]["pcap_filename"]
                alert_dict["not_fss_pcapname"] = alert_detail["_source"]["not_fss_pcapname"]
                data_return['alters'].append(alert_dict)
        else:
            for alert_detail in alert_detail_list:
                data_dict = alert_detail["_source"]
                data_dict["taskName"] = ""
                celery_id = data_dict["celeryId"]
                rst = self.mongodb.find_one('back_explore', {"celeryId": celery_id})
                if rst:
                    data_dict["taskName"] = rst['taskName']
                data_return['alters'].append(data_dict)
        return data_return
