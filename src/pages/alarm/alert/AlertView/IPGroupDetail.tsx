import { threatFlagNameMap } from '@/utils/enumList';
import { Button, Modal, Table, message } from 'antd';
import React from 'react';
import { DETAIL_QUERY_FUNC_MAP } from './EventDetail';
import LogDetail from './LogDetail';
import { FilterState, colRenderer, preparePayload } from './shared';


interface IPGroupDetailProps {
    className?: string
    type: string
    filter: FilterState
    record: any
    children: React.ReactNode
}

export default function IPGroupDetail({ className, type, filter, record, children }: IPGroupDetailProps) {
    const [modalVisible, setModalVisible] = React.useState(false);
    const [pagination, setPagination] = React.useState({ current: 1, pageSize: 10 });
    const [loading, setLoading] = React.useState(false);
    const [response, setResponse] = React.useState(null);

    const columns = React.useMemo(() => [
        {
            title: '发现时间',
            dataIndex: 'observedTime',
            className: 'ws-nowrap',
            render: colRenderer.dateTime,
        },
        {
            title: '源IP',
            dataIndex: ['flow', 'src_ip'],
            className: 'ws-nowrap max-w-40 truncate',
            render: colRenderer.srcIp,
        },
        {
            title: '目的IP',
            dataIndex: ['flow', 'dst_ip'],
            className: 'ws-nowrap max-w-40 truncate',
            render: colRenderer.dstIp,
        },
        {
            title: '威胁分类',
            dataIndex: type === 'vul' ? 'threatFlag' : 'threatType',
            className: 'ws-nowrap',
            render(_: any, record: any) {
                switch (type) {
                    case 'vul':
                        return threatFlagNameMap?.[record?.threatFlag];
                    default:
                        return record?.threatType;
                }
            },
        },
        {
            title: '威胁等级',
            dataIndex: 'threatLevel',
            className: 'ws-nowrap',
            render: colRenderer.threatLevel,
        },
        {
            title: '威胁评分',
            dataIndex: 'threatScore',
            className: 'ws-nowrap',
            render: colRenderer.threatScore,
        },
        {
            title: type === 'model' ? '模型名称' : '告警名称',
            dataIndex: 'responseName',
            className: 'ws-nowrap max-w-40',
            render: (_: any, record: any) => {
                const name = colRenderer.alertName(type, record);
                return (
                    <div className="truncate" title={name}>
                        {name}
                    </div>
                )
            },
        },
        {
            title: '杀伤链阶段',
            dataIndex: 'killchain',
            className: 'ws-nowrap',
            render: colRenderer.killchain,
        },
        {
            title: '操作',
            dataIndex: '',
            className: 'ws-nowrap',
            render: (_, value) => <LogDetail type={type} value={value} />
        },
    ], []);

    const handleQuery = (p = pagination) => {
        if (loading) return;

        setLoading(true);
        DETAIL_QUERY_FUNC_MAP[type]({
            ...preparePayload(filter),
            page: p.current,
            pageSize: p.pageSize,
            value: record?.[filter.groupKey],
        })
            .then(res => {
                if (!res.flag) {
                    return message.error(res?.message || '获取详情数据失败');
                }

                const { alters = [], total = 0 } = res?.data || {};

                setResponse({
                    total,
                    alerts: alters.map((x, idx) => ({ id: `${x.uniqueId}\x01${p.current + idx}`, ...x })),
                });
            })
            .finally(() => {
                setLoading(false);
            });
    }

    const handleOpen = () => {
        setModalVisible(true);
        handleQuery();
    };

    const handleClose = () => {
        setModalVisible(false);
    };

    return (
        <>
            <Button
                type="link"
                onClick={handleOpen}
            >
                {children}
            </Button>
            <Modal
                centered
                visible={modalVisible}
                title="详情"
                width="75rem"
                footer={null}
                onCancel={handleClose}
            >
                <Table
                    rowKey="id"
                    tableLayout="auto"
                    loading={loading}
                    columns={columns}
                    pagination={React.useMemo(() => ({
                        total: response?.total || 0,
                        size: 'small',
                        current: pagination.current || 1,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        pageSizeOptions: ['10', '20', '50', '100'],
                        showTotal: (total: number) => `共${total}条`,
                    }), [response, pagination])}
                    dataSource={response?.alerts || []}
                    onChange={(pagination) => {
                        setPagination(pagination);
                        handleQuery(pagination);
                    }}
                />
            </Modal>
        </>
    );
}
