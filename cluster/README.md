## NDR集群管理

集群管理(cluster)是一个独立的模块，部署在宿主机上。
该模块在宿主机上部署时，会启动三个服务：
1. 通信服务：用于管理节点与工作节点相互间进行通信
2. 升级服务：用于工作节点做软件升级，工作节点会从管理节点拉取升级文件完成升级
3. 数据升级服务(可选)：用于工作节点做数据升级，工作节点会从管理节点拉取升级文件完成升级

该模块可以调用ndr其他模块函数，ndr的其他模块不能调用该模块的函数。
host_celery服务调用该模块api实现文件同步、下发控制命令、收集状态和统计等功能，调用api前需要设置系统路径：
```python
import sys
sys.path.append('/opt/ndr')
```

## 应用场景

### 场景一：ndr下发控制命令到工作节点

1. 容器中的ndr服务将控制命令传递到宿主机上的host_celery服务
2. host_celery服务调用集群管理的api，将控制命令发送到工作节点
3. 工作节点完成控制命令执行后，将结果返回给管理节点

```python
from cluster.api.manager import MyClusterManager

my_cluster_mgr = MyClusterManager()
# 该函数在工作节点上，会先修改hdp.ini文件，然后通知hdp更新
my_cluster_mgr.hdp_set_file_size_by_cluster(1, 100)
```

### 场景二：ndr更新特征库等文件

以特征库为例：
1. 容器中的ndr服务通知宿主机上的host_celery服务
2. host_celery服务调用集群管理的api，将编译好的特征库文件发送到工作节点；将存储特征库的sqlite文件发送到工作节点
3. host_celery服务调用集群管理的api，下发控制命令到工作节点，通知hdp更新特征库
4. 工作节点返回特征库更新结果

```python
from cluster.api.manager import MyClusterManager

my_cluster_mgr = MyClusterManager()
# 该函数会先同步文件到工作节点，然后通知工作节点hdp更新
my_cluster_mgr.hdp_feature_add_by_cluster('/opt/feature/tmp/rule_path', '/opt/feature/tmp/rule_info_path')
```

### 场景三：只更新文件

1. 容器中的ndr服务通知宿主机上的host_celery服务
2. host_celery服务调用集群管理的api，将文件发送到工作节点

```python
from cluster.api.manager import MyClusterManager

my_cluster_mgr = MyClusterManager()
my_cluster_mgr.sync_file_safe_by_cluster('/opt/feature/db/KNDR_IOC.db', '/opt/feature/db/KNDR_IOC.db')
```

## 说明

1. xxx_by_cluster接口是对集群内非管理节点执行xxx操作。除了file_hash_by_cluster
2. 文件同步接口请使用sync_file_safe_by_cluster和sync_file_safe_by_node