#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import json
from flask import request

from api_1_0.knowledge.get_killchains import GetKillchains
from api_1_0.report.base_map import BaseMap
from utils.param_check import Validator
from utils.utils import flask_response
from utils import param_check
from utils.logger import get_ndr_logger
from utils.best_show_size import size_format

API_LOG = get_ndr_logger('api_log', __file__)


class KillchainsMap(BaseMap):
    '''杀伤链等级流量图'''

    def __init__(self):
        super(KillchainsMap, self).__init__("lockheedKillchainEN.keyword")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        es_return = self.get_es_data(**kwargs)
        message = es_return[1]
        if not es_return[0]:
            return flask_response(message, False, {})

        try:
            data = self.data_format(message)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(message, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, message):
        docs = []
        data_list = message["aggregations"]["group_by"]["buckets"]
        total = {}
        total_killchains = {i: 0 for i in list(GetKillchains.killchain_map.keys())}
        for data in data_list:
            killchains_dict = dict()
            time_stamp = data["key"]
            killchainsFlow = dict()
            for killchains in data["group_by"]["buckets"]:
                chains = killchains["key"]
                if not chains:
                    continue
                # 单位，返回为Mb
                flow_value = killchains["totalFlow"]["value"]
                total_killchains[chains] += flow_value
                killchainsFlow[chains] = {
                    "actualSize": flow_value,
                    "showSize": size_format(flow_value)
                }
            killchains_dict["time"] = time_stamp
            killchains_dict["killchainsFlow"] = killchainsFlow
            docs.append(killchains_dict)
        for key, value in total_killchains.items():
            total[key] = {
                "actualSize": value,
                "showSize": size_format(value)
            }

        reslut_docs = self.slice_time_data(docs, "killchainsFlow")
        data_return = {
            "total": total,
            "slice_flow": reslut_docs
        }
        return data_return
