/*
 * @Author: tianh
 * @Date: 2022-02-09 10:28:02
 * @LastEditors: tianh
 * @LastEditTime: 2022-05-30 17:07:42
 * @Descripttion:
 */

import React, { useEffect, useState } from 'react';
import { Button, Spin, Table, Checkbox, message, Tooltip } from 'antd';
import { ellipsis, splitItem } from '@/utils/utils';
import { filesColumns, filesHidden } from '@/utils/enumList';
import { getList, isExist } from '@/services/collect';
import SearchItem from './components/searchItem';
import moment from 'moment';
import store from '../store';
const Files = (props: any) => {
  const { params, setparams, activeKey, taskList } = props;

  const [total, settotal] = useState(0);
  const [loading, setloading] = useState(false);
  const [tableData, settableData] = useState([]);
  const [columns, setcolumns] = useState<any>([]);

  // 隐藏字段选择
  const handleFilter = (value: any) => {
    let col: any = store.getState().columns;
    const column = col.filter((item: any) => !item.isNew)
    value.selectedKeys.forEach((item: any, index: any) => {
      column.splice(-1, 0, {
        title: item,
        dataIndex: item,
        isNew: true,
        width: 150,
        render: (t: any) => {
          return <Tooltip title={t}>{ellipsis(t, 10)}</Tooltip>;
        },
      });
    });
    store.dispatch({
      type: 'changeColumns',
      value: column,
    });
    setcolumns(column);
    value.confirm();
  };

  const handleReset = () => {
    let arr = store.getState().columns;
    arr = arr.filter((item: any) => {
      return item.isNew === false;
    });
    setcolumns([...arr]);
  };

  const searchList = () => {
    let value = { ...params };
    if (value.time && value.time.length) {
      value.startTime =
        moment(value.time[0])
          .startOf('seconds')
          .unix() * 1000;
      value.stopTime =
        moment(value.time[1])
          .startOf('seconds')
          .unix() * 1000;
    }

    setloading(true);
    delete value.time;
    getList(value).then(res => {
      if (res.flag) {
        setloading(false);
        settotal(res.data.total);
        settableData(res.data.dpilog);
      } else {
        message.error(res.message);
      }
    });
  };
  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };

  // 下载文件
  const downloadFile = (fileName: any) => {
    const filename = encodeURIComponent(fileName);
    isExist({
      is_exist: true,
      filename,
    }).then(res => {
      if (res.flag) {
        window.location.href = `${window.location.protocol}//${window.location.host}/mica-api/api/v1/dpilog/mail_down?filename=${filename}`;
      } else {
        message.error(res.message);
      }
    });
  };

  useEffect(() => {
    const columnsArr: any = [
      {
        title: '时间',
        dataIndex: 'ts',
        isNew: false,
        fixed: 'left',
        width: 150,
        render: (t: number) => {
          return <span>{moment(t).format('YYYY-MM-DD HH:mm:ss')}</span>;
        },
      },
      {
        title: '源IP',
        dataIndex: 'src_ip',
        isNew: false,
        width: 150,
      },
      {
        title: '源端口',
        dataIndex: 'src_port',
        width: 150,
        isNew: false,
      },
      {
        title: '目的IP',
        dataIndex: 'dst_ip',
        width: 150,
        isNew: false,
      },
      {
        title: '目的端口',
        dataIndex: 'dst_port',
        width: 150,
        isNew: false,
      },
      {
        title: 'net_ext_flag',
        dataIndex: 'net_ext_flag',
        width: 150,
        isNew: false,
      },
      {
        title: ``,
        isNew: false,
        dataIndex: 'filter',
        fixed: 'right',
        width: 100,
        render: (t: any, record: any) => {
          return record.filename ? (
            <Button
              onClick={() => {
                downloadFile(record.filename);
              }}
              type="link"
            >
              下载
            </Button>
          ) : null;
        },
        filterDropdown: (props: any) => (
          <div style={{ padding: 8 }}>
            <div>
              <Checkbox.Group
                value={props.selectedKeys}
                onChange={e => {
                  props.setSelectedKeys(e);
                }}
              >
                {filesHidden.map((item: any) => {
                  return (
                    <div key={item}>
                      <Checkbox value={item}>{item}</Checkbox>
                    </div>
                  );
                })}
              </Checkbox.Group>
            </div>
            <Button
              type="primary"
              size="small"
              style={{
                width: 60,
                marginRight: 8,
              }}
              onClick={() => {
                handleFilter(props);
              }}
            >
              确定
            </Button>
            <Button
              size="small"
              onClick={() => {
                handleReset();
                props.setSelectedKeys([]);
                props.clearFilters();
              }}
              style={{ width: 60 }}
            >
              重置
            </Button>
          </div>
        ),
      },
    ];
    filesColumns.forEach(item => {
      let obj = {
        title: item.label,
        dataIndex: item.key,
        isNew: false,
        width: 150,
        render: (t: any, record: any) => {
          // if (t) {
          return (
            item.key === 'filename' ? <Tooltip placement="topLeft" title={splitItem(t)} arrowPointAtCenter>
              {ellipsis(splitItem(t), 10)}
            </Tooltip> : <Tooltip placement="topLeft" title={t} arrowPointAtCenter>
              {ellipsis(t, 10)}
            </Tooltip>
          );
          // }
        },
      };
      columnsArr.splice(-1, 0, obj);
    });
    store.dispatch({ type: 'changeColumns', value: columnsArr });
    setcolumns(columnsArr);
  }, []);

  return (
    <div>
      <SearchItem
        params={params}
        setparams={setparams}
        activeKey={activeKey}
        searchList={searchList}
        taskList={taskList}
      />
      <Spin spinning={loading}>
        <Table
          columns={columns}
          pagination={pagination}
          rowKey={(r: any, i: any) => r.uuid}
          scroll={{ x: 1200 }}
          dataSource={tableData}
        />
      </Spin>
    </div>
  );
};

export default Files;
