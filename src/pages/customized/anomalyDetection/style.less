.statsCard {
  margin-bottom: 16px;
  
  .ant-statistic-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }
  
  .ant-statistic-content {
    font-size: 24px;
    font-weight: 600;
  }
}

.mainCard {
  .ant-tabs-nav {
    margin-bottom: 24px;
  }
  
  .ant-tabs-tab {
    font-size: 16px;
    font-weight: 500;
  }
}

.taskCard {
  margin-bottom: 16px;
  
  .taskHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .taskTitle {
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
    }
    
    .taskStatus {
      &.active {
        color: #52c41a;
      }
      
      &.inactive {
        color: #d9d9d9;
      }
      
      &.error {
        color: #f5222d;
      }
    }
  }
  
  .taskMeta {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    
    .metaItem {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      
      .metaIcon {
        font-size: 14px;
      }
    }
  }
  
  .taskActions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

.resultCard {
  margin-bottom: 16px;
  border-left: 4px solid transparent;
  
  &.critical {
    border-left-color: #f5222d;
  }
  
  &.high {
    border-left-color: #fa8c16;
  }
  
  &.medium {
    border-left-color: #faad14;
  }
  
  &.low {
    border-left-color: #52c41a;
  }
  
  .resultHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .resultTitle {
      font-size: 16px;
      font-weight: 600;
    }
    
    .severityTag {
      &.critical {
        background-color: #fff2f0;
        color: #f5222d;
        border-color: #ffccc7;
      }
      
      &.high {
        background-color: #fff7e6;
        color: #fa8c16;
        border-color: #ffd591;
      }
      
      &.medium {
        background-color: #fffbe6;
        color: #faad14;
        border-color: #ffe58f;
      }
      
      &.low {
        background-color: #f6ffed;
        color: #52c41a;
        border-color: #b7eb8f;
      }
    }
  }
  
  .resultMeta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
    
    .metaItem {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
  
  .resultDescription {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.5;
  }
  
  .resultActions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

.alertCard {
  margin-bottom: 16px;
  
  .alertHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .alertTitle {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      
      .alertIcon {
        font-size: 18px;
        
        &.critical {
          color: #f5222d;
        }
        
        &.high {
          color: #fa8c16;
        }
      }
    }
    
    .alertStatus {
      &.pending {
        color: #faad14;
      }
      
      &.confirmed {
        color: #52c41a;
      }
      
      &.dismissed {
        color: #d9d9d9;
      }
    }
  }
  
  .alertMeta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
    
    .metaItem {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
  
  .alertActions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

.chartContainer {
  height: 400px;
  margin-bottom: 24px;
}

.filterBar {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  
  .filterRow {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
    
    .filterItem {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .filterLabel {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        white-space: nowrap;
      }
    }
  }
}

.emptyState {
  text-align: center;
  padding: 48px 0;
  color: rgba(0, 0, 0, 0.45);
  
  .emptyIcon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .emptyText {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .emptySubText {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.25);
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
