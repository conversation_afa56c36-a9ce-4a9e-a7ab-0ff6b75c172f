/*
 * @Author: 田浩
 * @Date: 2021-09-23 15:52:41
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-02 10:29:41
 * @Descripttion:
 */
import React, { forwardRef } from 'react';
import { Form, Input, Select, Row, Col } from 'antd';

const { Option } = Select;
const { TextArea } = Input;

const MeansForm = forwardRef((props: any, ref: any) => {
  const { value, isAdd } = props;
  value.ports = Object.keys(value.enable_port);
  
  const [form] = Form.useForm();
  const layout = {
    labelCol: { span: '30px' },
    // wrapperCol: { span: 18 },
  };
  
  return (
    <Form ref={ref} form={form} initialValues={value} {...layout}>
      <Row>
        <Col span={8} key="ip_addr">
          <Form.Item
            name="ip_addr"
            label="ip地址"
            rules={[
              {
                required: true,
                message: '请填写ip地址!',
              },
            ]}
          >
            <Input disabled={!isAdd} />
          </Form.Item>
        </Col>
        <Col span={8} key="name">
          <Form.Item name="name" label="资产名称">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="mac_addr">
          <Form.Item name="mac_addr" label="MAC地址">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={8} key="components">
          <Form.Item name="components" label="组件列表">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="department">
          <Form.Item name="department" label="所属部门">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="industry">
          <Form.Item name="industry" label="所属行业">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={8} key="country_code">
          <Form.Item name="country_code" label="国家代码">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="location">
          <Form.Item name="location" label="地理位置">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="is_sercret">
          <Form.Item
            name="is_sercret"
            label="涉密资产"
            initialValue={value.is_sercret ? value.is_sercret : false}
          >
            <Select>
              <Option value={true} key="true">是</Option>
              <Option value={false} key="false">否</Option>
            </Select>
          </Form.Item>
        </Col>

      </Row>
      <Row>
        <Col span={8} key="other">
          <Form.Item name="other" label="其他">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="domain">
          <Form.Item name="domain" label="域名">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="type">
          <Form.Item name="type" label="资产类型">
            <Input />
          </Form.Item>
        </Col>

      </Row>
      <Row>
        <Col span={8} key="os_info">
          <Form.Item name="os_info" label="操作系统">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="assets_num">
          <Form.Item name="assets_num" label="资产编号">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="organisation">
          <Form.Item name="organisation" label="所属组织">
            <Input />
          </Form.Item>
        </Col>

      </Row>
      <Row>
        <Col span={8} key="country">
          <Form.Item name="country" label="所属国家">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="person">
          <Form.Item name="person" label="责任人">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8} key="is_importent">
          <Form.Item
            name="is_importent"
            label="重点资产"
            initialValue={value.is_importent ? value.is_importent : false}
            rules={[
              {
                required: true,
                message: '请选择!',
              },
            ]}
          >
            <Select>
              <Option value={true}>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </Form.Item>
        </Col>

      </Row>
      <Row>
        <Col span={8} key="labels">
          <Form.Item
            name="labels"
            extra="多个标签请务必使用英文,进行分隔"
            label="资产标签"
            rules={[
              {
                required: false,
                pattern: /^([u4e00-\u9fa5_a-z_A-Z-0-9,]+)$/,
                message:
                  '只支持数字、中文、- 、_、英文，不区分大小写，请正确输入',
              },
            ]}
          >
            <TextArea
              autoSize={{ minRows: 4 }}
              placeholder="多个标签请务必使用英文逗号进行分隔"
            />
          </Form.Item>
        </Col>
        <Col span={16} key="ports">
          <Form.Item
            name="ports"
            label="启用端口"
            extra="可输入多个端口，回车确定输入"
          >
            <Select
              mode="tags"
              size='large'
            />
          </Form.Item>
        </Col>
      </Row>


    </Form >
  );
});

export default MeansForm;
