#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
""" 获取单个 IOC 告警详情 """
from flask import request

from api_1_0.back_event.get_ioc_list import format_es_temp
from utils.database import MongoDB
from utils.es_function import get_es_template
from utils.ndr_base import NdrResource
from utils.utils import flask_response
from utils.param_check import Validator, check_flask_args
from config.config import IOC_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)

TAG_CN = {
    "SQLI": "SQL注入",
    "WEBSHELL": "webshell",
    "XSS": "XSS跨站",
    "FILEI": "敏感文件访问",
    "CODE": "代码执行",
    "SCANNER": "恶意扫描",
    "SPECIAL": "特殊攻击",
    "COLLECTOR": "恶意采集",
    "OS_COMMAND": "远程命令",
    "LRFI": "文件包含",
    "OTHER": "其它攻击",
    "GLOBAL_DEFENSE": "协同防御",
    "LIMIT_RATE": "URL限速",
    "UA_BLACK": "UA黑名单",
    "DIR_LOCK": "后台锁",
    "URL_BLACK": "网址黑名单",
    "IP_BLACK": "IP黑名单",
    "AREA_LOCK": "区域访问控制",
    "APP_CC": "app防cc拦截",
    "IDC_IP": "协同防御IDC IP拦截",
    "PROXY_IP": "协同防御代理IP拦截",
    "WAF_BLOCK": "动态阻断拦截"
}


class IocDetailResource(NdrResource):
    """ 获取IOC数据详情 """

    def __init__(self):
        """初始化参数"""
        self.mongodb = MongoDB("ndr")
        super(IocDetailResource, self).__init__(es_template="ioc_detail")

    @check_flask_args(Validator("ioc_list"), request)
    def post(self, **kwargs):
        option_args = {
            "celeryId": kwargs["celeryId"],
            "threatLevel": kwargs["threatLevel"],
            "threatScore": kwargs["threatScore"],
            "killchain": kwargs["killchain"],
            "mbinfo": kwargs["mbinfo"],
            "threatType": kwargs["threatType"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        group = kwargs["groupKey"]
        if group != "uniqueId":
            group = "flow." + group
        group = group + ".keyword"
        unique_filter = {
            "term": {
                group: kwargs["value"]
            }
        }
        es_template["query"]["bool"]["must"].append(unique_filter)
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])
        es_template['from'] = (page - 1) * page_size
        es_template['size'] = page_size
        try:
            res = self.es_client.search(index=IOC_INDEX, body=es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(kwargs["groupKey"], res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, group_key, es_data):
        total = es_data["hits"]["total"]['value']
        data_return = {
            "total": total,
            "alters": []
        }
        ioc_detail_list = es_data["hits"]["hits"]
        if group_key == "uniqueId":
            for ioc_detail in ioc_detail_list:
                ioc_dict = dict()
                ioc_dict["threatType"] = ioc_detail["_source"]["threatType"]
                ioc_dict["refer"] = ioc_detail["_source"]["refer"]
                ioc_dict["tools"] = ioc_detail["_source"]["tools"]
                ioc_dict["aptOrganization"] = ioc_detail["_source"]["aptOrganization"]
                ioc_dict["ioc"] = ioc_detail["_source"]["ioc"]
                ioc_dict["taskType"] = ioc_detail["_source"]["taskType"]
                ioc_dict["occurredTime"] = ioc_detail["_source"]["occurredTime"]
                ioc_dict["observedTime"] = ioc_detail["_source"]["observedTime"]
                ioc_dict["src_mac"] = ioc_detail["_source"]["flow"]["src_mac"]
                ioc_dict["dst_mac"] = ioc_detail["_source"]["flow"]["dst_mac"]
                ioc_dict["responseName"] = ioc_detail["_source"]["responseName"]
                ioc_dict["src_ip"] = ioc_detail["_source"]["flow"]["src_ip"]
                ioc_dict["dst_ip"] = ioc_detail["_source"]["flow"]["dst_ip"]
                ioc_dict["src_port"] = ioc_detail["_source"]["flow"]["src_port"]
                ioc_dict["dst_port"] = ioc_detail["_source"]["flow"]["dst_port"]
                ioc_dict["proto"] = ioc_detail["_source"]["flow"]["proto"]
                ioc_dict["pcap_filename"] = ioc_detail["_source"]["pcap_filename"]
                ioc_dict["not_fss_pcapname"] = ioc_detail["_source"]["not_fss_pcapname"]
                data_return['alters'].append(ioc_dict)
        else:
            for ioc_detail in ioc_detail_list:
                data_dict = ioc_detail["_source"]
                data_dict["taskName"] = ""
                celery_id = data_dict["celeryId"]
                rst = self.mongodb.find_one('back_explore', {"celeryId": celery_id})
                if rst:
                    data_dict["taskName"] = rst['taskName']
                data_return['alters'].append(data_dict)
        return data_return
