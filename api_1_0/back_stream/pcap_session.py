# -*- coding: utf-8 -*-
# @Time    : 2019-05-15 11:11
# <AUTHOR> Shark
# @File    : pcap_session.py
# @Software: PyCharm

import requests
from requests.auth import HTTPDigestAuth
from flask import request
from flask_restful import Resource
from config.config import BackStreamConfig
from api_1_0.back_stream.utils import check_args


class PcapSession(Resource):
    """api接口/api/v1/pcap/session处理"""

    def __init__(self):
        self.args = request.values.to_dict()
        self.response = {
            "message": "",
            "data": {}
        }

    def args_valid_check(self):
        """ 根据正则匹配的方式检测每个参数是否有效，
        无效则返回 False 和错误参数
        有效则返回 True"""

        return check_args(self.args)

    def comb_url(self):
        """ 判断传入的参数是否为空，来确认是否给默认值，并组装最终的 URL 请求"""

        req_url = 'http://{host}:{port}/ndr-backndr/session/session_id/packets?line=true' \
            '&image=false&gzip=false&ts=false&decode=%7B%7D&showFrames=false'.format(
                host=BackStreamConfig.BackStream_HOST,
                port=BackStreamConfig.BackStream_PORT)

        # 显示的 packets 数量，默认为 3 个
        if self.args['num']:
            req_url += "&packets=" + self.args['num']
        else:
            req_url += "&packets=3"
        #
        if self.args['base']:
            req_url += "&base=" + self.args['base']
        #
        if self.args['sessionId']:
            req_url = req_url.replace('session_id', self.args['sessionId'])

        return req_url

    def get(self):
        """api接口/api/v1/pcap/session的get请求处理"""
        is_valid, msg = self.args_valid_check()

        if not is_valid:
            self.response['message'] = msg
            return self.response
        print(self.comb_url())
        resp = requests.get(self.comb_url(), auth=HTTPDigestAuth(
            BackStreamConfig.BackUsername,
            BackStreamConfig.BackPassword
        ))

        self.response['message'] = ""
        self.response['data'] = resp.text

        return self.response
