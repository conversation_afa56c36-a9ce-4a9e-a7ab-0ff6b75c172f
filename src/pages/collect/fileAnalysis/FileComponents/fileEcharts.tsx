import React, { useEffect, useState } from 'react';
import cn from 'classnames';
import moment from 'moment';
import { Empty } from 'antd';

const echarts = require('echarts');

import s from './style.less';
import { any } from 'prop-types';
interface FileListProps {
  className?: string;
  domId?: string;
  type?: string;
  data: any;
}
const FileEcharts = ({ className, domId, type, data,params }: FileListProps) => {
  const [title, setTitle] = useState({
    echarts1: '文件检测报告趋势',
    echarts2: '文件分析包类型',
    echarts3: '文件病毒引擎命中',
  });
  const optionObj = {
    bar: {
      backgroundColor: '#fff',
      color: ['#3398DB'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none',
        },
      },
      grid: {},
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
        },
        splitLine: {
          show: false,
        },
      },
      series: [
        {
          name: '安全',
          type: 'bar',
          stack: '总量',
          barWidth: 10,
          z: 2,
          itemStyle: {
            normal: {
              color: '#8fcc6b',
            },
          },
          data: [],
        },
        {
          name: '危急',
          type: 'bar',
          stack: '总量',
          z: 1,
          itemStyle: {
            normal: {
              color: '#6bbfcc',
            },
          },
          data: [],
        },
      ],
    },
    pie: {
      backgroundColor: '#fff',
      tooltip: {
        trigger: 'item',
        axisPointer: {
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
        formatter: params => {
          const { name, value, percent } = params;
          return `${name}: ${value}(${percent}%)`;
        },
      },
      legend: {
        icon: 'circle',
        orient: 'vertical',
        right: 10,
        top: 10,
        itemHeight: 12, // 修改icon图形大小
        itemStyle: {
          borderWidth: 0,
          shadowBlur: 0,
        },
      },
      series: [
        {
          left: '0%',
          center: ['30%', '50%'],
          hoverAnimation: false,
          label: {
            show: false,
          },
          // itemStyle: {
          //   borderWidth: 2,
          //   borderColor: '#fff',
          // },
          name: 'Access From',
          type: 'pie',
          radius: '50%',
          data: [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    },
  };
  const handle1Num = data => {
    const obj = { time: [], risk: [], safe: [] };
    data.map(e => {
      if(params.startTime<e.time){
        obj.time.push(moment(e.time).format('YYYY-MM-DD HH:mm:ss'));
        obj.risk.push(e.risk);
        obj.safe.push(e.safe);
      }
    });
    return obj;
  };
  useEffect(() => {
    if (!data) {
      return;
    }
    const option = optionObj[type];
    const fileEcharts1 = echarts.init(document.getElementById(domId));
    if (domId === 'echarts1') {
      const data1 = handle1Num(data);
      option.xAxis.data = data1.time;
      option.series[0].data = data1.safe;
      option.series[1].data = data1.risk;
    } else {
      option.series[0].data = data.map(e => {
        return {
          value: e.total,
          name: e.type,
        };
      });
    }
    fileEcharts1.setOption(option, true);
    fileEcharts1.resize();
    window.addEventListener('resize', () => {
      fileEcharts1.resize();
    });
    fileEcharts1.on('legendselectchanged', params => {
      fileEcharts1.setOption({
        legend: { selected: { [params.name]: true } },
      });
    });
    return () => {
      window.removeEventListener('resize', () => {
        fileEcharts1.resize();
      });
    };
  }, [data]);
  return (
    <div className={cn('pa-1', s.FileEcharts, className)}>
      <span className="c-t4 pos-absolute z-10">{title[domId]}</span>
      <div id={domId} style={{ width: '100%', height: '100%' }}>
        {!data && <Empty />}
      </div>
    </div>
  );
};
export default FileEcharts;
