import React, { useEffect, useState } from 'react';
import { Select, Button, message, Empty } from 'antd';
import copy from 'copy-to-clipboard';
import s from './style.less';
import { useDataList } from './hook';

const DataContent = ({ params }: any) => {
  const { getContent } = useDataList();
  const [options, setOptions] = useState([]);
  const [selVal, setSelVal] = useState('all');
  const [asciiStr, setAsciiStr] = useState({
    sStr: '',
    cStr: '',
  });
  const getContentFun = async () => {
    const res = await getContent(params);
    const { cbytes, sbytes, chost, cport, shost, sport, payloads } = res;
    if (!payloads?.length) {
      return;
    }
    let sStr = '';
    let cStr = '';
    payloads.map((item: any) => {
      if (item.s) {
        sStr += window.atob(item.d);
      } else {
        cStr += window.atob(item.d);
      }
    });
    setAsciiStr({ sStr, cStr });
    setOptions([
      { value: 'all', label: `整个对话(${cbytes + sbytes}bytes)` },
      { value: 's', label: `${shost}:${sport}->${chost}:${cport}(${sbytes}bytes)` },
      { value: 'c', label: `${chost}:${cport}->${shost}:${sport}(${cbytes}bytes)` },
    ]);
  };
  const onCopy = () => {
    const objT = {
      all: asciiStr.sStr + asciiStr.cStr,
      s: asciiStr.sStr,
      c: asciiStr.cStr,
    };
    const copyT = objT[selVal];
    if (copy(copyT)) {
      message.success('内容已复制！');
    } else {
      message.error('内容复制失败，请手动复制。');
    }
  };
  useEffect(() => {
    getContentFun();
  }, [params]);
  return (
    <div className={s.dataContent}>
      {!asciiStr.sStr && !asciiStr.cStr ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : (
        <p>
          <Select style={{ width: '300px' }} value={selVal} size="small" options={options} onChange={setSelVal} />
          <Button type="link" onClick={onCopy}>
            复制
          </Button>
        </p>
      )}
      <div>
        {['all', 'c'].includes(selVal) && <code className={s.cStr}>{asciiStr.cStr}</code>}
        {['all', 's'].includes(selVal) && <code className={s.sStr}>{asciiStr.sStr}</code>}
      </div>
    </div>
  );
};
export default DataContent;
