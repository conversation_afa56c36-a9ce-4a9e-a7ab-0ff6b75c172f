# -*- coding: utf-8 -*-
# @Time    : 2019-08-05 14:47
# <AUTHOR> <PERSON>
# @File    : get_killchains.py
# @Software: PyCharm
from utils.ndr_base import NdrResource
from utils.utils import flask_response


class GetKillchains(NdrResource):
    """获取杀伤链字段信息"""
    killchain_map = {
        "Reconnaissance": "侦查跟踪",
        "Weaponization": "武器构建",
        "Delivery": "载荷投递",
        "Exploitation": "漏洞利用",
        "Installation": "安装植入",
        "Command and Control": "命令控制",
        "Actions on Objective": "目标达成"
    }

    @staticmethod
    def get():
        """
        :return:
        """
        return_data = []
        for key, value in GetKillchains.killchain_map.items():
            return_data.append({"lockheedKillchainEN": key, "lockheedKillchainCN": value})
        return flask_response("", True, return_data)
