# -*-coding:utf-8-*-
# @File    : system_monitor.data.py
# @Software: PyCharm

import json

import traceback
import requests
from flask import request

from utils import param_check
from utils.json_format import J<PERSON>NEncoder
from flask_restful import Resource

from utils.param_check import Validator
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from config.config import NdrLog
from utils.database import MongoDB
from utils.cluster import *

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def get_node_name_condition(kwargs):
    node_name = ''
    if kwargs:
        args = kwargs.get("args", {})
        node_name = args.get("node_name", "")
        if not node_name:
            node_name = kwargs.get("node_name", "")

    if node_name == '':
        node_name = cluster_get_node_name()

    condition = {}
    if node_name != '':
        condition = {'node_name': node_name}

    return condition

class SystemMonitor(Resource):
    """ 硬件监控 """

    def __init__(self):
        self.db = MongoDB("ndr")


class GetSystemCpu(SystemMonitor):

    def __init__(self):
        super(GetSystemCpu, self).__init__()

    def get(self, **kwargs):
        try:
            condition = get_node_name_condition(request.values)
            LOG.info("cpu get condition：%s" % json.dumps(condition))
            peak_data = self.db.find_one('monitor_peak', condition, {"_id": 0})
            if peak_data is None:
                LOG.info("peak_data")
            peak_cpu = peak_data["cpuPeak"]
            peak_cpu_time = peak_data["cpuPeakTime"]
            return_data = {
                "peak_cpu": peak_cpu,
                "peak_cpu_time": peak_cpu_time,
                "cols": []
            }
            rst = self.db.find('monitor_cpu', condition, {"_id": 0})
            if rst is None:
                LOG.info("monitor_cpu")

            for col in rst:
                col = JSONEncoder().encode(col)
                return_data["cols"].append(json.loads(col))
            return flask_response("", True, return_data)
        except Exception as error:
            LOG.error("cpu get failed.Reason：%s\n%s" % (str(error), traceback.format_exc()))
            return flask_response("cpu get failed", False, {})


class GetSystemMem(SystemMonitor):

    def __init__(self):
        super(GetSystemMem, self).__init__()

    def get(self, **kwargs):
        try:
            condition = get_node_name_condition(request.values)
            LOG.info("mem get condition：%s" % json.dumps(condition))
            peak_data = self.db.find_one('monitor_peak', condition, {"_id": 0})
            peak_mem = peak_data["memPeak"]
            peak_mem_time = peak_data["memPeakTime"]
            return_data = {
                "peak_mem": peak_mem,
                "peak_mem_time": peak_mem_time,
                "cols": []
            }
            rst = self.db.find('monitor_mem', condition, {"_id": 0})
            for col in rst:
                col = JSONEncoder().encode(col)
                return_data["cols"].append(json.loads(col))
            return flask_response("", True, return_data)
        except Exception as error:
            LOG.error("mem get failed.Reason：%s\n%s" % (str(error), traceback.format_exc()))
            return flask_response("mem get failed", False, {})


class GetSystemDisk(SystemMonitor):

    def __init__(self):
        super(GetSystemDisk, self).__init__()

    def get(self, **kwargs):
        try:
            condition = get_node_name_condition(request.values)
            LOG.info("disk get condition：%s" % json.dumps(condition))
            rst = self.db.find_one('monitor_disk', condition, {"_id": 0, "node_name": 0})
            col = JSONEncoder().encode(rst)
            cols = json.loads(col)
            return flask_response("", True, cols)
        except Exception as error:
            LOG.error("disk get failed.Reason: %s\n%s" % (str(error), traceback.format_exc()))
            return flask_response("disk get failed", False, {})

