import base64

import requests
from pymongo import MongoClient
from kafka import KafkaConsumer
import json
import socket
import time
from email.mime.text import MIMEText
from datetime import datetime
from dateutil.relativedelta import relativedelta
import smtplib
import IPy
import uuid


query_cond = {
    "state": "enable",
    "$or": [
        {
            "$and": [
                {"syslog.is_enable": True},
                {"syslog.log_trigger_ip.0": {"$exists": True}}
            ]
        },
        {
            "$and": [
                {"emal.is_enable": True},
                {"emal.log_trigger_ip.0": {"$exists": True}}
            ]
        },
        {
            "$and": [
                {"threat_gw.is_enable": True},
                {"threat_gw.server": {"$ne": ""}},
                {"threat_gw.token": {"$ne": ""}},
            ]
        }
    ]
}

level_severity_map = {
    'High': 5,
    'Medium': 4,
    'Low': 3
}

consumer = KafkaConsumer('mail_logout', group_id=str(uuid.uuid1()), auto_offset_reset='latest',
                         enable_auto_commit=False, bootstrap_servers='kafka:9092', consumer_timeout_ms=20000)


def filter_data(filter_record, log_trigger_ip):
    """
      过滤数据
    """
    return_data = []
    for i in filter_record:
        src_ip = i["flow"]["src_ip"]
        dst_ip = i["flow"]["dst_ip"]
        for ip in log_trigger_ip:
            if ip.__contains__("/"):
                if src_ip in IPy.IP(ip, make_net=1) or dst_ip in IPy.IP(ip, make_net=1):
                    return_data.append(i)
                    break
            else:
                if src_ip == ip or dst_ip == ip:
                    return_data.append(i)
                    break
    return return_data


def send_mail(data_list, email_conf):
    print("send mail ---------")
    mail_server = email_conf["email"]["mail_server"]
    server_port = email_conf["email"]["server_port"]
    account = email_conf["email"]["account"]
    password = str(base64.b64decode(email_conf["email"]["password"].encode("utf-8")), 'utf-8')[0:-3]
    receiver = email_conf["email"]["receiver"]
    str1 = str(data_list)
    # 构造邮件，内容
    msg = MIMEText(str1)
    # 设置邮件主题
    msg['Subject'] = "告警日志外发"
    # 寄件者
    msg['From'] = account
    sendto_list = [receiver]
    # 收件者
    msg['To'] = ';'.join(sendto_list)
    mailserver = smtplib.SMTP(mail_server, server_port)
    # 登录邮箱
    mailserver.login(account, password)
    mailserver.sendmail(account, sendto_list, msg.as_string())
    mailserver.quit()


def send_syslog(data_list, syslog_conf):
    print("send syslog ---------")
    syslog_server = syslog_conf["syslog"]["syslog_server"]
    server_port = syslog_conf["syslog"]["server_port"]
    set_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    set_sock.connect((syslog_server, server_port))
    for data in data_list:
        set_sock.send(bytes(json.dumps(data), encoding="utf8"))
    set_sock.close()


def send_threat_gw(data, threat_gw_cfg):
    print("send threat ioc gateway ---------")
    remote_url = 'http://%s:7002/api/v1/ti' % threat_gw_cfg['threat_gw']['server']
    headers = {
        'Authorization': 'Bearer %s' % threat_gw_cfg['threat_gw']['token'],
        'Content-Type': 'application/json'
    }

    for org_data in data:
        if org_data.get('eve_type', None) != 'ioc_eve':
            continue
        if org_data['ioc_type'] == 'ip' or org_data['ioc_type'] == 'ip_ports':
            ioc_value = org_data['ioc'] if org_data['ioc_type'] == 'ip' else org_data['ioc'].split(':')[0]
            format_data = {
                'type': 'ip',
                'value': ioc_value,
                'confidence': 80,
                # 有效期从当前时间计算，至1个月后
                'valid_from': datetime.strftime(datetime.utcfromtimestamp(time.time()), '%Y-%m-%dT%H:%M:%SZ'),
                'valid_until': datetime.strftime(
                    datetime.utcfromtimestamp(time.time()) + relativedelta(months=1), '%Y-%m-%dT%H:%M:%SZ'),
                'source': org_data['refer'],
                'labels': [org_data['labels']],
                'severity': level_severity_map.get(org_data['threatLevel'], 1)
            }
            requests.post(remote_url, json=format_data, headers=headers, timeout=3)


def send_data(all_data: list, policy_data: list):
    print("send start ----------")
    for policy_cfg in policy_data:
        data = [i for i in all_data if i["policy_name"] == policy_cfg["policy_name"]]
        for key, func in policy_func_map.items():
            if policy_cfg[key]['is_enable']:
                trigger_ip = policy_cfg[key].get("log_trigger_ip", None)
                if trigger_ip:
                    pure_data = data if '0.0.0.0' in trigger_ip else filter_data(policy_data, trigger_ip)
                else:
                    pure_data = data

                if pure_data:
                    try:
                        func(pure_data, policy_cfg)
                    except Exception as e:
                        print('function: %s send failed! Reson: %s' % (str(func), str(e)))
                        continue


if __name__ == '__main__':
    print("alert_output start:--------")
    conn = MongoClient(host='mongo', port=27017, username="ksbox", password="FBgxC5PWAre", authSource='admin')
    db = conn["ndr"]
    policy_func_map = {
        'email': send_mail,
        'syslog': send_syslog,
        'threat_gw': send_threat_gw
    }

    while True:
        rst_list = db["security_policy"].find(query_cond, {"_id": 0}).sort([("priority", -1)])
        policy_list = list(rst_list)
        if not rst_list or len(policy_list) == 0:
            time.sleep(3)
            continue

        data_list = []
        # 从kafka获取20s的数据，如果超过50条，按照50条每批次发送，如果没超过，则单独作为一批发送
        for msg in consumer:
            record = msg.value.decode()
            record = json.loads(record)
            record.pop("celeryId")
            if record["eve_type"] == "rule_eve":
                record.pop("payload")
            elif record["eve_type"] == "model_eve":
                record.pop("detail")
            data_list.append(record)
            if data_list.__len__() > 50:
                send_data(data_list, policy_list)
                data_list.clear()

        if data_list.__len__() > 0:
            send_data(data_list, policy_list)

        time.sleep(1)
