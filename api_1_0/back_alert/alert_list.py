# -*- coding: utf-8 -*-
# @Time    : 2019-05-27 17:09
# <AUTHOR> Shark
# @File    : alert_list.py
# @Software: PyCharm

from flask import request

from api_1_0.utils import flask_log
from utils.es_data_format import es_data_format2, es_data_format
from utils.ndr_base import NdrResource
from utils import param_check
from config.config import NdrLog, RULE_INDEX
from utils.param_check import Validator
from utils.es_function import *
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from utils.database import MongoDB
from api_1_0.utils.flask_log import ndr_log_to_box

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)

field_temp = {
    "sid": "sid",
    "killchain": "lockheedKillchainEN.keyword",
    "celeryId": "celeryId.keyword",
    "threatFlag": "threatFlag.keyword",
    "threatLevel": "threatLevel.keyword",
    "confirm": "confirm"
}


def format_es_temp(option_args, es_template):
    """
    :param option_args:
    :return:
    """
    es_must_list = []
    for key, value in option_args.items():
        if value in ["", None]:
            continue
        if key == "mbinfo":
            query = get_mbinfo(option_args["mbinfo"])
            es_template["query"]["bool"]["must"].append(query)
        elif key == "threatScore":
            pass
            # threat_score_start, threat_score_stop = value.split('-')
            # es_template["query"]["bool"]["must"].append(
            #     get_range("threatScore", int(threat_score_start), int(threat_score_stop)))
        elif key == "sid":
            search_args = {field_temp[key]: int(value)}
            args_format = {"term": search_args}
            es_must_list.append(args_format)
        else:
            search_args = {field_temp[key]: value}
            args_format = {"term": search_args}
            es_must_list.append(args_format)
    es_template["query"]["bool"]["must"].extend(es_must_list)
    return es_template


class AlertList(NdrResource):
    """根据一项或多项参数查询告警事件"""

    threat_flag_list = [
        {
            "threatFlag": "Exploits and Attacks",
            "threatFlagCN": "攻击利用",
            "describe": "通过网络上已公布的漏洞进行攻击，使用漏洞攻击通常会造成数据窃取，拒绝服务，获取服务器控制权限。"
                        "攻击者通常通过漏洞发布平台（exploit-db，乌云，补天，CVE等）获取漏洞的利用手段进行攻击。",
            "advice": "关注CVE平台，微软官方网站，漏洞发布之后及时更新软件/系统版本，或者安装漏洞补丁，部署WAF防御针对web的攻击"
        },
        {
            "threatFlag": "APT",
            "threatFlagCN": "APT攻击",
            "describe": "某APT组织对主机展开的持续有效的攻击活动。",
            "advice": "1、部署主机防御产品HIDS、AV、EDR等;2、部署网络防御产品NIDS、NGFW等。"
        },
        {
            "threatFlag": "Malicious Host",
            "threatFlagCN": "恶意主机",
            "describe": "恶意主机针对利用漏洞对目标发起漏洞攻击，植入木马，达到远控服务器的目的。",
            "advice": "核查可疑文件，发现是病毒及时清除。"
        },
        {
            "threatFlag": "Suspicious",
            "threatFlagCN": "可疑行为",
            "describe": "可疑行为一般出现在网络内部，基于主机的可疑行为包括有可疑程序执行、DLL注入等等，"
                        "网络行为一般可能是流量异常或者请求可疑域名或IP。",
            "advice": "部署主机防御产品（HIDS/AV/EDR等）部署网络防御产品（NIDS/NGFW等) 部署SIEM/SOC等大数据分析工具"
        },
        {
            "threatFlag": "Botnet",
            "threatFlagCN": "僵尸网络",
            "describe": "僵尸网络，是指采用一种或多种传播手段，将大量主机感染僵尸程序病毒，从而在控制者和被感染主机"
                        "之间所形成的一个可一对多控制的网络。",
            "advice": "关闭不必要的对外端口，及时更新安全补丁，进行员工安全培训，定期进行网络脆弱评估与加固等，"
                      "在被僵尸网络病毒感染前做好事前措施。部署安全设备持续监控受保护的网络，发现潜在的僵尸网络病毒，"
                      "进行事件响应与取证分析，上报监管部门进行封堵。"
        },
        {
            "threatFlag": "Phishing",
            "threatFlagCN": "钓鱼邮件",
            "describe": "钓鱼邮件是指攻击者伪装成同事、合作伙伴、朋友、家人等用户信任的人，通过发送电子邮件的方式，"
                        "诱使用户回复邮件、点击嵌入邮件正文的恶意链接或者打开邮件附件以植入木马或间谍程序，"
                        "进而窃取用户敏感数据、个人银行账户和密码等信息，或者在设备上执行恶意代码实施进一步的网络攻击活动。",
            "advice": "部署邮件网关+AV用来检测邮件附件以及链接，部署终端安全防护设备，"
                      "检测下载的附件安全性以及邮件中链接的恶意性，提高员工安全意识"
        },
        {
            "threatFlag": "Scanning",
            "threatFlagCN": "恶意扫描",
            "describe": "黑客利用漏洞扫描器扫描网站，可以发现Web应用存在的漏洞，最终利用相关漏洞攻击网站。",
            "advice": "针对大规模嗅探的IP进行封禁管理"
        },
        {
            "threatFlag": "Malware",
            "threatFlagCN": "恶意软件",
            "describe": "恶意软件是一种秘密植入用户系统借以盗取用户机密信息，破坏用户软件和操作系统或是造成其它危害的一种网络程序。",
            "advice": "1、部署主机防御产品HIDS、AV、EDR等；2、部署网络防御产品NIDS、NGFW等。"
        },
        {
            "threatFlag": "DOS",
            "threatFlagCN": "DOS攻击",
            "describe": "分布式拒绝服务攻击，指处于不同位置的多个攻击者同时向一个或数个目标发动攻击，"
                        "或者一个攻击者控制了位于不同位置的多台机器并利用这些机器对受害者同时实施攻击。",
            "advice": "部署“抗D”或流量清洗等设备可在一定程度上减少被DDoS攻击的损失，检查受保护的网络中潜在沦为“肉鸡”的机器或漏洞，"
                      "避免成为攻击者手中进行DDoS攻击的武器。"
        },
        {
            "threatFlag": "Trojan",
            "threatFlagCN": "远控木马",
            "describe": "特洛伊木马（Trojan Horse）简称木马，在计算机领域中指的是一种后门程序，是黑客用来窃取用户资料，"
                        "甚至是远程控制对方的电子设备的恶意程序。和病毒相似，木马程序有很强的隐秘性，会随著操作系统启动而启动。",
            "advice": "根据客户的具体需求判定是否要进行封禁查杀拦截等处理。"
        },
        {
            "threatFlag": "Mining",
            "threatFlagCN": "挖矿木马",
            "describe": "挖矿木马是一种通过入侵后潜伏驻留在受害者计算机中，利用受害者计算机资源包括但不限于cpu、gpu、硬盘等，"
                        "进行虚拟币挖矿的木马程序。",
            "advice": "先排除是否为内部有员工通过浏览器访问的情况，或者根据自身业务判断是否需要对本次告警进行处理。"
        },
        {
            "threatFlag": "Ransomware",
            "threatFlagCN": "勒索软件",
            "describe": "勒索软件，又称勒索病毒，是一种特殊的恶意软件。一般通过对受害者系统上锁，或系统性地加密受害者硬盘上的文件"
                        "，然后要求受害者缴纳赎金以取回对电脑的控制权，或是提供对受害者被加密文件数据进行解密的服务，来谋取不法利益。",
            "advice": "1、停用系统并还原；2、执行恶意软件防护软件来扫瞄并清除勒索软件的相关文件；"
                      "3、某些勒索软件变种需要一些额外的清除步骤，请务必遵照所有必要的步骤来彻底清除您计算机所感染的特定勒索软件。"
        },
        {
            "threatFlag": "Spyware",
            "threatFlagCN": "间谍软件",
            "describe": "间谍软件是一些专门在用户不知情或未经用户准许的情况下收集用户的个人资料的恶意软件。"
                        "它所收集的资料范围可以很广阔，从该用户平日浏览的网站，到诸如用户名、密码等个人资料。",
            "advice": "1、部署主机防御产品HIDS、AV、EDR等；2、部署网络防御产品NIDS、NGFW等。"
        },
        {
            "threatFlag": "Webshell",
            "threatFlagCN": "WEBSHELL",
            "describe": "webshell就是以asp、php、jsp或者cgi等网页文件形式存在的一种代码执行环境，主要用于网站管理、服务器管理、"
                        "权限管理等操作。使用方法简单，只需上传一个代码文件，通过网址访问，便可进行很多日常操作，极大地方便了使用"
                        "者对网站和服务器的管理。正因如此，也有小部分人将代码修改后当作后门程序使用，以达到控制网站服务器的目的。",
            "advice": "部署WAF防御针对web的攻击"
        },
        {
            "threatFlag": "URL_malware",
            "threatFlagCN": "恶意网站",
            "describe": "包含恶意文件、恶意链接或是伪造网站进行钓鱼，窃取用户密码的网站。",
            "advice": "持续检测，必要是进行针对性的事件响应"
        },
        {
            "threatFlag": "Brute force",
            "threatFlagCN": "爆破",
            "describe": "通过计算机暴力枚举用户名和密码进行远程登录的行为。",
            "advice": "为了您的帐户安全，请尽量设置复杂密码，不要有规律。您容易记忆的密码，同时也很可能被轻易猜出来。"
                      "请参考以下建议： 1、密码长度为6到16个字符；2、密码安全性级别说明： a.当您仅使用英文字母、数字、"
                      "特殊字符中的其中一种来设置密码时系统会提示您密码的安全性级别为“不安全”； b.当您使用英文字母、"
                      "数字、特殊字符的任意两种组合时系统会提示您密码的安全性级别为“普通”； c.当您使用英文字母+数字+特殊"
                      "字符的组合时系统会提示您密码的安全性级别为“安全”。"
        }
    ]

    def __init__(self):
        """初始化参数"""
        self.mongodb = MongoDB("ndr")
        super(AlertList, self).__init__(es_template="alert_list")

    @param_check.check_flask_args(Validator("alert_list"), request)
    def post(self, **kwargs):
        index = RULE_INDEX
        if kwargs["groupKey"] == "uniqueId":
            index += "-agg"
            kwargs["groupKey"] = ""

        es_index = get_es_index(index, int(kwargs["startTime"]), int(kwargs["stopTime"]))
        es_index_list = []
        es_index_not = []
        for i in es_index:
            # 判断是否存在索引
            if self.es_client.indices.exists(i):
                es_index_list.append(i)
            else:
                es_index_not.append(i)
        if not es_index_list:
            return flask_response('index[%s] not exist' % str(es_index_not), True, {})
        option_args = {
            "celeryId": kwargs["celeryId"],
            "killchain": kwargs["killchain"],
            "sid": kwargs["sid"],
            "threatLevel": kwargs["threatLevel"],
            "threatScore": kwargs["threatScore"],
            "threatFlag": kwargs["threatFlag"],
            "mbinfo": kwargs["mbinfo"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        try:
            if not self.es_client.ping():
                flask_log.ndr_log_to_box(NdrLog.Type.RUN, event='Elasticsearch服务连接失败！')
                return flask_response('Elasticsearch服务连接失败！', False, {})
            res = self.es_client.search(index=es_index_list, body=es_template, request_timeout=60)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "规则告警获取失败")
            return flask_response(message, False, {})

        try:
            data = self.data_format(kwargs["groupKey"], res)
            ndr_log_to_box(NdrLog.Type.OPERATE, "规则告警获取成功")
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('reason: %s' % str(e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "规则告警获取失败")
            return flask_response(err_info, False, {})

    def data_format(self, group_key, es_data):
        if not group_key:
            total = es_data["hits"]["total"]["value"]
            alert_data_list = es_data["hits"]["hits"]
        else:
            total = es_data["aggregations"]["_count"]["value"]
            alert_data_list = es_data["aggregations"]["group_id"]["buckets"]
        count = total
        if total > 10000:
            total = 10000
            alert_data_list = alert_data_list[:total]
        data_return = {
            "total": total,
            "real_count": count,
            "alerts": []
        }
        if not group_key or group_key == "uniqueId":
            for alert_info_data in alert_data_list:
                each_alert_dict = es_data_format2(group_key, alert_info_data, self.mongodb)
                for i in self.threat_flag_list:
                    if each_alert_dict["threatFlag"] == i["threatFlag"]:
                        each_alert_dict["describe"] = i["describe"]
                        each_alert_dict["advice"] = i["advice"]
                data_return["alerts"].append(each_alert_dict)
        else:
            for alert_info_data in alert_data_list:
                each_alert_dict = es_data_format(group_key, "rule", alert_info_data)
                data_return["alerts"].append(each_alert_dict)
        return data_return

    def format_es_temp(option_args, es_template):
        """
        :param option_args:
        :return:
        """
        es_must_list = []
        for key, value in option_args.items():
            if value in ["", None]:
                continue
            if key == "mbinfo":
                query = get_mbinfo(option_args["mbinfo"])
                es_template["query"]["bool"]["must"].append(query)
            elif key == "threatScore":
                pass
                # threat_score_start, threat_score_stop = value.split('-')
                # es_template["query"]["bool"]["must"].append(
                #    get_range("threatScore", int(threat_score_start), int(threat_score_stop)))
            elif key == "sid":
                search_args = {field_temp[key]: int(value)}
                args_format = {"term": search_args}
                es_must_list.append(args_format)
            else:
                search_args = {field_temp[key]: value}
                args_format = {"term": search_args}
                es_must_list.append(args_format)
        es_template["query"]["bool"]["must"].extend(es_must_list)
        return es_template
