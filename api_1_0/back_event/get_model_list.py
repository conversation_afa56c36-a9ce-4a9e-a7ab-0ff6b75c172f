#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from flask import request
from api_1_0.utils import flask_log
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.es_data_format import es_data_format2, es_data_format
from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils.utils import flask_response
from utils.es_function import *
from utils import param_check
from utils.database import MongoDB
from config.config import MODEL_INDEX, NdrLog
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)

field_temp = {
    "killchain": "killchain.keyword",
    "modelName": "modelName.keyword",
    "celeryId": "celeryId.keyword",
    "threatLevel": "threatLevel.keyword",
    "confirm": "confirm"
}


def format_es_temp(option_args, es_template):
    """
    :param option_args:
    :return:
    """
    es_must_list = []
    for key, value in option_args.items():
        if value in ["", None]:
            continue
        if key == "mbinfo":
            query = get_mbinfo(option_args["mbinfo"])
            es_template["query"]["bool"]["must"].append(query)
        elif key == "threatScore":
            threat_score_start, threat_score_stop = value.split('-')
            es_template["query"]["bool"]["must"].append(
                get_range("threatScore", int(threat_score_start), int(threat_score_stop)))
        elif key == "threatType":
            query = get_contain(key, value)
            es_template["query"]["bool"]["must"].append(query)
        else:
            search_args = {field_temp[key]: value}
            args_format = {"term": search_args}
            es_must_list.append(args_format)
    es_template["query"]["bool"]["must"].extend(es_must_list)
    return es_template


class ModelListResource(NdrResource):
    """根据条件获取行为模型数据列表"""

    model_name_list = [
        {
            "modelName": "APT32_URL规则",
            "modelCode": "ndr_model_0001",
            "describe": "海莲花url生成算法",
            "advice": "进行事件响应与取证分析，上报监管部门进行封堵。"
        },
        {
            "modelName": "ICMP隐蔽隧道",
            "modelCode": "ndr_model_0002",
            "describe": "攻击者通过将流量封装进ping数据包中，利用ping数据穿透防火墙",
            "advice": "根据客户的具体需求判定是否要进行封禁查杀拦截等处理。"
        },
        {
            "modelName": "主机扫描",
            "modelCode": "ndr_model_0003",
            "describe": "攻击者对目标网段进行大规模扫描，探测存活主机IP",
            "advice": "针对大规模嗅探的IP进行封禁管理"
        },
        {
            "modelName": "密码爆破",
            "modelCode": "ndr_model_0004",
            "describe": "攻击者在短时间内对目标网站进行大量的弱密码登陆",
            "advice": "为了您的帐户安全，请尽量设置复杂密码，不要有规律。您容易记忆的密码，同时也很可能被轻易猜出来。"
                      "请参考以下建议： 1、密码长度为6到16个字符； 2、密码安全性级别说明： a.当您仅使用英文字母、数字、特殊字"
                      "符中的其中一种来设置密码时系统会提示您密码的安全性级别为“不安全”； b.当您使用英文字母、数字、特殊字符的"
                      "任意两种组合时系统会提示您密码的安全性级别为“普通”； c.当您使用英文字母+数字+特殊字符的组合时系统"
                      "会提示您密码的安全性级别为“安全”。"
        },
        {
            "modelName": "弱口令登录",
            "modelCode": "ndr_model_0005",
            "describe": "web应用登录的审计，口令满足【存在于弱口令库、6位以内连续的ascii码、6位以内纯数字】其中任意一个条件",
            "advice": "为了您的帐户安全，请尽量设置复杂密码，不要有规律。您容易记忆的密码，同时也很可能被轻易猜出来。"
                      "请参考以下建议： 1、密码长度为6到16个字符； 2、密码安全性级别说明： a.当您仅使用英文字母、"
                      "数字、特殊字符中的其中一种来设置密码时系统会提示您密码的安全性级别为“不安全”； "
                      "b.当您使用英文字母、数字、特殊字符的任意两种组合时系统会提示您密码的安全性级别为“普通”； "
                      "c.当您使用英文字母+数字+特殊字符的组合时系统会提示您密码的安全性级别为“安全”。"
        },
        {
            "modelName": "针对中国诱导网站检测模型",
            "modelCode": "ndr_model_0006",
            "describe": "检测DNS请求中是否存在重点目标网址从而判断是否为诱导网站。",
            "advice": "部署邮件网关+AV用来检测邮件附件以及链接，部署终端安全防护设备，检测下载的附件安全性以及邮件中链接"
                      "的恶意性，提高员工安全意识"
        },
        {
            "modelName": "WEB命令执行检测",
            "modelCode": "ndr_model_0007",
            "describe": "检测WEB 命令执行漏洞,对URL(GET)&BODY(POST)进行检测判断是否存在常见渗透过程中常见命令",
            "advice": "进行事件响应与取证分析，上报监管部门进行封堵。"
        },
        {
            "modelName": "DGA_domain",
            "modelCode": "ndr_model_0008",
            "describe": "Knownsec NDR Security Group DGA Model",
            "advice": "对访问源进行流控策略。"
        },
        {
            "modelName": "RDP爆破",
            "modelCode": "ndr_model_0009",
            "describe": "针对远程桌面3389端口的爆破攻击行为",
            "advice": "为了您的帐户安全，请尽量设置复杂密码，不要有规律。您容易记忆的密码，同时也很可能被轻易猜出来。请参考以下建议："
                      " 1、密码长度为6到16个字符； 2、密码安全性级别说明： a.当您仅使用英文字母、数字、特殊字符中的其中一种来设置密码时"
                      "系统会提示您密码的安全性级别为“不安全”； b.当您使用英文字母、数字、特殊字符的任意两种组合时系统会提示您密码的安全"
                      "性级别为“普通”； c.当您使用英文字母+数字+特殊字符的组合时系统会提示您密码的安全性级别为“安全”。。"
        },
        {
            "modelName": "服务器路径爆破检测模型",
            "modelCode": "ndr_model_0010",
            "describe": "检测同一个IP请求中是否出现大量的302，404 状态码",
            "advice": "针对大规模路径爆破探测的IP进行封禁管理"
        },
        {
            "modelName": "可疑Beacon检测模型",
            "modelCode": "ndr_model_0011",
            "describe": "Beacon进行方差与均差计算对偏差值过大的Beacon进行告警",
            "advice": "该会话beacon方差与均方差偏差值过大，请检查该会话是否正常"
        },
        {
            "modelName": "SSH爆破",
            "modelCode": "ndr_model_0012",
            "describe": "攻击者在短时间内对目标ip的22端口进行大量的登录爆破",
            "advice": "对被攻击主机进行流量取证，确认是否爆破成功，对攻击ip进行封禁处理"

        },
        {
            "modelName": "冰蝎v4.0通信流量",
            "modelCode": "ndr_model_0013",
            "describe": "Behinder v4.0上线流量包",
            "advice": "对被攻击主机进行上机取证，确认攻击入口，删除webshell"

        },
        {
            "modelName": "DNS隧道检测",
            "modelCode": "ndr_model_0014",
            "describe": "利用DNS请求和响应来承载经过编码或加密的数据内容，达到控制DNS服务器的目的",
            "advice": "对访问源进行流控策略。"
        },
        {
            "modelName": "端口扫描",
            "modelCode": "ndr_model_0015",
            "describe": "端口扫描是攻击者利用tcp或udp协议，通过构造数据包对目标主机的端口开启情况进行探测的行为",
            "advice": "针对大规模端口扫描的IP进行封禁管理。"
        },
        {
            "modelName": "Ddos_Flood_攻击",
            "modelCode": "ndr_model_0016",
            "describe": "DDOS本是利用合理的请求造成资源过载，导致服务不可用，常见的DDOS攻击有SYN flood、UDP flood、ICMP flood等。",
            "advice": "部署“抗D”或流量清洗等设备可在一定程度上减少被DDoS攻击的损失，检查受保护的网络中潜在沦为“肉鸡”的机器或漏洞，"
                      "避免成为攻击者手中进行DDoS攻击的武器。"
        }
    ]

    def __init__(self):
        super(ModelListResource, self).__init__(es_template="model_list")
        self.mongodb = MongoDB("ndr")

    @param_check.check_flask_args(Validator('model_list'), request)
    def post(self, **kwargs):
        index = MODEL_INDEX
        if kwargs["groupKey"] == "uniqueId":
            index += "-agg"
            kwargs["groupKey"] = ""

        es_index = get_es_index(index, int(kwargs["startTime"]), int(kwargs["stopTime"]))
        es_index_list = []
        es_index_not = []
        for i in es_index:
            # 判断是否存在索引
            if self.es_client.indices.exists(i):
                es_index_list.append(i)
            else:
                es_index_not.append(i)
        if not es_index_list:
            return flask_response('index[%s] not exist' % str(es_index_not), True, {})

        option_args = {
            "celeryId": kwargs["celeryId"],
            "modelName": kwargs["modelName"],
            "threatLevel": kwargs["threatLevel"],
            "killchain": kwargs["killchain"],
            "threatScore": kwargs["threatScore"],
            "mbinfo": kwargs["mbinfo"],
            "threatType": kwargs["threatType"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        try:
            if not self.es_client.ping():
                flask_log.ndr_log_to_box(NdrLog.Type.RUN, event='Elasticsearch服务连接失败！')
                return flask_response('Elasticsearch服务连接失败！', False, {})
            res = self.es_client.search(index=es_index_list, body=es_template, request_timeout=60)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(es_template, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "模型告警获取失败")
            return flask_response(message, False, {})
        try:
            data = self.data_format(kwargs["groupKey"], res)
            ndr_log_to_box(NdrLog.Type.OPERATE, "模型告警获取成功")
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "模型告警获取失败")
            return flask_response(err_info, False, {})

    def data_format(self, group_key, es_data):
        if not group_key:
            total = es_data["hits"]["total"]["value"]
            model_data_list = es_data["hits"]["hits"]
        else:
            total = es_data["aggregations"]["_count"]["value"]
            model_data_list = es_data["aggregations"]["group_id"]["buckets"]
        count = total
        if total > 10000:
            total = 10000
            model_data_list = model_data_list[:total]
        data_return = {
            "total": total,
            "real_count": count,
            "alerts": []
        }
        if not group_key or group_key == "uniqueId":
            for model_info_data in model_data_list:
                each_model_dict = es_data_format2(group_key, model_info_data, self.mongodb)
                for i in self.model_name_list:
                    if each_model_dict["modelCode"] == i["modelCode"]:
                        each_model_dict["describe"] = i["describe"]
                        each_model_dict["advice"] = i["advice"]
                data_return["alerts"].append(each_model_dict)
        else:
            for model_info_data in model_data_list:
                each_model_dict = es_data_format(group_key, "model", model_info_data)
                data_return["alerts"].append(each_model_dict)
        return data_return
