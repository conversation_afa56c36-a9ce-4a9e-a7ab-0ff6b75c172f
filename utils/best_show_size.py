#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

unit_list = ["B", "K", "M", "G", "T", "P", "E"]


def size_format(size, unit="K"):
    '''
    1-1024kb 展示kb
    1-1024Mb 展示Mb
    1-1024Gb 展示Gb
    1-1024Tb 展示Tb
    保留一位小数
    '''
    # 为跟网卡显示一致，除以 1000
    integral_digits = size // 1000
    if integral_digits:
        show_size = size / 1000
        index = unit_list.index(unit) + 1
        if index > len(unit_list) - 1:
            return str(round(show_size, 1)) + unit
        next_unit = unit_list[index]
        return size_format(show_size, next_unit)
    else:
        return str(round(size, 1)) + unit
