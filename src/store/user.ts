import { stringify } from 'qs';
import { getPageQuery } from '@/utils/utils';
import { logout, queryCurrent } from '@/services/user';
import { Rekv } from './rekv';

export interface UserInfo {
  id?: string;
  account?: string;
  avatar?: string;
  name?: string;
  title?: string;
  group?: string;
  signature?: string;
  modules?: string[];
  email?: string;
  mobile?: string;
  geographic?: any;
  tags?: {
    key: string;
    label: string;
  }[];
  googleAuth?: boolean;
  description?: string;
}

interface UserStore {
  user: UserInfo;
}

const initState: UserStore = {
  user: {},
};

/**
 * 用户状态存储
 */
const userStore = new Rekv({
  initState,
  effects: {
    /**
     * 退出登录
     */
    async logout() {
      const { redirect } = getPageQuery();
      // redirect
      if (window.location.pathname !== '/login' && !redirect) {
        await logout();
        const search = stringify({
          redirect: window.location.pathname,
        });
        // 这里强制刷新为了防止注销后使用不同权限的账号登录
        // 导致左边侧边栏的数据还是上一个账号的数据
        // eslint-disable-next-line require-atomic-updates
        window.location.href = `/login?${search}`;
      }
    },
    /**
     * 获取当前登录用户信息
     */
    async userInfo(store) {
      const { data } = await queryCurrent();

      store.setState({
        user: data,
      });
    },
  },
});

export default userStore;
