# -*- coding: utf-8 -*-
# @Time    : 2019-05-06 13:03
# <AUTHOR> hachi
# @File    : database.py
# @Software: PyCharm
import elasticsearch
import os
import sys
from config.config import ElasticSearchConfig
from pymongo import MongoClient
from config.config import MongoDBConfig
from utils.logger import get_ndr_logger

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


LOG = get_ndr_logger("mongo_log", __file__)


class BaseMongoDB(object):
    """
        celery使用的mongodb类，不使用单例模式；
        因为celery是多进程worker使用，pymongo实例不能运行在父子进程环境；每个进程必须使用独立的实例。
    """

    def __init__(self, database):
        self.conn = MongoClient(
            host=MongoDBConfig.MONGO_HOST,
            port=MongoDBConfig.MONGO_PORT,
            username=MongoDBConfig.MONGO_USERNAME,
            password=MongoDBConfig.MONGO_PASSWORD,
            authSource=MongoDBConfig.MONGO_AuthSource
        )
        self.db = self.conn[database]

    def get_state(self):

        # return self.conn is not None and self.db is not None

        try:
            client = self.conn
            # 利用server_info()判断mongodb状态
            client.server_info()
        except Exception as e:
            LOG.error(e)
            return False

        else:
            return True

    def insert_one(self, collection, data):
        if self.get_state():
            ret = self.db[collection].insert_one(data)
            return ret.inserted_id
        else:
            return ""

    def insert_many(self, collection, data):
        if self.get_state():
            ret = self.db[collection].insert_many(data)
            return ret.inserted_ids
        else:
            return ""

    def update(self, collection, filter, data, insert=False):
        # data format:
        if self.get_state():
            return self.db[collection].update_one(filter, {"$set": data}, upsert=insert)
        return 0

    def update_one(self, collection, filter, data, insert=False):
        # data format:
        if self.get_state():
            return self.db[collection].update_one(filter, data, upsert=insert)
        return 0

    def update_many(self, collection, filter, data):
        # data format:
        if self.get_state():
            return self.db[collection].update_many(filter, {"$set": data})
        return 0

    def aggs(self, collection, pipeline):
        if self.get_state():
            return list(self.db[collection].aggregate(pipeline))
        return None

    def find(self, col, condition, column=None):
        if self.get_state():
            if column is None:
                return self.db[col].find(condition)
            else:
                return self.db[col].find(condition, column)
        else:
            return None

    def find_one(self, col, condition, column=None):
        if self.get_state():
            if column is None:
                return self.db[col].find_one(condition)
            else:
                return self.db[col].find_one(condition, column)
        else:
            return None

    def delete(self, col, condition):
        if self.get_state():
            return self.db[col].delete_many(filter=condition).deleted_count
        return 0

    def count(self, col, condition):
        if self.get_state():
            return self.db[col].count_documents(condition)
        return 0

    def drop(self, col):
        if self.get_state():
            return self.db[col].drop()
        return 0

    def close(self):
        if self.get_state():
            self.conn.close()
            return True
        return False

    def list_collection(self):
        if self.get_state():
            return self.db.list_collection_names()
        return []

    def bulk_write(self, col, data):
        if self.get_state():
            return self.db[col].bulk_write(data)
        return 0


class MongoDB(BaseMongoDB):
    """一个数据集合只创建一个数据库连接，单例"""
    __instance = list()
    __first_init = list()
    __cls_pool = dict()
    __database_pool = dict()

    def __new__(cls, args):
        if args not in MongoDB.__instance:
            init_cls = object.__new__(cls)
            MongoDB.__instance.append(args)
            MongoDB.__cls_pool[args] = init_cls
        return MongoDB.__cls_pool[args]

    def __init__(self, database):
        #  覆盖__init__函数，不再使用父类__init__函数，也不再调用父类__init__函数
        if database not in self.__first_init:
            self.conn = MongoClient(
                host=MongoDBConfig.MONGO_HOST,
                port=MongoDBConfig.MONGO_PORT,
                username=MongoDBConfig.MONGO_USERNAME,
                password=MongoDBConfig.MONGO_PASSWORD,
                authSource=MongoDBConfig.MONGO_AuthSource
            )
            MongoDB.__first_init.append(database)
            db = self.conn[database]
            MongoDB.__database_pool[database] = db
        self.db = MongoDB.__database_pool[database]


def get_es_client():
    """
    创建 ES 连接
    :return:
    """
    es_client = elasticsearch.Elasticsearch(
        [dict(
            host=ElasticSearchConfig.ElasticSearch_HOST,
            port=ElasticSearchConfig.ElasticSearch_PORT
        )],
        timeout=ElasticSearchConfig.ES_CONN_TIMEOUT,
        http_auth=(
            ElasticSearchConfig.ElasticSearch_USER,
            ElasticSearchConfig.ElasticSearch_PASS
        )
    )

    return es_client
