import React, { useState, useEffect } from 'react';
import style from './index.less';

const Index = (Props: {
  show?: any;
  children?: any;
  getAxios?: any;
  line?: any;
  end?: any;
  item?: any;
}) => {
  const [show, setshow] = useState(Props.show || false);
  useEffect(() => {
    document
      .querySelector('#surveyKillChainEventListContent')
      .addEventListener('mousedown', hide);
    return () => {
      if (show) {
        document
          .querySelector('#surveyKillChainEventListContent')
          .removeEventListener('mousedown', hide);
      }
    };
  }, [show]);
  const Clickshow = () => {
    setshow(true);
    document
      .querySelector('#surveyKillChainEventListContent')
      .addEventListener('mousedown', hide);
  };
  const hide = (e: { target: { className: string | string[] } }) => {
    if (!e.target.className.includes('prevent-hide')) {
      const { getAxios, line, end, item } = Props;
      if(getAxios) {
        getAxios(line, end, false, item);
      }
      setshow(false);
      document
        .querySelector('#surveyKillChainEventListContent')
        .removeEventListener('mousedown', hide);
    }
  };
  const renderContent = () => {
    if (show) {
      return (
        <div className={style.visibleBox}>
          {React.cloneElement(Props.children, { show })}
        </div>
      );
    }
    return (
      <div className={style.hiddenBox}>
        {React.cloneElement(Props.children)}
      </div>
    );
  };
  return (
    <div>
      <section onClick={Clickshow}>{renderContent()}</section>
    </div>
  );
};
export default Index;
