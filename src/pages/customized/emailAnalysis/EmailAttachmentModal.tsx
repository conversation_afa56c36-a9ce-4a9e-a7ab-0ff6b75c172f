import React from 'react';
import { Modal } from 'antd';
import EmailDetailAttachmentList from './EmailDetailAttachmentList';
import { useEmailDetailData } from './hooks';

import type { Props } from './EmailDetailContent'


function EmailAttachmentModal({ data, open, onCancel }: Props & {
  open: boolean;
  onCancel: () => void
}): JSX.Element {

  return (
    <Modal
      open={open}
      width={1000}
      title="附件列表"
      onCancel={onCancel}
      destroyOnClose
      footer={null}
    >
      {
        !!data.msg_id && <EmailAttachmentModalList data={data} />
      }
    </ Modal>
  )
}

export default EmailAttachmentModal;

function EmailAttachmentModalList({ data }: Props) {
  const { data: detailData, loading } = useEmailDetailData(data)

  return <EmailDetailAttachmentList
    loading={loading}
    data={detailData?.attachment || []}
    timeRange={{ start_time: data.start_time, end_time: data.end_time }}
  />
}