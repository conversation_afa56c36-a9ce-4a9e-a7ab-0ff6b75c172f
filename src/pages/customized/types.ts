export type TablePagination = {
  page: number;
  pageSize: number;
  total: number;
}

export type TimeRange = {
  start_time: number;
  end_time: number;
}

export type ApiResponse<T> = {
  data: T,
  flag: boolean;
  message: string;
}

export type EmailListParams = {
  start_time: number;
  end_time: number;
  rsv_org?: string;
  severity?: number[]
  msg_id?: string;
  page?: number;
  page_size?: number;
}

export type EmailDetailParams = Required<Pick<EmailListParams, 'msg_id'>> &
  Pick<EmailListParams, 'start_time' | 'end_time' | 'page' | 'page_size'>


export type EmailFileDetailParams = Pick<EmailListParams, 'start_time' | 'end_time'> & {
  uuid: string;
}

export type EmailListItem = {
  msg_id: string;
  timestamp: number;
  from: string;
  to: string[];
  cc: string[];
  bcc: string[];
  subject: string;
  threat: string[];
  fetch: boolean;
  severity: number
}

export type EmailAttachment = {
  category: string;
  threatName: string;
  fileid: string;
  filename: string;
  filesize: string;
  filetype: string;
  fuid: string;
  threat: string[];
  timestamp: number;
}

export type EmailDetail = EmailListItem & {
  email_body: string;
  attachment: EmailAttachment[]
}

export type EmailAttachmentReportDetail = FileLog

export type EmailSendRecvInfoParams = Required<Pick<EmailListParams, 'msg_id'>> &
  Pick<EmailListParams, 'start_time' | 'end_time'>

export type EmailThreatCommon = {
  from: string;
  to: string[];
  src_ip: string;
  date: string;
}

export type EmailSendRecvInfo = {
  pop3: (EmailThreatCommon & {
    pop3_ip: string;
  })[];
  totalTo: string[];
  smtp: (Omit<EmailThreatCommon, 'to'> & {
    smtp_ip: string;
    received: string;
  })[];
  imap: (EmailThreatCommon & {
    imap_ip: string;
    received: string;
  })[]
}

export type EmailAttachmentListParams = EmailListParams & {
  filename?: string;
  md5?: string;
}

export type EmailAttachmentItem = EmailAttachment & {
  msg_id: string;
  md5: string;
  to: string[];
  uuid: string;
  severity: number;
  subject: string;
}

export type EmailTargetedAttackParams = Omit<EmailListParams, 'severity' | 'msg_id' | 'sort'>

export type EmailTargetedAttackListItem = Omit<EmailListItem, 'fetch' | 'id' | 'threat'> &
  Pick<EmailAttachmentItem, 'filename' | 'md5' | 'tags' | 'family' | 'msg_id'> & Pick<EmailTargetedAttackParams, 'rsv_org'>

// file detail type

export type GeoLocation = {
  areacode: string;
  latitude: string;
  longitude: string;
  city_name: string;
  country_name: string;
};

export type Mail = {
  date: string;
  cc: string;
  bcc: string;
  file_names: string;
  subject: string;
  received: string;
  body: string;
  app_proto: string;
  return_path: string;
  reply_to: string;
  rsv_org: string;
  net_ext_flag: string;
  from: string;
  to: string;
  msg_id: string;
  user_agent: string;
  file_md5s: string;
};

export type SrcIpMb = {
  organisation: string;
  industry: string;
};

export type FileInfo = {
  file_path: string;
  filetype: string;
  delivery: string;
  sha256: string;
  create_time: number;
  gene: string;
  filesize: number;
  file_origin: string;
  ssdeep: string;
  platform: string;
  intelligence: string;
  tags: string[];
  filename: string;
  multiav: string;
  fuid: string;
  crc32: string;
  md5: string;
};

export type ThreatLevel = {
  knownsec: string;
  clamav: string;
};

export type FileLogSource = {
  kill_chain_sort: number;
  victim_location: GeoLocation;
  mail: Mail;
  hostip: string;
  rid: number;
  uuid: string;
  'virus_desc.clamav': string;
  is_read: boolean;
  is_collect: boolean;
  hostname: string;
  dst_ip_location: GeoLocation;
  classtype_cn: string;
  src_ip_flags: string[];
  report_path: string;
  threat_ioc: string;
  threat_suggestion: string;
  is_process: boolean;
  attack_status: number;
  threat_level: ThreatLevel;
  pcap_filename: string;
  'virus_name.clamav': string;
  dst_ip_domain: string;
  proto: string;
  src_ip_mb: SrcIpMb;
  topic: string;
  conn_id: string;
  'virus_type.clamav': string;
  pcap_warning: string;
  uniqueId: string;
  sub_category: string;
  attacker_location: GeoLocation;
  reliability: number;
  threat_desc: string;
  interface: number;
  'virus_type.knownsec': string;
  dst_ip: string;
  src_ip: string;
  cve: string;
  file: FileInfo;
  victim: string;
  visit_direction: number;
  'virus_desc.knownsec': string;
  timestamp: number;
  severity: number;
  src_mac: string;
  src_ip_location: GeoLocation;
  src_ip_domain: string;
  category_cn: string;
  attacker_flags: string[];
  celeryId: string;
  dst_ip_flags: string[];
  attacker: string;
  policy_name: string;
  'virus_name.knownsec': string;
  app_proto: string;
  dst_mac: string;
  src_port: number;
  engine_type: string;
  threat_name: string;
  net_ext_flag: string;
  victim_flags: string[];
  dst_port: number;
  task_type: string;
  family: string;
  attack_tool: string;
  taskId: string;
  dst_ip_mb: SrcIpMb;
  ts: number;
};

export type FileLog = {
  _index: string;
  _type: string;
  _id: string;
  _score: number;
  _source: FileLogSource;
};

// report type

export type ReportDetail = {
  info: Info
  metadata: Metadata
  network: Network<string>
  screenshots: ScreenShots[]
  signatures: Signatures[]
  static: Static;
  strings: string[];
  target: Target

}

type GitInfo = {
  head: string;
  fetch_head: string;
}

type MachineInfo = {
  status: string;
  name: string;
  label: string;
  manager: string;
  started_on: string;
  shutdown_on: string;
}

type Info = {
  added: number;
  started: number;
  duration: number;
  ended: number;
  owner: string;
  score: number;
  id: number;
  category: string;
  git: GitInfo;
  monitor: string;
  package: string;
  route: string;
  custom: string;
  machine: MachineInfo;
  platform: string;
  version: string;
  options: string;
}

type PcapOutput = {
  pcap: {
    basename: string;
    sha256: string;
    dirname: string;
  };
}

type Metadata = {
  output: PcapOutput;
}

type Network<T> = {
  tls: T[];
  udp: T[];
  dns_servers: T[];
  http: T[];
  icmp: T[];
  smtp: T[];
  tcp: T[];
  smtp_ex: T[];
  mitm: T[];
  hosts: T[];
  pcap_sha256: string;
  dns: T[];
  http_ex: T[];
  domains: T[];
  dead_hosts: T[];
  irc: T[];
  https_ex: T[];
};

type ScreenShots = {
  path: string;
  ocr: string;
  img_stream: string;
};

type TTP = {
  short: string;
  long: string;
};

type Mark = {
  category: string;
  ioc: string;
  type: string;
  description: string | null;
};

type Signatures = {
  families: string[];
  description: string;
  severity: number;
  ttp: Record<string, TTP>;
  label: string;
  markcount: number;
  references: string[];
  marks: Mark[];
  description_cn: string;
  labelCN: string;
  name: string;
};

type PESection = {
  size_of_data: string;
  virtual_address: string;
  entropy: number;
  name: string;
  virtual_size: string;
};

type Static = {
  pdb_path: string | null;
  pe_imports: string[];
  peid_signatures: string | null;
  keys: string[];
  signature: string[];
  pe_timestamp: string;
  pe_exports: string[];
  imported_dll_count: number;
  pe_imphash: string;
  pe_resources: string[];
  pe_versioninfo: string[];
  pe_sections: PESection[];
};


type Target = {
  category: string;
  target: TargetFile;
}

type TargetFile = {
  yara: YaraRule[];
  sha1: string;
  name: string;
  type: string;
  sha256: string;
  urls: string[];
  crc32: string;
  ssdeep: string | null;
  size: number;
  sha512: string;
  md5: string;
};

type YaraRule = {
  meta: {
    description: string;
    author: string;
  };
  name: string;
  offsets: {
    [key: string]: Array<[number, number]>;
  };
  strings: string[];
};
