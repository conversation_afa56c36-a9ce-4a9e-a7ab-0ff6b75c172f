#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import datetime
import ipaddress
import json
import os
import shutil
import sqlite3
import time
import zipfile
import xlrd
import requests
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.es_template.get_es_template import ES_Template
from utils.json_format import J<PERSON>NEncoder
from utils.database import MongoDB, get_es_client
from utils.logger import get_ndr_logger
from utils.utils import flask_response, supervisorctl_host_service
from utils.cluster import *
from flask_restful import Resource
from utils.param_check import Validator
from config.config import NdrLog, ClickHouseConfig, MbCfg, KnowledgeNDR
from utils import param_check
from flask import request
from clickhouse_driver import Client as CH_client

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class AssetsResource(Resource):
    def __init__(self):
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)
        self.mongodb = MongoDB("ndr")
        self.es_client = get_es_client()
        self.sqlite = sqlite3.connect(MbCfg.DbPath)
        self.cursor = self.sqlite.cursor()

    def __del__(self):
        self.ch_client.disconnect()
        self.sqlite.close()

    def get_val_from_database(self, sql):
        ret_list = list()
        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except:
            ndr_log_to_box(NdrLog.Type.OPERATE, "ClickHouse数据库连接或执行失败！")
            return ret_list
        if raw is None or not raw[0]:
            return ret_list
        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]
        for i in range(len(raw_val)):
            data = {}
            for idx, item in enumerate(raw_val[i]):
                if isinstance(item, bytes):
                    item = str(item)
                data[raw_key[idx][0]] = item
            ret_list.append(data)
        return ret_list

    def insert_mb_assets_to_sqlite(self, data):
        sql = "INSERT INTO organization VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')" % (
            data['ip_addr'], data["domain"], data["name"], data["type"], data["organisation"],
            data["industry"], data["country"], data["location"], data["components"], data["other"])
        self.cursor.execute(sql)
        self.sqlite.commit()
        cluster_sync_file(MbCfg.DbPath)


class AssetsList(AssetsResource):
    """
    资产列表
    """

    def __init__(self):
        super(AssetsList, self).__init__()

    def __del__(self):
        super(AssetsList, self).__del__()

    # 查询条件解析
    @staticmethod
    def query_condition(ip, name, is_outer, assets_num, is_importent):
        condition = {}
        if ip:
            condition["ip_addr"] = ip
        if name:
            condition["name"] = name
        if is_outer:
            condition['is_outer'] = True if is_outer == 'true' else False
        if assets_num:
            condition["assets_num"] = assets_num
        if is_importent:
            condition['is_importent'] = True if is_importent == 'true' else False

        return condition

    @param_check.check_flask_args(Validator("assets_list_info_schema"), request)
    def get(self, **kwargs):
        """
        获取资产列表
        :return:
        """
        ip = kwargs["args"]["ip_addr"]
        name = kwargs["args"]["name"]
        is_outer = kwargs['is_outer']
        assets_num = kwargs["args"]["assets_num"]
        page = int(kwargs["args"]["page"])
        page_size = int(kwargs["args"]["pageSize"])
        is_importent = kwargs['is_importent']
        try:
            condition = self.query_condition(ip, name, is_outer, assets_num, is_importent)
            total = self.mongodb.find("assets_info", condition).count()
            rst = self.mongodb.find("assets_info", condition).sort([("create_time", -1)]).limit(page_size).skip(
                (page - 1) * page_size)
            cols = []
            for asset in rst:
                application_stream = []
                send_data = []
                # 应用流量
                application = asset["app_traffic"]
                application_sort = sorted(application.items(), key=lambda x: x[1], reverse=True)
                for i in application_sort[0:10]:
                    application_stream.append({"name": i[0].lower(), "value": i[1]})
                asset["app_traffic"] = application_stream
                # 发送流量
                send = asset["send_traffic"]
                send_sort = sorted(send.items(), key=lambda x: x[1], reverse=True)
                for i in send_sort[0:5]:
                    send_data.append({"name": i[0].replace("_", "."), "value": i[1]})
                asset["send_traffic"] = send_data
                # 周流量
                week_stream_list = asset["latest_week_stream"]
                latest_week_stream = []
                if week_stream_list:
                    week_stream_sort = sorted(week_stream_list, key=lambda x: x['total_bytes'], reverse=True)
                    # 取出流量最大值
                    big_total_byte = week_stream_sort[0]['total_bytes']
                    # 通过最大值换算单位
                    if big_total_byte > 1024 * 1024 * 1024:
                        divisor = 1024 * 1024 * 1024
                        unit = "GB"
                    elif big_total_byte > 1024 * 1024:
                        divisor = 1024 * 1024
                        unit = "MB"
                    elif big_total_byte > 1024:
                        divisor = 1024
                        unit = "KB"
                    else:
                        divisor = 1
                        unit = "B"
                    for week_stream in week_stream_list:
                        latest_week_stream.append(
                            {"time": week_stream["time_stamp"] * 1000,
                             "total_bytes": round(week_stream["total_bytes"] / divisor, 2),
                             "unit": unit})
                asset["latest_week_stream"] = latest_week_stream
                asset = JSONEncoder().encode(asset)
                cols.append(json.loads(asset))
            data = {"total": total,
                    "cols": cols,
                    "page": page,
                    "pageSize": page_size}
            ndr_log_to_box(NdrLog.Type.OPERATE, "资产获取成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error("assest get failed reason: %s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "资产获取失败")
            return flask_response('', False, {})

    @param_check.check_flask_args(Validator("assets_list_add_schema"), request)
    def post(self, **kwargs):
        ip = kwargs["args"]["ip_addr"]
        labels = []
        if kwargs["args"]["labels"]:
            labels = kwargs["args"]["labels"].split(",")
        rst = self.mongodb.find_one("assets_info", {"ip_addr": ip})
        if rst:
            return flask_response('ip %s already exists!' % ip, False, {})
        try:
            body = {
                "name": kwargs["args"]["name"],  # 资产名称
                "ip_addr": ip,  # IP地址
                "is_outer": False if ipaddress.ip_address(ip).is_private else True,  # 是否外网
                "mac_addr": kwargs["args"]["mac_addr"],  # MAC地址
                "enable_port": kwargs["enable_port"],  # 启用的端口
                "type": kwargs["args"]["type"],  # 资产类型
                "assets_num": kwargs["args"]["assets_num"],  # 资产编号
                "is_sercret": kwargs["args"]["is_sercret"],  # 是否涉密
                "is_importent": kwargs["args"]["is_importent"],  # 是否重点资产
                "location": kwargs["args"]["location"],  # 所在位置
                "country": kwargs["args"]["country"],  # 所在国家
                "country_code": kwargs["args"]["country_code"],  # 国家代码
                "department": kwargs["args"]["department"],  # 所属部门
                "industry": kwargs["args"]["industry"],  # 所属行业
                "organisation": kwargs["args"]["organisation"],  # 所属组织
                "components": kwargs["args"]["components"],  # 组件列表
                "domain": kwargs["args"]["domain"],  # 域名
                "other": kwargs["args"]["other"],  # 其他
                "person": kwargs["args"]["person"],  # 责任人
                "os_info": kwargs["args"]["os_info"],  # 操作系统信息
                "labels": labels,  # 标签列表
                "send_count": 0,  # 发起的链接数
                "accept_count": 0,  # 接收的链接数
                "app_total_traffic": 0,  # 交互的流量总量
                "app_traffic": {},  # 应用流量Top10
                "send_traffic": {},  # 发送流量Top5
                "access_sample": {},  # 访问抽样
                "popular": 1,  # 每次在告警中发现加5，资产画像查询的时候就加1
                "latest_week_stream": {},  # 周流量
                "create_time": int(time.time() * 1000),  # 资产信息创建时间
                "update_time": int(time.time() * 1000)  # 资产信息更新时间
            }
            self.mongodb.insert_one("assets_info", body)

            if kwargs["is_importent"]:
                self.insert_mb_assets_to_sqlite(kwargs)

        except Exception as error:
            LOG.error("update failed.Reason: %s." % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "资产[%s]添加失败" % ip)
            return flask_response('ip %s insert failed!' % ip, False, {})
        ndr_log_to_box(NdrLog.Type.OPERATE, "资产[%s]添加成功" % ip)
        return flask_response('ip %s insert successful' % ip, True, {})

    @param_check.check_flask_args(Validator("assets_list_delete_schema"), request)
    def delete(self, **kwargs):
        ip_list = kwargs["args"]["ip_list"]
        if not ip_list:
            return flask_response('delete ip list must not empty!', False, {})
        try:
            # 删除sqlite中的重点资产
            data = self.mongodb.find("assets_info", {"ip_addr": {"$in": ip_list}, "is_importent": True}, {"ip_addr": 1})
            if data:
                sqlite_ip_list = ["'%s'" % item['ip_addr'] for item in data]
                self.cursor.execute("DELETE from organization where ip in (%s)" % ",".join(sqlite_ip_list))
                self.sqlite.commit()

            self.mongodb.delete("assets_info", {"ip_addr": {'$in': ip_list}})
            ndr_log_to_box(NdrLog.Type.OPERATE, "资产删除成功")
            return flask_response('assets delete successful', True, {})
        except Exception as e:
            LOG.error("assets delete failed reason: %s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "资产删除失败")
            return flask_response('assets delete failed', False, {})

    @param_check.check_flask_args(Validator("assets_list_add_schema"), request)
    def put(self, **kwargs):
        ip = kwargs["ip_addr"]
        labels = []
        if kwargs["args"]["labels"]:
            labels = kwargs["args"]["labels"].split(",")
        option_args = {
            "name": kwargs["name"],  # 资产名称
            "mac_addr": kwargs["mac_addr"],  # MAC地址
            "type": kwargs["type"],  # 资产类型
            "enable_port": kwargs["enable_port"],  # 启用的端口
            "assets_num": kwargs["assets_num"],  # 资产编号
            "is_sercret": kwargs["is_sercret"],  # 是否涉密
            "is_importent": kwargs["args"]["is_importent"],  # 是否重点资产
            "location": kwargs["location"],  # 所在位置
            "country": kwargs["country"],  # 所在国家
            "country_code": kwargs["country_code"],  # 国家代码
            "department": kwargs["department"],  # 所属部门
            "industry": kwargs["industry"],  # 所属行业
            "organisation": kwargs["organisation"],  # 所属组织
            "components": kwargs["components"],  # 组件列表
            "domain": kwargs["domain"],  # 域名
            "other": kwargs["other"],  # 其他
            "person": kwargs["person"],  # 责任人
            "os_info": kwargs["os_info"],  # 操作系统信息
            "labels": labels,  # 标签列表
            "update_time": int(time.time() * 1000)  # 资产信息更新时间
        }
        try:
            rst = self.mongodb.find_one("assets_info", {"ip_addr": ip}, {'is_importent': 1})
            if rst:
                # 如果重点资产变化
                if kwargs["is_importent"] != rst['is_importent']:
                    if kwargs['is_importent']:
                        self.insert_mb_assets_to_sqlite(kwargs)
                    else:
                        self.cursor.execute("DELETE from organization where ip in ('%s');" % ip)
                        self.sqlite.commit()
                # 如果重点资产没有改变但是却是重点资产，需要更新
                elif rst['is_importent']:
                    self.cursor.execute("DELETE from organization where ip in ('%s');" % ip)
                    self.sqlite.commit()
                    self.insert_mb_assets_to_sqlite(kwargs)
            else:
                return flask_response("ip assets is not exists!", False, {})

            self.mongodb.update_many("assets_info", {"ip_addr": ip}, option_args)
            ndr_log_to_box(NdrLog.Type.OPERATE, "资产[%s]编辑成功" % ip)
            return flask_response("assets update successful", True, {})
        except Exception as e:
            LOG.error("assests [%s] update failed reason: %s" % (ip, e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "资产[%s]编辑失败" % ip)
            return flask_response("assets update successful", False, {})


class AssetsThreatEvent(AssetsResource):
    """
    资产画像威胁事件
    """

    def __init__(self):
        super(AssetsThreatEvent, self).__init__()

    def __del__(self):
        super(AssetsThreatEvent, self).__del__()

    @param_check.check_flask_args(Validator("assets_image_schema"), request)
    def get(self, **kwargs):
        ip = kwargs["args"]["ip_addr"]
        try:
            # 访问资产画像热度值+1
            rst = self.mongodb.find_one("assets_info", {"ip_addr": ip})
            if rst:
                popular = rst["popular"] + 1
                self.mongodb.update("assets_info", {"ip_addr": ip}, {"popular": popular})
            es_template_alter = ES_Template().read_template('get_assets_threat_event_alter')
            es_template_model = ES_Template().read_template('get_assets_threat_event_model')
            es_template_ioc = ES_Template().read_template('get_assets_threat_event_ioc')
            es_template_alter = self.format_es_temp(es_template_alter, ip)
            es_template_model = self.format_es_temp(es_template_model, ip)
            es_template_ioc = self.format_es_temp(es_template_ioc, ip)
            alter_data = self.es_client.search(index="rule-eve", body=es_template_alter)
            model_data = self.es_client.search(index="model-eve", body=es_template_model)
            ioc_data = self.es_client.search(index="ioc-eve", body=es_template_ioc)
            return_data = {}
            return_data = self.data_format(alter_data, ip, "alter", return_data)
            return_data = self.data_format(model_data, ip, "model", return_data)
            return_data = self.data_format(ioc_data, ip, "ioc", return_data)
        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            return flask_response(err_info, False, {})
        return flask_response('', True, return_data)

    def format_es_temp(self, es_template, ip):
        es_template["query"]["bool"]["should"][0]["term"]["flow.src_ip.keyword"] = ip
        es_template["query"]["bool"]["should"][1]["term"]["flow.dst_ip.keyword"] = ip
        return es_template

    def data_format(self, rst_data, ip, data_type, return_data):
        rst_list = rst_data["aggregations"]["group_id"]["buckets"]
        for rst in rst_list:
            data = {}
            data["count"] = rst["doc_ids"]["hits"]["total"]['value']
            if data_type == "alter":
                data["name"] = rst["doc_ids"]["hits"]["hits"][0]["_source"]["vulName"]
                data["threat_event"] = rst["doc_ids"]["hits"]["hits"][0]["_source"]["lockheedKillchainCN"]
            elif data_type == "model":
                data["name"] = rst["doc_ids"]["hits"]["hits"][0]["_source"]["info"]
                data["threat_event"] = rst["doc_ids"]["hits"]["hits"][0]["_source"]["modelName"]
            else:
                data["name"] = "ioc检测命中 " + rst["doc_ids"]["hits"]["hits"][0]["_source"]["ioc"]
                data["threat_event"] = rst["doc_ids"]["hits"]["hits"][0]["_source"]["aptOrganization"]
            data["threat_level"] = rst["doc_ids"]["hits"]["hits"][0]["_source"]["threatLevel"]
            src_ip = rst["doc_ids"]["hits"]["hits"][0]["_source"]["flow"]["src_ip"]
            dst_ip = rst["doc_ids"]["hits"]["hits"][0]["_source"]["flow"]["dst_ip"]
            rst_ip = dst_ip if ip == src_ip else src_ip
            if rst_ip in return_data.keys():
                return_data[rst_ip].append(data)
            else:
                return_data[rst_ip] = []
                return_data[rst_ip].append(data)
        return return_data


class AssetsOperSystem(AssetsResource):
    """
    操作系统
    """

    def __init__(self):
        super(AssetsOperSystem, self).__init__()

    def __del__(self):
        super(AssetsOperSystem, self).__del__()

    @param_check.check_flask_args(Validator("assets_image_schema"), request)
    def get(self, **kwargs):
        system_list = ["windows", "dos", "unix", "linux", "freebsd", "mac", "palm"]
        ip = kwargs["args"]["ip_addr"]
        # present_time = int(time.mktime(datetime.datetime.now().timetuple()))
        # past_day_time = int(time.mktime((datetime.datetime.now() - datetime.timedelta(days=2)).timetuple()))
        sql_str = "select user_agent from dpilog_http where src_ip='%s' or dst_ip='%s' limit 1" % (ip, ip)
        rst = self.get_val_from_database(sql_str)
        return_data = []
        if rst:
            user_agent = rst[0]["user_agent"]
            for system in system_list:
                if user_agent.__contains__(system):
                    return_data.append({"system": system})
                    return flask_response('', True, return_data)
        return flask_response('', True, return_data)


class CustomizedMbUpdate(Resource):
    """
        预定义 重点资产 导入处理
    """

    def post(self):
        file = request.files["fileName"]
        if os.path.exists(MbCfg.DefaultZIPPath):
            try:
                shutil.copyfile(MbCfg.DefaultZIPPath, MbCfg.DefaultZIPPathBK)
                shutil.chown(MbCfg.DefaultZIPPathBK, 'kslab', 'kslab')
                shutil.copyfile(MbCfg.DefaultDbPath, MbCfg.DefaultDbPathBK)
                shutil.chown(MbCfg.DefaultDbPathBK, 'kslab', 'kslab')
            except Exception as e:
                LOG.error('mb file check failed:' + str(e))
                ndr_log_to_box(NdrLog.Type.OPERATE, "重点资产升级失败")
                return flask_response(str(e), False, {})
        file.save(MbCfg.DefaultZIPPath)
        os.system("chown -R kslab:kslab %s" % MbCfg.DefaultZIPPath)
        # 解压
        zip_file = zipfile.ZipFile(MbCfg.DefaultZIPPath)
        for file in zip_file.namelist():
            try:
                password = KnowledgeNDR.ZipPwd
                zip_file.extract(file, KnowledgeNDR.Path, password)
                shutil.chown(MbCfg.DefaultDbPath, 'kslab', 'kslab')
            except Exception as e:
                LOG.error("The customize mb update 失败：%s。" % str(e))
                ndr_log_to_box(NdrLog.Type.OPERATE, "重点资产升级失败")
                return flask_response(str(e), False, {})
        zip_file.close()
        ndr_log_to_box(NdrLog.Type.OPERATE, "重点资产升级成功")
        cluster_sync_file(MbCfg.DefaultDbPath)
        return flask_response("The customize mb update successfully", True, {})


class CustomizedMbImport(AssetsResource):
    """
    自定义重点资产导入处理
    """

    def __init__(self):
        super(CustomizedMbImport, self).__init__()

    def __del__(self):
        super(CustomizedMbImport, self).__del__()

    def format_mb_assets_info(self, data_list):
        """
        data_list: [ip, domain, assetsName, assetsType, oranization, industry, country, address, components, other]
        """
        body = {
            "name": data_list[2],  # 资产名称
            "ip_addr": data_list[0],  # IP地址
            "mac_addr": '',  # MAC地址
            "enable_port": {},  # 启用的端口
            "type": data_list[3],  # 资产类型
            "assets_num": '',  # 资产编号
            "is_sercret": False,  # 是否涉密
            "is_importent": True,  # 是否重点资产
            "location": '',  # 所在位置
            "country": data_list[6],  # 所在国家
            "department": data_list[7],  # 所属部门
            "industry": data_list[5],  # 所属行业
            "organisation": data_list[4],  # 所属组织
            "components": data_list[8],  # 组件列表
            "domain": data_list[1],  # 域名
            "other": data_list[9],  # 其他
            "person": '',  # 责任人
            "os_info": '',  # 操作系统信息
            "labels": '',  # 标签列表
            "send_count": 0,  # 发起的链接数
            "accept_count": 0,  # 接收的链接数
            "app_total_traffic": 0,  # 交互的流量总量
            "app_traffic": {},  # 应用流量Top10
            "send_traffic": {},  # 发送流量Top5
            "access_sample": {},  # 访问抽样
            "popular": 1,  # 每次在告警中发现加5，资产画像查询的时候就加1
            "latest_week_stream": {},  # 周流量
            "create_time": int(time.time() * 1000),  # 资产信息创建时间
            "update_time": int(time.time() * 1000)  # 资产信息更新时间
        }

        return body

    def post(self):
        """
        :return:
        """
        file_obj = request.files["fileName"]
        if not os.path.exists(MbCfg.DbPath):
            LOG.error("The customize mb database file does not exist.")
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义重点目标数据库文件不存在。")
            return flask_response("The customize mb database file does not exist.", False, {"errorCode": 409})
        try:
            file_obj.save(MbCfg.ImportExcelPath + "/" + file_obj.filename)
        except Exception as error:
            LOG.error("Customized mb file [%s] import failed.Reason: %s。" % (file_obj.filename, str(error)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "[%s]导入失败：%s。" % (file_obj.filename, str(error)))
            return flask_response(str(error), False, {})
        data = xlrd.open_workbook(MbCfg.ImportExcelPath + "/" + file_obj.filename)
        table = data.sheet_by_index(0)
        try:
            ip_list = []
            sqlite_repeat_ip_list = []
            sqlite_mb_list = []
            mongo_mb_list = []
            mongo_repeat_list = []
            # 第一行是类型标题，跳过
            for num in range(1, table.nrows):
                parm = list(table.row_values(num))
                # 如果ip字段为空或重复，跳过
                if not parm[0] or parm[0] in ip_list:
                    continue
                ip_list.append(parm[0])
                parm_list = []
                for i in parm:
                    parm_list.append(i)

                rst = self.mongodb.find_one("assets_info", {"ip_addr": parm[0]})
                if rst:
                    mongo_repeat_list.append(parm[0])
                sqlite_mb_list.append(','.join(["'%s'" % item for item in parm_list]))
                mongo_mb_list.append(self.format_mb_assets_info(parm_list))

            # 如果ip重复，先删除之前的再更新
            mb_old = self.cursor.execute("SELECT ip from organization where ip in (%s)" % ','.join(
                ["'%s'" % ip for ip in ip_list]))
            repeat_data = mb_old.fetchall()
            for item in repeat_data:
                sqlite_repeat_ip_list.append(item[0])

            # 先删除重复的ip
            if sqlite_repeat_ip_list:
                self.cursor.execute("DELETE from organization where ip in (%s)" % ','.join(
                    ["'%s'" % ip for ip in sqlite_repeat_ip_list]))
                self.sqlite.commit()
            if mongo_repeat_list:
                self.mongodb.delete("assets_info", {"ip_addr": {'$in': mongo_repeat_list}})

            # 更新重点资产到资产管理列表
            self.mongodb.insert_many("assets_info", mongo_mb_list)
            for sqlite_item in sqlite_mb_list:
                self.cursor.execute("INSERT INTO organization VALUES (%s)" % sqlite_item)

            self.sqlite.commit()

        except Exception as error:
            LOG.error("Customized mb import failed.Reason: %s." % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "自定义重点资产[%s]导入失败" % file_obj.filename)
            return flask_response(str(error), False, {})
        LOG.info("Customized mb file [%s] import successfully." % file_obj.filename)
        ndr_log_to_box(NdrLog.Type.OPERATE, "自定义重点资产[%s]导入成功。" % file_obj.filename)
        cluster_sync_file(MbCfg.DbPath)
        return flask_response("Customized mb import successfully", True, {})


class SearchIPC(Resource):
    def __int__(self):
        pass

    def post(self):
        token = "10cd8a73aa5d46bb920e85a1b34c8674"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36"
        }
        try:
            ip = request.json['ip']
            url = "http://************:7070/?ipLocation=%s&token=%s" % (ip, token)
            req = requests.get(url=url, headers=headers, verify=False)
            json_data = json.loads(req.text)
            if "Error" in json_data:
                return flask_response('Search IPC failed.', False, {"err_msg": json_data["Error"]})
            
            ipc_str = ""
            for key, value in json_data.items():
                ipc_str += f'{key}: {value}     \r\n'
            print(ipc_str, flush=True)
            return flask_response('Search IPC success.', True, {'ipc': ipc_str, 'ip': ip})
        except Exception as e:
            LOG.error("Search IPC failed.Reason: %s." % str(e))
            return flask_response(str(e), False, {})
