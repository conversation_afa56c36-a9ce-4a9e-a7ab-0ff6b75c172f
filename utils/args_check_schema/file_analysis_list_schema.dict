{
	"type": "object",
	"required": [],
	"properties": {
        "startTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
		},
        "endTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
		},
        "sort": {
            "type": "string",
            "enum": ["desc", "asc"],
            "default": "desc",
		},
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        },
        "srcIp": {
            "type": "string",
            "default": "",
		},
        "dstIp": {
            "type": "string",
            "default": "",
		},
        "fileName": {
            "type": "string",
            "default": "",
		},
        "fileType": {
            "type": "string",
            "default": "",
		},
        "protocol": {
            "type": "string",
            "default": "",
		},
        "threatLevel": {
            "type": "string",
            "default": "",
		},
        "celeryId": {
            "type": "string",
            "default": "",
		}
	}
}