import React from 'react';
import { EmailAttachment, TimeRange } from '../types';
import { Button, Divider, Space, Table, Tag } from 'antd';
import { renderTags } from './EmailList';
import { Link } from 'umi';
import { parseQueryString } from '@/utils/utils';

export type Props = {
  data: EmailAttachment[];
  loading: boolean;
  timeRange: TimeRange
  onDownloadFile: (fileid: string) => void
}

function EmailDetailAttachmentList({ data, loading, timeRange, onDownloadFile }: Props): JSX.Element {

  const columns = [
    {
      title: "文件名",
      width: 150,
      dataIndex: "filename",
      render: (t: string) => t || '-',
    },
    {
      title: "文件类型",
      width: 80,
      dataIndex: "filetype",
    },
    {
      title: "文件大小",
      width: 100,
      dataIndex: "filesize",
      render: bytesToKB
    },
    {
      title: "检测结果",
      width: 300,
      dataIndex: "threat",
      render: (threat: string[], { threatName }: EmailAttachment) => {
        return <>
          <Tag color='red'>{threatName}</Tag>
          {renderTags(threat, 9)}
        </>
      },
    },
    {
      title: "操作",
      width: 130,
      align: 'center',
      fixed: 'right',
      key: "action",
      render: (_: never, record: EmailAttachment) => {
        const { fileid } = record

        return (
          <Space split={<Divider type="vertical" />}>
            <Link
              className="!px-0"
              target="_blank"
              to={{
                pathname: '/app/mica/threatAnalysis/emailAnalysis/emailAttachmentDetail',
                search: `${parseQueryString({ ...timeRange, uuid: fileid, })}`,
              }}
            >
              报告详情
            </Link>

            <Button
              type='link'
              style={{ padding: 0 }}
              onClick={() => {
                onDownloadFile(fileid);
              }}
            >
              文件下载
            </Button>
          </Space>
        );
      }
    }
  ];

  return (
    <Table
      loading={loading}
      rowKey="fuid"
      dataSource={data || []}
      columns={columns as any[]}
      scroll={{ x: 1200, }}
    />
  );
}

export default EmailDetailAttachmentList;


export function bytesToKB(bytes: number) {
  if (bytes === 0) return '0 KB';
  const kBytes = bytes / 1024;
  return kBytes.toFixed(2) + ' KB';
}