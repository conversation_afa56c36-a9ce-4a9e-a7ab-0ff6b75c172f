import json
import time
import sys
sys.path.append('/opt/ndr')
from datetime import datetime
from pyhive import hive
from host_celery.utils import MongoDB, get_redis_container_ip
from clickhouse_driver import Client as CH_client
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed


HIVE_CONN_ITEMS = [
    "c_netnum",
    "c_ip",
    "c_flowid",
    "c_src_ipv4",
    "c_src_ipv6",
    "c_src_port",
    "c_s_tunnel_ip",
    "c_s_tunnel_port",
    "c_dest_ipv4",
    "c_dest_ipv6",
    "c_dest_port",
    "c_d_tunnel_ip",
    "c_d_tunnel_port",
    "c_packet_group",
    "c_proto_type",
    "c_connect_status",
    "c_direct",
    "c_server_dir",
    "c_up_packets",
    "c_up_bytes",
    "c_down_packets",
    "c_down_bytes",
    "c_c2s_packet_jitter",
    "c_s2c_packet_jitter",
    "c_log_time",
    "c_app_type",
    "c_stream_time",
    "c_hostr",
    "c_s_boundary",
    "c_s_region",
    "c_s_city",
    "c_s_district",
    "c_s_operators",
    "c_s_owner",
    "c_d_boundary",
    "c_d_region",
    "c_d_city",
    "c_d_district",
    "c_d_operators",
    "c_d_owner"
]

HIVE_URL_ITEMS = [
    "c_netnum",
    "c_ip",
    "c_time",
    "c_flowid",
    "c_src_ipv4",
    "c_src_ipv6",
    "c_src_port",
    "c_s_tunnel_ip",
    "c_s_tunnel_port",
    "c_dest_ipv4",
    "c_dest_ipv6",
    "c_dest_port",
    "c_d_tunnel_ip",
    "c_d_tunnel_port",
    "c_proto_type",
    "c_hostr",
    "c_uri",
    "c_cookie",
    "c_ua",
    "c_method",
    "c_response_code",
    "c_content_length",
    "c_content_type",
    "c_accept",
    "c_accept_encoding",
    "c_location",
    "c_referer",
    "c_filetype",
    "c_filename",
    "c_s_boundary",
    "c_s_region",
    "c_s_city",
    "c_s_district",
    "c_s_operation",
    "c_s_owner",
    "c_d_boundary",
    "c_d_region",
    "c_d_city",
    "c_d_district",
    "c_d_operation",
    "c_d_owner",
    "c_msisdn",
    "c_imsi",
    "c_imei",
    "c_meid",
    "c_uli",
    "c_bsid",
    "c_apn",
    "c_req_body",
    "c_res_body"
]

HIVE_TLS_ITEMS = [
    "c_netnum",
    "c_ip",
    "c_time",
    "c_flowid",
    "c_src_ipv4",
    "c_src_ipv6",
    "c_src_port",
    "c_s_tunnel_ip",
    "c_s_tunnel_port",
    "c_dest_ipv4",
    "c_dest_ipv6",
    "c_dest_port",
    "c_d_tunnel_ip",
    "c_d_tunnel_port",
    "c_sni",
    "c_version",
    "c_certificate",
    "c_s_boundary",
    "c_s_region",
    "c_s_city",
    "c_s_district",
    "c_s_operators",
    "c_s_owner",
    "c_d_boundary",
    "c_d_region",
    "c_d_city",
    "c_d_district",
    "c_d_operators",
    "c_d_owner"
]


HIVE_DNS_ITEMS = [
    "c_dnsid",
    "c_ipversion",
    "c_sip",
    "c_dip",
    "c_sipv6",
    "c_dipv6",
    "c_sport",
    "c_domain",
    "c_type",
    "c_scount",
    "c_srate",
    "c_dir",
    "c_rcode",
    "c_aa",
    "c_rd",
    "c_qlen",
    "c_rlen",
    "C_rrname",
    "c_rrtype",
    "c_value",
    "c_ttl",
    "c_avalue",
    "c_time",
    "c_province",
    "c_city",
    "c_isp",
    "c_pcode",
    "c_nodeip"
]


HIVE_LJC_ITEMS = [
    "c_rip",
    "c_sip",
    "c_dip",
    "c_nip",
    "c_input",
    "c_output",
    "c_packets",
    "c_bytes",
    "c_load_time",
    "c_start_time",
    "c_end_time",
    "c_sport",
    "c_dport",
    "c_flags",
    "c_proto",
    "c_tos",
    "c_sas",
    "c_das",
    "c_src",
    "c_drc",
    "c_province",
    "c_operator",
    "c_rid",
    "c_spc",
    "c_dpc",
    "c_scc",
    "c_dcc",
    "c_sop",
    "c_dop"
]


class HiveProcess(object):
    def __init__(self, source_name):
        self.ck_tab_prefix = 'apt_'
        self.source_name = source_name
        self.source_col = 'cert_hive_source'
        self.time_interval = 15 * 60
        self.ch_client = CH_client(host='clickhouse', user='default', password='ndr@ck++', database='hive')
        self.mg = MongoDB("ndr")

    @staticmethod
    def ip_int_to_str(ip_int):
        return '.'.join([str(int(ip_int / (256 ** i) % 256)) for i in range(4)][::-1])

    @staticmethod
    def ip_str_to_int(ip_str):
        return sum([256**j*int(i) for j, i in enumerate(ip_str.split('.')[::-1])])

    def format_rules_cond(self, data_type, rules) -> str:
        if data_type in ['conn', 'url', 'tls']:
            cond_s_port = 'c_src_port'
            cond_d_port = 'c_dest_port'
            cond_s_ip = 'c_src_ipv4'
            cond_d_ip = 'c_dest_ipv4'
        else:
            cond_s_port = 'c_sport'
            cond_d_port = 'c_dport'
            cond_s_ip = 'c_sip'
            cond_d_ip = 'c_dip'
        cond_list = list()
        for rule in rules:
            if rule['ip'].startswith('{') and rule['ip'].endswith('}'):
                if data_type == 'url':
                    # example: {'uri': 'AAA.php', 'domain': 'AAA', 'method': 'POST'}
                    data = json.loads(rule['ip'])
                    url_cond = []
                    if data.get('uri', None):
                        url_cond.append(f'(c_uri like "%{data["uri"]}%")')
                    if data.get('domain', None):
                        url_cond.append(f'(c_hostr like "%{data["domain"]}%")')
                    if data.get('method', None):
                        url_cond.append(f'(c_method="{data["method"]}")')

                    if url_cond:
                        cond_list.append('(%s)' % ' and '.join(url_cond))
            else:
                ip_int = self.ip_str_to_int(rule['ip'])
                src_port_cond_list = list()
                dest_port_cond_list = list()
                for port in rule['ports'].split(','):
                    port = port.strip()
                    if port.isdigit():
                        src_port_cond_list.append(f'{cond_s_port}={port}')
                        if data_type != 'dns':
                            dest_port_cond_list.append(f'{cond_d_port}={port}')

                if len(src_port_cond_list):
                    src_port_cond = ' or '.join(src_port_cond_list)
                    src_cond = f'({cond_s_ip}={ip_int} and ({src_port_cond}))'
                else:
                    src_cond = f'({cond_s_ip}={ip_int})'

                if len(dest_port_cond_list):
                    dest_port_cond = ' or '.join(dest_port_cond_list)
                    dest_cond = f'({cond_d_ip}={ip_int} and ({dest_port_cond}))'
                else:
                    dest_cond = f'({cond_d_ip}={ip_int})'

                cond_list.append(f'({src_cond} or {dest_cond})')

        return ' or '.join(cond_list)

    def get_hive_sql(self, data_type, source_info):
        cond_str = self.format_rules_cond(data_type, source_info['rules'])
        c_day_min = int(time.strftime("%Y%m%d", time.localtime(int(float(source_info['start_time'])))))
        c_day_max = int(time.strftime("%Y%m%d", time.localtime(int(float(source_info['end_time'])))))
        c_day = c_day_min
        while c_day <= c_day_max:
            if 'conn' == data_type:
                sql = "select %s from wa_wa1c_zysjlc_db.t_connlog_level02 where c_day=%d and (%s)" % (
                    ','.join(HIVE_CONN_ITEMS), c_day, cond_str)
            elif 'url' == data_type:
                sql = "select %s from wa_wa1c_zysjlc_db.t_urllog_level02 where c_day=%d and (%s)" % (
                    ','.join(HIVE_URL_ITEMS), c_day, cond_str)
            elif 'tls' == data_type:
                sql = "select %s from wa_wa1c_zysjlc_db.t_tls_log_level02 where c_day=%d and (%s)" % (
                    ','.join(HIVE_TLS_ITEMS), c_day, cond_str)
            elif 'dns' == data_type:
                sql = "select %s from wa_wa1c_zysjlc_db.t_dams_dnsc2f_level02 where c_day=%d and (%s)" % (
                    ','.join(HIVE_DNS_ITEMS), c_day, cond_str)
            elif 'ljc' == data_type:
                sql = "select %s from wa_wa1c_zysjlc_db.t_flowdata_pms_level02 where c_day=%d and (%s)" % (
                    ','.join(HIVE_LJC_ITEMS), c_day, cond_str)
            else:
                raise Exception('can not support data type: %s!' % data_type)

            yield sql

            c_day += 1

    def query_thread_func(self, data_type, source_info):
        hive_client = hive.Connection(
            host='************', port=10009, auth='LDAP',
            username='u_wa_wa1c_wzwxhttp_ctdi', password='273f03a49fe059bc45007d3af24583a5').cursor()
        self.ch_client = CH_client(host='clickhouse', user='default', password='ndr@ck++', database='hive')
        print('DEBUG: Thread[%s] start query data from hive database.' % data_type)
        for sql in self.get_hive_sql(data_type, source_info):
            print('DEBUG: Thread[%s] sql: %s' % (data_type, sql))
            hive_client.execute(sql)
            insert_cnt = 0
            ck_values = list()
            for row in hive_client.fetchall():
                # add or deal with item.
                meta_data = [datetime.now(), source_info['update_id']]
                row = [i if i is not None else '::' for i in row]
                ck_values.append(tuple(meta_data + list(row)))
                insert_cnt += 1
                if insert_cnt % 100000 == 0:
                    self.ch_client.execute(f'INSERT INTO {self.ck_tab_prefix}{data_type} VALUES', ck_values)
                    ck_values = list()

            if insert_cnt % 100000 != 0 and len(ck_values) != 0:
                self.ch_client.execute(f'INSERT INTO {self.ck_tab_prefix}{data_type} VALUES', ck_values)

            print(f'INFO: clickhouse insert {insert_cnt} data to {self.ck_tab_prefix}{data_type}.')

            source_info = self.mg.find_one(self.source_col, {"source_name": self.source_name})
            if not source_info:
                print(f'WARNING: get mongo info of data type: {data_type} failed!')
                time.sleep(self.time_interval)
                continue

            old_cnt = source_info['statistics'].get(data_type, 0)
            self.mg.update(self.source_col, {"source_name": self.source_name},
                           {f'statistics.{data_type}': insert_cnt + old_cnt})

            time.sleep(self.time_interval)

        return True

    def run(self):
        try:
            print('INFO: hive process start run with source name: %s...' % self.source_name)

            source_info = self.mg.find_one(self.source_col, {"source_name": self.source_name})
            if not source_info:
                print('ERROR: can not find source name : %s' % self.source_name)
                exit(-2)

            print('DEBUG: source name: %s mongo info [update_id: %s, data_types: %s]' % (
                self.source_name, source_info['update_id'], source_info['data_types']))

            data_types_li = source_info['data_types'].split(',')
            if 'conn' not in data_types_li:
                data_types_li.append('conn')

            self.mg.update(self.source_col, {"source_name": self.source_name},
                           {'statistics': {'conn': 0, 'url': 0, 'dns': 0, 'tls': 0, 'ljc': 0}})

            with ThreadPoolExecutor(5) as query_thread:
                thread_res = [
                    query_thread.submit(self.query_thread_func, data_type, source_info) for data_type in data_types_li]

            for future in as_completed(thread_res):
                try:
                    ret_data = future.result()
                except Exception as exc:
                    print('ERROR: generated an exception: %s' % exc)
                else:
                    source_info = self.mg.find_one(self.source_col, {"source_name": self.source_name})
                    if not source_info:
                        print('ERROR: can not find source name : %s' % self.source_name)
                        exit(-2)
                    # self.mg.update(self.source_col, {"source_name": self.source_name},
                    #                {'statistics': source_info.get('statistics', '') + ret_data})

            print('INFO: hive process completed with source name: %s...' % self.source_name)
        except Exception as e:
            print('ERROR: except an error: %s' % str(e))
            exit(-10)


if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        print("ERROR: No arguments provided. source name must is the first argument!")
        exit(-1)

    HiveProcess(sys.argv[1]).run()

