/*
 * @Author: 田浩
 * @Date: 2021-07-28 10:26:50
 * @LastEditors: tianh
 * @LastEditTime: 2022-02-21 14:40:43
 * @Descripttion:
 */
import React, { useEffect, useState } from 'react';
import style from './style.less';
import { Card, Tooltip, Table, Button, message, Popconfirm } from 'antd';
import {
  hardware,
  memory,
  disk,
  service,
  handleStatus,
  getNetworkFlow,
} from '@/services/monitor';
import moment from 'moment';
import { toThousands } from '@/utils/utils';
import { useHistory } from '@/hooks/global';
const echarts = require('echarts');
const Index = () => {
  const history = useHistory();
  let timer: any = null;
  const [flowData, setflowData] = useState<any>([]);
  const [cpuData, setcpuData] = useState<any>({
    cols: [],
    peak_cpu_time: '',
    peak_cpu: 0,
  });
  const [memoryData, setmemoryData] = useState<any>({
    cols: [],
    peak_mem_time: '',
    peak_mem: 0,
  });
  const [diskList, setdiskList] = useState({});
  const [serviceData, setserviceData] = useState([]);
  const columns = [
    {
      title: '服务名',
      dataIndex: 'name',
    },
    {
      title: '运行时间',
      dataIndex: 'runtime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (t: any, record: any) => {
        return record.status === 'running' ? '运行中' : '停止';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: any, record: any) => {
        if (record.name !== 'ndr') {
          const handle = (value: any, action: any) => {
            handleStatus({ serverName: value.name, action: action }).then(
              res => {
                if (res.flag) {
                  message.success(res.message);
                  setTimeout(() => {
                    getService();
                  }, 1000);
                }
              },
            );
          };
          return record.status === 'running' ? (
            <div>
              <Popconfirm
                title={record.stop}
                onConfirm={() => {
                  handle(record, 'stop');
                }}
              >
                <Button type="link">停止</Button>
              </Popconfirm>
              <Popconfirm
                title={record.restart}
                onConfirm={() => {
                  handle(record, 'restart');
                }}
              >
                <Button type="link">重启</Button>
              </Popconfirm>
            </div>
          ) : (
            <Popconfirm
              title={record.start}
              onConfirm={() => {
                handle(record, 'start');
              }}
            >
              <Button type="link">启动</Button>
            </Popconfirm>
          );
        }
      },
    },
  ];
  /**
   * @name:
   * @description: 获取cpu监控数据
   * @param {*}
   * @return {*}
   */

  const getHardwate = () => {
    hardware().then(res => {
      if (res.flag) {
        setcpuData(res.data);
      }
    });
  };
  /**
   * @name:
   * @description: 获取内存监控数据
   * @param {*}
   * @return {*}
   */

  const getMemory = () => {
    memory().then(res => {
      if (res.flag) {
        setmemoryData(res.data);
      }
    });
  };
  /**
   * @name:
   * @description: 获取磁盘监控数据
   * @param {*}
   * @return {*}
   */

  const getDisk = () => {
    disk().then(res => {
      if (res.flag) {
        setdiskList(res.data);
      }
    });
  };
  /**
   * @name:
   * @description:获取服务监控数据
   * @param {*}
   * @return {*}
   */
  const getService = () => {
    service().then(res => {
      if (res.flag) {
        setserviceData(res.data);
      }
    });
  };
  /**
   * @name:
   * @description: 获取网口流量数据
   * @param {*}
   * @return {*}
   */

  const getFlow = () => {
    getNetworkFlow().then(res => {
      if (res.flag) {
        setflowData(res.data);
      }
    });
  };
  const initnetwork = () => {
    if (flowData.speed) {
      let chartDom = document.getElementById('network');
      let myChart = echarts.init(chartDom);
      let text = '';
      let data = flowData.speed;
      let time = flowData.speed.createTime.map((item: any) => {
        return moment(item).format('YYYY-MM-DD HH:mm:ss');
      });
      delete data.createTime;
      let series: {
        name: string;
        type: string;
        data: any;
      }[] = [];
      Object.keys(data).map((item, index) => {
        series.push({
          name: `${item}(Mbps)`,
          type: 'line',
          data: data[item],
        });
      });
      Object.keys(flowData.total).map((item, index) => {
        let num = toThousands(flowData.total[item]);
        let str = `${item}:  ${num}(MB)        `;
        text = text + str;
      });
      let option = {
        title: {
          text: text,
          left: 'left',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          data: time, // x轴坐标名称
          type: 'category',
        },
        yAxis: {
          // type: 'value',
          // axisLabel: {
          //   // formatter: '{value}/mbps', //字符串模板
          //   formatter: (value: any) => {
          //     console.log(2121122, value);
          //   },
          // },
        },
        series: series,
      };
      option && myChart.setOption(option);
    }
  };
  const initCpuChart = () => {
    let chartDom = document.getElementById('cpu');
    let myChart = echarts.init(chartDom);
    let date = cpuData.cols.map((item: any) => {
      return item.createTime;
    });
    let data = cpuData.cols.map((item: any) => {
      return parseInt(`${item.percent * 100}`);
    });
    let option = {
      tooltip: {
        trigger: 'axis',
        position: function(pt: any[]) {
          return [pt[0], '10%'];
        },
      },
      title: {
        text: `历史峰值:${(cpuData.peak_cpu * 100).toFixed(0)}% (时间:${
          cpuData.peak_cpu_time
        })`,
        left: 'left',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: date,
      },
      yAxis: {
        min: 0,
        max: 100,
        data: [0.2, 0.4, 0.6, 0.8, 1],
        type: 'value',
        boundaryGap: [0, '100%'],
        axisLabel: {
          formatter: '{value}%', //字符串模板
        },
      },
      // dataZoom: [
      //   {
      //     type: 'inside',
      //     start: 80,
      //     end: 100,
      //   },
      //   {
      //     start: 80,
      //     end: 100,
      //   },
      // ],
      series: [
        {
          name: '占用率%',
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: 'rgb(255, 70, 131)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(255, 158, 68)',
              },
              {
                offset: 1,
                color: 'rgb(255, 70, 131)',
              },
            ]),
          },
          data: data,
        },
      ],
    };

    option && myChart.setOption(option);
  };
  const initMemChart = () => {
    let chartDom = document.getElementById('memory');
    let myChart = echarts.init(chartDom);
    let date = memoryData.cols.map((item: any) => {
      return item.createTime;
    });
    let data = memoryData.cols.map((item: any) => {
      return parseInt(`${item.percent * 100}`);
    });
    // let num =
    let option = {
      tooltip: {
        trigger: 'axis',
        position: function(pt: any[]) {
          return [pt[0], '10%'];
        },
      },
      title: {
        left: 'left',
        text: `历史峰值:${(memoryData.peak_mem * 100).toFixed(0)}% (时间:${
          memoryData.peak_mem_time
        })`,
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: date,
      },
      yAxis: {
        min: 0,
        max: 100,
        data: [0.2, 0.4, 0.6, 0.8, 1],
        type: 'value',
        boundaryGap: [0, '100%'],
        axisLabel: {
          formatter: '{value}%', //字符串模板
        },
      },
      // dataZoom: [
      //   {
      //     type: 'inside',
      //     start: 80,
      //     end: 100,
      //   },
      //   {
      //     start: 80,
      //     end: 100,
      //   },
      // ],
      series: [
        {
          name: '占用率%',
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: 'rgb(141,163,226)',
          },
          //   line: {
          label: {
            formatter: '{value}%',
          },
          //   },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(141,163,226)',
              },
              {
                offset: 1,
                color: 'rgb(141,163,226)',
              },
            ]),
          },
          data: data,
        },
      ],
    };

    option && myChart.setOption(option);
  };
  const handleRefresh = () => {
    getHardwate();
    getMemory();
    getFlow();
    getDisk();
    getService();
  };
  useEffect(() => {
    getHardwate();
    getFlow();
    getMemory();
    getDisk();
    getService();
  }, []);
  useEffect(() => {
    timer = setInterval(() => {
      handleRefresh();
    }, 10000);
    return () => {
      clearInterval(timer);
    };
  }, []);
  /**
   * @name:
   * @description: cpu数据发生变化时绘制表格
   * @param {*}
   * @return {*}
   */

  useEffect(() => {
    initCpuChart();
  }, [cpuData]);
  /**
   * @name:
   * @description: 内存数据发生变化时绘制表格
   * @param {*}
   * @return {*}
   */

  useEffect(() => {
    initMemChart();
  }, [memoryData]);

  useEffect(() => {
    initnetwork();
  }, [flowData]);
  return (
    <div className={style.box}>
      <div className={style.top_title}>
        <div className={style.title}>主机监控</div>
        <div>
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <Card title="网口流量统计" style={{ width: '100%', marginTop: 10 }}>
        <div className={style.cupCharts} id="network"></div>
      </Card>
      <div className={style.item}>
        <Card title="CPU占用" style={{ width: '50%', marginRight: 10 }}>
          <div className={style.cupCharts} id="cpu"></div>
        </Card>
        <Card title="内存占用" style={{ width: '50%' }}>
          <div className={style.cupCharts} id="memory"></div>
        </Card>
      </div>
      <div className={style.top_title}>
        <div className={style.title}>磁盘监控</div>
        <div>
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <div className={style.disk_box}>
        {Object.keys(diskList).map(key => (
          <div className={style.disk_item} key={key}>
            <div className={style.disk_topText}>
              <div className={style.disk_title}>
                <div className={style.disk_text}>
                  磁盘: <span style={{ fontWeight: 'bold' }}>{key}</span>
                </div>
                <div>
                  历史峰值: {`${(diskList[key].peak * 100).toFixed(0)}%`}
                  &nbsp;(时间:
                  {diskList[key].peakTime})
                </div>
              </div>
            </div>
            <div className={style.disk_img}>
              <div className={style.disk_charts}>
                <Tooltip
                  title={`已用:${
                    parseFloat((diskList[key].use / 1024).toFixed(1)) > 1000
                      ? `${(diskList[key].use / 1024 / 1024).toFixed(1)}T`
                      : `${(diskList[key].use / 1024).toFixed(1)}G`
                  }
                `}
                >
                  <div
                    style={{
                      width: `${diskList[key].percent * 100}%`,
                    }}
                    className={style.disk_use}
                  ></div>
                </Tooltip>
                <Tooltip
                  title={`未用:${
                    parseFloat((diskList[key].free / 1024).toFixed(1)) > 1000
                      ? `${(diskList[key].free / 1024 / 1024).toFixed(1)}T`
                      : `${(diskList[key].free / 1024).toFixed(1)}G`
                  }
                `}
                >
                  <div
                    style={{ width: `100%` }}
                    className={style.disk_noUse}
                  ></div>
                </Tooltip>
              </div>
            </div>
            <p className={style.disk_bottomText}>
              <span className={style.disk_text}>
                总共:
                {/* 超过1000g 单位换位T显示 */}
                {parseFloat((diskList[key].total / 1024).toFixed(1)) > 1000
                  ? `${(diskList[key].total / 1024 / 1024).toFixed(1)}T`
                  : `${(diskList[key].total / 1024).toFixed(1)}G`}
              </span>
              <span className={style.disk_text}>
                已用:
                {parseFloat((diskList[key].use / 1024).toFixed(1)) > 1000
                  ? `${(diskList[key].use / 1024 / 1024).toFixed(1)}T`
                  : `${(diskList[key].use / 1024).toFixed(1)}G`}
              </span>
              <span className={style.disk_text}>
                未用:
                {parseFloat((diskList[key].free / 1024).toFixed(1)) > 1000
                  ? `${(diskList[key].free / 1024 / 1024).toFixed(1)}T`
                  : `${(diskList[key].free / 1024).toFixed(1)}G`}
              </span>
              <span className={style.disk_text}>
                使用率:{(diskList[key].percent * 100).toFixed(0)}%
              </span>
            </p>
          </div>
        ))}
      </div>
      <div className={style.top_title}>
        <div className={style.title}>服务监控</div>
        <div>
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <Table
        columns={columns}
        rowKey={(r, i) => i}
        pagination={false}
        dataSource={serviceData}
      ></Table>
    </div>
  );
};
export default Index;
