import React, { useEffect, useState } from 'react';
import cn from 'classnames';
const echarts = require('echarts');
import 'echarts-wordcloud';
import { Empty } from 'antd';
import { isEmpty } from 'lodash';
import s from './style.less';
interface FileListProps {
  className?: string;
  data: any;
}
const FileEcharts = ({ className, data }: FileListProps) => {
  const option = {
    backgroundColor: '#fff',
    tooltip: {
      trigger: 'item',
      padding: [4, 6],
      textStyle: {
        fontSize: 12,
      },
      formatter: ({ name, value }) => {
        return ` 病毒家族：${name} <br/>
                 数量：${value}
                `;
      },
    },
    series: [
      {
        type: 'wordCloud',
        gridSize: 20,
        sizeRange: [20, 50],
        rotationRange: [0, 0],
        shape: 'diamond',
        textStyle: {
          color: () => {
            const r = 100 + ~~(Math.random() * 100);
            const g = 135 + ~~(Math.random() * 100);
            const b = 100 + ~~(Math.random() * 100);
            return `rgb(${r}, ${g}, ${b})`;
          },
        },
        emphasis: {
          shadowBlur: 10,
          shadowColor: '#000',
        },
        data: [],
      },
    ],
  };

  useEffect(() => {
    if (isEmpty(data)) {
      return;
    }
    const fileEcharts1 = echarts.init(document.getElementById('wordCloud'));
    option.series[0].data = data.map(e => {
      return { name: e.type, value: e.total };
    });
    fileEcharts1.setOption(option, true);
    fileEcharts1.resize();
    window.addEventListener('resize', () => {
      fileEcharts1.resize();
    });
    fileEcharts1.on('legendselectchanged', params => {
      fileEcharts1.setOption({
        legend: { selected: { [params.name]: true } },
      });
    });
    return () => {
      window.removeEventListener('resize', () => {
        fileEcharts1.resize();
      });
    };
  }, [data]);
  return (
    <div className={cn('bg-d0 pa-1', className, s.fileWord)}>
      <p className="c-t4">高危文件病毒家族</p>
      {!isEmpty(data) ? <div id="wordCloud" style={{ width: '100%', height: '250px' }}></div> : <Empty />}
    </div>
  );
};
export default FileEcharts;
