{
    "type": "object",
    "required": [
        "pcap_name"
    ],
    "properties": {
        "pcap_name": {
            "type": "string",
        },
        "frame": {
            "type": "string",
            "default": "1"
        },
        "follow": {
            "type": "string",
        },
        "filter": {
            "type": "string",
        },
        "skip": {
            "type": "string",
            "default": "0"
        },
        "limit": {
            "type": "string",
            "default": "100"
        },
        "is_workbench": {
            "type": "string",
            "default": "true",
            "enum": [
                "true",
                "false"
            ]
        }
    }
}