# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import die_pb2 as die__pb2


class DIEServiceStub(object):
    """The die service definition.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Feature = channel.stream_unary(
                '/die.DIEService/Feature',
                request_serializer=die__pb2.DIEFeatureRequest.SerializeToString,
                response_deserializer=die__pb2.ReplyHeader.FromString,
                )
        self.AddFeature = channel.stream_unary(
                '/die.DIEService/AddFeature',
                request_serializer=die__pb2.DIEAddFeatureRequest.SerializeToString,
                response_deserializer=die__pb2.ReplyHeader.FromString,
                )
        self.OperateFeature = channel.unary_unary(
                '/die.DIEService/OperateFeature',
                request_serializer=die__pb2.DIEOperateFeatureRequest.SerializeToString,
                response_deserializer=die__pb2.ReplyHeader.FromString,
                )
        self.NewSsnLogSwitch = channel.unary_unary(
                '/die.DIEService/NewSsnLogSwitch',
                request_serializer=die__pb2.DIENewSsnLogSwitchRequest.SerializeToString,
                response_deserializer=die__pb2.ReplyHeader.FromString,
                )
        self.FileRestoreProtocol = channel.unary_unary(
                '/die.DIEService/FileRestoreProtocol',
                request_serializer=die__pb2.FileRestoreAppRequest.SerializeToString,
                response_deserializer=die__pb2.ReplyHeader.FromString,
                )
        self.FileRestoreType = channel.unary_unary(
                '/die.DIEService/FileRestoreType',
                request_serializer=die__pb2.FileRestoreTypeRequest.SerializeToString,
                response_deserializer=die__pb2.ReplyHeader.FromString,
                )
        self.FileRestoreSize = channel.unary_unary(
                '/die.DIEService/FileRestoreSize',
                request_serializer=die__pb2.FileRestoreSizeRequest.SerializeToString,
                response_deserializer=die__pb2.ReplyHeader.FromString,
                )


class DIEServiceServicer(object):
    """The die service definition.
    """

    def Feature(self, request_iterator, context):
        """for feature upgrade|recover
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddFeature(self, request_iterator, context):
        """for feature add|modify
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OperateFeature(self, request, context):
        """for feature del|enable|disable
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def NewSsnLogSwitch(self, request, context):
        """for new-session log start|stop
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FileRestoreProtocol(self, request, context):
        """for file restore protocol
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FileRestoreType(self, request, context):
        """for file restore type
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FileRestoreSize(self, request, context):
        """for file restore size
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DIEServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Feature': grpc.stream_unary_rpc_method_handler(
                    servicer.Feature,
                    request_deserializer=die__pb2.DIEFeatureRequest.FromString,
                    response_serializer=die__pb2.ReplyHeader.SerializeToString,
            ),
            'AddFeature': grpc.stream_unary_rpc_method_handler(
                    servicer.AddFeature,
                    request_deserializer=die__pb2.DIEAddFeatureRequest.FromString,
                    response_serializer=die__pb2.ReplyHeader.SerializeToString,
            ),
            'OperateFeature': grpc.unary_unary_rpc_method_handler(
                    servicer.OperateFeature,
                    request_deserializer=die__pb2.DIEOperateFeatureRequest.FromString,
                    response_serializer=die__pb2.ReplyHeader.SerializeToString,
            ),
            'NewSsnLogSwitch': grpc.unary_unary_rpc_method_handler(
                    servicer.NewSsnLogSwitch,
                    request_deserializer=die__pb2.DIENewSsnLogSwitchRequest.FromString,
                    response_serializer=die__pb2.ReplyHeader.SerializeToString,
            ),
            'FileRestoreProtocol': grpc.unary_unary_rpc_method_handler(
                    servicer.FileRestoreProtocol,
                    request_deserializer=die__pb2.FileRestoreAppRequest.FromString,
                    response_serializer=die__pb2.ReplyHeader.SerializeToString,
            ),
            'FileRestoreType': grpc.unary_unary_rpc_method_handler(
                    servicer.FileRestoreType,
                    request_deserializer=die__pb2.FileRestoreTypeRequest.FromString,
                    response_serializer=die__pb2.ReplyHeader.SerializeToString,
            ),
            'FileRestoreSize': grpc.unary_unary_rpc_method_handler(
                    servicer.FileRestoreSize,
                    request_deserializer=die__pb2.FileRestoreSizeRequest.FromString,
                    response_serializer=die__pb2.ReplyHeader.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'die.DIEService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DIEService(object):
    """The die service definition.
    """

    @staticmethod
    def Feature(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(request_iterator, target, '/die.DIEService/Feature',
            die__pb2.DIEFeatureRequest.SerializeToString,
            die__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddFeature(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(request_iterator, target, '/die.DIEService/AddFeature',
            die__pb2.DIEAddFeatureRequest.SerializeToString,
            die__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OperateFeature(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/die.DIEService/OperateFeature',
            die__pb2.DIEOperateFeatureRequest.SerializeToString,
            die__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def NewSsnLogSwitch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/die.DIEService/NewSsnLogSwitch',
            die__pb2.DIENewSsnLogSwitchRequest.SerializeToString,
            die__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FileRestoreProtocol(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/die.DIEService/FileRestoreProtocol',
            die__pb2.FileRestoreAppRequest.SerializeToString,
            die__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FileRestoreType(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/die.DIEService/FileRestoreType',
            die__pb2.FileRestoreTypeRequest.SerializeToString,
            die__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FileRestoreSize(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/die.DIEService/FileRestoreSize',
            die__pb2.FileRestoreSizeRequest.SerializeToString,
            die__pb2.ReplyHeader.FromString,
            options, channel_credentials,
            call_credentials, compression, wait_for_ready, timeout, metadata)
