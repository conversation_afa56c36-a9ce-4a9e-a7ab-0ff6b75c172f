# -*- coding: utf-8 -*-
# @Time    : 2019-05-06 13:03
# <AUTHOR> hachi
# @File    : database.py
# @Software: PyCharm

import os
import time
import requests
import random
import json
import hashlib
import base64
import subprocess
import socket
import queue
from datetime import datetime
from ftplib import FTP
from celery_tasks import app
from config.config import SupervisorConfig
from flask import make_response

ip_proto_dict = {
    "HOPOPT": "ip proto 0",
    "ICMP": "ip proto 1",
    "IGMP": "ip proto 2",
    "GGP": "ip proto 3",
    "IPv4": "ip proto 4",
    "ST": "ip proto 5",
    "TCP": "ip proto 6",
    "CBT": "ip proto 7",
    "EGP": "ip proto 8",
    "IGP": "ip proto 9",
    "BBN-RCC-MON": "ip proto 10",
    "NVP-II": "ip proto 11",
    "PUP": "ip proto 12",
    "ARGUS": "ip proto 13",
    "EMCON": "ip proto 14",
    "XNET": "ip proto 15",
    "CHAOS": "ip proto 16",
    "UDP": "ip proto 17",
    "MUX": "ip proto 18",
    "DCN-MEAS": "ip proto 19",
    "HMP": "ip proto 20",
    "PRM": "ip proto 21",
    "XNS-IDP": "ip proto 22",
    "TRUNK-1": "ip proto 23",
    "TRUNK-2": "ip proto 24",
    "LEAF-1": "ip proto 25",
    "LEAF-2": "ip proto 26",
    "RDP": "ip proto 27",
    "IRTP": "ip proto 28",
    "ISO-TP4": "ip proto 29",
    "NETBLT": "ip proto 30",
    "MFE-NSP": "ip proto 31",
    "MERIT-INP": "ip proto 32",
    "DCCP": "ip proto 33",
    "3PC": "ip proto 34",
    "IDPR": "ip proto 35",
    "XTP": "ip proto 36",
    "DDP": "ip proto 37",
    "IDPR-CMTP": "ip proto 38",
    "TP++": "ip proto 39",
    "IL": "ip proto 40",
    "IPv6": "ip proto 41",
    "SDRP": "ip proto 42",
    "IPv6-Route": "ip proto 43",
    "IPv6-Frag": "ip proto 44",
    "IDRP": "ip proto 45",
    "RSVP": "ip proto 46",
    "GRE": "ip proto 47",
    "MHRP": "ip proto 48",
    "BNA": "ip proto 49",
    "ESP": "ip proto 50",
    "AH": "ip proto 51",
    "I-NLSP": "ip proto 52",
    "SWIPE": "ip proto 53",
    "NARP": "ip proto 54",
    "MOBILE": "ip proto 55",
    "TLSP": "ip proto 56",
    "SKIP": "ip proto 57",
    "IPv6-ICMP": "ip proto 58",
    "IPv6-NoNxt": "ip proto 59",
    "IPv6-Opts": "ip proto 60",
    "CFTP": "ip proto 62",
    "SAT-EXPAK": "ip proto 64",
    "KRYPTOLAN": "ip proto 65",
    "RVD": "ip proto 66",
    "IPPC": "ip proto 67",
    "SAT-MON": "ip proto 69",
    "VISA": "ip proto 70",
    "IPCV": "ip proto 71",
    "CPNX": "ip proto 72",
    "CPHB": "ip proto 73",
    "WSN": "ip proto 74",
    "PVP": "ip proto 75",
    "BR-SAT-MON": "ip proto 76",
    "SUN-ND": "ip proto 77",
    "WB-MON": "ip proto 78",
    "WB-EXPAK": "ip proto 79",
    "ISO-IP": "ip proto 80",
    "VMTP": "ip proto 81",
    "SECURE-VMTP": "ip proto 82",
    "VINES": "ip proto 83",
    "TTP": "ip proto 84",
    "IPTM": "ip proto 84",
    "NSFNET-IGP": "ip proto 85",
    "DGP": "ip proto 86",
    "TCF": "ip proto 87",
    "EIGRP": "ip proto 88",
    "OSPF": "ip proto 89",
    "Sprite-RPC": "ip proto 90",
    "LARP": "ip proto 91",
    "MTP": "ip proto 92",
    "AX.25": "ip proto 93",
    "IPIP": "ip proto 94",
    "MICP": "ip proto 95",
    "SCC-SP": "ip proto 96",
    "ETHERIP": "ip proto 97",
    "ENCAP": "ip proto 98",
    "GMTP": "ip proto 100",
    "IFMP": "ip proto 101",
    "PNNI": "ip proto 102",
    "PIM": "ip proto 103",
    "ARIS": "ip proto 104",
    "SCPS": "ip proto 105",
    "QNX": "ip proto 106",
    "A/N": "ip proto 107",
    "IPComp": "ip proto 108",
    "SNP": "ip proto 109",
    "Compaq-Peer": "ip proto 110",
    "IPX-in-IP": "ip proto 111",
    "VRRP": "ip proto 112",
    "PGM": "ip proto 113",
    "L2TP": "ip proto 115",
    "DDX": "ip proto 116",
    "IATP": "ip proto 117",
    "STP": "ip proto 118",
    "SRP": "ip proto 119",
    "UTI": "ip proto 120",
    "SMP": "ip proto 121",
    "SM": "ip proto 122",
    "PTP": "ip proto 123",
    "IS-IS": "ip proto 124",
    "FIRE": "ip proto 125",
    "CRTP": "ip proto 126",
    "CRUDP": "ip proto 127",
    "SSCOPMCE": "ip proto 128",
    "IPLT": "ip proto 129",
    "SPS": "ip proto 130",
    "PIPE": "ip proto 131",
    "SCTP": "ip proto 132",
    "FC": "ip proto 133",
    "RSVP-E2E-IGNORE": "ip proto 134",
    "Mobility": "ip proto 135",
    "UDPLite": "ip proto 136",
    "MPLS-in-IP": "ip proto 137",
    "manet": "ip proto 138",
    "HIP": "ip proto 139",
    "Shim6": "ip proto 140",
    "WESP": "ip proto 141",
    "ROHC": "ip proto 142",
}

country_images = {
    "Afghanistan": "/api/v1/image_country/af.png",
    "Albania": "/api/v1/image_country/al.png",
    "Algeria": "/api/v1/image_country/dz.png",
    "Andorra": "/api/v1/image_country/ad.png",
    "Angola": "/api/v1/image_country/ao.png",
    "Antigua and Barbuda": "/api/v1/image_country/ag.png",
    "Argentina": "/api/v1/image_country/ar.png",
    "Armenia": "/api/v1/image_country/am.png",
    "Australia": "/api/v1/image_country/au.png",
    "Austria": "/api/v1/image_country/at.png",
    "Azerbaijan": "/api/v1/image_country/az.png",
    "Bahamas": "/api/v1/image_country/bs.png",
    "Bahrain": "/api/v1/image_country/bh.png",
    "Bangladesh": "/api/v1/image_country/bd.png",
    "Barbados": "/api/v1/image_country/bb.png",
    "Belarus": "/api/v1/image_country/by.png",
    "Belgium": "/api/v1/image_country/be.png",
    "Belize": "/api/v1/image_country/bz.png",
    "Benin": "/api/v1/image_country/bj.png",
    "Bhutan": "/api/v1/image_country/bt.png",
    "Bolivia": "/api/v1/image_country/bo.png",
    "Bosnia and Herzegovina": "/api/v1/image_country/ba.png",
    "Botswana": "/api/v1/image_country/bw.png",
    "Brazil": "/api/v1/image_country/br.png",
    "Brunei": "/api/v1/image_country/bn.png",
    "Bulgaria": "/api/v1/image_country/bg.png",
    "Burkina Faso": "/api/v1/image_country/bf.png",
    "Burundi": "/api/v1/image_country/bi.png",
    "Cambodia": "/api/v1/image_country/kh.png",
    "Cameroon": "/api/v1/image_country/cm.png",
    "Canada": "/api/v1/image_country/ca.png",
    "Cabo Verde": "/api/v1/image_country/cv.png",
    "Central African Republic": "/api/v1/image_country/cf.png",
    "Chad": "/api/v1/image_country/td.png",
    "Chile": "/api/v1/image_country/cl.png",
    "Colombia": "/api/v1/image_country/co.png",
    "Comoros": "/api/v1/image_country/km.png",
    "Costa Rica": "/api/v1/image_country/cr.png",
    "Ivory Coast": "/api/v1/image_country/ci.png",
    "Croatia": "/api/v1/image_country/hr.png",
    "Cuba": "/api/v1/image_country/cu.png",
    "Cyprus": "/api/v1/image_country/cy.png",
    "Czechia": "/api/v1/image_country/cz.png",
    "Congo": "/api/v1/image_country/cd.png",
    "Denmark": "/api/v1/image_country/dk.png",
    "Djibouti": "/api/v1/image_country/dj.png",
    "Dominica": "/api/v1/image_country/dm.png",
    "Dominican Republic": "/api/v1/image_country/do.png",
    "Democratic Republic of Timor-Leste": "/api/v1/image_country/tl.png",
    "Ecuador": "/api/v1/image_country/ec.png",
    "Egypt": "/api/v1/image_country/eg.png",
    "El Salvador": "/api/v1/image_country/sv.png",
    "Equatorial Guinea": "/api/v1/image_country/gq.png",
    "Eritrea": "/api/v1/image_country/er.png",
    "Estonia": "/api/v1/image_country/ee.png",
    "Ethiopia": "/api/v1/image_country/et.png",
    "Fiji": "/api/v1/image_country/fj.png",
    "Finland": "/api/v1/image_country/fi.png",
    "France": "/api/v1/image_country/fr.png",
    "Gabon": "/api/v1/image_country/ga.png",
    "Gambia": "/api/v1/image_country/gm.png",
    "Cook Islands": "/api/v1/image_country/ck.png",
    "Germany": "/api/v1/image_country/de.png",
    "Ghana": "/api/v1/image_country/gh.png",
    "Greece": "/api/v1/image_country/gr.png",
    "Grenada": "/api/v1/image_country/gd.png",
    "Guatemala": "/api/v1/image_country/gt.png",
    "Guinea": "/api/v1/image_country/gn.png",
    "Guinea-Bissau": "/api/v1/image_country/gw.png",
    "Guyana": "/api/v1/image_country/gy.png",
    "Haiti": "/api/v1/image_country/ht.png",
    "Honduras": "/api/v1/image_country/hn.png",
    "Hungary": "/api/v1/image_country/hu.png",
    "Iceland": "/api/v1/image_country/is.png",
    "India": "/api/v1/image_country/in.png",
    "Indonesia": "/api/v1/image_country/id.png",
    "Iran": "/api/v1/image_country/ir.png",
    "Iraq": "/api/v1/image_country/iq.png",
    "Ireland": "/api/v1/image_country/ie.png",
    "Israel": "/api/v1/image_country/il.png",
    "Italy": "/api/v1/image_country/it.png",
    "Jamaica": "/api/v1/image_country/jm.png",
    "Japan": "/api/v1/image_country/jp.png",
    "Hashemite Kingdom of Jordan": "/api/v1/image_country/jo.png",
    "Kazakhstan": "/api/v1/image_country/kz.png",
    "Kenya": "/api/v1/image_country/ke.png",
    "Kiribati": "/api/v1/image_country/ki.png",
    "Kosovo": "/api/v1/image_country/ks.png",
    "Kuwait": "/api/v1/image_country/kw.png",
    "Kyrgyzstan": "/api/v1/image_country/kg.png",
    "Laos": "/api/v1/image_country/la.png",
    "Latvia": "/api/v1/image_country/lv.png",
    "Lebanon": "/api/v1/image_country/lb.png",
    "Lesotho": "/api/v1/image_country/ls.png",
    "Liberia": "/api/v1/image_country/lr.png",
    "Libya": "/api/v1/image_country/ly.png",
    "Liechtenstein": "/api/v1/image_country/li.png",
    "Republic of Lithuania": "/api/v1/image_country/lt.png",
    "Luxembourg": "/api/v1/image_country/lu.png",
    "Madagascar": "/api/v1/image_country/mg.png",
    "Malawi": "/api/v1/image_country/mw.png",
    "Malaysia": "/api/v1/image_country/my.png",
    "Maldives": "/api/v1/image_country/mv.png",
    "Mali": "/api/v1/image_country/ml.png",
    "Malta": "/api/v1/image_country/mt.png",
    "Macedonia": "/api/v1/image_country/mk.png",
    "Marshall Islands": "/api/v1/image_country/mh.png",
    "Mauritania": "/api/v1/image_country/mr.png",
    "Mauritius": "/api/v1/image_country/mu.png",
    "Mexico": "/api/v1/image_country/mx.png",
    "Federated States of Micronesia": "/api/v1/image_country/fm.png",
    "Republic of Moldova": "/api/v1/image_country/md.png",
    "Monaco": "/api/v1/image_country/mc.png",
    "Mongolia": "/api/v1/image_country/mn.png",
    "Montenegro": "/api/v1/image_country/me.png",
    "Morocco": "/api/v1/image_country/ma.png",
    "Mozambique": "/api/v1/image_country/mz.png",
    "Myanmar": "/api/v1/image_country/mm.png",
    "Namibia": "/api/v1/image_country/na.png",
    "Nauru": "/api/v1/image_country/nr.png",
    "Nepal": "/api/v1/image_country/np.png",
    "Netherlands": "/api/v1/image_country/nl.png",
    "New Zealand": "/api/v1/image_country/nz.png",
    "Nicaragua": "/api/v1/image_country/ni.png",
    "Niger": "/api/v1/image_country/ne.png",
    "Nigeria": "/api/v1/image_country/ng.png",
    "North Korea": "/api/v1/image_country/kp.png",
    "Norway": "/api/v1/image_country/no.png",
    "Oman": "/api/v1/image_country/om.png",
    "Pakistan": "/api/v1/image_country/pk.png",
    "Palau": "/api/v1/image_country/pw.png",
    "Panama": "/api/v1/image_country/pa.png",
    "Papua New Guinea": "/api/v1/image_country/pg.png",
    "Paraguay": "/api/v1/image_country/py.png",
    "China": "/api/v1/image_country/cn.png",
    "Peru": "/api/v1/image_country/pe.png",
    "Philippines": "/api/v1/image_country/ph.png",
    "Poland": "/api/v1/image_country/pl.png",
    "Portugal": "/api/v1/image_country/pt.png",
    "Qatar": "/api/v1/image_country/qa.png",
    "Taiwan": "/api/v1/image_country/tw.png",
    "Republic of the Congo": "/api/v1/image_country/cg.png",
    "Romania": "/api/v1/image_country/ro.png",
    "Russia": "/api/v1/image_country/ru.png",
    "Rwanda": "/api/v1/image_country/rw.png",
    "St Kitts and Nevis": "/api/v1/image_country/kn.png",
    "Saint Lucia": "/api/v1/image_country/lc.png",
    "Saint Vincent and the Grenadines": "/api/v1/image_country/vc.png",
    "Samoa": "/api/v1/image_country/ws.png",
    "San Marino": "/api/v1/image_country/sm.png",
    "S\u00e3o Tom\u00e9 and Pr\u00edncipe": "/api/v1/image_country/st.png",
    "Saudi Arabia": "/api/v1/image_country/sa.png",
    "Senegal": "/api/v1/image_country/sn.png",
    "Serbia": "/api/v1/image_country/rs.png",
    "Seychelles": "/api/v1/image_country/sc.png",
    "Sierra Leone": "/api/v1/image_country/sl.png",
    "Singapore": "/api/v1/image_country/sg.png",
    "Slovakia": "/api/v1/image_country/sk.png",
    "Slovenia": "/api/v1/image_country/si.png",
    "Solomon Islands": "/api/v1/image_country/sb.png",
    "Niue": "/api/v1/image_country/nu.png",
    "Somalia": "/api/v1/image_country/so.png",
    "South Sudan": "/api/v1/image_country/ss.png",
    "South Africa": "/api/v1/image_country/za.png",
    "South Korea": "/api/v1/image_country/kr.png",
    "Spain": "/api/v1/image_country/es.png",
    "Sri Lanka": "/api/v1/image_country/lk.png",
    "Sudan": "/api/v1/image_country/sd.png",
    "Suriname": "/api/v1/image_country/sr.png",
    "Eswatini": "/api/v1/image_country/sz.png",
    "Sweden": "/api/v1/image_country/se.png",
    "Switzerland": "/api/v1/image_country/ch.png",
    "Syria": "/api/v1/image_country/sy.png",
    "Tajikistan": "/api/v1/image_country/tj.png",
    "Tanzania": "/api/v1/image_country/tz.png",
    "Thailand": "/api/v1/image_country/th.png",
    "Togo": "/api/v1/image_country/tg.png",
    "Tonga": "/api/v1/image_country/to.png",
    "Trinidad and Tobago": "/api/v1/image_country/tt.png",
    "Tunisia": "/api/v1/image_country/tn.png",
    "Turkey": "/api/v1/image_country/tr.png",
    "Turkmenistan": "/api/v1/image_country/tm.png",
    "Georgia": "/api/v1/image_country/ge.png",
    "Tuvalu": "/api/v1/image_country/tv.png",
    "Uganda": "/api/v1/image_country/ug.png",
    "Ukraine": "/api/v1/image_country/ua.png",
    "United Arab Emirates": "/api/v1/image_country/ae.png",
    "United Kingdom": "/api/v1/image_country/gb.png",
    "United States": "/api/v1/image_country/us.png",
    "Uruguay": "/api/v1/image_country/uy.png",
    "Uzbekistan": "/api/v1/image_country/uz.png",
    "Vanuatu": "/api/v1/image_country/vu.png",
    "Vatican City": "/api/v1/image_country/va.png",
    "Venezuela": "/api/v1/image_country/ve.png",
    "Vietnam": "/api/v1/image_country/vn.png",
    "Western Sahara": "/api/v1/image_country/eh.png",
    "Yemen": "/api/v1/image_country/ye.png",
    "Zambia": "/api/v1/image_country/zm.png",
    "Zimbabwe": "/api/v1/image_country/zw.png"
}

en_to_cn_country = {
    "Afghanistan": "阿富汗",
    "Albania": "阿尔巴尼亚",
    "Algeria": "阿尔及利亚",
    "Andorra": "安道尔",
    "Angola": "安哥拉",
    "Antigua and Barbuda": "安提瓜和巴布达",
    "Argentina": "阿根廷",
    "Armenia": "亚美尼亚",
    "Australia": "澳大利亚",
    "Austria": "奥地利",
    "Azerbaijan": "阿塞拜疆",
    "Bahamas": "巴哈马",
    "Bahrain": "巴林",
    "Bangladesh": "孟加拉国",
    "Barbados": "巴巴多斯",
    "Belarus": "白俄罗斯",
    "Belgium": "比利时",
    "Belize": "伯利兹",
    "Benin": "贝宁",
    "Bhutan": "不丹",
    "Bolivia": "玻利维亚",
    "Bosnia and Herzegovina": "波黑",
    "Botswana": "波札那",
    "Brazil": "巴西",
    "Brunei": "文莱",
    "Bulgaria": "保加利亚",
    "Burkina Faso": "布基纳法索",
    "Burundi": "蒲隆地",
    "Cambodia": "柬埔寨",
    "Cameroon": "喀麦隆",
    "Canada": "加拿大",
    "Cabo Verde": "佛得角",
    "Central African Republic": "中非共和国",
    "Chad": "乍得",
    "Chile": "智利",
    "Colombia": "哥伦比亚",
    "Comoros": "葛摩",
    "Costa Rica": "哥斯达黎加",
    "Ivory Coast": "科特迪瓦",
    "Croatia": "克罗地亚",
    "Cuba": "古巴",
    "Cyprus": "塞浦路斯",
    "Czechia": "捷克",
    "Congo": "刚果民主共和国",
    "Denmark": "丹麦",
    "Djibouti": "吉布提",
    "Dominica": "多米尼克",
    "Dominican Republic": "多明尼加共和国",
    "Democratic Republic of Timor-Leste": "东帝汶",
    "Ecuador": "厄瓜多尔",
    "Egypt": "埃及",
    "El Salvador": "萨尔瓦多",
    "Equatorial Guinea": "赤道几内亚",
    "Eritrea": "厄立特里亚",
    "Estonia": "爱沙尼亚",
    "Ethiopia": "埃塞俄比亚",
    "Fiji": "斐济",
    "Finland": "芬兰",
    "France": "法国",
    "Gabon": "加蓬",
    "Gambia": "冈比亚",
    "Cook Islands": "库克群岛",
    "Germany": "德国",
    "Ghana": "加纳",
    "Greece": "希腊",
    "Grenada": "格林纳达",
    "Guatemala": "危地马拉",
    "Guinea": "几内亚",
    "Guinea-Bissau": "几内亚比绍",
    "Guyana": "圭亚那",
    "Haiti": "海地",
    "Honduras": "洪都拉斯",
    "Hungary": "匈牙利",
    "Iceland": "冰岛",
    "India": "印度",
    "Indonesia": "印度尼西亚",
    "Iran": "伊朗",
    "Iraq": "伊拉克",
    "Ireland": "爱尔兰岛",
    "Israel": "以色列",
    "Italy": "意大利",
    "Jamaica": "牙买加",
    "Japan": "日本",
    "Hashemite Kingdom of Jordan": "约旦",
    "Kazakhstan": "哈萨克斯坦",
    "Kenya": "肯尼亚",
    "Kiribati": "基里巴斯",
    "Kosovo": "科索沃",
    "Kuwait": "科威特",
    "Kyrgyzstan": "吉尔吉斯斯坦",
    "Laos": "老挝",
    "Latvia": "拉脱维亚",
    "Lebanon": "黎巴嫩",
    "Lesotho": "莱索托",
    "Liberia": "利比里亚",
    "Libya": "利比亚",
    "Liechtenstein": "列支敦斯登",
    "Republic of Lithuania": "立陶宛",
    "Luxembourg": "卢森堡",
    "Madagascar": "马达加斯加",
    "Malawi": "马拉维",
    "Malaysia": "马来西亚",
    "Maldives": "马尔代夫",
    "Mali": "马里共和国",
    "Malta": "马耳他",
    "Macedonia": "马其顿",
    "Marshall Islands": "马绍尔群岛",
    "Mauritania": "毛里塔尼亚",
    "Mauritius": "毛里求斯",
    "Mexico": "墨西哥",
    "Federated States of Micronesia": "密克罗尼西亚群岛",
    "Republic of Moldova": "摩尔多瓦",
    "Monaco": "摩纳哥",
    "Mongolia": "蒙古国",
    "Montenegro": "黑山",
    "Morocco": "摩洛哥",
    "Mozambique": "莫桑比克",
    "Myanmar": "缅甸",
    "Namibia": "纳米比亚",
    "Nauru": "瑙鲁",
    "Nepal": "尼泊尔",
    "Netherlands": "荷兰",
    "New Zealand": "新西兰",
    "Nicaragua": "尼加拉瓜",
    "Niger": "尼日尔",
    "Nigeria": "尼日利亚",
    "North Korea": "朝鲜",
    "Norway": "挪威",
    "Oman": "阿曼",
    "Pakistan": "巴基斯坦",
    "Palau": "帕劳",
    "Panama": "巴拿马",
    "Papua New Guinea": "巴布亚新几内亚",
    "Paraguay": "巴拉圭",
    "China": "中国",
    "Peru": "秘鲁",
    "Philippines": "菲律宾",
    "Poland": "波兰",
    "Portugal": "葡萄牙",
    "Qatar": "卡塔尔",
    "Taiwan": "台湾",
    "Republic of the Congo": "刚果共和国",
    "Romania": "罗马尼亚",
    "Russia": "俄罗斯",
    "Rwanda": "卢旺达",
    "St Kitts and Nevis": "瓜德罗普",
    "Saint Lucia": "圣卢西亚",
    "Saint Vincent and the Grenadines": "诺福克岛",
    "Samoa": "萨摩亚",
    "San Marino": "圣马力诺",
    "São Tomé and Príncipe": "圣多美和普林西比",
    "Saudi Arabia": "沙特阿拉伯",
    "Senegal": "塞内加尔",
    "Serbia": "塞尔维亚",
    "Seychelles": "塞舌尔",
    "Sierra Leone": "塞拉利昂",
    "Singapore": "新加坡",
    "Slovakia": "斯洛伐克",
    "Slovenia": "斯洛文尼亚",
    "Solomon Islands": "所罗门群岛",
    "Niue": "纽埃",
    "Somalia": "索马里",
    "South Sudan": "南苏丹",
    "South Africa": "南非",
    "South Korea": "韩国",
    "Spain": "西班牙",
    "Sri Lanka": "斯里兰卡",
    "Sudan": "苏丹",
    "Suriname": "苏里南",
    "Eswatini": "斯威士兰",
    "Sweden": "瑞典",
    "Switzerland": "瑞士",
    "Syria": "叙利亚",
    "Tajikistan": "塔吉克斯坦",
    "Tanzania": "坦桑尼亚",
    "Thailand": "泰国",
    "Togo": "多哥",
    "Tonga": "汤加",
    "Trinidad and Tobago": "特立尼达和多巴哥",
    "Tunisia": "突尼斯",
    "Turkey": "土耳其",
    "Turkmenistan": "土库曼斯坦",
    "Georgia": "格鲁吉亚",
    "Tuvalu": "图瓦卢",
    "Uganda": "乌干达",
    "Ukraine": "乌克兰",
    "United Arab Emirates": "阿拉伯联合酋长国",
    "United Kingdom": "英国",
    "United States": "美国",
    "Uruguay": "乌拉圭",
    "Uzbekistan": "乌兹别克斯坦",
    "Vanuatu": "瓦努阿图",
    "Vatican City": "梵蒂冈",
    "Venezuela": "委内瑞拉",
    "Vietnam": "越南",
    "Western Sahara": "阿拉伯撒哈拉民主共和国",
    "Yemen": "也门",
    "Zambia": "赞比亚",
    "Zimbabwe": "津巴布韦"
}


def create_task_id():
    """create task id"""
    md5_str = hashlib.md5(str(time.clock()).encode('utf-8'))
    return md5_str.hexdigest()


def flask_response(msg, flag, data):
    response = {
        "message": msg,
        "flag": flag,
        "data": data
    }
    return_data = make_response(json.dumps(response))
    return_data.mimetype = "application/json"
    return return_data


def get_time_slice(time_slice, num=1, times=None):
    """根据传入的时间片和个数，返回时间长度(s)，如果是月，则需要计算月的起始时间戳
    :param time_slice: 时间分片
    :param num: 分片个数
    :param times: 想要计算的处于当前月的时间
    :return: 时间长度(s)
    """
    slice_dict = {
        "Minute": 60 * num,
        "Hour": 60 * 60 * num,
        "Day": 60 * 60 * 24 * num,
        "Week": 60 * 60 * 24 * 7 * num
    }
    if time_slice == "Month":
        start = times
        month = datetime.fromtimestamp(start).month + num
        year = datetime.fromtimestamp(start).year
        if month > 12:
            year += 1
            month -= 12
        stop = set_time(start, month=month, year=year)
        slice_dict["Month"] = stop - start

    return slice_dict[time_slice]


def get_mongo_col(slice):
    """
    :param slice: Get请求传入的分片
    :return: 分片对应的mongo表
    """
    slice_dict = {
        "Minute": "event_outline_minutes",
        "Hour": "event_outline_hours",
        "Day": "event_outline_days",
        "Week": "event_outline_weeks",
        "Month": "event_outline_months"
    }
    return slice_dict[slice]


def generate_time_list(args, event_list=None):
    """根据前端传入的起止时间和分片，返回生成的timeList
    :param args: 前端传入的参数, 需要包含，start_time, stop_time, time_slice
    :return: 生成的timeList
    """
    start_time = int((args["start_time"] / 1000))
    stop_time = int((args["stop_time"] / 1000))
    now_time = int(datetime.now().timestamp())

    if start_time >= now_time:
        return {}
    if stop_time > now_time:
        stop_time = now_time

    time_list = {}
    while start_time <= stop_time:
        time_list[start_time * 1000] = {
            "sliceStart": start_time * 1000,
            "sliceStop": (start_time - 1 + get_time_slice(args["time_slice"], 1, start_time)) * 1000
        }
        if event_list:
            time_list[start_time * 1000]["eventList"] = []
        start_time += get_time_slice(args["time_slice"], 1, start_time)
    return time_list


def set_time(times, second=None, minute=None, hour=None, day=None, month=None, year=None):
    """
    :param times: 需要修改的时间
    :param second: 设置的秒数
    :param minute: 设置的分钟数
    :param hour: 设置的小时数
    :param day: 设置的天数
    :param month: 设置的月数
    :return: 返回修改之后的时间的时间戳
    """
    times = datetime.fromtimestamp(times)
    if second is None:
        second = times.second
    if minute is None:
        minute = times.minute
    if hour is None:
        hour = times.hour
    if day is None:
        day = times.day
    if month is None:
        month = times.month
    if year is None:
        year = times.year
    return int(times.replace(second=second, minute=minute, hour=hour, day=day,
                             month=month, year=year).timestamp())


def split_time(time_list):
    """
    :param time_list: res中的timeList
    :return: 返回split的time
    判断当前时间与timeList的最后一个分片的结束时间，
    如果结束时间小于当前时间，则最后一个分片不需要拆分，直接在对应的表查询即可
    如果结束时间大于等于当前，则最后一个分片需要聚合(天、小时、分钟)的结果
    """
    current_time = int(datetime.now().timestamp())
    time_list.sort(key=lambda k: (k.get('sliceStart', 0)))
    last_slice = time_list[-1]
    first_slice = time_list[0]

    time_split = {
        "slice": {"start": 0, "stop": 0},
        "slice_Day": {"start": 0, "stop": 0},
        "slice_Hour": {"start": 0, "stop": 0},
        "slice_Minute": {"start": 0, "stop": 0}
    }

    # 如果结束时间小于当前时间，则所有分片对应的数据都有，直接查即可
    if (last_slice["sliceStop"] // 1000) <= current_time:
        time_split["slice"]["start"] = first_slice["sliceStart"]
        time_split["slice"]["stop"] = last_slice["sliceStop"]
        return time_split

    # 如果结束时间大于等于当前时间，则除了最后一个分片，之前的所有的分片都可以直接查
    try:
        # 有可能出现只有一个分片，且分片未满的情况
        second_last_slice = time_list[-2]
        time_split["slice"]["start"] = first_slice["sliceStart"]
        time_split["slice"]["stop"] = second_last_slice["sliceStop"]
    except Exception as e:
        print(e)

    # 拆分最后一个分片为天、小时、分钟
    current_datetime = datetime.timetuple(datetime.fromtimestamp(current_time))
    slice_start_datetime = datetime.timetuple(
        datetime.fromtimestamp(last_slice["sliceStart"] // 1000))

    day = current_datetime.tm_mday - slice_start_datetime.tm_mday
    hour = current_datetime.tm_hour - slice_start_datetime.tm_hour
    min = current_datetime.tm_min - slice_start_datetime.tm_min

    # 计算天、小时、分钟的开始和结束时间
    start_time = last_slice["sliceStart"]
    if day:
        time_split["slice_Day"]["start"] = start_time
        time_split["slice_Day"]["stop"] = start_time + (get_time_slice("Day", day) - 1) * 1000
        start_time = time_split["slice_Day"]["stop"] + 1 * 1000
    if hour:
        time_split["slice_Hour"]["start"] = start_time
        time_split["slice_Hour"]["stop"] = start_time + (get_time_slice("Hour", hour) - 1) * 1000
        start_time = time_split["slice_Hour"]["stop"] + 1 * 1000
    if min:
        time_split["slice_Minute"]["start"] = start_time
        time_split["slice_Minute"]["stop"] = start_time + (get_time_slice("Minute", min) - 1) * 1000

    return time_split


def get_first_timestamp(times, time_slice):
    first_timestamp = 0
    if time_slice == "Month":
        first_timestamp = set_time(times, second=0, minute=0, hour=0, day=1)
    elif time_slice == "Week":
        start_date = datetime.fromtimestamp(times).timetuple()
        first_day = start_date.tm_mday - start_date.tm_wday
        first_timestamp = set_time(times, second=0, minute=0, hour=0, day=first_day)
    elif time_slice == "Day":
        first_timestamp = set_time(times, second=0, minute=0, hour=0)
    elif time_slice == "Hour":
        first_timestamp = set_time(times, second=0, minute=0)
    else:
        first_timestamp = set_time(times, second=0)
    return first_timestamp


def ftp_server_connect(host, port, username, password):
    try:
        ftp = FTP()
        # ftp.set_debuglevel(2)
        ftp.connect(host, port)
        ftp.login(user=username, passwd=base64.b64decode(password).decode())
        # ftp.getwelcome()
        # ftp.set_pasv(1)  # 0主动模式 1 #被动模式
    except Exception as error:
        return error, False
    return ftp, True


def ftp_server_disconnect(ftp):
    try:
        ftp.quit()
    except Exception as error:
        return False
    return True


def ftp_server_reconnect(host, port, username, password):
    for i in range(3):
        ftp, rst = ftp_server_connect(host, port, username, password)
        if rst:
            break
        else:
            time.sleep(6)
    return ftp, rst


def ftp_server_recurse_pcap(ftp, start_dir):
    recurse_pcap_list = []
    ftp.cwd(start_dir)
    dir_res = []
    ftp.dir('.', dir_res.append)  # 将当前目录下所有文件加入到dir_res
    for file_info in dir_res:
        file = file_info.split(" ")[-1]
        file_path = os.path.join(ftp.pwd(), file)
        if file_info.startswith("d"):
            # drw-r--r--    1 <USER>     <GROUP>     1105890161 Oct 08 16:43 ts1-2021-10-08-04-38
            recurse_pcap_list += ftp_server_recurse_pcap(ftp, file_path)
            ftp.cwd('..')
        else:
            if file_path.endswith(('.pcap', '.cap', '.pcapng')):
                recurse_pcap_list.append(file_path)
    return recurse_pcap_list


def get_country_image(area_code):
    """ 根据地区码获取国旗 """
    url = ""
    if area_code != 'B1' and area_code != "":
        url = "/api/v1/image_country/%s.png" % area_code.lower()
    return url


def run_command(command, ignore_error=False, timeout=None):
    """ 
    Run bash command, return stdout or stderr, all data is saved in RAM, 
    So do not use this function when data is huge!
    """
    proc = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    try:
        output, error = proc.communicate(timeout=timeout)

        if proc.returncode != 0 and not ignore_error:
            return False, str(error, 'utf-8')
        else:
            return True, output.decode("utf-8")
    except subprocess.TimeoutExpired:
        proc.kill()
        return False, 'Timeout'


def pcap_fix(src_pcap_path):
    output_name = src_pcap_path + ".fixed"
    ret, _ = run_command("pcapfix -d -k -o '%s' '%s'" % (output_name, src_pcap_path), timeout=20)

    if ret and os.path.isfile(output_name):
        os.remove(src_pcap_path)
        os.rename(output_name, src_pcap_path)
        print("Pcapfix: fixed file %s successed." % src_pcap_path)
    else:
        if os.path.isfile(output_name):
            os.remove(output_name)
        print("Pcapfix: %s fix failed!!!" % src_pcap_path)


# 可以使用select监控多个queue而不使用轮询的方式获取队列，不用定时获取，也不用阻塞，有消息则触发
class PollableQueue(queue.Queue):
    def __init__(self, maxsize=0):
        super().__init__(maxsize)
        # Create a pair of connected sockets
        if os.name == 'posix':
            self._putsocket, self._getsocket = socket.socketpair()
        else:
            # Compatibility on non-POSIX systems
            server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server.bind(('127.0.0.1', 0))
            server.listen(1)
            self._putsocket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._putsocket.connect(server.getsockname())
            self._getsocket, _ = server.accept()
            server.close()

    def fileno(self):
        return self._getsocket.fileno()

    def put(self, item):
        super().put(item)
        self._putsocket.send(b'x')

    def get(self):
        self._getsocket.recv(1)
        return super().get()


def supervisorctl_host_service(action, service):
    if action not in ['stop', 'start', 'restart']:
        return

    cmd = "supervisorctl -u %s -p %s restart %s" % (SupervisorConfig.User, SupervisorConfig.Password, service)

    app.send_task('host_celery.tasks.run_command_in_host', args=[cmd], queue='host_task', routing_key='host_task')


# 组装需要编译的Json文本格式
def get_hdp_feature_str_from_query(query):
    key_li = ['vulName', 'cve', 'threatScore', 'threatFlag', 'lockheedKillchainCN', 'lockheedKillchainEN',
              'featureGroup', 'featureContent']
    feature_dict = {}
    feat_str = None
    if not isinstance(query, dict) or not query:
        return None
    for key in key_li:
        if key not in query.keys():
            return None
        feature_dict[key] = query[key]

    try:
        feat_str = json.dumps(feature_dict)
    except Exception as e:
        print("Error: " + str(e))

    return feat_str + '\n'


def generate_random_string(length):
    characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    return ''.join(random.choices(characters, k=length))