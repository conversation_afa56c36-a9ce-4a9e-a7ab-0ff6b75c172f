.grid_table{
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 20px;
  height: 390px;
  justify-content: center;


  .table_content{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    flex:1;
  }

  .table_content_giant_item {
    grid-template-columns: 1fr;
  }

}


.table_item {
  height: 140px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  
  .item_option{
    border-top: 1px solid #e0e0e0;
  }

  .item_tags{
    height: 50px;
    overflow: auto;
  }
}