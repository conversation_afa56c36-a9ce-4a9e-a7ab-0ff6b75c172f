# -*- coding: utf-8 -*-
# @Time    : 2019-05-31 14:10
# <AUTHOR> wu
# @File    : operate.py
# @Software: PyCharm

"""module explore"""

import datetime
import json
import time
import hashlib
import os
from croniter import croniter
from flask import request
from flask_restful import Resource
from config.config import CustomizedPcapPath, NdrLog, NASConfig
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from utils.database import MongoDB, get_es_client
from utils.json_format import JSONEncoder
from utils.param_check import Validator, check_flask_args
from utils.best_show_size import size_format
from celery_tasks import explore_task
from api_1_0.auth.auths import Authenticate
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def create_task_id():
    """create task id"""
    md5_str = hashlib.md5(str(time.clock()).encode('utf-8'))
    return md5_str.hexdigest()


# parameter check
def param_check(name, feature_grp_list, mongodb, pcap_list, user_id, dir_name, task_type, data_type):
    rst = []
    if name == "":
        rst.append('The task name cannot be empty')
    elif len(name) > 255:
        rst.append('Task name cannot exceed 255 characters in length')
    if data_type != 'pcap' and data_type != 'other':
        rst.append('The task type wrong')
    if task_type == "singleTask":
        try:
            if pcap_list:
                for file in pcap_list:
                    if not os.path.exists(CustomizedPcapPath.Path + dir_name + '/' + file):
                        rst.append(file + ' not exist')
            else:
                rst.append("pcap_list cannot be empty")
        except Exception as e:
            rst.append(e)
    if data_type == 'pcap':
        if feature_grp_list:
            for grp_id in feature_grp_list:
                item = mongodb.find_one('feature_group', {'$and': [{'groupId': grp_id}, {'createByUserId': user_id}]})
                if not item:
                    rst.append("Feature group [ %s ] is not exist" % grp_id)
        else:
            rst.append("Feature group list cannot be empty")
    return rst


def get_duration_time(s_time, e_time):
    """
    :param s_time: 开始时间
    :param e_time: 结束时间
    :return: 时间间隔(秒)
    """
    if s_time == "":
        return 0
    start = int(time.mktime(time.strptime(s_time, "%Y-%m-%d %H:%M:%S")))
    if e_time != "":
        end = int(time.mktime(time.strptime(e_time, "%Y-%m-%d %H:%M:%S")))
    else:
        end = int(time.time())
    return end - start


class ExploreTaskWithId(Resource):
    """
        单个探索任务的获取；
        单个探索任务的删除；
        单个探索任务的修改；
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    # get one task
    def get(self, task_id):
        """
        :param task_id:
        :return:
        """
        rst = self.mongodb.find_one('back_explore', {"taskId": task_id},
                                    {"_id": 0,
                                     "taskName": 1,
                                     "type": 1,
                                     "taskId": 1,
                                     "createByUserId": 1,
                                     "createByUserName": 1,
                                     "curFeatureGrpList": 1,
                                     "createTime": 1,
                                     "pcapList": 1,
                                     "sidList": 1,
                                     "startTime": 1,
                                     "endTime": 1,
                                     "status": 1,
                                     "process": 1,
                                     "flowSize": 1,
                                     "celeryId": 1,
                                     "statisticsInfo": 1
                                     })
        if rst is None:
            return flask_response("Not found", False, {})

        if rst['createByUserId'] != self.user_id and self.user_name != 'admin':
            LOG.error("Insufficient permissions.")
            return flask_response("Insufficient permissions.", False, {})

        if rst["status"] == "finish":
            # 任务执行完成后，每次重新聚合，以确保获取最新统计数据
            self.mongodb.update(
                "back_explore",
                {"taskId": task_id},
                {"statisticsInfo": explore_task.get_es_statistics_info(rst["celeryId"], "celeryId")})
            rst = self.mongodb.find_one('back_explore', {"taskId": task_id},
                                        {"_id": 0,
                                         "taskName": 1,
                                         "type": 1,
                                         "taskId": 1,
                                         "createByUserId": 1,
                                         "createByUserName": 1,
                                         "curFeatureGrpList": 1,
                                         "createTime": 1,
                                         "pcapList": 1,
                                         "sidList": 1,
                                         "startTime": 1,
                                         "endTime": 1,
                                         "status": 1,
                                         "process": 1,
                                         "flowSize": 1,
                                         "celeryId": 1,
                                         "statisticsInfo": 1
                                         })
        # 获取统计信息数据
        rst["statisticsInfo"]["flowSize"] = size_format(rst["flowSize"] / 1024)
        rst["statisticsInfo"]["durationTime"] = \
            get_duration_time(rst['startTime'], rst['endTime'])
        rst.pop("flowSize")
        rst["createTime"] = int(time.mktime(time.strptime(rst["createTime"], "%Y-%m-%d %H:%M:%S")) * 1000)
        if rst["startTime"] != "":
            rst["startTime"] = int(time.mktime(time.strptime(rst["startTime"], "%Y-%m-%d %H:%M:%S")) * 1000)
        if rst["endTime"] != "":
            rst["endTime"] = int(time.mktime(time.strptime(rst["endTime"], "%Y-%m-%d %H:%M:%S")) * 1000)
        col_fmt = JSONEncoder().encode(rst)
        col_rst = json.loads(col_fmt)
        return flask_response("", True, col_rst)

    # delete task
    def delete(self, task_id):
        """
        :param task_id:
        :return:
        """
        task = self.mongodb.find_one("back_explore", {"taskId": task_id})
        # scheduler delete process
        if task is None:
            message = 'Task does not exist.'
            LOG.error("task: %s delete failed.%s" % (task_id, message))
            ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]删除失败:%s" % (task_id, message))
            return flask_response(message, False, {})

        if task['createByUserId'] != self.user_id and self.user_name != 'admin':
            LOG.error("Insufficient permissions.")
            ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务删除失败：权限不足。")
            return flask_response("Insufficient permissions.", False, {})
        if task['process'] != 1:
            return flask_response("探索任务[%s]删除失败,没有执行完成" % task["taskName"], False, {})
        if task['taskType'] == 'singleTask':
            for file_name in task["pcapList"]:
                file_obj = self.mongodb.find_one('customized_pcap',
                                                 {"fileName": file_name, "dir_name": task["dir_name"]})
                if file_obj["taskList"]:
                    if task['taskName'] in file_obj["taskList"]:
                        file_obj["taskList"].remove(task['taskName'])
                    self.mongodb.update('customized_pcap', {"fileName": file_name, "dir_name": task["dir_name"]},
                                        {'taskList': file_obj["taskList"]})
        self.mongodb.delete("back_explore", {"taskId": task_id})
        es_client = get_es_client()
        query = {'query': {'match': {'taskId': task_id}}}
        es_client.delete_by_query(index='rule-eve,ioc-eve,model-eve', body=query, conflicts='proceed',
                                  wait_for_completion=False)
        LOG.info("task: %s, %s deleted success." % (task["taskName"], task["taskId"]))
        ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]删除成功" % task["taskName"])
        return flask_response("Deleted successfully.", True, {})

    def task_modify(self, old_task, task_id, action):
        """
        :param old_task:
        :param task_id:
        :param action:
        :return:
        """
        if action == 'start':
            es_client = get_es_client()
            query = {'query': {'match': {'taskId': task_id}}}
            es_client.delete_by_query(index='rule-eve,ioc-eve,model-eve,file-eve', body=query, conflicts='proceed',
                                      wait_for_completion=False)
            if old_task['taskType'] == 'singleTask':
                f_list = []
                for file in old_task["pcapList"]:
                    f_list.append(CustomizedPcapPath.Path + old_task["dir_name"] + '/' + file.split('/')[-1])
                self.mongodb.update('back_explore', {"taskId": task_id},
                                    {'action': action, 'process': 0, 'pcapInfo': []})
                if old_task['type'] == 'pcap':
                    explore_task.start_task.delay(task_id, f_list)
                elif old_task['type'] == 'other':
                    explore_task.start_file_task.delay(task_id, f_list)
            else:
                # 定时任务需要立即更新状态，否则不会被调度到
                self.mongodb.update('back_explore', {"taskId": task_id}, {
                    'action': action, 'process': 0, 'status': 'running',
                    "endTime": "", 'replay_endTime': '', 'pcapInfo': []})
            LOG.info("task: %s start success." % task_id)
            ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]执行成功." % old_task['taskName'])
            return flask_response("", True, {})
        if action == 'stop':
            if old_task['type'] == 'pcap':
                explore_task.stop_task.delay(task_id)
            elif old_task['type'] == 'other':
                explore_task.stop_file_task.delay(task_id)
            self.mongodb.update('back_explore', {"taskId": task_id}, {'action': action})
            LOG.info("task: %s stop success." % task_id)
            ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]停止成功." % old_task['taskName'])
            return flask_response("", True, {})
        LOG.error("task: %s %s failed.Reason: No such operation" % (task_id, action))
        ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]修改失败：不支持此操作。" % old_task['taskName'])
        return flask_response('No such operation', False, {})

    # Modify task
    @check_flask_args(Validator("explore_update_schema"), request)
    def put(self, task_id, **kwargs):
        """
        :param task_id:
        :return:
        """
        old_task = self.mongodb.find_one('back_explore', {"taskId": task_id})
        if old_task is None:
            LOG.error("task: %s update failed. The task does not exist." % task_id)
            return flask_response("The task does not exist.", False, {})
        if old_task['createByUserId'] != self.user_id and self.user_name != 'admin':
            LOG.error("Insufficient permissions.")
            ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]修改失败：权限不足。" % old_task['taskName'])
            return flask_response("Insufficient permissions.", False, {})
        action = kwargs["action"]
        if action == 'update':
            if not old_task['endTime']:
                message = "Task is running, can not update"
                ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]修改失败：处于运行状态。" % old_task['taskName'])
                return flask_response(message, False, {})
            feature_grp_list = kwargs["curFeatureGrpList"] if kwargs["curFeatureGrpList"] else old_task[
                'curFeatureGrpList']
            name = kwargs["taskName"] if kwargs["taskName"] else old_task['taskName']
            if old_task['taskType'] == 'singleTask':
                if 'pcapList' not in kwargs:
                    return flask_response("'pcapList' is request!", False, {})
                pcap_list = kwargs["pcapList"]
                dir_name = kwargs["dir_name"] if kwargs["dir_name"] else old_task['dir_name']
                rst = param_check(name, feature_grp_list, self.mongodb, pcap_list, self.user_id, dir_name,
                                  old_task['taskType'], old_task['type'])
                if rst:
                    LOG.error("task: %s %s failed.Reason: %s" % (task_id, action, rst))
                    ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]修改失败：%s。" % (old_task['taskName'], rst))
                    return flask_response(rst, False, {})
                file_info = []
                # 之前的pcap表更新
                for file in old_task['pcapList']:
                    file_obj = self.mongodb.find_one('customized_pcap',
                                                     {"fileName": file, "dir_name": old_task["dir_name"]})
                    file_obj["taskList"].remove(old_task['taskName'])
                    self.mongodb.update('customized_pcap', {"fileName": file, "dir_name": old_task["dir_name"]},
                                        {'taskList': file_obj["taskList"]})
                # 当前的pcap表更新
                for file in pcap_list:
                    file_obj = self.mongodb.find_one('customized_pcap',
                                                     {"fileName": file, "dir_name": dir_name})
                    file_obj["taskList"].append(name)
                    self.mongodb.update('customized_pcap', {"fileName": file, "dir_name": dir_name},
                                        {'taskList': file_obj["taskList"]})
                    pcap_name = CustomizedPcapPath.Path + dir_name + '/' + file.split('/')[-1]
                    file_info.append({'pcapName': pcap_name, 'pcapSize': 0, 'pcapStatus': 'ready'})
                self.mongodb.update('back_explore', {"taskId": task_id},
                                    {'action': action,
                                     'taskName': name,
                                     "pcapList": pcap_list,
                                     "dir_name": dir_name,
                                     'pcapInfo': file_info,
                                     "curFeatureGrpList": feature_grp_list})
                # 是否立刻执行调度
                if kwargs['executeNow']:
                    new_task = self.mongodb.find_one('back_explore', {"taskId": task_id})
                    ndr_log_to_box(NdrLog.Type.OPERATE, "单次任务[%s]重新执行成功." % name)
                    return self.task_modify(new_task, task_id, 'start')
            else:
                # translate '8:12:32' to ['8', '12', '32'] or '12:32' to ['12', '32']
                base_time = kwargs['schedule']['baseTime'].split(':')
                schedule_expr = ""
                if kwargs['schedule']['period'] == 'hour':
                    schedule_expr = '%s * * * *' % (base_time[0])
                elif kwargs['schedule']['period'] == 'day':
                    schedule_expr = '%s %s * * *' % (base_time[1], base_time[0])
                elif kwargs['schedule']['period'] == 'week':
                    schedule_expr = '%s %s * * %d' % (base_time[1], base_time[0], kwargs['schedule']['baseDate'])
                elif kwargs['schedule']['period'] == 'month':
                    schedule_expr = '%s %s %d * *' % (base_time[1], base_time[0], kwargs['schedule']['baseDate'])
                # 由于根据croniter只能精确到分钟，需要在执行任务时延迟到秒执行
                task_delay = base_time[-1]
                schedule = kwargs['schedule']
                # 根据当前时间计算任务执行的时间和下次任务执行的时间，get_next只能计算到分钟，需要再加上秒级数字
                cron = croniter(schedule_expr, datetime.datetime.now())
                next_time = cron.get_next(datetime.datetime) + datetime.timedelta(seconds=int(task_delay))

                self.mongodb.update(
                    'back_explore', {"taskId": task_id},
                    {
                        'action': action,
                        # 是否重新处理数据源所有数据
                        'exploredPcapList': old_task['exploredPcapList'] if kwargs['dealWithAll'] == False else [],
                        "sourceData": kwargs['sourceData'],
                        'schedule_expr': schedule_expr,  # 定时任务使用，cortab调度时间语法
                        "schedule": schedule,
                        "taskDelay": task_delay,  # 定时任务精确秒级时间
                        "nextTime": next_time,  # 下次定时任务执行时间，定时任务使用
                        "curFeatureGrpList": feature_grp_list,
                        "status": old_task['status'] if kwargs['executeNow'] == False else 'running',
                        "replay_endTime": old_task['replay_endTime'] if not kwargs['executeNow'] else '',
                        'preTime': old_task['preTime'] if kwargs['executeNow'] == False else datetime.datetime.now(),
                        'celeryId': old_task['celeryId'] if kwargs['dealWithAll'] == False else '',
                        "pcapInfo": old_task['pcapInfo'] if kwargs['dealWithAll'] == False else [],
                        # 处理所有数据时需要清空起止时间，任务重新执行的时候再赋值
                        "startTime": old_task['startTime'] if kwargs['dealWithAll'] == False else '',
                        "endTime": old_task['endTime'] if kwargs['dealWithAll'] == False else ''
                    }
                )

                # 是否立刻执行调度
                if kwargs['executeNow']:
                    self.mongodb.update('back_explore', {"taskId": task_id}, {'process': 0})
                    if old_task['type'] == 'pcap':
                        explore_task.start_timed_task.delay(task_id)
                    elif old_task['type'] == 'other':
                        explore_task.start_timed_file_task.delay(task_id)
                    ndr_log_to_box(NdrLog.Type.OPERATE, "定时任务[%s]重新执行成功." % old_task['taskName'])

            return flask_response("探索任务[%s]修改成功" % old_task['taskName'], True, {})

        return self.task_modify(old_task, task_id, action)


class ExploreTask(Resource):
    """
        创建探索任务；
        获取所有探索任务；
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    # create task
    @check_flask_args(Validator("explore_create_schema"), request)
    def post(self, **kwargs):
        """
        :return:
        """
        name = kwargs["taskName"]
        dir_name = kwargs["dir_name"]
        task_name = self.mongodb.find_one('back_explore',
                                          {"$and": [{"taskName": name}, {"createByUserId": self.user_id}]})
        if task_name is not None:
            message = 'The explore task [%s] already exists.' % name
            LOG.error("task: created failed.Reason: %s" % message)
            ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]创建失败：已存在。" % name)
            return flask_response(message, False, {})
        pcap_list = kwargs["pcapList"]
        feature_grp_list = kwargs["curFeatureGrpList"]
        # 参数检查
        rst = param_check(name, feature_grp_list, self.mongodb, pcap_list, self.user_id, dir_name, kwargs['taskType'], kwargs["type"])
        if rst:
            LOG.error("task: %s created failed.Reason: %s" % (name, rst))
            ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]创建失败：%s。" % (name, rst))
            return flask_response(rst, False, {})
        schedule = ''
        schedule_expr = ''
        next_time = ''
        task_delay = ''
        if kwargs['taskType'] == 'timedTask':
            nas_data = self.mongodb.find_one('nas_manager', {'serverId': kwargs['sourceData']}, {'aliasName'})
            if nas_data:
                dir_name = nas_data['aliasName']
            else:
                return flask_response("timeTask 数据源有误 ", False, {})
            # def generate_crontab_expression(schedule):
            # translate '8:12:32' to ['8', '12', '32'] or '12:32' to ['12', '32']
            base_time = kwargs['schedule']['baseTime'].split(':')
            if kwargs['schedule']['period'] == 'hour':
                schedule_expr = '%s * * * *' % (base_time[0])
            elif kwargs['schedule']['period'] == 'day':
                schedule_expr = '%s %s * * *' % (base_time[1], base_time[0])
            elif kwargs['schedule']['period'] == 'week':
                schedule_expr = '%s %s * * %d' % (base_time[1], base_time[0], kwargs['schedule']['baseDate'])
            elif kwargs['schedule']['period'] == 'month':
                schedule_expr = '%s %s %d * *' % (base_time[1], base_time[0], kwargs['schedule']['baseDate'])

            # 由于根据croniter只能精确到分钟，需要在执行任务时延迟到秒执行
            task_delay = base_time[-1]
            schedule = kwargs['schedule']
            # 根据当前时间计算任务执行的时间和下次任务执行的时间，get_next只能计算到分钟，需要再加上秒级数字
            cron = croniter(schedule_expr, datetime.datetime.now())
            next_time = cron.get_next(datetime.datetime) + datetime.timedelta(seconds=int(task_delay))

        file_info = []
        for file in pcap_list:
            pcap_name = CustomizedPcapPath.Path + dir_name + '/' + file.split('/')[-1]
            file_info.append({'pcapName': pcap_name, 'pcapSize': 0, 'pcapStatus': 'ready'})
        body = {
            "taskType": kwargs['taskType'],
            "type": kwargs['type'],
            "sourceData": kwargs['sourceData'],
            'schedule_expr': schedule_expr,  # 定时任务使用，cortab调度时间语法
            "schedule": schedule,
            "taskDelay": task_delay,  # 定时任务精确秒级时间
            "taskName": name,  # 任务名称
            "taskId": create_task_id(),  # 任务 ID
            "createByUserId": self.user_id,
            "createByUserName": self.user_name,
            "action": "start",  # 任务操作动作
            "createTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 任务创建时间
            "pcapList": pcap_list,  # 自定义回放 pcap 文件列表
            "curFeatureGrpList": feature_grp_list,  # 规则列表
            "flowSize": 0,  # 回放周期内的 pcap 文件总大小(字节)
            "exploredPcapList": [],  # 已经回放完成的 pcap 文件列表
            "replayInterface": "",  # 回放使用的虚拟接口
            "celeryId": "",  # celery idCelery
            "timedCeleryId": "",  # 定时任务使用，用来保存定时任务的celeryId, 而定时任务的celeryId则失去原有意义，只作页面搜索使用
            "startTime": '',  # 任务开始时间
            "endTime": "",  # 任务结束时间
            "replay_endTime": "",  # 回放结束时间，表明数据包回放已经完成，但是任务也许还在执行，会等待刷新数据后结束任务
            "preTime": '',  # 上次定时任务执行时间
            "nextTime": next_time,  # 下次定时任务执行时间，定时任务使用
            # 任务当前的状态：preprocessing, running, pause, stop ,finish, failed,
            # ES 是否刷新状态，任务结束后会持续刷新ES以保证数据全部统计完全
            "status": "preprocessing" if kwargs['taskType'] == 'singleTask' else 'running',
            "dir_name": dir_name,
            "process": 0.0,  # 任务执行进度
            "pcapInfo": file_info,  # 用于记录每个包的回放状态信息
            # pcap统计结果信息
            'pcapStatisticsInfo': {
                'logCount': 0,
                "conn": 0,
                "http": 0,
                "dns": 0,
                "tls": 0
            },
            "statisticsInfo": {  # 统计信息
                "vul": {
                    "killChains": [],
                    "logCount": 0,
                    "hitCount": 0,
                    "threatScore": 0,
                    "threatLevel": {
                        "high": 0,
                        "medium": 0,
                        "low": 0
                    }
                },
                "ioc": {
                    "killChains": [],
                    "logCount": 0,
                    "hitCount": 0,
                    "threatScore": 0,
                    "threatLevel": {
                        "high": 0,
                        "medium": 0,
                        "low": 0
                    }
                },
                "model": {
                    "killChains": [],
                    "logCount": 0,
                    "hitCount": 0,
                    "threatScore": 0,
                    "threatLevel": {
                        "high": 0,
                        "medium": 0,
                        "low": 0
                    }
                },
                "file_log": {
                    "killChains": [],
                    "logCount": 0,
                    "hitCount": 0,
                    "severity": []
                }
            }
        }
        self.mongodb.insert_one("back_explore", body)
        # 单次任务执行code，定时任务由timed_task_poll轮询任务启动，这里只更新定时任务的基础信息到MongoDB
        if kwargs['taskType'] == 'singleTask':
            f_list = []
            for file in pcap_list:
                file_obj = self.mongodb.find_one('customized_pcap', {"fileName": file, "dir_name": kwargs["dir_name"]})
                file_obj["taskList"].append(body['taskName'])
                self.mongodb.update('customized_pcap', {"fileName": file, "dir_name": kwargs["dir_name"]},
                                    {'taskList': file_obj["taskList"]})
                f_list.append(CustomizedPcapPath.Path + dir_name + '/' + file.split('/')[-1])
            if kwargs['type'] == 'pcap': # 流量包
                explore_task.start_task.delay(body['taskId'], f_list)
            elif kwargs['type'] == 'other': # 文件
                explore_task.start_file_task.delay(body['taskId'], f_list)
        else:  # 定时任务是否立刻执行调度
            if kwargs['executeNow']:
                if kwargs['type'] == 'pcap': # 流量包
                    explore_task.start_timed_task.delay(body['taskId'])
                elif kwargs['type'] == 'other': # 文件
                    explore_task.start_timed_file_task.delay(body['taskId'])
                self.mongodb.update('back_explore', {"taskName": name}, {
                    'startTime': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                ndr_log_to_box(NdrLog.Type.OPERATE, "探索定时任务[%s]立即执行成功." % body['taskName'])
            else:
                self.mongodb.update('back_explore', {"taskName": name}, {'startTime': str(next_time)})
        LOG.info("task: %s, %s created success" % (body["taskName"], body["taskId"]))
        ndr_log_to_box(NdrLog.Type.OPERATE, "探索任务[%s]创建成功。" % name)
        data = {'taskId': body["taskId"]}

        return flask_response("success", True, data)

    # 查询条件解析
    def query_condition(self, create_start_time, create_end_time, task_name, task_type):
        """
        :param create_start_time:
        :param create_end_time:
        :param task_name:
        :param kill_chains:
        :param vul_min_count:
        :param vul_max_count:
        :param threat_min_score:
        :param threat_max_score:
        :return:
        """
        condition = {"message": "", "flag": True, "data": {"$and": []}}

        try:
            start = int(time.mktime(time.strptime(create_start_time, "%Y-%m-%d %H:%M:%S")))
            end = int(time.mktime(time.strptime(create_end_time, "%Y-%m-%d %H:%M:%S")))
        except ValueError as msg:
            condition["flag"] = False
            condition["message"] = "Date or time format error"
            LOG.error(msg)
            return condition

        if start > end:
            condition["flag"] = False
            condition["message"] = "End time cannot be earlier than start time"
            return condition
        condition['data']["$and"].append({"createTime": {'$gte': create_start_time, '$lte': create_end_time}})
        if task_name:
            condition['data']["$and"].append({"taskName": {'$regex': task_name, '$options': 'i'}})
        if self.user_name != 'admin':
            condition['data']["$and"].append({"createByUserId": self.user_id})
        if task_type != '':
            condition['data']['$and'].append({'taskType': task_type})

        return condition

    # get all task
    @check_flask_args(Validator("explore_get_all_schema"), request)
    def get(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        try:
            create_start_time = time.strftime("%Y-%m-%d %H:%M:%S",
                                              time.localtime((int(kwargs["createStartTime"]) / 1000)))
            create_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime((int(kwargs["createEndTime"]) / 1000)))
            sort = kwargs["sort"]
            reverse = 1 if kwargs["reverse"] == 'false' else -1
            page = int(kwargs["page"])
            page_size = int(kwargs["pageSize"])
            condition = self.query_condition(
                create_start_time,
                create_end_time,
                kwargs["taskName"],
                kwargs['taskType'])
            if not condition['flag']:
                return flask_response(condition['message'], False, {})
            t_count = self.mongodb.find('back_explore', condition['data']).count()
            data = {"count": t_count}
            rst = self.mongodb.find(
                'back_explore', condition['data'], {
                    'pcapList': 0, 'exploredPcapList': 0}).sort([(sort, reverse)]).limit(page_size).skip(
                (page - 1) * page_size)

            if rst is None:
                return flask_response('Not found', False, {})

            data["detail"] = self.format_data(rst)
            data["page"] = page
            data["pageSize"] = page_size
            return flask_response("", True, data)
        except Exception as err:
            message = 'error.Reason: %s' % err
            LOG.error(err)
            return flask_response(message, False, {})

    def format_data(self, raw_data):
        def get_feature_group_list(cur_feat_grp_list):
            feature_goup_list = []
            for grp in cur_feat_grp_list:
                mongo_data = self.mongodb.find_one('feature_group', {'groupId': grp}, {'groupName': 1})
                if mongo_data:
                    feature_goup_list.append({'groupName': mongo_data['groupName'], 'groupId': grp})

            return feature_goup_list

        def get_source_data(server_id):
            ret = ''
            if server_id:
                mongo_data = self.mongodb.find_one('nas_manager', {'serverId': server_id}, {'aliasName': 1})
                if mongo_data:
                    ret = {'serverId': server_id, 'aliasName': mongo_data['aliasName']}

            return ret

        def get_timestamp_from_str(strtime_obj):
            return '' if not strtime_obj else int(time.mktime(time.strptime(strtime_obj, "%Y-%m-%d %H:%M:%S")) * 1000)

        def get_timestamp_from_datetime(datetime_obj):
            return '' if not datetime_obj else time.mktime(
                datetime_obj.timetuple()) * 1000.0 + datetime_obj.microsecond / 1000.0

        cols = []
        for col in raw_data:
            cols.append(
                {
                    'taskId': col['taskId'],
                    'type': col['type'],
                    'celeryId': col['celeryId'],
                    'taskName': col['taskName'],
                    'taskType': col['taskType'],
                    'startTime': get_timestamp_from_str(col['startTime']),
                    'endTime': get_timestamp_from_str(col['endTime']),
                    'nextTime': get_timestamp_from_datetime(col['nextTime']),
                    'preTime': get_timestamp_from_datetime(col['preTime']),
                    'schedule': col['schedule'],
                    'process': col['process'],
                    'dir_name': col['dir_name'],
                    'sourceData': get_source_data(col['sourceData']),
                    'curFeatureGrpList': get_feature_group_list(col['curFeatureGrpList']),
                    'statisticsInfo': {
                        **col['statisticsInfo'],
                        'flowSize': size_format(col["flowSize"] / 1024),
                        'durationTime': get_duration_time(col['startTime'], col['endTime']),
                        'pcap': col['pcapStatisticsInfo']
                    }
                }
            )

        return cols


class ExploreTaskPcap(Resource):
    """
    查看任务处理的pcap信息，包含数据包名，处理阶段
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @check_flask_args(Validator("explore_task_pcap_schema"), request)
    def get(self, **kwargs):
        detail = []
        status_dict = {
            'failed': '回放失败',
            'success': '回放完成',
            'revoke': '回放撤销',
            'ready': '准备就绪',
            'download success': '下载完成',
            'download failed': '下载失败'
        }

        raw_data = self.mongodb.find_one('back_explore', {'taskName': kwargs['taskName']}, {
            'pcapList': 0, 'exploredPcapList': 0})

        if not raw_data:
            return flask_response("获取任务数据失败！", False, {})

        if raw_data['taskType'] == 'singleTask':
            prefix_len = len(os.path.join(CustomizedPcapPath.Path, raw_data['dir_name']))
        else:
            nas_data = self.mongodb.find_one('nas_manager', {'serverId': raw_data["sourceData"]})
            if not nas_data:
                return flask_response("获取服务器数据失败！", False, {})

            if nas_data['serverType'] == 'ftp':
                prefix_len = 0
            else:
                prefix_len = len(os.path.join(NASConfig.Path, raw_data['dir_name']))

        for pcap in raw_data['pcapInfo']:
            pcap_path = pcap['pcapName'][prefix_len:]
            detail.append({'pcapName': pcap_path, 'status': status_dict[pcap['pcapStatus']]})

        return flask_response("", True, {'detail': detail, 'count': len(detail)})
