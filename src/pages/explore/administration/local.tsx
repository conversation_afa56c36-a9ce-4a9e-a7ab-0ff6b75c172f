import React, { useEffect, useState, forwardRef } from 'react';
import { Form, Select, Modal, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { getAllOffLineMenu } from '@/services/explore';
import style from './style.less';
const { Option } = Select;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const MAX_ALLOW_SIZE = 20 * 1024 * 1024 * 1024; // 最大允许上传20G
const Local = forwardRef((Props: any, ref: any) => {
  const { getFileList } = Props;
  const [form] = Form.useForm();
  const [menu, setmenu] = useState([]);
  const [upDisabled, setUpDisabled] = useState(true);
  const [accept, setAccept] = useState('.cap,.pcap,.pcapng');
  const [readyList, setreadyList] = useState([]);
  const getMemu = () => {
    getAllOffLineMenu().then(res => {
      if (res.flag) {
        setmenu(res.data.detail);
      }
    });
  };
  const changeDir = (e: any) => {
    const flagItem = menu.find(item => {
      return item.dirName === e;
    });
    if (flagItem.type == '1') {
      setAccept('.cap,.pcap,.pcapng');
    } else {
      setAccept('');
    }
    form.setFieldsValue({ type: flagItem.type });
  };
  // 选择文件
  const onFileChange = (e: { target: { files: any } }) => {
    const files = e.target.files;
    let status = true;
    for (const item of files) {
      const index = item.name.lastIndexOf('.');
      const name = item.name.substring(index + 1, item.name.length);
      if (accept) {
        if (name.includes('cap') || name.includes('pacp') || name.includes('pcapng')) {
          status = true;
        } else {
          message.error('请检查上传文件类型');
          status = false;
          break;
        }
      }
    }
    if (!status) {
      return;
    }
    const largeList = checkFileSize(files);
    if (largeList.length > 0) {
      // notification.info({ message: '文件大小不能超过20G' })
      Modal.info({
        title: '提示',
        content: (
          <div>
            <div>不能上传大于20G的文件，以下文件大于20G</div>
            {largeList.map(v => (
              <div key={v.file.name}>{v.file.name}</div>
            ))}
          </div>
        ),
      });
      return;
    }

    const readyLists = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      readyLists.push({ file });
    }
    setreadyList(readyLists);
    getFileList(readyLists);
  };
  // 过滤文件大于20G的文件
  const checkFileSize = (files: string | any[]) => {
    const len = files.length;
    const result = [];

    for (let i = 0; i < len; i++) {
      const file = files[i];
      if (file.size > MAX_ALLOW_SIZE) {
        result.push({ file });
      }
    }
    return result;
  };
  useEffect(() => {
    getMemu();
  }, []);
  return (
    <div>
      <Form ref={ref} form={form} {...formItemLayout}>
        <Form.Item label="上传目录" name="dir">
          <Select
            onChange={e => {
              changeDir(e);
              setUpDisabled(false);
            }}
          >
            {menu.map((item: { createByUserId: string; dirName: string }) => {
              return (
                <Option value={item.dirName} key={item.dirName}>
                  {item.dirName}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item name="type" noStyle></Form.Item>
        <Form.Item label="选择文件">
          <div className={style.dirName}>
            <label htmlFor="pcap-upload" className={style.pcapLabel}>
              <input
                id="pcap-upload"
                disabled={upDisabled}
                className={style.pcapInput}
                type="file"
                accept={accept}
                multiple
                onChange={onFileChange}
              />
              <span className={style.pcapUploadBtn}>
                <PlusOutlined />
              </span>
            </label>
            <div style={{ marginLeft: '10px' }}>已选择{readyList.length}个文件</div>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
});

export default Local;
