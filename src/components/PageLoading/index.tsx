import React, { useEffect, useState, CSSProperties } from 'react';
import { Icon } from 'antd';
import { useIntl } from 'umi';
import classnames from 'classnames';

import styles from './styles.less';

interface Props {
  style?: CSSProperties;
  className?: string;
  delay?: number;
}

const PageLoding: React.FC<Props> = ({ className, delay, style }) => {
  const [loading, setLoading] = useState(false);
  const { formatMessage } = useIntl();

  useEffect(() => {
    const id = setTimeout(() => {
      setLoading(true);
    }, delay || 500);
    return () => {
      clearTimeout(id);
    };
  });

  if (!loading) return null;
  const cls = classnames(styles.mask, className);
  return (
    <div className={cls} style={style || {}}>
      <Icon type="loading" spin />
      <span className={styles.text}>
        {formatMessage({
          id: 'global.loading.text',
        })}
      </span>
    </div>
  );
};

export default PageLoding;
