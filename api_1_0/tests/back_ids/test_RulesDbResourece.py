#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: test_RulesDbResourece.py
# @time: 2019/07/11
# @software: PyCharm

from io import BytesIO
import unittest
from api_1_0.router import app, api
import json
from api_1_0.back_rules.rules_content_manager import RulesDbResourece


class TestRulesDbResourece(unittest.TestCase):

    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    # 非法文件上传
    def test_lack_of_required_parameters(self):
        data = {
            'file': (BytesIO(b'my test file'), 'test_file.txt')
        }
        resp_dict = self.post(RulesDbResourece, data)
        self.assertEqual(resp_dict["flag"], False)

    def post(self, url, data):
        response = self.client.post(api.url_for(url), data=data, content_type='multipart/form-data')
        resp_dict = json.loads(response.data)
        return resp_dict

    def get(self, url):
        response = self.client.get(api.url_for(url))
        resp_dict = json.loads(response.data)
        return resp_dict


if __name__ == '__main__':
    suite = unittest.TestSuite()
    suite.addTest()
    # 执行测试
    runner = unittest.TextTestRunner()
    runner.run(suite)
