# -*- coding: utf-8 -*-
# @Time    : 2020-08-03 14:10
# <AUTHOR> wu
# @File    : customize_ioc_manager.py
# @Software: PyCharm
"""
自定义 ioc 管理
"""
import json
import os, shutil
import sqlite3
import xlrd
import uuid
import time
from flask import request, send_file, make_response
from flask_restful import Resource

from api_1_0.auth.auths import Authenticate
from api_1_0.knowledge.get_killchains import GetKillchains
from utils import param_check
from utils.database import MongoDB
from utils.json_format import JSONEncoder
from utils.param_check import Validator
from utils.utils import flask_response
from utils.logger import get_ndr_logger, LogToDb
from utils.cluster import *
from config.config import IocCfg, NdrLog, KnowledgeNDR
import zipfile
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)

ioc_killchain_map = {
    "侦查跟踪": "Reconnaissance",
    "武器构建": "Weaponization",
    "载荷投递": "Delivery",
    "漏洞利用": "Exploitation",
    "安装植入": "Installation",
    "命令控制": "Command and Control",
    "目标达成": "Actions on Objective"
}


class IocThreatType(Resource):
    def __init__(self):
        self.sqlite = sqlite3.connect(IocCfg.DefaultDbPath)
        self.cursor = self.sqlite.cursor()

    def get(self):
        data = {"threat_type": []}
        try:
            res = self.cursor.execute("select * from threat_type").fetchall()
            for i in res:
                data["threat_type"].append(i[0])
            return flask_response("", True, data)
        except Exception as e:
            LOG.error("get threat_type failed:" + str(e))
            return flask_response("error", False, data)


class CustomizeIocUpdate(Resource):
    """
     预定义 IOC 导入处理
    """

    def post(self):
        start_time = int(time.time())
        name, _, user_ip = Authenticate().get_current_user()
        db = MongoDB("ndr")
        ioc_version = db.find_one("knowledge_config", {})["ioc_version"]
        file = request.files["fileName"]
        if os.path.exists(IocCfg.TempPath):
            shutil.rmtree(IocCfg.TempPath)
        os.makedirs(IocCfg.TempPath)
        file.save(IocCfg.TempPath + "ioc.zip")
        os.system("chown -R kslab:kslab %s" % IocCfg.TempPath)
        # 解压
        unzip_str = "unzip -P '%s' %s -d %s" % (IocCfg.ZipPwd, IocCfg.TempPath + "ioc.zip", IocCfg.TempPath)
        os.system(unzip_str)
        os.system("chown -R kslab:kslab %s" % IocCfg.TempPath)
        # 判断升级版本是否正确：
        version_line = ""
        with open(IocCfg.TempPath + "config.json", 'r') as file:
            lines = file.readlines()
            for line in lines:
                version_line = version_line + line
        version_dict = json.loads(version_line)
        version = version_dict["version"]
        if version.count(".") != 1:
            LogToDb().log_to_db("ioc_log", name, "all", start_time, int(time.time()), ioc_version, version,
                                "failed")
            return flask_response("版本有误,系统版本：%s,升级版本：%s" % (ioc_version, version), False, {})

        if ioc_version[0] != version[0]:
            LogToDb().log_to_db("ioc_log", name, "all", start_time, int(time.time()), ioc_version, version,
                                "failed")
            return flask_response("版本有误,系统版本：%s,升级版本：%s" % (ioc_version, version), False, {})
        try:
            if os.path.exists(IocCfg.DefaultDbPath):
                shutil.move(IocCfg.DefaultDbPath, IocCfg.DefaultDbPathBK)
            shutil.move(IocCfg.TempPath + "KNDR_IOC_default.db",
                        KnowledgeNDR.Path + "KNDR_IOC_default.db")
            cluster_sync_file(KnowledgeNDR.Path + "KNDR_IOC_default.db", KnowledgeNDR.Path + "KNDR_IOC_default.db")
        except Exception as e:
            LogToDb().log_to_db("ioc_log", name, "all", start_time, int(time.time()), ioc_version, version, "failed")
            LOG.error('ioc file check failed:' + str(e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报[%s]文件有误" % file)
            return flask_response(str(e), False, {})
        # 更新情报版本
        db.update("knowledge_config", {"ioc_version": ioc_version}, {"ioc_version": version})
        ndr_log_to_box(NdrLog.Type.OPERATE, "情报更新成功")
        LogToDb().log_to_db("ioc_log", name, "all", start_time, int(time.time()), ioc_version, version, "success")
        return flask_response("Pre Ioc update successfully", True, {})


class IocUpdateLog(Resource):
    def __init__(self):
        self.db = MongoDB("ndr")

    @param_check.check_flask_args(Validator("ioc_update_log"), request)
    def get(self, **kwargs):
        page = int(kwargs["args"]["page"])
        page_size = int(kwargs["args"]["pageSize"])
        logs = []
        try:
            total = self.db.find("ioc_log", {}).count()
            cols = self.db.find("ioc_log", {}).sort([("start_time", -1)]).limit(page_size).skip(
                (page - 1) * page_size)
            for col in cols:
                col = JSONEncoder().encode(col)
                logs.append(json.loads(col))
            return_data = {"total": total,
                           "log": logs,
                           "page": page,
                           "pageSize": page_size}
            return flask_response("", True, return_data)
        except Exception as e:
            LOG.error("error: %s" % e)
            return flask_response("", False, {})


class IocVersion(Resource):
    def __init__(self):
        self.db = MongoDB("ndr")

    def get(self):
        data = {"version": ""}
        try:
            version = self.db.find_one("knowledge_config", {})["ioc_version"]
            data["version"] = version
        except Exception as e:
            LOG.error("error: %s" % e)
            return flask_response("", False, {})
        return flask_response("", True, data)


class CustomizeIocImport(Resource):
    """
    自定义 IOC 导入处理
    """

    def __init__(self):
        self.sqlite = sqlite3.connect(IocCfg.AllDbPath)
        self.cursor = self.sqlite.cursor()
        self.sqlite1 = sqlite3.connect(IocCfg.DefaultDbPath)
        self.cursor1 = self.sqlite1.cursor()
        self.threat_type = []

    def post(self):
        """
        :return:
        """
        fail_ioc = []
        try:
            res = self.cursor1.execute("select * from threat_type").fetchall()
            for i in res:
                self.threat_type.append(i[0])
            self.sqlite1.close()
        except Exception as e:
            LOG.error("ioc threar_type get failed :" + str(e))
            self.sqlite1.close()
            return flask_response("ioc threar_type get failed.", False, {})
        file_obj = request.files["fileName"]
        if not os.path.exists(IocCfg.AllDbPath):
            self.sqlite.close()
            LOG.error("IOC 文件不存在.")
            return flask_response("The ioc file does not exist.", False, {"errorCode": 409})
        try:
            file_obj.save(IocCfg.ImportExcelPath + "/" + file_obj.filename)
        except Exception as error:
            self.sqlite.close()
            LOG.error("文件[%s]上传失败：%s。" % (file_obj.filename, error))
            return flask_response(str(error), False, {})
        data = xlrd.open_workbook(IocCfg.ImportExcelPath + "/" + file_obj.filename)
        table = data.sheet_by_index(0)
        for num in range(1, table.nrows):
            parm = []
            parm += table.row_values(num)
            protocols = ""
            ports = ""
            threat_type = parm[0]
            ioc = parm[1]
            if threat_type not in self.threat_type:
                fail_ioc.append(ioc)
                continue
            ioc_type = parm[2]
            if not ioc_type:
                fail_ioc.append(ioc)
                continue
            disclosure_time = parm[3]
            labels = parm[4]
            tools = parm[5]
            organizations = parm[6]
            killchainstage = ""
            try:
                killchainstage = ioc_killchain_map[parm[7]]
            except Exception as e:
                LOG.error(e)
            if killchainstage not in list(GetKillchains.killchain_map.keys()):
                fail_ioc.append(ioc)
                continue
            score = parm[8]
            reference = parm[9]
            if disclosure_time:
                # 转为数组
                dis_time = time.strptime(str(int(disclosure_time)), "%Y%m%d")
                # 转为其它显示格式
                disclosure_time = time.strftime("%Y-%m-%d", dis_time)
            uid = str(uuid.uuid1())
            ti_data_sql = [uid, threat_type, protocols, ioc, ports, ioc_type, disclosure_time, labels, tools,
                           organizations,
                           killchainstage, score, reference]
            try:
                self.cursor.execute('replace into %s values (?,?,?,?,?,?,?,?,?,?,?,?,?)' % ioc_type, ti_data_sql)
            except Exception as e:
                LOG.error("ioc 批量导入异常 %s" % e)
                ndr_log_to_box(NdrLog.Type.OPERATE, "自定义情报导入失败")
                return flask_response("ioc 批量导入异常.", False, {})
        self.sqlite.commit()
        self.sqlite.close()
        ndr_log_to_box(NdrLog.Type.OPERATE, "自定义情报导入成功")
        cluster_sync_file(IocCfg.AllDbPath)
        return flask_response("ioc 批量导入完成", True, {"failed_total": "ioc 导入失败个数: %s" % len(fail_ioc)})

    def get(self):
        """
        自定义情报模板导出
        """
        file_path = '/opt/ndr/docs/custom_ioc_template.xlsx'

        if not os.path.exists(file_path):
            return flask_response('Customize ioc template file not found!', True, {})

        ret = send_file(file_path, as_attachment=True)

        return make_response(ret)


class CustomizeIocSingle(Resource):
    """
    单条特 ioc 处理(增、删、改、查)
    """

    def __init__(self):
        self.sqlite = sqlite3.connect(IocCfg.AllDbPath)
        self.cursor = self.sqlite.cursor()

    # 删除单条 ioc
    @param_check.check_flask_args(Validator('ioc_rule_delete_schema'), request)
    def delete(self, **kwargs):
        """
        :param sid:
        :return:
        """
        ioc_type = kwargs["ioc_type"]
        ioc = kwargs["ioc"]
        try:
            self.cursor.execute("DELETE from %s where ioc='%s';" % (ioc_type, ioc))
            self.sqlite.commit()
            del_count = self.sqlite.total_changes
            self.sqlite.close()
            if del_count:
                ndr_log_to_box(NdrLog.Type.OPERATE, "情报删除成功")
                cluster_sync_file(IocCfg.AllDbPath)
                return flask_response("Ioc delete successfully.", True, {})
            else:
                ndr_log_to_box(NdrLog.Type.OPERATE, "情报删除失败")
                return flask_response("Ioc delete failed.Reason:[%s] does not exist." % ioc, False, {})
        except Exception as error:
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报删除失败")
            return flask_response("Ioc delete failed.Reason:%s" % str(error), False, {})

    # 修改 ioc
    @param_check.check_flask_args(Validator('ioc_rule_add_schema'), request)
    def put(self, **kwargs):
        """
        :param sid:
        :return:
        """
        threat_type = kwargs["threat_type"]
        protocols = kwargs["protocols"]
        ioc = kwargs["ioc"]
        ports = kwargs["ports"]
        ioc_type = kwargs["ioc_type"]
        disclosure_time = kwargs["discoverytime"]
        labels = kwargs["labels"]
        tools = kwargs["tools"]
        organizations = kwargs["organizations"]
        killchainstage = kwargs["killchainstage"]
        score = kwargs["score"]
        reference = kwargs["reference"]
        if disclosure_time:
            disclosure_time = time.strftime("%Y-%m-%d", time.localtime(int(disclosure_time) / 1000))
        uid = str(uuid.uuid1())
        ti_data_sql = [uid, threat_type, protocols, ioc, ports, ioc_type, disclosure_time, labels, tools, organizations,
                       killchainstage, score, reference]
        try:
            self.cursor.execute('replace into %s values (?,?,?,?,?,?,?,?,?,?,?,?,?)' % ioc_type, ti_data_sql)
        except Exception as e:
            LOG.error(e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报修改失败")
            return flask_response("Ioc update fail(single).", False, {})
        self.sqlite.commit()
        self.sqlite.close()
        ndr_log_to_box(NdrLog.Type.OPERATE, "情报修改成功")
        cluster_sync_file(IocCfg.AllDbPath)
        return flask_response("Ioc update success", True, {})


class CustomizeIoc(Resource):
    """ ioc 处理（增加单条，删除所有，获取所有自定义 IOC） """

    def __init__(self):
        self.sqlite = sqlite3.connect(IocCfg.AllDbPath)
        self.cursor = self.sqlite.cursor()

    # 删除所有自定义 ioc
    def delete(self):
        """
        :return:
        """
        try:
            self.cursor.execute("DELETE from ip")
            self.cursor.execute("DELETE from domain")
            self.sqlite.commit()
            self.sqlite.close()
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报删除成功")
            cluster_sync_file(IocCfg.AllDbPath, IocCfg.AllDbPath)
            return flask_response("Ioc delete successfully.Del", True, {})
        except Exception as error:
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报删除失败")
            return flask_response("Ioc delete failed.Reason:%s" % str(error), False, {})

    # 获取所有 ioc
    @param_check.check_flask_args(Validator('ioc_rule_get_all_schema'), request)
    def get(self, **kwargs):
        """
        :return:
        """
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])
        parm = "limit %d offset %d" % (page_size, (page - 1) * page_size)
        ioc_type = kwargs['ioc_type']
        if ioc_type:
            sqlstr = "SELECT * from %s" % ioc_type
        else:
            sqlstr = "SELECT * from ip union SELECT * from domain"
        cond_list = []
        cond = ""
        if kwargs['ioc']:
            cond_list.append("ioc='%s'" % kwargs['ioc'])
        if kwargs['labels']:
            cond_list.append("labels like '%%%s%%'" % kwargs['labels'])
        # 如果有条件，加上where关键字
        if cond_list:
            cond = 'where ' + ' and '.join(cond_list)
        ioc_res = self.cursor.execute("SELECT * from (%s) as t1 %s %s;" % (sqlstr, cond, parm)).fetchall()
        ioc_count = self.cursor.execute("SELECT count(*) from (%s) as t1 %s" % (sqlstr, cond)).fetchone()
        detail = []
        for row in ioc_res:
            disclosure_time = ""
            if row[6]:
                disclosure_time = int(time.mktime(time.strptime(row[6], "%Y-%m-%d"))) * 1000
            temp = {
                "uid": row[0],
                "threat_type": row[1],
                "protocols": row[2],
                "ioc": row[3],
                "ports": row[4],
                "ioc_type": row[5],
                "discoverytime": disclosure_time,
                "labels": row[7],
                "tools": row[8],
                "organizations": row[9],
                "killchainstage": row[10],
                "score": row[11],
                "reference": row[12]
            }
            detail.append(temp)
        self.sqlite.close()
        data = {
            "total": ioc_count[0],
            "detail": detail,
            "page": page,
            "pageSize": page_size
        }
        ndr_log_to_box(NdrLog.Type.OPERATE, "情报获取成功")
        return flask_response("", True, data)

    @param_check.check_flask_args(Validator('ioc_rule_add_schema'), request)
    def post(self, **kwargs):
        """
        :return:
        """
        threat_type = kwargs["threat_type"]
        if not threat_type:
            return flask_response("threat_type is Empty.", False, {})
        protocols = kwargs["protocols"]
        ioc = kwargs["ioc"]
        if not ioc:
            return flask_response("ioc is Empty.", False, {})
        ports = kwargs["ports"]
        ioc_type = kwargs["ioc_type"]
        disclosure_time = kwargs["discoverytime"]
        labels = kwargs["labels"]
        tools = kwargs["tools"]
        organizations = kwargs["organizations"]
        killchainstage = kwargs["killchainstage"]
        score = kwargs["score"]
        reference = kwargs["reference"]
        if disclosure_time:
            disclosure_time = time.strftime("%Y-%m-%d", time.localtime(int(disclosure_time) / 1000))
        uid = str(uuid.uuid1())
        ti_data_sql = [uid, threat_type, protocols, ioc, ports, ioc_type, disclosure_time, labels, tools, organizations,
                       killchainstage, score, reference]
        try:
            self.cursor.execute('replace into %s values (?,?,?,?,?,?,?,?,?,?,?,?,?)' % ioc_type, ti_data_sql)
        except Exception as e:
            LOG.error(e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "情报添加失败")
            return flask_response("Ioc add fail(single).", False, {})
        self.sqlite.commit()
        self.sqlite.close()
        LOG.info("Ioc add success(single).[%s]" % ioc)
        ndr_log_to_box(NdrLog.Type.OPERATE, "情报添加成功")

        # 集群模式下，将文件同步到工作节点(不包含管理节点)
        cluster_sync_file(IocCfg.AllDbPath, IocCfg.AllDbPath)
        return flask_response("Ioc add success(single).[%s]" % ioc, True, {})
