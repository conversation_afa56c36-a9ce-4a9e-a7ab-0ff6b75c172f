import isPlainObject from 'lodash/isPlainObject';
import { history as router } from 'umi';
import { parse } from 'qs';
import moment from 'moment';
import defaultSettings from '../../config/defaultSettings';
import _ from 'lodash';
import { Form } from 'antd';
// eslint-disable-next-line max-len, no-useless-escape
const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

const isUrl = (path: string): boolean => reg.test(path);

/**
 * 删除含有 null 或者 undefined 的字段
 * @param data 要检测的数据
 */
function deleteNullOrUndefinedField(data: object) {
  return Object.keys(data).reduce((pre, key) => {
    let item = data[key];
    if (isPlainObject(item)) {
      item = deleteNullOrUndefinedField(item);
    }
    if (!(item == null)) {
      pre[key] = item; // eslint-disable-line no-param-reassign
    }
    return pre;
  }, {});
}

function replaceRouter(redirect: string) {
  router.replace(redirect || '/');
}

function getPageQuery() {
  return parse(window.location.search, {
    ignoreQueryPrefix: true,
  });
}

function deepFlatten<T>(arr: T[]): T[] {
  const ret: T[] = [];
  return ret.concat(...arr.map(v => (Array.isArray(v) ? deepFlatten(v) : v)));
}

function flatTree<T, K extends keyof T>(arr: T[], path: K): T[] {
  const emptyArr: T[] = [];
  return emptyArr.concat(
    ...arr.map(item => {
      const child: T[] = (item[path] as any) as T[];
      if (Array.isArray(child)) {
        return [item].concat(...flatTree(child, path));
      }
      return item;
    }),
  );
}

const isDev = (): boolean => {
  const { NODE_ENV } = process.env;
  return NODE_ENV === 'development';
};

// 获取 websocket 的连接地址与协议
const getWebsocketAddr = () => {
  let isHttps = false;
  if (window.location.protocol.startsWith('https')) {
    isHttps = true;
  }
  const { ws }: any = defaultSettings;
  const wsAddr = isDev() ? ws : `${window.location.host}/mica-ws`;
  return isHttps ? `wss://${wsAddr}` : `ws://${wsAddr}`;
};

// 枚举对象转数组
const enumToArray = function(value: object) {
  const arr = [];
  for (const i in value) {
    const obj = {
      name: i,
      value: value[i],
    };
    arr.push(obj); // 属性
  }
  return arr;
};

/**
 * 超长补...
 *
 * @param {String} string
 * @param {Number} limit
 */
function ellipsis(string = '', limit = 15) {
  if (string.length > limit) {
    return `${string.substring(0, limit)}...`;
  }
  return string;
}

/**
 * 截取文件名
 *
 * @param {String} string
 */
function splitItem(string = '') {
  const splitArr = string
    .toString()
    .split(';')
    .map(item => {
      const index = item.toString().indexOf('__');
      if (index !== -1) {
        return item.substring(index + 2);
      }
      return item;
    });
  return splitArr.join(';');
}

// 13位时间戳转时间
function formatTime(timestamp: number, format: string) {
  if (isNaN(+timestamp)) {
    return '';
  }
  return moment(timestamp).format(format || 'YYYY-MM-DD HH:mm:ss');
}

function parseQueryString(params: object) {
  let qs: any = [];
  if (_.isString(params)) {
    return params;
  }
  if (!params || _.isEmpty(params)) {
    return '';
  }
  if (params) {
    Object.keys(params).map(key => {
      const value = params[key];
      if (value !== null && value !== undefined && value !== '') {
        qs.push(`${key}=${encodeURIComponent(params[key])}`);
      }
    });
  }
  if (qs.length > 0) {
    qs = [...new Set(qs)];
    qs.sort();
    return `?${qs.join('&')}`;
  }
  return '';
}

// 零时区转东八区
function zoneTransfer(time: moment.MomentInput, format: any) {
  if (!time) {
    return '-';
  }
  return moment(time)
    .utc()
    .utcOffset(8)
    .format(format || 'YYYY-MM-DD HH:mm');
}
/**
 * @description 大文件生成切片
 * @param file 大文件对象
 * @param size 切片大小 默认100M = 100*1024*1024
 */
const createFileChunk = (file: { size: any; slice: (arg0: number, arg1: number) => any }, size = 10 * 1024 * 1024) => {
  const chunkList = [];
  const fileSize = file.size;
  let curSize = size;
  let cur = 0;

  // 小于100m
  if (fileSize < 100 * 1024 * 1024) {
    curSize = fileSize;
  } else {
    curSize = 100 * 1024 * 1024;
  }
  while (cur < fileSize) {
    const blob = file.slice(cur, cur + curSize);
    chunkList.push({
      file: blob,
      size: blob.size,
      percent: 0,
    });
    cur += curSize;
  }
  return chunkList;
};
function getQueryVariable(variable: string) {
  var query = window.location.search.substring(1);
  var vars = query.split('&');
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=');
    if (pair[0] == variable) {
      return pair[1];
    }
  }
  return '';
}

function throttle(fn: () => void, delay: number) {
  var prev = Date.now();
  return function() {
    var now = Date.now();
    if (now - prev > delay) {
      fn();
      prev = Date.now();
    }
  };
}
function toThousands(num: any) {
  var l;
  if (num < 0) {
    l = 4;
  } else {
    l = 3;
  }
  let tail = '';
  let ind = num.toString().indexOf('.');
  if (ind > 0) {
    tail = num.toString().substr(ind);
  }

  num = Math.floor(num);
  num = (num || 0).toString();
  let result = '';
  while (num.length > l) {
    result = ',' + num.slice(-3) + result;
    num = num.slice(0, num.length - 3);
  }
  if (num) {
    result = num + result;
  }
  return result + tail;
}
function calculationByte(value: any) {
  if (!value) return '0 B';
  const k = 1024,
    sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
    i = Math.floor(Math.log(value) / Math.log(k));

  return (value / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
}

function randomString(length: any) {
  var str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  var result = '';
  for (var i = length; i > 0; --i) result += str[Math.floor(Math.random() * str.length)];
  return result;
}

// 处理空值展示
function handleEmpty(value: any, strReplace: string = '-') {
  if (!value) {
    return strReplace;
  }
  return value;
}

function computeWeekTime() {
  let nowDay = moment(new Date()).format('yyyy-MM-DD');
  const hourArr = [0, 4, 8, 12, 16, 20];
  const nowHours = moment().hours();
  let HourSpilt = 0;
  for (let i = 0; i < hourArr.length; i++) {
    if (nowHours <= hourArr[i]) {
      HourSpilt = hourArr[i];
      break;
    } else if (nowHours > 20) {
      nowDay = moment()
        .add(1, 'days')
        .format('yyyy-MM-DD');
      HourSpilt = 0;
      break;
    }
  }
  let startTime = `${nowDay} ${HourSpilt}:00:00`;
  let weekArr = [];
  // 一周
  for (let i = 0; i < 42; i++) {
    weekArr.push({
      time: moment(startTime).unix() * 1000,
      total_bytes: 0,
      unit: 'MB',
    });
    startTime = moment(startTime)
      .subtract(4, 'hour')
      .format('yyyy-MM-DD HH:mm:ss');
  }
  return weekArr.reverse();
}

function transformData(originalData: any[]) {
  // let columns = [
  //   {
  //     title: '序号',
  //     first: 'id',
  //   },
  //   {
  //     title: '单位名称',
  //     first: 'company_name',
  //   },
  //   {
  //     title: '涉事设施',
  //     first: 'inv_facilities',
  //   },
  // ];

  // let data = [
  //   {
  //     company_name: '测试',
  //     inv_facilities: '涉事1',
  //   },
  //   {
  //     company_name: '单位2',
  //     inv_facilities: '22',
  //   },
  // ];
  // data.forEach((item:any) => {
  //   item.title='',
  //   item.dataIndex:
  // });
  // let resultColumns = [];
  // newColumns.forEach((item, index) => {
  //   resultColumns.push({
  //     title: ``,
  //   });
  // });
  let keys = Object.keys(originalData[0]).filter(key => key !== 'key');
  let columns = [{ title: '字段名称', dataIndex: 'first', key: 'first' }];

  originalData.forEach((key, index) => {
    columns.push({
      title: `数据${index + 1}`,
      dataIndex: `col${index + 1}`,
     
      key: `col${index + 1}`,
    });
  });

  let transformedData = keys.map((key, index) => {
    let rowData: any = { key: `${index + 1}` };
    originalData.forEach((item, idx) => {
      rowData[`col${idx + 1}`] = item[key];
    });
    rowData.first = key;
    return rowData;
  });

  return { columns, data: transformedData };

  // return { columns:data, data: columns };
}

export {
  isDev,
  isUrl,
  deleteNullOrUndefinedField,
  replaceRouter,
  getPageQuery,
  deepFlatten,
  flatTree,
  getWebsocketAddr,
  enumToArray,
  ellipsis,
  splitItem,
  formatTime,
  parseQueryString,
  zoneTransfer,
  createFileChunk,
  getQueryVariable,
  throttle,
  toThousands,
  calculationByte,
  randomString,
  handleEmpty,
  computeWeekTime,
  transformData,
};
