import React, { useEffect, useState } from 'react';
import { Tooltip } from 'antd';
import cn from 'classnames';
import s from './style.less';
const data = [
  {
    title: '文件名',
    key: 'filename',
    content: '- -',
  },
  {
    title: '攻击链阶段',
    key: 'killchain',
    content: '- -',
  },
  {
    title: '资源地址',
    key: 'reportPath',
    content: '- -',
  },
  {
    title: '内容类型',
    key: 'filetype',
    content: '- -',
  },
  {
    title: '源',
    key: 'src',
    content: '- -',
  },
  {
    title: '目的',
    key: 'dst',
    content: '- -',
  },
];
interface DownInfoProps {
  down: any;
}
const DownInfo = ({ down }: DownInfoProps) => {
  const killchainList = JSON.parse(localStorage.killchainList);
  const [info, setInfo] = useState(data);
  const initData = () => {
    const { flow, srcIpGeoInfo, dstIpGeoInfo } = down;
    down.src = [
      flow.src_ip,
      srcIpGeoInfo.city_name,
      srcIpGeoInfo.longitude && `经纬度: ${srcIpGeoInfo.longitude},${srcIpGeoInfo.latitude}`,
    ];
    down.dst = [flow.dst_ip, dstIpGeoInfo.city_name, dstIpGeoInfo.longitude && `经纬度: ${dstIpGeoInfo.longitude},${dstIpGeoInfo.latitude}`];
    const data_ = data.map(e => {
      down[e.key] && (e.content = down[e.key]);
      if(down[e.key]&&e.key==='killchain'){
        e.content = killchainList[down[e.key]]
      }
      if (Array.isArray(e.content)) {
        e.content = e.content.map(text => {
          return <span className="mr-3">{text}</span>;
        });
      }
      return e;
    });
    setInfo(data_);
  };
  useEffect(() => {
    initData();
  }, [down]);
  return (
    <div className={cn('h-22rem of-hidden pa-2', s.down_info)}>
      <p className={s.title}>
        <span>文件下载信息</span>
      </p>
      <div className="flex flex-1 mt-2">
        <div className={s.downBox}>
          {info.map((item, i) => {
            return (
              <p className={cn('flex flex-1 items-center')}>
                <span className={s.titleSpan}>{item.title}</span>
                <Tooltip title={item.content} placement="topLeft" overlayStyle={{"maxWidth":'20rem'}}>
                  <span className={s.contentSpan}>{item.content}</span>
                </Tooltip>
              </p>
            );
          })}
        </div>
      </div>
    </div>
  );
};
export default DownInfo;
