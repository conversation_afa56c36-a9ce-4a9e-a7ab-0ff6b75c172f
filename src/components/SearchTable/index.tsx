import React, { BaseSyntheticEvent } from 'react';
import { FormComponentProps } from 'antd/lib/form';
import { TableProps, ColumnProps } from 'antd/lib/table';
import { PaginationConfig } from 'antd/lib/pagination';
import { Table, Form, Button, Divider, Row, Col } from 'antd';
import { ResponseError } from 'umi-request';
import request from '@/utils/request';
import styles from './index.less';
import PageLoding from '../PageLoading';

const FormItem = Form.Item;

interface FormConfigItem {
  name: string;
  key: string;
  component?: any;
  render?: any;
  initialValue?: string | number | any[];
  rules?: any[];
}
export type IFormConfig = FormConfigItem[];
interface State {
  loading: boolean;
  filter: any;
  list: any[];
  total: number;
  page: number;
  pageSize: number;
}
export interface TSearchTableProps extends FormComponentProps {
  // 是否是前端分页
  isFrontPage?: boolean;
  // Table 的表格定义
  tableColumns: ColumnProps<any>[];
  // 请求 Table 数据的 URL，必须是 GET 请求
  tableQueryURL: string;
  // 自定义 Table 配置
  customTableOptions?: TableProps<any>;
  // 搜索表单配置
  queryFormConfig?: IFormConfig;
  // 默认查询条件，通常用于把查询条件通过路由传递的情况
  defaultFilter?: Record<string, any>;
  /**
   * 获取该组件的 this 对象，可用于调用该组件中的 resetForm 或 loadData 方法
   * @param target
   */
  onRef?: (target: any) => void;
  /**
   * 将表格数据API获取的数据整理为state
   * @param responeseData 表格 API 返回的结果
   */
  tableResponseToState?: (
    responeseData: Record<string, any>,
  ) => { list: any[]; total: number };
  /**
   * 将 Form 的结果转换成请求的参数
   * @param formValue Form 的结果
   */
  tableFormValuesToQuery?: (
    formValue: Record<string, any>,
  ) => Record<string, any>;
  /**
   * http request 错误处理
   * 用于覆盖默认错误处理
   */
  errorHandler?: (error?: ResponseError) => void;
}
interface DefaultProps {
  queryFormConfig: IFormConfig;
  defaultFilter: Record<string, any>;
  tableResponseToState: (
    responeseData: Record<string, any>,
  ) => { list: any[]; total: number };
  tableFormValuesToQuery: (
    formValue: Record<string, any>,
  ) => Record<string, any>;
  isFrontPage: boolean;
}

/**
 * 生成一个统一的管理页面，通过传入的参数对生成的页面进行定义
 */
class SearchTable extends React.Component<TSearchTableProps, State> {
  static defaultProps: DefaultProps = {
    isFrontPage: false,
    queryFormConfig: [],
    defaultFilter: {},
    tableResponseToState: (responeseData: any) => responeseData,
    tableFormValuesToQuery: (formValue: Record<string, any>) => formValue,
  };

  state = {
    loading: false,
    filter: this.props.defaultFilter,
    list: [],
    total: 0,
    page: 1,
    pageSize: 10,
  };

  componentDidMount() {
    this.loadData();
    if (this.props.onRef) {
      this.props.onRef(this);
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.tableQueryURL !== this.props.tableQueryURL) {
      this.loadData();
    }
  }

  /**
   * 获取页面中的表格数据
   * @param page 当前分页数，从 1 开始
   * @param pageSize 分页大小
   */
  loadData = async (
    page: number = this.state.page,
    pageSize: number = this.state.pageSize,
  ) => {
    this.setState({
      loading: true,
    });
    const { filter } = this.state;
    const { tableResponseToState, tableQueryURL, errorHandler } = this.props;
    const params = { ...filter, page, pageSize };

    try {
      Object.keys(params).forEach(key => {
        if (typeof params[key] === 'object') {
          params[key] = JSON.stringify(params[key]);
        }
      });

      const { data } = await request(tableQueryURL, {
        params,
        ...(errorHandler ? { errorHandler } : {}),
      });
      const nextState = tableResponseToState && tableResponseToState(data);
      this.setState(
        Object.assign(nextState, {
          page,
          pageSize,
        }),
      );
    } catch (e) {
      // 通用错误处理
    } finally {
      this.setState({
        loading: false,
      });
    }
  };

  /**
   * 重置搜索表单
   */
  resetForm = () => {
    this.props.form.resetFields();
  };

  onChangePage = (page: number, pageSize?: number) => {
    const { isFrontPage } = this.props;
    if (isFrontPage) {
      this.setState({
        page,
      });
    } else {
      this.loadData(page, pageSize);
    }
  };

  onChangePageSize = (current: number, pageSize: number) => {
    const { isFrontPage } = this.props;
    if (isFrontPage) {
      this.setState({
        page: current,
        pageSize,
      });
    } else {
      this.loadData(current, pageSize);
    }
  };

  onSubmitForm = (e: BaseSyntheticEvent) => {
    e.preventDefault();
    const { form, tableFormValuesToQuery } = this.props;
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      const filterValues = values;
      // 过滤空字符串
      Object.keys(filterValues).forEach(key => {
        if (filterValues[key] === '') {
          delete filterValues[key];
        } else if (
          filterValues[key] instanceof Array &&
          filterValues[key].length <= 0
        ) {
          delete filterValues[key];
        }
      });
      const query =
        tableFormValuesToQuery && tableFormValuesToQuery(filterValues);
      this.setState({ filter: query, page: 1 }, () => {
        this.loadData();
      });
    });
  };

  renderQueryForm = () => {
    const {
      form: { getFieldDecorator },
      queryFormConfig,
    } = this.props;

    return (
      queryFormConfig &&
      queryFormConfig.length > 0 && (
        <>
          <Form
            className={styles.queryForm}
            onSubmit={this.onSubmitForm}
            layout="inline"
          >
            <Row>
              {queryFormConfig.map(
                ({ name, key, initialValue, rules, component, render }) => (
                  <Col lg={8} key={key}>
                    <FormItem label={name}>
                      {getFieldDecorator(key, {
                        initialValue,
                        rules,
                      })(component || render())}
                    </FormItem>
                  </Col>
                ),
              )}
              <Col lg={(3 - (queryFormConfig.length % 3)) * 8}>
                <FormItem className={styles.formBtnGroup}>
                  <Button
                    disabled={this.state.loading}
                    loading={this.state.loading}
                    type="primary"
                    htmlType="submit"
                  >
                    查询
                  </Button>
                  <Button className={styles.btnMargin} onClick={this.resetForm}>
                    重置
                  </Button>
                </FormItem>
              </Col>
            </Row>
          </Form>
          <Divider />
        </>
      )
    );
  };

  render() {
    const { loading, list, total, page, pageSize } = this.state;
    const { customTableOptions, tableColumns } = this.props;
    // 分页属性
    const paginationOptions: PaginationConfig = {
      pageSize,
      showSizeChanger: true,
      current: page,
      // 当删除列表末页(如第2页)的全部数据后  pageSize * (page - 1) === total
      // 此时 Pagination 组件根据pageSize和total计算出来显示的页码是删除操作时的前一页(第1页)
      // 且无数据，并且不可点击，造成Bug
      // 这里对这种情况做处理，total增加1,使删除后仍然停留在第2页且无数据，用户的操作反馈更直观
      total: total && pageSize * (page - 1) === total ? total + 1 : total,
      onChange: this.onChangePage,
      pageSizeOptions: ['10', '20', '50', '100'],
      onShowSizeChange: this.onChangePageSize,
      hideOnSinglePage: false,
    };
    const tableOptions: TableProps<any> = {
      pagination: paginationOptions,
      loading: {
        indicator: (
          <PageLoding
            delay={200}
            style={{
              position: 'absolute',
              fontSize: 14,
              width: 'initial',
              height: 'initial',
            }}
          />
        ),
        spinning: loading,
      },
      dataSource: list,
      columns: tableColumns,
      rowKey: record => record.id,
    };
    if (
      customTableOptions &&
      typeof customTableOptions === 'object' &&
      Object.prototype.toString.call(customTableOptions) === '[object Object]'
    ) {
      Object.assign(tableOptions, customTableOptions);
    }

    return (
      <>
        {this.renderQueryForm()}
        {this.props.children}
        <Table className={styles.table} size="middle" {...tableOptions} />
      </>
    );
  }
}

export default Form.create<TSearchTableProps>()(SearchTable);
