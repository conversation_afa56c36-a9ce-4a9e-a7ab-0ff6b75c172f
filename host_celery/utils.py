# -*- coding: utf-8 -*-
import os
from pymongo import MongoClient
import subprocess
import datetime
import time

def run_command(command, ignore_error=False, timeout=None):
    """
    Run bash command, return stdout or stderr, all data is saved in RAM,
    So do not use this function when data is huge!
    """
    proc = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    try:
        output, error = proc.communicate(timeout=timeout)

        if proc.returncode != 0 and not ignore_error:
            return False, str(error, 'utf-8')
        else:
            return True, output.decode("utf-8")
    except subprocess.TimeoutExpired:
        proc.kill()
        return False, 'Timeout'


def get_redis_container_ip(db):
    cmd = "docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' " + db
    ret, msg = run_command(cmd)
    if not ret or not msg:
        os._exit(1)
    return msg.strip()

class MongoDB(object):
    def __init__(self, database):
        self.conn = MongoClient(
            host="mongo",
            port=27017,
            username="ksbox",
            password="FBgxC5PWAre",
            authSource="admin"
        )
        self.db = self.conn[database]

    def get_state(self):

        # return self.conn is not None and self.db is not None

        try:
            client = self.conn
            # 利用server_info()判断mongodb状态
            client.server_info()
        except Exception as e:
            return False

        else:
            return True

    def insert_one(self, collection, data):
        if self.get_state():
            ret = self.db[collection].insert_one(data)
            return ret.inserted_id
        else:
            return ""

    def insert_many(self, collection, data):
        if self.get_state():
            ret = self.db[collection].insert_many(data)
            return ret.inserted_ids
        else:
            return ""

    def update(self, collection, filter, data, insert=False):
        # data format:
        if self.get_state():
            return self.db[collection].update_one(filter, {"$set": data}, upsert=insert)
        return 0

    def update_one(self, collection, filter, data, insert=False):
        # data format:
        if self.get_state():
            return self.db[collection].update_one(filter, data, upsert=insert)
        return 0

    def update_many(self, collection, filter, data):
        # data format:
        if self.get_state():
            return self.db[collection].update_many(filter, {"$set": data})
        return 0

    def aggs(self, collection, pipeline):
        if self.get_state():
            return list(self.db[collection].aggregate(pipeline))
        return None

    def find(self, col, condition, column=None):
        if self.get_state():
            if column is None:
                return self.db[col].find(condition)
            else:
                return self.db[col].find(condition, column)
        else:
            return None

    def find_one(self, col, condition, column=None):
        if self.get_state():
            if column is None:
                return self.db[col].find_one(condition)
            else:
                return self.db[col].find_one(condition, column)
        else:
            return None

    def delete(self, col, condition):
        if self.get_state():
            return self.db[col].delete_many(filter=condition).deleted_count
        return 0

    def count(self, col, condition):
        if self.get_state():
            return self.db[col].count_documents(condition)
        return 0

    def drop(self, col):
        if self.get_state():
            return self.db[col].drop()
        return 0

    def close(self):
        if self.get_state():
            self.conn.close()
            return True
        return False

    def list_collection(self):
        if self.get_state():
            return self.db.list_collection_names()
        return []


def get_cmd_data(cmd):
    """
    获取命令行执行结果
    """
    cmd_data = os.popen(cmd)
    data = cmd_data.read().strip()
    cmd_data.close()
    return data


def update_disk(node_name, coll, disk_total, disk_use, disk_free, percent):
    mongodb = MongoDB("ndr")
    create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    condition = {"node_name": node_name} if node_name else {}
    try:
        peak_data = mongodb.find_one("monitor_disk", condition)
        peak = 0
        if peak_data and (coll in peak_data):
            peak = peak_data[coll]["peak"]
            peak_time = peak_data[coll]["peakTime"]
        # 更新历史峰值
        if peak <= percent:
            body = {coll: {
                "node_name": node_name,
                "peak": percent,
                "peakTime": create_time,
                "total": disk_total,
                "use": disk_use,
                "free": disk_free,
                "percent": percent,
                "createTime": create_time}}
        else:
            body = {coll: {
                "node_name": node_name,
                "peak": peak,
                "peakTime": peak_time,
                "total": disk_total,
                "use": disk_use,
                "free": disk_free,
                "percent": percent,
                "createTime": create_time}}
        body['node_name'] = node_name
        mongodb.update("monitor_disk", condition, body, insert=True)
    except Exception as e:
        print("disk__" + str(e))
    mongodb.close()


class HostMonitor:
    cmd1 = "ps -eo pid,lstart,etime,cmd | grep "
    cmd2 = " |grep -v grep | awk '{print $7}'"

    time_server = {
        "alert_output": "nohup python37 /opt/ndr/alert_output/alert_output.py >> /opt/ndr/alert_output/output.log 2>&1 &"
    }

    server = {
        "bdp-model": "/app/model",
        "kafka": "kafka.Kafka",
        "clickhouse": "/usr/bin/clickhouse-server",
        "elasticsearch": "org.elasticsearch.bootstrap.Elasticsearch",
        "hdp": "/opt/hdp/bin/hdp_ids.*--master-lcore",
        "ndr": "'python37 /opt/ndr/app.py'",
        "alert_output": "alert_output.py",
        "dga-model": "dgaDetect"
    }

    server_action = {
        "bdp-model": "model",
        "alert_output": "nohup python37 /opt/ndr/alert_output/alert_output.py >> /opt/ndr/alert_output/output.log 2>&1 &",
        "kafka": "kafka",
        "clickhouse": "clickhouse",
        "elasticsearch": "elasticsearch",
        "dga-model": "mlmodel"
    }
