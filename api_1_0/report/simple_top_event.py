#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from utils import param_check
from flask_restful import Resource
from flask import request

from utils.es_template.get_es_template import ES_Template
from utils.param_check import Validator
from utils.utils import flask_response
from utils.utils import en_to_cn_country
from utils.database import MongoDB, get_es_client
from utils.mongo_template.get_mongo_template import MongoTemplate
from api_1_0.knowledge.get_eventName import get_event_properties
from utils.logger import get_ndr_logger

LOG = get_ndr_logger("api_log", __file__)
ES_LOG = get_ndr_logger('es_log', __file__)


class EventTopResource(Resource):
    """报告模块-精简报表 热门活动top10"""
    def __init__(self):
        self.es_template = ES_Template().read_template("get_report_top_event")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["args"]["startTime"])
        stop_time = int(kwargs["args"]["stopTime"])
        if start_time >= stop_time:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})

        time_mode = "observedTime" if kwargs["timeMode"] == 'observed' else "occurredTime"
        self.es_template["query"]["bool"]["must"][0]["range"] = {time_mode: {"gte": start_time, "lte": stop_time}}

        # 连接ES查询
        es_client = get_es_client()
        try:
            query_data = es_client.search(index="rule-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return False, message

        data_return = self.get_data(query_data)

        return data_return

    @staticmethod
    def get_data(query_data):
        docs = []
        try:
            res = query_data["aggregations"]["vulName"]["buckets"]
            for event in res:
                top_event = event["srcIp"]["buckets"][0]["top_event"]["hits"]["hits"][0]["_source"]
                eve = dict()
                score = top_event["threatScore"]
                if not score:
                    continue
                src_ip = top_event["flow"]["src_ip"]
                killchain_list = [top_event["lockheedKillchainEN"]]
                count = event["srcIp"]["buckets"][0]['doc_count']
                event_name, event_type = get_event_properties(killchain_list, count, src_ip, "null")
                eve["behaviors"] = event_name + ' [%s]' % top_event['vulName']
                eve['eventType'] = event_type
                eve["threatScore"] = score
                eve["srcIp"] = src_ip
                eve['threatFlag'] = top_event['threatFlag']
                eve['observedTime'] = top_event['observedTime']
                eve['occurredTime'] = top_event['occurredTime']
                eve['count'] = count
                eve['lockheedKillchainENs'] = killchain_list
                docs.append(eve)
        except Exception as e:
            LOG.error('params[{0}],reason[{1}]'.format(query_data, str(e)))

        return flask_response("success", True, docs)
