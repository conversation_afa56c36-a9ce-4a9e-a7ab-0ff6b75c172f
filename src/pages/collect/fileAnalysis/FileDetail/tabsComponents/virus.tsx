import { Table } from 'antd';
import React from 'react';
import s from './style.less';
import cn from 'classnames';
import { CheckOutlined, WarningOutlined } from '@ant-design/icons';
interface partProps {
  info: any;
}
const Virus = ({ info = {} }: partProps) => {
  const columns = [
    {
      title: '文件类型',
      dataIndex: 'filetype',
    },
    {
      title: '文件大小',
      dataIndex: 'filesize',
    },
    {
      title: 'CRC32',
      dataIndex: 'crc32',
    },
    {
      title: '哈希256',
      dataIndex: 'sha256',
    },
  ];
  const data = [info];
  return (
    <div className={s.virusBox}>
      <Table className={s.table} bordered size="middle" columns={columns} dataSource={data} pagination={false} />
      <div className="flex mt-2rem justify-center">
        <div className={s.divBox}>
          <p className={s.title}>ClamAV</p>
          <p>版本：1.101.1</p>
          <p>
            家族：
            {info['virus_name.clamav'] ? (
              <>
                <WarningOutlined style={{ color: 'red' }} />
                {info['virus_name.clamav']}
              </>
            ) : (
              <>
                <CheckOutlined style={{ color: '#098609' }} />
                无毒
              </>
            )}
          </p>
        </div>
        <div className={s.divBox}>
          <p className={s.title}>knownsec</p>
          <p>版本：0.101.1</p>
          <p>
            家族：
            {info['virus_name.knownsec'] ? (
              <>
                <WarningOutlined style={{ color: 'red' }} />
                {info['virus_name.knownsec']}
              </>
            ) : (
              <>
                <CheckOutlined style={{ color: '#098609' }} />
                无毒
              </>
            )}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Virus;
