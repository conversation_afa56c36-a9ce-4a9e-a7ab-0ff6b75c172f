import React, { useEffect, useRef, useState } from 'react';
import Prism from 'prismjs';
import './index.less';

Prism.languages.snort = {
  comment: /#.*/,
  'invalid bold': /(\$[A-Z0-9_]*[a-z]+[A-Z0-9_]*|<-)/,
  constant: /(\$[A-Z0-9_]+)/,
  'operator bold': /\s*(pass|drop|reject|alert)\s+/,
  tag: /(any|tcp|udp|icmp|ip|http|ftp|tls|smb|dns|dcerpc|ssh|smtp|imap|msn|modbus|dnp3|enip|nfs|ikev2|krb5|ntp|dhcp)\b/,
  url: RegExp('url,([^;]+)', 'i'),
  string: {
    pattern: /(:)"[^"]+"/,
    lookbehind: true,
    greedy: true,
  },
  property: {
    pattern: /[^;:]+;/,
    lookbehind: true,
  },
  keyword: {
    pattern: /([a-z_]+):/,
    greedy: true,
  },
  symbol: /(<>|->)/,
  punctuation: /[{}[\];(),.:]/,
  number: {
    pattern: /\d+/,
    greedy: true,
  },
};
const CodeHighlight = (Props: {
  code: any;
  language: any;
  hasPre: any;
  contentEditable?: false | undefined;
  changeRule: any;
}) => {
  const [code, setcode] = useState(Props.code || '');
  const [language, setlanguage] = useState(Props.language || 'http');
  const [hasPre, sethasPre] = useState(Props.hasPre || true);
  const [contentEditable, setcontentEditable] = useState(
    Props.contentEditable || false,
  );
  // const [changeRule, setchangeRule] = useState(Props.changeRule|| ()=>{})
  const ref = useRef(null);
  useEffect(() => {
    highlight();
  }, []);
  const highlight = () => {
    if (ref && ref.current) {
      Prism.highlightElement(ref.current);
    }
  };
  // const { code, language, hasPre, contentEditable = false, changeRule } = Props;
  if (hasPre) {
    return (
      <pre>
        <code ref={ref} className={`language-${language}`}>
          {code.trim()}
        </code>
      </pre>
    );
  }
  return (
    <code
      ref={ref}
      contentEditable={contentEditable}
      suppressContentEditableWarning
      className={`language-${language}`}
      // onKeyUp={e => {
      //   changeRule(e);
      // }}
      // onPaste={e => {
      //   changeRule(e);
      // }}
      role="button"
      id={contentEditable ? 'edit_rule' : ''}
      style={{
        width: '100%',
        height: '100%',
        overflowY: 'auto',
        wordWrap: 'break-word',
        wordBreak: 'break-all',
      }}
    >
      {code}
    </code>
  );
};
export default CodeHighlight;
