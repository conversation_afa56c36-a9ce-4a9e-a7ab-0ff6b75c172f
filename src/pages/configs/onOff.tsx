/*
 * @Author: 田浩
 * @Date: 2021-10-15 10:05:43
 * @LastEditors: tianh
 * @LastEditTime: 2022-03-04 15:35:29
 * @Descripttion:
 */
import React from 'react';
import { Tooltip, Popconfirm } from 'antd';
import { RedoOutlined, PoweroffOutlined } from '@ant-design/icons';
import { Restart, Off } from '@/services/configs';
const OnOff = () => {
  const handleOff = () => {
    Off().then(res => {
      console.log(res);
    });
  };
  const handleRestart = () => {
    Restart().then(res => {
      console.log(res);
    });
  };
  return (
    <div
      style={{ width: 400, display: 'flex', justifyContent: 'space-evenly' }}
    >
      <div>
        <Popconfirm title="是否确认重启?" onConfirm={handleRestart}>
          <RedoOutlined
            style={{
              fontSize: 25,
              color: '#00e600',
              transform: 'translateX(28px)',
            }}
          />
          <div>设备安全重启</div>
        </Popconfirm>
      </div>
      <div>
        <Tooltip title="关机"></Tooltip>
        <Popconfirm title="是否确认关机?" onConfirm={handleOff}>
          <PoweroffOutlined
            style={{
              fontSize: 25,
              color: 'red',
              transform: 'translateX(28px)',
            }}
          />
          <div>设备安全关机</div>
        </Popconfirm>
      </div>
    </div>
  );
};
export default OnOff;
