#!/usr/bin/env python3
# -*- coding:utf-8 -*-
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator, check_flask_args
from config.config import UserConfig, NdrLog
from utils import param_check
from flask import request
import datetime
import time
from clickhouse_driver import Client as CH_client
from config.config import ClickHouseConfig
from utils.logger import get_ndr_logger, LogToDb
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class DpilogResource(Resource):
    def __init__(self):
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)

    def __del__(self):
        self.ch_client.disconnect()

    def get_val_from_database(self, sql):
        ret_list = list()

        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except:
            ndr_log_to_box(NdrLog.Type.OPERATE, "ClickHouse数据库连接或执行失败！")
            return 'ClickHouse数据库连接或执行失败！', ret_list

        if raw is None or not raw[0]:
            return 'Not found!', ret_list

        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]

        for i in range(len(raw_val)):
            data = {}

            for idx, item in enumerate(raw_val[i]):
                data[raw_key[idx][0]] = item

            ret_list.append(data)

        return "", ret_list


class DpilogIpSend(DpilogResource):
    def __init__(self):
        super(DpilogIpSend, self).__init__()

    def __del__(self):
        super(DpilogIpSend, self).__del__()

    @param_check.check_flask_args(Validator('dpilog_ip_info_schema'), request)
    def get(self, **kwargs):
        celeryId = kwargs['celeryId']
        sql = "select ts from dpilog_conn where celeryId = '%s' order by ts desc limit 1 " % (celeryId)
        msg, ts_list = self.get_val_from_database(sql)
        if len(ts_list) > 0:
            ts_date = ts_list[0]['ts']
        else:
            return flask_response('data is none', True, ts_list)
        date_big = ts_date
        date_one = (date_big + datetime.timedelta(days=-1))
        date_two = (date_big + datetime.timedelta(days=-2))
        date_three = (date_big + datetime.timedelta(days=-3))
        date_four = (date_big + datetime.timedelta(days=-4))

        date_big_1 = str(date_big)[0:11] + "00:00:00"
        date_big_2 = str(date_big)[0:11] + "23:59:59"

        date_one_1 = str(date_one)[0:11] + "00:00:00"
        date_one_2 = str(date_one)[0:11] + "23:59:59"

        date_two_1 = str(date_two)[0:11] + "00:00:00"
        date_two_2 = str(date_two)[0:11] + "23:59:59"

        date_three_1 = str(date_three)[0:11] + "00:00:00"
        date_three_2 = str(date_three)[0:11] + "23:59:59"

        date_four_1 = str(date_four)[0:11] + "00:00:00"
        date_four_2 = str(date_four)[0:11] + "23:59:59"

        ip_list = []
        ip1 = self.ip_search(celeryId, date_big_2, date_big_1)
        ip2 = self.ip_search(celeryId, date_one_2, date_one_1)
        ip3 = self.ip_search(celeryId, date_two_2, date_two_1)
        ip4 = self.ip_search(celeryId, date_three_2, date_three_1)
        ip5 = self.ip_search(celeryId, date_four_2, date_four_1)
        ip_list.append(ip1)
        ip_list.append(ip2)
        ip_list.append(ip3)
        ip_list.append(ip4)
        ip_list.append(ip5)
        return flask_response(msg, True, ip_list)

    def ip_search(self, celeryId, date1, date2):
        sql = "select sum(orig_ip_bytes) as total ,src_ip as ip from " \
              " dpilog_conn where celeryId = '%s' and ts >= '%s' and ts <= '%s' group by src_ip order by total desc limit 20" \
              % (celeryId, date2, date1)
        LOG.info("--------" + sql)
        msg, ret_list = self.get_val_from_database(sql)
        ip_list = []
        for ip_send in ret_list:
            ip = ip_send['ip']
            total = ip_send['total']
            ip_rst = {}
            ip_rst["ip"] = ip
            ip_rst["total"] = round((float(total) / 1024 / 1024), 2)
            ip_list.append(ip_rst)
        ip_list_info = {}
        ip_list_info['ts'] = date1[0:10]
        ip_list_info['ip_list'] = ip_list
        return ip_list_info

    def judge_legal_ipv4(self, one_str):
        '''
        简单的字符串判断ip
        '''
        if '.' not in one_str:
            return False
        elif one_str.count('.') != 3:
            return False
        else:
            flag = True
            one_list = one_str.split('.')
            for one in one_list:
                try:
                    one_num = int(one)
                    if one_num >= 0 and one_num <= 255:
                        pass
                    else:
                        flag = False
                except:
                    flag = False
            return flag


class DpilogAccept(DpilogResource):
    def __init__(self):
        super(DpilogAccept, self).__init__()

    def __del__(self):
        super(DpilogAccept, self).__del__()

    @param_check.check_flask_args(Validator('dpilog_ip_info_schema'), request)
    def get(self, **kwargs):
        ip = kwargs['ip']
        celeryId = kwargs['celeryId']
        start_time = kwargs['startTime']
        end_time = kwargs['endTime']
        sql = "select sum(orig_ip_bytes) as total, dst_ip as ip from dpilog_conn where celeryId = '%s' and ts >= '%s' and ts <= '%s' " \
              " and src_ip = '%s' group by dst_ip order by total desc limit 20 " % (celeryId, start_time, end_time, ip)
        LOG.info("--------" + sql)
        msg, ret_list = self.get_val_from_database(sql)
        ip_list = []
        for ip_accept in ret_list:
            ip = ip_accept['ip']
            total = ip_accept['total']
            ip_rst = {}
            ip_rst["ip"] = ip
            ip_rst["total"] = round((float(total) / 1024 / 1024), 2)
            ip_list.append(ip_rst)
        return flask_response(msg, True, ip_list)
