/*
 * @Author: tianh
 * @Date: 2022-02-09 10:28:02
 * @LastEditors: tianh
 * @LastEditTime: 2022-05-30 17:07:37
 * @Descripttion:
 */

import React, { useEffect, useState } from 'react';
import { message } from "antd";
import { getChartData } from '@/services/collect';
import { formatTime } from '@/utils/utils';
import styles from "../../style.less";
const echarts = require('echarts');


const Charts = (props: any) => {
  const { params } = props;
  const [chartData, setchartData] = useState<any>([]);

  const initChart = () => {
    const myChart = echarts.init(document.getElementById('evidences'));
    let label = '';
    const totalData: {
      actualSize: any;
      showSize: any;
    }[][] = [];
    const kind: string[] = [];
    const xAxis = chartData.length ? chartData.map((v: any) => formatTime(v.time, 'YYYY-MM-DD HH:mm')) : [];
    if (chartData.length) {
      const { time, ...obj } = chartData[0];
      label = obj.total_bytes.actualSize.match(/\D+/g)[1];
      const keys = Object.keys(obj);
      keys.forEach((v, j) => {
        totalData[j] = [];
        kind.push(v);
        for (let i = 0; i < chartData.length; i++) {
          const len = chartData[i][v].actualSize.length - label.length;
          // 将数据为0的设置为null,即使设计最小高度也不会显示
          const actualSize =
            chartData[i][v].actualSize.substr(0, len) === '0.0' ? null : chartData[i][v].actualSize.substr(0, len);
          const data = {
            actualSize,
            showSize: chartData[i][v].showSize,
          };
          totalData[j].push(data);
        }
      });
    }
    let option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
        formatter(params: string | any[]) {
          // 数据单位格式化
          let relVal = params[0].name; // x轴名称
          relVal += "<div style='width:160px'>";
          for (let i = 0, l = params.length; i < l; i++) {
            // 数据为0时设置了null,会导致tooltip不显示，重置为0就会显示
            if (params[i].value === null) {
              params[i].value = 0;
            }
            if (params[i].value || params[i].value === 0) {
              relVal += `<span  style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params[i].color};'><span  style="display:block;padding-left:15px;margin-top:-4px">${params[i].seriesName} : ${params[i].data.showSize}</span></span><br>`;
            }
          }
          relVal += '</div>';
          return relVal;
        },
      },
      legend: {
        show: false,
        data: kind,
      },
      grid: {
        left: '3%',
        right: '1%',
        bottom: '5%',
        top: 30,
        containLabel: true,
      },
      yAxis: {
        type: 'value',
        name: label,
        splitLine: {
          lineStyle: {
            type: 'dotted',
          },
        },
        axisTick: {
          show: false,
        },
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        data: xAxis,
      },
      series: totalData.length
        ? totalData.map((v, i) => ({
            // name: logType[kind[i]],
            name: '流量',
            type: 'bar',
            stack: '总量',
            barCategoryGap: 0,
            barMinHeight: 1, // 设置最小高度
            data: v.map(va => ({
              value: va.actualSize,
              showSize: va.showSize,
            })),
          }))
        : [],
    };
    myChart.setOption(option);
  };

  const getChart = () => {
    getChartData({ ...params, time: undefined }).then(res => {
      if (res.flag) {
        setchartData(res.data);
      } else {
        message.error(res.message);
      }
    });
  };

  useEffect(()=>{
    getChart()
  }, [params])

  useEffect(() => {
    echarts.init(document.getElementById('evidences')).dispose();
    initChart();
  }, [chartData]);

  return (
    <div
      className={styles.chart}
      id="evidences"
    ></div>
  );
};

export default Charts;
