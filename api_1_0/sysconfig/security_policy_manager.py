# -*- coding: utf-8 -*-
# @Time    : 2020-09-10 14:28
# <AUTHOR> luoyf
# @File    : hdp_white_manager.py
# @Software: PyCharm
"""
自定义 自签名模型白名单 管理
"""
import base64

from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.database import MongoDB
import hashlib
import time
import json
import smtplib

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class SecurityPolicy(Resource):
    """ 安全策略处理 """

    def __init__(self):
        self.db = MongoDB("ndr")

    def get_group_name(self, group_id_list):
        rule_groups_name = []
        for id in group_id_list:
            rst = self.db.find_one("feature_group", {"groupId": id}, {"groupName": 1})
            rule_groups_name.append(rst["groupName"])
        return rule_groups_name

    def get_group_id(self, group_name_list):
        rule_groups_id = []
        for name in group_name_list:
            rst = self.db.find_one("feature_group", {"groupName": name}, {"groupId": 1})
            rule_groups_id.append(rst["groupId"])
        return rule_groups_id

    def get_interface_id(self, interface_name_list):
        interface_id = []
        for interface_name in interface_name_list:
            rst = self.db.find_one("interface_of_work", {"if_name": interface_name}, {"if_index": 1})
            interface_id.append(rst["if_index"])
        return interface_id

    def get_interface_name(self, interface_id_list):
        interface_name = []
        for interface_id in interface_id_list:
            rst = self.db.find_one("interface_of_work", {"if_index": interface_id}, {"if_name": 1})
            interface_name.append(rst["if_name"])
        return interface_name


class SecurityPolicyExport(SecurityPolicy):
    """ 安全策略导出 """

    def __init__(self):
        super(SecurityPolicyExport, self).__init__()

    def post(self):
        return_data = []
        try:
            rst = self.db.find("security_policy", {}, {"_id": 0})
        except Exception as e:
            LOG.error("security_policy export failed reason:%s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略导出失败")
            return flask_response("security_policy export failed", False, [])
        for data in rst:
            rule_groups_name = self.get_group_name(data["rule_groups"])
            data["rule_groups"] = rule_groups_name
            source_if_list = self.get_interface_name(data["source_if_list"])
            data["source_if_list"] = source_if_list
            return_data.append(data)
        ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略导出成功")
        return flask_response("security_policy export successfully", True, return_data)


class SecurityPolicyImport(SecurityPolicy):
    """ 安全策略导入 """

    def __init__(self):
        super(SecurityPolicyImport, self).__init__()

    def post(self):
        file_obj = request.files["fileName"]
        try:
            file_obj.save("/tmp/" + file_obj.filename)
            data = ""
            with open("/tmp/" + file_obj.filename, 'r') as file:
                read_data = file.readlines()
                for i in read_data:
                    data += i.strip()
            json_data_list = json.loads(data)
        except Exception as error:
            LOG.error("security_policy import failed reason:%s" % error)
            return flask_response("安全策略导入异常: 数据有误", False, {})
        fail_data = []
        success_data = []
        for json_data in json_data_list:
            try:
                policy_name = json_data["policy_name"]
                if not policy_name:
                    fail_data.append("安全策略名为空")
                    continue
                # 参数检测
                validator = Validator('security_policy_import_schema')
                msg, err_value = validator.validate(json_data)
                if msg != "success":
                    fail_data.append("安全策略: %s 参数有误" % policy_name)
                    continue
                check, msg = self.checkArgs(json_data)
                if not check:
                    fail_data.append(msg)
                    continue
                rule_groups_id = self.get_group_id(json_data["rule_groups"])
                source_if_list = self.get_interface_id(json_data["source_if_list"])
                priority = 0
                rst = self.db.find("security_policy", {}, {"priority": 1}).sort([("priority", -1)]).limit(1)
                if rst.count() > 0:
                    priority = rst[0]["priority"] + 1
                unique_id = hashlib.md5(str(time.clock()).encode('utf-8')).hexdigest()
                json_data["unique_id"] = unique_id
                json_data["priority"] = priority
                json_data["source_if_list"] = source_if_list
                json_data["rule_groups"] = rule_groups_id
                self.db.insert_one("security_policy", json_data)
                success_data.append(policy_name)
            except Exception as e:
                LOG.error("security_policy import failed reason:%s" % e)
                fail_data.append("安全策略导入异常: %s" % json_data["policy_name"])
        LOG.info("security_policy import complete")
        if not success_data:
            ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略导入失败")
            return flask_response("安全策略导入失败: %s  " % str(fail_data), False, {})
        if not fail_data:
            ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略导入成功")
            return flask_response("安全策略导入成功: %s  " % str(success_data), True, {})
        return flask_response("安全策略导入成功: %s, 安全策略导入失败: %s " % (str(success_data), str(fail_data)), True, {})

    def checkArgs(self, json_data):
        policy_name = json_data["policy_name"]
        source_if_list = json_data["source_if_list"]
        rule_groups = json_data["rule_groups"]
        rst = self.db.find("security_policy", {"policy_name": policy_name})
        if rst.count() > 0:
            return False, "安全策略: %s 已存在" % policy_name
        if not source_if_list:
            return False, "安全策略: %s 源接口为空" % policy_name
        if not rule_groups:
            return False, "安全策略: %s 规则组为空" % policy_name
        # 获取到所有的源接口
        interface_rst = self.db.find("interface_of_work", {})
        interface_list = {}
        for inter in interface_rst:
            interface_list[inter["if_name"]] = inter["if_index"]
        for i in source_if_list:
            if i not in interface_list.keys():
                return False, "安全策略: %s 源接口:%s 不存在" % (policy_name, i)
            rst = self.db.find_one("security_policy", {"source_if_list": {"$elemMatch": {"$eq": interface_list[i]}}})
            if rst:
                return False, "安全策略: %s 源接口:%s 被使用" % (policy_name, i)
        return True, ""


class SecurityPolicySingle(SecurityPolicy):
    """ 安全策略处理（增、删、改、查） """

    def __init__(self):
        super(SecurityPolicySingle, self).__init__()

    # 删除安全策略
    @param_check.check_flask_args(Validator('security_policy_delete_schema'), request)
    def delete(self, **kwargs):
        try:
            unique_id_list = kwargs["unique_id_list"]
            for unique_id in unique_id_list:
                self.db.delete("security_policy", {"unique_id": unique_id})
        except Exception as e:
            LOG.error("security_policy  delete failed. Reason：%s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略删除失败。")
            return flask_response("security_policy delete failed", False, {})
        LOG.info("security_policy delete successfully")
        ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略删除成功。")
        return flask_response("security_policy delete successfully.", True, {})

    # 获取安全策略
    @param_check.check_flask_args(Validator('security_policy_get_schema'), request)
    def get(self, **kwargs):
        """
        :return:
        """
        try:
            policy_name = kwargs["policy_name"]
            if policy_name:
                policy = self.db.find_one("security_policy", {"policy_name": policy_name})
                if policy:
                    return flask_response("policy_name: %s existed" % policy_name, False, {})
                else:
                    return flask_response("policy_name: %s 可用" % policy_name, True, {})
            return_data = []
            rst = self.db.find("security_policy", {}, {"_id": 0}).sort([("priority", -1)])
            for data in rst:
                rule_groups_name = self.get_group_name(data["rule_groups"])
                data["rule_groups"] = rule_groups_name
                source_if_list = self.get_interface_name(data["source_if_list"])
                data["source_if_list"] = source_if_list
                return_data.append(data)
            return flask_response("security_policy get successful", True, return_data)
        except Exception as error:
            LOG.error("security_policy get failed. Reason：%s" % str(error))
            return flask_response("security_policy get failed", False, {})

    # 添加安全策略
    @param_check.check_flask_args(Validator('security_policy_schema'), request)
    def post(self, **kwargs):
        """
        :return:
        """
        action = kwargs["action"]
        rule_groups_id = self.get_group_id(kwargs["rule_groups"])
        source_if_list = self.get_interface_id(kwargs["source_if_list"])
        priority = kwargs["priority"]
        unique_id = kwargs["unique_id"]
        body = kwargs["args"]
        body['rule_groups'] = rule_groups_id
        body['source_if_list'] = source_if_list
        del body["action"]
        if action == "add":
            try:
                rst = self.db.find("security_policy", {"policy_name": kwargs["policy_name"]})
                if rst.count() > 0:
                    return flask_response("security_policy [ %s ] existed" % kwargs["policy_name"], False, {})
                rst = self.db.find("security_policy", {}, {"priority": 1}).sort([("priority", -1)]).limit(1)
                if rst.count() > 0:
                    priority = rst[0]["priority"] + 1
                unique_id = hashlib.md5(str(time.clock()).encode('utf-8')).hexdigest()
                body['priority'] = priority
                body['unique_id'] = unique_id
                self.db.insert_one("security_policy", body)
                LOG.info("security_policy add successfully")
                ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略[%s]添加成功。" % kwargs["policy_name"])
                return flask_response("security_policy add successfully.", True, {})
            except Exception as e:
                LOG.error("security_policy add failed : %s" % e)
                ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略[%s]添加失败。" % kwargs["policy_name"])
                return flask_response("security_policy add failed.", False, {})
        elif action == "update":
            try:
                rst = self.db.find_one("security_policy", {"unique_id": unique_id}, {"state": 1, "policy_name": 1})
                if rst["state"] == "enable" and rst["policy_name"] != "default_policy":
                    return flask_response("security_policy update failed. is enable ", False, {})
                self.db.delete("security_policy", {"unique_id": unique_id})
                self.db.insert_one("security_policy", body)
                LOG.info("security_policy update successfully")
                ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略[%s]更新成功。" % kwargs["policy_name"])
                return flask_response("security_policy update successfully.", True, {})
            except Exception as e:
                LOG.error("security_policy update failed : %s" % e)
                ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略[%s]更新失败。" % kwargs["policy_name"])
                return flask_response("security_policy update failed.", False, {})
        else:
            try:
                state = kwargs["state"]
                self.db.update_many("security_policy", {"unique_id": unique_id}, {"state": state})
                LOG.info("security_policy state set successfully")
                ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略状态设置成功。")
                return flask_response("security_policy state set successfully.", True, {})
            except Exception as e:
                LOG.error("security_policy state set failed : %s" % e)
                ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略状态设置失败。")
                return flask_response("security_policy state set failed.", False, {})

    # 修改安全策略
    @param_check.check_flask_args(Validator('security_policy_update_schema'), request)
    def put(self, **kwargs):
        """
        :return:
        """
        priority_list = kwargs["priority_list"]
        try:
            for i in priority_list:
                unique_id = i["unique_id"]
                policy_id = i["priority"]
                self.db.update_many("security_policy", {"unique_id": unique_id}, {"priority": policy_id})
        except Exception as e:
            LOG.error("security_policy priority update failed : %s" % e)
            return flask_response("security_policy priority update failed.", False, {})
        LOG.info("security_policy priority update successfully")
        ndr_log_to_box(NdrLog.Type.OPERATE, "安全策略优先级更新成功。")
        return flask_response("security_policy priority update successfully.", True, {})


class SecurityPolicyMailConn(Resource):
    """ 邮箱连接测试 """

    @param_check.check_flask_args(Validator('security_policy_mail_conn_schema'), request)
    def post(self, **kwargs):
        mail_server = kwargs["mail_server"]
        server_port = kwargs["server_port"]
        account = kwargs["account"]
        password = str(base64.b64decode(kwargs["password"].encode("utf-8")), 'utf-8')[0:-3]
        try:
            # smtp协议的默认端口是25
            mailserver = smtplib.SMTP(mail_server, server_port)
            # 登录邮箱
            mailserver.login(account, password)
            return flask_response("邮箱连接成功.", True, {})
        except Exception as e:
            LOG.error("mail connection failed. reason: %s" % e)
            return flask_response("邮箱连接异常，请重新输入.", False, {})


class SecurityPolicyInterface(SecurityPolicy):
    """ 安全策略接口获取 """

    def __init__(self):
        super(SecurityPolicyInterface, self).__init__()

    def get(self):
        try:
            interface_rst = self.db.find("interface_of_work", {}, {"if_name": 1})
            interface_list = []
            for inter in interface_rst:
                interface_list.append(inter["if_name"])
            rst = self.db.find("security_policy", {}, {"source_if_list": 1})
            for i in rst:
                eno_list = i["source_if_list"]
                eno_name = self.get_interface_name(eno_list)
                for eno in eno_name:
                    if eno in interface_list:
                        interface_list.remove(eno)
            return flask_response("安全策略接口获取成功.", True, interface_list)
        except Exception as e:
            LOG.error("security_policy interface get failed. reason: %s" % e)
            return flask_response("安全策略接口获取异常.", False, {})
