import React, { forwardRef } from 'react';
import { Form, Button, Select, message, Tooltip, Row, Col } from 'antd';
import { DownloadOutlined } from "@ant-design/icons";
import {
  flowAnalysisList,
  alarmAnalysisList,
  fileType,
} from '@/utils/enumList';
import { allFlow, gethandleExportData, getExportFlie } from '@/services/report';
import './index.less';
const { Option } = Select;
const reportForm = forwardRef((props: any, ref: any) => {
  const {
    formValue,
    threatenLvObj,
    eventData,
    threatenTypesData,
    dangerous,
    hotEvent,
    totalKillChains,
  } = props;
  const [form] = Form.useForm();
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then(async value => {
      let _data = {
        timeMode: formValue.timeMode,
        startTime: formValue.time[0].valueOf(),
        stopTime: formValue.time[1].valueOf(),
      };
      gethandleExportData({ ...value, ..._data }).then(res => {
        if (res.flag) {
        } else {
          message.error(res.message);
        }
      });
      let _params = {
        startTime: formValue.time[0].valueOf(),
        stopTime: formValue.time[1].valueOf(),
        exportTime: 0,
        format: value.fileType,
        data: {
          statistics_level: {},
          statistics_killchains: {},
          statistics_threat: {},
          killchains_flow: {},
          threat_flow: {},
          proto_flow: {},
          all_flow: {},
          event_list: {},
        },
      };
      // 默认热门事件
      _params.data.event_list = hotEvent;
      //  组装数据
      // 告警分析
      value.alarmAnalysis.forEach((item: string) => {
        if (item === 'statistics_level') {
          // 威胁等级
          _params.data.statistics_level = eventData;
        } else if (item === 'statistics_killchains') {
          // 杀伤链阶段统计
          _params.data.statistics_killchains = totalKillChains;
        } else if (item === 'statistics_threat') {
          // 威胁分类
          _params.data.statistics_threat = threatenTypesData;
        }
      });
      // 循环流量分析
      for (let item of value.flowAnalysis) {
        switch (item) {
          case 'all_flow':
            _params.data.all_flow = await getAllFlow({
              ...value,
              ..._data,
            });
            break;
          case 'threat_flow.slice_flow':
            //流量分析--按威胁等级统计的危险流量
            _params.data.threat_flow.slice_flow = threatenLvObj.slice_flow;
            break;
          case 'threat_flow.total':
            _params.data.threat_flow.total = threatenLvObj.total;
            break;
          // 流量分析--按应用协议统计的危险流量
          case 'proto_flow.slice_flow':
            _params.data.proto_flow.slice_flow = dangerous.slice_flow;
            break;
          case 'proto_flow.total':
            _params.data.proto_flow.total = dangerous.total;
            break;
        }
      }
      _params.exportTime = new Date().valueOf();
      getExportFlie(_params).then(res => {
        if (res.flag) {
          window.location.href = `${window.location.origin}/mica-api/api/v1/report/export?format=${_params.format}&exportTime=${_params.exportTime}`;
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const getAllFlow = (data: object) => {
    return new Promise((resolve, reject) => {
      allFlow(data).then(res => {
        if (!res.flag) {
          message.error(res.message);
          reject();
        } else {
          resolve(res.data);
        }
      });
    });
  };
  const formItemLayout = {
    labelCol: {
      span: 4, // * ≥576px
    },
    wrapperCol: {
      span: 15,
    },
  };
  return (
    <div>
      <Form
        {...formItemLayout}
        form={form}
        ref={ref}
        layout="inline"
        onFinish={onSubmit}
      >
        <Row>
          <Col span={5}>
            <Form.Item
              label="告警分析"
              name="alarmAnalysis"
              initialValue={['statistics_level']}
              rules={[{ required: true, message: '请选择告警分析' }]}
            >
              <Select mode="multiple">
                {alarmAnalysisList.map((item: any) => {
                  return (
                    <Option value={item.value} key={item.value}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item
              label="流量分析"
              name="flowAnalysis"
              initialValue={['all_flow']}
              rules={[{ required: true, message: '请选择流量分析' }]}
            >
              <Select style={{ width: '300px !important' }} mode="multiple">
                {flowAnalysisList.map((item: any) => {
                  return (
                    <Option value={item.value} key={item.value}>
                      <Tooltip title={item.key}>{item.key}</Tooltip>
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item
              label="导出格式"
              name="fileType"
              initialValue={'pdf'}
              rules={[{ required: true, message: '清选择导出格式' }]}
            >
              <Select>
                {fileType.map((item: any) => {
                  return (
                    <Option value={item.value} key={item.value}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item label="热门事件">
              <Select disabled={true} value={0}>
                <Option value={0}>热门事件TOP10</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                icon={<DownloadOutlined />}
                className='margin_left_30'>
                导出
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
});

export default reportForm;
