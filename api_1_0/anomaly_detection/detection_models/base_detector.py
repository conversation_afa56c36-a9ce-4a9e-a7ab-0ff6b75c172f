# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : base_detector.py
# @Software: PyCharm

"""异常检测基础类"""

import uuid
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from config.config import NdrLog, ClickHouseConfig
from utils.clickhouse_client import ClickHouseClient

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class BaseDetector(ABC):
    """异常检测基础类"""

    def __init__(self, task_config: Dict[str, Any]):
        self.task_config = task_config
        self.task_id = task_config.get('task_id')
        self.model_type = task_config.get('model_type')
        self.target_type = task_config.get('target_type')
        self.target_value = task_config.get('target_value')
        self.detection_config = task_config.get('detection_config', {})

        # 数据库连接
        self.mongodb = MongoDB('ndr')
        self.ch_client = ClickHouseClient()

    @abstractmethod
    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        执行异常检测

        Args:
            start_time: 检测开始时间
            end_time: 检测结束时间

        Returns:
            检测结果列表
        """
        pass

    def save_result(self, detection_results: List[Dict[str, Any]], detection_time: datetime):
        """保存检测结果"""
        try:
            for result in detection_results:
                result_data = {
                    'result_id': str(uuid.uuid4()),
                    'task_id': self.task_id,
                    'model_type': self.model_type,
                    'target_type': self.target_type,
                    'target_value': self.target_value,
                    'detection_time': detection_time,
                    'create_time': datetime.now(),
                    'status': 'new',
                    'confirmed': False,
                    **result
                }

                self.mongodb.insert_one('anomaly_results', result_data)

                # 如果是高危异常，创建告警
                if result.get('severity') in ['high', 'critical']:
                    self.create_alert(result_data)

        except Exception as e:
            LOG.error(f"保存检测结果失败: {str(e)}")

    def create_alert(self, result_data: Dict[str, Any]):
        """创建告警"""
        try:
            alert_data = {
                'alert_id': str(uuid.uuid4()),
                'result_id': result_data['result_id'],
                'task_id': result_data['task_id'],
                'model_type': result_data['model_type'],
                'target_value': result_data['target_value'],
                'severity': result_data['severity'],
                'anomaly_score': result_data.get('anomaly_score', 0),
                'description': result_data.get('description', ''),
                'alert_time': result_data['detection_time'],
                'create_time': datetime.now(),
                'status': 'pending',
                'handled_by': None,
                'notes': ''
            }

            self.mongodb.insert_one('anomaly_alerts', alert_data)
            LOG.info(f"创建告警成功: {alert_data['alert_id']}")

        except Exception as e:
            LOG.error(f"创建告警失败: {str(e)}")

    def get_historical_data(self, table: str, start_time: datetime, end_time: datetime,
                          additional_conditions: str = "") -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            # 构建IP条件
            if self.target_type == 'ip':
                if ':' in self.target_value:
                    ip_condition = f"(src_ipv6=toIPv6('{self.target_value}') OR dst_ipv6=toIPv6('{self.target_value}'))"
                else:
                    ip_condition = f"(src_ipv4=toIPv4('{self.target_value}') OR dst_ipv4=toIPv4('{self.target_value}'))"
            elif self.target_type == 'domain':
                if table == 'dpilog_dns':
                    ip_condition = f"query LIKE '%{self.target_value}%'"
                elif table == 'dpilog_http':
                    ip_condition = f"host LIKE '%{self.target_value}%'"
                else:
                    return []
            else:
                return []

            # 构建SQL查询
            sql = f"""
                SELECT * FROM {table}
                WHERE ts >= '{start_time.strftime('%Y-%m-%d %H:%M:%S')}'
                AND ts <= '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'
                AND {ip_condition}
                {additional_conditions}
                ORDER BY ts DESC
                LIMIT 10000
            """

            result = self.ch_client.execute(sql)
            return result if result else []

        except Exception as e:
            LOG.error(f"获取历史数据失败: {str(e)}")
            return []

    def calculate_z_score(self, value: float, mean: float, std: float) -> float:
        """计算Z-score"""
        if std == 0:
            return 0
        return abs(value - mean) / std

    def get_severity_level(self, anomaly_score: float) -> str:
        """根据异常分数确定严重程度"""
        if anomaly_score >= 0.9:
            return 'critical'
        elif anomaly_score >= 0.7:
            return 'high'
        elif anomaly_score >= 0.5:
            return 'medium'
        else:
            return 'low'

    def __del__(self):
        """清理资源"""
        try:
            if hasattr(self, 'ch_client'):
                self.ch_client.disconnect()
        except:
            pass

    @abstractmethod
    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        执行异常检测

        Args:
            start_time: 检测开始时间
            end_time: 检测结束时间

        Returns:
            检测结果列表
        """
        pass

    def save_result(self, detection_results: List[Dict[str, Any]], detection_time: datetime):
        """保存检测结果"""
        try:
            for result in detection_results:
                result_data = {
                    'result_id': str(uuid.uuid4()),
                    'task_id': self.task_id,
                    'model_type': self.model_type,
                    'target_type': self.target_type,
                    'target_value': self.target_value,
                    'detection_time': detection_time,
                    'create_time': datetime.now(),
                    'status': 'new',
                    'confirmed': False,
                    **result
                }

                self.mongodb.insert_one('anomaly_results', result_data)

                # 如果是高危异常，创建告警
                if result.get('severity') in ['high', 'critical']:
                    self.create_alert(result_data)

        except Exception as e:
            LOG.error(f"保存检测结果失败: {str(e)}")

    def create_alert(self, result_data: Dict[str, Any]):
        """创建告警"""
        try:
            alert_data = {
                'alert_id': str(uuid.uuid4()),
                'result_id': result_data['result_id'],
                'task_id': result_data['task_id'],
                'model_type': result_data['model_type'],
                'target_value': result_data['target_value'],
                'severity': result_data['severity'],
                'anomaly_score': result_data.get('anomaly_score', 0),
                'description': result_data.get('description', ''),
                'alert_time': result_data['detection_time'],
                'create_time': datetime.now(),
                'status': 'pending',
                'handled_by': None,
                'notes': ''
            }

            self.mongodb.insert_one('anomaly_alerts', alert_data)
            LOG.info(f"创建告警成功: {alert_data['alert_id']}")

        except Exception as e:
            LOG.error(f"创建告警失败: {str(e)}")

    def get_historical_data(self, table: str, start_time: datetime, end_time: datetime,
                          additional_conditions: str = "") -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            # 构建IP条件
            if self.target_type == 'ip':
                if ':' in self.target_value:
                    ip_condition = f"(src_ipv6=toIPv6('{self.target_value}') OR dst_ipv6=toIPv6('{self.target_value}'))"
                else:
                    ip_condition = f"(src_ipv4=toIPv4('{self.target_value}') OR dst_ipv4=toIPv4('{self.target_value}'))"
            elif self.target_type == 'domain':
                if table == 'dpilog_dns':
                    ip_condition = f"query LIKE '%{self.target_value}%'"
                elif table == 'dpilog_http':
                    ip_condition = f"host LIKE '%{self.target_value}%'"
                else:
                    return []
            else:
                return []

            # 构建SQL查询
            sql = f"""
                SELECT * FROM {table}
                WHERE ts >= '{start_time.strftime('%Y-%m-%d %H:%M:%S')}'
                AND ts <= '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'
                AND {ip_condition}
                {additional_conditions}
                ORDER BY ts DESC
                LIMIT 10000
            """

            result = self.ch_client.execute(sql)
            return result if result else []

        except Exception as e:
            LOG.error(f"获取历史数据失败: {str(e)}")
            return []

    def calculate_z_score(self, value: float, mean: float, std: float) -> float:
        """计算Z-score"""
        if std == 0:
            return 0
        return abs(value - mean) / std

    def get_severity_level(self, anomaly_score: float) -> str:
        """根据异常分数确定严重程度"""
        if anomaly_score >= 0.9:
            return 'critical'
        elif anomaly_score >= 0.7:
            return 'high'
        elif anomaly_score >= 0.5:
            return 'medium'
        else:
            return 'low'

    def __del__(self):
        """清理资源"""
        try:
            if hasattr(self, 'ch_client'):
                self.ch_client.disconnect()
        except:
            pass
