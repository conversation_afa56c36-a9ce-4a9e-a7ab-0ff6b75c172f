/*
 * @Author: tianh
 * @Date: 2022-01-18 16:26:51
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-06 10:54:19
 * @Descripttion:
 */
import React, { useState, useEffect } from 'react';
import { Input, Button, Table, Select, Form, Row, Col, Modal, message } from 'antd';
import { SearchOutlined } from "@ant-design/icons";
import { forwardingMode, modelStatus } from '@/utils/enumList';
import { getInterfaceList, getWork, addInterface, deleteInterface, changeInterfaceStatus } from '@/services/configs';

const { Option } = Select;

const Interface = (props: any) => {
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [params, setparams] = useState({
    group_name: '',
  });
  const [interfaceList, setinterfaceList] = useState([]);
  const [visible, setvisible] = useState(false);
  const [tableData, settableData] = useState([]);
  const handleOk = () => {
    form.validateFields().then(value => {
      value.interface_list = [value.list1, value.list2];
      delete value.list1;
      delete value.list2;
      addInterface(value).then(res => {
        if (res.flag) {
          form.resetFields();
          setvisible(false);
          message.success(res.message);
          getList();
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const handleAdd = () => {
    getWorkList();
    setvisible(true);
  };
  useEffect(() => {
    getList();
  }, [params]);

  const getList = () => {
    getInterfaceList(params.group_name).then(res => {
      if (res.flag) {
        settableData(res.data.data);
      }
    });
  };

  const handleDelete = (data: any) => {
    deleteInterface(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };
  const search = () => {
    searchForm.validateFields().then(value => {
      setparams(value);
    });
  };
  const handle = (value: any) => {
    let data = {
      status: '',
      group_name: value.group_name,
    };
    if (value.status === 'enable') {
      data.status = 'disable';
    } else {
      data.status = 'enable';
    }
    changeInterfaceStatus(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };

  const getWorkList = () => {
    getWork().then(res => {
      if (res.flag) {
        setinterfaceList(res.data);
      }
    });
  };

  const columns = [
    {
      title: '转发组名称',
      dataIndex: 'group_name',
    },
    {
      title: '转发模式',
      dataIndex: 'forword_mode',
      render: (t: any) => {
        return '二层透传模式';
      },
    },
    {
      title: '接口列表',
      dataIndex: 'interface_list',
      render: (t: any, record: any) => {
        if (t) {
          return t.toString();
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (t: any) => {
        if (t === 'enable') {
          return '生效';
        } else {
          return '未生效';
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              disabled={record.status === 'enable'}
              type="link"
              onClick={() => {
                handle(record);
              }}
            >
              启用
            </Button>
            <Button
              type="link"
              onClick={() => {
                handle(record);
              }}
              disabled={record.status !== 'enable'}
            >
              禁用
            </Button>
            <Button
              type="link"
              disabled={record.status === 'enable'}
              onClick={() => {
                handleDelete(record.group_name);
              }}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <Form onFinish={search} form={searchForm}>
        <Row>
          <Col span={6}>
            <Form.Item label="转发组名称" name="group_name">
              <Input allowClear placeholder='请输入转发组名称' />
            </Form.Item>
          </Col>
          <Col offset={12} span={6}>
            <div className="group_btns">
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                查询
              </Button>
              <Button type="primary" ghost className="midBtn" onClick={handleAdd}>
                添加
              </Button>
            </div>
          </Col>
        </Row>
      </Form>

      <Table rowKey={(r, i) => r.group_name} dataSource={tableData} pagination={false} columns={columns} />
      <Modal
        destroyOnClose={true}
        title="新建转发组"
        visible={visible}
        onOk={handleOk}
        onCancel={() => {
          form.resetFields();
          setvisible(false);
        }}
      >
        <Form form={form}>
          <Form.Item
            name="group_name"
            label="转发组名称"
            rules={[
              {
                required: true,
                message: '请填写!',
              },
              {
                pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
                message: '请输入中文、字母、数字、下划线',
              },
              {
                max: 31,
                message: '最大长度31位',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="转发模式"
            name="forword_mode"
            rules={[
              {
                required: true,
                message: '请选择!',
              },
            ]}
          >
            <Select>
              {forwardingMode.map(item => {
                return (
                  <Option value={item.value} key={item.value}>
                    {item.key}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="接口1"
            name="list1"
            rules={[
              {
                required: true,
                message: '请选择!',
              },
            ]}
          >
            <Select>
              {interfaceList.map((item: any) => {
                return (
                  <Option disabled={item.work_mode === 'black_hole'} value={item.if_name} key={item.if_name}>
                    {item.if_name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="接口2"
            name="list2"
            rules={[
              {
                required: true,
                message: '请选择!',
              },
            ]}
          >
            <Select>
              {interfaceList.map((item: any) => {
                return (
                  <Option disabled={item.work_mode === 'black_hole'} value={item.if_name} key={item.if_name}>
                    {item.if_name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="是否启用"
            name="status"
            rules={[
              {
                required: true,
                message: '请选择!',
              },
            ]}
          >
            <Select>
              {modelStatus.map((item: any) => {
                return (
                  <Option value={item.value} key={item.value}>
                    {item.key}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <p>提示：只有当工作口设置为"二层转发模式"时，接口才能被选择。</p>
        </Form>
      </Modal>
    </div>
  );
};
export default Interface;
