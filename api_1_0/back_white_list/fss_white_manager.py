# -*- coding: utf-8 -*-
# @Time    : 2020-09-10 14:28
# <AUTHOR> luoyf
# @File    : hdp_white_manager.py
# @Software: PyCharm
"""
自定义 自签名模型白名单 管理
"""

import datetime, os
import sqlite3
import xlrd
from flask import request
from flask_restful import Resource
from config.config import WhiteListCfg, NdrLog, SystemFeaturePath
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response, run_command, supervisorctl_host_service
from utils.cluster import *
from api_1_0.utils.flask_log import ndr_log_to_box
from utils.database import MongoDB

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class FssWhiteResource(Resource):
    def __init__(self):
        self.sqlite = sqlite3.connect(WhiteListCfg.DbPath)
        self.cursor = self.sqlite.cursor()
        self.mongodb = MongoDB("ndr")
        self.policy = self.mongodb.find_one('system_config', {})['full_stream_storage_policy']

    def __del__(self):
        try:
            self.sqlite.close()
        except:
            pass

    def hdp_update_white_list(self):
        if self.policy == 'full_store':
            run_command('rm -fr %s' % WhiteListCfg.Hdp_WhiteSigPath)
            supervisorctl_host_service('reatart', 'hdp')
            return True
        elif self.policy == 'user_define':
            write_mode = "w"
        else:
            write_mode = "a+"
            run_command('cp %s %s' % (WhiteListCfg.WhiteListDefaultPath, WhiteListCfg.WhiteRulePath))

        target_list = self.cursor.execute("select type,target from fss_white where source='user_def';").fetchall()
        with open(WhiteListCfg.WhiteRulePath, write_mode) as white_fd:
            for target in target_list:
                if not target[1]:
                    continue
                # 按照规定格式写入白名单文件；format：'domain;www.google.com'
                white_fd.write(target[0] + ';' +target[1] + '\n')

        sipack_path = os.path.join(SystemFeaturePath.RULES_PATH, 'whitelist-sigpack-en.dat')
        ret, msg = run_command(WhiteListCfg.Hdp_WhiteCompile + ' --featurePath %s --sigPackPath %s' % (
            WhiteListCfg.WhiteRulePath, sipack_path))

        if not ret:
            LOG.error("fss_white file [%s] complile failed. Reason: %s。" % (WhiteListCfg.WhiteRulePath, msg))
            return False

        run_command('cp %s %s' % (sipack_path, WhiteListCfg.Hdp_WhiteSigPath))

        supervisorctl_host_service('reatart', 'hdp')
        # 同步文件并重启hdp
        cluster_sync_file_and_restart_hdp(WhiteListCfg.Hdp_WhiteSigPath)

        return True


class FssWhiteConfig(FssWhiteResource):
    def __init__(self):
        super(FssWhiteConfig, self).__init__()

    def __del__(self):
        super(FssWhiteConfig, self).__del__()

    @param_check.check_flask_args(Validator('fss_white_config_post_schema'), request)
    def post(self, **kwargs):
        self.mongodb.update('system_config', {}, {'full_stream_storage_policy': kwargs['policy']})

        self.policy = kwargs['policy']

        if not self.hdp_update_white_list():
            return flask_response("fss_white complile failed", False, {})

        return flask_response("设置成功", True, {})

    def get(self):
        return flask_response("", True, {'policy': self.policy})


class FssWhiteImport(FssWhiteResource):
    def __init__(self):
        super(FssWhiteImport, self).__init__()

    def __del__(self):
        super(FssWhiteImport, self).__del__()

    def post(self):
        file_obj = request.files["fileName"]
        if not os.path.exists(WhiteListCfg.DbPath):
            LOG.error("The white list database file does not exist.")
            ndr_log_to_box(NdrLog.Type.OPERATE, "白名单数据库文件不存在。")
            return flask_response("The white list file does not exist.", False, {"errorCode": 409})
        try:
            file_obj.save(WhiteListCfg.ImportExcelPath + "/" + file_obj.filename)
        except Exception as error:
            LOG.error("fss_white file [%s] import failed.Reason: %s。" % (file_obj.filename, str(error)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "[%s]导入失败：%s。" % (file_obj.filename, str(error)))
            return flask_response(str(error), False, {})
        data = xlrd.open_workbook(WhiteListCfg.ImportExcelPath + "/" + file_obj.filename)
        table = data.sheet_by_index(0)
        create_time = datetime.datetime.now().strftime("%Y-%m-%d")
        try:
            for num in range(table.nrows):
                if num > 0:
                    parm = []
                    parm += table.row_values(num)
                    parm_str = str(parm)
                    parm_str = str(parm_str[1: -1]) + ",'%s' ,'%s'" % (create_time, 'user_def')

                    try:
                        self.cursor.execute(
                            "INSERT INTO fss_white VALUES (%s)" % parm_str)
                        self.sqlite.commit()
                    except Exception as error:
                        LOG.error("Import failed.Reason: %s." % str(error))
                        continue
            add_count = self.sqlite.total_changes
        except Exception as error:
            LOG.error("fss_white import failed.Reason: %s." % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "[%s]导入失败：%s。" % (file_obj.filename, str(error)))
            return flask_response(str(error), False, {})

        if not self.hdp_update_white_list():
            return flask_response("fss_white complile failed", False, {})

        LOG.info("fss_white file [%s] import successfully." % file_obj.filename)
        ndr_log_to_box(NdrLog.Type.OPERATE, "全流量留存过滤白名单[%s]导入成功。数量：%d" % (file_obj.filename, add_count))

        cluster_sync_file(WhiteListCfg.DbPath)

        return flask_response("fss_white import successfully.Count: %d" % add_count, True, {})


class FssWhite(FssWhiteResource):
    """ 全流量留存白名单 处理（增、删、查） """

    def __init__(self):
        super(FssWhite, self).__init__()

    def __del__(self):
        super(FssWhite, self).__del__()

    # 删除全流量留存白名单
    def delete(self):
        args = request.json
        if 'target_list' not in args:
            return flask_response("参数传输错误", False, {})

        target_list = args["target_list"]

        # 删除所有
        if not target_list:
            self.cursor.execute("DELETE from fss_white;")
            self.sqlite.commit()
            ndr_log_to_box(NdrLog.Type.OPERATE, "删除所有全流量留存过滤白名单成功。[%s]" % target_list)

            return flask_response("fss_white delete all successfully.", True, {})

        param = ""
        for target in target_list:
            if not target:
                continue
            param = param + "'%s'" % target + ","

        if param != "":
            param = "(" + param[:-1] + ")"
        else:
            param = "()"

        try:
            self.cursor.execute("DELETE from fss_white where target in %s;" % param)

            self.sqlite.commit()
        except Exception as error:
            LOG.error("fss_white delete failed.Reason：%s" % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "全流量留存过滤白名单删除失败：%s。" % str(error))

            return flask_response("fss_white delete failed", False, {})

        if not self.hdp_update_white_list():
            return flask_response("fss_white complile failed", False, {})

        ndr_log_to_box(NdrLog.Type.OPERATE, "全流量留存过滤白名单删除成功。[%s]" % target_list)
        cluster_sync_file(WhiteListCfg.DbPath)

        return flask_response("fss_white delete successfully.", True, {})

    # 获取全流量留存白名单
    @param_check.check_flask_args(Validator('fss_white_get_schema'), request)
    def get(self, **kwargs):
        """
        :return:
        """
        target = kwargs["target"]
        if target:
            sql_cond = "where source='user_def' and target like '%%%s%%'" % target
        else:
            sql_cond = "where source='user_def'"

        try:
            limit_offset = int(kwargs['pageSize']) * (int(kwargs['page']) - 1)
            self.cursor.execute("SELECT * from fss_white %s limit %s offset %s;" % (
                sql_cond, int(kwargs['pageSize']), limit_offset))
            white_res = self.cursor.fetchall()
            white_list = []
            for row in white_res:
                temp = {
                    "target": row[0],
                    "type": row[1],
                    "tag": row[2],
                    "createTime": row[3],
                    "source": row[4]
                }
                white_list.append(temp)

            # 获取总数
            count = self.cursor.execute("SELECT count(*) from fss_white %s;" % sql_cond).fetchone()

            data = {"white_list": white_list, "total": count[0], "pageSize": int(kwargs['pageSize'])}

            return flask_response("fss_white get successful", True, data)
        except Exception as error:
            LOG.error("fss_white get failed. Reason：%s" % str(error))

            return flask_response("fss_white get failed", False, {})

    # 添加全流量留存白名单
    @param_check.check_flask_args(Validator('fss_white_post_schema'), request)
    def post(self, **kwargs):
        """
        :return:
        """
        if not kwargs['target_list']:
            return flask_response("参数传输错误", False, {})

        create_time = datetime.datetime.now().strftime("%Y-%m-%d")

        for target in kwargs['target_list']:
            if not target['target']:
                return flask_response("白名单不能为空", False, {})
            parm_str = "'%s','%s','%s','%s','%s'" % (target['target'], target['type'], target['tag'], create_time, 'user_def')
            try:
                self.cursor.execute("INSERT INTO fss_white VALUES (%s)" % parm_str)
                self.sqlite.commit()
            except Exception as error:
                if "UNIQUE constraint" in str(error):
                    error = "[%s] already exist." % target['target']
                LOG.error("fss_white add failed.Reason：%s" % str(error))
                ndr_log_to_box(NdrLog.Type.OPERATE, "全流量留存过滤白名单添加失败：%s。" % str(error))

                return flask_response(str(error), False, {})

        if not self.hdp_update_white_list():
            return flask_response("fss_white complile failed", False, {})

        LOG.info("fss_white add successfully")
        ndr_log_to_box(NdrLog.Type.OPERATE, "全流量留存过滤白名单添加成功。")

        cluster_sync_file(WhiteListCfg.DbPath)

        return flask_response("fss_white add successfully.", True, {})
