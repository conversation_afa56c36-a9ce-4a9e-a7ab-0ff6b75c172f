# -*- coding: utf-8 -*-
# @Time    : 2020-04-14 14:35
# <AUTHOR> wu
# @File    : pcap.py
# @Software: PyCharm

"""module explore"""

import os
import json
import shutil
import psutil
import datetime
from flask import request
from flask_restful import Resource
from config.config import CustomizedPcapPath, NdrLog
from utils.logger import get_ndr_logger
from utils.utils import flask_response, pcap_fix
from utils.database import MongoDB
from utils.param_check import Validator, check_flask_args
from utils.json_format import JSONEncoder
from utils.best_show_size import size_format
from utils.cluster import *
from api_1_0.auth.auths import Authenticate
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class PcapChunk(Resource):
    """
    自定义 pcpap 包分片处理
    """

    def __init__(self):
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    # @check_flask_args(Validator("pcap_chunk_schema"), request)
    def post(self):
        """
        :return:
        """
        disk_info = psutil.disk_usage('/var')
        if disk_info[3] > 80:
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件上传失败：磁盘空间剩余不足。")
            return flask_response("Insufficient disk space.", False, {})
        file_obj = request.files.get("fileObject")
        file_name = request.form.get("fileName")
        file_id = request.form.get("fileId")
        chunk_id = request.form.get("chunkId")
        cover = request.form.get("cover")
        dir_name = request.form.get("dir")
        path = CustomizedPcapPath.Path + dir_name
        if os.path.exists(path + '/' + file_name):
            if cover == "false":
                ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]已存在。" % file_name)
                return flask_response("File [%s] already exist" % file_name, False, {"errorCode": 409})
            os.remove(path + '/' + file_name)
        try:
            if not os.path.exists(path + '/' + file_id):
                os.makedirs(path + '/' + file_id)
            with open(path + '/' + file_id + '/' + chunk_id, 'wb') as file:
                file.write(file_obj.read())
        except Exception as error:
            LOG.error(error)
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]上传失败：%s。" % (file_name, error))
            return flask_response(error, False, {})
        return flask_response("", True, {})


class PcapUpload(Resource):
    """
    自定义 pcpap 包分片传输完成处理
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    @check_flask_args(Validator("pcap_upload_schema"), request)
    def post(self, **kwargs):
        """
        :return:
        """
        file_name = kwargs["fileName"]
        tag = kwargs["tag"]
        file_id = kwargs["fileId"]
        chunk_list = kwargs["chunkList"]
        dir_name = kwargs["dir"]
        path = CustomizedPcapPath.Path + dir_name
        try:
            if not os.path.exists(path + '/' + file_id):
                ndr_log_to_box(NdrLog.Type.OPERATE, "文件分块不存在，上传失败。")
                LOG.error("File chunk does not exist.")
                return flask_response("File chunk does not exist.", False, {})
            with open(path + '/' + file_name, 'ab') as file_pcap:
                for chunk in range(chunk_list[0], chunk_list[1] + 1):
                    chunk_file = open(path + '/' + file_id + '/' + str(chunk), 'rb')
                    file_pcap.write(chunk_file.read())
                    chunk_file.close()
            shutil.rmtree(path + '/' + file_id)
            # 支持文件检测【1: 流量分析，2: 文件分析】
            if 1 == kwargs['type']:
                pcap_fix(path + '/' + file_name)  # fix the pcap-file if needed
            body = {"fileName": file_name,
                    "tag": tag,
                    "size": os.stat(path + '/' + file_name).st_size,
                    "createByUserId": self.user_id,
                    "createByUserName": self.user_name,
                    "createTime": int(
                        os.stat(path + '/' + file_name).st_ctime * 1000),
                    "taskList": [],
                    "dir_name": dir_name,
                    "isLasted": "true",
                    "source": "upload"}
            file_obj = self.mongodb.find_one('customized_pcap', {"$and": [{"fileName": file_name},
                                                                          {"createByUserId": self.user_id},
                                                                          {"dir_name": dir_name}]})
            if file_obj is None:
                self.mongodb.insert_one("customized_pcap", body)
            else:
                body["taskList"] = file_obj["taskList"]
                self.mongodb.update('customized_pcap', {"_id": file_obj["_id"]}, body)
                LOG.info("File [%s] upload successfully." % file_name)
                ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]上传成功。" % file_name)
            self.mongodb.update_many('directory_pcap', {"dirName": dir_name},
                                     {"updateTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})

            cluster_sync_file(path + '/' + file_name)
        except Exception as error:
            LOG.error(error)
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]上传失败：%s。" % (file_name, str(error)))
            return flask_response("Upload file failed.", False, {})
        return flask_response("", True, {})


class CustomizedData(Resource):
    """
    自定义 pcpap 包处理
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    # get all file list
    @check_flask_args(Validator("pcap_get_all_schema"), request)
    def get(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        dir_name = kwargs["args"]["dir"]
        page = int(kwargs["args"]["page"])
        page_size = int(kwargs["args"]["pageSize"])
        lastest = int(kwargs["lastest"])
        condition = {"dir_name": dir_name}
        if lastest:
            t_count = self.mongodb.count('customized_pcap', {"$and": [{"dir_name": dir_name}, {"isLasted": "true"}]})
            data = {"count": t_count}
            pcap_rst = self.mongodb.find('customized_pcap', {"$and": [{"dir_name": dir_name}, {"isLasted": "true"}]})
            cols = []
            for col in pcap_rst:
                col["humanSize"] = size_format(col.get("size") / 1000)
                col = JSONEncoder().encode(col)
                cols.append(json.loads(col))
            data["detail"] = cols
            data["page"] = page
            data["pageSize"] = page_size
            return flask_response("", True, data)
        t_count = self.mongodb.count('customized_pcap', condition)
        data = {"count": t_count}
        rst = self.mongodb.find('customized_pcap', condition, {"_id": 0,
                                                               "fileName": 1,
                                                               "createByUserId": 1,
                                                               "createByUserName": 1,
                                                               "tag": 1,
                                                               "size": 1,
                                                               "createTime": 1,
                                                               "source": 1
                                                               }) \
            .sort([("createTime", -1)]).limit(page_size).skip((page - 1) * page_size)

        if rst is None:
            return flask_response('Not found', False, {})
        cols = []
        for col in rst:
            col["humanSize"] = size_format(col.get("size") / 1000)
            col = JSONEncoder().encode(col)
            cols.append(json.loads(col))
        data["detail"] = cols
        data["page"] = page
        data["pageSize"] = page_size
        return flask_response("", True, data)

    @check_flask_args(Validator("pcap_delete_schema"), request)
    def delete(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        file_list = kwargs["fileList"]
        dir = kwargs["dir"]
        all = kwargs["all"]
        rst = []
        if all == "1":
            file_list = []
            pcap_obj = self.mongodb.find('customized_pcap', {"dir_name": dir})
            for pcap in pcap_obj:
                a = {}
                a["fileName"] = pcap['fileName']
                a["userId"] = pcap['createByUserId']
                file_list.append(a)
        for file in file_list:
            # 将当前操作的用户强制认定为admin用户，TODO: 后面作用户鉴权的时候修改
            file['userId'] = self.user_id
            if self.user_name != 'admin' and file['userId'] != self.user_id:
                rst.append("Do not have enough permissions to delete [" + file['fileName'] + "].")
                LOG.error("Do not have enough permissions to delete [" + file['fileName'] + "].")
                ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]删除失败：权限不足。" % file['fileName'])
                continue
            file_obj = self.mongodb.find_one('customized_pcap', {"$and": [{"fileName": file['fileName']},
                                                                          {"dir_name": dir},
                                                                          {"createByUserId": file['userId']}]})
            if file_obj is None:
                rst.append("[" + file['fileName'] + "] not exist.")
                LOG.error("[" + file['fileName'] + "] not exist.")
                ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]删除失败：不存在。" % file['fileName'])
                continue
            if file_obj["taskList"]:
                for task in file_obj["taskList"]:
                    rst.append("[" + file['fileName'] + "] is using by task [%s]" % task)
                    LOG.error("[" + file['fileName'] + "] is using by task [%s]" % task)
                    ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]删除失败：被探索任务[%s]引用。" % (file['fileName'], task))
                continue
            try:
                os.remove(CustomizedPcapPath.Path + dir + '/' + file['fileName'])
                self.mongodb.delete("customized_pcap", {"fileName": file['fileName'], "dir_name": dir})
                LOG.info('File [%s] is deleted successfully.' % file['fileName'])
                ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]删除成功。" % file['fileName'])

                cluster_remove_file(CustomizedPcapPath.Path + dir + '/' + file['fileName'])
            except Exception as error:
                LOG.error(error)
        if rst:
            return flask_response(rst, False, {})
        self.mongodb.update_many('directory_pcap', {"dirName": dir},
                                 {"updateTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
        return flask_response("", True, {})

    @check_flask_args(Validator("pcap_modify_schema"), request)
    def put(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        file_name = kwargs["fileName"]
        dir_name = kwargs['dir']
        # uid = kwargs["userId"]
        # 将当前操作的用户强制认定为admin用户，TODO: 后面作用户鉴权的时候修改
        uid = self.user_id
        if self.user_name != 'admin':
            condition = {"$and": [{"fileName": file_name}, {"createByUserId": self.user_id}, {"dir_name": dir_name}]}
        else:
            if uid != '':
                condition = {"$and": [{"fileName": file_name}, {"createByUserId": uid}, {"dir_name": dir_name}]}
            else:
                condition = {"$and": [{"fileName": file_name}, {"dir_name": dir_name}]}
        old_file = self.mongodb.find_one('customized_pcap', condition)
        if old_file is None:
            LOG.error("Update failed. [ %s ] does not exist." % file_name)
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]修改失败：不存在。" % file_name)
            return flask_response("[ %s ] does not exist." % file_name, False, {})

        tag = kwargs["tag"]
        self.mongodb.update('customized_pcap', condition, {'tag': tag})
        self.mongodb.update_many('directory_pcap', {"dirName": dir_name},
                                 {"updateTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
        LOG.info("[ %s ] update success." % file_name)
        ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]修改成功。" % file_name)
        return flask_response("", True, {})


class CustomizedDataSinge(Resource):
    """
    单个 pcap 文件信息获取
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    @check_flask_args(Validator("pcap_get_singe_schema"), request)
    def get(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        file_name = kwargs["fileName"]
        if self.user_name != 'admin':
            condition = {"$and": [{"fileName": file_name}, {"createByUserId": self.user_id}]}
        else:
            condition = {"fileName": file_name}
        file_obj = self.mongodb.find_one('customized_pcap', condition, {"_id": 0,
                                                                        "fileName": 1,
                                                                        "tag": 1,
                                                                        "size": 1,
                                                                        "createTime": 1,
                                                                        "createByUserId": 1,
                                                                        "createByUserName": 1,
                                                                        })
        if file_obj is None:
            return flask_response("Not found", False, {})
        file_obj["humanSize"] = size_format(file_obj["size"] / 1024)
        return flask_response("", True, file_obj)


class PcapExistCheck(Resource):
    """
    检查 pcap 文件是否已存在
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    @check_flask_args(Validator("pcap_exist_check_schema"), request)
    def post(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        file_names = kwargs["fileNames"]
        dir_name = kwargs["dir"]
        rst = []
        for file in file_names:
            if os.path.exists(CustomizedPcapPath.Path + dir_name + '/' + file):
                rst.append(file)
        res = self.mongodb.find('customized_pcap', {"dir_name": dir_name})
        if res:
            self.mongodb.update_many('customized_pcap', {"dir_name": dir_name}, {"isLasted": "false"})
        return flask_response("", True, rst)


class MakePcapDir(Resource):
    """
        创建目录
        """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    @check_flask_args(Validator("dir_check_schema"), request)
    def get(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        t_count = self.mongodb.count('directory_pcap', {})
        data = {"count": t_count}
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])
        foldName = kwargs['foldName']
        if foldName:
            dir_obj = self.mongodb.find('directory_pcap', {"dirName": foldName}, {"_id": 0,
                                                                                  "dirName": 1,
                                                                                  "type": 1,
                                                                                  "createByUserName": 1,
                                                                                  "createByUserId": 1,
                                                                                  "createTime": 1,
                                                                                  "updateTime": 1
                                                                                  })
            if dir_obj is None:
                return flask_response('Not found', False, {})
            cols = []
            for col in dir_obj:
                col = JSONEncoder().encode(col)
                cols.append(json.loads(col))
            data["detail"] = cols
            return flask_response("", True, data)
        else:
            dir_obj = self.mongodb.find('directory_pcap', {}, {"_id": 0,
                                                               "dirName": 1,
                                                               "type": 1,
                                                               "createByUserName": 1,
                                                               "createByUserId": 1,
                                                               "createTime": 1,
                                                               "updateTime": 1
                                                               }) \
                .sort([("createTime", -1)]).limit(page_size).skip((page - 1) * page_size)
            if dir_obj is None:
                return flask_response('Not found', False, {})
            cols = []
            for col in dir_obj:
                col = JSONEncoder().encode(col)
                cols.append(json.loads(col))
            data["detail"] = cols
            data["page"] = page
            data["pageSize"] = page_size
            return flask_response("", True, data)

    @check_flask_args(Validator("dir_make_check_schema"), request)
    def post(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        dir_name = kwargs["dir"]
        data_type = int(kwargs['type'])  # 支持文件检测【1: 流量分析，2: 文件分析】
        if not dir_name:
            return flask_response("目录不能为空", False, {})
        if not os.path.exists(CustomizedPcapPath.Path):
            os.makedirs(CustomizedPcapPath.Path)
        os.chdir(CustomizedPcapPath.Path)
        if os.path.exists(dir_name):
            return flask_response("目录已存在", False, {})
        os.makedirs(CustomizedPcapPath.Path + dir_name)
        body = {"dirName": dir_name,
                "type": data_type,
                "createByUserId": self.user_id,
                "createByUserName": self.user_name,
                "createTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 创建时间,
                "updateTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "taskList": [],
                "dirPath": CustomizedPcapPath.Path}
        try:
            self.mongodb.insert_one("directory_pcap", body)
        except Exception as e:
            LOG.error("目录[%s]创建失败,reason:%s" % (dir_name, e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "目录[%s]创建失败" % dir_name)
            return flask_response("目录[%s]创建失败" % dir_name, False, {})
        ndr_log_to_box(NdrLog.Type.OPERATE, "目录[%s]创建成功" % dir_name)
        return flask_response("目录[%s]创建成功" % dir_name, True, {})

    @check_flask_args(Validator("dir_delete_check_schema"), request)
    def delete(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        dir_list = kwargs["fileList"]
        rst = []
        try:
            for dir in dir_list:
                if self.user_name != 'admin' and dir['userId'] != self.user_id:
                    rst.append("Do not have enough permissions to delete [" + dir['dir'] + "].")
                    LOG.error("Do not have enough permissions to delete [" + dir['dir'] + "].")
                    ndr_log_to_box(NdrLog.Type.OPERATE, "目录[%s]删除失败：权限不足。" % dir['dir'])
                    continue
                dir_obj = self.mongodb.find_one('directory_pcap', {"dirName": dir['dir']})
                pcap_res = self.mongodb.find_one('customized_pcap', {"dir_name": dir['dir']})
                if dir_obj is None:
                    rst.append("[" + dir['dir'] + "] not exist.")
                    LOG.error("[" + dir['dir'] + "] not exist.")
                    ndr_log_to_box(NdrLog.Type.OPERATE, "目录[%s]删除失败：不存在。" % dir['dir'])
                    continue
                if dir_obj["taskList"]:
                    for task in dir_obj["taskList"]:
                        rst.append("[" + dir['dir'] + "] is using by task [%s]" % task)
                        LOG.error("[" + dir['dir'] + "] is using by task [%s]" % task)
                        ndr_log_to_box(NdrLog.Type.OPERATE, "目录[%s]删除失败：被探索任务[%s]引用。" % (dir['dir'], task))
                    continue
                if pcap_res is not None:
                    rst.append("[" + dir['dir'] + "] is not empty")
                    LOG.error("[" + dir['dir'] + "] is not empty")
                    ndr_log_to_box(NdrLog.Type.OPERATE, "目录[%s]删除失败:is not empty。" % (dir['dir']))
                    continue
                self.mongodb.delete("directory_pcap", {"dirName": dir['dir']})
                LOG.info('dirName [%s] is deleted successfully.' % dir['dir'])
                ndr_log_to_box(NdrLog.Type.OPERATE, "目录[%s]删除成功。" % dir['dir'])
                try:
                    os.removedirs(CustomizedPcapPath.Path + dir['dir'])
                except Exception as error:
                    LOG.error(error)
            if rst:
                ndr_log_to_box(NdrLog.Type.OPERATE, "目录删除失败")
                return flask_response(rst, False, {})
            ndr_log_to_box(NdrLog.Type.OPERATE, "目录删除成功")
            return flask_response("", True, {})
        except Exception as e:
            LOG.error("目录删除失败,reason:%s" % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "目录删除失败")
            return flask_response("", False, {})
