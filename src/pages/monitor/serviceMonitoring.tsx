/*
 * @Author: tianh
 * @Date: 2022-02-21 11:42:20
 * @LastEditors: tianh
 * @LastEditTime: 2022-02-21 14:55:46
 * @Descripttion:
 */
import React, { useEffect, useState } from 'react';
import style from './style.less';
import moment from 'moment';
import { Table, message, Popconfirm, Button } from 'antd';
import { service, handleStatus } from '@/services/monitor';
const ServiceMonitoring = () => {
  let timer: any = null;
  const [serviceData, setserviceData] = useState([]);
  const columns = [
    {
      title: '服务名',
      dataIndex: 'name',
    },
    {
      title: '运行时间',
      dataIndex: 'runtime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (t: any, record: any) => {
        return record.status === 'running' ? '运行中' : '停止';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: any, record: any) => {
        if (record.name !== 'ndr') {
          const handle = (value: any, action: any) => {
            handleStatus({ serverName: value.name, action: action }).then(
              res => {
                if (res.flag) {
                  message.success(res.message);
                  setTimeout(() => {
                    getService();
                  }, 1000);
                }
              },
            );
          };
          return record.status === 'running' ? (
            <div>
              <Popconfirm
                title={record.stop}
                onConfirm={() => {
                  handle(record, 'stop');
                }}
              >
                <Button type="link">停止</Button>
              </Popconfirm>
              <Popconfirm
                title={record.restart}
                onConfirm={() => {
                  handle(record, 'restart');
                }}
              >
                <Button type="link">重启</Button>
              </Popconfirm>
            </div>
          ) : (
            <Popconfirm
              title={record.start}
              onConfirm={() => {
                handle(record, 'start');
              }}
            >
              <Button type="link">启动</Button>
            </Popconfirm>
          );
        }
      },
    },
  ];
  /**
   * @name:
   * @description:获取服务监控数据
   * @param {*}
   * @return {*}
   */
  const getService = () => {
    service().then(res => {
      if (res.flag) {
        setserviceData(res.data);
      }
    });
  };
  useEffect(() => {
    getService();
  }, []);
  useEffect(() => {
    timer = setInterval(() => {
      getService();
    }, 10000);
    return () => {
      clearInterval(timer);
    };
  }, []);
  return (
    <div>
      <div className={style.top_title}>
        <div className={style.title}>服务监控</div>
        <div>
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <Table
        columns={columns}
        rowKey={(r, i) => i}
        pagination={false}
        dataSource={serviceData}
      ></Table>
    </div>
  );
};
export default ServiceMonitoring;
