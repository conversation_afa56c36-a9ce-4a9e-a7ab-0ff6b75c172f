# !/usr/bin/env python3
# -*- coding:utf-8 -*-
import copy
from flask import request
from flask_restful import Resource
from clickhouse_driver import Client as CH_client

from api_1_0.file_analysis.file_statistics import FileResource
from config.config import NdrLog
from config.config import ClickHouseConfig
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response
from utils.database import get_es_client

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class MailAnalysisList(FileResource):
    """
    邮件分析列表查询
    """

    def __init__(self):
        super(MailAnalysisList, self).__init__()
        self.es_client = get_es_client()
        self.es_index = "mail_analysis"
        self.start_time = 0
        self.end_time = 0
        self.msg_id = ''
        self.threat_level = ''
        self.to = ''

    @param_check.check_flask_args(Validator("mail_analysis_list_schema"), request)
    def post(self, **kwargs):
        """
        查询邮件分析列表数据
        :return:
        """
        self.start_time = kwargs["args"]["start_time"]
        self.end_time = kwargs["args"]["end_time"]
        page = kwargs["args"].get("page", 1)
        page_size = kwargs["args"].get("pageSize", 10)
        self.msg_id = kwargs["args"].get("msg_id", "")
        self.threat_level = kwargs["args"].get("threatLevel", "")
        self.to = kwargs["args"].get("to", "")

        try:
            return_data = {}

            # 构建聚合查询条件
            query_body = self._build_aggregation_query("desc", int(page), int(page_size))

            # 执行聚合查询
            result_data = self.es_client.search(index=self.es_index, body=query_body)

            # 处理聚合结果
            return_data["total"] = result_data["aggregations"]["unique_emails"]["value"]
            return_data["detail"] = []

            for bucket in result_data["aggregations"]["emails"]["buckets"]:
                hit = bucket["latest_email"]["hits"]["hits"][0]
                source_data = copy.deepcopy(hit["_source"])  # 避免修改原始数据
                source_data["threat"] = ["已知恶意文件"]
                source_data["severity"] = 2.0
                return_data["detail"].append(source_data)

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            return flask_response(err_info, False, {})

        return flask_response('获取成功', True, return_data)

    def _build_aggregation_query(self, sort, page, page_size):
        """
        构建ES聚合查询语句
        """
        """
        构建ES聚合查询语句，使用bucket_sort实现分页
        """
        from_offset = (page - 1) * page_size
        query_body = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "range": {
                                "timestamp": {
                                    "gte": self.start_time,
                                    "lte": self.end_time
                                }
                            }
                        }
                    ]
                }
            },
            "size": 0,  # 不返回具体文档，只返回聚合结果
            "track_total_hits": True,  # 确保返回准确的总数
            "aggs": {
                "emails": {
                    "terms": {
                        "field": "msg_id.keyword",
                        "size": 2147483647  # 设置一个非常大的值以获取所有bucket
                    },
                    "aggs": {
                        "max_timestamp": {
                            "max": {
                                "field": "timestamp"
                            }
                        },
                        "latest_email": {
                            "top_hits": {
                                "size": 1,
                                "sort": [
                                    {
                                        "timestamp": {
                                            "order": "desc"
                                        }
                                    }
                                ]
                            }
                        },
                        "sort_time": {
                            "bucket_sort": {
                                "sort": {
                                    "max_timestamp": {
                                        "order": sort
                                    }
                                },
                                "from": from_offset,
                                "size": page_size
                            }
                        }
                    }
                },
                "unique_emails": {
                    "cardinality": {
                        "field": "msg_id.keyword"
                    }
                }
            }
        }

        # 添加可选查询条件
        if self.msg_id:
            query_body["query"]["bool"]["must"].append({
                "term": {
                    "msg_id.keyword": self.msg_id
                }
            })

        if self.threat_level:
            query_body["query"]["bool"]["must"].append({
                "term": {
                    "threatLevel.keyword": self.threat_level
                }
            })

        if self.to:
            query_body["query"]["bool"]["must"].append({
                "wildcard": {
                    "to.keyword": f"*{self.to}*"
                }
            })

        return query_body


class MailAnalysisDetail(FileResource):
    """
    邮件分析列表查询
    """

    def __init__(self):
        super(MailAnalysisDetail, self).__init__()
        self.es_client = get_es_client()
        self.es_index = "mail_analysis"
        self.start_time = 0
        self.end_time = 0
        self.msg_id = ''

    def get(self, **kwargs):
        """
        查询邮件详细信息，包括所有附件
        :return:
        """
        paras = request.values.to_dict()

        try:
            start_time = int(paras["start_time"])
            end_time = int(paras["end_time"])
            msg_id = paras["msg_id"]

            # 构建聚合查询
            query_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "timestamp": {
                                        "gte": start_time,
                                        "lte": end_time
                                    }
                                }
                            },
                            {
                                "term": {
                                    "msg_id.keyword": msg_id
                                }
                            }
                        ]
                    }
                },
                "aggs": {
                    "email_info": {
                        "terms": {
                            "field": "msg_id.keyword",
                            "size": 1
                        },
                        "aggs": {
                            "latest_record": {
                                "top_hits": {
                                    "size": 1,
                                    "sort": [
                                        {
                                            "timestamp": {
                                                "order": "desc"
                                            }
                                        }
                                    ]
                                }
                            },
                            "attachments": {
                                "top_hits": {
                                    "size": 100  # 假设一个邮件最多有100个附件
                                }
                            }
                        }
                    }
                },
                "size": 0  # 不返回具体文档，只返回聚合结果
            }

            # 执行查询
            result = self.es_client.search(index=self.es_index, body=query_body)

            # 处理结果
            return_data = self._format_response(result)

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            return flask_response(err_info, False, {})

        return flask_response('获取成功', True, return_data)

    def _format_response(self, result):
        """
        格式化响应数据
        """
        return_data = {
            "total": 0,
            "detail": {}
        }

        buckets = result["aggregations"]["email_info"]["buckets"]
        if not buckets:
            return return_data

        bucket = buckets[0]
        if bucket["doc_count"] == 0:
            return return_data

        # 获取邮件基本信息（使用最新的记录）
        latest_hit = bucket["latest_record"]["hits"]["hits"][0]["_source"]

        # 格式化时间戳
        timestamp_ms = latest_hit["timestamp"]
        # 转换为标准时间格式
        from datetime import datetime
        dt = datetime.fromtimestamp(timestamp_ms / 1000)
        formatted_timestamp = dt.strftime("%a, %d %b %Y %H:%M:%S +0800")

        # 构建返回数据
        return_data["total"] = 1
        return_data["detail"] = {
            "timestamp": formatted_timestamp,
            "from": latest_hit.get("from", ""),
            "to": latest_hit.get("to", []),
            "cc": latest_hit.get("cc", []),
            "bcc": latest_hit.get("bcc", []),
            "subject": latest_hit.get("subject", ""),
            "email_body": latest_hit.get("body", ""),
            "attachment": []
        }

        # 收集所有附件信息
        attachment_hits = bucket["attachments"]["hits"]["hits"]
        for hit in attachment_hits:
            source = hit["_source"]
            attachment_info = {
                "labels": source.get("labels", ""),
                "threatName": source.get("threatName", ""),
                "filename": source.get("filename", "").split("__")[-1],  # 只取文件名部分
                "filesize": source.get("filesize", 0),
                "filetype": source.get("filetype", "")
            }
            return_data["detail"]["attachment"].append(attachment_info)

        return return_data


class MailAnalysisAttachList(FileResource):
    """
    邮件分析列表查询
    """

    def __init__(self):
        super(MailAnalysisAttachList, self).__init__()
        self.es_client = get_es_client()
        self.es_index = "mail_analysis"
        self.start_time = 0
        self.end_time = 0
        self.msg_id = ''
        self.threat_level = ''
        self.file_name = ''
        self.md5 = ''

    @param_check.check_flask_args(Validator("mail_analysis_list_schema"), request)
    def post(self, **kwargs):
        """
        查询邮件分析列表数据
        :return:
        """
        self.start_time = kwargs["args"]["start_time"]
        self.end_time = kwargs["args"]["end_time"]
        page = kwargs["args"].get("page", 1)
        page_size = kwargs["args"].get("pageSize", 10)
        self.msg_id = kwargs["args"].get("msg_id", "")
        self.threat_level = kwargs["args"].get("threatLevel", "")
        self.to = kwargs["args"].get("to", "")
        self.file_name = kwargs["args"].get("filename", "")
        self.md5 = kwargs["args"].get("md5", "")

        try:
            return_data = {}

            # 构建查询条件
            query_body = self._build_query("desc")

            # 获取总数
            total_data = self.es_client.search(index=self.es_index, body=query_body)
            return_data["total"] = total_data["hits"]["total"]["value"]

            # 获取分页数据
            query_body = self._add_pagination(query_body, int(page), int(page_size))
            result_data = self.es_client.search(index=self.es_index, body=query_body)
            return_data["detail"] = []
            for data in result_data["hits"]["hits"]:
                source_data = copy.deepcopy(data["_source"])
                source_data["threat"] = ["已知恶意文件"]
                source_data["severity"] = 2.0
                return_data["detail"].append(source_data)

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            return flask_response(err_info, False, {})

        return flask_response('获取成功', True, return_data)

    def _build_query(self, sort):
        """
        构建ES查询语句
        """
        query_body = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "range": {
                                "timestamp": {
                                    "gte": self.start_time,
                                    "lte": self.end_time
                                }
                            }
                        }
                    ]
                }
            },
            "sort": [
                {
                    "timestamp": {
                        "order": sort
                    }
                }
            ]
        }

        # 添加可选查询条件
        if self.msg_id:
            query_body["query"]["bool"]["must"].append({
                "term": {
                    "msg_id.keyword": self.msg_id
                }
            })

        if self.threat_level:
            query_body["query"]["bool"]["must"].append({
                "term": {
                    "threatLevel.keyword": self.threat_level
                }
            })

        if self.file_name:
            query_body["query"]["bool"]["must"].append({
                "wildcard": {
                    "file_name.keyword": f"*{self.file_name}*"
                }
            })

        if self.md5:
            query_body["query"]["bool"]["must"].append({
                "term": {
                    "md5.keyword": self.md5
                }
            })
        return query_body

    def _add_pagination(self, query_body, page, page_size):
        """
        添加分页参数
        """
        query_body["from"] = (page - 1) * page_size
        query_body["size"] = page_size
        return query_body


class MailAnalysisRoute(FileResource):
    """
    邮件收发信息统计， 根据起止时间和邮件id赛选日志，然后聚合相关信息。
    """

    def __init__(self):
        super(MailAnalysisRoute, self).__init__()
        # 初始化ClickHouse连接
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            port=9000,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE,
        )
        self.ch_table = 'dpilog_mail'

    @param_check.check_flask_args(Validator("mail_analysis_list_schema"), request)
    def post(self, **kwargs):
        """
        查询邮件收发信息统计
        :return:
        """
        start_time = kwargs["args"]["start_time"] / 1000   # 转成秒
        end_time = kwargs["args"]["end_time"] / 1000    # 转成秒
        msg_id = kwargs["args"]["msg_id"]

        try:
            # 构建查询语句
            query = f"""
            SELECT 
                service,
                src_ip,
                dst_ip,
                create_time,
                `from`,
                to,
                cc,
                bcc,
                received
            FROM {self.ch_table}
            WHERE 
                create_time >= toDateTime(%(start_time)s) AND
                create_time <= toDateTime(%(end_time)s) AND
                msg_id = %(msg_id)s
            ORDER BY create_time
            """

            # 执行查询
            result = self.ch_client.execute(
                query,
                {
                    'start_time': start_time,
                    'end_time': end_time,
                    'msg_id': msg_id
                }
            )

            # 处理结果
            return_data = self._format_response(result)

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            return flask_response(err_info, False, {})

        return flask_response('获取成功', True, return_data)

    def _format_response(self, result):
        """
        格式化响应数据
        """
        # 初始化返回数据结构
        return_data = {
            "pop3": [],
            "imap": [],
            "smtp": [],
            "totalTo": set()  # 使用set去重
        }

        # 处理收件人字段，按分隔符拆分为数组
        def split_recipients(recipient_str):
            if not recipient_str:
                return []

            # 支持多种分隔符
            import re
            # 使用正则表达式分割，支持逗号、分号和空格作为分隔符
            recipients = re.split(r'[,;\s]+', recipient_str)
            # 过滤空字符串并去除两端空格
            return [r.strip() for r in recipients if r.strip()]

        # 处理查询结果
        all_recipients = set()  # 用于收集所有收件人

        for row in result:
            service = row[0].lower() if row[0] else ""
            src_ip = row[1] or ""
            dst_ip = row[2] or ""
            create_time = row[3] or ""
            from_addr = row[4] or ""
            to_addr = row[5] or ""
            cc_addr = row[6] or ""
            bcc_addr = row[7] or ""
            received = row[8] or ""

            # 格式化时间
            if create_time:
                from datetime import datetime
                if isinstance(create_time, str):
                    # 如果是字符串，尝试解析
                    try:
                        dt = datetime.fromisoformat(create_time)
                    except:
                        dt = datetime.strptime(create_time, "%Y-%m-%d %H:%M:%S")
                else:
                    # 如果是datetime对象
                    dt = create_time
                formatted_date = dt.strftime("%a, %d %b %Y %H:%M:%S +0800")
            else:
                formatted_date = ""

            # 收集所有收件人
            for recipient_str in [to_addr, cc_addr, bcc_addr]:
                if recipient_str:
                    recipients = split_recipients(recipient_str)
                    all_recipients.update(recipients)

            # 根据服务类型构建不同的数据结构
            if service in ["pop3", "imap"]:
                email_info = {
                    "src_ip": src_ip,
                    f"{service}_ip": dst_ip,
                    "date": formatted_date,
                    "from": from_addr,
                    "to": split_recipients(to_addr)
                }
                return_data[service].append(email_info)

            elif service == "smtp":
                email_info = {
                    "src_ip": src_ip,
                    "smtp_ip": dst_ip,
                    "date": formatted_date,
                    "from": from_addr,
                    "received": received
                }
                return_data[service].append(email_info)

        # 转换set为list
        return_data["totalTo"] = list(all_recipients)

        # 包装返回数据
        response_data = {
            "total": 1,
            "detail": return_data
        }

        return response_data