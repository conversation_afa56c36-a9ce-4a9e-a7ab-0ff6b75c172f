# -*- coding: utf-8 -*-
# @Time    : 2023-11-15 15:00
# <AUTHOR> <EMAIL>
import copy
import logging
import os
import time
import elasticsearch
import requests

from pymongo import MongoClient
from datetime import datetime
from threading import Thread, current_thread
import requests.packages.urllib3


class DownloadPcap(object):
    def __init__(self, save_dir='/var/ndr_warning_pcaps/'):
        self.mg = MongoClient(
            host='mongo', port=27017, username="ksbox", password="FBgxC5PWAre", authSource='admin'
        )['ndr']
        self.es = elasticsearch.Elasticsearch(
            [dict(
                host='elasticsearch',
                port=9200
            )],
            timeout=60000
        )
        self.save_dir = save_dir
        self.alert_type_time_offset = {
            'rule': 180,
            'ioc': 300,
            'file': 300,
            'model': 600
        }
        self.dl_limit = {
            'rule': {'max_store_size': 100 * 1024 * 1024, 'cur_size': 0, 'time_stamp': 0},
            'ioc': {'max_store_size': 120 * 1024 * 1024, 'cur_size': 0, 'time_stamp': 0},
            'model': {'max_store_size': 100 * 1024 * 1024, 'cur_size': 0, 'time_stamp': 0},
            'file': {'max_store_size': 100 * 1024 * 1024, 'cur_size': 0, 'time_stamp': 0}
        }
        self.wk_interval = 15  # 下载循环周期(单位秒)
        self.steno_url = 'https://127.0.0.1:1234/query'
        self.steno_key = '/opt/stenographer/certs/client_key.pem'
        self.steno_cert = '/opt/stenographer/certs/client_cert.pem'
        self.steno_ca = '/opt/stenographer/certs/ca_cert.pem'
        self.max_size = 10485760  # 最大下载10M
        self.es_template = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "range": {"observedTime": {"lte": 168}}
                        },
                        {  # 必须是实时任务
                            "term": {"taskId.keyword": {"value": "00"}}
                        },
                        {  # not_fss_pcapname必须有值
                            "bool": {
                                "must": {"exists": {"field": "not_fss_pcapname"}}
                            }
                        },
                        {  # 第四个条件：download_cnt字段不存在或者download_cnt值小于等于3（下载失败的给三次机会）
                            "bool": {
                                "should": [
                                    {
                                        "bool": {"must_not": {"exists": {"field": "download_cnt"}}}
                                    },
                                    {
                                        "range": {"download_cnt": {"lte": 3}}
                                    }
                                ]
                            }
                        }
                    ]
                }
            },
            "size": 200,
            "_source": {
                "includes": [
                    "occurredTime",
                    "flow",
                    "conn_id",
                    "download_cnt",
                    "not_fss_pcapname"
                ]
            }
        }
        self.proto_map = {
            "HOPOPT": "ip proto 0",
            "ICMP": "ip proto 1",
            "IGMP": "ip proto 2",
            "GGP": "ip proto 3",
            "IPv4": "ip proto 4",
            "ST": "ip proto 5",
            "TCP": "ip proto 6",
            "CBT": "ip proto 7",
            "EGP": "ip proto 8",
            "IGP": "ip proto 9",
            "BBN-RCC-MON": "ip proto 10",
            "NVP-II": "ip proto 11",
            "PUP": "ip proto 12",
            "ARGUS": "ip proto 13",
            "EMCON": "ip proto 14",
            "XNET": "ip proto 15",
            "CHAOS": "ip proto 16",
            "UDP": "ip proto 17",
            "MUX": "ip proto 18",
            "DCN-MEAS": "ip proto 19",
            "HMP": "ip proto 20",
            "PRM": "ip proto 21",
            "XNS-IDP": "ip proto 22",
            "TRUNK-1": "ip proto 23",
            "TRUNK-2": "ip proto 24",
            "LEAF-1": "ip proto 25",
            "LEAF-2": "ip proto 26",
            "RDP": "ip proto 27",
            "IRTP": "ip proto 28",
            "ISO-TP4": "ip proto 29",
            "NETBLT": "ip proto 30",
            "MFE-NSP": "ip proto 31",
            "MERIT-INP": "ip proto 32",
            "DCCP": "ip proto 33",
            "3PC": "ip proto 34",
            "IDPR": "ip proto 35",
            "XTP": "ip proto 36",
            "DDP": "ip proto 37",
            "IDPR-CMTP": "ip proto 38",
            "TP++": "ip proto 39",
            "IL": "ip proto 40",
            "IPv6": "ip proto 41",
            "SDRP": "ip proto 42",
            "IPv6-Route": "ip proto 43",
            "IPv6-Frag": "ip proto 44",
            "IDRP": "ip proto 45",
            "RSVP": "ip proto 46",
            "GRE": "ip proto 47",
            "MHRP": "ip proto 48",
            "BNA": "ip proto 49",
            "ESP": "ip proto 50",
            "AH": "ip proto 51",
            "I-NLSP": "ip proto 52",
            "SWIPE": "ip proto 53",
            "NARP": "ip proto 54",
            "MOBILE": "ip proto 55",
            "TLSP": "ip proto 56",
            "SKIP": "ip proto 57",
            "IPv6-ICMP": "ip proto 58",
            "IPv6-NoNxt": "ip proto 59",
            "IPv6-Opts": "ip proto 60",
            "CFTP": "ip proto 62",
            "SAT-EXPAK": "ip proto 64",
            "KRYPTOLAN": "ip proto 65",
            "RVD": "ip proto 66",
            "IPPC": "ip proto 67",
            "SAT-MON": "ip proto 69",
            "VISA": "ip proto 70",
            "IPCV": "ip proto 71",
            "CPNX": "ip proto 72",
            "CPHB": "ip proto 73",
            "WSN": "ip proto 74",
            "PVP": "ip proto 75",
            "BR-SAT-MON": "ip proto 76",
            "SUN-ND": "ip proto 77",
            "WB-MON": "ip proto 78",
            "WB-EXPAK": "ip proto 79",
            "ISO-IP": "ip proto 80",
            "VMTP": "ip proto 81",
            "SECURE-VMTP": "ip proto 82",
            "VINES": "ip proto 83",
            "TTP": "ip proto 84",
            "IPTM": "ip proto 84",
            "NSFNET-IGP": "ip proto 85",
            "DGP": "ip proto 86",
            "TCF": "ip proto 87",
            "EIGRP": "ip proto 88",
            "OSPF": "ip proto 89",
            "Sprite-RPC": "ip proto 90",
            "LARP": "ip proto 91",
            "MTP": "ip proto 92",
            "AX.25": "ip proto 93",
            "IPIP": "ip proto 94",
            "MICP": "ip proto 95",
            "SCC-SP": "ip proto 96",
            "ETHERIP": "ip proto 97",
            "ENCAP": "ip proto 98",
            "GMTP": "ip proto 100",
            "IFMP": "ip proto 101",
            "PNNI": "ip proto 102",
            "PIM": "ip proto 103",
            "ARIS": "ip proto 104",
            "SCPS": "ip proto 105",
            "QNX": "ip proto 106",
            "A/N": "ip proto 107",
            "IPComp": "ip proto 108",
            "SNP": "ip proto 109",
            "Compaq-Peer": "ip proto 110",
            "IPX-in-IP": "ip proto 111",
            "VRRP": "ip proto 112",
            "PGM": "ip proto 113",
            "L2TP": "ip proto 115",
            "DDX": "ip proto 116",
            "IATP": "ip proto 117",
            "STP": "ip proto 118",
            "SRP": "ip proto 119",
            "UTI": "ip proto 120",
            "SMP": "ip proto 121",
            "SM": "ip proto 122",
            "PTP": "ip proto 123",
            "IS-IS": "ip proto 124",
            "FIRE": "ip proto 125",
            "CRTP": "ip proto 126",
            "CRUDP": "ip proto 127",
            "SSCOPMCE": "ip proto 128",
            "IPLT": "ip proto 129",
            "SPS": "ip proto 130",
            "PIPE": "ip proto 131",
            "SCTP": "ip proto 132",
            "FC": "ip proto 133",
            "RSVP-E2E-IGNORE": "ip proto 134",
            "Mobility": "ip proto 135",
            "UDPLite": "ip proto 136",
            "MPLS-in-IP": "ip proto 137",
            "manet": "ip proto 138",
            "HIP": "ip proto 139",
            "Shim6": "ip proto 140",
            "WESP": "ip proto 141",
            "ROHC": "ip proto 142",
        }

    def check_device_version(self):
        # 全流量留存版本启动载数据包任务
        raw_data = self.mg['device_info'].find_one({})

        return 'NOT_FSS' if raw_data and raw_data.get('divice', 'D5000') in ['D5000', 'D8000'] else 'FSS'

    def download_pcap_file(self, es_id, data, index, index_type):
        filepath = ''
        try:
            filename = data['not_fss_pcapname']
            time_str = filename.split('_')[1]
            # example: vul_20231115_10.8.197.54_10.10.4.101_yF09FaL2a3y7LLay.pcap
            day_dir = os.path.join(self.save_dir, time_str)
            if not os.path.exists(day_dir):
                os.mkdir(day_dir)

            filepath = os.path.join(day_dir, filename)
            # 安照发现时间处理
            time_offset = self.alert_type_time_offset.get(index_type, 300)
            timestamp = int(data['occurredTime']) / 1000
            after_time = "after " + str(datetime.utcfromtimestamp(timestamp - time_offset)).replace(" ", "T") + "Z"
            before_time = "before " + str(datetime.utcfromtimestamp(timestamp + time_offset)).replace(" ", "T") + "Z"
            ip_cond = f"host {data['flow']['src_ip']} && host {data['flow']['dst_ip']}"
            port_cond = f'port {data["flow"]["src_port"]} && port {data["flow"]["dst_port"]}'
            if index_type == 'model':
                query_cond = [after_time, before_time, ip_cond]
            else:
                query_cond = [after_time, before_time, ip_cond, port_cond]

            proto = self.proto_map.get(data["flow"]["proto"], None)
            if proto:
                query_cond.append(proto)

            filesize = 0
            with open(filepath, 'wb') as f:
                rst = requests.post(
                    self.steno_url, data=' && '.join(query_cond), timeout=50,
                    cert=(self.steno_cert, self.steno_key), verify=self.steno_ca, stream=True)
                if 200 != rst.status_code:
                    logging.error('Download pcap failed! request status code %s' % str(rst.status_code))
                    return

                for chunk in rst.iter_content(chunk_size=1024):
                    if chunk:
                        f.write(chunk)
                        filesize += len(chunk)
                    if filesize > self.max_size:
                        break
            self.dl_limit[index_type]['cur_size'] += filesize
            data['download_cnt'] = 3  # 已经下载成功，不再下载
            logging.info('[id: %s] download pcap [%s] successed...' % (es_id, filepath))
        except requests.exceptions.Timeout:
            logging.error("[id: %s] download pcap [%s] timeout!" % (es_id, filepath))
        except Exception as err:
            logging.error("[id: %s] download pcap [%s] error! %s" % (es_id, filepath, str(err)))
        finally:
            # 更新es数据库
            self.es.update(index=index, id=es_id, body={"doc": {"download_cnt": data.get('download_cnt', 0) + 1}})

    def get_current_es_index(self, index_type):
        now = datetime.now()
        ids = (now.day + 6) // 7
        if ids > 4:
            ids = 4  # 每个月最多4个索引
        return "%s-eve-%d%02d%02d" % (index_type, now.year, now.month, ids)

    def dl_limit_init(self, index_type):
        limit_info = self.dl_limit[index_type]
        limit_info['time_stamp'] = int(time.time())

    def dl_limit_match(self, index_type):
        """控制每个线程 每小时的下载量"""
        limit_info = self.dl_limit[index_type]
        cur_time = int(time.time())
        if cur_time - limit_info['time_stamp'] > 3600:
            limit_info['cur_size'] = 0
            limit_info['time_stamp'] = cur_time

        if limit_info['cur_size'] > limit_info['max_store_size']:
            return True
        else:
            return False

    def is_download_really(self, index_type, index, data, model_cnt):
        dl_really = True
        if index_type == "model":
            mcd = data['_source']['modelCode']
            if mcd in ["ndr_model_0003", "ndr_model_0008", "ndr_model_0015"]:
                model_cnt[mcd] += 1
                if model_cnt[mcd] > 2:
                    # 这几个模型容易频繁命中， 每批(15秒)最多只下载两个，剩下的只标记不真正去下载
                    self.es.update(index=index, id=data['_id'], body={"doc": {"download_cnt": 4}})
                    dl_really = False
        return dl_really

    def download_pcap(self, index_type):
        t_name = current_thread().getName()
        logging.info("%s pcap download task starting..." % t_name)
        self.dl_limit_init(index_type)
        es_template = copy.deepcopy(self.es_template)
        if index_type == "model":
            es_template["_source"]["includes"].append("modelCode")

        while True:
            if self.dl_limit_match(index_type):
                logging.info('download index [%s] match the files-size limit.' % index_type)
                time.sleep(self.wk_interval)
                continue  # 当前这个小时下载的量操过限制，不进行后面的下载了

            try:
                es_index = self.get_current_es_index(index_type)
                es_template["query"]["bool"]["must"][0] = {
                    "range": {"observedTime": {"lte": int(round(time.time() * 1000)) - (120 * 1000)}}
                }

                es_data = self.es.search(index=es_index, body=es_template)
                total = es_data["hits"]["total"]["value"]
                if 0 == total:
                    logging.info('download index [%s] completed, without pcap to download...' % es_index)
                    time.sleep(self.wk_interval)
                    continue
                logging.info('index [%s] have [%d] pcaps are waiting for download. deal-with hits %d' % (
                             es_index, total, len(es_data["hits"]["hits"])))
                init_ts = int(time.time())
                cut_ts = init_ts
                model_cnt = {"ndr_model_0003": 0, "ndr_model_0008": 0, "ndr_model_0015": 0}
                for data in es_data["hits"]["hits"]:
                    if self.is_download_really(index_type, es_index, data, model_cnt) is True:
                        self.download_pcap_file(data['_id'], data['_source'], es_index, index_type)
                    #else:
                    #    logging.info('NotReally download[%s] of model %s' % (data['_id'], data['_source']['modelCode']))
                    cut_ts = int(time.time())
                    if cut_ts - init_ts > 5:
                        break  # 为了防止下载pcap文件占用过多的系统资源，最多只查询下载处理5秒中。
                # 刷新一下，使更新写入磁盘可以被立即查询到
                self.es.indices.refresh(index=es_index)

                if cut_ts - init_ts < self.wk_interval:
                    sleep_time = self.wk_interval - (cut_ts - init_ts)
                else:
                    sleep_time = 5  # 至少停5秒
                time.sleep(sleep_time)
            except Exception as e:
                logging.error('%s catch error: %s' % (t_name, str(e)))
                time.sleep(self.wk_interval)
                continue

    def run(self):
        """
        下载数据包处理程序, 主进程负责下载当天的数据包，线程负责下载老的数据包.
        ES中每天的数据都有一个index，格式：'threat-log-20231212'.
        查询条件:
            1. 实时任务。
            2. 字段download_cnt不存在或者存在且不大于3。
        """
        logging.info("Main process download pcaps starting...")
        # 启动线程下载最近半月的数据包
        # 每个月有4个索引

        rule_thread = Thread(target=self.download_pcap, args=('rule',), name='rule_thread')
        model_thread = Thread(target=self.download_pcap, args=('model',), name='model_thread')
        ioc_thread = Thread(target=self.download_pcap, args=('ioc',), name='ioc_thread')

        rule_thread.start()
        model_thread.start()
        ioc_thread.start()

        rule_thread.join()
        model_thread.join()
        ioc_thread.join()


if __name__ == '__main__':
    requests.packages.urllib3.disable_warnings()
    logging.getLogger("elasticsearch").setLevel(logging.ERROR)  # 关闭es查询的详细信息
    logging.basicConfig(format='[%(asctime)s] -%(levelname)5s: %(message)s', level=logging.INFO)

    dl_pcap = DownloadPcap()
    if 'NOT_FSS' == dl_pcap.check_device_version():
        logging.error('Device is not fss version, So not support download pcap from stenographer!')
        exit(0)

    dl_pcap.run()
