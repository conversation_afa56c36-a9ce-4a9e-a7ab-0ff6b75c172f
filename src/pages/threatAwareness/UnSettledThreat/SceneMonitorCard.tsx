import React, { useEffect, useState } from 'react';
import { Button, Select, Spin, Tag } from 'antd';
import moment from 'moment';
import cs from 'classnames';

import { DashCard } from '../DashCard';
import ThreatGridTable from './ThreatGridTable';
import SceneListModal from '../Modal/SceneListModal';
import { TimeRange } from './HighRiskHostCard';
import { SECURITY_COLORS } from '../constant';
import SceneDetailModal from '../Modal/SceneDetailModal';

import { useConstantOptions, useOverviewSceneData } from '../hooks';

import style from './grid-table.less'

export type Props = {
  style?: React.CSSProperties;
  className?: string;
  timeRange: TimeRange
}

const readOptions = [
  { label: '全部', value: '1' },
  { label: '已读', value: '2' },
  { label: '未读', value: '0' },
]

function SceneMonitorCard({ timeRange }: Props): JSX.Element {
  const { serverirtyMap } = useConstantOptions()
  const [showSenceListModal, setShow] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const {
    namesLoading,
    scenes,
    total,
    page,
    loading,
    list,
    currentScene,
    setCurrentScene,
    readScene,
    updateTimeRange,
    updateQueryParams,
    getSceneName,
  } = useOverviewSceneData({
    name: '',
    is_read: 1,
    page: 1,
    pageSize: 2,
    ...timeRange
  });

  useEffect(() => {
    updateTimeRange(timeRange)
  }, [timeRange])

  const nameListOptions = scenes.map(({ name }) => ({ label: name, value: name }))

  const showModal = () => {
    setShow(true)
  }

  const closeModal = () => {
    setShow(false)
  }

  const titleRender = () => {
    return (
      <div className='flex justify-between items-center flex-1 pr-6'>
        <span>场景监测</span>
        <div>
          场景名称：
          {
            namesLoading ? <Spin /> : (
              <Select
                size='small'
                defaultValue={nameListOptions[0]?.value}
                style={{ width: 120 }}
                onChange={handleSceneNameChange}
                options={nameListOptions}
              />
            )
          }
        </div>
        <div>
          状态：
          <Select
            size='small'
            defaultValue='1'
            style={{ width: 120, marginRight: 5 }}
            onChange={handleStatusChange}
            options={readOptions}
          />
          <Button
            type="primary"
            size='small'
            onClick={showModal}
          >
            设置
          </Button>
        </div>
      </div>
    )
  }

  const handleStatusChange = (value: string) => {
    updateQueryParams({
      is_read: + value,
      page: 1,
    })
  }

  const handleSceneNameChange = (value: string) => {
    updateQueryParams({
      name: value,
      page: 1
    })
  }

  const itemRender = (record: any) => {
    const {
      uuid,
      src_ip,
      dst_ip,
      severity,
      src_ip_country,
      dst_ip_country,
      src_ip_city,
      dst_ip_city,
      attack_status,
      src_ip_flags,
      dst_ip_flags,
      category,
      timestamp,
      is_read
    } = record;

    const isAttacker = src_ip_flags.includes('attacker');

    const isVictim = dst_ip_flags.includes('victim');

    return (
      <div className={cs('flex flex-col', style.table_item)}>
        <div className='flex flex-col p-3 gap-2 flex-1'>
          <div className='flex'>
            <span className='mr-2'>源IP: {src_ip || 'unknown'}</span>
            <Tag >{(src_ip_country || src_ip_city) ? `${src_ip_country}/${src_ip_city}` : '未知归属地'} </Tag>
            <Tag color={isAttacker ? 'red' : 'orange'}>{isAttacker ? '攻击者' : '受害者'}</Tag>
            <Tag className='font-500' color={SECURITY_COLORS[attack_status]}>{serverirtyMap[attack_status]}</Tag>
          </div>
          <div className='flex'>
            <span className='mr-2'>目的IP: {dst_ip || 'unknown'}</span>
            <Tag >{(dst_ip_country || dst_ip_city) ? `${dst_ip_country}/${dst_ip_city}` : '未知归属地'}</Tag>
            <Tag color={isVictim ? 'orange' : 'red'}>{isVictim ? '受害者' : '攻击者'}</Tag>
            <Tag className='font-500' color={SECURITY_COLORS[severity]}>{serverirtyMap[severity]}</Tag>
          </div>
          <div className='flex flex-wrap justify-between'>
            {category ? <Tag className='font-500' color="#fd9831">{category}</Tag> : <span></span>} 
            <span>{moment(timestamp).format('YYYY-MM-DD HH:mm:ss')}</span>
          </div>
        </div>
        <div className={cs('flex justify-evenly items-center', style.item_option)}>
          <Button onClick={() => handleDetail(record)} className='!c-blue' type='text' >详情</Button>
          <Button disabled={is_read} onClick={() => handleRead(uuid)} className='!c-blue' type='text' >{is_read ? '已' : '未'}读</Button>
        </div>
      </div>
    )
  }

  const handlePageChange = (page: number) => {
    updateQueryParams({
      page,
    })
  }

  const handleRead = (uuid: string) => {
    readScene({
      start_time: timeRange.start_time.toString(),
      end_time: timeRange.end_time.toString(),
      uuid,
      value: true,
      action: 'read',
      type: 'event',
      terms: 'event',
    })
  }

  const handleDetail = (record: Record<string, any>) => {
    const { _source } = record;
    setCurrentScene(() => {
      setShowDetailModal(true);
      return { _source }
    })
  }

  return (
    <DashCard title={titleRender()}>
      <ThreatGridTable
        loading={loading}
        total={total}
        page={page}
        className='flex-1'
        oneColumn
        data={list}
        isTotalData={false}
        itemRender={itemRender}
        onPageChange={handlePageChange}
      />
      {
        showSenceListModal && (
          <SceneListModal
            show={showSenceListModal}
            onClose={closeModal}
            onCreate={getSceneName}
          />
        )
      }
      {
        showDetailModal && (
          <SceneDetailModal
            currentScene={currentScene}
            dateRange={timeRange}
            show={showDetailModal}
            onClose={() => setShowDetailModal(false)}
          />)
      } 
    </DashCard>
  );
}

export default SceneMonitorCard;
