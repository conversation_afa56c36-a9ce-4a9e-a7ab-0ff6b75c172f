# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : init_scheduler.py
# @Software: PyCharm

"""异常检测调度器初始化"""

import atexit
from utils.logger import get_ndr_logger
from config.config import NdrLog
from .scheduler import start_scheduler, stop_scheduler

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def init_anomaly_detection_scheduler():
    """初始化异常检测调度器"""
    try:
        # 启动调度器
        start_scheduler()
        
        # 注册应用退出时停止调度器
        atexit.register(stop_scheduler)
        
        LOG.info("异常检测调度器初始化完成")
        
    except Exception as e:
        LOG.error(f"异常检测调度器初始化失败: {str(e)}")


# 在模块导入时自动初始化
if __name__ != '__main__':
    init_anomaly_detection_scheduler()
