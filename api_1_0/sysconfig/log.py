
# -*- coding: utf-8 -*-
# @Time    : 2020-05-12 11:10
# <AUTHOR> wu
# @File    : log.py
# @Software: PyCharm

"""module log"""

import os
import time
import datetime
import re
import json
import subprocess
import zipfile
from shutil import copyfile
from flask import request
from flask import send_from_directory
from flask import Response
from flask_restful import Resource
from config.config import NdrLog
from utils.utils import flask_response
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils.json_format import JSONEncoder
from utils.param_check import Validator, check_flask_args
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger('api_log', __file__)


class WebLog(Resource):
    """write log to mongodb """
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @check_flask_args(Validator("log_get_all_schema"), request)
    def get(self, **kwargs):
        """
        :return:
        """
        log_type = kwargs["type"]
        log_query = kwargs["q"]
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])

        condition = {"$and": []}
        if log_query != "":
            condition["$and"].append({'$or': [{"event": {'$regex': log_query, '$options': 'i'}},
                                              {"user": {'$regex': log_query, '$options': 'i'}}]})
        if log_type != "":
            condition["$and"].append({"type": log_type})
        if log_type == "" and log_query == "":
            condition = {}
        log_count = self.mongodb.count('log', condition)
        data = {"count": log_count}
        rst = self.mongodb.find('log', condition, {"_id": 0,
                                                   "user": 1,
                                                   "type": 1,
                                                   "user_ip": 1,
                                                   "event": 1,
                                                   "writeTime": 1
                                                   }) \
            .sort([("writeTime", -1)]).limit(page_size).skip((page - 1) * page_size)
        if rst is None:
            return flask_response('Not found.', False, {})
        cols = []
        for col in rst:
            col = JSONEncoder().encode(col)
            cols.append(json.loads(col))
        data["detail"] = cols
        data["page"] = page
        data["pageSize"] = page_size
        return flask_response("", True, data)


class ZipObj():
    """zip process"""
    def __init__(self, file_name, password):
        self.file_name = file_name.rstrip('/')
        self.password = password

    def encryption(self, del_source=False):
        """
        :param del_source:
        :return:
        """

        cmd = ['zip', '-rP', self.password, self.file_name + ".zip", self.file_name]
        child = subprocess.Popen(cmd)
        child.wait()
        if del_source:
            child = subprocess.Popen(['rm', '-rf', self.file_name, self.file_name + '.zip'])
            child.wait()

    def decrypt(self):
        """
        :return:
        """
        zip_file = zipfile.ZipFile(self.file_name + ".zip")
        zip_file.extractall(r"zipdata", pwd=self.password.encode('utf-8'))


class LogFileDownLoad(Resource):
    """ download log file """

    @check_flask_args(Validator("log_download_schema"), request)
    def get(self, **kwargs):
        """
        :return:
        """
        log_type = kwargs["type"]
        start_date = int(kwargs["startDate"]) if kwargs["startDate"] else None
        end_date = int(kwargs["endDate"]) if kwargs["endDate"] else int(time.time() * 1000)
        if log_type == "":
            re_str = '.'
        else:
            re_str = log_type + '\.'
        if not start_date:
            start_temp = datetime.date.today() - datetime.timedelta(days=7)
            start_date = int(time.mktime(time.strptime(str(start_temp), '%Y-%m-%d'))) * 1000
        if not os.path.exists(NdrLog.DownloadPath):
            os.mkdir(NdrLog.DownloadPath)
        else:
            os.system("rm -rf %s* %sdownload_log.zip" % (NdrLog.DownloadPath, NdrLog.ZipPath))
        for root, _, files in os.walk(NdrLog.OriginalPath):
            for log_file in files:
                try:
                    if re.match(r'%slog$' % re_str, log_file):
                        copyfile(os.path.join(root, log_file), NdrLog.DownloadPath + log_file)
                        continue
                    if re.findall(log_type, log_file):
                        date = int(time.mktime(time.strptime((log_file.split('.'))[2], "%Y-%m-%d"))) * 1000
                        if start_date <= date <= end_date:
                            copyfile(os.path.join(root, log_file), NdrLog.DownloadPath + log_file)
                except Exception as error:
                    ndr_log_to_box(NdrLog.Type.OPERATE, '日志文件下载失败：%s' % str(error))
        # if log_type == "": 其他组件日志处理
        #     copyfile(os.path.join(root, log_file), NdrLog.DownloadPath + log_file)
        if os.listdir(NdrLog.DownloadPath):
            ZipObj(NdrLog.DownloadPath, NdrLog.ZipPwd).encryption()
            return send_from_directory(NdrLog.ZipPath, 'download_log.zip')
        return Response("The log file does not exist.", 205)
        # return flask_response("The log file does not exist.", False, {})
