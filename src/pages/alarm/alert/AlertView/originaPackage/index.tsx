import React, { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import { get } from 'lodash';
import s from './style.less';
import DataPack from './dataPack';
import DataContent from './dataContent';
import { useDataList } from './hook';

const OriginaPackage = ({ selRow, filePath }: any) => {
  const { pcapByeFun } = useDataList();
  const pcapPath = filePath || get(selRow, '_source.pcap_warning', '').replace(/\.pcap$/, '');
  const [params, setParams] = useState({});
  const onChange = (e: any) => {
    setParams(e);
  };
  useEffect(() => {
    return () => {
      pcapByeFun({ pcap_name: pcapPath });
    };
  }, []);
  return (
    <div className={s.originaPackage}>
      <Tabs size="small">
        <Tabs.TabPane tab="数据包" key="1">
          <DataPack pcapPath={pcapPath} onChange={onChange} filePath={filePath}/>
        </Tabs.TabPane>
        <Tabs.TabPane tab="内容" key="2">
          <DataContent params={params} />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};
export default OriginaPackage;
