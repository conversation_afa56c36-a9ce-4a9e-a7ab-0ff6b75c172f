/*
 * @Author: tianh
 * @Date: 2022-07-05 15:42:52
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-05 15:47:44
 * @Descripttion:
 */
import Reconnaissance from '@/assets/images/killchains1.svg';
import Weaponization from '@/assets/images/killchains2.svg';
import Delivery from '@/assets/images/killchains3.svg';
import Exploitation from '@/assets/images/killchains4.svg';
import Installation from '@/assets/images/killchains5.svg';
import CommandandControl from '@/assets/images/killchains6.svg';
import ActionsonObjective from '@/assets/images/killchains7.svg';
import { getAttacks } from '@/services/collect';
import { attacksReplayType, threatFlagNameMap, timeTypeColect } from '@/utils/enumList';
import { getQueryVariable, handleEmpty } from '@/utils/utils';
import { SearchOutlined } from '@ant-design/icons';
import { But<PERSON>, Collapse, DatePicker, Form, Input, Select } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
const echarts = require('echarts');
const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;
import './style.less';
const AttacksAnalysis = () => {
  const [form] = Form.useForm();
  const [params, setparams] = useState({ timeType: 'observedTime' });
  const [isShow, setIsShow] = useState(true);
  const [chartsData, setchartsData] = useState<any>({});

  useEffect(() => {
    if (JSON.stringify(chartsData) !== '{}') {
      initChart();
    }
  }, [chartsData]);

  useEffect(() => {
    if (getQueryVariable('startTime') && getQueryVariable('stopTime') && getQueryVariable('ip')) {
      let time = [moment(parseInt(getQueryVariable('startTime'))), moment(parseInt(getQueryVariable('stopTime')))];

      setparams({ time, ip: getQueryVariable('ip') });
      form.setFieldsValue({
        time,
        ip: getQueryVariable('ip'),
        replay: getQueryVariable('replay') === 'replay' ? true : false,
      });
      onSubmit();
    }
  }, []);
  const onSubmit = () => {
    setIsShow(false);
    form.validateFields().then((value: any) => {
      if (value.time) {
        value.startTime = (moment(value.time[0]).startOf('seconds').unix() * 1000).toString();
        value.stopTime = (moment(value.time[1]).startOf('seconds').unix() * 1000).toString();
        delete value.time;
        value.timeType = 'observedTime';
        getAttacks(value).then((res) => {
          if (res.flag) {
            setIsShow(true);
            setchartsData(res.data);
          }
        });
      }
    });
  };
  const initChart = () => {
    const myChart = echarts.init(document.getElementById('main'));

    const scatter_data = chartsData.scatter.map((item: any) => {
      return [item.xAxis, `${item.yAxis}`, item];
    });
    const line_data = chartsData.line.map((item: any) => {
      return [item.xAxis, `${item.yAxis}`, item];
    });

    const option = {
      tooltip: {
        trigger: 'item',
        // trigger: 'xAxis', //若需要使用默认的『显示』『隐藏』触发规则，则可以去掉trigger的配置
        // axisPointer: { type: 'cross', show: true },
        // formatter: '{a}{b}{c}', //默认触发规则中散点展示的内容，{a}标题;{b}X坐标;{c}Y坐标
        formatter: (value: any) => {
          let data = value.data[2];

          return `
            <div>
              <div style='border-bottom:2px solid #fff;padding:4px 0;'>
                <span style='word-wrap:break-word;white-space:normal;'>
                  ${data.modelName ? `模型名称：${data.modelName}` : ''}
                  ${data.ioc ? `ioc：${data.ioc}` : ''}
                  ${data.vulName ? `告警名称：${data.vulName} ` : ''}
                  <span style='display:inine-block;font-weight:blod;margin-left:10px;
                    background: ${data.threatLevel === 'High' ? '#ff6666' : ''}
                    ${data.threatLevel === 'Low' ? '#66ff33' : ''}
                    ${data.threatLevel === 'Medium' ? '#ffbb33' : ''};
                    border-radius:4px;padding:0 6px;'
                  >
                    ${data.threatLevel === 'High' ? '高危' : ''}
                    ${data.threatLevel === 'Low' ? '低危' : ''}
                    ${data.threatLevel === 'Medium' ? '中危' : ''}
                  </span>
                </span>
              </div>
              <div style='padding-top:8px'>
                <p>发现时间：${moment(data.observedTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                <p>源ip：${handleEmpty(data.flow.src_ip)}</p>
                <p>目的ip：${handleEmpty(data.flow.dst_ip)}</p>
                <p>源ip国家：${handleEmpty(data.srcIpGeoInfo.country_name)}</p>
                <p style='word-wrap:break-word;white-space:normal;'>
                  威胁分类：${
                    data.threat_type ? data.threat_type : data.threatFlag ? threatFlagNameMap[data.threatFlag] : '-'
                  }
                </p>
              </div>
            </div>
            `;
        },
        extraCssText: 'width:250px;',
        backgroundColor: '#e9edf0',
        axisPointer: {
          label: {},
        },
      },
      xAxis: [
        {
          position: 'top',
          type: 'category',
          data: chartsData.xAxis,
          // data: ['Sunny', '威胁'],
          axisPointer: {
            type: 'shadow',
          },
          axisLine: {
            show: true, // 是否显示坐标轴轴线
            symbol: ['none', 'arrow'], // 轴线两端箭头，两个值，none表示没有箭头，arrow表示有箭头
            symbolSize: [10, 15], // 轴线两端箭头大小，数值一表示宽度，数值二表示高度
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
            },
            formatter: function (value: any) {
              switch (value) {
                case '侦查跟踪':
                  return '{Reconnaissance|}' + value;
                  break;
                case '武器构建':
                  return '{Weaponization|}' + value;
                  break;
                case '载荷投递':
                  return '{Delivery|}' + value;
                  break;
                case '漏洞利用':
                  return '{Exploitation|}' + value;
                  break;
                case '安装植入':
                  return '{Installation|}' + value;
                  break;
                case '命令控制':
                  return '{CommandandControl|}' + value;
                  break;
                case '目标达成':
                  return '{ActionsonObjective|}' + value;
                  break;
              }
            },
            rich: {
              Reconnaissance: {
                height: 20,
                // align: 'center',
                backgroundColor: {
                  image: Reconnaissance,
                },
              },
              Weaponization: {
                height: 20,
                // align: 'center',
                backgroundColor: {
                  image: Weaponization,
                },
              },
              Delivery: {
                height: 20,
                // align: 'center',
                backgroundColor: {
                  image: Delivery,
                },
              },
              Exploitation: {
                height: 20,
                // align: 'center',
                backgroundColor: {
                  image: Exploitation,
                },
              },
              Installation: {
                height: 20,
                // align: 'center',
                backgroundColor: {
                  image: Installation,
                },
              },
              CommandandControl: {
                height: 20,
                // align: 'center',
                backgroundColor: {
                  image: CommandandControl,
                },
              },
              ActionsonObjective: {
                height: 20,
                // align: 'center',
                backgroundColor: {
                  image: ActionsonObjective,
                },
              },
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'category',
          data: chartsData.yAxis,
          axisLabel: {
            formatter: (value: any) => {
              value = parseInt(value);
              return moment(value).format('YYYY-MM-DD HH:mm:ss');
            },
          },
        },
      ],
      grid: {
        //与绝对定位相似，top，left，right，bottom 设定是根据上级盒子宽高来计算
        top: '5%',
        left: '10%',
        right: '5%',
        bottom: '5%',
      },
      series: [
        {
          // name: '随机点2',
          type: 'scatter',
          data: scatter_data,
          Symbol: 'triangle',
          // symbol: 'image://' + './logo.png',
          symbolSize: 16,
          lineStyle: {
            color: '#5470C6',
            width: 4,
            type: 'dashed',
          },
          itemStyle: {
            color: function (params: any) {
              if (params.value[2].spotType === 'line') {
                return '#ff3333';
              } else {
                return '#4d94ff';
              }
            },
          },
        },
        {
          // name: '温度',
          type: 'line',
          data: line_data,
          color: 'red',
        },
      ],
    };
    window.onresize = myChart.resize;
    myChart.setOption(option);
  };

  let box_height = chartsData.yAxis && chartsData.yAxis.length ? chartsData.yAxis.length * 35 : 400;

  return (
    <div>
      <Collapse
        defaultActiveKey={['1']}
        style={{
          minWidth: '800px',
        }}
      >
        <Panel header="搜索条件" key="1">
          <Form form={form} className="!flex gap-3" onFinish={onSubmit} initialValues={params}>
            <Form.Item
              label="时间范围"
              name="time"
              rules={[
                {
                  required: true,
                  message: '请填写!',
                },
              ]}
            >
              <DatePicker.RangePicker
                className="w-100%"
                ranges={{
                  近一年: [moment().add(-1, 'year'), moment()],
                  近半年: [moment().add(-6, 'month'), moment()],
                  近一月: [moment().add(-1, 'month'), moment()],
                  近一周: [moment().add(-7, 'd'), moment()],
                  近一天: [moment().add(-1, 'd'), moment()],
                  今天: [moment().startOf('day'), moment().endOf('day')],
                  本周: [moment().startOf('week'), moment().endOf('week')],
                  本月: [moment().startOf('month'), moment().endOf('month')],
                  本年度: [moment().startOf('year'), moment().endOf('year')],
                }}
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
            <Form.Item
              label="IP地址"
              name="ip"
              rules={[
                {
                  required: true,
                  message: '请填写!',
                },
              ]}
            >
              <Input placeholder="请输入IP地址" allowClear />
            </Form.Item>
            <Form.Item
              label="是否回放"
              name="replay"
              rules={[
                {
                  required: true,
                  message: '请填写!',
                },
              ]}
            >
              <Select placeholder="请选择是否回放" allowClear>
                {attacksReplayType.map((item: any) => {
                  return (
                    <Option value={item.value} key={item.key}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item>
              <div className="group_btns">
                <Button className="searchBtn" type="primary" onClick={onSubmit} icon={<SearchOutlined />}>
                  搜索
                </Button>
              </div>
            </Form.Item>
          </Form>
        </Panel>
      </Collapse>
      {isShow && (
        <div
          className="mt-4"
          style={{
            width: '100%',
            height: `${box_height}px`,
            minHeight: '600px',
            minWidth: '800px',
            background: '#fff',
            paddingTop: '20px',
          }}
          id="main"
        ></div>
      )}
    </div>
  );
};
export default AttacksAnalysis;
