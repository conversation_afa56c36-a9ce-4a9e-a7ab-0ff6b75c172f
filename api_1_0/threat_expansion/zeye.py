#!/usr/bin/env python
# -*- coding: utf-8 -*-

import zoomeye.sdk as zoomeye


class ZoomEye:
    def __init__(self, api_key=""):
        self.zm = zoomeye.ZoomEye()
        self.zm.api_key = api_key

    def set_api_key(self, api_key):
        self.zm.api_key = api_key

    def search(self, query):
        zm = self.zm
        zm.multi_page_search(query, page=1)
        data_count = zm.show_count()
        page_count = int(data_count/20) + 1

        if data_count == 0:
            return []

        # 超过40条，就只打印前40条
        if page_count > 2:
            page_count = 2

        all_zoomeye_data = zm.multi_page_search(query, page=page_count)
        results = []
        for data in all_zoomeye_data:
            data["timestamp"] = data.get("timestamp", "")
            data["ip"] = data["ip"]
            data["port"] = data.get("portinfo", {}).get("port", "")
            data["country"] = data.get("geoinfo", {}).get("country", {}).get("names", {}).get("zh-CN", "")
            data["province"] = data.get("geoinfo", {}).get("subdivisions", {}).get("names", {}).get("zh-CN", "")
            data["city"] = data.get("geoinfo", {}).get("city", {}).get("names", {}).get("zh-CN", "")
            results.append(data)
        return results


zoomeye_singleton = ZoomEye()


if __name__ == '__main__':
    zoomeye_singleton.set_api_key("e65a0dBD-A92c-76620-7D52-7F02AAF6002")
    result = zoomeye_singleton.search('"ckId" +country:"KR"')
    for r in result:
        print(str(r))
