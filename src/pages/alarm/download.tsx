import { agreementsType, downloadtaskType } from '@/utils/enumList';
import { parseQueryString } from '@/utils/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { Button, Col, Collapse, DatePicker, Form, Input, Row, Select, message } from 'antd';
import moment from 'moment';
import React from 'react';
import './style.less';

const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;

const Download = (props: any) => {
  const [form] = Form.useForm();
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      if (value.time[1].valueOf() - value.time[0].valueOf() > 86400000) {
        message.error('时间范围为24小时');
        return;
      }
      value.startTime = value.time[0].valueOf();
      value.stopTime = value.time[1].valueOf();
      delete value.time;
      window.location.href = `${window.location.protocol}//${window.location.host}/mica-api/api/v1/package${parseQueryString(
        value,
      )}`;
    });
  };
  const formItemLayout = {
    labelCol: {
      span: 9, // * ≥576px
    },
    wrapperCol: {},
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form {...formItemLayout} form={form} className="searchForm" onFinish={onSubmit} layout="inline">
            <Row>
              <Col span={7}>
                <Form.Item
                  labelCol={{ span: 4 }}
                  wrapperCol={{ span: 18 }}
                  label="时间"
                  name="time"
                  rules={[
                    {
                      required: true,
                      message: '请选择时间范围',
                    },
                  ]}
                >
                  <DatePicker.RangePicker
                    allowClear
                    className="w-100%"
                    ranges={{
                      最近1分钟: [moment(new Date()).add(-1, 'minute'), moment()],
                      最近5分钟: [moment(new Date()).add(-5, 'minute'), moment()],
                      最近30分钟: [moment(new Date()).add(-30, 'minute'), moment()],
                      最近1小时: [moment(new Date()).add(-1, 'hour'), moment()],
                      最近4小时: [moment(new Date()).add(-4, 'hour'), moment()],
                      最近12小时: [moment(new Date()).add(-12, 'hour'), moment()],
                      最近1天: [moment(new Date()).add(-1, 'day'), moment()],
                    }}
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="任务类型"
                  name="replay"
                  rules={[
                    {
                      required: true,
                      message: '请选择任务类型!',
                    },
                  ]}
                >
                  <Select placeholder="请选择任务类型" allowClear>
                    {downloadtaskType.map((item: any) => {
                      return (
                        <Option key={item.value} value={item.value}>
                          {item.key}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="IP1"
                  name="src_ip"
                  rules={[
                    {
                      required: true,
                      message: '请输入IP1!',
                    },
                  ]}
                >
                  <Input placeholder="请输IP1" allowClear />
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item label="IP2" name="dst_ip">
                  <Input style={{ maxWidth: 170 }} placeholder="请输入IP2" allowClear />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={7}>
                <Form.Item labelCol={{ span: 4 }} wrapperCol={{ span: 18 }} label="源端口" name="src_port">
                  <Input placeholder="请输入源端口" allowClear />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="目的端口" name="dst_port">
                  <Input placeholder="请输入目的端口" allowClear />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="协议" name="proto">
                  <Select placeholder="请选择协议" allowClear>
                    {agreementsType.map((item: any) => {
                      return (
                        <Option key={item.value} value={item.value}>
                          {item.key}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={5}>
                <div className="text-right" style={{ marginRight: '30px' }}>
                  <Button type="primary" htmlType="submit" icon={<DownloadOutlined />}>
                    下载
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
    </div>
  );
};

export default Download;
