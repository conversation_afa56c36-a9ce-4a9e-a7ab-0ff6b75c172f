#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_eventName.py
# @time: 2019/08/21
# @software: PyCharm
from api_1_0.knowledge.get_killchains import GetKillchains


def get_kill_chains_cn(kill_chains):
    """
    :param kill_chains:
    :return:
    """
    return GetKillchains.killchain_map.get(kill_chains, "")


class EventNameList(object):
    def a1_name(self, killchains_start, killchains_stop, attack_ip):
        event_name = "杀伤链阶段：从%s到%s的主机：%s" % (get_kill_chains_cn(killchains_start),
                                             get_kill_chains_cn(killchains_stop), attack_ip)
        if killchains_stop in list(GetKillchains.killchain_map.keys())[3:]:
            event_type = "恶意数据流"
        else:
            event_type = "恶意通信"
        return event_name, event_type

    def a2_name(self, killchains, attack_ip):
        killchain_desc = [
            "主机上的可疑侦查活动：%s" % attack_ip,
            "主机上的制作武器活动：%s" % attack_ip,
            "主机上的可疑载荷投递活动：%s" % attack_ip,
            "主机上的关键漏洞利用活动：%s" % attack_ip,
            "主机上可疑的安装植入活动：%s" % attack_ip,
            "主机上的致命 C2 通信活动：%s" % attack_ip,
            "主机上可疑的目标达成活动：%s" % attack_ip
        ]
        # 组合成字典
        switcher = dict(zip(list(GetKillchains.killchain_map.keys()), killchain_desc))
        event_name = switcher.get(killchains, '杀伤链阶段错误')
        event_type = "恶意通信"
        return event_name, event_type

    def a3_name(self, killchains, attack_ip):
        event_name = "目标主机：%s，重复进行%s尝试" % (attack_ip, get_kill_chains_cn(killchains))
        event_type = "受攻击主机"
        return event_name, event_type

    def a4_name(self, attack_ip):
        event_name = "主机上的关键杀伤链升级活动：%s" % attack_ip
        event_type = "杀伤链升级"
        return event_name, event_type

    def a5_name(self, attack_ip):
        event_name = "连接到可疑域的域名或IP：%s" % attack_ip
        event_type = "恶意通信"
        return event_name, event_type

    # def a1_name(self, killchains_start, killchains_stop, attack_ip):
    #     event_name = "Kill Chain Progression: {0} to {1} on Host: {2}".format(killchains_start, killchains_stop,
    #                                                                           attack_ip)
    #     if killchains_stop in ["Exploitation", "Beacon", "CnC", "Actions on Objective"]:
    #         event_type = "Malicious Flow"
    #     else:
    #         event_type = "Malicious Conversation"
    #     return event_name, event_type
    #
    # def a2_name(self, killchains, attack_ip):
    #     switcher = {
    #         "Recon": "Suspicious Recon Activity on Hosts: %s" % attack_ip,
    #         "Weaponization": "Evil Weaponization Activity on Hosts: %s" % attack_ip,
    #         "Delivery": "Suspicious Delivery Activity on Hosts: %s" % attack_ip,
    #         "Exploitation": "Critical Exploit Activity on Hosts: %s" % attack_ip,
    #         "Beacon": "Probable Beaconing Activity on Hosts: %s" % attack_ip,
    #         "CnC": "Deadly CnC Activity on Host: Hosts: %s" % attack_ip,
    #         "Actions on Objective": "Probable Actions on Objective Activity on Hosts: %s" % attack_ip,
    #     }
    #     event_name = switcher.get(killchains, 'wrong value')
    #     event_type = "Malicious Conversation"
    #     return event_name, event_type
    #
    # def a3_name(self, killchains, attack_ip):
    #     event_name = "Targeted Host: {0}, Repeat {1} Attempts".format(attack_ip, killchains)
    #     event_type = "Compromised Host"
    #     return event_name, event_type
    #
    # def a4_name(self, attack_ip):
    #     event_name = "Critical Killchains Escalation Activity on Hosts: %s" % attack_ip
    #     event_type = "Killchains Escalation"
    #     return event_name, event_type
    #
    # def a5_name(self, attack_ip):
    #     event_name = "Connection to Suspicious domain or ip by Host: %s" % attack_ip
    #     event_type = "Malicious Conversation"
    #     return event_name, event_type


def get_event_properties(killchains_list, triggerCount, src_ip, ioc):
    event_list = EventNameList()
    if ioc == 1:
        event_name, event_type = event_list.a5_name(src_ip)
        return event_name, event_type
    elif len(killchains_list) == 0:
        event_name, event_type = event_list.a4_name(src_ip)
    elif len(killchains_list) == 1:
        if triggerCount < 3:
            event_name, event_type = event_list.a2_name(killchains_list[0], src_ip)
        else:
            event_name, event_type = event_list.a3_name(killchains_list[0], src_ip)
    else:
        event_name, event_type = event_list.a1_name(killchains_list[0], killchains_list[-1], src_ip)

    return event_name, event_type
