import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Collapse,
  Col,
  Row,
  Button,
  Select,
  DatePicker,
  message,
  Card,
  Tooltip,
} from 'antd';
import moment from 'moment';
import classnames from 'classnames';
import _ from 'lodash';
import { SearchOutlined } from "@ant-design/icons";
import { ellipsis } from '@/utils/utils';
import { timeType } from '@/utils/enumList';
import {
  eventDetail,
  getThreatenType,
  getFlow,
  getDanger,
  killchainsFlow,
  hotEventList,
  getKillChainsList,
} from '@/services/report';
import {
  threatFlagImg,
  threatFlagNameMap,
  threatLevelColorMap,
  killChainMap,
} from '@/utils/enumList';
import ReportFrom from './reportForm';
import style from './index.less';
const echarts = require('echarts');

const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;
// 图表定位
const chartGridTop = 20;
const chartGridHeight = 40;

const Index = (props: any) => {
  const [form] = Form.useForm();
  const childForm = useRef<any>(null);
  const [formValue, setformValue] = useState({
    time: [
      moment()
        .subtract(7, 'days')
        .startOf('day'),
      moment().startOf('day'),
    ],
    timeMode: 'observed',
  });
  const [eventData, seteventData] = useState<any>({
    totalCount: null,
    threatLevels: {
      High: null,
      Low: null,
      Medium: null,
    },
  });
  const [threatenTypesData, setthreatenTypesData] = useState({
    threatFlags: {},
  });
  const [allFlow, setallFlow] = useState<any>({});
  const [dangerous, setdangerous] = useState({
    slice_flow: {},
    total: {},
  });
  const [threatenLvObj, setthreatenLvObj] = useState({ total: {} });
  const [threatFlow, setthreatFlow] = useState({
    time: [],
    Medium: [],
    Low: [],
    High: [],
  });
  const [appFlow, setappFlow] = useState({
    keys: [],
    time: [],
  });
  const [hotEvent, sethotEvent] = useState([]);
  const [totalKillChains, settotalKillChains] = useState({});
  const [killChainsTotal, setkillChainsTotal] = useState(0);
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then(value => {
      const formvalue = {
        ...formValue,
        ...value,
        timeMode: 'observed',
      };
      echarts.init(document.getElementById('threatenChart')).dispose();
      echarts.init(document.getElementById('dangerousChart')).dispose();
      echarts.init(document.getElementById('appFlow')).dispose();
      setformValue(formvalue);
    });
  };
  const getEvent = () => {
    let _params = {
      startTime: formValue.time[0].valueOf(),
      stopTime: formValue.time[1].valueOf(),
      timeMode: formValue.timeMode,
    };
    eventDetail(_params).then(res => {
      if (res.flag) {
        seteventData(res.data);
      } else {
        message.error(res.message);
      }
    });
  };
  const ThreatenType = () => {
    let _params = {
      startTime: formValue.time[0].valueOf(),
      stopTime: formValue.time[1].valueOf(),
      timeMode: formValue.timeMode,
    };
    getThreatenType(_params).then(res => {
      if (res.flag) {
        setthreatenTypesData(res.data);
      } else {
        message.error(res.message);
      }
    });
  };
  const getFlowDetail = () => {
    let _params = {
      startTime: formValue.time[0].valueOf(),
      stopTime: formValue.time[1].valueOf(),
      timeMode: formValue.timeMode,
    };
    getFlow({
      ..._params,
    }).then(res => {
      if (res.flag) {
        let threatFlow = {
          time: [],
          High: [],
          Low: [],
          Medium: [],
        };
        threatFlow.time = res.data.slice_flow.map((item: { time: null }) => {
          return moment(item.time).format('YYYY-MM-DD HH:mm:ss');
        });
        threatFlow.High = res.data.slice_flow.map(
          (item: {
            threatFlow: {
              High: any;
            };
          }) => {
            return item.threatFlow.High;
          },
        );
        threatFlow.Low = res.data.slice_flow.map(
          (item: {
            threatFlow: {
              Low: any;
            };
          }) => {
            return item.threatFlow.Low;
          },
        );
        threatFlow.Medium = res.data.slice_flow.map(
          (item: {
            threatFlow: {
              Medium: any;
            };
          }) => {
            return item.threatFlow.Medium;
          },
        );
        setthreatFlow(threatFlow);
        setthreatenLvObj(res.data);
      } else {
        message.error(res.message);
      }
    });
  };
  const getKillChains = () => {
    let _params = {
      startTime: formValue.time[0].valueOf(),
      stopTime: formValue.time[1].valueOf(),
      timeMode: formValue.timeMode,
    };
    getKillChainsList(_params).then(res => {
      if (res.flag) {
        settotalKillChains(res.data.Killchains);
        setkillChainsTotal(res.data.totalCount);
      }
    });
  };
  const getDangerousFlow = () => {
    let _params = {
      startTime: formValue.time[0].valueOf(),
      stopTime: formValue.time[1].valueOf(),
      timeMode: formValue.timeMode,
    };
    getDanger({
      ..._params,
    }).then(res => {
      if (res.flag) {
        setdangerous(res.data);
        let _data = {
          keys: [],
          time: [],
          // TCP: [],
        };
        _data.keys = Object.keys(res.data.slice_flow[0].appFlow);

        res.data.slice_flow.forEach((item: any) => {
          _data.keys.forEach((ele: any) => {
            if (!_data[ele]) {
              _data[ele] = [];
            }
            _data[ele].push(item.appFlow[ele]);
          });
        });
        _data.time = res.data.slice_flow.map(
          (item: { time: moment.MomentInput }) => {
            return moment(item.time).format('YYYY-MM-DD HH:mm:ss');
          },
        );
        setappFlow(_data);
      } else {
        message.error(res.message);
      }
    });
  };
  const initThreatenChart = () => {
    const myChart = echarts.init(document.getElementById('threatenChart'));
    const option = {
      // title: {
      //   x: 'left',
      //   text: '威胁等级',
      // },
      color: ['#3398DB'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter(params: string | any[]) {
          // 数据单位格式化
          let relVal = params[0].name; // x轴名称
          relVal += "<div style='width:120px'>";
          for (let i = 0, l = params.length; i < l; i++) {
            // 数据为0时设置了null,会导致tooltip不显示，重置为0就会显示
            if (params[i].value === null) {
              params[i].value = 0;
            }
            if (params[i].value || params[i].value === 0) {
              relVal += `<span  style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params[i].color};'><span  style="display:block;padding-left:15px;margin-top:-4px">${params[i].seriesName} : ${params[i].data.showSize}</span></span><br>`;
            }
          }
          relVal += '</div>';
          return relVal;
        },
      },
      legend: {
        data: ['低', '中', '高'],
        align: 'right',
        right: 20,
      },
      xAxis: {
        type: 'category',
        data: threatFlow.time || [],
        axisLine: {
          lineStyle: {
            color: '#c5cace',
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        min: 0,
        splitLine: { show: false },
        axisLabel: {
          formatter: '{value} K',
        },
        axisLine: {
          lineStyle: {
            color: '#c5cace',
          },
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: '低',
          type: 'bar',
          // barWidth: '40%',
          barCategoryGap: '70%',
          barMaxWidth: 60,
          barMinHeight: 2,
          stack: 'a',
          itemStyle: {
            color: '#f0ce52',
          },
          // data:threatFlow.Low
          data: threatFlow.Low
            ? threatFlow.Low.map(
              (v: { actualSize: number; showSize: any }) => ({
                value: v.actualSize === 0 ? null : v.actualSize,
                showSize: v.showSize,
              }),
            )
            : [],
        },
        {
          name: '中',
          type: 'bar',
          // barWidth: '40%',
          barCategoryGap: '70%',
          barMaxWidth: 60,
          barMinHeight: 2,
          stack: 'a',
          itemStyle: {
            color: '#f56e00',
          },
          // data:threatFlow.Medium
          data: threatFlow.Medium
            ? threatFlow.Medium.map(
              (v: { actualSize: number; showSize: any }) => ({
                value: v.actualSize === 0 ? null : v.actualSize,
                showSize: v.showSize,
              }),
            )
            : [],
        },
        {
          name: '高',
          type: 'bar',
          // barWidth: '40%',
          barCategoryGap: '70%',
          barMaxWidth: 60,
          barMinHeight: 2,
          stack: 'a',
          itemStyle: {
            color: '#a22508',
          },
          // data:threatFlow.High
          data: threatFlow.High
            ? threatFlow.High.map(
              (v: { actualSize: number; showSize: any }) => ({
                value: v.actualSize === 0 ? null : v.actualSize,
                showSize: v.showSize,
              }),
            )
            : [],
        },
      ],
    };
    myChart.setOption(option);
  };
  const initdangerousChart = () => {
    const myChart = echarts.init(document.getElementById('dangerousChart'));
    const option = {
      // title: {
      //   x: 'left',
      //   text: '危险流量',
      // },
      color: ['#F7C744', '#73A0FA', '#74DEB3', '#EB7F65 ', '#197094'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        formatter(params: string | any[]) {
          // 数据单位格式化
          let relVal = params[0].name; // x轴名称
          relVal += "<div style='width:120px'>";
          for (let i = 0, l = params.length; i < l; i++) {
            if (params[i].value) {
              relVal += `<span  style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params[i].color};'><span  style="display:block;padding-left:15px;margin-top:-4px">${params[i].seriesName} : ${params[i].data.showSize}</span></span><br>`;
            }
          }
          relVal += '</div>';
          return relVal;
        },
      },
      legend: {
        data: appFlow.keys,
        align: 'right',
        right: 20,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: appFlow.time,
          axisLine: {
            lineStyle: {
              color: '#c5cace',
            },
          },
          axisTick: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: '{value} K',
          },
          axisLine: {
            lineStyle: {
              color: '#c5cace',
            },
          },
          splitLine: { show: false },
          axisTick: {
            show: false,
          },
        },
      ],
      series: getData(appFlow),
    };
    myChart.setOption(option);
  };
  const getData = (appFlow: {
    [x: string]: any[];
    keys?: any;
    time?: never[];
  }) => {
    const { keys } = appFlow;
    let series = [];
    if (keys && keys.length) {
      series = keys.map((v: string | number) => {
        return {
          name: v,
          type: 'line',
          smooth: true,
          smoothMonotone: 'x',
          symbol: 'none',
          areaStyle: {},
          data: appFlow[v].map((item: any) => {
            if (!item) {
              return {
                value: '',
                showSize: '',
              };
            } else {
              return {
                value: item.actualSize,
                showSize: item.showSize,
              };
            }
          }),
        };
      });
    }
    return series;
  };
  const getKillchainsFlow = () => {
    let _params = {
      startTime: formValue.time[0].valueOf(),
      stopTime: formValue.time[1].valueOf(),
      timeMode: formValue.timeMode,
    };
    killchainsFlow({
      ..._params,
    }).then(res => {
      if (res.flag) {
        if (!_.isEmpty(res.data)) {
          const killChainFlow = {
            stage1: [],
            stage2: [],
            stage3: [],
            stage4: [],
            stage5: [],
            stage6: [],
            stage7: [],
            time: [],
          };
          // res.data.slice_flow[0].killchainsFlow.ActionsOnObjective.actualSize = 80;
          // res.data.slice_flow[1].killchainsFlow.ActionsOnObjective.actualSize = 40;
          // res.data.slice_flow[0].killchainsFlow.Weaponization.actualSize = 20;

          killChainFlow.time = res.data.slice_flow.map((v: any) =>
            moment(v.time).format('YYYY-MM-DD HH:mm'),
          );
          killChainFlow.stage1 = res.data.slice_flow.map(
            (v: any) => v.killchainsFlow.Reconnaissance,
          );
          killChainFlow.stage2 = res.data.slice_flow.map(
            (v: any) => v.killchainsFlow.Weaponization,
          );
          killChainFlow.stage3 = res.data.slice_flow.map(
            (v: any) => v.killchainsFlow.Delivery,
          );
          killChainFlow.stage4 = res.data.slice_flow.map(
            (v: any) => v.killchainsFlow.Exploitation,
          );
          killChainFlow.stage5 = res.data.slice_flow.map(
            (v: any) => v.killchainsFlow.Installation,
          );
          killChainFlow.stage6 = res.data.slice_flow.map(
            (v: any) => v.killchainsFlow['Command and Control'],
          );
          killChainFlow.stage7 = res.data.slice_flow.map(
            (v: any) => v.killchainsFlow['Actions on Objective'],
          );
          //  return {

          //    killChainFlow,
          //    simpleReportListLoading: false,
          //    killChainsVital: data.total,
          //    killchains_flow: data,
          //  };
          setallFlow(killChainFlow);
        }
      }
    });
  };
  const getHootEvent = () => {
    let _params = {
      startTime: formValue.time[0].valueOf(),
      stopTime: formValue.time[1].valueOf(),
      timeMode: formValue.timeMode,
    };
    hotEventList(_params).then((res: any) => {
      if (res.flag) {
        sethotEvent(res.data);
      }
    });
  };
  const initAllFlow = () => {
    const myChart = echarts.init(document.getElementById('appFlow'));

    const option = {
      // title: {
      //   x: 'left',
      //   text: '流量统计',
      // },
      axisPointer: {
        type: 'shadow',
        link: {
          xAxisIndex: 'all',
        },
      },
      yAxis: [
        makeYAxis(
          0,
          {
            // name: '预测实际热量',
            // splitNumber: 3, // 调整间隔
            show: false,
            splitLine: { show: false }, // 去除网格线
          },
          getMax(allFlow.stage7),
        ),

        makeYAxis(
          1,
          {
            show: false,
            splitLine: { show: false }, // 去除网格线
          },
          getMax(allFlow.stage6),
        ),
        makeYAxis(
          2,
          {
            show: false,
            splitLine: { show: false }, // 去除网格线
          },
          getMax(allFlow.stage5),
        ),
        makeYAxis(
          3,
          {
            show: false,
            splitLine: { show: false }, // 去除网格线
          },
          getMax(allFlow.stage4),
        ),
        makeYAxis(
          4,
          {
            show: false,
            splitLine: { show: false }, // 去除网格线
          },
          getMax(allFlow.stage3),
        ),
        makeYAxis(
          5,
          {
            show: false,
            splitLine: { show: false }, // 去除网格线
          },
          getMax(allFlow.stage2),
        ),
        makeYAxis(
          6,
          {
            show: false,
            splitLine: { show: false }, // 去除网格线
          },
          getMax(allFlow.stage1),
        ),
      ],

      tooltip: {
        // 移动端展示方式
        trigger: 'axis',
        transitionDuration: 0,
        confine: true,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#333',
        backgroundColor: 'rgba(255,255,255,0.9)',
        textStyle: {
          fontSize: 12,
          color: '#333',
        },
        formatter(params: any) {
          // 数据单位格式化
          let relVal = params[0].name; // x轴名称
          relVal += "<div style='width:120px'>";
          for (let i = 0, l = params.length; i < l; i++) {
            if (params[i].value) {
              relVal += `<span  style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params[i].color};'><span  style="display:block;padding-left:15px;margin-top:-4px">${params[i].seriesName} : ${params[i].data.showSize}</span></span><br>`;
            }
          }
          relVal += '</div>';
          return relVal;
        },
      },
      grid: [
        makeGrid(chartGridTop, chartGridHeight),
        makeGrid(chartGridTop + (chartGridHeight + 10), chartGridHeight),
        makeGrid(chartGridTop + (chartGridHeight + 10) * 2, chartGridHeight),
        makeGrid(chartGridTop + (chartGridHeight + 10) * 3, chartGridHeight),
        makeGrid(chartGridTop + (chartGridHeight + 10) * 4, chartGridHeight),
        makeGrid(chartGridTop + (chartGridHeight + 10) * 5, chartGridHeight),
        makeGrid(chartGridTop + (chartGridHeight + 10) * 6, chartGridHeight),
      ],
      xAxis: [
        makeXAxis(0, {}, allFlow.time),
        makeXAxis(1, {}, allFlow.time),
        makeXAxis(2, {}, allFlow.time),
        makeXAxis(3, {}, allFlow.time),
        makeXAxis(4, {}, allFlow.time),
        makeXAxis(5, {}, allFlow.time),
        makeXAxis(6, {}, allFlow.time),
      ],
      series: [
        makeGridData(0, 0, 'bar', '目标达成', allFlow.stage7, {
          smooth: true,
          color: '#ec4a27',
        }),
        makeGridData(1, 1, 'bar', '命令控制', allFlow.stage6, {
          smooth: true,
          color: '#e4942a',
        }),
        makeGridData(2, 2, 'bar', '安装植入', allFlow.stage5, {
          smooth: true,
          color: '#d8ce45',
        }),
        makeGridData(3, 3, 'bar', '漏洞利用', allFlow.stage4, {
          smooth: true,
          color: '#eeeb40',
        }),
        makeGridData(4, 4, 'bar', '载荷投递', allFlow.stage3, {
          smooth: true,
          color: '#afc466',
        }),
        makeGridData(5, 5, 'bar', '武器构建', allFlow.stage2, {
          smooth: true,
          color: '#63a8b1',
        }),
        makeGridData(6, 6, 'bar', '侦查跟踪', allFlow.stage1, {
          smooth: true,
          color: '#235f90',
        }),
      ],
    };

    myChart.setOption(option);
  };
  // 数据生成器
  const makeGridData = (
    xAxisIndex: any,
    yAxisIndex: any,
    chartType: any,
    chartName: any,
    chartData = [],
    opt: any,
  ) => {
    return echarts.util.merge(
      {
        type: chartType,
        name: chartName,
        xAxisIndex,
        yAxisIndex,
        data: chartData.map((v: any) => ({
          value: v.actualSize,
          showSize: v.showSize,
        })),
        barCategoryGap: '70%',
        barMaxWidth: 60,
      },
      opt || {},
      true,
    );
  };
  // X轴生成器
  const makeXAxis = (gridIndex: any, opt: any, overviewDate: any) => {
    // 避免X轴数据显示过于频繁
    let axisLabelFlag = false;
    //  if (gridIndex % 2 == 0) {
    //      axisLabelFlag = true;
    //  }
    if (gridIndex === 6) {
      axisLabelFlag = true;
    }

    return echarts.util.merge(
      {
        type: 'category',
        gridIndex,
        // 统一时间轴数据
        data: overviewDate || [],
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#c5cace',
          },
        },
        axisLabel: {
          show: axisLabelFlag,
          /* formatter: function(value) {
  return echarts.format.formatTime('MM-dd', value);
} */
        },
      },
      opt || {},
      true,
    );
  };
  // Y轴生成器
  const makeYAxis = (gridIndex: any, opt: any, logBase: any) => {
    return echarts.util.merge(
      {
        type: 'log',
        logBase: logBase || 10,
        nameLocation: 'middle',
        nameGap: '40',
        gridIndex,
        nameTextStyle: {
          color: '#333',
        },
        axisTick: {
          show: false,
        },
      },
      opt || {},
      true,
    );
  };
  // 获取最大值作为logBase
  const getMax = (arr = [{ actualSize: 0 }]) =>
    Math.max(...arr.map(v => v.actualSize));
  // 直角坐标系内绘图网格
  const makeGrid = (top: any, height: any) => {
    return echarts.util.merge(
      {
        left: 60,
        right: 30,
        top,
        height,
      },
      {},
      true,
    );
  };
  useEffect(() => {
    getEvent();
    ThreatenType();
    getFlowDetail();
    getDangerousFlow();
    getKillchainsFlow();
    getHootEvent();
    getKillChains();
  }, [formValue]);
  useEffect(() => {
    initThreatenChart();
  }, [threatFlow]);
  useEffect(() => {
    initdangerousChart();
  }, [appFlow]);
  useEffect(() => {
    initAllFlow();
  }, [allFlow]);
  let total = 0;
  if (!_.isEmpty(threatenLvObj.total)) {
    Object.keys(threatenLvObj.total).forEach(v => {
      return (total += threatenLvObj.total[v].actualSize);
    });
  }
  let dangerTotal = 0;
  if (!_.isEmpty(dangerous.total)) {
    Object.keys(dangerous.total).forEach(v => {
      dangerTotal += dangerous.total[v].actualSize;
    });
  }

  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form
            form={form}
            className="searchForm"
            onFinish={onSubmit}
            layout="inline"
          >
            <Row>
              <Col span={10}>
                <Form.Item
                  label="时间"
                  name="time"
                  initialValue={formValue.time}
                >
                  <RangePicker
                    allowClear
                    ranges={{
                      '近一年': [moment().add(-1, 'year'), moment()],
                      '近半年': [moment().add(-6, 'month'), moment()],
                      '近一月': [moment().add(-1, 'month'), moment()],
                      '近一周': [moment().add(-7, 'd'), moment()],
                      '近一天': [moment().add(-1, 'd'), moment()],
                      '今天': [moment().startOf('day'), moment().endOf('day')],
                      '本周': [moment().startOf('week'), moment().endOf('week')],
                      '本月': [moment().startOf('month'), moment().endOf('month')],
                      '本年度': [moment().startOf('year'), moment().endOf('year')],
                    }}
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SearchOutlined />}
                    className='margin_left_30'
                  >
                    搜索
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="导出报表" key="1">
          <ReportFrom
            eventData={eventData}
            dangerous={dangerous}
            threatenTypesData={threatenTypesData}
            threatenLvObj={threatenLvObj}
            formValue={formValue}
            totalKillChains={totalKillChains}
            hotEvent={hotEvent}
            ref={childForm}
          />
        </Panel>
      </Collapse>
      <div className={style.chartBox}>
        <div className={style.top}>
          <Card
            title="事件攻击总数"
            bodyStyle={{ padding: 0 }}
            style={{ width: '33%' }}
          >
            <div className={style.threatenBox}>
              <p className={style.eventNumber}>{eventData.totalCount}</p>
              <div className={style.eventBox}>
                <div className={style.high}>
                  高危 {eventData.threatLevels.High}
                </div>
                <div className={style.mid}>
                  中危 {eventData.threatLevels.Medium}
                </div>
                <div className={style.low}>
                  低危 {eventData.threatLevels.Low}
                </div>
              </div>
            </div>
            {/* <div className="ant-card-head-title"></div> */}
            <Card title="基于威胁等级的危险流量统计">
              <div className={style.threatenBox}>
                {Object.keys(threatenLvObj.total).map((v, index) => (
                  <div className={style.Listcontent} key={index}>
                    <div className={style.progressBarWrap}>
                      <div className={style.progressBar}>
                        <div
                          className={style.source}
                          style={{
                            width: `${(threatenLvObj.total[v].actualSize /
                              total) *
                              100}%`,
                            backgroundColor: threatLevelColorMap[v],
                          }}
                        />
                      </div>
                      <div>{v}</div>
                    </div>
                    <div className={style.score}>
                      {threatenLvObj.total[v].showSize}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Card>
          <Card title="威胁分类统计" style={{ width: '33%' }}>
            <div className={style.threalFlag}>
              {Object.keys(threatenTypesData.threatFlags).map((v, index) => (
                <div key={index}>
                  <div>
                    <img
                      src={threatFlagImg[v]}
                      className={style.threatFlag}
                      alt=""
                    />
                    <span>{threatFlagNameMap[v]}</span>
                    <p className={style.threatenNumber}>
                      {threatenTypesData.threatFlags[v]}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card title="基于应用协议的危险流量统计" style={{ width: '33%' }}>
            {!_.isEmpty(dangerous.total) &&
              Object.keys(dangerous.total).map((v, index) => (
                <div className={style.Listcontent} key={index}>
                  <div className={style.progressBarWrap}>
                    <div className={style.progressBar}>
                      <div
                        className={style.source}
                        style={{
                          width: `${(dangerous.total[v].actualSize /
                            dangerTotal) *
                            100}%`,
                          backgroundColor: '##003793',
                        }}
                      />
                    </div>
                    <div>{v}</div>
                  </div>
                  <div className={style.score}>
                    {dangerous.total[v].showSize}
                  </div>
                </div>
              ))}
          </Card>
        </div>
        <div className={`${style.chartBox} ${style.top}`}>
          <Card title="杀伤链阶段统计" style={{ width: '33%' }}>
            <div className={style.TotalKillChains}>
              {Object.keys(totalKillChains).map((v, index) => (
                <div className={style.killChainStage} key={index}>
                  <div
                    className={classnames(
                      style[`icon${index + 1}`],
                      style.icon,
                    )}
                  />
                  <div className={style.progressBarWrap}>
                    <div className={style.progressBar}>
                      <div
                        className={style.source}
                        style={{
                          width: `${(totalKillChains[v] / killChainsTotal) *
                            100}%`,
                        }}
                      />
                    </div>
                    <div>{killChainMap[v]}</div>
                  </div>
                  <div className={style.number}>{totalKillChains[v]}</div>
                </div>
              ))}
            </div>
          </Card>
          <Card
            title="基于时间和威胁等级的危险流量统计"
            style={{ width: '66.5%' }}
          >
            <div id="threatenChart" className={style.threatenChartBox}></div>
          </Card>
        </div>
        <div className={`${style.eventBox} ${style.chartBox}`}>
          <Card title="热门事件" style={{ width: '33%' }}>
            <div className={style.HotEvent}>
              {hotEvent.map((v: any, index: any) => (
                <div className={style.item} key={index}>
                  <div className={style.number}>
                    <div style={{ fontSize: 22 }}>{v.threatScore}</div>
                    <div
                      style={{
                        fontWeight: 'normal',
                        color: 'rgba(0, 0, 0, 0.65)',
                      }}
                    >
                      得分
                    </div>
                  </div>

                  <div className={style.hot_center}>
                    <Tooltip title={v.behaviors}>
                      {ellipsis(v.behaviors, 60)}
                    </Tooltip>

                    <div className={style.top}>
                      <div>
                        发生时间:
                        {moment(v.occurredTime).format('YYYY-MM-DD HH:mm:ss')}
                      </div>
                      <div>
                        攻击次数:<span className={style.count}>{v.count}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
          <div style={{ width: '66%' }}>
            <Card
              title="基于时间和应用协议的危险流量统计"
              style={{ width: '100%' }}
            >
              <div id="dangerousChart" className={style.threatenChartBox}></div>
            </Card>
            <Card
              title="基于时间和杀伤链的威胁流量统计"
              style={{ width: '100%' }}
            >
              <div className={style.killChains}>
                <div className={style.images}>
                  {Object.keys(killChainMap).map((v, index) => (
                    <div
                      className={classnames(
                        style.icon,
                        style[`icon${index + 1}`],
                      )}
                      key={index}
                    />
                  ))}
                </div>
                <div id="appFlow" className={style.appFlowBox}></div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Index;
