# -*- coding: utf-8 -*-
# @Time    : 2019-08-12 14:10
# <AUTHOR> wu
# @File    : feature_group_manager.py
# @Software: PyCharm

"""module explore"""

import datetime
import json
import time
import hashlib
from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from utils.database import MongoDB
from utils.json_format import JSONEncoder
from utils.param_check import Validator, check_flask_args
from api_1_0.auth.auths import Authenticate
from api_1_0.utils.flask_log import ndr_log_to_box
from celery_tasks.feature_activate_task import periodic_task

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def create_group_id():
    """
    :return: group id
    """
    md5_str = hashlib.md5(str(time.clock()).encode('utf-8'))
    return md5_str.hexdigest()[16:24]  # 保存到hdp内存中的数据太长，截断


# parameter check
def param_check(name, sid_list, action, mongodb):
    """
    :param name:      特征组名称
    :param sid_list:  特征列表
    :param action:    操作类型
    :param mongodb:   mongodb
    :return:          正确返回空，否则返回错误提示信息
    """
    rst = []
    if name == "" or name is None:
        rst.append('The group name cannot be empty')
    elif len(name) > 128:
        rst.append('Group name cannot exceed 128 characters in length')
    # if sid_list and action == "add":
    #     tmp = []
    #     for sid in sid_list:
    #         item = mongodb.find_one('feature', {'sid': sid})
    #         if item is None:
    #             tmp.append(sid)
    #     if tmp:
    #         rst.append("Sid " + str(tmp) + " is not exist")

    return rst


class FeatureGroupWithId(Resource):
    """
        单个特征组删除；
        单个特征组修改；
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    # # get one feature group
    # @check_flask_args(Validator("feature_group_id_get_schema"), request)
    # def get(self, **kwargs):
    #     group_id = kwargs["args"]["group_id"]
    #     page = int(kwargs["args"]["page"])
    #     page_size = int(kwargs["args"]["pageSize"])
    #     """Get one feature group"""
    #     rst = self.mongodb.find_one('feature_group', {"groupId": group_id},
    #                                 {"_id": 0,
    #                                  "groupName": 1,
    #                                  "groupId": 1,
    #                                  "createTime": 1,
    #                                  "createByUserId": 1,
    #                                  "createByUserName": 1,
    #                                  "sidList": 1
    #                                  })
    #     if rst is not None:
    #         if rst['createByUserId'] != self.user_id and self.user_name != 'admin':
    #             LOG.error("Insufficient permissions.")
    #             return flask_response("Insufficient permissions.", False, {})
    #         sid_list = rst['sidList']
    #         rst["sidList"] = []
    #         rst["featureValidCount"] = 0
    #         rst["featureInvalidCount"] = 0
    #         for sid in sid_list:
    #             item = self.mongodb.find_one('feature', {'sid': sid}, {"_id": 0})
    #             if item:
    #                 if not item['isCustomer']:
    #                     item.pop('featureContent')
    #                 rst['sidList'].append(item)
    #                 rst["featureValidCount"] += 1
    #             else:
    #                 rst['sidList'].append({"sid": sid, "featureStatus": "deleted"})
    #                 rst["featureInvalidCount"] += 1
    #         sid_list = rst['sidList']
    #         rst['search_count'] = len(sid_list)
    #         rst['sidList'] = sid_list[((page - 1) * page_size): (page * page_size)]
    #         rst['page'] = page
    #         rst['pageSize'] = page_size
    #         col_fmt = JSONEncoder().encode(rst)
    #         col_rst = json.loads(col_fmt)
    #         return flask_response({}, True, col_rst)
    #
    #     return flask_response("Not found", False, {})

    # delete group
    @check_flask_args(Validator("feature_group_id_delete_schema"), request)
    def delete(self, **kwargs):
        """
        :param group_id:
        :return:
        """
        group_id = kwargs["args"]["group_id"]
        group = self.mongodb.find_one("feature_group", {"groupId": group_id})
        # delete process
        if group is None:
            message = 'The feature group does not exists'
            LOG.error("group: %s delete failed.%s" % (group_id, message))
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]删除失败：不存在。" % group_id)
            return flask_response(message, False, {})
        if group['createByUserId'] != self.user_id and self.user_name != 'admin':
            LOG.error("Insufficient permissions.")
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]删除失败：权限不足。" % group["groupName"])
            return flask_response("Insufficient permissions.", False, {})
        if group['groupName'] == "default_allRules":
            return flask_response("默认特征组不能被删除", False, {})
        task_group = self.mongodb.find_one("back_explore", {"curFeatureGrpList": {"$elemMatch": {"$eq": group_id}}})
        if task_group is not None:
            message = 'The feature group is used task %s' % task_group["taskName"]
            LOG.error("group: %s delete failed.%s" % (group_id, message))
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]删除失败。%s" % (group_id, message))
            return flask_response(message, False, {})
        # 更新特征的特征组字段
        all_feature = self.mongodb.find('feature', {"featureGroup": {"$in": [group_id]}})
        for feat in all_feature:
            self.mongodb.update_one('feature', {"_id": feat['_id']}, {"$pull": {"featureGroup": group_id}})

        self.mongodb.delete("feature_group", {"groupId": group_id})
        ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]删除成功。" % group["groupName"])
        periodic_task.delay(upgrade=True)
        LOG.info("group: %s, %s deleted success." % (group["groupName"], group["groupId"]))
        return flask_response("Deleted successfully.", True, {})

    # Modify group
    @check_flask_args(Validator("feature_group_update_schema"), request)
    def put(self, **kwargs):
        group_id = kwargs["group_id"]
        """
        :param group_id:
        :return:
        """
        group = self.mongodb.find_one('feature_group', {"groupId": group_id})
        if group is None:
            message = "The group does not exist."
            LOG.error("group: %s update failed.%s" % (group_id, message))
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]修改失败：不存在。" % group_id)
            return flask_response(message, False, {})
        if group['createByUserId'] != self.user_id and self.user_name != 'admin':
            LOG.error("Insufficient permissions.")
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]修改失败：权限不足。" % group["groupName"])
            return flask_response("Insufficient permissions.", False, {})
        if group['groupName'] in ["default_allRules", "default_apt_attack", "default_exploit_attack",
                                  "default_remote_ctrl", "default_malware"]:
            LOG.error("[%s]不能被操作." % group["groupName"])
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]不能被操作。" % group["groupName"])
            return flask_response("[%s]不能被操作." % group["groupName"], False, {})
        sid_list = kwargs["sidList"]
        action = kwargs["action"]
        if action == "add":
            for sid in sid_list:
                self.mongodb.update_one('feature', {"sid": sid}, {"$addToSet": {"featureGroup": group_id}})
        else:
            for sid in sid_list:
                self.mongodb.update_one('feature', {"sid": sid}, {"$pull": {"featureGroup": group_id}})

        ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]修改成功。" % group["groupName"])
        periodic_task.delay(upgrade=True)
        LOG.info("group: %s update success." % group_id)
        return flask_response("", True, {})


class FeatureGroup(Resource):
    """
        创建特征组；
        按分页获取特征组；
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()

    # create feature group
    @check_flask_args(Validator("feature_group_create_schema"), request)
    def post(self, **kwargs):
        """Post method"""
        name = kwargs["args"]["name"]
        descrip = kwargs["args"]["descrip"]
        group = self.mongodb.find_one('feature_group',
                                      {"$and": [{"groupName": name}, {"createByUserId": self.user_id}]})
        if group is not None:
            message = 'The feature group [%s] already exists.' % name
            LOG.error("group: created failed.Reason: %s" % message)
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]创建失败：已存在。" % name)
            return flask_response(message, False, {})
        body = {
            "groupName": name,  # 特征组名称
            "description": descrip,  # 描述
            "attribute": "user",  # 属性
            "groupId": create_group_id(),  # 特征组 ID
            "createTime": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 创建时间
            "createByUserId": self.user_id,
            "createByUserName": self.user_name
        }
        self.mongodb.insert_one("feature_group", body)
        LOG.info("group: %s, %s created success" % (body["groupName"], body["groupId"]))
        ndr_log_to_box(NdrLog.Type.OPERATE, "特征组[%s]创建成功。" % name)
        return flask_response("特征组[%s]创建成功" % name, True, {'groupId': body["groupId"]})

    # get all groups 按分页获取特征组
    @check_flask_args(Validator("feature_group_get_all_schema"), request)
    def get(self, **kwargs):
        """Get method"""
        page = int(kwargs["args"]["page"])
        page_size = int(kwargs["args"]["pageSize"])
        if page <= 0 or page_size <= 0:
            message = 'Parameter page or pagesize error'
            return flask_response(message, False, {})
        condition = {}
        if self.user_name != 'admin':
            condition = {'createByUserId': self.user_id}
        group_feat_count = {}
        # 查询每个group的feature数量
        groups = self.mongodb.find('feature_group', condition, {'groupId': 1})
        for group in groups:
            group_feat_count[group['groupId']] = 0

        for key in group_feat_count.keys():
            group_feat_count[key] = self.mongodb.find('feature', {"featureGroup": {"$in": [key]}}).count()

        t_count = self.mongodb.find('feature_group', condition).count()
        rst = self.mongodb.find('feature_group', condition, {"_id": 0,
                                                             "groupName": 1,
                                                             "description": 1,
                                                             "attribute": 1,
                                                             "groupId": 1,
                                                             "createTime": 1,
                                                             "createByUserId": 1,
                                                             "createByUserName": 1, })\
            .sort([("createTime", -1)]).limit(page_size).skip((page - 1) * page_size)
        if rst is None:
            message = 'Not found'
            return flask_response(message, False, {})
        cols = []
        data = {"count": t_count}
        for col in rst:
            col["featureValidCount"] = group_feat_count[col['groupId']]
            col = JSONEncoder().encode(col)
            cols.append(json.loads(col))
        data["detail"] = cols
        data["page"] = page
        data["pagesize"] = page_size
        return flask_response("", True, data)
