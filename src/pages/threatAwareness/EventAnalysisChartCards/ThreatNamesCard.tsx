import React, { useEffect } from 'react';

import { DashCard } from '../DashCard';
import ReactECharts from 'echarts-for-react';
import { Empty, Select } from 'antd';
import { TimeRange } from '../UnSettledThreat/HighRiskHostCard';
import { useThreatNameData } from '../hooks';
import { barChartSortOptions } from '../constant';


export type Props = {
  style?: React.CSSProperties;
  className?: string;
  timeRange: TimeRange;
}

function ThreatNamesCard({ timeRange }: Props): JSX.Element {

  const { loading, data, setSort, setTimeRange } = useThreatNameData(timeRange);

  useEffect(() => {
    setTimeRange(timeRange)
  }, [timeRange])

  const titleRender = () => {
    return (
      <div className='flex justify-between items-center flex-1 pr-6'>
        <span>威胁名称 TOP10</span>
        <div>
          排序方式：
          <Select
            size='small'
            defaultValue="all"
            style={{ width: 200 }}
            onChange={setSort}
            options={barChartSortOptions}
          />
        </div>
      </div>
    )
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      icon: 'circle',
      right: 'center',
      top: '5%',
      itemWidth: 10,
      itemHeight: 10,
    },

    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      show: false
    },
    yAxis: {
      type: 'category',
      data: data.names,
      inverse: true,
    },
    series: [
      {
        name: '事件总数量',
        type: 'bar',
        barWidth: '10px',
        data: data.total,
        label: {
          show: true,
          position: 'right',
        },
        itemStyle: {
          color: '#52ACCC',
          borderRadius: [0, 4, 4, 0]
        },
        barGap: '20%',
        barCategoryGap: '50%',
      },
      {
        name: '成功事件数量',
        type: 'bar',
        barWidth: '10px',
        data: data.success,
        label: {
          show: true,
          position: 'right',
        },
        itemStyle: {
          color: '#FF6B6B',
          borderRadius: [0, 4, 4, 0]
        },
      },
    ]
  };


  return (
    <DashCard contentStyle={{ minHeight: 400 }} title={titleRender()}>
      {
        data.total.length || loading ? (
          <ReactECharts
            showLoading={loading}
            option={option}
            notMerge={true}
            style={{ width: '100%', height: 400 }}
            opts={{ renderer: 'svg' }}
          />
        ) : (<Empty />)
      }

    </DashCard>
  );
}

export default ThreatNamesCard;
