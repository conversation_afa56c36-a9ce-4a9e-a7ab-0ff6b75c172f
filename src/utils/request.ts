import { notification, Modal, message } from 'antd';
import { formatMessage, history as router } from 'umi';
import { isDev } from '@/utils/utils';
// eslint-disable-next-line import/no-extraneous-dependencies
import { MessageValue } from 'umi-types/locale';
/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { extend, RequestOptionsInit as UmiRequestOptionsInit, ResponseError } from 'umi-request';
import isPlainObject from 'lodash/isPlainObject';

import { ShowType } from '@/common/constants';

import { deleteNullOrUndefinedField } from './utils';

export interface PageRequest {
  page?: number;
  pageSize?: number;
  all?: boolean;
}
export interface Response<T> {
  msg: string;
  code: number;
  data: T;
}

export interface PageData<T> {
  docs: T[];
  limit: number;
  page: number;
  pages: number;
  total: number;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface PageResponse<T> extends Response<PageData<T>> {}

/**
 * 异常处理程序
 */
const errorHandler = async (error: ResponseError): Promise<void> => {
  const { response } = error;

  const { status, statusText, url } = response;

  const errorText = formatMessage({ id: `error.server.status.${status}` }) || statusText || '未知错误';

  switch (status) {
    case 400:
      // eslint-disable-next-line no-case-declarations
      const { message: errMsg } = await response.json();
      notification.error({
        message: `${status}`,
        description: errMsg || errorText,
      });
      break;
    case 401:
      window.location.href = `/login?redirect=${window.location.pathname || ''}`;
      break;
    case 403:
      router.push('/exception/403');
      break;
    case 404:
      router.push('/exception/404');
      break;
    // case 498: // license 证书不合法!
    //   router.push('/app/box/sys/license');
      break;
    case 500:
      router.push('/exception/500');
      break;
    default:
      notification.error({
        message: `请求错误 ${status}: ${url}`,
        description: errorText,
      });
  }
  throw error;
};

/**
 * 处理国际化消息
 * @param {Response<T>} res - 接口返回数据
 * @param {function|object} placeholderVales - 处理国际化占位符
 * @returns {string} 处理过的消息
 * @todo 处理成功的消息，但是目前对于成功消息暂时还没有想到合适的办法
 */
export function i18nMsg<T>(
  res: Response<T>,
  placeholderVales?: (data: Response<T>) => { [key: string]: MessageValue } | { [key: string]: MessageValue },
): string {
  const { code, data } = res;
  let { msg } = res;
  if (code !== 0) {
    msg = formatMessage(
      {
        id: `error.server.${code}`,
        defaultMessage: msg,
      },
      typeof placeholderVales === 'function' ? placeholderVales(res) : placeholderVales || data || {},
    );
  }
  return msg;
}

/**
 * 配置request请求时的默认参数
 */
const request = extend({
  // errorHandler, // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
  // eslint-disable-next-line
  prefix: window.__POWERED_BY_QIANKUN__ ? '/mica-api/api/v1/' : '',
});

/**
 * 业务错误处理
 * code 为非0值时处理
 */

export interface RequestOptionsInit extends UmiRequestOptionsInit {
  showType?: ShowType;
}

let resolveErrorFlag = false;
request.interceptors.response.use(async (response, options: RequestOptionsInit) => {
  if (options.responseType === 'blob') {
    return response;
  }
  const { status } = response;
  const data = await response.clone().json();
  // console.log('[ data ]-143', data)
  if (status >= 200 && status < 300) {
    const {
      message,
      code,
      flag,
    }: {
      message: string;
      showType: number;
      code: number;
      flag: boolean;
    } = data || {};

    if (flag === false && message === '请先登录！') {
      window.location.href = `/login`;
      throw Error('401 Unauthorized');
    }
    // 未登录特殊处理
    if (code === 10004) {
      // window.location.href = `/login?redirect=${window.location.pathname || ''}`;
      window.location.href = `/login`;
      throw Error('401 Unauthorized');
    }

    // 当 code 为 0时并且又有 msg 时，表示需要提示成功信息
    // 成功信息强制采用 antd.message.success
    // 这样一来，showType 就被定义为了错误提示类型
    const showType = (code === 0 ? ShowType.MESSAGE_SUCCESS : null) || options.showType || data.showType || ShowType.NONE;

    if (!resolveErrorFlag && message && showType !== ShowType.NONE) {
      resolveErrorFlag = true;

      const content = i18nMsg(data) || '发生未知错误，请稍后重试！';
      const openAlert = (type: string) => {
        Modal[type]({
          title: '通知',
          content,
        });
      };
      const openNotification = (type: string) => {
        notification[type]({
          message: '通知',
          description: content,
        });
      };
      const openMessage = (type: string) => {
        message[type](content, 5);
      };

      switch (showType) {
        case ShowType.ALERT_ERROR:
          openAlert('error');
          break;
        case ShowType.ALERT_WARNING:
          openAlert('warning');
          break;
        case ShowType.ALERT_SUCCESS:
          openAlert('success');
          break;
        case ShowType.ALERT_INFO:
          openAlert('info');
          break;
        case ShowType.NOTIFICATION_ERROR:
          openNotification('error');
          break;
        case ShowType.NOTIFICATION_WARNING:
          openNotification('warning');
          break;
        case ShowType.NOTIFICATION_SUCCESS:
          openNotification('success');
          break;
        case ShowType.NOTIFICATION_INFO:
          openNotification('info');
          break;
        case ShowType.MESSAGE_ERROR:
          openMessage('error');
          break;
        case ShowType.MESSAGE_WARNING:
          openMessage('warning');
          break;
        case ShowType.MESSAGE_SUCCESS:
          openMessage('success');
          break;
        case ShowType.MESSAGE_INFO:
          openMessage('info');
          break;
        default:
          break;
      }

      // 1秒内多个错误请求不再弹出提示
      setTimeout(() => {
        resolveErrorFlag = false;
      }, 1000);
    }
  }

  return response;
});

// request拦截器, 移除掉请求参数中值为 null 或者 undefined 的字段.
request.interceptors.request.use((url, options) => {
  let { params, data, headers, url: path, apiVersion } = options;
  let env = isDev();

  let prefix = window.__POWERED_BY_QIANKUN__ ? `/mica-api/api/${apiVersion || 'v1'}/` : '';
  let devRequest = window.__POWERED_BY_QIANKUN__ ? `` : `/api/${apiVersion || 'v1'}/`;
  prefix = env ? devRequest : prefix;
  if (isPlainObject(data)) {
    data = deleteNullOrUndefinedField(data);
  }

  // 将 CSRF Token 加入 header 中
  const matches = /csrfToken=([\w-]+)\b/.exec(document.cookie);
  if (matches) {
    const csrfToken = matches[1];
    if (typeof Headers === 'function' && headers instanceof Headers) {
      headers.append('x-csrf-token', csrfToken);
    } else if (typeof headers === 'object') {
      headers['x-csrf-token'] = csrfToken;
    } else if (typeof headers === 'undefined') {
      headers = {
        'x-csrf-token': csrfToken,
      };
    }
  }

  if (params) {
    params = deleteNullOrUndefinedField(params);
  }

  return {
    url: prefix + path,
    options: { ...options, data, params, headers },
  };
});

export default request;
