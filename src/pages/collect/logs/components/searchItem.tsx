/*
 * @Author: tianh
 * @Date: 2022-02-09 10:28:02
 * @LastEditors: tianh
 * @LastEditTime: 2022-05-30 17:07:37
 * @Descripttion:
 */

import { regPort } from '@/utils/enumList';
import { getQueryVariable } from '@/utils/utils';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Collapse, DatePicker, Form, Input, Select } from 'antd';
import cn from 'classnames';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import '../../style.less';
import Charts from './charts';
import s from './searchItem.less';

const { Option } = Select;
const { Panel } = Collapse;
const { RangePicker } = DatePicker;

const Dhcp = (props: any) => {
  const [advanceFilterVisible, setAdvanceFilterVisible] = useState(false);
  const { activeKey, searchList, taskList = [], params, setparams } = props;
  const [form] = Form.useForm();
  const isAccuracy = [
    { value: true, key: '是' },
    { value: false, key: '否' },
  ];

  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      const [startTime, stopTime] = value.time || [];
      startTime?.millisecond(0);
      stopTime?.millisecond(0);
      setparams({
        ...params,
        ...value,
        startTime: startTime?.toDate()?.getTime() || undefined,
        stopTime: stopTime?.toDate()?.getTime() || undefined,
        page: 1,
        pageSize: 10,
      });
    });
  };

  const changeTask = (value: any) => {
    taskList.forEach((item: any) => {
      if (item.celeryId === value) {
        if (value === '00') {
          if (getQueryVariable('startTime') && getQueryVariable('stopTime')) {
            form.setFieldsValue({
              time: [moment(parseInt(getQueryVariable('startTime'))), moment(parseInt(getQueryVariable('stopTime')))],
            });
          } else {
            const searchTime = form.getFieldsValue(['time']).time;
            if (searchTime && searchTime.length !== 0) {
              form.setFieldsValue({
                time: searchTime,
              });
            }
          }
        } else {
          form.setFieldsValue({
            time: [moment(item.startTime), moment(item.endTime)],
          });
        }
      }
    });
  };

  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(params);
    if (params.time && params.time.length) {
      searchList();
    }
  }, [params]);

  const fields = React.useMemo(() => {
    return [
      {
        label: '时间范围',
        key: 'time',
        name: 'time',
        rules: [
          {
            required: true,
            message: '请输入时间范围',
          },
        ],
        components: (
          <RangePicker
            allowClear
            ranges={{
              最近5秒: [moment(new Date()).add(-5, 's'), moment()],
              最近1分钟: [moment(new Date()).add(-1, 'minute'), moment()],
              最近5分钟: [moment(new Date()).add(-5, 'minute'), moment()],
              最近30分钟: [moment(new Date()).add(-30, 'minute'), moment()],
              最近1小时: [moment(new Date()).add(-1, 'hour'), moment()],
              最近4小时: [moment(new Date()).add(-4, 'hour'), moment()],
              最近12小时: [moment(new Date()).add(-12, 'hour'), moment()],
              最近1天: [moment(new Date()).add(-1, 'day'), moment()],
              最近一周: [moment(new Date()).add(-7, 'day'), moment()],
              最近一月: [moment(new Date()).add(-1, 'month'), moment()],
              上个月: [
                moment(new Date()).add(-1, 'month').startOf('month'),
                moment(new Date()).add(-1, 'month').endOf('month'),
              ],
              今天: [moment(new Date()).startOf('day'), moment().endOf('day')],
              本周: [moment(new Date()).startOf('week'), moment().endOf('week')],
              本月: [moment(new Date()).startOf('month'), moment().endOf('month')],
            }}
            format="YYYY-MM-DD HH:mm:ss"
          />
        ),
      },
      {
        label: '精确匹配IP',
        key: 'isAccurate',
        name: 'isAccurate',
        components: (
          <Select allowClear={true}>
            {isAccuracy.map((item: any) => {
              return (
                <Option key={item.key} value={item.value}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        ),
      },
      {
        label: params.isAccurate === true ? '源IP' : 'IP1',
        key: 'ip1',
        name: 'ip1',
        components: <Input />,
      },
      {
        label: params.isAccurate === true ? '目的IP' : 'IP2',
        key: 'ip2',
        name: 'ip2',
        components: <Input />,
      },
      {
        label: '任务名称',
        key: 'celeryId',
        name: 'celeryId',
        components: (
          <Select onChange={changeTask} allowClear={true}>
            {taskList.map((item: any) => {
              return (
                <Option key={item.celeryId} value={item.celeryId}>
                  {item.taskName}
                </Option>
              );
            })}
          </Select>
        ),
      },
      {
        label: '关键字',
        key: 'keyword',
        name: 'keyword',
        components: <Input />,
      },
      {
        label: '源端口',
        key: 'srcPort',
        name: 'srcPort',
        rules: [{ pattern: regPort, message: '请检查源端口号' }],
        components: <Input />,
      },
      {
        label: '目的端口',
        key: 'dstPort',
        name: 'dstPort',
        rules: [{ pattern: regPort, message: '请检查目的端口号' }],
        components: <Input />,
      },
      {
        label: '数据包名',
        key: 'pcap_filename',
        name: 'pcap_filename',
        components: <Input />,
      },
    ]
      .concat(
        activeKey === 'mail'
          ? [
              {
                label: '邮件主题',
                key: 'subject',
                name: 'subject',
                components: <Input />,
              },
              {
                label: '邮件内容',
                key: 'body',
                name: 'body',
                components: <Input />,
              },
              {
                label: '发件人',
                key: 'from',
                name: 'from',
                components: <Input />,
              },
              {
                label: '收件人',
                key: 'to',
                name: 'recipient',
                components: <Input />,
              },
              {
                label: '附件名',
                key: 'file_names',
                name: 'file_names',
                components: <Input />,
              },
            ]
          : [],
      )
      .concat(
        activeKey === 'conn'
          ? [
              {
                label: '应用协议',
                key: 'application',
                name: 'application',
                components: <Input />,
              },
            ]
          : [],
      );
  }, [activeKey, taskList]);

  const handleFieldsRender = (fieldList: object[]) => {
    return fieldList.map((item: any, index: number) => {
      const { components, ...reset } = item;
      return <Form.Item {...reset}>{components}</Form.Item>;
    });
  };

  const renderFields = () => {
    if (advanceFilterVisible) {
      return handleFieldsRender(fields);
    }
    return handleFieldsRender(fields.slice(0, 10));
  };

  return (
    <div className="seachItem">
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <div className="flex">
            <Form
              labelCol={{ span: 5 }}
              wrapperCol={{ span: 17 }}
              className={cn('of-hidden !grid !gap-3 !grid-cols-3', s.form)}
              form={form}
              onFinish={onSubmit}
              layout="inline"
              initialValues={params}
            >
              {renderFields()}
            </Form>
            <div className="flex flex-col pt-[10px]">
              <Button type="primary" onClick={onSubmit}>
                搜索
              </Button>
              <Button
                className={fields.length <= 10 ? '!invisible' : ''}
                type="link"
                icon={advanceFilterVisible ? <UpOutlined /> : <DownOutlined />}
                onClick={() => {
                  setAdvanceFilterVisible(!advanceFilterVisible);
                }}
              >
                {advanceFilterVisible ? '收起全部' : '展开全部'}
              </Button>
            </div>
          </div>
        </Panel>
      </Collapse>
      <Charts params={params} />
    </div>
  );
};

export default Dhcp;
