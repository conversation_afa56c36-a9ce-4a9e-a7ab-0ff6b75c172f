/*
 * @Author: 田浩
 * @Date: 2021-09-16 17:15:50
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-06 17:39:25
 * @Descripttion:
 */
import React, { useEffect, useState, useRef } from 'react';
import { Button, Card, Table, Timeline, Tooltip, Modal, message, Typography, Empty, Input, Spin } from 'antd';
import moment from 'moment';
import classnames from 'classnames';
import { getQueryVariable, calculationByte, ellipsis, computeWeekTime } from '@/utils/utils';
import { threatLevelColorMap } from '@/utils/enumList';
import { meansList, threat_event, editMeans, filingInformation } from '@/services/collect';
import { useHistory } from '@/hooks/global';
import computer from '@/assets/images/computer.png';
import commomStyle from '@/style/index.less';
import MeansForm from './meansForm';
import style from './style.less';
import _ from 'lodash';
import { SearchOutlined } from '@ant-design/icons';
import { divide } from 'numeral';

const echarts = require('echarts');
const { Paragraph } = Typography;
const initWeekData = computeWeekTime();

const Means = () => {
  const childForm = useRef<any>();
  const history = useHistory();
  const [headDetail, setheadDetail] = useState<any>({});
  const [currentData, setCurrentData] = useState<any>({});
  const [sendColor, setsendColor] = useState<any>([]);
  const [colorList, setcolorList] = useState<any>([]);
  const [threatData, setthreatData] = useState({});
  const [weekData, setweekData] = useState<any>(initWeekData.slice());
  const [tableData, settableData] = useState([]);
  const [visible, setvisible] = useState(false);
  const [editValue, seteditValue] = useState<any>({});
  const [detailVisible, setdetailVisible] = useState(false);
  const [infoData, setinfoData] = useState<any>({});
  const [loading, setloading] = useState(false);
  const columns = [
    {
      title: '目的ip',
      dataIndex: 'ip',
    },
    {
      title: '目的端口列表',
      dataIndex: 'port',
      width: '80%',
      render: (t: any) => {
        return (
          <div>
            <Tooltip title={t.toString()}>{ellipsis(t.toString(), 20)}</Tooltip>
          </div>
        );
      },
    },
  ];
  const handleDoubleClick = () => {
    setdetailVisible(true);
  };
  const onSearchInfo = (value: any) => {
    console.log(value);
    setloading(true);
    filingInformation({ ip: value }).then((res) => {
      if (res.flag) {
        setloading(false);
        setinfoData(res.data);
      } else {
        message.error(res.message);
      }
    });
  };

  const handleOk = () => {
    childForm.current.validateFields().then((value: any) => {
      if (value.ports.length) {
        let boo = false;
        value.ports.forEach((item: string) => {
          if (!(/^[0-9]*$/.test(item) && 1 <= Number(item) && Number(item) <= 65535)) {
            boo = true;
          }
        });
        if (boo) {
          message.error('请输入1~65535的纯数字');
          return;
        }
      }
      let obj = {};
      value.ports.forEach((item: string) => {
        if (editValue.enable_port.hasOwnProperty(item)) {
          obj[item] = editValue.enable_port[item];
        } else {
          obj[item] = 0;
        }
      });
      value.enable_port = obj;
      delete value.ports;
      // 编辑
      editMeans(value).then((res) => {
        if (res.flag) {
          message.success(res.message);
          setvisible(false);
          getHeadInfo(getQueryVariable('ip_addr'));
        } else {
          message.error(res.message);
        }
      });
    });
  };

  // 获取威胁事件历史记录
  const getThreat_event = (ip_addr: any) => {
    threat_event({ ip_addr: ip_addr }).then((res) => {
      if (res.flag) {
        setthreatData(res.data);
      }
    });
  };
  const handleEdit = () => {
    let value = { ...headDetail };
    value.labels = value.labels.toString();
    seteditValue(value);
    setvisible(true);
  };
  const showMore = () => {
    history.push(`/app/mica/meansManagement`);
  };
  /**
   * @name:
   * @description: 初始化应用流量图表
   * @param {*}
   * @return {*}
   */

  const initApplication = () => {
    const myChart = echarts.init(document.getElementById('application'));
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          // 字节自动转单位
          let str = '';
          str = calculationByte(params.value);
          return `<div style='color:${params.color}'>${params.name} : <span style='margin-right:10px'>${str}111</span></div>`;
        },
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          labelLine: {
            show: false,
          },
          data: headDetail.app_traffic,
        },
      ],
    };
    myChart.setOption(option);
    setcolorList(myChart.getModel().option.color);
  };

  const applicationShowMore = (value: any) => {
    let startTime = moment().subtract(7, 'days').startOf('day').valueOf();
    let stopTime = moment().endOf('day').valueOf();
    switch (value) {
      case 'http':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=http&startTime=${startTime}&stopTime=${stopTime}&ip=${headDetail.ip_addr}`,
        );
        break;
      case 'https':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=ssl&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
      case 'dns':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=dns&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
      case 'ftp':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=ftp&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
      case 'smtp':
        history.push(`/app/mica/collect/logCollect?dpilogType=smtp&startTime=${startTime}&stopTime=${stopTime}`);
        break;
      case 'pop3':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=pop3&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
      case 'imap':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=mail&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
      case 'icmp':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=icmp&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
      case 'snmp':
        history.push(
          `/app/mica/collect/logCollect?dpilogType=snmp&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
      default:
        history.push(
          `/app/mica/collect/logCollect?dpilogType=conn&application=${value}&startTime=${startTime}&stopTime=${stopTime}}&ip=${headDetail.ip_addr}`,
        );
        break;
    }
  };
  /**
   * @name:
   * @description: 初始化发送流量图表
   * @param {*}
   * @return {*}
   */

  const initSendData = () => {
    const myChart = echarts.init(document.getElementById('sendData'));
    if (headDetail.send_traffic && headDetail.send_traffic.length) {
      headDetail.send_traffic.forEach((item: any, index: any) => {
        item.size = item.value;
        item.value = 5 - index;
      });

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params: any) {
            // 字节自动转单位
            let str = '';
            str = calculationByte(params.data.size);
            return `<div style='color:${params.color}'>${params.name} : <span style='margin-right:10px'>${str}</span></div>`;
          },
        },

        series: [
          {
            name: 'Expected',
            type: 'funnel',
            left: '5%',
            label: {
              formatter: function (params: any) {
                // 字节自动转单位
                let str: string = '';
                str = calculationByte(params.data.size);
                return str;
              },
            },
            data: headDetail.send_traffic,
          },
        ],
      };
      myChart.setOption(option);
      setsendColor(myChart.getModel().option.color);
    }
  };
  const weekChart = () => {
    const myChart = echarts.init(document.getElementById('week'));
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: weekData.map((item: any) => {
            return moment(item.time).format('YYYY-MM-DD HH:mm:ss');
          }),
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: function (params: any) {
              let str = weekData[0].unit;
              return `${params}${str}`;
            },
          },
        },
      ],
      series: [
        {
          // name: '直接访问',
          type: 'bar',
          barWidth: '60%',
          data: weekData.map((item: any) => {
            return item.total_bytes;
          }),
        },
      ],
    };
    myChart.setOption(option);
  };

  const sendShowMore = (value: any) => {
    let startTime = moment().subtract(7, 'days').startOf('day').valueOf();
    let stopTime = moment().endOf('day').valueOf();
    history.push(
      `/app/mica/collect/logCollect?dpilogType=conn&startTime=${startTime}&stopTime=${stopTime}&ip=${headDetail.ip_addr}&ip2=${value.name}`,
    );
  };

  // 近期流量访问抽样更多
  const flowShowMore = () => {
    let startTime = moment().valueOf();
    let stopTime = moment().subtract(1, 'days').valueOf();
    history.push(
      `/app/mica/collect/logCollect?dpilogType=conn&startTime=${startTime}&stopTime=${stopTime}&ip=${currentData.ip_addr}`,
    );
  };

  // 近一周流量趋势更多
  const weekShowMore = () => {
    let startTime = moment().subtract(7, 'days').startOf('day').valueOf();
    let stopTime = moment().endOf('day').valueOf();
    history.push(
      `/app/mica/collect/logCollect?dpilogType=conn&startTime=${startTime}&stopTime=${stopTime}&ip=${currentData.ip_addr}`,
    );
  };

  const tableClick = (value: any) => {
    let startTime = moment().subtract(2, 'days').startOf('day').valueOf();
    let stopTime = moment().endOf('day').valueOf();
    history.push(
      `/app/mica/collect/logCollect?dpilogType=conn&startTime=${startTime}&stopTime=${stopTime}&ip=${headDetail.ip_addr}&ip2=${value.ip}`,
    );
  };
  const getHeadInfo = (ip_addr: any) => {
    if (ip_addr) {
      meansList({ ip_addr: ip_addr }).then((res) => {
        if (res.flag && res?.data?.total > 0) {
          res.data.cols[0].app_traffic.forEach((item: any) => {
            item.str = calculationByte(item.value);
          });
          setCurrentData(res.data.cols[0]);

          res.data.cols[0].latest_week_stream.forEach((item: any) => {
            weekData.forEach((item1: any, index: number) => {
              if (item.time === item1.time) {
                weekData[index] = item;
              }
            });
          });
          setweekData([...weekData]);

          setTimeout(() => {
            getThreat_event(res.data.cols[0].ip_addr);
            let access_sample: any = [];
            Object.keys(res.data.cols[0].access_sample).forEach((key) => {
              access_sample.push({
                ip: key,
                port: res.data.cols[0].access_sample[key],
              });
            });
            settableData(access_sample);
          }, 100);

          setheadDetail(res.data.cols[0]);
        } else {
          setCurrentData({});
          setweekData(initWeekData.slice());
          settableData([]);
          setthreatData({});
          setheadDetail({});
        }
      });
    }
  };

  // 更多启用端口显示
  const morePort = () => {
    Modal.info({
      title: '启用的端口',
      width: '40%',
      okText: '关闭',
      content: (
        <Paragraph className={classnames(style.card_port, commomStyle.scrollY)}>
          {headDetail.enable_port &&
            Object.keys(headDetail.enable_port).map((item: any, index: number) => {
              if (headDetail.enable_port[item]) {
                const time = moment(headDetail.enable_port[item] * 1000).format('yyyy-MM-DD HH:mm:ss');
                const sub = moment().unix() - headDetail.enable_port[item];
                return (
                  <Tooltip
                    placement="topLeft"
                    title={`最新活跃时间：${time}`}
                    key={index}
                    className={sub < 24 * 60 * 60 ? style.port_active : style.port_inactive}
                  >
                    {item}
                  </Tooltip>
                );
              } else {
                return (
                  <Tooltip placement="topLeft" title={`最新活跃时间：- `} key={index} className={style.port_inactive}>
                    {item}
                  </Tooltip>
                );
              }
            })}
        </Paragraph>
      ),
      onOk() {},
    });
  };

  const [ipAddr, setIpAddr] = useState(getQueryVariable('ip_addr'));
  useEffect(() => {
    if (!ipAddr) return;
    getHeadInfo(ipAddr);
  }, []);

  useEffect(() => {
    initApplication();
    initSendData();
  }, [headDetail]);

  useEffect(() => {
    weekChart();
  }, [weekData]);

  return (
    <div className={style.meansBox}>
      <div className="bg-d0 px-4 py-2 mb-4">
        <div style={{ display: 'flex' }}>
          <Input
            style={{ width: '95%' }}
            onChange={(e) => {
              setIpAddr(e.target.value);
            }}
            defaultValue={ipAddr}
            addonBefore="资产IP"
          />
          <div
            style={{width: 50, height: 31, border: '1px solid #d9d9d9', backgroundColor: '#fafafa' }}
            onDoubleClick={() => {
              handleDoubleClick();
            }}
            onClick={() => {
              getHeadInfo(ipAddr);
            }}
          ></div>
          <SearchOutlined   onDoubleClick={() => {
              handleDoubleClick();
            }}
            onClick={() => {
              getHeadInfo(ipAddr);
            }} style={{ fontSize: 24, transform: 'translateX(-35px) translateY(4px)' }} />
        </div>
      </div>
      <div className={style.header}>
        <img className={style.computer} src={computer} />
        <div className={style.head_info}>
          <div className={style.top_info}>
            <span className={style.head_ip}>{headDetail.ip_addr}</span>
            {headDetail.labels
              ? headDetail.labels.map((item: any, index: any) => {
                  return (
                    <span key={index} className={style.labels}>
                      {item}
                    </span>
                  );
                })
              : null}
          </div>
          <div className={style.text_box}>
            <div className={style.head_text}>
              资产名：
              <Tooltip title={headDetail.name}>{ellipsis(headDetail.name, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              资产类型：
              <Tooltip title={headDetail.type}>{ellipsis(headDetail.type, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              所属组织：
              <Tooltip title={headDetail.organisation}>{ellipsis(headDetail.organisation, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              所属行业：
              <Tooltip title={headDetail.industry}>{ellipsis(headDetail.industry, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              所属国家：
              <Tooltip title={headDetail.country}>{ellipsis(headDetail.country, 3)}</Tooltip>
            </div>

            <div className={style.head_text}>
              地理位置：
              <Tooltip title={headDetail.location}>{ellipsis(headDetail.location, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>涉密资产：{headDetail.is_sercret ? '是' : '否'}</div>
            <div className={style.head_text}>重点资产：{headDetail.is_importent ? '是' : '否'}</div>
            {headDetail.create_time ? (
              <div className={style.head_text} style={{ width: 250 }}>
                资产创建时间：
                <Tooltip title={moment(headDetail.create_time).format('YYYY-MM-DD HH:mm:ss')}>
                  {moment(headDetail.create_time).format('YYYY-MM-DD HH:mm:ss')}
                </Tooltip>
              </div>
            ) : null}
          </div>
          <div className={style.text_box}>
            <div className={style.head_text}>
              资产编号：
              <Tooltip title={headDetail.assets_num}>{ellipsis(headDetail.assets_num, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              所属部门：
              <Tooltip title={headDetail.department}>{ellipsis(headDetail.department, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              责任人：
              <Tooltip title={headDetail.person}>{ellipsis(headDetail.person, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              操作系统：
              <Tooltip title={headDetail.os_info}>{ellipsis(headDetail.os_info, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              组件列表：
              <Tooltip title={headDetail.components}>{ellipsis(headDetail.components, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              域名：
              <Tooltip title={headDetail.domain}>{ellipsis(headDetail.domain, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              MAC地址：
              <Tooltip title={headDetail.mac_addr}>{ellipsis(headDetail.mac_addr, 3)}</Tooltip>
            </div>
            <div className={style.head_text}>
              其他：
              <Tooltip title={headDetail.other}>{ellipsis(headDetail.other, 3)}</Tooltip>
            </div>
            {headDetail.update_time ? (
              <div className={style.head_text} style={{ width: 250 }}>
                最近更新时间：
                <Tooltip title={moment(headDetail.update_time).format('YYYY-MM-DD HH:mm:ss')}>
                  {moment(headDetail.update_time).format('YYYY-MM-DD HH:mm:ss')}
                </Tooltip>
              </div>
            ) : null}
          </div>
        </div>
        <div className={style.head_btn}>
          <Button style={{ display: 'block' }} type="link" disabled={!headDetail?.ip_addr} onClick={handleEdit}>
            <span style={{ textDecoration: 'underline' }}>编辑资产信息</span>
          </Button>
          <Button style={{ display: 'block' }} type="link" onClick={showMore}>
            <span style={{ textDecoration: 'underline' }}>查看更多资产</span>
          </Button>
        </div>
      </div>
      <div className={style.cards_box}>
        <Card bordered={false} className={style.card}>
          <p className={style.card_title}>连接统计</p>
          <div className={style.charts_box}>
            <div className={style.translate}>
              <p>
                发起的连接数： <span className={style.purple}>{headDetail.send_count}</span>
              </p>
              <p>
                接收的连接数： <span className={style.purple}>{headDetail.accept_count}</span>
              </p>
            </div>
          </div>
          <div className={style.ports}>
            <p className={style.card_title}>启用的端口</p>
            <Paragraph className={style.ports_con}>
              {headDetail.enable_port &&
                Object.keys(headDetail.enable_port).map((item: any, index: number) => {
                  if (headDetail.enable_port[item]) {
                    const time = moment(headDetail.enable_port[item] * 1000).format('yyyy-MM-DD HH:mm:ss');
                    const sub = moment().unix() - headDetail.enable_port[item];
                    return (
                      <Tooltip
                        placement="topLeft"
                        title={`最新活跃时间：${time}`}
                        key={index}
                        className={sub < 24 * 60 * 60 ? style.port_active : style.port_inactive}
                      >
                        {item}
                      </Tooltip>
                    );
                  } else {
                    return (
                      <Tooltip placement="topLeft" title={`最新活跃时间：-`} key={index} className={style.port_inactive}>
                        {item}
                      </Tooltip>
                    );
                  }
                })}
            </Paragraph>
            {headDetail.enable_port && Object.keys(headDetail.enable_port).length >= 13 && (
              <p className={style.morePort} onClick={morePort}>
                更多启用端口&gt;&gt;
              </p>
            )}
          </div>
        </Card>
        <Card bordered={false} className={style.card}>
          <p className={style.card_title}>应用流量TOP10</p>
          {!(headDetail.app_traffic && headDetail.app_traffic.length) ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ marginTop: '80px' }} />
          ) : null}
          <div className={style.charts_box}>
            <div
              className={style.applicationBox}
              id="application"
              style={{ display: headDetail.app_traffic && headDetail.app_traffic.length ? 'block' : 'none' }}
            ></div>
            {headDetail.app_traffic && headDetail.app_traffic.length ? (
              <div className={style.application}>
                {headDetail.app_traffic.map((item: any, index: any) => {
                  return (
                    <div key={index} className={style.application_row}>
                      <div
                        style={{
                          background: index >= 9 ? colorList[index - 9] : colorList[index],
                        }}
                        className={style.circular}
                      ></div>
                      <div className={style.legendCon}>
                        <Paragraph ellipsis={{ tooltip: item.name }} className={style.legend_name_10}>
                          {item.name}
                        </Paragraph>
                        <span>{item.str}</span>
                      </div>
                      <span
                        onClick={() => {
                          applicationShowMore(item.name);
                        }}
                        className={style.pointer}
                      >
                        more&gt;&gt;
                      </span>
                    </div>
                  );
                })}
              </div>
            ) : null}
          </div>
          <div style={{ fontSize: 12, paddingTop: 45 }}>
            总流量：
            <span className={style.blue}>{calculationByte(headDetail.app_total_traffic)}</span>
            {headDetail.create_time ? (
              <span>
                统计时间范围：
                <span style={{ color: '#8080ff' }}>
                  {moment(headDetail.create_time - 1296000000).format('YYYY-MM-DD HH:mm:ss')}~
                  {moment(headDetail.update_time).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </span>
            ) : null}
          </div>
        </Card>
        <Card bordered={false} className={style.card}>
          <p className={style.card_title}>发送流量TOP5</p>
          {!(headDetail.send_traffic && headDetail.send_traffic.length) ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ marginTop: '80px' }} />
          ) : null}
          <div className={style.charts_box}>
            <div
              className={style.send_box}
              id="sendData"
              style={{ display: headDetail.send_traffic && headDetail.send_traffic.length ? 'block' : 'none' }}
            ></div>
            <div className={style.send_legend}>
              {headDetail.send_traffic && headDetail.send_traffic.length
                ? headDetail.send_traffic.map((item: any, index: any) => {
                    return (
                      <div key={index} className={style.application_row}>
                        <div
                          style={{
                            background: sendColor[index],
                          }}
                          className={style.square}
                        ></div>
                        <div className={style.legendCon}>
                          <Paragraph ellipsis={{ tooltip: item.name }} className={style.legend_name_5}>
                            {item.name}
                          </Paragraph>
                          <span>{item.str}</span>
                        </div>
                        <span
                          onClick={() => {
                            sendShowMore(item);
                          }}
                          className={style.pointer}
                        >
                          more&gt;&gt;
                        </span>
                      </div>
                    );
                  })
                : null}
            </div>
          </div>
        </Card>
      </div>
      <div className={style.bottom_charts}>
        <Card bordered={false} bodyStyle={{ padding: '24px 10px 10px 24px' }} className={style.card}>
          <p className={style.card_title}>威胁事件历史记录</p>
          {/* {threatData} */}
          <div className={classnames(style.threat_box, commomStyle.scrollY)}>
            <Timeline>
              {threatData
                ? Object.keys(threatData).map((item) => {
                    return (
                      <Timeline.Item key={item}>
                        <div className={style.timeline_box}>
                          <div className={style.threat_ip}>{item}</div>
                          <div
                            className={style.threat_item}
                            style={{
                              transform: 'translateY(-40px)',
                            }}
                          >
                            {threatData[item].map((ele: any, index: any) => {
                              return (
                                <div
                                  key={index}
                                  style={index !== threatData[item].length - 1 || index !== 0 ? { margin: '10px 0' } : null}
                                  className={`${style.detail_item}`}
                                >
                                  <div className={style.time_count}>
                                    <span className={style.count_color}>{ele.count}</span>
                                    <span style={{ paddingLeft: 4 }}>次</span>
                                  </div>
                                  <div className={style.time_content}>
                                    <Paragraph
                                      style={{ margin: 0, color: '#8080ff' }}
                                      ellipsis={{ tooltip: ele.name, rows: 2 }}
                                    >
                                      {ele.name}
                                    </Paragraph>
                                    <p className={style.time_content_item}>
                                      <span
                                        style={{
                                          color: '#3399ff',
                                          paddingLeft: 2,
                                        }}
                                      >
                                        威胁事件:
                                        <Tooltip title={ele.threat_event}>{ellipsis(ele.threat_event, 8)}</Tooltip>
                                      </span>
                                      <span
                                        style={{
                                          paddingLeft: 8,
                                          color: threatLevelColorMap[ele.threat_level],
                                        }}
                                      >
                                        等级:
                                        {ele.threat_level === 'High' ? '高危' : null}
                                        {ele.threat_level === 'Medium' ? '中危' : null}
                                        {ele.threat_level === 'Low' ? '低危' : null}
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </Timeline.Item>
                    );
                  })
                : null}
            </Timeline>
          </div>
        </Card>
        <Card bordered={false} className={style.card}>
          <div className={`${style.card_title} ${style.flex_between}`}>
            <span>近一周流量趋势</span>
            <span onClick={weekShowMore} className={style.pointer} style={{ fontWeight: 'normal', fontSize: 14 }}>
              查看更多&gt;&gt;
            </span>
          </div>
          <div className={style.week_chart_box} id="week"></div>
        </Card>
        <Card bordered={false} className={style.card}>
          <div className={`${style.card_title} ${style.flex_between}`}>
            <span>近期流量访问抽样</span>
            <span onClick={flowShowMore} className={style.pointer} style={{ fontWeight: 'normal', fontSize: 14 }}>
              查看更多&gt;&gt;
            </span>
          </div>
          <Table
            style={{ marginTop: 20 }}
            pagination={false}
            onRow={(record) => {
              return {
                onClick: () => {
                  tableClick(record);
                },
              };
            }}
            rowKey="ip"
            columns={columns}
            dataSource={tableData}
          />
        </Card>
      </div>
      <Modal
        width={1200}
        destroyOnClose={true}
        onOk={handleOk}
        onCancel={() => {
          setvisible(false);
        }}
        visible={visible}
        title={'编辑'}
      >
        <MeansForm value={editValue} ref={childForm} />
      </Modal>
      <Modal
        width={800}
        destroyOnClose={true}
        onOk={() => {
          setdetailVisible(false);
        }}
        onCancel={() => {
          setdetailVisible(false);
        }}
        visible={detailVisible}
        title={'资产备案信息查询'}
      >
        <Input.Search allowClear enterButton="搜索" size="large" onSearch={onSearchInfo} />
        <div style={{ minHeight: 200, paddingTop: 40 }}>
          <Spin spinning={loading}>
            <p>{infoData.ip}</p>
            <p>{infoData.ipc}</p>
          </Spin>
        </div>
      </Modal>
    </div>
  );
};
export default Means;
