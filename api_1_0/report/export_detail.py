#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import time
from flask import send_file, make_response
import pandas as pd
from flask import request
from utils.ndr_base import NdrResource
from utils import param_check
from utils.param_check import Validator
from utils.utils import flask_response
from utils.logger import get_ndr_logger

API_LOG = get_ndr_logger('api_log', __file__)


class DetailExport(NdrResource):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.excel_file_path = ''
        self.export_data = []

    @param_check.check_flask_args(Validator('export_detail'), request)
    def post(self, **kwargs):
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        self.excel_file_path = '/tmp/detail_report_%s.xlsx' % time_str
        try:
            self.export_data = [kwargs["data"]]
            self.export_excel()
        except Exception as e:
            err_info = "error: " + str(e)
            return flask_response(err_info, False, {})
        return make_response(send_file(self.excel_file_path, as_attachment=True))

    def export_excel(self):
        data = pd.DataFrame(self.export_data)
        writer = pd.ExcelWriter(self.excel_file_path)  # 写入Excel文件
        data.to_excel(writer)
        writer.save()
