# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : time_series_detector.py
# @Software: PyCharm

"""时间序列趋势异常检测模型"""

import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any
from .base_detector import BaseDetector
from utils.logger import get_ndr_logger
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class TimeSeriesDetector(BaseDetector):
    """时间序列趋势异常检测器
    
    利用平均值、标准差、偏离度判断流量/连接数异常，基于Z-score识别统计异常
    比如某IP在某时段流量激增
    """
    
    def __init__(self, task_config: Dict[str, Any]):
        super().__init__(task_config)
        self.z_score_threshold = self.detection_config.get('z_score_threshold', 3.0)
        self.time_window_hours = self.detection_config.get('time_window_hours', 1)
        self.baseline_days = self.detection_config.get('baseline_days', 7)
        self.metric_type = self.detection_config.get('metric_type', 'bytes')  # bytes, packets, connections
    
    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """执行时间序列异常检测"""
        try:
            results = []
            
            # 获取基线数据（过去N天的同时段数据）
            baseline_data = self._get_baseline_data(start_time, end_time)
            if not baseline_data:
                LOG.warning(f"无法获取基线数据: {self.target_value}")
                return results
            
            # 获取当前时段数据
            current_data = self._get_current_data(start_time, end_time)
            if not current_data:
                LOG.info(f"当前时段无数据: {self.target_value}")
                return results
            
            # 计算基线统计信息
            baseline_stats = self._calculate_baseline_stats(baseline_data)
            
            # 计算当前时段统计信息
            current_stats = self._calculate_current_stats(current_data)
            
            # 执行异常检测
            anomaly_result = self._detect_anomaly(baseline_stats, current_stats, start_time, end_time)
            if anomaly_result:
                results.append(anomaly_result)
            
            return results
            
        except Exception as e:
            LOG.error(f"时间序列异常检测失败: {str(e)}")
            return []
    
    def _get_baseline_data(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取基线数据"""
        baseline_data = []
        
        for i in range(1, self.baseline_days + 1):
            baseline_start = start_time - timedelta(days=i)
            baseline_end = end_time - timedelta(days=i)
            
            data = self.get_historical_data('dpilog_conn', baseline_start, baseline_end)
            baseline_data.extend(data)
        
        return baseline_data
    
    def _get_current_data(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取当前时段数据"""
        return self.get_historical_data('dpilog_conn', start_time, end_time)
    
    def _calculate_baseline_stats(self, baseline_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算基线统计信息"""
        if not baseline_data:
            return {}
        
        # 按小时聚合数据
        hourly_stats = {}
        for record in baseline_data:
            hour = record['ts'].hour
            if hour not in hourly_stats:
                hourly_stats[hour] = {'bytes': 0, 'packets': 0, 'connections': 0}
            
            hourly_stats[hour]['bytes'] += record.get('src_bytes', 0) + record.get('dst_bytes', 0)
            hourly_stats[hour]['packets'] += record.get('src_pkts', 0) + record.get('dst_pkts', 0)
            hourly_stats[hour]['connections'] += 1
        
        # 计算统计指标
        metric_values = []
        for hour_data in hourly_stats.values():
            metric_values.append(hour_data[self.metric_type])
        
        if not metric_values:
            return {}
        
        return {
            'mean': statistics.mean(metric_values),
            'std': statistics.stdev(metric_values) if len(metric_values) > 1 else 0,
            'median': statistics.median(metric_values),
            'max': max(metric_values),
            'min': min(metric_values),
            'count': len(metric_values)
        }
    
    def _calculate_current_stats(self, current_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算当前时段统计信息"""
        if not current_data:
            return {}
        
        total_bytes = sum(record.get('src_bytes', 0) + record.get('dst_bytes', 0) for record in current_data)
        total_packets = sum(record.get('src_pkts', 0) + record.get('dst_pkts', 0) for record in current_data)
        total_connections = len(current_data)
        
        current_value = {
            'bytes': total_bytes,
            'packets': total_packets,
            'connections': total_connections
        }[self.metric_type]
        
        return {
            'value': current_value,
            'bytes': total_bytes,
            'packets': total_packets,
            'connections': total_connections
        }
    
    def _detect_anomaly(self, baseline_stats: Dict[str, float], current_stats: Dict[str, float], 
                       start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """检测异常"""
        if not baseline_stats or not current_stats:
            return None
        
        current_value = current_stats['value']
        baseline_mean = baseline_stats['mean']
        baseline_std = baseline_stats['std']
        
        # 计算Z-score
        z_score = self.calculate_z_score(current_value, baseline_mean, baseline_std)
        
        # 判断是否异常
        if z_score >= self.z_score_threshold:
            # 计算异常分数 (0-1)
            anomaly_score = min(z_score / 10.0, 1.0)
            
            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)
            
            # 计算变化率
            change_rate = ((current_value - baseline_mean) / baseline_mean * 100) if baseline_mean > 0 else 0
            
            return {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'z_score': z_score,
                'current_value': current_value,
                'baseline_mean': baseline_mean,
                'baseline_std': baseline_std,
                'change_rate': change_rate,
                'metric_type': self.metric_type,
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"{self.target_value}在{self.metric_type}指标上出现异常，"
                             f"当前值{current_value:.2f}，基线均值{baseline_mean:.2f}，"
                             f"Z-score={z_score:.2f}，变化率{change_rate:.1f}%",
                'details': {
                    'current_stats': current_stats,
                    'baseline_stats': baseline_stats,
                    'detection_config': self.detection_config
                }
            }
        
        return None
