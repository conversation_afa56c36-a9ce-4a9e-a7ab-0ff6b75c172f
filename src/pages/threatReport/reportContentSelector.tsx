import { reportContentOptions } from "@/utils/enumList"
import Checkbox, { CheckboxChangeEvent } from "antd/lib/checkbox"
import React, { useEffect, useState } from "react"
import CheckboxWithSelect from "./chekcboxWithSelect"
import { Row } from "antd"

export type ReportContent = {
  suggest: boolean;
  high_risk: number[];
  risk: number[];
  stat: boolean;
  overview: boolean;
}

interface ReportContentSelectProps {
  value?: ReportContent;
  onChange?: (value: ReportContent) => void;
};

const ReportContentSelect = ({ value, onChange }: ReportContentSelectProps) => {
  const [reportContent, setReportContent] = useState<ReportContent>(value as ReportContent)

  const onRiskDetailChange = (value: string, key: string) => {
    setReportContent(prev => {
      const newValue = {
        ...prev,
        [key]: value
      }
      return newValue;
    })
  };

  const onCheckboxChange = ({ target: { name, checked } }: CheckboxChangeEvent) => {
    setReportContent((prev) => {
      const newValue = {
        ...prev,
        [name!]: checked
      }
      return newValue;
    })
  };

  useEffect(() => {
    onChange?.(reportContent);
  }, [reportContent]);

  return (
    <div>
      {
        reportContentOptions.map(({ label, value }) => {
          if (['risk', 'high_risk'].includes(value)) {
            return (
              <CheckboxWithSelect
                key={value}
                label={label}
                value={reportContent[value]}
                onChange={(v) => onRiskDetailChange(v, value)}
              />
            )
          }
          return (
            <Row key={value}>
              <Checkbox
                name={value}
                checked={reportContent[value]}
                onChange={onCheckboxChange}
              >
                {label}
              </Checkbox>
            </Row>
          )
        })
      }
    </div >
  )
}

export default ReportContentSelect