import { Collapse, Table, Tree } from 'antd';
import React, { useEffect, useState } from 'react';
import s from './style.less';

const { Panel } = Collapse;
interface PartProps {
  info: any;
}
const StaticInfo = ({ info = {} }: PartProps) => {
  const [treeData, setTreeData] = useState([]);
  const columns1 = [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '虚拟地址',
      dataIndex: 'virtual_address',
    },
    {
      title: '真实长度',
      dataIndex: 'virtual_size',
    },
    {
      title: '物理长度',
      dataIndex: 'size_of_data',
    },
    {
      title: '熵',
      dataIndex: 'entropy',
    },
  ];
  const columns2 = [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '文件类型',
      dataIndex: 'filetype',
    },
    {
      title: '大小',
      dataIndex: 'size',
    },
    {
      title: '语言',
      dataIndex: 'language',
    },
    {
      title: '子语言',
      dataIndex: 'sublanguage',
    },
    {
      title: '文件偏移地址',
      dataIndex: 'offset',
    },
  ];
  const columns3 = [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '值',
      width: '70%',
      dataIndex: 'value',
    },
  ];
  const handleTree = () => {
    const tData = info.pe_imports.map((e, i) => {
      return {
        title: e.dll,
        key: i,
        children: e.imports.length
          ? e.imports.map((child, ci) => {
              return {
                title: child.name,
                key: `${i }-${ ci}`,
              };
            })
          : [],
      };
    });
    setTreeData(tData);
  };
  useEffect(() => {
    info.pe_imports&&handleTree();
  }, [info]);
  return (
    <div className={s.staticBox}>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="文件头" key="1">
          <div className='p-[0_1.5rem]'>
            <p>
              <span>时间</span>
              <span>{info.pe_timestamp}</span>
            </p>
            <p>
              <span>PDB路径</span>
              <span>{info.pdb_path}</span>
            </p>
          </div>
        </Panel>
        <Panel header="PE节表信息" key="2">
        <div className='p-[0_1.5rem]'>
            <Table
              className={s.table}
              bordered
              scroll={{ y: 300 }}
              size="middle"
              columns={columns1}
              dataSource={info.pe_sections}
              pagination={false}
            />
          </div>
        </Panel>
        <Panel header="PE资源信息" key="3">
        <div className='p-[0_1.5rem]'>
            <Table
              className={s.table}
              bordered
              scroll={{ y: 300 }}
              size="middle"
              columns={columns2}
              dataSource={info.pe_resources}
              pagination={false}
            />
          </div>
        </Panel>
        <Panel header="PE版本信息" key="4">
        <div className='p-[0_1.5rem]'>
            <Table
              className={s.table}
              bordered
              size="middle"
              scroll={{ y: 300 }}
              columns={columns3}
              dataSource={info.pe_versioninfo}
              pagination={false}
            />
          </div>
        </Panel>
        <Panel header="PE导入表" key="5">
        <div className='p-[0_1.5rem]'>
            <Tree showLine={true} showIcon={true} defaultExpandedKeys={['0-0-0']} treeData={treeData} />
          </div>
        </Panel>
      </Collapse>
    </div>
  );
};

export default StaticInfo;
