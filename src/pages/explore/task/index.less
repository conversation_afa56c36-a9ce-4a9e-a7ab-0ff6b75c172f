.threatFlag {
  width: 23px;
  height: 23px;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

.center_text {
  text-align: center;
}

.img_box {
  display: flex;
}

.table_top {
  width: 100%;
  height: 32px;
  padding-left: 16px;
  line-height: 32px;
  background: #e9edf0;
  border-bottom: 1px solid #e8e8e8;
}

.sortBox {
  margin: 0 10px;
}

.drawer {
  position: absolute;
}

.contentBox {
  position: relative;
  min-height: 830px;
  overflow: hidden;
}

.iconFont {
  color: #acacac;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
}

.target {
  text-align: left;
}

.slider {
  width: 167px;
}

.btn {
  flex: 1;
  text-align: right;
}

.score {
  font-size: 18px;
}

.last {
  display: inline;
  margin-left: 10px;
}

.log {
  display: flex;
  padding: 10px 30px;
  border-bottom: 1px dotted #ccc;
  justify-content: center;
  align-items: center;

  .logRes {
    font-weight: 700;
  }
}

.button {
  margin: 0 3px;
  padding: 0 3px;
  font-weight: 500;
  border-left: 1px solid #aaa;
  cursor: pointer;
  transition: all 0.25s ease;

  &:hover {
    color: #ff8005;
  }

  &.active {
    color: #ff8005;
    border-bottom: 2px solid #ff8005;
  }
}

.allpacList {
  // margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btnGroup {
  display: flex;
  justify-content: center;
}

.determine {
  margin-left: 10px;
}

.pacp_box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.detail_box {
  display: flex;
}

.detail_pacp {
  padding-right: 10px;
  width: 25%;
  border-right: 1px solid;
}

.detail_other {
  width: 74%;
  padding-left: 10px;
}

.pacp_item {
  height: 46px;
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  font-weight: 700;
  border-bottom: 1px dotted #ccc;
}

:global {
  .ant-form-item {
    margin-bottom: 0;
  }
}
