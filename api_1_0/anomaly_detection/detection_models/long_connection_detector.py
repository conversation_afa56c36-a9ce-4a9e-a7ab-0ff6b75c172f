# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : long_connection_detector.py
# @Software: PyCharm

"""长连接异常检测模型"""

import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any
from .base_detector import BaseDetector
from utils.logger import get_ndr_logger
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class LongConnectionDetector(BaseDetector):
    """长连接异常检测器
    
    分析是否有异常时长连接
    比如HTTP长连接超10分钟
    """
    
    def __init__(self, task_config: Dict[str, Any]):
        super().__init__(task_config)
        # 不同协议的异常时长阈值（秒）
        self.duration_thresholds = self.detection_config.get('duration_thresholds', {
            'http': 600,    # HTTP连接超过10分钟
            'https': 1800,  # HTTPS连接超过30分钟
            'tcp': 3600,    # TCP连接超过1小时
            'dns': 30,      # DNS连接超过30秒
            'default': 1800 # 默认30分钟
        })
        self.min_bytes_threshold = self.detection_config.get('min_bytes_threshold', 1024)  # 最小字节数
        self.percentile_threshold = self.detection_config.get('percentile_threshold', 95)  # 百分位阈值
    
    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """执行长连接异常检测"""
        try:
            results = []
            
            # 获取连接数据
            conn_data = self.get_historical_data('dpilog_conn', start_time, end_time)
            if not conn_data:
                LOG.info(f"当前时段无连接数据: {self.target_value}")
                return results
            
            # 过滤有效连接（有持续时间的连接）
            valid_connections = [
                conn for conn in conn_data 
                if conn.get('duration', 0) > 0
            ]
            
            if not valid_connections:
                return results
            
            # 按协议分组检测
            protocol_groups = self._group_by_protocol(valid_connections)
            
            for protocol, connections in protocol_groups.items():
                protocol_results = self._detect_protocol_long_connections(
                    protocol, connections, start_time, end_time
                )
                results.extend(protocol_results)
            
            # 检测异常持久连接模式
            persistent_results = self._detect_persistent_patterns(
                valid_connections, start_time, end_time
            )
            results.extend(persistent_results)
            
            return results
            
        except Exception as e:
            LOG.error(f"长连接异常检测失败: {str(e)}")
            return []
    
    def _group_by_protocol(self, connections: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按协议分组连接"""
        protocol_groups = {}
        
        for conn in connections:
            protocol = conn.get('app_proto', conn.get('proto', 'unknown')).lower()
            if protocol not in protocol_groups:
                protocol_groups[protocol] = []
            protocol_groups[protocol].append(conn)
        
        return protocol_groups
    
    def _detect_protocol_long_connections(self, protocol: str, connections: List[Dict[str, Any]],
                                        start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测特定协议的长连接异常"""
        results = []
        
        # 获取协议阈值
        threshold = self.duration_thresholds.get(protocol, self.duration_thresholds['default'])
        
        # 计算连接时长统计
        durations = [conn['duration'] for conn in connections]
        if not durations:
            return results
        
        # 计算统计信息
        mean_duration = statistics.mean(durations)
        median_duration = statistics.median(durations)
        max_duration = max(durations)
        
        # 计算百分位数
        durations_sorted = sorted(durations)
        percentile_index = int(len(durations_sorted) * self.percentile_threshold / 100)
        percentile_duration = durations_sorted[percentile_index] if percentile_index < len(durations_sorted) else max_duration
        
        # 检测异常长连接
        long_connections = [
            conn for conn in connections 
            if conn['duration'] >= threshold or conn['duration'] >= percentile_duration
        ]
        
        if long_connections:
            # 按连接时长排序，取最长的几个
            long_connections.sort(key=lambda x: x['duration'], reverse=True)
            top_long_connections = long_connections[:10]
            
            for conn in top_long_connections:
                anomaly_result = self._create_long_connection_anomaly(
                    protocol, conn, threshold, percentile_duration,
                    mean_duration, start_time, end_time
                )
                if anomaly_result:
                    results.append(anomaly_result)
        
        return results
    
    def _detect_persistent_patterns(self, connections: List[Dict[str, Any]],
                                  start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测持久连接模式"""
        results = []
        
        # 按源IP-目标IP对分组
        ip_pairs = {}
        for conn in connections:
            src_ip = conn.get('src_ip', '')
            dst_ip = conn.get('dst_ip', '')
            if src_ip and dst_ip:
                pair_key = f"{src_ip}->{dst_ip}"
                if pair_key not in ip_pairs:
                    ip_pairs[pair_key] = []
                ip_pairs[pair_key].append(conn)
        
        # 检测每个IP对的持久连接模式
        for pair_key, pair_connections in ip_pairs.items():
            if len(pair_connections) >= 3:  # 至少3个连接才考虑持久模式
                persistent_result = self._analyze_persistent_pattern(
                    pair_key, pair_connections, start_time, end_time
                )
                if persistent_result:
                    results.append(persistent_result)
        
        return results
    
    def _create_long_connection_anomaly(self, protocol: str, connection: Dict[str, Any],
                                      threshold: float, percentile_duration: float,
                                      mean_duration: float, start_time: datetime, 
                                      end_time: datetime) -> Dict[str, Any]:
        """创建长连接异常结果"""
        duration = connection['duration']
        
        # 计算异常分数
        if duration >= threshold:
            anomaly_score = min(duration / (threshold * 2), 1.0)
        else:
            anomaly_score = min(duration / (percentile_duration * 2), 1.0)
        
        # 确定严重程度
        severity = self.get_severity_level(anomaly_score)
        
        # 计算数据传输量
        total_bytes = connection.get('src_bytes', 0) + connection.get('dst_bytes', 0)
        
        return {
            'anomaly_score': anomaly_score,
            'severity': severity,
            'connection_type': 'long_connection',
            'protocol': protocol,
            'duration': duration,
            'duration_minutes': duration / 60,
            'threshold': threshold,
            'percentile_duration': percentile_duration,
            'mean_duration': mean_duration,
            'total_bytes': total_bytes,
            'src_ip': connection.get('src_ip', ''),
            'dst_ip': connection.get('dst_ip', ''),
            'src_port': connection.get('src_port', 0),
            'dst_port': connection.get('dst_port', 0),
            'conn_id': connection.get('conn_id', ''),
            'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
            'description': f"检测到{protocol}协议长连接异常：{connection.get('src_ip', '')} -> "
                         f"{connection.get('dst_ip', '')}:{connection.get('dst_port', '')}，"
                         f"连接时长{duration:.1f}秒（{duration/60:.1f}分钟），超过阈值{threshold}秒",
            'details': {
                'connection_info': connection,
                'detection_config': self.detection_config
            }
        }
    
    def _analyze_persistent_pattern(self, pair_key: str, connections: List[Dict[str, Any]],
                                  start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """分析持久连接模式"""
        # 计算总连接时长
        total_duration = sum(conn.get('duration', 0) for conn in connections)
        
        # 计算连接间隔
        timestamps = sorted([conn.get('ts') for conn in connections if conn.get('ts')])
        if len(timestamps) < 2:
            return None
        
        # 计算时间跨度
        time_span = (timestamps[-1] - timestamps[0]).total_seconds()
        
        # 计算平均连接间隔
        intervals = []
        for i in range(1, len(timestamps)):
            interval = (timestamps[i] - timestamps[i-1]).total_seconds()
            intervals.append(interval)
        
        avg_interval = statistics.mean(intervals) if intervals else 0
        
        # 判断是否为持久连接模式
        # 条件：连接数量多、总时长长、间隔相对规律
        connection_count = len(connections)
        if (connection_count >= 5 and 
            total_duration >= 1800 and  # 总时长超过30分钟
            time_span >= 3600):  # 时间跨度超过1小时
            
            # 计算异常分数
            anomaly_score = min((connection_count * total_duration) / 100000, 1.0)
            
            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)
            
            # 计算总数据传输量
            total_bytes = sum(
                conn.get('src_bytes', 0) + conn.get('dst_bytes', 0) 
                for conn in connections
            )
            
            src_ip, dst_ip = pair_key.split('->')
            
            return {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'connection_type': 'persistent_pattern',
                'src_ip': src_ip,
                'dst_ip': dst_ip,
                'connection_count': connection_count,
                'total_duration': total_duration,
                'total_duration_hours': total_duration / 3600,
                'time_span_hours': time_span / 3600,
                'avg_interval_minutes': avg_interval / 60,
                'total_bytes': total_bytes,
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"检测到持久连接模式：{src_ip} -> {dst_ip}，"
                             f"在{time_span/3600:.1f}小时内建立{connection_count}个连接，"
                             f"总时长{total_duration/3600:.1f}小时",
                'details': {
                    'connections_sample': connections[:5],  # 保存前5个连接作为样本
                    'intervals': intervals[:10],  # 保存前10个间隔
                    'detection_config': self.detection_config
                }
            }
        
        return None
