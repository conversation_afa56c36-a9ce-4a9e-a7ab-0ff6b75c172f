import React, { useState } from 'react';
import { Button, Modal, Table } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useIgnoreList } from '../hooks';

export type Props = {
  show: boolean;
  onClose: () => void;
  onDelete: () => void;
  style?: React.CSSProperties;
  className?: string;
}

const { confirm } = Modal;

function IgonreListModal({ show, onClose, onDelete }: Props): JSX.Element {

  const {
    loading,
    list,
    total,
    pagination,
    setPagination,
    ignoreItemsDelete
  } = useIgnoreList()

  const [selectedRowKeys, setselectedRowKeys] = useState([]);

  const showDeleteConfirm = (id?: string) => {
    confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除吗？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => handleDelete(id),
    });
  };

  const handleDelete = (id?: string) => {
    return Promise.resolve().then(() => {
      if (id) {
        return ignoreItemsDelete([id]);
      }
      return ignoreItemsDelete(selectedRowKeys);
    }).then(() => {
      setselectedRowKeys([])
      onDelete()
    })
  }

  const groupColumns = [
    {
      title: '已忽略主机 IP',
      dataIndex: 'ip',
    },
    {
      title: '添加日期',
      dataIndex: 'create',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      render: (_: any, { _id }: any) => {
        return (
          <>
            <Button
              type="link"
              danger
              onClick={() => showDeleteConfirm(_id)}
            >
              删除
            </Button>
          </>
        );
      },
    },
  ];

  const handleOnPageChange = (page: number, pageSize: number) => {
    setPagination({
      page,
      pageSize,
    })
  }

  const paginationData = {
    total,
    showSizeChanger: true,
    showQuickJumper: true,
    current: pagination.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: pagination.pageSize,
    onChange: handleOnPageChange,
  };

  const rowSelection = {
    fixed: true,
    columnWidth: 60,
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys,
  };

  return (
    <Modal
      visible={show}
      width={600}
      title="忽略列表"
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
      onCancel={onClose}
    >
      <div className='flex gap-3'>
        <Button
          type="primary"
          disabled={!selectedRowKeys.length}
          danger
          onClick={() => showDeleteConfirm()}
        >
          删除
        </Button>
        {
          !!selectedRowKeys.length && (
            <span className='flex items-center'>
              已经选择 {selectedRowKeys.length} 条数据
            </span>
          )
        }
      </div>
      <Table
        loading={loading}
        className="mt-4"
        size="small"
        rowKey="_id"
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        scroll={{ y: 500 }}
        pagination={paginationData}
        dataSource={list}
        columns={groupColumns}
      />
    </Modal>
  );
}

export default IgonreListModal;
