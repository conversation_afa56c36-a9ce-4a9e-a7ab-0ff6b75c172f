import React from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import styles from './index.less';
import SearchTable, {
  TSearchTableProps,
  IFormConfig as ISearchFormConfig,
} from '../SearchTable';

interface Props extends Omit<TSearchTableProps, 'form'> {
  pageHeaderRender?: (props: any) => React.ReactNode;
  children: React.ReactNode;
}

export type FormConfig = ISearchFormConfig;

/**
 * 生成一个统一的管理页面，通过传入的参数对生成的页面进行定义
 */
export default function BaseManagePage(props: Props) {
  return (
    <PageHeaderWrapper pageHeaderRender={props.pageHeaderRender}>
      <div className={styles.cardBody}>
        <SearchTable {...props} />
      </div>
    </PageHeaderWrapper>
  );
}
