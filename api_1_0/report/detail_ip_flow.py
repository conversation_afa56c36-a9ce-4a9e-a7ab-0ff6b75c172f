#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils import param_check
from flask import request
from utils.es_function import get_range
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from utils.es_function import get_country_term
from config.config import RULE_INDEX
from utils.best_show_size import size_format
from utils.ip_judgment import is_internal_ip


ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class IpSum(NdrResource):
    '''基于IP地址流量统计'''

    field_template = {
        "occurred": "occurredTime",
        "observed": "observedTime"
    }

    def __init__(self):
        super(IpSum, self).__init__(es_template="ip_flow")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        # 开始时间&结束时间校验
        response = self.time_verify(**kwargs)
        if response is not True:
            return response

        start_time = kwargs["startTime"]
        stop_time = kwargs["stopTime"]
        time_mode = kwargs["timeMode"]
        country = kwargs["country"]
        time_mode = self.field_template[time_mode]
        ranges = get_range(time_mode, start_time, stop_time)
        self.es_template["query"]["bool"]["must"].append(ranges)
        if country:
            country_term = get_country_term(country)
            self.es_template["query"]["bool"]["must"].append(country_term)

        try:
            res = self.es_client.search(index=RULE_INDEX, body=self.es_template)
        except Exception as e:
            err_info = "error: " + str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(err_info, False, [])

        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [])

    @staticmethod
    def data_format(data):

        aggs_data = data["aggregations"]
        total_flow = aggs_data["total_flow"]["value"]
        internal_hosts = 0
        external_hosts = 0
        all_ip_buckets = aggs_data["agg_attack"]["buckets"] + aggs_data["agg_victim"]["buckets"]

        # 统计内网，外网ip数
        ip_list = list(set([i["key"] for i in all_ip_buckets]))
        for ip in ip_list:
            is_internal = is_internal_ip(ip)
            if is_internal:
                internal_hosts += 1
            else:
                external_hosts += 1

        ip_flow = [
            {
                "ip": i["key"],
                "actualSize": i["total_flow"]["value"],
                "showSize": size_format(i["total_flow"]["value"])
            } for i in all_ip_buckets
        ]
        ip_flow = sorted(ip_flow, key=lambda x: x["actualSize"], reverse=True)[:10]
        result = {
            "threatFlow": total_flow,
            "internalHosts": internal_hosts,
            "externalHosts": external_hosts,
            "ipFlow": ip_flow
        }

        return result
