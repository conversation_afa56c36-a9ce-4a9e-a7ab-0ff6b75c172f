.title{
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ccc;
  &>span{
    font-size: 1.2rem;
    font-weight: bold;
  }
}
.base_info{
  display: flex;
  flex-direction: column;
  .downBtn{
    padding: 1px 6px;
    height: 24px;
  }
  .file_level{
    border-radius: 24px;
    padding: 4px 8px;
    color: #fff;
  }
  .listBox{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 1rem;
  }
  .f4f5f6{
    background: #f4f5f6;
  }
  :global{
    .ant-list-split .ant-list-item{
      border:none !important
    }
  }
}
.down_info{
  display: flex;
  flex-direction: column;
  .downBox{
    display: flex;
    flex:1;
    flex-direction: column;
    border:1px solid rgb(238, 238, 238)
  }
  .titleSpan{
    width: 7rem; 
    padding: 3px 0 3px 6px; 
    border-right: 1px solid rgb(238, 238, 238)
  }
  .contentSpan{
    flex: 1;
    padding: 3px 0 3px 6px; 
  }
}
.tabsInfo{
  .btnBox{
    background: #f0f2f5;
    padding-bottom: 0.5rem;
  }
  :global{
    .ant-tabs-nav-wrap{
      display: none !important;
    }
  }
}