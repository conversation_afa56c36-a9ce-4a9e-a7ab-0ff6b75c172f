import multiprocessing
import subprocess

import os
import signal
import redis
import time

from . import celery
from .utils import MongoDB, get_redis_container_ip


r = redis.Redis(host=get_redis_container_ip("redis"), port=celery.RedisConfig.Port, password=celery.RedisConfig.Password, db=0)
table_name = 'cert_hive_source'


class LongRunningTask:
    def __init__(self, typ):
        self.type = typ

    def key(self, task_id):
        return '%s:%s' % (self.type, task_id)

    def run(self, task_id, command, timeout=None):
        process = subprocess.Popen(command, preexec_fn=os.setsid, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        child_pid = process.pid

        # 获取进程启动时间
        start_time = self.get_process_start_time(child_pid)

        # 存储任务信息到 Redis
        task_info = {
            'pid': child_pid,
            'start_time': start_time,
            'command': command[0]
        }
        r.hset(self.key(task_id), mapping=task_info)

        stdout, stderr = process.communicate()
        print(str(stderr, 'utf-8'))
        print(stdout.decode("utf-8"))
        r.delete(self.key(task_id))
        return process.returncode

    def run_command(self, command, queue):
        # 创建子进程，并将其加入新的进程组
        process = subprocess.Popen(command, preexec_fn=os.setsid, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        child_pid = process.pid
        queue.put(child_pid)
        stdout, stderr = process.communicate()
        return stdout, stderr

    def get_process_start_time(self, pid):
        """获取进程的启动时间（在 Unix 系统上）"""
        try:
            with open(f'/proc/{pid}/stat', 'r') as f:
                stat = f.read().split()
                # 启动时间是stat文件中的第22项，单位是时钟周期
                start_time_ticks = int(stat[21])
                clock_ticks_per_second = os.sysconf(os.sysconf_names['SC_CLK_TCK'])
                start_time_seconds = start_time_ticks / clock_ticks_per_second
                boot_time = os.stat('/proc').st_ctime
                return boot_time + start_time_seconds
        except FileNotFoundError:
            return None

    def stop_task(self, task_id):
        task_info = r.hgetall(self.key(task_id))
        if task_info:
            pid = int(task_info.get(b'pid'))
            start_time = float(task_info.get(b'start_time'))
            original_command = task_info.get(b'command').decode()

            try:
                if not self.validate_process(pid, start_time, original_command):
                    return 1
                os.kill(pid, signal.SIGTERM)
                r.delete(self.key(task_id))
                return 0
            except ProcessLookupError:
                return 2
        return 3

    def validate_process(self, pid, original_start_time, original_command):
        """验证进程是否与原始任务匹配"""
        try:
            # 检查启动时间
            start_time = self.get_process_start_time(pid)
            if start_time != original_start_time:
                return False

            # 检查命令行参数
            with open(f'/proc/{pid}/cmdline', 'r') as f:
                cmdline = f.read().replace('\x00', ' ').strip()
                if original_command not in cmdline:
                    return False

            return True
        except FileNotFoundError:
            print(f"Process {pid} not found", flush=True)
            return False


@celery.app.task(queue='host_task', routing_key='host_task')
def hive_source_run(source_name):
    mg = MongoDB("ndr")
    print("run: %s" % source_name)
    r = LongRunningTask('hive').run(
        source_name, ["python37", "/opt/ndr/host_celery/hive_process.py", source_name], timeout=3600)
    mg.update_one(table_name, {'source_name': source_name}, {'$set': {'status': 'success' if r == 0 else 'failed'}})


@celery.app.task(queue='host_task', routing_key='host_task')
def hive_source_stop(source_name):
    mg = MongoDB("ndr")
    print("stop: %s" % source_name)
    r = LongRunningTask('hive').stop_task(source_name)
    if r == 0:
        mg.update_one(table_name, {'source_name': source_name}, {'$set': {'status': 'stopped'}})
    return {"status": r}


if __name__ == "__main__":
    import json
    import argparse

    parser = argparse.ArgumentParser(description="LongRunningTask Test Programme")
    parser.add_argument('--operation', default='run', help='operation configuration')
    args = parser.parse_args()
    if args.operation == 'run':
        r = LongRunningTask('hive').run("1", ["ping", "-c", "10000", "google.com"], timeout=3600)
        if r:
            print(json.dumps(r))
    elif args.operation == 'stop':
        r = LongRunningTask('hive').stop_task("1")
        if r:
            print(json.dumps(r))



