@import '~@/style/index.less';

.threatFlag {
  width: 23px;
  height: 23px;
  margin: 0 auto;

  img {
    width: 100%;
  }
}

image {
  width: 50px;
  height: 33px;
}

.center_text {
  text-align: center;
}

.img_box {
  display: flex;
}

.table_top {
  width: 100%;
  padding: 10px 16px;
  line-height: 32px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sortBox {
  margin: 0 30px 0 10px;
}

.filterBox {
  line-height: 30px;
  color: #1890ff;

  img {
    margin-right: 5px;
  }
}

.drawer {
  position: absolute;
}

.contentBox {
  position: relative;
  overflow: hidden;
}

.iconFont {
  color: #acacac;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
}

.target {
  text-align: left;
}

.searchBtn {
  margin: 0 0 0 30px;
}

.score {
  font-size: 18px;
}

.btnGroup {
  display: inline;
  float: right;
  position: absolute;
  right: 30px;
  top: 33px;
}

.tag {
  height: 30px;
  line-height: 30px;
}

.serach_title {
  display: flex;
  justify-content: space-between;
}

.panel_title {
  padding-top: 7px;
}

.filter_btn {
  text-align: right;
}

.filter_border {
  border-top: 1px solid #d9d9d9;
}

.confirm_text {
  padding-right: 10px;
}

:global {
  .ant-picker-time-panel-column:hover {
    .scrollY;
  }

  .ant-row {
    padding: 0 0 10px 0 !important;
    padding-bottom: 10px !important;
  }
}
