/*
 * @Author: 田浩
 * @Date: 2021-10-25 18:02:00
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-18 14:43:23
 * @Descripttion:
 */
import React, { useEffect, useState, forwardRef } from 'react';
import { getOnlineDetail } from '@/services/explore';
import { Form, Tree, Radio, message } from 'antd';
const SourceForm = forwardRef((props: any, ref: any) => {
  const [form] = Form.useForm();
  let { detailparams, chidlValue, isAdd } = props;
  const [treeData, settreeData] = useState<any>([{ title: '/', key: '/' }]);
  const [checkedKeys, setcheckedKeys] = useState([]);
  const [loadedKeys, setLoadedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };

  useEffect(() => {
    setcheckedKeys(chidlValue.monitor_dirs);
  }, []);

  const onLoadData = (treeNode: any) => {
    return new Promise<void>(resolve => {
      // 加载过就不加载
      // if (treeNode.children) {
      //   resolve();
      //   return;
      // }

      detailparams.path = treeNode.key;
      getOnlineDetail(detailparams).then(res => {
        if (res.flag) {
          settreeData((data: any) =>
            handleTreeData({
              data,
              children: res.data.detail,
              key: treeNode.key,
            })
          );

          resolve();
        } else {
          message.error(res.message);
        }
      });
    });
  };

  const onExpand = (keys: any) => {
    let newLoadedKeys = loadedKeys;
    // 原本展开的节点树 大于当前展开的 说明是收起
    if (expandedKeys.length > keys.length) {
      // 当节点时收起 将该节点移除已加载节点
      newLoadedKeys = loadedKeys.filter(i => keys.includes(i));
    }
    setExpandedKeys(keys);
    setLoadedKeys(newLoadedKeys);
  };

  // 设置当前已加载的节点
  const onload = (loadKeys: any) => {
    setLoadedKeys(loadKeys);
  };

  // 处理树结构数据
  const handleTreeData = ({ data, children, key }: { data: any; children: any; key: string }) => {
    return data.map((item: any) => {
      if (item.key === key) {
        return {
          ...item,
          // 处理文件禁止选择
          children: children.map((item2: any) => {
            return {
              ...item2,
              disabled: item2.key.indexOf('.') === -1 ? false : true,
            };
          }),
        };
      }

      if (item.children) {
        return {
          ...item,
          children: handleTreeData({
            data: item.children,
            children,
            key,
          }),
        };
      }
      return item;
    });
  };

  const onCheck = (checkedKeys: any) => {
    setcheckedKeys(checkedKeys.checked);
    form.setFieldsValue({
      monitorDirs: checkedKeys.checked,
    });
  };
  const changeType = (value: any) => {
    detailparams.onlyDir = value.target.value;
  };

  return (
    <div className="source">
      <Form ref={ref} form={form} {...layout}>
        <p>请选择需要监控的目录（可多选）</p>
        <Form.Item label="仅展示目录" initialValue={'true'} name="onlyDir">
          <Radio.Group disabled={!isAdd} onChange={changeType}>
            {/* get请求所以值为string */}
            <Radio value={'true'}>是</Radio>
            <Radio value={'false'}>否</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="数据源" initialValue={checkedKeys} name="monitorDirs">
          <Tree
            disabled={!isAdd}
            loadData={onLoadData}
            checkable
            onCheck={onCheck}
            checkedKeys={checkedKeys}
            treeData={treeData}
            onExpand={onExpand}
            loadedKeys={loadedKeys}
            expandedKeys={expandedKeys}
            onLoad={onload}
            checkStrictly
          />
        </Form.Item>
      </Form>
    </div>
  );
});

export default SourceForm;
