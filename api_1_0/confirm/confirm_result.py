#!/usr/bin/env python3
# -*- coding:utf-8 -*-
""" 更新告警数据处理状态 """
from flask import request
from utils.ndr_base import NdrResource
from utils.utils import flask_response
from utils.param_check import Validator, check_flask_args
from config.config import NdrLog
from utils.logger import get_ndr_logger
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)
RULE_INDEX = {
    "file": "file-eve",
    "vul": "rule-eve",
    "ioc": "ioc-eve",
    "model": "model-eve"
}


class WaringResultConfirmResource(NdrResource):
    """ 更新告警数据处理状态 """

    def __init__(self):
        super(WaringResultConfirmResource, self).__init__(es_template="confirm_result")

    @check_flask_args(Validator("confirm_schema"), request)
    def post(self, **kwargs):
        warning_index = RULE_INDEX[kwargs["type"]]
        self.es_template["script"]["inline"] = "ctx._source.confirm=%d" % kwargs["confirm"]
        self.es_template["query"]["terms"]["uniqueId"] = kwargs["id_list"]
        try:
            res = self.es_client.update_by_query(index=warning_index, body=self.es_template)
            if not res["total"]:
                LOG.error('Processed failed.Reason: %s does not exist.' % str(kwargs["id_list"]))
                ndr_log_to_box("%s 标记处置失败：不存在。" % str(kwargs["id_list"]))
                return flask_response('%s does not exist.' % str(kwargs["id_list"]), False, {})
        except Exception as error:
            LOG.error('Processed failed.Reason: %s' % str(error))
            ndr_log_to_box("%s 标记处置失败：%s" % (str(kwargs["id_list"]), str(error)))
            return flask_response("Processed failed", False, {})
        return flask_response("Processed successfully.", True, {})
