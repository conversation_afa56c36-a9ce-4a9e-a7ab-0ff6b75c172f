# -*- coding: utf-8 -*-
# @Time    : 2019-08-05 14:47
# <AUTHOR> <PERSON>
# @File    : get_threat_level.py
# @Software: PyCharm
from utils.base_knowledge import BaseKnowledge


class GetThreatLevel(BaseKnowledge):
    """获取威胁等级字段信息"""

    def __init__(self):
        """初始化参数"""
        super(GetThreatLevel, self).__init__(col="knowledge_threat_level", filter_name="threatLevel")

    @staticmethod
    def get_filed():
        """获取threat_level字段"""
        return {
            "name": "threatLevel",
            "list": []
        }
