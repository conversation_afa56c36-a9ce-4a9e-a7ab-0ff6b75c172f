/**
 * <AUTHOR>
 * @Desc
 * @Date 2022-08-04 16:55:23
 * @LastEditors fangh3
 * @LastEditTime 2022-08-04 17:22:12
 * @List
 */
import React, { useState, useEffect } from 'react';
import { Table, Button, message, Select, Switch } from 'antd';
import { workSatus } from '@/utils/enumList';
import { getWork, saveWorkStatus } from '@/services/configs';
const { Option } = Select;
const ApiManage = () => {
  const [dataSource, setdataSource] = useState([]);
  const columns = [
    {
      title:'节点名称',
      dataIndex:'node_name'
    },
    {
      title: '索引序号',
      dataIndex: 'if_index',
    },
    {
      title: '接口名称',
      dataIndex: 'if_name',
    },
    {
      title: 'PCI地址',
      dataIndex: 'pci_addr',
    },
    {
      title: '带宽',
      dataIndex: 'speed',
    },
    {
      title: '工作模式',
      dataIndex: 'work_mode',
      render: (t: any, record: any) => {
        const change = (value: any) => {
          record.work_mode = value;
        };
        return (
          <Select onChange={change} defaultValue={t}>
            {workSatus.map((item: any) => {
              return (
                <Option value={item.value} key={item.key}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        );
      },
    },
    {
      title: '链路状态',
      dataIndex: 'link',
      render: (t: any) => {
        if (t === 0) {
          return <div style={{ color: ' #e62e00' }}>Down</div>;
        } else if (t === 1) {
          return <div style={{ color: ' #00cc66' }}>Up</div>;
        } else {
          return <div>Unknown</div>;
        }
      },
    },
    {
      title: '流量开关',
      dataIndex: 'status',
      render: (t: any, record: any, index: any) => {
        const changeStatus = (value: any) => {
          if (value === true) {
            record.status = 'working';
            dataSource[index].switch = 'working';
          } else {
            record.status = 'idle';
            dataSource[index].switch = 'idle';
          }
          setdataSource([...dataSource]);
        };
        // if (t === 'idle') {
        //   return '闲置';
        // } else {
        //   return '工作中';
        // }
        return <Switch onChange={changeStatus} checked={record.switch === 'working'} />;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              type="link"
              onClick={() => {
                handleSave(record);
              }}
            >
              保存
            </Button>
          </div>
        );
      },
    },
  ];
  const getWorkList = () => {
    getWork().then(res => {
      if (res.flag) {
        // 给datasource 添加一个switch 属性控制开关 不然会导致修改了状态很久不改变状态的bug
        res.data.forEach((item: any) => {
          item.switch = item.status;
        });
        setdataSource(res.data);
      }
    });
  };
  const handleSave = (value: any) => {
    let data = {
      action: '',
      if_name: value.if_name,
      work_mode: value.work_mode,
    };
    if (value.status === 'working') {
      data.action = 'start';
    } else {
      data.action = 'stop';
    }
    saveWorkStatus(data).then(res => {
      if (res.flag) {
        message.success(res.mesage);
        getWorkList();
      } else {
        message.error(res.message);
      }
    });
  };
  useEffect(() => {
    getWorkList();
  }, []);
  return <Table columns={columns} rowKey={(r, i) => i} dataSource={dataSource} />;
};
export default ApiManage;
