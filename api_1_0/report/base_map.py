#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import calendar
from copy import deepcopy
from datetime import datetime
from flask_restful import Resource

from api_1_0.knowledge.get_killchains import GetKillchains
from utils.es_template.get_es_template import ES_Template
from utils.es_function import *
from utils.database import get_es_client
from config.config import RULE_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)


class BaseMap(Resource):
    field_template = {
        "observed": "observedTime",
        "occurred": "occurredTime",
    }
    size_format = {
        "actualSize": 0,
        "showSize": "0K"
    }
    flow_template = {
        "killchainsFlow": {i: {"actualSize": 0, "showSize": "0K"} for i in list(GetKillchains.killchain_map.keys())},
        "threatFlow": {
            "High": size_format,
            "Medium": size_format,
            "Low": size_format
        }
    }

    def __init__(self, map_keyword):
        self.es_template = ES_Template().read_template("get_flow_groupBy_period")
        self.map_keyword = map_keyword
        self.start_time = 0
        self.stop_time = 0
        self.month_start_time = 0
        self.time_period = ""

    def get_es_data(self, **kwargs):
        '''
           当间隔等于一天时，按照小时进行展示
           当间隔时间大于一天不满一个月，按照天进行展示
           当间隔时间大于一个月按照月进行展示
        '''
        self.start_time = start_time = int(kwargs["args"]["startTime"])
        start_date = datetime.fromtimestamp(int(start_time / 1000))  # 这里的int是为了精确到秒即可
        self.month_start_time = int(start_date.replace(day=1, hour=0, minute=0, second=0).timestamp() * 1000)
        # 结束时间往后顺延一天取整，比如选的是23号，那么结束时间是24号的0点
        self.stop_time = stop_time = int(kwargs["args"]["stopTime"]) + 24 * 60 * 60 * 1000
        if start_time >= stop_time:
            message = "时间范围错误，开始时间大于等于结束时间"
            return False, message
        timeMode = self.field_template[kwargs["args"]["timeMode"]]
        args = {
            "start_time": start_time,
            "stop_time": stop_time,
            "timeMode": timeMode
        }
        self.format_es_template(args)

        # 连接ES查询
        es_client = get_es_client()
        try:
            res = es_client.search(index=RULE_INDEX, body=self.es_template)
            return True, res
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return False, message

    def format_es_template(self, args):
        start_time = args["start_time"]
        stop_time = args["stop_time"]
        timeMode = args["timeMode"]

        # 一天按照小时展示
        if stop_time - start_time == 3600 * 24 * 1000:
            self.time_period = time_period = '1h'
        # 大于1天小于30天按照天进行展示
        elif stop_time - start_time <= 3600 * 24 * 30 * 1000:
            self.time_period = time_period = '1d'
        # 超过30天按照月进行展示
        else:
            self.time_period = time_period = '1M'
        self.es_template["query"]["bool"]["must"].append(get_range(timeMode, start_time, stop_time))
        self.es_template["aggs"]["group_by"]["date_histogram"]["interval"] = time_period
        self.es_template["aggs"]["group_by"]["date_histogram"]["field"] = timeMode
        self.es_template["aggs"]["group_by"]["aggs"]["group_by"]["terms"]["field"] = self.map_keyword

    def slice_time_data(self, docs, type, is_app=False, proto_list=None):

        '''
        :param type: killchainsFlow, appFlow, threatFlow
        '''

        start = int(self.start_time / 1000)
        stop = int(self.stop_time / 1000)
        month_start = int(self.month_start_time / 1000)
        time_block_list = list()
        if self.time_period == "1h":
            index = start
            while index < stop:
                time_block_list.append(index * 1000)
                index += 3600

        elif self.time_period == "1d":
            index = start
            while index < stop:
                time_block_list.append(index * 1000)
                index += 3600 * 24

        else:
            index = month_start
            while index < stop:
                time_block_list.append(index * 1000)
                start_date = datetime.fromtimestamp(index)
                monthRange = calendar.monthrange(start_date.year, start_date.month)[1]
                index += 3600 * 24 * monthRange

        if is_app:
            proto_dict = dict()
            for key in proto_list:
                proto_dict[key] = {
                    "actualSize": 0,
                    "showSize": "0K"
                }
            docs_template = [
                {
                    "time": time_block,
                    type: deepcopy(proto_dict)

                } for time_block in time_block_list
            ]

        else:
            docs_template = [
                {
                    "time": time_block,
                    type: deepcopy(self.flow_template[type])

                } for time_block in time_block_list
            ]

        for doc in docs:
            try:
                posi = time_block_list.index(doc["time"])
                docs_template[posi][type].update(doc[type])
            except Exception:
                pass

        return docs_template
