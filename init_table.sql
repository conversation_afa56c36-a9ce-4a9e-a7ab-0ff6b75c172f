-----------------------------------------------------------------
-- NDR DPI 日志从Kafka导入到clickhouse表的sql语句
--
-- clickhouse通过命令行执行*.sql文件：
--   clickhouse-client --user 登录名 --password 密码 -d 数据库 --multiquery <  /usr/local/temp.sql
------------------------------------------------------------------
CREATE DATABASE IF NOT EXISTS ndr;

 create table if not exists ndr.dpilog_conn(
    create_time   DateTime,
    uuid          UUID,
    ts            DateTime,
    conn_id       String,
    net_ext_flag  String,
    src_ip        String,
    src_ipv4      IPv4,
    src_ipv6      IPv6,
    src_port      UInt32,
    dst_ip        String,
    dst_ipv4      IPv4,
    dst_ipv6      IPv6,
    dst_port      UInt32,
    proto         String,
    app_proto     String,
    duration      Float32,
    src_bytes    UInt64,
    dst_bytes    UInt64,
    conn_state    String,
    normal_start  UInt8,
    missed_bytes  UInt64,
    history       String,
    src_pkts     UInt64,
    src_ip_bytes UInt64,
    dst_pkts     UInt64,
    dst_ip_bytes UInt64,
    taskId        String,
    celeryId      String,
    topic         String,
    interface     UInt32,
    pcap_filename String
  ) ENGINE = MergeTree() 
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_http(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip          String,
    src_ipv4        String,
    src_ipv6        String,
    src_port        UInt32,
    dst_ip          String,
    dst_ipv4        String,
    dst_ipv6        String,
    dst_port        UInt32,
    taskId           String,
    celeryId         String,
    interface        UInt32,
    pcap_filename    String,
    topic            String,
   `method`          String,
    uri              String,
    version          String,
    host             String,
    user_agent       String,
    referrer         String,
    xff              String,
    client_header_names String,
    client_body      String,
    resp_version     String,
    status_code      String,
    status_msg       String,
    server           String,
    content_type     String,
    server_header_names String,
    server_body      String
  ) ENGINE = MergeTree() 
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_dns(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    proto            String,
    trans_id         UInt32,
    query            String,
    query_len        UInt32,
    qclass_name      String,
    qtype_name       String,
    result           String,
    mx               String,
    cname            String,
    flags            UInt32,
    query_num        UInt32,
    answer_num       UInt32,
    auth_num         UInt32,
    ext_num          UInt32,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree() 
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_ftp(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    `user`           String,
    `password`       String,
    cmd_sequence     UInt32,
    command          String,
    arg              String,
    reply_code       String,
    reply_msg        String,
    file_size        UInt64,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree() 
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_mail(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    app_proto          String,
    msg_id           String,
    `date`           String,
    user_agent       String,
    `from`           String,
    reply_to         String,
    `to`             String,
    cc               String,
    subject          String,
    bcc              String,
    return_path      String,
    received         String,
    body             String,
    file_md5s        String,
    file_names       String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree() 
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_ssl(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    server_name      String,
    version          String,
    client_cipher_num UInt16,
    server_cipher    String,
    curve            String,
    resumed          UInt8,
    alerts           String,
    next_protocol    String,
    established      UInt8,
    ja3              String,
    ja3s             String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String,
    server_cert_serial_numbers String,
    server_extension String,
    client_extension String,
    cert_issuer      String,
    cert_validity    String,
    cert_subject     String,
    cert_ext_keyId   String,
    client_ext_num   UInt8
  ) ENGINE = MergeTree() 
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_files(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String,
    proto            String,
    app_proto        String,
    file_origin      String,
    file_path        String,
    fuid             String,
    filename         String,
    filetype         String,
    filesize         UInt32,
    md5              String,
    crc32            String,
    sha256           String
  ) ENGINE = MergeTree() 
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ip, dst_ip, celeryId, filename, md5)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_icmp(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    itype            UInt8,
    icode            UInt8,
    echo_id          UInt16,
    seq              UInt16,
    payload_len      UInt16,
    payload          String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_mysql(
    create_time         DateTime,
    uuid                UInt64,
    ts                  DateTime,
    conn_id             String,
    net_ext_flag        String,
    src_ip              String,
    src_ipv4            IPv4,
    src_ipv6            IPv6,
    src_port            UInt32,
    dst_ip              String,
    dst_ipv4            IPv4,
    dst_ipv6            IPv6,
    dst_port            UInt32,
    protocol            UInt32,
    version             String,
    server_capabilities String,
    auth_plugin         String,
    username            String,
    password            String,
    client_auth_plugin  String,
    client_capabilities String,
    response_code       String,
    statement           String,
    taskId              String,
    celeryId            String,
    topic               String,
    interface           UInt32,
    pcap_filename       String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_login(
    create_time      DateTime,
    uuid             UUID,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    app_proto        String,
    username         String,
    password         String,
    encrypted_password String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_dhcp(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    msg_type         UInt32,
    client_mac       String,
    op_msg_type      String,
    op_host_name     String,
    op_domain_name   String,
    op_domain_name_server   String,
    op_ip_addr       String,
    op_server_ident  String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_telnet(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    username         String,
    password         String,
    command          String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_nfs(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    rpc_ver          UInt32,
    rpc_prg          String,
    rpc_prg_ver      UInt32,
    rpc_proc         String,
    version          String,
    reply_state      String,
    reply_len        UInt32,
    procedure        String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_modbus(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    trans_id         UInt32,
    proto_id         String,
    unit_id          UInt32,
    func_code        String,
    req_data         String,
    rsp_data         String,
    excep_code       String,
    diag_code        String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_new_conn(
    create_time      DateTime,
    uuid             UUID,
    ts               DateTime,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    proto            String,
    topic            String,
    interface        UInt32
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_tftp(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    opcode           String,
    filename         String,
    type             String,
    blknum           UInt32,
    blksize          UInt32,
    tsize            UInt32,
    error_msg        String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_rip(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    command          String,
    version          UInt32,
    ip_mask_metric   String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_netbios(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    service_type     String,
    nbns_msg_type    String,
    nbns_trans_id    String,
    nbns_name        String,
    nbns_rsp_ip      String,
    nbss_msg_type    String,
    nbss_called_name String,
    nbss_calling_name String,
    nbss_error_code  String,
    nbds_msg_type    String,
    nbds_src_ip      String,
    nbds_src_port    UInt32,
    nbds_src_name    String,
    nbds_dst_name    String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_snmp(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    version          String,
    community        String,
    encrypted        UInt8,
    pdu_type         String,
    request_id       UInt32,
    error_status     String,
    error_index      UInt32,
    enterprise       String,
    agent_addr       String,
    trap_type        String,
    specific_trap    UInt32,
    time_stamp       UInt32,
    kv_list          String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_smb(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    version          String,
    username         String,
    hostname         String,
    tree_connect     String,
    command          String,
    filename         String,
    rsp_status       String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_igmp(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    version          String,
    type             String,
    max_resp_time    String,
    multicast_addr   String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;


create table if not exists ndr.dpilog_mssql(
    create_time      DateTime,
    uuid             UInt64,
    ts               DateTime,
    conn_id          String,
    net_ext_flag     String,
    src_ip           String,
    src_ipv4         IPv4,
    src_ipv6         IPv6,
    src_port         UInt32,
    dst_ip           String,
    dst_ipv4         IPv4,
    dst_ipv6         IPv6,
    dst_port         UInt32,
    pkt_type         String,
    req_sqls         String,
    rsp_tokens       String,
    taskId           String,
    celeryId         String,
    topic            String,
    interface        UInt32,
    pcap_filename    String
  ) ENGINE = MergeTree()
    PARTITION BY (toYYYYMMDD(create_time), toYYYYMMDD(ts))
    ORDER BY (create_time, ts,  src_ipv4, src_ipv6, dst_ipv4, dst_ipv6, celeryId)
    TTL create_time + INTERVAL 6 MONTH
    SETTINGS index_granularity=8192;

