# -*- coding: utf-8 -*-
# @Time    : 2019-09-02 18:35
# <AUTHOR> Shark
# @File    : test_alert_list.py
# @Software: PyCharm
import json
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_alert.alert_list import AlertList


class TestAlertList(TestCase):
    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_startTime_not_exists(self):
        resp = self.client.get(
            api.url_for(AlertList,
                        stopTime=1578430872367,
                        pageSize=10
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_stopTime_not_exists(self):
        resp = self.client.get(
            api.url_for(<PERSON>ertList,
                        startTime=1504527700099
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_all_true(self):
        resp = self.client.get(
            api.url_for(AlertList,
                        startTime=1567501666714,
                        stopTime=1567501666714,
                        vulName="PHP",
                        cve="cve-2019-0019",
                        ip="**********",
                        killchains="Weaponization",
                        sid=2018581,
                        threatLevel="High",
                        threatScore="*-*",
                        threatFlag="Scanning",
                        country="United States",
                        result=False,
                        # taskId="",
                        g_vulName="PHP",
                        g_cve="cve-2019-0019",
                        g_ip="**********",
                        g_killchains="Weaponization",
                        g_sid=2018581,
                        g_threatLevel="High",
                        g_threatFlag="Scanning",
                        g_country="United States",
                        # g_taskId=""
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)
