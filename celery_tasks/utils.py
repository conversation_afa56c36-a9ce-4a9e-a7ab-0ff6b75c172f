#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_event_metadata.py
# @time: 2019/07/18
# @software: PyCharm

from config.config import ClickHouseConfig, NdrLog
from clickhouse_driver import Client as CH_client
from api_1_0.utils.flask_log import ndr_log_to_box


class ClickHouse(object):
    def __init__(self):
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)

    def get_val_from_database(self, sql):
        ret_list = list()
        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except:
            return ret_list
        if raw is None or not raw[0]:
            return ret_list
        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]
        for i in range(len(raw_val)):
            data = {}
            for idx, item in enumerate(raw_val[i]):
                if isinstance(item, bytes):
                    item = str(item)
                data[raw_key[idx][0]] = item
            ret_list.append(data)
        return ret_list

    def get_clickhouse_data(self, ip_str, start_time, end_time):
        # 处理ipv4和ipv6
        src_ip_cond = "(src_ipv6=toIPv6('%s'))" % ip_str if ':' in ip_str else "(src_ipv4=toIPv4('%s'))" % ip_str
        dst_ip_cond = "(dst_ipv6=toIPv6('%s'))" % ip_str if ':' in ip_str else "(dst_ipv4=toIPv4('%s'))" % ip_str
        time_cond = '(create_time >= %s and create_time <= %s)' % (start_time, end_time)
        # 发起的链接

        send_count_sql = "select count(*) as src_count from dpilog_conn where %s and %s" % (src_ip_cond, time_cond)
        # 接受的链接
        accept_count_sql = "select count(*) as dst_count from dpilog_conn where %s and %s" % (dst_ip_cond, time_cond)
        # 启用的端口信息
        enable_port_sql = "select distinct(dst_port) from dpilog_conn where %s and conn_state = 'established' " \
                          "and dst_port < src_port and %s" % (dst_ip_cond, time_cond)
        # 应用流量统计
        app_traffic_sql = "select application,sum(orig_ip_bytes)+sum(resp_ip_bytes) as total_bytes from dpilog_conn " \
                          "where (%s or %s) and %s group by application order by total_bytes desc limit 20 " \
                          % (src_ip_cond, dst_ip_cond, time_cond)
        # 应用总流量统计
        total_app_traffic_sql = "select sum(orig_ip_bytes)+sum(resp_ip_bytes) as total_bytes from dpilog_conn " \
                                "where (%s or %s) and %s" % (src_ip_cond, dst_ip_cond, time_cond)
        # 发送流量统计

        send_traffic_sql = "select dst_ip,sum(orig_ip_bytes) as total_orig_bytes from dpilog_conn where %s and %s " \
                           "group by dst_ip order by total_orig_bytes desc limit 10" % (src_ip_cond, time_cond)
        # 访问抽样
        access_sample_sql = "select dst_ip,groupUniqArray(dst_port) as port_list from dpilog_conn " \
                            "where %s and %s group by dst_ip limit 10" % (src_ip_cond, time_cond)
        # 周流量   当前时间取向上取整除4小时的整点时间
        full_time = int(((end_time + 14399) // 14400) * 14400)
        week_stream = {}
        week_stream["time_stamp"] = full_time
        week_stream["total_bytes"] = 0

        send_rst = self.get_val_from_database(send_count_sql)
        accept_rst = self.get_val_from_database(accept_count_sql)
        enable_port_rst = self.get_val_from_database(enable_port_sql)
        app_traffic_rst = self.get_val_from_database(app_traffic_sql)
        total_traffic_rst = self.get_val_from_database(total_app_traffic_sql)
        send_traffic_rst = self.get_val_from_database(send_traffic_sql)
        access_sample_rst = self.get_val_from_database(access_sample_sql)

        send_count = 0
        accept_count = 0
        enable_port = []
        app_traffic = {}
        total_traffic = 0
        send_traffic = {}
        access_sample = {}
        if send_rst:
            send_count = send_rst[0]["src_count"]
        if accept_rst:
            accept_count = accept_rst[0]["dst_count"]
        if enable_port_rst:
            for i in enable_port_rst:
                enable_port.append(i["dst_port"])
        if app_traffic_rst:
            for i in app_traffic_rst:
                app_traffic[i["application"]] = i["total_bytes"]
        if total_traffic_rst:
            total_traffic = total_traffic_rst[0]["total_bytes"]
            # 周流量
            week_stream["total_bytes"] = total_traffic_rst[0]["total_bytes"]
        if send_traffic_rst:
            for i in send_traffic_rst:
                send_traffic[i["dst_ip"]] = i["total_orig_bytes"]
        if access_sample_rst:
            for i in access_sample_rst:
                access_sample[i["dst_ip"]] = i["port_list"]
        return send_count, accept_count, enable_port, app_traffic, total_traffic, send_traffic, access_sample, week_stream
