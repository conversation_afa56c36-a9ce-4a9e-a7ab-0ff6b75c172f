# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : scheduler.py
# @Software: PyCharm

"""异常检测任务调度器"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any
from utils.logger import get_ndr_logger
from config.config import NdrLog
from api_1_0.utils.flask_log import ndr_log_to_box
from .detection_executor import DetectionExecutor

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class AnomalyDetectionScheduler:
    """异常检测任务调度器"""
    
    def __init__(self):
        self.executor = DetectionExecutor()
        self.running = False
        self.thread = None
        self.check_interval = 60  # 检查间隔，秒
    
    def start(self):
        """启动调度器"""
        if self.running:
            LOG.warning("调度器已经在运行中")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()
        
        LOG.info("异常检测调度器已启动")
        ndr_log_to_box(NdrLog.Type.OPERATE, "异常检测调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            LOG.warning("调度器未在运行")
            return
        
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        LOG.info("异常检测调度器已停止")
        ndr_log_to_box(NdrLog.Type.OPERATE, "异常检测调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        LOG.info("调度器主循环开始")
        
        while self.running:
            try:
                # 执行所有活跃任务
                self._execute_scheduled_tasks()
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                LOG.error(f"调度器执行异常: {str(e)}")
                time.sleep(self.check_interval)
        
        LOG.info("调度器主循环结束")
    
    def _execute_scheduled_tasks(self):
        """执行计划任务"""
        try:
            # 执行所有活跃任务
            result = self.executor.execute_all_active_tasks()
            
            if result['total'] > 0:
                LOG.info(f"调度执行完成: 总数{result['total']}, 成功{result['success']}, 失败{result['failed']}")
                
                # 如果有失败的任务，记录详细信息
                if result['failed'] > 0:
                    failed_tasks = [r for r in result['results'] if not r['success']]
                    for task in failed_tasks:
                        LOG.error(f"任务执行失败: {task['task_name']} - {task.get('error', '未知错误')}")
            
        except Exception as e:
            LOG.error(f"执行计划任务失败: {str(e)}")
    
    def execute_task_now(self, task_id: str) -> bool:
        """立即执行指定任务"""
        try:
            success = self.executor.execute_task(task_id)
            if success:
                LOG.info(f"手动执行任务成功: {task_id}")
                ndr_log_to_box(NdrLog.Type.OPERATE, f"手动执行异常检测任务: {task_id}")
            else:
                LOG.error(f"手动执行任务失败: {task_id}")
            
            return success
            
        except Exception as e:
            LOG.error(f"手动执行任务异常: {task_id}, 错误: {str(e)}")
            return False
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'running': self.running,
            'check_interval': self.check_interval,
            'thread_alive': self.thread.is_alive() if self.thread else False,
            'last_check_time': datetime.now().isoformat(),
        }
    
    def set_check_interval(self, interval: int):
        """设置检查间隔"""
        if interval < 10:
            raise ValueError("检查间隔不能小于10秒")
        
        self.check_interval = interval
        LOG.info(f"调度器检查间隔已设置为: {interval}秒")


# 全局调度器实例
_scheduler_instance = None


def get_scheduler() -> AnomalyDetectionScheduler:
    """获取调度器实例（单例模式）"""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = AnomalyDetectionScheduler()
    return _scheduler_instance


def start_scheduler():
    """启动调度器"""
    scheduler = get_scheduler()
    scheduler.start()


def stop_scheduler():
    """停止调度器"""
    scheduler = get_scheduler()
    scheduler.stop()


def execute_task_manually(task_id: str) -> bool:
    """手动执行任务"""
    scheduler = get_scheduler()
    return scheduler.execute_task_now(task_id)


def get_scheduler_status() -> Dict[str, Any]:
    """获取调度器状态"""
    scheduler = get_scheduler()
    return scheduler.get_scheduler_status()
