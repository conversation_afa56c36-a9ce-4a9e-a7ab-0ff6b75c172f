#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: test_RulesEnable.py
# @time: 2019/07/12
# @software: PyCharm
import random
import unittest
from api_1_0.router import app, api
import json
from api_1_0.back_rules.rules_status_manager import RulesEnable


class TestRulesEnable(unittest.TestCase):

    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        self.query_string = {
            "action": "enable",
            "sidList": []
        }

    def tearDown(self):
        pass

    # 缺少必要字段
    def test_lack_of_required_parameters(self):
        required_list = ["action", "sidList"]
        self.query_string.pop(random.sample(required_list, 1)[0])
        resp_dict = self.post(RulesEnable, self.query_string)
        self.assertEqual(resp_dict["flag"], False)

    # action error
    def test_post_illegal_of_action(self):
        illegal_action = ["", "sadasd", "enbbaa", "1213__swde", "*&^^"]
        for line in illegal_action:
            self.query_string["action"] = line
            resp_dict = self.post(RulesEnable, self.query_string)
            self.assertEqual(resp_dict["flag"], False)

    # sid error
    def test_post_illegal_of_sid(self):
        illegal_sid = ["", "sadasd", "enbbaa", "1213__swde", "*&^^"]
        self.query_string["sidList"].append(random.sample(illegal_sid, 1)[0])
        resp_dict = self.post(RulesEnable, self.query_string)
        self.assertEqual(resp_dict["flag"], False)

    def post(self, url, data):
        response = self.client.post(api.url_for(url), json=data)
        resp_dict = json.loads(response.data)
        return resp_dict

    def get(self, url):
        response = self.client.get(api.url_for(url))
        resp_dict = json.loads(response.data)
        return resp_dict


if __name__ == '__main__':
    suite = unittest.TestSuite()
    suite.addTest()
    # 执行测试
    runner = unittest.TextTestRunner()
    runner.run(suite)
