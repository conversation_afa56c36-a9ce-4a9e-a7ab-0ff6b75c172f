import { getIntelligenceList, getLoopHoleList, getModalList, getFileList } from '@/services/alarm';
import { message } from 'antd';
import cn from 'classnames';
import moment from 'moment';
import React from 'react';
import AlertTable from './AlertTable';
import DateRangePicker from './DateRangePicker';
import Filter from './Filter';
import type { FilterState } from './shared';
import { DEFAULT_FILTER_STATE, preparePayload } from './shared';

interface AlertViewProps {
  className?: string;
  type: string;
}

const DATALIST_QUERY_FUNC_MAP = {
  vul: getLoopHoleList,
  ioc: getIntelligenceList,
  model: getModalList,
  file: getFileList,
};

export default function AlertView({ type, className }: AlertViewProps) {
  const search = new URLSearchParams(location.search);
  const defaultCeleryId = search.get('celeryId');
  const [loading, setLoading] = React.useState(false);

  const defaultState = React.useMemo(() => {
    if (defaultCeleryId) {
      return {
        ...DEFAULT_FILTER_STATE,
        celeryId: defaultCeleryId,
        dateRange: search.get('startTime')
          ? [
              moment(parseInt(search.get('startTime') || Date.now().toString(), 10)),
              moment(parseInt(search.get('endTime') || Date.now().toString(), 10)),
            ]
          : undefined,
      };
    }
    let dateRange;
    try {
      dateRange = JSON.parse(sessionStorage.getItem('dateRange') || '');
    } catch {
      return DEFAULT_FILTER_STATE;
    }
    if (!dateRange || !Array.isArray(dateRange) || dateRange.length === 0) return DEFAULT_FILTER_STATE;

    return {
      ...DEFAULT_FILTER_STATE,
      dateRange: dateRange?.map((x) => moment(x)),
    };
  }, []);
  const [filter, setFilter] = React.useState<FilterState>(defaultState);
  const handleFilterChange = (values: FilterState) => {
    if (values.dateRange) {
      sessionStorage.setItem('dateRange', JSON.stringify(values.dateRange?.map((x: moment.Moment) => x.toDate().getTime())));
    } else {
      sessionStorage.removeItem('dateRange');
    }

    setFilter({ ...values, page: filter.page !== values.page ? values.page : 1 });
  };

  const [tableData, setTableData] = React.useState([]);

  React.useEffect(() => {
    if (loading) return;

    setLoading(true);

    const payload = preparePayload(filter);

    DATALIST_QUERY_FUNC_MAP[type](payload)
      .then((res) => {
        if (res.flag) {
          setTableData(res.data);
        } else {
          message.error(res.message);
        }
      })
      .catch((error: Error) => {
        setTableData([]);
        message.error(error?.data?.message || error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [filter]);

  return (
    <div style={{ height: 'calc(100vh - 74px)' }} className={cn('of-hidden flex flex-col', className)}>
      <DateRangePicker
        className="!absolute !top-2 left-12 !z-20"
        value={filter.dateRange}
        disabled={loading}
        onChange={(dateRange) =>
          handleFilterChange({
            ...filter,
            dateRange: dateRange as [moment.Moment, moment.Moment],
          })
        }
      />
      <div className="flex-[1] of-auto">
        <Filter loading={loading} type={type} value={filter} onChange={(values) => handleFilterChange(values)} />
        <AlertTable
          loading={loading}
          type={type}
          value={tableData}
          filter={filter}
          onChange={(values) => handleFilterChange(values)}
        />
      </div>
    </div>
  );
}
