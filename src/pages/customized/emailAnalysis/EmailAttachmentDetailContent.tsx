import React, { useState } from 'react';

import cs from 'classnames';
import { useEmailAttachmentDetailData } from './hooks';
import { Card, Spin } from 'antd';
import BaseInfo from '@/pages/collect/fileAnalysis/FileDetail/baseInfo';
import DownInfo from '@/pages/collect/fileAnalysis/FileDetail/downInfo';
import TabsInfo from '@/pages/collect/fileAnalysis/FileDetail/tabsInfo';

import styleModule from './style.less';
import { pick } from 'lodash';

export type Props = {
  data: {
    end_time: number;
    start_time: number;
    uuid: string;
  };
};

function EmailAttachmentDetailContent({ data }: Props): JSX.Element {
  const { fileBaseData, fileDetailData, loading } = useEmailAttachmentDetailData(data);

  const renderDetailContent = () => {
    if (!fileBaseData || !fileDetailData) return null;
    const { _source: fileBaseSource } = fileBaseData;

    const base = pick(fileBaseSource, [
      'file.md5',
      'file.tags',
      'file.fuid',
      'file.file_path',
      'file.filetype',
      'virus_name.knownsec',
      'virus_name.clamav',
      'severity',
      'pcap_filename',
      'labels',
    ]);

    const down = pick(fileBaseSource, [
      'report_path',
      'file.filename',
      'file.filetype',
      'dst_ip_location',
      'src_ip',
      'dst_ip',
      'kill_chain_sort',
      'src_ip_location',
    ]);

    const fileInfo = pick(fileBaseSource, [
      'virus_name.knownsec',
      'virus_name.clamav',
      'file.filetype',
      'file.filesize',
      'file.crc32',
      'file.sha256',
    ]);

    const detail = {
      fileInfo,
      ...fileDetailData,
    };

    return (
      <>
        <BaseInfo base={base} />
        <DownInfo down={down} />
        <TabsInfo detail={detail} />
      </>
    );
  };

  return (
    <Spin tip="加载中..." spinning={loading}>
      <div className={cs('of-hidden', styleModule.detailBox)} style={{ height: 'calc(100vh - 74px)' }}>
        {renderDetailContent()}
      </div>
    </Spin>
  );
}

export default EmailAttachmentDetailContent;
