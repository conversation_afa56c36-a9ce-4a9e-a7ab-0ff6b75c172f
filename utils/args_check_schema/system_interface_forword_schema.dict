{
	"type": "object",
	"required": ["group_name", "forword_mode", "interface_list", "status"],
	"properties": {
		"group_name": {
			"type": "string",
			"pattern": r"^[A-Za-z0-9_\-]{1,31}$"
		},
		"forword_mode": {
			"type": "string",
			"enum": ["l2_transparent_mode"]
		},
		"interface_list": {
			"type": "array",
			"items": {
                "type": "string",
                "pattern": r"^e.{3,7}$"
              }
		},
		"status": {
			"type": "string",
			"enum": ["enable", "disable"]
		},
	}
}