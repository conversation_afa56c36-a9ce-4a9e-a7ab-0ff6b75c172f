# -*- coding: utf-8 -*-
# @Time    : 2019-05-05 14:10
# <AUTHOR> hachi
# @File    : feature_task_manager.py
# @Software: PyCharm
"""
特征任务管理
"""
import os.path
import time
import datetime
from flask import request
from flask_restful import Resource

from api_1_0.hdp_grpc.die import grpc_feature_add, grpc_feature_operate
from utils.database import MongoDB
from utils import param_check
from utils.logger import get_ndr_logger
from utils.cluster import *
from celery_tasks.feature_activate_task import periodic_task
from utils.param_check import Validator
from utils.utils import flask_response, run_command
from config.config import DetectProg

LOG = get_ndr_logger('api_log', __file__)


class FeatureEnableTask(Resource):
    """
    批量特征启用/禁用处理
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @param_check.check_flask_args(Validator('feature_enable_schema'), request)
    def post(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        valid_list = []
        invalid_list = []
        sid_list = kwargs["args"]["sidList"]
        control = kwargs["args"]["control"]
        tlv_bin_file_prefix = '/tmp/' + str(hash(time.time()))
        if kwargs["args"]["action"] == "enable":
            for sid in sid_list:
                if self.mongodb.update("feature", {"sid": sid, "featureStatus": "disable"},
                                       {"featureStatus": "enable", "control": control}):
                    valid_list.append(sid)
                    continue
                LOG.info("Feature [%s] has been activated." % sid)
                invalid_list.append(sid)
                continue
        elif kwargs["args"]["action"] == "disable":
            for sid in sid_list:
                if self.mongodb.update("feature", {"sid": sid, "featureStatus": "enable"},
                                       {"featureStatus": "disable", "control": control}):
                    valid_list.append(sid)
                    continue
                LOG.info("Feature [%s] %s does not exist." % sid)
                invalid_list.append(sid)

        periodic_task.delay()
        # hdp添加已启用的规则
        if kwargs["args"]["action"] == "enable":
            # 将已启用的规则保存为文件
            with open('/tmp/hdp_enable.feature', 'w') as hdp_file:
                for sid in valid_list:
                    feature = self.mongodb.find_one("feature", {"sid": sid})
                    hdp_file.write(feature['featureContent'].strip() + '\n')

            run_command('%s --featurePath %s --saveTlvPath %s' % (
                DetectProg.Hdp_SigCompile,
                '/tmp/hdp_enable.feature',
                tlv_bin_file_prefix
            ))

            rule_path = tlv_bin_file_prefix + '_detect.bin'
            rule_info_path = tlv_bin_file_prefix + '_describe.bin'

            if os.path.getsize(str(rule_path)) > 8096 or os.path.getsize(str(rule_info_path)) > 8096:
                return flask_response("Too many rules are enabled, please reduce the number", False, {})

            response = grpc_feature_add(rule_path, rule_info_path)
            if not response:
                return flask_response("Grpc add feature failed.", False, {})
            cluster_hdp_feature_add(rule_path, rule_info_path)
        # hdp删除已禁用的规则
        elif kwargs["args"]["action"] == "disable":
            if len(valid_list) > 1024:
                return flask_response("Delete rules over 1024", False, {})

            grpc_feature_operate([int(sid) for sid in valid_list])
            cluster_hdp_feature_operate([int(sid) for sid in valid_list])

        data = {"successCount": len(valid_list),
                "operateFail": invalid_list}
        return flask_response("", True, data)

# class FeatureActiveTaskOne(Resource):
#     """
#     单个特征激活任务处理
#     """
#
#     def __init__(self):
#         self.mongodb = MongoDB("ndr")
#
#     # 根据任务 ID 获取单个特征激活的详细信息
#     def get(self, task_id):
#         """
#         获取当前某个定时激活规则详细信息
#         :param task_id:
#         :return:
#         """
#         data = self.mongodb.find_one("feature_active_task", {"taskId": task_id},
#                                      {"_id": 0,
#                                       "activateTime": 1,
#                                       "taskId": 1,
#                                       "celeryId": 1,
#                                       "taskName": 1,
#                                       "createdAt": 1,
#                                       "taskStatus": 1})
#         if data:
#             data['activateTime'] = int(time.mktime(time.strptime(data['activateTime'], "%Y-%m-%d %H:%M:%S")) * 1000)
#             data['createdAt'] = int(time.mktime(time.strptime(data['createdAt'], "%Y-%m-%d %H:%M:%S")) * 1000)
#         return flask_response("", True, data)
#
#     def delete(self, task_id):
#         """
#         删除当前某个激活特征任务
#         :param task_id:
#         :return:
#         """
#         old_task = self.mongodb.find_one("feature_active_task", {"taskId": task_id},
#                                          {"_id": 0, "celeryId": 1})
#         if old_task:
#             app.control.revoke(old_task["celeryId"], terminate=True, signal='SIGKILL')
#             self.mongodb.delete("feature_active_task", {"taskId": task_id})
#             return flask_response("", True, {})
#         LOG.error("Feature active-task [%s] does not exist." % task_id)
#         return flask_response("Feature active-task [%s] does not exist." % task_id, False, {})
#
#     @param_check.check_flask_args(Validator('feature_activate_modify_schema'), request)
#     def put(self, task_id, **kwargs):
#         """
#         修改当前某个定时激活特征任务
#         :param task_id:
#         :return:
#         """
#         old_task = self.mongodb.find_one("feature_active_task", {"taskId": task_id},
#                                          {"_id": 0,
#                                           "taskName": 1,
#                                           "taskId": 1,
#                                           "celeryId": 1
#                                           })
#         if old_task:
#             activate_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(kwargs["args"]["enableTime"]) / 1000))
#             created_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#
#             if time.mktime(time.strptime(activate_time, '%Y-%m-%d %H:%M:%S')) <= \
#                     time.mktime(time.strptime(created_time, '%Y-%m-%d %H:%M:%S')):
#                 LOG.error("The enable time must later than current time.")
#                 return flask_response("The enable time must later than current time.", False, {})
#
#             app.control.revoke(old_task["celeryId"], terminate=True, signal='SIGKILL')
#             celery_id_new = periodic_task.apply_async(eta=activate_time + "+08:00")
#             self.mongodb.update("feature_active_task", {"taskId": task_id},
#                                 {"activateTime": activate_time,
#                                  "timestamp": time.mktime(time.strptime(activate_time,
#                                                                         '%Y-%m-%d %H:%M:%S')),
#                                  "celeryId": celery_id_new.id})
#             LOG.info("Feature active-task [%s] modify success." % old_task["taskName"])
#             data = {"taskName": old_task["taskName"],
#                     "taskId": old_task["taskId"],
#                     "celeryId": celery_id_new.id}
#             return flask_response("", True, data)
#
#         LOG.error("Feature active-task modify failed.Reason: task [%s] does not exist." % task_id)
#         return flask_response("Modify failed.Reason: Feature active-task [%s] does not exist."
#                               % task_id, False, {})
#
#
# class FeatureActiveTask(Resource):
#     """
#     1.创建特征激活任务
#     2.获取特征激活任务列表
#     """
#
#     def __init__(self):
#         self.mongodb = MongoDB("ndr")
#
#     @param_check.check_flask_args(Validator('feature_activate_add_schema'), request)
#     def post(self, **kwargs):
#         """
#         创建特征列表激活任务
#         :return:
#         """
#         # 实时激活
#         if kwargs["args"]["enableFlag"] == 1:
#             periodic_task.delay()
#             return flask_response("", True, {})
#         # 定时激活
#         if kwargs["args"]["enableFlag"] == 0:
#             activate_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(kwargs["args"]["enableTime"]) / 1000))
#             task_name = kwargs["args"]["taskName"]
#             created_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#             try:
#
#                 if time.mktime(time.strptime(activate_time, '%Y-%m-%d %H:%M:%S')) < \
#                         time.mktime(time.strptime(created_at, '%Y-%m-%d %H:%M:%S')):
#                     LOG.error("The enable time must later than current time.")
#                     return flask_response("The enable time must "
#                                           "later than current time.", False, {})
#             except ValueError as err_msg:
#                 LOG.error(err_msg)
#                 return flask_response(err_msg, False, {})
#
#             celery_id = periodic_task.apply_async(eta=activate_time + "+08:00")
#
#             body = {
#                 "activateTime": activate_time,
#                 "taskName": task_name,
#                 "createdAt": created_at,
#                 "timestamp": time.mktime(time.strptime(activate_time, '%Y-%m-%d %H:%M:%S')),
#                 "taskId": create_task_id(),
#                 "celeryId": str(celery_id),
#                 "status": "PENDING"
#             }
#
#             self.mongodb.insert_one("feature_active_task", body)
#             LOG.info("Feature active-task [%s] create success."
#                      "Task id is: %s" % (body["taskName"], body["taskId"]))
#             return flask_response("", True, {"taskName": body["taskName"],
#                                              "taskId": body["taskId"]})
#
#         msg = "Feature active-task [%s] create failed.Reason: no such operation." % body["taskName"]
#         LOG.error(msg)
#         return flask_response(msg, False, {})
#
#     def get(self):
#         """
#         获取当前定时激活的所有任务， get all
#         :return:
#         """
#         task_list = []
#         for task in self.mongodb.find("feature_active_task", {},
#                                       {"_id": 0,
#                                        "activateTime": 1,
#                                        "taskId": 1,
#                                        "celeryId": 1,
#                                        "taskName": 1,
#                                        "createdAt": 1,
#                                        "status": 1}):
#             task['activateTime'] = int(time.mktime(time.strptime(task['activateTime'], "%Y-%m-%d %H:%M:%S")) * 1000)
#             task['createdAt'] = int(time.mktime(time.strptime(task['createdAt'], "%Y-%m-%d %H:%M:%S")) * 1000)
#             task_list.append(task)
#         data = {"count": len(task_list), "detail": task_list}
#         return flask_response("", True, data)
