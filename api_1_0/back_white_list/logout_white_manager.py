# -*- coding: utf-8 -*-
# @Time    : 2020-08-03 14:10
# <AUTHOR> wu
# @File    : customize_ioc_manager.py
# @Software: PyCharm
"""
自定义日志白名单  管理
"""
import os
import sqlite3
import xlrd
from flask import request
from flask_restful import Resource
from config.config import WhiteListCfg, NdrLog
from utils.database import get_es_client
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response
from utils.cluster import *
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class LogOutWhiteResource(Resource):
    def __init__(self):
        self.sqlite = sqlite3.connect(WhiteListCfg.DbPath)
        self.cursor = self.sqlite.cursor()

    def __del__(self):
        try:
            self.sqlite.close()
        except:
            pass


class LogOutWhiteImport(LogOutWhiteResource):
    """
    日志白名单导入处理
    """

    def __init__(self):
        super(LogOutWhiteImport, self).__init__()

    def __del__(self):
        super(LogOutWhiteImport, self).__del__()

    def post(self):
        """
        :return:
        """
        file_obj = request.files["fileName"]
        if not os.path.exists(WhiteListCfg.DbPath):
            LOG.error("The white list database file does not exist.")
            ndr_log_to_box(NdrLog.Type.OPERATE, "白名单数据库文件不存在。")
            return flask_response("The white list file does not exist.", False, {"errorCode": 409})
        try:
            file_obj.save(WhiteListCfg.ImportExcelPath + "/" + file_obj.filename)
        except Exception as error:
            LOG.error("logout_white file [%s] import failed.Reason: %s。" % (file_obj.filename, str(error)))
            ndr_log_to_box(NdrLog.Type.OPERATE, "[%s]导入失败：%s。" % (file_obj.filename, str(error)))
            return flask_response(str(error), False, {})
        data = xlrd.open_workbook(WhiteListCfg.ImportExcelPath + "/" + file_obj.filename)
        table = data.sheet_by_index(0)
        try:
            for num in range(table.nrows):
                if num > 0:
                    parm = []
                    parm += table.row_values(num)
                    parm_str = str(parm)
                    parm_str = str(parm_str[1: -1])
                    try:
                        self.cursor.execute(
                            "INSERT INTO logout_white (target, type, tag) VALUES (%s)" % parm_str)
                        self.sqlite.commit()
                    except Exception as error:
                        LOG.error("Import failed.Reason: %s." % str(error))
                        continue
            add_count = self.sqlite.total_changes
        except Exception as error:
            LOG.error("logout_white import failed.Reason: %s." % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "[%s]导入失败：%s。" % (file_obj.filename, str(error)))
            return flask_response(str(error), False, {})

        LOG.info("logout_white file [%s] import successfully." % file_obj.filename)
        ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单[%s]导入成功。数量：%d" % (file_obj.filename, add_count))
        cluster_sync_file(WhiteListCfg.DbPath)
        return flask_response("logout_white import successfully.Count: %d" % add_count, True, {})


class LogOutWhite(LogOutWhiteResource):
    """ 日志白名单 处理（增、删、改、查） """

    def __init__(self):
        super(LogOutWhite, self).__init__()

    def __del__(self):
        super(LogOutWhite, self).__del__()

    # 修改日志白名单
    @param_check.check_flask_args(Validator('logout_white_update_schema'), request)
    def put(self, **kwargs):
        """
        :return:
        """
        target = "'%s'" % kwargs["target"]
        tag = "'%s'" % kwargs["tag"]
        try:
            sql = "UPDATE logout_white set tag=%s WHERE target=%s" % (tag, target)
            self.cursor.execute(sql)
            self.sqlite.commit()
            total = self.sqlite.total_changes
        except Exception as error:
            LOG.error("logout_white update failed.Reason：%s。" % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单[%s]修改失败：%s。" % (target, str(error)))
            return flask_response(str(error), False, {})

        if total:
            LOG.info("logout_white [%s] update successfully." % target)
            ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单[%s]修改成功。" % target)
            cluster_sync_file(WhiteListCfg.DbPath)
            return flask_response("logout_white [%s] update successfully." % target, True, {})
        LOG.info("logout_white update failed.Reason: [%s] does not exist." % target)
        ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单修改失败：[%s]不存在。" % target)
        return flask_response("logout_white update failed.Reason: [%s] does not exist." % target, True, {})

    # 删除日志白名单,仅支持单条删除或所有删除
    @param_check.check_flask_args(Validator('logout_white_delete_schema'), request)
    def delete(self, **kwargs):
        """
        :return:
        """
        target = kwargs["target"]
        try:
            if target:
                self.cursor.execute("DELETE from logout_white where target='%s';" % target)
            else:
                self.cursor.execute("DELETE from logout_white;")
            self.sqlite.commit()
            del_count = self.sqlite.total_changes

            if del_count >= 1:
                ndr_log_to_box(NdrLog.Type.OPERATE, "所有日志白名单删除成功。" if del_count > 1 else "日志白名单[%s]删除成功。" % target)
                cluster_sync_file(WhiteListCfg.DbPath)
                return flask_response("logout_white delete successfully.Del count:%d." % del_count, True, {})

            if target:
                ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单删除失败：[%s]不存在。" % target)
                return flask_response("Delete failed.Reason:[%s] does not exist." % target, True, {})
            ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单[%s]删除失败：不存在日志白名单。" % target)
            return flask_response("Delete failed.Reason: [%s] exist." % target, True, {})
        except Exception as error:
            ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单删除失败：%s。" % str(error))
            return flask_response("logout_white delete failed.Reason:%s" % str(error), False, {})

    # 获取所有日志白名单
    @param_check.check_flask_args(Validator('logout_white_get_all_schema'), request)
    def get(self, **kwargs):
        """
        :return:
        """
        target = kwargs["target"]
        target_type = kwargs["type"]
        tag = kwargs["tag"]
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])
        parm = ""
        if target:
            parm += "target like '%%%s%%' and " % target
        if target_type:
            parm += "type='%s' and " % target_type
        if tag:
            parm += "tag like '%%%s%%' and " % tag
        if parm != "":
            parm = "where " + parm[0: -5]
        parm1 = parm
        parm += "limit %d offset %d" % (page_size, (page - 1) * page_size)
        self.cursor.execute("SELECT * from logout_white %s;" % parm)
        white_list_res = self.cursor.fetchall()
        self.cursor.execute("SELECT count(*) from logout_white %s" % parm1)
        white_list_count = self.cursor.fetchone()
        detail = []
        for row in white_list_res:
            temp = {
                "target": row[0],
                "type": row[1],
                "tag": row[2]
            }
            detail.append(temp)

        data = {
            "total": white_list_count[0],
            "detail": detail,
            "page": page,
            "pageSize": page_size
        }
        return flask_response("", True, data)

    @param_check.check_flask_args(Validator('logout_white_add_schema'), request)
    def post(self, **kwargs):
        """
        :return:
        """
        target_list = kwargs["target"]
        if not target_list:
            return flask_response("白名单不能为空", False, {})
        target_type = kwargs["type"]
        if not target_type:
            return flask_response("白名单类型不能为空", False, {})
        tag = str(kwargs["tag"])
        try:
            for target in target_list:
                if target:
                    parm_str = "'%s'" % target + ",'%s'" % target_type + ",'%s'" % tag
                    self.cursor.execute("REPLACE INTO logout_white VALUES (%s)" % parm_str)
            self.sqlite.commit()
        except Exception as error:
            LOG.error("logout_white add failed.Reason：%s" % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单添加失败：%s。" % str(error))
            return flask_response("logout_white add failed", False, {})
        LOG.info("logout_white add successfully")
        ndr_log_to_box(NdrLog.Type.OPERATE, "日志白名单[%s]添加成功。" % str(target_list))
        cluster_sync_file(WhiteListCfg.DbPath, WhiteListCfg.DbPath)
        return flask_response("logout_white add successfully.", True, {})
