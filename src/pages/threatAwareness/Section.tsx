import React from 'react';
import cs from 'classnames';
import { PieChartTwoTone } from '@ant-design/icons';
import style from './index.less';

export type SectionProps = {
  title: string;
  className?: string;
  children?: React.ReactNode
}

export const Section = ({ title, children, className, }: SectionProps) => {
  return (
    <div className={style.overview_section}>
      <div className="py-2">
        <PieChartTwoTone className={style.section_icon} />
        <span className={style.section_title}>{title}</span>
      </div>
      <div className={cs(style.section_content, className)}>
        {children}
      </div>
    </div>
  );
};
