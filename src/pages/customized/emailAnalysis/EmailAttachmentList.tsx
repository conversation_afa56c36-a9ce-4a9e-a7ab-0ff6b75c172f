import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import moment from 'moment';
import cs from 'classnames';
import { Button, Divider, Popover, Space, Table, Tag, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Link } from 'umi';

import QueryBox from './QueryBox';
import { EmailAttachmentItem, TimeRange } from '../types';
import { useEmailAttachmentListData } from './hooks';
import { renderTags } from './EmailList';
// import { useConstantOptions } from '@/pages/threatAwareness/hooks';
import { SECURITY_COLORS } from '@/pages/threatAwareness/constant';
import { parseQueryString } from '@/utils/utils';

import styleModule from '../style.less'

export type Props = {
  timeRange: TimeRange
  onJumpSearch?: (key: string, params: any) => void
  style?: React.CSSProperties;
  className?: string;
}

function EmailAttachmentList({
  style,
  className,
  timeRange,
  onJumpSearch,
}: Props, ref: React.Ref<{ search?: (params: any) => void }>): JSX.Element {
  // const { serverirtyMap } = useConstantOptions();
  const severityMap: Record<number, string> = {
    1: '低危',
    2: '中危',
    3: '高危',
  }
  const controllerRef = useRef<AbortController>()

  const handleAbort = (controller: AbortController) => {
    controllerRef.current = controller
  }

  const {
    list,
    loading,
    pagination: paginationData,
    setSort,
    updatePagination,
    setTimeRange,
    setQueryParams,
  } = useEmailAttachmentListData(timeRange, handleAbort);

  const searchBoxRef = useRef<{ search: (params: any) => void }>(null)

  useEffect(() => {
    setTimeRange(timeRange)
  }, [timeRange])

  useImperativeHandle(ref, () => ({
    search: (params: any) => {
      controllerRef.current?.abort()
      searchBoxRef.current?.search(params)
    }
  }));

  const columns = [
    {
      title: "最近检测时间",
      width: 200,
      fixed: 'left',
      dataIndex: "timestamp",
      render: (t: number) => moment(t).format('YYYY-MM-DD HH:mm:ss'),
      sorter: {
        compare: (a: EmailAttachmentItem, b: EmailAttachmentItem) => a.timestamp - b.timestamp,
      },
    },
    {
      title: "文件名/MD5",
      width: 200,
      dataIndex: "filename",
      render: (filename: string, record: EmailAttachmentItem) => {
        return (
          <>
            <Button
              type="link"
              style={{ padding: 0 }}
              onClick={() => {
                searchBoxRef.current?.search({
                  filename
                })
              }}
            >
              <Tooltip placement="topLeft" title={filename} >
                <span className='w-full text-left truncate'>{filename}</span>
              </Tooltip>
            </Button>
            <div>{record.md5}</div>
          </>

        )
      }
    },
    {
      title: "检测结果",
      width: 250,
      dataIndex: "threat",
      render: (threat: string[], { threatName }: EmailAttachmentItem) => {
        return <Space direction='vertical'>
          <Tag color='red'>{threatName}</Tag>
          {renderTags(threat, 4)}
        </Space>
      },
    },
    {
      title: "发件人",
      width: 200,
      dataIndex: "from",
      render: (t: string) => t || '-',
    },
    {
      title: "收件人",
      width: 260,
      dataIndex: "to",
      render: (to: string[]) => {
        return <div>收件人: {renderTags(to, 1)}</div>
      },
    },
    {
      title: "邮件ID/主题",
      width: 200,
      dataIndex: "subject",
      render: (subject: string, record: EmailAttachmentItem) => (
        <>
          <Button
            type="link"
            className='w-full'
            style={{ padding: 0 }}
            onClick={() => searchBoxRef.current?.search({ msg_id: record.msg_id })}
          >
            <Tooltip placement="topLeft" title={record.msg_id} >
              <span className='w-full text-left truncate'>{record.msg_id}</span> 
            </Tooltip>
          </Button>
          <Tooltip placement="topLeft" title={subject} >
            <div className='line-clamp-2'>{subject}</div>
          </Tooltip>

        </>
      ),
    },
    {
      title: '威胁等级',
      width: 110,
      fixed: 'right',
      align: 'center',
      dataIndex: "severity",
      render: (v: number) => <Tag className={styleModule.severity_tag} color={SECURITY_COLORS[v]}>{severityMap[v]}</Tag>,
    },
    {
      title: "操作",
      width: 130,
      align: 'center',
      fixed: 'right',
      key: "action",
      render: (_: never, record: EmailAttachmentItem) => {
        const { uuid } = record

        return (
          <Space split={<Divider type="vertical" />}>
            <Link
              className="!px-0"
              target="_blank"
              to={{
                pathname: '/app/mica/threatAnalysis/emailAnalysis/emailAttachmentDetail',
                search: `${parseQueryString({ ...timeRange, uuid, })}`,
              }}
            >
              威胁报告
            </Link>
          </Space>
        );
      }
    }
  ];

  const totalInfoRender = (total: number) => (
    <span>
      <Popover
        content="最多展示10000条数据"
        title="说明"
        trigger="hover"
      >
        <InfoCircleOutlined className="pr-2" />
      </Popover>
      总计 {total} 条
    </span >
  );

  const pagination = {
    total: paginationData.total,
    showSizeChanger: true,
    showQuickJumper: true,
    current: paginationData.page,
    showTotal: totalInfoRender,
    pageSize: paginationData.pageSize,
    onChange: (page: number, pageSize: number) => updatePagination({ page, pageSize }),
  };

  return (
    <div style={style} className={cs(className)}>
      <QueryBox ref={searchBoxRef} listType="emailAttachmentList" onSearch={setQueryParams} timeRange={timeRange} />
      <Table
        loading={loading}
        rowKey="uuid"
        columns={columns as any[]}
        scroll={{ x: 1200, }}
        dataSource={list}
        pagination={pagination}
        onChange={(_, __, sorter) => {
          const { order } = sorter as any
          setSort?.(order?.replace('end', ''))
        }}
      />
    </div>
  );
}

export default forwardRef(EmailAttachmentList);
