{
	"type": "object",
	"required": [],
	"properties": {
		"ip_addr": {
			"type": "string",
			"format": "ipv4|ipv6",
			"default": ""
		},
		"name": {
			"type": "string",
			"default": ""
		},
		"is_importent": {
		    "type": "string",
		    "default": ""
		},
		"is_outer": {
		    "type": "string",
		    "default": ""
		},
		"assets_num": {
			"type": "string",
			"default": ""
		},
		"pageSize": {
			"type": "string",
			"pattern": r'''[1-9]{1}\d{0,3}''',
			"default": "10"
		},
		"page": {
			"type": "string",
			"pattern": r'''[1-9]{1}\d{0,8}''',
			"default": "1"
		}
	}
}