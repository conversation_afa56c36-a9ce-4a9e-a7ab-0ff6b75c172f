# -*- coding: utf-8 -*-
# @Time    : 2019-06-18 10:10
# <AUTHOR> wu
# @File    : auths.py
# @Software: PyCharm

"""module explore"""

import time
import datetime
import jwt
from flask import request
from flask import make_response
from flask import jsonify
from flask_restful import Resource
from werkzeug.security import check_password_hash
from config.config import UserConfig, NdrLog
from config.authority import OPERATOR, AUDITOR
from utils.database import MongoDB
from utils.param_check import Validator, check_flask_args
from utils.logger import get_ndr_logger, LogToDb
from utils.utils import flask_response

LOG = get_ndr_logger(NdrLog.Type.LOGIN, __file__)


def init_online_user():
    """init online user"""
    mongodb = MongoDB('ndr')
    users = mongodb.find('user', {})
    for user in users:
        mongodb.update('user', {'id': user['id']}, {'status': 'offline'})
    return 0


def check_password(username, password):
    """check password"""
    mongodb = MongoDB("ndr")
    user = mongodb.find_one('user', {'name': username})
    if user is None:
        rst = False, 'User does not exist'
    else:
        ret = check_password_hash(user['password'], password)
        if ret is True:
            rst = True, ''
        else:
            rst = False, 'Wrong password'
    return rst


class Login(Resource):
    """login"""
    @check_flask_args(Validator("auth_login_schema"), request)
    def post(self, **kwargs):
        """用户登录"""
        name = kwargs["args"]["username"]
        password = kwargs["args"]["password"]
        if not name or not password:
            LOG.error("Login failed.Username or password is empty.")
            return flask_response('Username or password cannot be empty.', False, {})
        body = Authenticate(request.headers.get('X-Real-IP')).authenticate(name, password)
        if body['flag']:
            data = make_response(jsonify(body))
            data.set_cookie('token', body['data']['token'], max_age=86400)
            LogToDb().write_to_db(name, 'login', '用户[%s]登录成功.' % name, user_ip=request.headers.get('X-Real-IP'))
            return data
        return flask_response('Login failed.%s' % body['message'], False, {})


class Logout(Resource):
    """logout"""

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.body = {
            "message": "",
            "flag": False,
            "data": {}
        }

    @check_flask_args(Validator("auth_logout_schema"), request)
    def post(self, **kwargs):
        """logout"""
        user_id = kwargs["userId"]
        name, _, user_ip = Authenticate().get_current_user()
        body = Authenticate().identify()
        if body['flag']:
            user = self.mongodb.find_one('user', {'id': user_id})
            if user is None:
                LOG.error("Logout failed. User does not exist")
                LogToDb().write_to_db(name, 'login', '用户[%s]退出失败：用户不存在。' % user_id)
                return flask_response('User does not exist', False, {})
            Authenticate().encode_auth_token(user['id'], expired_time=1)
            self.mongodb.update('user', {'id': user['id']}, {'status': 'offline'})
            LOG.info("%s logout success." % user['name'])
            data = make_response(jsonify(body))
            data.delete_cookie('token')
            LogToDb().write_to_db(name, 'login', '用户[%s]退出成功.' % name, user_ip)
            return flask_response('Logout success.', True, {})
        LogToDb().write_to_db(name, 'login', '用户[%s]退出失败.%s' % (user_id, body["message"]), user_ip)
        LOG.error("Logout failed.Reason: %s" % body["message"])
        return flask_response(body["message"], False, {})


class Authenticate():
    """Authenticate"""
    def __init__(self, user_ip='127.0.0.1'):
        self.mongodb = MongoDB("ndr")
        self.user_ip = user_ip if user_ip else '127.0.0.1'
        self.response = {
            "message": "",
            "flag": False,
            "data": {}
        }

    @staticmethod
    def encode_auth_token(user_id, login_time=time.time(), expired_time=86400, status='offline'):
        """生成认证Token"""
        try:
            payload = {
                'exp': datetime.datetime.utcnow() + datetime.timedelta(days=0, seconds=expired_time),
                'iat': datetime.datetime.utcnow(),
                # 'iss': 'ken',
                'data': {
                    'id': user_id,
                    'login_time': login_time,
                    'status': status
                }
            }
            return jwt.encode(payload, UserConfig.Secret_Key, algorithm='HS256')
        except ValueError:
            return 'Parameter error'

    @staticmethod
    def decode_auth_token(auth_token):
        """验证Token"""
        try:
            payload = jwt.decode(auth_token, UserConfig.Secret_Key, options={'verify_exp': True})
            if 'data' in payload and 'id' in payload['data']:
                rst = payload
            else:
                raise jwt.InvalidTokenError
        except jwt.ExpiredSignatureError:
            rst = 'Token expired'
        except jwt.InvalidTokenError:
            rst = 'Token error'
        return rst

    def authenticate(self, name, password):
        """用户登录，登录成功返回token，将登录时间写入数据库；登录失败返回失败原因"""
        user = self.mongodb.find_one('user', {'name': name})
        if user is None:
            self.response['flag'] = False
            self.response['message'] = 'The user does not exist'
        elif not user['active']:
            self.response['message'] = 'The user is not activated'
            LOG.error("Login failed.%s" % self.response['data'])
        else:
            rst, _ = check_password(name, password)
            if rst:
                login_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
                self.mongodb.update('user', {'name': name},
                                    {'last_login_time': int(time.time() * 1000),
                                     'status': 'online',
                                     'user_ip': self.user_ip})
                token = self.encode_auth_token(user['id'], login_time, status='online')
                self.response['flag'] = True
                self.response['data'] = {'id': user['id'],
                                         'group': user['group'],
                                         'token': token.decode()}
                LOG.info("%s login success." % name)
            else:
                self.response['flag'] = False
                self.response['message'] = 'Wrong password'
                LOG.error("Login failed.%s" % self.response['message'])
        return self.response

    def identify(self):
        """用户鉴权"""
        header_token = request.headers.get('Authorization')
        cookie_token = request.cookies.get('token')

        if header_token or cookie_token:
            if header_token:
                auth_token = header_token
            else:
                auth_token = cookie_token
            payload = self.decode_auth_token(auth_token)
            if not isinstance(payload, str):
                user = self.mongodb.find_one('user', {'id': payload['data']['id']})
                if user is None:
                    self.response['message'] = 'Identify failed.User does not exist.'
                else:
                    if user['status'] == 'online':
                        # 权限分离判断
                        if user['group'] == 'administrator':
                            self.response['flag'] = True
                            LOG.info("Identify success.")
                        elif user['group'] == 'operator':
                            if str(request.url_rule) in OPERATOR:
                                self.response['flag'] = True
                                LOG.info("Identify success.")
                            else:
                                self.response['message'] = 'Insufficient permissions.'
                                LOG.error("Identify failed.%s" % self.response['message'])
                        elif user['group'] == 'auditor':
                            if str(request.url_rule) in AUDITOR:
                                self.response['flag'] = True
                                LOG.info("Identify success.")
                            else:
                                self.response['message'] = 'Insufficient permissions.'
                                LOG.error("Identify failed.%s" % self.response['message'])
                        else:
                            self.response['message'] = 'Insufficient permissions.'
                            LOG.error("Identify failed.%s" % self.response['message'])
                    else:
                        self.response['message'] = 'User is offline.'
                        LOG.error("Identify failed.%s" % self.response['message'])
            else:
                self.response['message'] = "Identify failed."
                self.response['data'] = payload
                LOG.error("Identify failed,please check parameter.%s" % self.response['data'])
        else:
            self.response['message'] = 'No authentication token provided.'
            LOG.error("Identify failed.%s" % self.response['data'])
        return self.response

    def get_current_user(self):
        """用户鉴权"""
        # header_token = request.headers.get('Authorization')
        # cookie_token = request.cookies.get('token')
        # if header_token or cookie_token:
        #     if header_token:
        #         auth_token = header_token
        #     else:
        #         auth_token = cookie_token
        #     payload = self.decode_auth_token(auth_token)
        #     if not isinstance(payload, str):
        #         user = self.mongodb.find_one('user', {'id': payload['data']['id']})
        #         if user:
        #             return user['name'], user["id"], user['user_ip']
        return "admin", "8543a91c8a2383dd1327a55c7154e25b", ""
