@import '~@/style/index.less';

.keyword {
  width: 400px;
  max-height: 80px;
  overflow: auto;
}

.eventDetail {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  padding: 16px;
  overflow-y: auto;
  background-color: #fff;

  .content {
    display: flex;
    align-items: center;
    width: 50%;
    padding: 5px 15px;
  }
}

.meansBox {
  padding: 20px;
}

.header {
  display: flex;
  padding-bottom: 20px;
}

.computer {
  display: block;
  width: 80px;
  height: 80px;
}

.head_ip {
  // padding-right: 20px;
  font-weight: bold;
  font-size: 18px;
}

.head_info {
  padding: 4px 0 0 20px;
}

.labels {
  margin: 0 10px;
  padding: 4px 6px;
  color: #fff;
  background-color: #40a9ff;
  border-radius: 6px;
}

.text_box {
  display: flex;
  padding-top: 4px;
}

.head_text {
  width: 142px;
  height: 21px;
  padding-right: 20px;
}

.top_info {
  height: 27px;
  // padding-bottom: 20px;
}

.head_btn {
  margin-left: auto;
  margin-top: 28px;
}

.cards_box {
  display: flex;
  justify-content: space-between;

  .card {
    width: 32%;

    .card_title {
      font-weight: bold;
      font-size: 18px;
    }

    .charts_box {
      display: flex;
      justify-content: space-between;

      .applicationBox {
        width: 200px;
        height: 200px;
      }

      .application {
        .application_row {
          display: flex;
          align-items: center;

          .legendCon {
            width: 130px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-right: 25px;

            .legend_name_10 {
              width: 50px;
              margin: 0;
            }
          }

          .circular {
            width: 8px;
            height: 8px;
            margin-right: 10px;
            border-radius: 50%;
            display: inline-block;
          }

          .pointer {
            cursor: pointer;
            color: #40a9ff;
          }
        }
      }

      .translate {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-bottom: 20px;

        .purple {
          color: purple;
          font-size: 24px;
          font-weight: bold;
        }
      }

      .send_box {
        width: 280px;
        height: 240px;
      }

      .send_legend {
        padding-top: 60px;

        .application_row {
          display: flex;
          align-items: center;

          .legendCon {
            width: 100px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-right: 10px;

            .legend_name_5 {
              width: 90px;
              margin: 0;
            }
          }

          .square {
            width: 6px;
            height: 6px;
            display: inline-block;
            transform: translateY(-1px);
            margin-right: 10px;
          }

          .pointer {
            cursor: pointer;
            color: #40a9ff;
          }
        }
      }
    }

    .ports {
      .ports_con {
        height: 130px;
        overflow: hidden;

        .port_active {
          display: inline-block;
          font-size: 28px;
          font-weight: bold;
          color: #1BC126;
          width: 25%;
        }

        .port_inactive {
          display: inline-block;
          font-size: 28px;
          font-weight: bold;
          width: 25%;
        }
      }

      .morePort {
        text-align: right;
        cursor: pointer;
        background: #fff;
        padding: 0 10px;
        color: #40a9ff;
        width: 100%;
        font-size: 12px;
        margin: 0;
      }
    }
  }
}

.bottom_charts {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;

  .card {
    width: 32%;

    .card_title {
      font-weight: bold;
      font-size: 18px;
      width: 100%;
    }

    .flex_between {
      display: flex;
      justify-content: space-between;

    }
  }
}

.card_port {
  max-height: 500px;

  .port_active {
    display: inline-block;
    font-size: 28px;
    font-weight: bold;
    color: #1BC126;
    width: 20%;
  }

  .port_inactive {
    display: inline-block;
    font-size: 28px;
    font-weight: bold;
    width: 20%;
  }
}


.blue {
  color: #40a9ff;
  padding-right: 16px;
}

.week_chart_box {
  width: 450px;
  height: 260px;
}

.threat_box {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 600px;
  padding-top: 40px;

  .timeline_box {
    display: flex;
    justify-content: space-around;

    .threat_ip {
      width: 110px;
      color: #005ce6;
      font-weight: bold;
    }

    .threat_item {
      width: 320px;
      padding: 2px;
      background: #ebedf0;
      // height: 76px;
      margin-right: 10px;

      .detail_item {
        width: 320px;
        background: #f2f4f5;
        padding: 10px;
        height: 96px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .time_count {
          display: inline-block;
          // padding-top: 14px;
          margin-right: 10px;
          font-size: 18px;
        }

        .time_content {
          width: 220px;
          color: #8080ff;

          .time_content_item {
            margin: 0;
          }
        }
      }
    }
  }
}

.count_color {
  font-size: 24px;
  color: #ff8000;
}

.pointer {
  cursor: pointer;
}

.group_btns {
  text-align: right;
}

:global {
  .ant-form-item {
    margin-bottom: 0;
  }

  .ant-select {
    .ant-select-selector {
      .scrollY;
      display: flex;
      align-items: flex-start;

      .ant-select-selection-item {
        align-items: center;
      }
    }
  }
}