import { SearchOutlined } from '@ant-design/icons';
import { pick } from 'lodash';
import { Button, Collapse, DatePicker, Form, Input, Select, message as Message } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import cn from 'classnames';
import { getFileStatistics } from '@/services/collect';
import FileNum from './FileComponents/fileNum';
import FileEcharts from './FileComponents/fileEcharts';
import FileList from './FileComponents/fileList';
import FileWord from './FileComponents/fileWord';
import FileTable from './FileComponents/fileTable';
import s from './style.less';
import { getQueryVariable } from '@/utils/utils';
import { getTaskName } from '@/services/common';
const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;

const AttacksAnalysis = () => {
  const celeryId = getQueryVariable('celeryId') || null;
  const [form] = Form.useForm();
  const [taskList, settaskList] = useState([]);

  const [params, setparams] = useState({ celeryId });
  const [chartsData, setchartsData] = useState<any>({});
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      if (value.time) {
        value.startTime = moment(value.time[0])
          .valueOf()
          .toString();
        value.endTime = moment(value.time[1])
          .valueOf()
          .toString();
        delete value.time;
        setparams(value);
        getFileStatistics(value).then(res => {
          if (res.flag) {
            const fileNum = pick(res.data, ['report_total', 'safe_total', 'low_risk', 'middle_risk', 'high_risk']);
            const fileTendency = res.data.report_tendency;
            const fileType = res.data.packet_type;
            const fileEngine = res.data.virus_engine;
            const fileFamily = res.data.high_risk_family;
            setchartsData({ fileNum, fileTendency, fileType, fileEngine, fileFamily });
          }
        });
      } else {
        Message.error(`请选择时间范围`);
      }
    });
  };
  useEffect(() => {
    getSelectTaskName();

    if (getQueryVariable('startTime') && getQueryVariable('endTime')) {
      const startTime = parseInt(getQueryVariable('startTime'));
      const stopTime = parseInt(getQueryVariable('endTime'));
      form.setFieldsValue({ time: [moment(startTime), moment(stopTime)] });
    }
    if (getQueryVariable('celeryId')) {
      form.setFieldsValue({ celeryId: getQueryVariable('celeryId') });
      onSubmit();
    }
  }, []);

  const getSelectTaskName = () => {
    getTaskName({}).then(res => {
      if (res.flag) {
        console.log('[  ]-70', res.data);
        settaskList(res.data);
      }
    });
  };

  return (
    <div style={{ height: 'calc(100vh - 74px)' }} className={cn('of-hidden flex flex-col')}>
      <Collapse
        defaultActiveKey={['1']}
        style={{
          minWidth: '800px',
        }}
      >
        <Panel header="搜索条件" key="1">
          <div className="grid items-start grid-cols-[90%_2fr]">
            <Form
              form={form}
              className={cn('!grid grid-cols-[28rem_2fr_2fr_2fr] gap-[0.5rem_0rem] of-hidden', s.form)}
              onFinish={onSubmit}
              layout="inline"
            >
              <Form.Item
                labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}
                className="of-hidden"
                label="时间范围"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请选择时间范围',
                  },
                ]}
              >
                <RangePicker
                  className="w-100%"
                  ranges={{
                    // 近一年: [moment().add(-1, 'year'), moment()],
                    // 近半年: [moment().add(-6, 'month'), moment()],
                    近一月: [moment().add(-1, 'month'), moment()],
                    近一周: [moment().add(-7, 'd'), moment()],
                    近一天: [moment().add(-1, 'd'), moment()],
                    今天: [moment().startOf('day'), moment().endOf('day')],
                    本周: [moment().startOf('week'), moment().endOf('week')],
                    本月: [moment().startOf('month'), moment().endOf('month')],
                    近三月: [moment().add(-3, 'month'), moment()],
                    // 本年度: [moment().startOf('year'), moment().endOf('year')],
                  }}
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </Form.Item>
              <Form.Item className="of-hidden" label="源IP" key="srcIp" name="srcIp">
                <Input />
              </Form.Item>
              <Form.Item className="of-hidden" label="目的IP" key="dstIp" name="dstIp">
                <Input />
              </Form.Item>
              <Form.Item className="of-hidden" label="文件名" key="fileName" name="fileName">
                <Input />
              </Form.Item>
              <Form.Item className="of-hidden" label="文件类型" key="fileType" name="fileType">
                <Input />
              </Form.Item>
              <Form.Item className="of-hidden" label="协议" key="protocol" name="protocol">
                <Input />
              </Form.Item>
              <Form.Item className="of-hidden" label="威胁等级" key="threatLevel" name="threatLevel">
                <Select allowClear>
                  <Option value="High">高</Option>
                  <Option value="Medium">中</Option>
                  <Option value="Low">低</Option>
                </Select>
              </Form.Item>
              <Form.Item className="of-hidden" label="任务名称" key="celeryId" name="celeryId">
                <Select
                  allowClear={true}
                  onChange={e => {
                    taskList.filter((item: any) => {
                      if (item.celeryId === e) {
                        form.setFieldsValue({ time: [moment(item.startTime), moment(item.endTime)] });
                      }
                    });
                  }}
                >
                  {taskList.map((item: any) => {
                    return (
                      <Option key={item.celeryId} value={item.celeryId}>
                        {item.taskName}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Form>
            <div className="group_btns">
              <Button className="searchBtn" type="primary" onClick={onSubmit} icon={<SearchOutlined />}>
                搜索
              </Button>
            </div>
          </div>
        </Panel>
      </Collapse>
      <div className={cn('mt-4 flex flex-col of-auto', s.fileContent)}>
        <div className={s.contentD1}>
          <FileNum data={chartsData.fileNum} />
          <FileEcharts
            className="h-22rem"
            domId="echarts1"
            type="bar"
            params={params}
            data={chartsData.fileTendency}
            key="fileTendency"
          />
        </div>
        <div className="flex mt2">
          <FileList className="w-30% mr-2 h-18.5rem" params={params} key="srcIP" type="srcIP" />
          <FileList className="mr-2 flex-1 h-18.5rem" params={params} key="url" type="url" />
          <FileEcharts className="flex-1 h-18.5rem" domId="echarts2" type="pie" data={chartsData.fileType} key="fileType" />
        </div>
        <div className="flex mt2">
          <FileList className="w-30% mr-2 h-18.5rem" params={params} key="dstIP" type="dstIP" />
          <FileList className="mr-2 flex-1 h-18.5rem" params={params} key="email" type="email" />
          <FileEcharts
            className="flex-1 h-18.5rem"
            domId="echarts3"
            type="pie"
            data={chartsData.fileEngine}
            key="fileEngine"
          />
        </div>
        <FileWord className="flex-1 mt2" data={chartsData.fileFamily} />
        <FileTable className="flex-1 mt2" params={params} key="report" />
      </div>
    </div>
  );
};
export default AttacksAnalysis;
