#!/usr/bin/env python3
# coding:utf-8

from rpyc.utils.registry import UDPRegistryClient

from cluster.settings import *
from cluster.utils.logger import get_logger
from cluster.modules.auth import MasterAuthenticator

logger = get_logger(__name__)

### TODO: 增加SSL保护，license 校验方式修改。
def connect(host, port=CLUSTER_SERVER_PORT):
    try:
        c = MasterAuthenticator()
    except:
        logger.exception('license file error')
        return None
    return c.connect(host, port)


def discover(service_name, registrar=None):
    if registrar is None:
        registrar = UDPRegistryClient(
            port=CLUSTER_REGISTRY_PORT, logger=get_logger('registry_client')
        )
    addrs = registrar.discover(service_name)
    if not addrs:
        return ()
    return addrs
