#!/usr/bin/env python3
# coding:utf-8

import sys

sys.path.insert(0, '/opt/ndr')

import json
import argparse

from cluster.api.manager import MyClusterManager
from host_celery.utils import MongoDB

example = '''
[
  {
    "node_name": "ndr53",
    "node_ip": "*************",
    "node_role": "master",
    "node_status": "online",
    "description": ""
  },
  {
    "node_name": "vmndr1",
    "node_ip": "**************",
    "node_role": "slave",
    "node_status": "online",
    "description": ""
  }
]
'''

example2 = '''
{ "node_name" : "ndr53", "if_index" : 0, "if_name" : "ens1f0", "pci_addr" : "0000:02:00.0", "speed" : 10, "work_mode" : "black_hole", "status" : "working", "link" : "down", "device" : "82599ES 10-Gigabit SFI/SFP+ Network Connection" }
{ "node_name" : "vmndr1", "if_index" : 0, "if_name" : "eth1", "pci_addr" : "0000:00:04.0", "speed" : 1, "work_mode" : "black_hole", "status" : "working", "link" : "down", "device" : "Virtio network device 1000"}
'''


def create_cluster(config):
    my_cluster_mgr = MyClusterManager()

    fp = open(config, 'r')
    nodes = json.load(fp)
    node_ips = []
    master_ip = ""
    for node in nodes:
        if node['node_role'] == 'master':
            master_ip = node['node_ip']
        else:
            node_ips.append(node['node_ip'])

    my_cluster_mgr.create_cluster(master_ip, node_ips)
    for node in nodes:
        my_cluster_mgr.set_node_info(node)

    mongodb = MongoDB("ndr")
    mongodb.drop("cluster")
    for node in nodes:
        mongodb.insert_one("cluster", node)

def create_interfaces(config):
    mongodb = MongoDB("ndr")
    mongodb.drop("interface_of_work")
    with open(config, 'r') as f:
        for line in f:
            intf = json.loads(line.strip())
            mongodb.insert_one("interface_of_work", intf)

def main():
    parser = argparse.ArgumentParser(description="Cluster Management Client Programme")

    # 创建子命令解析器
    subparsers = parser.add_subparsers(dest='command', required=True)

    cluster_parser = subparsers.add_parser('create-cluster', help='Create a cluster')
    cluster_parser.add_argument('--config', default='/etc/ksp/cluster/init.json', help='cluster configuration')

    interface_parser = subparsers.add_parser('import-interface', help='Import interfaces')
    interface_parser.add_argument('--config', default='/etc/ksp/cluster/interface.jsonline', help='interface configuration')

    args = parser.parse_args()
    if args.command == 'create-cluster':
        create_cluster(args.config)
    elif args.command == 'import-interface':
        create_interfaces(args.config)


if __name__ == "__main__":
    main()