# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : anomaly_alert_manager.py
# @Software: PyCharm

"""异常检测告警管理API"""

import json
import time
from datetime import datetime, timedelta
from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from utils.database import MongoDB
from utils.param_check import Validator, check_flask_args
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class AnomalyAlertList(Resource):
    """异常检测告警列表"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'anomaly_alerts'
    
    
    def get(self):
        """获取异常告警列表"""
        try:
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 10))
            status = request.args.get('status')
            severity = request.args.get('severity')
            start_time = request.args.get('start_time')
            end_time = request.args.get('end_time')
            
            # 构建查询条件
            query = {}
            if status:
                query['status'] = status
            if severity:
                query['severity'] = severity
            
            # 时间范围查询
            if start_time and end_time:
                start_dt = datetime.fromtimestamp(int(start_time) / 1000)
                end_dt = datetime.fromtimestamp(int(end_time) / 1000)
                query['alert_time'] = {'$gte': start_dt, '$lte': end_dt}
            
            # 分页查询
            skip = (page - 1) * page_size
            alerts = list(self.mongodb.find(
                self.collection, 
                query, 
                {'_id': 0}
            ).sort('alert_time', -1).skip(skip).limit(page_size))
            
            total = self.mongodb.count(self.collection, query)
            
            # 格式化时间戳
            for alert in alerts:
                if 'alert_time' in alert:
                    alert['alert_time'] = int(alert['alert_time'].timestamp() * 1000)
                if 'create_time' in alert:
                    alert['create_time'] = int(alert['create_time'].timestamp() * 1000)
                if 'update_time' in alert:
                    alert['update_time'] = int(alert['update_time'].timestamp() * 1000)
            
            result_data = {
                'alerts': alerts,
                'total': total,
                'page': page,
                'page_size': page_size
            }
            
            return flask_response('获取告警列表成功', True, result_data)
            
        except Exception as e:
            LOG.error(f"获取告警列表失败: {str(e)}")
            return flask_response(f'获取告警列表失败: {str(e)}', False, {})
    
    
    def put(self):
        """批量更新告警状态"""
        try:
            data = request.get_json()
            alert_ids = data.get('alert_ids', [])
            status = data.get('status')
            
            if not alert_ids or not status:
                return flask_response('缺少必需参数', False, {})
            
            # 批量更新
            update_data = {
                'status': status,
                'update_time': datetime.now()
            }
            
            if 'handled_by' in data:
                update_data['handled_by'] = data['handled_by']
            if 'notes' in data:
                update_data['notes'] = data['notes']
            
            result = self.mongodb.update_many(
                self.collection, 
                {'alert_id': {'$in': alert_ids}}, 
                {'$set': update_data}
            )
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"批量更新告警状态: {len(alert_ids)}条")
            LOG.info(f"批量更新告警状态成功: {result.modified_count}条")
            
            return flask_response(f'成功更新{result.modified_count}条告警', True, {})
            
        except Exception as e:
            LOG.error(f"批量更新告警失败: {str(e)}")
            return flask_response(f'批量更新告警失败: {str(e)}', False, {})


class AnomalyAlertStats(Resource):
    """异常告警统计"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'anomaly_alerts'
    
    
    def get(self):
        """获取告警统计信息"""
        try:
            # 获取最近24小时的告警统计
            now = datetime.now()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)
            
            # 最近24小时告警数
            alerts_24h = self.mongodb.count(self.collection, {
                'alert_time': {'$gte': last_24h}
            })
            
            # 最近7天告警数
            alerts_7d = self.mongodb.count(self.collection, {
                'alert_time': {'$gte': last_7d}
            })
            
            # 未处理告警数
            unhandled_alerts = self.mongodb.count(self.collection, {
                'status': 'pending'
            })
            
            # 高危告警数
            high_severity_alerts = self.mongodb.count(self.collection, {
                'severity': 'high',
                'alert_time': {'$gte': last_24h}
            })
            
            # 按严重程度统计最近7天
            severity_pipeline = [
                {'$match': {'alert_time': {'$gte': last_7d}}},
                {'$group': {'_id': '$severity', 'count': {'$sum': 1}}},
                {'$sort': {'_id': 1}}
            ]
            severity_stats = list(self.mongodb.aggs(self.collection, severity_pipeline))
            
            # 按模型类型统计最近7天
            model_pipeline = [
                {'$match': {'alert_time': {'$gte': last_7d}}},
                {'$group': {'_id': '$model_type', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}}
            ]
            model_stats = list(self.mongodb.aggs(self.collection, model_pipeline))
            
            # 按小时统计最近24小时
            hourly_pipeline = [
                {'$match': {'alert_time': {'$gte': last_24h}}},
                {'$group': {
                    '_id': {'$hour': '$alert_time'},
                    'count': {'$sum': 1}
                }},
                {'$sort': {'_id': 1}}
            ]
            hourly_stats = list(self.mongodb.aggs(self.collection, hourly_pipeline))
            
            # 格式化小时统计数据
            formatted_hourly_stats = []
            for i in range(24):
                hour_data = next((stat for stat in hourly_stats if stat['_id'] == i), None)
                formatted_hourly_stats.append({
                    'hour': i,
                    'count': hour_data['count'] if hour_data else 0
                })
            
            stats_data = {
                'alerts_24h': alerts_24h,
                'alerts_7d': alerts_7d,
                'unhandled_alerts': unhandled_alerts,
                'high_severity_alerts': high_severity_alerts,
                'severity_stats': severity_stats,
                'model_stats': model_stats,
                'hourly_stats': formatted_hourly_stats
            }
            
            return flask_response('获取告警统计成功', True, stats_data)
            
        except Exception as e:
            LOG.error(f"获取告警统计失败: {str(e)}")
            return flask_response(f'获取告警统计失败: {str(e)}', False, {})
