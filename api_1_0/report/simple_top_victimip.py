#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from utils import param_check
from flask import request
from flask_restful import Resource
from utils.param_check import Validator
from utils.utils import flask_response
from utils.database import get_es_client
from utils.es_function import get_country_term
from config.config import RULE_INDEX
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger

API_LOG = get_ndr_logger("api_log", __file__)
ES_LOG = get_ndr_logger('es_log', __file__)


class TopVictim(Resource):
    '''top受害者列表(前五)'''

    field_template = {
        "occurred": "occurredTime",
        "observed": "observedTime",
    }

    def __init__(self):
        self.es_template = ES_Template().read_template("report_top_ip")
        self.es_template["aggs"]["topIpList"]["terms"]["field"] = "rulesInfo.victimIp.keyword"

    @param_check.check_flask_args(Validator('report_top_ip_check'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["args"]["startTime"])
        stop_time = int(kwargs["args"]["stopTime"])
        self.sort = kwargs["args"]["sort"]
        timeMode = self.field_template[kwargs["args"]["timeMode"]]
        if start_time >= stop_time:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})
        search_time_array = {
            "range": {
                timeMode: {
                    "gte": start_time,
                    "lte": stop_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(search_time_array)
        if self.sort == "count":
            self.es_template["aggs"]["topIpList"]["terms"]["order"] = {"_count": "desc"}
        else:
            self.es_template["aggs"]["topIpList"]["terms"]["order"] = {"maxThreatScore": "desc"}
        country = kwargs["country"]
        if country:
            country_term = get_country_term(country)
            self.es_template["query"]["bool"]["must"].append(country_term)

        # 连接ES查询
        es_client = get_es_client()
        try:
            res = es_client.search(index=RULE_INDEX, body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response("", False, message)

        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, res):
        docs = list()
        for data in res["aggregations"]["topIpList"]["buckets"]:
            ip_dict = dict()
            ip_dict["ip"] = data["key"]
            ip_dict["triggerCount"] = data["doc_count"]
            ip_dict["threatScore"] = data["maxThreatScore"]["value"]
            docs.append(ip_dict)
        if self.sort == "score":
            docs = sorted(docs, key=self.sort_rule, reverse=True)

        return docs

    @staticmethod
    def sort_rule(data):
        # 最高权重分
        threatScore = data["threatScore"]
        # 权重
        lenth = len(str(data["triggerCount"]))
        # 权重等级分
        level = lenth / 100
        # 最低权重分
        score = data["triggerCount"] / 10 ** (lenth + 2)

        final_score = score + level + threatScore

        return final_score
