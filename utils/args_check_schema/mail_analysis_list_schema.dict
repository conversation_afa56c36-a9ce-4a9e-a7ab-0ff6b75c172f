{"type": "object", "required": ["start_time", "end_time"], "properties": {"start_time": {"type": "integer", "minimum": 0}, "end_time": {"type": "integer", "minimum": 0}, "page": {"type": "integer", "minimum": 1}, "pageSize": {"type": "integer", "minimum": 1, "maximum": 100}, "msg_id": {"type": "string"}, "threatLevel": {"type": "string"}, "to": {"type": "string"}, "filename": {"type": "string"}, "md5": {"type": "string"}}}