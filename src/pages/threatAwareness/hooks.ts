import { useCallback, useEffect, useState } from "react"
import { get, set } from 'lodash';
import { message } from "antd";
import moment from "moment";
import {
  DisposeThreatParams,
  OverviewStaticParams,
  SceneHaveReadParams,
  SceneQueryParams,
  addIgnoreItem,
  deleteIgnoreItems,
  disposeThreat,
  getAttackCountry,
  getAttackDirection,
  getConstOptions,
  getEventTrends,
  getIgnoreList,
  getKillChain,
  getOverviewStatic,
  getSceneList,
  getSceneNameList,
  getThreatLevel,
  getThreatNames,
  readSceneItem
} from "@/services/threatAwareness";
import { TimeRange } from "./UnSettledThreat/HighRiskHostCard";


export function useOverviewConstants() {
  const [loading, setLoading] = useState<boolean>(true);
  const { store, setItem, getItem } = useGlobalLocalStorage('threatAwareness')

  const fetchOptionConfig = async () => {
    try {
      if (!store || !Object.keys(store).length) {
        setLoading(true)
        const { data, flag } = await getConstOptions()
        if (flag) {
          setItem('', data)
        }
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  // useEffect(() => {
    // fetchOptionConfig()
  // }, []);

  return {
    store,
    setItem,
    getItem,
    loading
  }
}

export function useThreatData(initParams: OverviewStaticParams, onDiffData?: (value: object) => void) {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState();
  const [queryParams, setQueryParams] = useState(initParams);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [currentThreat, setCurrentThreat] = useState<{ ip: string, suggestion: string } | undefined>()

  const getHostStaticData = async (params: OverviewStaticParams = queryParams) => {
    try {
      setLoading(true)
      const { data: { list, count }, flag, message: msg } = await getOverviewStatic(params);
      if (flag) {
        setList(list)
        setTotal(count)
        setPage(1);
      } else {
        throw new Error(msg)
      }

      return count
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  const getStaticData = async (total: number) => {
    const lastTimeRange = getLastTimeRange(queryParams);
    const params = { ...queryParams, ...lastTimeRange }
    const { data: { count } } = await getOverviewStatic(params);
    return {
      total,
      diff: total - count
    }
  }

  const updateQueryParams = (params: Partial<OverviewStaticParams>) => {
    setQueryParams(prev => ({
      ...prev,
      ...params
    }))
  }

  const updateTimeRange = ({ start_time, end_time }: TimeRange) => {
    setQueryParams((prev) => {
      if (prev.start_time === start_time && prev.end_time === end_time) {
        return prev
      }
      return {
        ...prev,
        start_time,
        end_time
      }
    })
  }

  const ignoreIp = async (ip: string) => {
    try {
      const { flag, message: msg } = await addIgnoreItem({ ip })
      if (flag) {
        setTimeout(() => {
          getHostStaticData()
          message.success(msg)
        }, 1000);
      } else {
        throw new Error(msg)
      }
    } catch (error: any) {
      message.error(error.message)
    }
  }

  const updateThreat = async (data: DisposeThreatParams) => {
    try {
      const { flag, message: msg } = await disposeThreat(data);
      if (flag) {
        setTimeout(() => {
          getHostStaticData()
          message.success(msg)
        }, 1000);
      }
    } catch (error: any) {
      message.success(error.message)
    }
  }

  useEffect(() => {
    getHostStaticData(queryParams)
      .then((count) => {
        return getStaticData(count)
      })
      .then((value) => {
        onDiffData?.(value)
      })
  }, [queryParams])

  return {
    list,
    loading,
    total,
    page,
    currentThreat,
    setCurrentThreat,
    setPage,
    updateQueryParams,
    updateTimeRange,
    updateThreat,
    ignoreIp,
    getHostStaticData,
  }
}

export function useWeekTrendsData(type: string) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Record<string, number[]>>({
    timestamps: [],
    values: []
  })

  const getCurrentWeekData = async () => {
    try {
      const startOfWeek = moment().startOf('week');
      const weekDayTimestamps = [];

      // 生成7天的时间戳
      for (let i = 0; i < 7; i++) {
        const day = moment(startOfWeek).add(i, 'days');
        const startDate = day.startOf('day').valueOf(); // 开始时间戳
        const endDate = day.endOf('day').valueOf(); // 结束时间戳
        weekDayTimestamps.push({ start_time: startDate, end_time: endDate });
      }
      setLoading(true)
      const resultArray = await Promise.all(
        weekDayTimestamps.map((timeRange) => {
          return getOverviewStatic({
            sort_agg_field: type,
            status: '1',
            all_host: 1,
            ...timeRange,
          })
        })
      );

      const trendsData = {
        values: resultArray.map(({ data }) => data.count),
        timestamps: weekDayTimestamps.map(({ start_time }) => start_time)
      }

      setData(trendsData)
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getCurrentWeekData()
  }, [])

  return {
    loading,
    data,
  }
}

export function useOverviewSceneData(initParams: SceneQueryParams) {
  const [namesLoading, setNamesLoading] = useState(false);
  const [scenes, setScenes] = useState([]);
  const [currentScene, setCurrentScene] = useState<any>();

  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [queryParams, setQueryParams] = useState(initParams);
  const [total, setTotal] = useState(0);

  const getSceneName = async () => {
    try {
      setNamesLoading(true);
      const { data: { list }, flag } = await getSceneNameList({ page: 1, pageSize: 1000 })
      if (flag) {
        setScenes(list)
        if (list.length) {
          updateQueryParams({
            name: list[0].name
          })
        } else {
          setList([]);
          setTotal(0);
        }
      }
    } catch (error: any) {
      message.error(error.message);
    } finally {
      setNamesLoading(false)
    }
  }


  const getSceneListData = async (params: SceneQueryParams = queryParams) => {
    try {
      setLoading(true)
      const { data: { list, total }, flag, message: msg } = await getSceneList(params);
      if (flag) {
        setList(list)
        setTotal(total)
      } else {
        throw new Error(msg)
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  const updateQueryParams = (params: Partial<SceneQueryParams>) => {
    setQueryParams(prev => ({
      ...prev,
      ...params
    }))
  }

  const updateTimeRange = ({ start_time, end_time }: TimeRange) => {
    setQueryParams((prev) => {
      if (prev.start_time === start_time && prev.end_time === end_time) {
        return prev
      }
      return {
        ...prev,
        start_time,
        end_time
      }
    })
  }

  const readScene = async (data: SceneHaveReadParams) => {
    try {
      const { flag, message: msg } = await readSceneItem(data);
      if (flag) {
        setTimeout(() => {
          getSceneListData()
          message.success(msg)
        }, 1000);
      }
    } catch (error: any) {
      message.success(error.message)
    }
  }

  useEffect(() => {
    getSceneName();
  }, []);

  useEffect(() => {
    if (queryParams.name) {
      getSceneListData(queryParams)
    }
  }, [queryParams])

  return {
    list,
    loading,
    scenes,
    total,
    page: queryParams.page,
    namesLoading,
    currentScene,
    setCurrentScene,
    updateQueryParams,
    updateTimeRange,
    readScene,
    getSceneName,
  }
}

export function useKillChainData({ start_time, end_time }: { start_time: number, end_time: number }) {
  const [timeRange, updateTimeRange] = useTimeRange({ start_time, end_time })
  const [data, setData] = useState(useLocalStorage().get('threatAwareness.kill_chain') || []);
  const [loading, setLoading] = useState(false);
  const getChain = async () => {
    try {
      setLoading(true);
      const { data, flag } = await getKillChain(timeRange)

      if (flag) {
        const valueMap = data.reduce((acc: any, item: any) => {
          acc[item.key] = item.doc_count;
          return acc;
        }, {});

        setData((prev: any[]) => {
          return prev.map((item) => {
            return {
              ...item,
              value: valueMap[item.shortId] || 0
            }
          })
        })
      }
    } catch (error: any) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    getChain()
  }, [timeRange])

  return {
    loading,
    data,
    updateTimeRange
  }
}

export function useThreatNameData(timeRange: TimeRange) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({ names: [], total: [], success: [] });
  const [dataSort, setSort] = useState('all');
  const [range, setTimeRange] = useTimeRange(timeRange)

  const getThreatNamesData = async () => {
    try {
      setLoading(true)
      const { data, flag } = await getThreatNames({
        ...timeRange,
        sort: dataSort,
      })

      if (flag) {
        const result = data.reduce((acc: Record<string, any[]>, item: any) => {
          const { level: { buckets }, key: name, doc_count } = item;
          const valueMap = Object.fromEntries(buckets?.map(({ key, doc_count }: any) => [key, doc_count]) || [])
          acc.names.push(name || '未知');
          acc.total.push(doc_count || 0);
          acc.success.push(valueMap?.[3] || 0);
          return acc;
        }, { names: [], total: [], success: [] })
        setData(result)
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getThreatNamesData();
  }, [range, dataSort])

  return {
    loading,
    data,
    setSort,
    setTimeRange,
  }
}

export function useAttackCountryData(timeRange: TimeRange) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({ names: [], total: [], success: [] });
  const [dataSort, setSort] = useState('all');
  const [range, setTimeRange] = useTimeRange(timeRange)

  const getAttackCountryData = async () => {
    try {
      setLoading(true)
      const { data, flag } = await getAttackCountry({
        ...timeRange,
        sort: dataSort,
      })

      if (flag) {
        const result = data.reduce((acc: Record<string, any[]>, item: any) => {
          const { level: { buckets }, key: name, doc_count } = item;
          const valueMap = Object.fromEntries(buckets?.map(({ key, doc_count }: any) => [key, doc_count]) || [])
          acc.names.push(name || '未知');
          acc.total.push(doc_count || 0);
          acc.success.push(valueMap?.[3] || 0);
          return acc;
        }, { names: [], total: [], success: [] })
        setData(result)
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getAttackCountryData();
  }, [range, dataSort])

  return {
    loading,
    data,
    setSort,
    setTimeRange,
  }
}

export function useEventTrendsData(timeRange: TimeRange, onDiffData?: (value: object) => void) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({ timestamp: [], total: [], success: [], attempt: [], suspicious: [] });
  const [range, setTimeRange] = useTimeRange(timeRange);
  const attackStatus = useLocalStorage().get('threatAwareness.attack_status');


  const getEventTrendsData = async () => {
    try {
      setLoading(true)
      const { data, flag } = await getEventTrends(range)
      if (flag) {
        const result: any = { timestamp: [], total: [], success: [], attempt: [], suspicious: [] }
        data.forEach((item: any, dataIndex: number) => {
          if (item.key < range.start_time) {
            item.key = range.start_time
          }
          result.total.push(item.doc_count);  
          result.timestamp.push(item.key)

          attackStatus.forEach((status: any) => {
            const value = item.level.buckets.find(({ key }: any) => key === status.value)?.doc_count;
            result[status.name][dataIndex] = value || 0;
          })
        });
        setData(result)
        return result
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  const getStaticData = async (currentResult: any) => {
    const lastTimeRange = getLastTimeRange(range);
    const { data } = await getEventTrends(lastTimeRange);
    const lastResult: any = { timestamp: [], total: [], success: [], attempt: [], suspicious: [] }

    data?.forEach((item: any, dataIndex: number) => {
      lastResult.total.push(item.doc_count);
      lastResult.timestamp.push(item.key)

      item.level?.buckets?.forEach((bucket: any) => {
        const constant = attackStatus.find((state: any) => state.value === bucket.key);
        if (constant) {
          lastResult[constant.name][dataIndex] = bucket.doc_count;
        }
      });
    });

    const lastTotal: number = lastResult.total.reduce((acc: number, curr: number) => acc + curr, 0)
    const currentTotal: number = currentResult.total.reduce((acc: number, curr: number) => acc + curr, 0)
    const successTotal = currentResult.success.reduce((acc: number, curr: number) => acc + curr, 0)
    const suspiciousTotal = currentResult.suspicious.reduce((acc: number, curr: number) => acc + curr, 0)
    const attemptTotal = currentResult.attempt.reduce((acc: number, curr: number) => acc + curr, 0)

    return {
      total: currentTotal,
      diff: currentTotal - lastTotal,
      successTotal,
      suspiciousTotal,
      attemptTotal
    }
  }

  useEffect(() => {
    getEventTrendsData()
      .then((result) => {
        return getStaticData(result)
      }).then((value) => {
        onDiffData?.(value)
      })
  }, [range])

  return {
    loading,
    data,
    setTimeRange,
  }
}


export function useAttackDirectionData(timeRange: TimeRange) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [range, setTimeRange] = useTimeRange(timeRange)
  const visitDirection = useLocalStorage().get('threatAwareness.visit_direction') || [];
  const visitDirectionMap = Object.fromEntries(visitDirection.map(({ nameCn, value }: any) => [value, nameCn]))

  const getAttackDirectionData = async () => {
    try {
      setLoading(true)
      const { data, flag } = await getAttackDirection(timeRange)

      if (flag) {
        const result = data.map(({ doc_count, key }: any) => {
          const name = visitDirectionMap[key] || '其他';
          return {
            name,
            value: doc_count
          }
        })
        setData(result)
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getAttackDirectionData();
  }, [range])

  return {
    loading,
    data,
    setTimeRange,
  }
}

export function useThreatLevelData(timeRange: TimeRange) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [range, setTimeRange] = useTimeRange(timeRange)
  const serverirty = useLocalStorage().get('threatAwareness.serverirty') || [];

  const getLevelData = async () => {
    try {
      setLoading(true)
      const { data, flag } = await getThreatLevel(timeRange)

      if (flag) {
        const result = data.map(({ doc_count, key }: any) => {
          const name = serverirty.find(({ value }: any) => value === key).nameCn;
          return {
            name,
            value: doc_count
          }
        })
        setData(result)
      }
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getLevelData();
  }, [range])

  return {
    loading,
    data,
    setTimeRange,
  }
}


export function useIgnoreList() {
  const [loading, setLoading] = useState(false)
  const [list, setList] = useState([])
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
  })
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [total, setTotal] = useState(0)

  const getIgnoreListData = async () => {
    try {
      setLoading(true);
      const { data: { list, total }, flag } = await getIgnoreList(pagination)
      if (flag) {
        setList(list);
        setTotal(total)
      }
    } catch (error: any) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  }

  const ignoreItemsDelete = async (ids: string[]) => {
    try {
      setConfirmLoading(true)
      const { flag, message: msg } = await deleteIgnoreItems({ ids })
      if (flag) {
        message.success(msg);
        setPagination(prev => ({
          ...prev,
          page: 1
        }))
      } else {
        throw new Error(msg)
      }
      return flag
    } catch (error: any) {
      message.error(error.message);
    } finally {
      setConfirmLoading(false)
    }
  }

  useEffect(() => {
    getIgnoreListData();
  }, [pagination])


  return {
    list,
    loading,
    confirmLoading,
    total,
    pagination,
    setPagination,
    ignoreItemsDelete,
  }
}


export function useTimeRange(range: TimeRange): [TimeRange, (range: TimeRange) => void] {
  const [timeRange, setTimeRange] = useState(range);

  function updateTimeRange({ start_time, end_time }: TimeRange) {
    setTimeRange((prev) => {
      if (prev.start_time === start_time && prev.end_time === end_time) {
        return prev
      }
      return {
        ...prev,
        start_time,
        end_time
      }
    })
  }

  return [timeRange, updateTimeRange]

}


export function getLastTimeRange(timeRange: TimeRange) {
  const { start_time, end_time } = timeRange
  const diff = end_time - start_time;

  return {
    start_time: start_time - diff,
    end_time: start_time - 1,
  }
}

export function useLocalStorage() {
  const getValue = (key: string) => {
    try {
      const [firstKey, ...restKey] = key.split('.')
      const storedValue = JSON.parse(localStorage.getItem(firstKey) || '{}');
      if (!restKey.length) {
        return storedValue
      }
      const value = get(storedValue, restKey.join('.'))
      return value;
    } catch (error) {
      return null;
    }
  };

  const setValue = (key: string, value: any) => {
    try {
      const [firstKey, ...restKey] = key.split('.');
      const object = getValue(firstKey);
      if (!object) {
        if (Array.isArray(value)) {
          localStorage.setItem('key', JSON.stringify(value))
        } else {

        }
      }
      set(object, restKey?.join('.'), value);
      return value;
    } catch (error) {
      return null
    }
  };

  return { get: getValue, set: setValue };
}

export function useThreatNameOptions() {
  const category = useLocalStorage().get('info.category') || [];
  const threatNames = useLocalStorage().get('info.tethreat_name') || [];
  const threatNamesMap = Object.fromEntries(Object.entries(threatNames).map(([value, key]) => [key, value]));

  const options = Object.entries(category).map(([parentName, childrenNames]: any) => {
    return {
      label: parentName,
      value: (childrenNames as string[]).map((name: string) => threatNamesMap[name]).join(),
      children: childrenNames.map((childName: string) => ({
        label: childName,
        value: threatNamesMap[childName],
      }))
    };
  });

  return options
}


export const useGlobalLocalStorage = (storageKey: string) => {
  const [localStorageData, setLocalStorageData] = useState(() => {
    const storedData = localStorage.getItem(storageKey);
    return storedData ? JSON.parse(storedData) : {};
  });

  const setItem = useCallback((path, value) => {
    setLocalStorageData((prevData: any) => {
      if (!(path.trim())) {
        const data = {
          ...prevData,
          ...value
        }
        localStorage.setItem(storageKey, JSON.stringify(data));

        return data
      }
      const pathKeys = path.split('.');
      const newData = { ...prevData };
      let currentLevel = newData;
      for (let i = 0; i < pathKeys.length - 1; i++) {
        const key = pathKeys[i];
        currentLevel[key] = currentLevel[key] || {};
        currentLevel = currentLevel[key];
      }

      const lastKey = pathKeys[pathKeys.length - 1];
      currentLevel[lastKey] = value;

      localStorage.setItem(storageKey, JSON.stringify(newData));
      return newData;
    });
  }, [storageKey]);

  const getItem = useCallback((path) => {
    if (!path) return localStorageData
    const pathKeys = path.split('.');
    let currentLevel = localStorageData;

    for (let i = 0; i < pathKeys.length; i++) {
      const key = pathKeys[i];
      currentLevel = currentLevel[key];

      if (currentLevel === undefined) {
        return undefined;
      }
    }

    return currentLevel;
  }, [localStorageData]);

  return { store: localStorageData, setItem, getItem };
};

export function useConstantOptions() {
  const { store } = useOverviewConstants()

  return {
    store,
    disposeOptions: Object.entries(store.dispose_status || []).map(([value, label]) => ({ label, value })),
    serverirtyMap: store.serverirty?.reduce((acc: any, { nameCn, value }: any) => {
      acc[value] = nameCn;
      return acc;
    }, {}),
    typeOptions: Object.entries(store.all_host || []).map(([value, label]) => ({ label, value })),
    killChainList: store.kill_chain
  }

}