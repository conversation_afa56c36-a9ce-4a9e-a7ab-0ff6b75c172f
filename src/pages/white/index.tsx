/*
 * @Author: 田浩
 * @Date: 2021-09-30 14:14:56
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-18 14:46:01
 * @Descripttion:
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Collapse,
  Col,
  Row,
  Button,
  Input,
  Select,
  Table,
  Popconfirm,
  message,
  Modal,
  Upload,
  Progress,
} from 'antd';
import { SearchOutlined, UploadOutlined } from "@ant-design/icons";
import {
  getWhiteData,
  delWhiteData,
  addWhite,
  editWhite,
} from '@/services/whitelist';
import { WhiteType } from '@/utils/enumList';
import EditForm from './editForm';

const { Panel } = Collapse;
const { Option } = Select;

const Index = (props: any) => {
  const [form] = Form.useForm();
  const childForm = useRef<any>({});
  const [total, settotal] = useState(0);
  const [tableData, settableData] = useState([]);
  const [params, setparams] = useState({
    page: 1,
    pageSize: 10,
  });
  const [isAdd, setisAdd] = useState(false);
  const [visible, setvisible] = useState(false);
  const [formValue, setformValue] = useState({});
  const [uploadVisible, setuploadVisible] = useState(false);
  const [defaultPercent, setdefaultPercent] = useState(0);
  const columns = [
    {
      title: '白名单值',
      dataIndex: 'target',
    },
    {
      title: '类型',
      dataIndex: 'type',
    },
    {
      title: '备注',
      dataIndex: 'tag',
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              type="link"
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定删除吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={() => handleDel(record)}
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];
  useEffect(() => {
    getTableData();
  }, [params]);
  const getTableData = () => {
    getWhiteData(params).then(res => {
      if (res.flag) {
        settableData(res.data.detail);
        settotal(res.data.total);
      }
    });
  };
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      setparams({ ...params, ...value, page: 1, pageSize: 10 });
    });
  };
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: params.pageSize,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const UpdateProps = {
    name: 'fileName',
    action: '/mica-api/api/v1/whitelist/import',
    showUploadList: false,
    onChange: (info: {
      file: {
        status: string;
        percent: number;
        response: any;
      };
    }) => {
      if (info.file.status === 'uploading') {
        toggleVisibleStatusTrue(Math.floor(info.file.percent));
      } else if (!info.file.response.flag) {
        setuploadVisible(false);
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        return;
      } else if (info.file.status === 'done' && info.file.response.flag) {
        setTimeout(() => {
          if (info.file.response.data.failed_total) {
            message.warning(
              `${info.file.response.message},${info.file.response.data.failed_total}`,
            );
          } else {
            message.success(info.file.response.message);
            setuploadVisible(false);
            getTableData();
          }
        }, 3000);
      } else if (info.file.status === 'error') {
        setuploadVisible(false);
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
      }
    },
    beforeUpload: (file: any) => {
      let pos = file.name.lastIndexOf('.');
      let result = file.name.substring(pos + 1, file.name.length);
      if (!['xls', 'xlsx'].includes(result)) {
        message.error('仅支持上传xls/xlsx格式的文件');
        return Upload.LIST_IGNORE;
      }
    },
  };
  // 展示进度条
  const toggleVisibleStatusTrue = (percent: any) => {
    setuploadVisible(true);
    setdefaultPercent(percent);
  };
  const handleAdd = () => {
    setisAdd(true);
    setformValue({});
    setvisible(true);
    // let count = Object.keys(childForm.current).length;
    // if (count) {
    // }
  };
  const handleEdit = (value: React.SetStateAction<{}>) => {
    setformValue(value);
    setisAdd(false);
    setvisible(true);
  };
  const handleDel = (value: { target: any }) => {
    delWhiteData({ target: value.target }).then(res => {
      if (res.flag) {
        getTableData();
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    });
  };
  const handleOk = () => {
    childForm.current.validateFields().then((value: any) => {
      if (isAdd) {
        addWhite(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            childForm.current.resetFields();
            setvisible(false);
            getTableData();
          } else {
            message.error(res.message);
          }
        });
      } else {
        editWhite(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            childForm.current.resetFields();
            setvisible(false);
            getTableData();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };
  const handleCancel = () => {
    childForm.current.resetFields();
    setvisible(false);
  };
  const formItemLayout = {
    labelCol: {
      span: 9, // * ≥576px
    },
    wrapperCol: {
      span: 15,
    },
  };
  return (
    <div style={{ background: '#fff' }}>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="告警日志白名单" key="1">
          <Form
            {...formItemLayout}
            form={form}
            className="!flex gap-3 !mt-5"
            onFinish={onSubmit}
          >
            <Form.Item label="白名单值" name="target">
              <Input allowClear placeholder='请输入白名单值' />
            </Form.Item>
            <Form.Item label="类型" name="type">
              <Select allowClear placeholder='请选择类型'>
                {WhiteType.map(item => {
                  return (
                    <Option value={item.value} key={item.key}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item label="备注" name="tag">
              <Input allowClear placeholder='请输入备注' />
            </Form.Item>
            <Form.Item wrapperCol={{ span: 24 }}>
              <div className='flex gap-3 justify-end'>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Upload {...UpdateProps} accept=".xls, .xlsx">
                  <Button type="primary" ghost icon={<UploadOutlined />} >白名单导入</Button>
                </Upload>
                <Button onClick={handleAdd} type="primary" ghost>
                  自定义白名单
                </Button>
              </div>
            </Form.Item>
          </Form>
        </Panel>
      </Collapse>
      <Table
        rowKey={(r, i) => i}
        pagination={pagination}
        dataSource={tableData}
        columns={columns}
      ></Table>
      <Modal
        title={isAdd ? '新增白名单' : '编辑白名单'}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose={true}
      >
        <EditForm ref={childForm} isAdd={isAdd} value={formValue} />
      </Modal>
      <Modal
        destroyOnClose={true}
        title="文件正在上传..."
        visible={uploadVisible}
        maskClosable={false}
        keyboard={false}
        footer={null}
        closable={false}
      >
        <Progress percent={defaultPercent} status="active" />
      </Modal>
    </div>
  );
};
export default Index;
