"""
定时获取系统状态数据
"""
# -*- coding: utf-8 -*-
# @Time    : 2019-12-06 14:10
# <AUTHOR> wu
# @File    : get_system_status.py
# @Software: PyCharm

import time
import datetime
import psutil
import pymongo
from celery_tasks import app
from config.config import NdrLog
from utils.database import BaseMongoDB
from utils.best_show_size import size_format
from utils.logger import get_ndr_logger, LogToDb

LOG = get_ndr_logger(NdrLog.Type.RUN, __file__)


class GetSystemStatus():
    """获取系统状态信息，此处为获取历史数据信息时调用的方法"""
    def __init__(self):
        self.mongodb = BaseMongoDB("ndr")

    @staticmethod
    def get_net_data(device):
        """
        获取指定网卡的流量信息
        :param device: 网卡名
        :return: 网卡的流量信息
        """
        receive = 0
        transmit = 0
        with open('/proc/net/dev', 'r') as file:
            for line in file:
                if line.find(device) >= 0:
                    receive = line.split(':')[1].split()[0]
                    transmit = line.split(':')[1].split()[8]
        return int(receive), int(transmit)

    def speed_monitor(self, device):
        """
        :param device: 网卡名
        :return: 返回一秒内的网速
        """
        receive_old, transmit_old = self.get_net_data(device)
        time.sleep(1)
        receive, transmit = self.get_net_data(device)
        speed = {'rx': size_format((receive - receive_old) / 2014) + '/S',
                 'tx': size_format((transmit - transmit_old) / 1024) + '/S'}
        return speed

    def get_net_card_info(self):
        """
        :return: 网卡信息，这里按照页面配置从 MongoDB 获取工作口和管理口信息，不获取所有网卡信息
        """
        net_card_info = []
        n_net = self.mongodb.find_one('network', {'type': "worker"}, {"_id": 0, "interfaceList": 1})
        if n_net:
            net_card_info = n_net["interfaceList"]
            m_net = self.mongodb.find_one('network', {'type': "manager"}, {"_id": 0, "name": 1})
            if m_net:
                net_card_info.append(m_net["name"])
        return net_card_info

    def get(self):
        """
        :return: 系统状态信息
        """
        boot_time = psutil.boot_time()
        cpu_time = psutil.cpu_times_percent()
        cpu_percent = psutil.cpu_percent()
        memory_info = psutil.virtual_memory()
        disk_info = psutil.disk_usage('/')
        net = []
        net_info = psutil.net_io_counters(pernic=True)
        net_card = self.get_net_card_info()
        net_address = psutil.net_if_addrs()
        for card in net_card:
            if card in list(net_info.keys()):
                net.append({"netName": card,
                            "address": net_address[card][0][1],
                            "totalSend": size_format(net_info[card][0] / 1024),
                            "totalRecv": size_format(net_info[card][1] / 1024),
                            "packetsSend": net_info[card][2],
                            "packetsRecv": net_info[card][3],
                            "netSpeed": self.speed_monitor(card)})
        status = {"bootTime": int(boot_time * 1000),
                  "cpuUser": cpu_time[0],
                  "cpuNice": cpu_time[1],
                  "cpuSystem": cpu_time[2],
                  "cpuIdle": cpu_time[3],
                  "cpuPercent": cpu_percent,
                  "diskUsage": disk_info[3],
                  "memPercent": memory_info[2],
                  "memTotal": memory_info[0],
                  "netInfo": net}
        return status


@app.task()
def get_system_status():
    """
    定时获取系统状态写入 MongoDB
    :return:
    """
    mongodb = BaseMongoDB("ndr")
    collection = mongodb.db.system_hours_status
    # 超过 24 小时自动老化
    collection.create_index([("recordTime", pymongo.ASCENDING)], expireAfterSeconds=86400)
    status = GetSystemStatus().get()  # 获取一次系统信息结果
    if status:
        body = {"recordTime": datetime.datetime.utcnow(),
                "status": status}
        mongodb.insert_one("system_hours_status", body)  # 将结果存入 MongoDB
    else:
        LogToDb().write_to_db(log_type=NdrLog.Type.RUN, event='获取系统资源失败：%s' % error)
        LOG.error("Get system hour status error.")
