.fileContent {
  .contentD1 {
    display: grid;
    grid-template-columns: 30% 70%;
    grid-template-rows: 22rem;
    grid-gap: 0.5rem;
  }
}
.detailBox {
  height: calc(100vh - 74px);
  display: grid;
  grid-template-rows: 22rem;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    'a b'
    'c c';
  grid-gap: 10px;
  overflow: scroll;
  p {
    margin-bottom: 0;
  }
  & > div {
    background: #fff;
  }
  & > div:nth-child(3) {
    grid-area: c;
  }
}
