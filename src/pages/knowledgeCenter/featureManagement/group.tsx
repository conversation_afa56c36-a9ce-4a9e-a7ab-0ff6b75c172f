import { useHistory } from '@/hooks/global';
import {
  addFeatureGroup,
  delFeatureGroup,
  getGroupData,
} from '@/services/feature';
import { Button, Form, Input, Modal, Popconfirm, Table, message } from 'antd';
import React, { useEffect, useState } from 'react';
import store from './store';

const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};
const Group = (props: any) => {
  const { getValue } = props;
  const [form] = Form.useForm();
  const [params, setparams] = useState({
    page: 1,
    pageSize: 10,
  });
  const [tableData, settableData] = useState([]);
  const [total, settotal] = useState(0);
  const [visible, setvisible] = useState(false);

  const columns = [
    {
      title: '组名',
      dataIndex: 'groupName',
      render(groupName, { groupNameCN }) {
        return (
          <span>{groupName}{groupNameCN ? ` (${groupNameCN})` : ''}</span>
        )
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '属性',
      dataIndex: 'attribute',
      render(attribute: string) {
        return attribute === 'system' ? '系统预定义' : '用户自定义';
      }
    },
    {
      title: '特征数量',
      dataIndex: 'featureValidCount',
    },
    {
      title: '用户',
      dataIndex: 'createByUserName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (text: string, record: any) => {
        return (
          <div>
            <Button
              type="link"
              onClick={() => {
                handleDetail(record);
              }}
            >
              查看
            </Button>
            <Popconfirm
              title="确定删除吗？"
              disabled={record.attribute === 'system'}
              okText="确定"
              cancelText="取消"
              onConfirm={() => handleDel(record)}
            >
              <Button
                disabled={record.attribute === 'system'}
                type="link"
              >
                删除
              </Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: params.pageSize,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  useEffect(() => {
    getTableData();
  }, [params]);
  const getTableData = () => {
    getGroupData(params).then(res => {
      if (res.flag) {
        settableData(res.data.detail);
        settotal(res.data.count);
      }
    });
  };
  const handleDetail = (value: any) => {
    getValue();
    const action = {
      type: 'changeGroup',
      value: value,
    };
    store.dispatch(action);
  };
  const handleDel = (value: { groupId: any }) => {
    delFeatureGroup({ group_id: value.groupId }).then(res => {
      if (res.flag) {
        message.success(res.message);
        getTableData();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleAdd = () => {
    setvisible(true);
  };
  const handleOk = () => {
    form.validateFields().then((value: any) => {
      addFeatureGroup(value).then(res => {
        if (res.flag) {
          message.success(res.message);
          setvisible(false);
          form.resetFields();
          getTableData();
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const handleCancel = () => {
    setvisible(false);
  };

  return (
    <div>
      <Button type="primary" onClick={handleAdd}>
        添加组
      </Button>
      <Table
        className="mt-4"
        rowKey={(r, i) => i}
        pagination={pagination}
        dataSource={tableData}
        columns={columns}
      ></Table>
      <Modal
        destroyOnClose={true}
        title="添加组"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form form={form} {...layout}>
          <Form.Item
            label="组名称"
            name="name"
            rules={[
              {
                required: true,
                message: '请填写!',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="描述"
            name="descrip"
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Group;
