import { Collapse, Table, Tree } from 'antd';
import React, { useEffect, useState } from 'react';
import s from './style.less';
const { Panel } = Collapse;
interface PartProps {
  info: any;
}
const ShellCode = ({ info = {} }: PartProps) => {
  const [treeData, setTreeData] = useState([]);
  const columns1 = [
    {
      title: '规则ID',
      dataIndex: 'name',
    },
    {
      title: '威胁类型',
      dataIndex: 'virtual_address',
    },
    {
      title: '信息',
      dataIndex: 'virtual_size',
    },
    {
      title: '协议',
      dataIndex: 'size_of_data',
    },
    {
      title: '来源IP',
      dataIndex: 'entropy',
    },
    {
      title: '来源端口',
      dataIndex: 'entropy',
    },
    {
      title: '目的IP',
      dataIndex: 'entropy',
    },
    {
      title: '目的端口',
      dataIndex: 'entropy',
    },
  ];
  const columns2 = [
    {
      title: '文件名称',
      dataIndex: 'name',
    },
    {
      title: '反汇编结果',
      dataIndex: 'filetype',
    },
    {
      title: '操作',
      dataIndex: 'size',
    },
  ];
  useEffect(() => {}, [info]);
  return (
    <div className={s.staticBox}>
      <Table
        className={s.table}
        bordered
        scroll={{ y: 300 }}
        size="middle"
        columns={columns1}
        dataSource={[]}
        pagination={false}
      />
    </div>
  );
};

export default ShellCode;
