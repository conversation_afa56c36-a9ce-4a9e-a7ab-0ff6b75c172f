#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>


from celery_tasks import app
import os
from config.config import PACKAGE_DIR
from datetime import datetime
from celery_tasks import explore_task


def format_args(startTime, stopTime):
    startTimeStamp = datetime.fromtimestamp(startTime)
    stopTimeStamp = datetime.fromtimestamp(stopTime)
    query_start_time = int(startTimeStamp.replace(hour=0, minute=0, second=0).timestamp())
    query_end_time = int(stopTimeStamp.replace(hour=23, minute=59, second=59).timestamp())
    rangeTimeTuple = []
    while True:
        start = query_start_time
        stop = start + 60 * 60 * 24
        rangeTimeTuple.append((start, stop))
        if stop >= query_end_time:
            break
        query_start_time = stop

    query_string_list = list()
    file_list = list()
    for T in rangeTimeTuple:
        # 转换为UTC时间
        after_time = "after " + str(datetime.utcfromtimestamp(T[0])).replace(" ", "T") + "Z"
        before_time = "before " + str(datetime.utcfromtimestamp(T[1])).replace(" ", "T") + "Z"
        # 命名不需要UTC时间
        file_name = str(datetime.fromtimestamp(T[0])).split(" ")[0]
        md5_pcap = file_name + ".pcap"
        file_path = PACKAGE_DIR + md5_pcap
        file_list.append(file_path)
        # 若文件已生成过，则不再执行生成任务
        if os.path.exists(file_path):
            continue
        query_string = '''sudo stenoread "{0} && {1}" -w {2}'''.format(after_time, before_time, file_path)
        query_string_list.append(query_string)
    return query_string_list, file_list


@app.task()
def get_package(start, stop, task_id, task_type, user_id):
    startTime = int(int(start) / 1000)
    stopTime = int(int(stop / 1000))

    steno_query_list, file_list = format_args(startTime, stopTime)

    for steno_query in steno_query_list:
        os.system(steno_query)

    explore_task.start_task.delay(task_id, task_type, file_list, user_id)
