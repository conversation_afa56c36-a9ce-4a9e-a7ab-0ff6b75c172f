/*
 * @Author: tianh
 * @Date: 2022-02-14 10:30:49
 * @LastEditors: tianh
 * @LastEditTime: 2022-02-18 17:16:45
 * @Descripttion:
 */
import React, { forwardRef, useEffect, useState } from 'react';
import { Form, Input, Select, InputNumber, DatePicker } from 'antd';
import moment from 'moment';
import {
  killChainsOptions,
  intelligenceType,
  threatFlagNameMap,
} from '@/utils/enumList';
import { getThreatenTypes } from '@/services/infoManagement';
const { TextArea } = Input;
const { Option } = Select;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};
const EditForm = forwardRef((props: any, ref: any) => {
  const { value, isAdd } = props;
  const [form] = Form.useForm();
  const [threat_type, setthreat_type] = useState([]);
  if (value.disclosureTime) {
    value.disclosureTime = moment(value.disclosureTime);
  }
  const getType = () => {
    getThreatenTypes().then(res => {
      console.log(res);
      if (res.flag) {
        setthreat_type(res.data.threat_type);
      }
    });
  };
  useEffect(() => {
    getType();
  }, []);

  return (
    <Form form={form} ref={ref} initialValues={value} {...layout}>
      <Form.Item
        label="ioc类型"
        name="ioc_type"
        rules={[{ required: true, message: '请填写情报类型' }]}
      >
        <Select disabled={!isAdd}>
          {intelligenceType.map(item => {
            return (
              <Option key={item.key} value={item.value}>
                {item.key}
              </Option>
            );
          })}
        </Select>
      </Form.Item>
      <Form.Item
        label="ioc"
        name="ioc"
        rules={[{ required: true, message: '请填写' }]}
      >
        <Input disabled={!isAdd} />
      </Form.Item>
      <Form.Item label="发现时间" name="discoverytime">
        <DatePicker />
      </Form.Item>
      <Form.Item label="标签" name="labels">
        <Input />
      </Form.Item>
      <Form.Item label="利用工具" name="tools">
        <Input />
      </Form.Item>
      <Form.Item label="组织名" name="organizations">
        <Input />
      </Form.Item>
      <Form.Item
        label="杀伤链阶段"
        name="killchainstage"
        rules={[{ required: true, message: '请选择杀伤链阶段' }]}
      >
        <Select>
          {killChainsOptions.map(item => {
            return (
              <Option value={item.key} key={item.key}>
                {item.value}
              </Option>
            );
          })}
        </Select>
      </Form.Item>
      <Form.Item
        label="威胁得分"
        name="score"
        rules={[{ required: true, message: '请填写威胁得分' }]}
      >
        <InputNumber max={100} min={0} />
      </Form.Item>
      <Form.Item
        label="威胁分类"
        name="threat_type"
        rules={[{ required: true, message: '请选择' }]}
      >
        <Select>
          {threat_type.map((item: any, index: any) => {
            return (
              <Option key={index} value={item}>
                {item}
              </Option>
            );
          })}
        </Select>
      </Form.Item>
      <Form.Item label="参考" name="reference">
        <TextArea autoSize={{ minRows: 4 }} />
      </Form.Item>
    </Form>
  );
});

export default EditForm;
