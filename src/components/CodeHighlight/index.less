:global(.code-highlight) {
  :global(.ant-tooltip-inner) {
    background-color: #fff;
  }
}

code[class*='language-'],
pre[class*='language-'] {
  color: black;
  font-size: 1em;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  line-height: 1.5;
  text-align: left;
  word-wrap: normal;
  word-break: normal;
  word-spacing: normal;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  background: none;
}

/* Code blocks */
pre[class*='language-'] {
  margin: 0.5em 0;
  padding: 1em;
  overflow: auto;
}

:not(pre) > code[class*='language-'],
pre[class*='language-'] {
  background: #fff;
}

/* Inline code */
:not(pre) > code[class*='language-'] {
  padding: 0.1em;
  white-space: normal;
  border-radius: 0.3em;
}

code[class*='language-'] {
  display: block;
}

code[class*='language-']:focus {
  outline: 0;
}

:global(.token.comment) {
  color: #999;
}

:global(.token.punctuation) {
  color: #ccc;
}

:global(.token.tag) {
  color: #e2777a;
}

:global(.token.number) {
  color: #6196cc;
}

:global(.token.invalid) {
  color: red;
}

:global(.token.property),
:global(.token.constant),
:global(.token.symbol) {
  color: #f8c555;
}

:global(.token.keyword::before) {
  display: block;
  content: '';
}

:global(.token.keyword) {
  color: #cc99cd;
}

:global(.token.string) {
  color: #7ec699;
}

:global(.token.operator),
:global(.token.url) {
  color: #67cdcc;
}

:global(.token.bold) {
  font-weight: bold;
}
:global(.token.italic) {
  font-style: italic;
}

:global(.token.entity) {
  cursor: help;
}
