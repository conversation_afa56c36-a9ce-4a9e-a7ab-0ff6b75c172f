import React, { useState, useEffect } from 'react';
import { Modal, Form } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import styles from './style.less';

interface Props extends FormComponentProps {
  title?: string;
  visible?: boolean;
  width?: number;
  onCancel?: () => void;
  onSubmit?: (values: any) => Promise<void>;
  children: (form: WrappedFormUtils<any>, submit: () => void) => any;
}

function FormModal(props: Props) {
  const [loading, setLoading] = useState(false);
  const [shake, setShake] = useState(false);
  const {
    title,
    visible = false,
    onCancel,
    onSubmit,
    children,
    form,
    width,
  } = props;

  const cancel = () => {
    if (typeof onCancel === 'function') {
      onCancel();
    }
  };

  const doShake = () => {
    setShake(true);
    setTimeout(() => {
      setShake(false);
    }, 200);
  };

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const submit = () => {
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    form.validateFields(async (err, values) => {
      if (err) {
        doShake();
        return;
      }
      if (typeof onSubmit !== 'function') {
        return;
      }
      setLoading(true);
      try {
        await onSubmit(values);
      } catch (e) {
        doShake();
      }
      setLoading(false);
    });
  };

  return (
    <Modal
      title={title}
      confirmLoading={loading}
      visible={visible}
      onOk={submit}
      onCancel={cancel}
      width={width || 640}
      className={shake ? styles.shakeLittle : undefined}
    >
      {children(form, submit)}
    </Modal>
  );
}

export default Form.create<Props>()(FormModal);
