kind: pipeline
type: kubernetes
name: code-check

steps:
  - name: branch-test
    image: registry.k8s.intra.knownsec.com/xn-base/branch-test:1.0.1
  - name: lint
    image: registry.k8s.intra.knownsec.com/ks-box/admin-for-box-base:2.0.0
    commands:
      - ln -s /opt/node_app/node_modules /drone/src/node_modules && yarn lint
  - name: failure-notify
    image: registry.k8s.intra.knownsec.com/xn-base/notify:1.0.2
    settings:
      template: 项目 Boss Admin For Box 的分支 ${DRONE_SOURCE_BRANCH} 检查失败，请进行检查后重试
    environment:
      ROBOT_KEY:
        from_secret: robot_key
    when:
      status:
        - failure

trigger:
  event:
    - push
    - tag
    - pull_request
  branch:
    - master
    - dev
    - release*

---
kind: pipeline
type: kubernetes
name: continuous-deploy

steps:
  - name: tag-build
    image: registry.k8s.intra.knownsec.com/xn-base/tag-builder:1.1.0
  - name: image-build
    image: plugins/docker
    settings:
      username:
        from_secret: harbor_username
      password:
        from_secret: harbor_password
      repo: registry.k8s.intra.knownsec.com/ks-box/admin-for-box
      registry: registry.k8s.intra.knownsec.com
      dockerfile: Dockerfile
      insecure: true
    when:
      branch:
        - dev
  - name: success-notify
    image: registry.k8s.intra.knownsec.com/xn-base/work-wechat-notify:0.1.1
    settings:
      template: 项目 Boss Admin For Box 镜像打包成功
    environment:
      ROBOT_KEY:
        from_secret: robot_key
  - name: failed-notify
    image: registry.k8s.intra.knownsec.com/xn-base/work-wechat-notify:0.1.1
    settings:
      project_name: Boss Admin For Box
      status: failed
    environment:
      ROBOT_KEY:
        from_secret: robot_key
    when:
      status:
        - failure

depends_on:
  - code-check

trigger:
  event:
    - push
    - tag
  branch:
    - master
    - dev
    - release*

# harbor 仓库相关 secret
---
kind: secret
name: harbor_username
get:
  path: harbor
  name: username

---
kind: secret
name: harbor_password
get:
  path: harbor
  name: password
