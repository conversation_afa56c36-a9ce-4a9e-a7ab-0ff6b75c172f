/*
 * @Author: 田浩
 * @Date: 2021-09-16 11:47:14
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-18 10:14:23
 * @Descripttion:
 */
import { useHistory } from '@/hooks/global';
import { addMeans, deleteMeans, editMeans, meansList } from '@/services/collect';
import { calculationByte, ellipsis, getQueryVariable } from '@/utils/utils';
import {
  Button, Form, Input, message,
  Modal, Progress, Select, Table, Tooltip, Typography, Upload
} from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import MeansForm from './meansForm';


const { Option } = Select;
const { Paragraph } = Typography;

const MeansList = () => {
  const [form] = Form.useForm();
  let childForm = useRef<any>();
  const editBtn = useRef<any>();
  const history = useHistory();
  const [tableData, settableData] = useState<any>([]);
  const [visible, setvisible] = useState(false);
  const [uploadVisible, setuploadVisible] = useState(false);
  const [defaultPercent, setdefaultPercent] = useState(0);
  const [isAdd, setisAdd] = useState(false);
  const [editValue, seteditValue] = useState<any>({});
  const [params, setparams] = useState({
    page: 1,
    pageSize: 10,
    ip_addr: '',
  });
  const [total, settotal] = useState(0);
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox');
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [columns, setcolumns] = useState([
    {
      title: 'ip地址',
      dataIndex: 'ip_addr',
      fixed: 'left',
      width: 150,
    }, {
      title: '资产标签',
      dataIndex: 'labels',
      width: 150,
      render: (t: any) => {
        if (t) {
          return <Tooltip title={t.toString()}>{ellipsis(t.toString(), 10)}</Tooltip>;
        }
      },
    },
    {
      title: '启用端口',
      dataIndex: 'enable_port',
      width: 150,
      render: (t: any) => {
        return <Paragraph style={{ margin: 0 }} ellipsis={{ tooltip: Object.keys(t).join(',') }}>
          {Object.keys(t).join(',')}
        </Paragraph>

      },
    },
    {
      title: '发起连接数',
      dataIndex: 'send_count',
      width: 150,
    },
    {
      title: '接收连接数',
      dataIndex: 'accept_count',
      width: 150,
    },
    {
      title: '总流量',
      dataIndex: 'app_total_traffic',
      width: 150,
      render: (t: any) => {
        return calculationByte(t);
        // if (t >= 1024 * 1024 * 1024) {
        //   let num = t / 1024 / 1024 / 1024;
        //   num = num.toFixed(2);
        //   return `${num}G`;
        // } else if (t >= 1024 * 1024) {
        //   let num = t / 1024 / 1024;
        //   num = num.toFixed(2);
        //   return `${num}M`;
        // } else if (t >= 1024) {
        //   let num = t / 1024;
        //   num = num.toFixed(2);
        //   return `${num}KB`;
        // } else if (t < 1024) {
        //   return `${t} B`;
        // }
      },
    },

    {
      title: 'MAC地址',
      dataIndex: 'mac_addr',
      width: 150,
    },
    {
      title: '重点资产',
      dataIndex: 'is_importent',
      width: 150,
      render: (t: any) => {
        if (t) {
          return '是';
        } else {
          return '否';
        }
      },
    },
    {
      title: '涉密资产',
      dataIndex: 'is_sercret',
      width: 150,
      render: (t: any) => {
        if (t) {
          return '是';
        } else {
          return '否';
        }
      },
    },
    {
      title: '地理位置',
      dataIndex: 'location',
      width: 150,
    },
    {
      title: '所属国家',
      dataIndex: 'country',
      width: 150,
    },
    {
      title: '国家代码',
      dataIndex: 'country_code',
      width: 150,
    },
    {
      title: '域名',
      dataIndex: 'domain',
      width: 150,
    },
    {
      title: '资产名称',
      dataIndex: 'name',
      width: 150,
    },
    {
      title: '资产编号',
      dataIndex: 'assets_num',
      width: 150,
    },
    {
      title: '资产类型',
      dataIndex: 'type',
      width: 150,
    }, {
      title: '所属组织',
      dataIndex: 'organisation',
      width: 150,
    },
    {
      title: '所属部门',
      dataIndex: 'department',
      width: 150,
    },
    {
      title: '责任人',
      dataIndex: 'person',
      width: 150,
    },
    {
      title: '操作系统',
      dataIndex: 'os_info',
      width: 150,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      width: 200,
      render: (t: any) => {
        return moment(t).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      width: 200,
      render: (t: any) => {
        return moment(t).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      fixed: 'right',
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              type="link"
              ref={editBtn}
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                goto(record);
              }}
            >
              资产画像
            </Button>
          </div>
        );
      },
    },
  ]);
  const goto = (value: any) => {
    history.push(`/app/mica/meansDetail?ip_addr=${value.ip_addr}`);
  };
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      setparams({ ...params, ...value, page: 1 });
    });
  };

  // 获取数据
  const getList = () => {
    setLoading(true)
    meansList(params).then(res => {
      if (res.flag) {
        setselectedRowKeys([]);
        settableData(res.data.cols);
        settotal(res.data.total);
        setLoading(false)
      } else {
        setLoading(false)
        setselectedRowKeys([]);
        settableData([]);
        message.error(res.message);
      }
    });
  };
  // 关闭进度条
  const toggleVisibleStatusFalse = () => {
    setuploadVisible(false);
  };
  // 展示进度条
  const toggleVisibleStatusTrue = (percent: any) => {
    setuploadVisible(true);
    setdefaultPercent(percent);
  };
  // 删除/批量删除
  const handleDelete = () => {
    if (!selectedRowKeys || !selectedRowKeys.length) {
      message.error('请选择数据');
      return;
    }
    let ip_list: string[] = [];
    selectedRowKeys.forEach((item: any) => {
      const choosed = tableData.filter((item0: any) => item0._id === item);
      if (choosed.length) {
        ip_list.push(choosed[0].ip_addr);
      }
    });
    deleteMeans({ ip_list: ip_list }).then(res => {
      console.log(res);
      if (res.flag) {
        message.success(res.message);
        getList();
      } else {
        message.error(res.message);
      }
    });
  };

  const handleOk = () => {
    childForm.current.validateFields().then((value: any, err: any) => {
      if (value.ports.length) {
        let boo = false;
        value.ports.forEach((item: string) => {
          if (!(/^[0-9]*$/.test(item) && 1 <= Number(item) && Number(item) <= 65535)) {
            boo = true;
          }
        })
        if (boo) {
          message.error('请输入1~65535的纯数字');
          return;
        }
      }

      // 新增
      if (isAdd) {
        let obj = {};
        value.ports.forEach((item: string) => {
          obj[item] = 0;
        })
        value.enable_port = obj;
        delete value.ports;
        addMeans(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            setvisible(false);
            getList();
          } else {
            message.error(res.message);
          }
        });
      } else {
        let obj = {};
        value.ports.forEach((item: string) => {
          if (editValue.enable_port.hasOwnProperty(item)) {
            obj[item] = editValue.enable_port[item]
          } else {
            obj[item] = 0;
          }
        })
        value.enable_port = obj;
        delete value.ports;
        // 编辑
        editMeans(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            setvisible(false);
            getList();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };

  // 编辑
  const handleEdit = (value: any) => {
    value.labels = value.labels.toString();
    seteditValue(value);
    setisAdd(false);
    setvisible(true);
  };
  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const rowSelection = {
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys,
  };
  useEffect(() => {
    if (getQueryVariable('ip_addr')) {
      params.ip_addr = getQueryVariable('ip_addr');
      setparams(params);
    }
  }, []);

  useEffect(() => {
    getList();
  }, [params]);

  const formItemLayout = {
    labelCol: {
      span: 9, // * ≥576px
    },
    wrapperCol: {
      span: 15,
    },
  };
  const UpdateProps = {
    name: 'fileName',
    action: '/mica-api/api/v1/customize/assets/update',
    showUploadList: false,
    onChange: (info: {
      file: {
        status: string;
        percent: number;
        response: { flag: boolean; message: any };
      };
    }) => {
      if (info.file.status === 'uploading') {
        toggleVisibleStatusTrue(Math.floor(info.file.percent));
      } else if (!info.file.response.flag) {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        return;
      } else if (info.file.status === 'done') {
        setTimeout(() => {
          message.success('文件上传成功');
          toggleVisibleStatusFalse();
          getList();
        }, 3000);
      } else if (info.file.status === 'error') {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
      }
    },
    beforeUpload: (file: any) => {
      let pos = file.name.lastIndexOf('.');
      let result = file.name.substring(pos + 1, file.name.length);
      if (result !== 'zip') {
        message.error('仅支持上传zip格式的文件');
        return Upload.LIST_IGNORE;
      }
    },
  };
  const importProps = {
    name: 'fileName',
    action: '/mica-api/api/v1/customize/assets/import',
    showUploadList: false,
    onChange: (info: {
      file: {
        status: string;
        percent: number;
        response: any;
      };
    }) => {
      if (info.file.status === 'uploading') {
        toggleVisibleStatusTrue(Math.floor(info.file.percent));
      } else if (!info.file.response.flag) {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
        return;
      } else if (info.file.status === 'done' && info.file.response.flag) {
        setTimeout(() => {
          if (info.file.response.data.failed_total) {
            message.warning(`${info.file.response.message},${info.file.response.data.failed_total}`);
          } else {
            message.success(info.file.response.message);
          }
          toggleVisibleStatusFalse();
          getList();
        }, 3000);
      } else if (info.file.status === 'error') {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
      }
    },
    beforeUpload: (file: any) => {
      let pos = file.name.lastIndexOf('.');
      let result = file.name.substring(pos + 1, file.name.length);
      if (!['xls', 'xlsx'].includes(result)) {
        message.error('仅支持上传xls/xlsx格式的文件');
        return Upload.LIST_IGNORE;
      }
    },
  };
  return (
    <div>
      <Form className="bg-d0 !p-4 b b-solid b-brd6 form-inline" form={form} onFinish={onSubmit} layout="inline">
        <div className="flex gap-4 w-100%">
          <Form.Item className="!flex-[1]" name="ip_addr" label="IP地址">
            <Input placeholder='请输入IP地址'/>
          </Form.Item>
          <Form.Item className="!flex-[1]" name="name" label="资产名称">
            <Input placeholder='请输入资产名称'/>
          </Form.Item>
          <Form.Item className="!flex-[1]" name="assets_num" label="资产编号">
            <Input placeholder='请输入资产编号'/>
          </Form.Item>
          <Form.Item className="!flex-[1]" name="is_importent" label="重点资产">
            <Select allowClear={true} placeholder="请选择是否是重点资产">
              <Option value="true" key="0">是</Option>
              <Option value="false" key="1">否</Option>
            </Select>
          </Form.Item>
          <Form.Item className="!flex-[1]" name="is_outer" label="是否是外网">
            <Select allowClear={true}>
              <Option value="true" key="0">是</Option>
              <Option value="false" key="1">否</Option>
            </Select>
          </Form.Item>
        </div>
        <div className="mt-4 flex gap-3">
          <Button type="primary" htmlType="submit">
            搜索
          </Button>
          <Upload {...UpdateProps} accept=".zip">
            <Button ghost type="primary">
              重点资产库升级
            </Button>
          </Upload>
          <Upload {...importProps} accept=".xls, .xlsx">
            <Button ghost type="primary">
              重点资产导入
            </Button>
          </Upload>
          <Button
            ghost
            type="primary"
            onClick={() => {
              seteditValue({enable_port: {}});
              setisAdd(true), setvisible(true);
            }}
          >
            新增
          </Button>
          <Button danger onClick={handleDelete} className="searchBtn">
            删除
          </Button>
        </div>
      </Form>
      <Table
        className="mt-4"
        loading={loading}
        rowSelection={{
          type: selectionType,
          ...rowSelection,
        }}
        rowKey="_id"
        pagination={pagination}
        columns={columns}
        scroll={{ x: 1200 }}
        dataSource={tableData}
      />
      <Modal
        width={1200}
        destroyOnClose={true}
        onOk={handleOk}
        onCancel={() => {
          setvisible(false);
        }}
        visible={visible}
        maskClosable={false}
        title={isAdd ? '新增' : '编辑'}
      >
        <MeansForm isAdd={isAdd} value={editValue} ref={childForm} />
      </Modal>
      <Modal
        destroyOnClose={true}
        title="文件正在上传..."
        visible={uploadVisible}
        maskClosable={false}
        keyboard={false}
        footer={null}
        closable={false}
      >
        <Progress percent={defaultPercent} status="active" />
      </Modal>
    </div>
  );
};
export default MeansList;
