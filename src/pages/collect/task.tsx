import React, { useEffect, useState } from 'react';
import {
  But<PERSON>,
  DatePicker,
  Collapse,
  Select,
  Form,
  Input,
  Spin,
  Table,
  Modal,
  Row,
  Col,
  message,
  Popconfirm,
  Tooltip,
} from 'antd';
import { ellipsis } from '@/utils/utils';
import { taskList, actionTask, createTask, getTaskReslut, downloadResult } from '@/services/collect';
import moment from 'moment';
import { SearchOutlined } from '@ant-design/icons';
const { Panel } = Collapse;
const Task = () => {
  const [form] = Form.useForm();
  const [taskDetail, settaskDetail] = useState<any>({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAdd, setisAdd] = useState(false);
  const [taskForm] = Form.useForm();
  const [params, setparams] = useState<any>({});
  const [tableData, setTableData] = useState<any>([]);
  const [total, settotal] = useState(0);
  const [loading, setloading] = useState(false);
  const [analysisDialog, setanalysisDialog] = useState(false);
  const [detailTableData, setdetailTableData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
    },

    {
      title: '任务类型',
      dataIndex: 'rule_type',
      render: (t: any) => {
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">{t === 'backtrance' ? '回溯任务' : ''}</div>
            <div className="text-left ws-nowrap">{t === 'trance' ? '追踪任务' : ''}</div>
          </div>
        );
      },
    },
    {
      title: '任务时间',
      dataIndex: 'time',
      render: (t: any, record: any) => {
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">开始时间:{record.start_time}</div>
            <div className="text-left ws-nowrap">结束时间:{record.end_time}</div>
          </div>
        );
      },
    },
    {
      title: '数据源',
      dataIndex: 'source',
    },
    {
      title: '日志类型',
      dataIndex: 'log_type',
    },
    {
      title: '目标IOC',
      dataIndex: 'target_ioc',
      render: (t: any) => {
        return (
          <Tooltip placement="topLeft" arrowPointAtCenter title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },

    {
      title: '任务状态',
      dataIndex: 'task_status',
      render: (t: any) => {
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">{t === -1 ? '未连接' : ''}</div>
            <div className="text-left ws-nowrap">{t === 0 ? '待审核' : ''}</div>
            <div className="text-left ws-nowrap">{t === 1 ? '等待源审核' : ''}</div>
            <div className="text-left ws-nowrap">{t === 2 ? '审核通过' : ''}</div>
            <div className="text-left ws-nowrap">{t === 3 ? '审核失败' : ''}</div>
            <div className="text-left ws-nowrap">{t === 4 ? '等待' : ''}</div>
            <div className="text-left ws-nowrap">{t === 5 ? '运行' : ''}</div>
            <div className="text-left ws-nowrap">{t === 6 ? '完成' : ''}</div>
            <div className="text-left ws-nowrap">{t === 7 ? '失败' : ''}</div>
            <div className="text-left ws-nowrap">{t === 10 ? '不存在' : ''}</div>
          </div>
        );
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      width: 240,
      render: (t: any, record: any) => {
        return (
          <>
            <Button
              onClick={() => {
                handleEdit();
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              编辑
            </Button>
            <Popconfirm title="确定删除吗？" okText="确定" cancelText="取消" onConfirm={() => deleteTask(record)}>
              <Tooltip>
                <Button type="link" danger>
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>

            <Button
              onClick={() => {
                taskStart(record);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              开始
            </Button>
            <Button
              onClick={() => {
                showResult(record);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              查看分析结果
            </Button>
          </>
        );
      },
    },
  ];
  const detailColumns = [
    {
      title: '源ip',
      dataIndex: 'name',
    },
    {
      title: '目的ip',
      dataIndex: 'name',
    },
    {
      title: '目的端口',
      dataIndex: 'name',
    },

    {
      title: '开始时间',
      dataIndex: 'name',
    },
    {
      title: '结束时间',
      dataIndex: 'name',
    },
    {
      title: 'url',
      dataIndex: 'name',
    },
    {
      title: 'Body',
      dataIndex: 'name',
    },
    {
      title: '联通次数',
      dataIndex: 'name',
    },
    {
      title: '上行流量',
      dataIndex: 'name',
    },
    {
      title: '下行流量',
      dataIndex: 'name',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];
  const taskType = [
    {
      label: '回溯任务',
      value: 'backtrance',
    },
    {
      label: '追踪任务',
      value: 'trance',
    },
  ];
  const logType = [
    {
      label: '通联日志',
      value: 'conn',
    },
  ];
  const downLoadDetail = ()=>{
    console.log(33,taskDetail)
    downloadResult({name:taskDetail.name}).then(res=>{
        
    })
  }
  const onSelectChange = (newSelectedRowKeys:any) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const toMark = ()=>{

  }
  const handleEdit = () => {
    setisAdd(false);
    setIsModalOpen(true);
  };
  const taskStart = (data: any) => {
    data.action = 'start';
    requestData(data);
  };
  const requestData = (data: any) => {
    actionTask(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      }
    });
  };
  const showResult = (data: any) => {
    settaskDetail(data)
    getTaskReslut({name:data.name}).then(res=>{
        if(res.flag) {
            setanalysisDialog(true)
            setdetailTableData(res.data.data)
        }
    })
  };
  const deleteTask = (data: any) => {
    data.action = 'start';
    requestData(data);
  };
  const handleAdd = () => {
    setisAdd(true);
    setIsModalOpen(true);
  };
  const searchList = () => {
    let value = { ...params };
    taskList(value).then(res => {
      if (res.flag) {
        setloading(false);
        settotal(res.data.total);
        setTableData(res.data.detail);
      } else {
        message.error(res.message);
      }
    });
  };

  useEffect(() => {
    searchList();
  }, []);

  const handleCancel = () => {
    taskForm.resetFields();
    setIsModalOpen(false);
  };
  const handleOk = async () => {
    taskForm.validateFields().then((value: any) => {
      if (isAdd) {
        createTask(value).then(res => {
          if (res.flag) {
            setIsModalOpen(false);
            message.success(res.message);
            searchList();
          }
        });
      } else {
        value.action = 'edit';
        actionTask(value).then(res => {
          if (res.flag) {
            setIsModalOpen(false);
            message.success(res.message);
            searchList();
          }
        });
      }
    });
  };
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      searchList();
    });
  };
  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const formItemLayout = {
    labelCol: {
      span: 4, // * ≥576px
    },
    wrapperCol: {},
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form {...formItemLayout} layout="inline" initialValues={params} form={form} className="!flex gap-3 !mt-5">
            <Row>
              <Col span={8}>
                <Form.Item label="任务名称:" name="name">
                  <Input />
                </Form.Item>
              </Col>
              <Col offset={12} span={4}>
                <div style={{ textAlign: 'right' }}>
                  <Button
                    style={{ margin: '0 10px' }}
                    className="searchBtn"
                    type="primary"
                    onClick={onSubmit}
                    icon={<SearchOutlined />}
                  >
                    搜索
                  </Button>
                  <Button style={{ color: '#1890ff' }} type="dashed" onClick={handleAdd}>
                    新建
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <Spin spinning={loading}>
        <Table dataSource={tableData} columns={columns} pagination={pagination} />
      </Spin>
      <Modal
        width={600}
        title={isAdd ? '新建任务' : '编辑任务'}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form initialValues={taskDetail} form={taskForm}>
          <Form.Item name="name" label="任务名称" rules={[{ required: true, message: '请填写!' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="task_type" label="任务类型" rules={[{ required: true, message: '请填写!' }]}>
            <Select>
              {taskType.map(item => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="source" label="数据源" rules={[{ required: true, message: '请填写!' }]}>
            <Select>
              {logType.map(item => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="log_type" label="日志类型" rules={[{ required: true, message: '请填写!' }]}>
            <Select>
              {logType.map(item => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="start_time" label="开始时间" rules={[{ required: true, message: '请选择!' }]}>
            <DatePicker allowClear showTime className="w-100%" format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item name="stop_time" label="结束时间" rules={[{ required: true, message: '请选择!' }]}>
            <DatePicker allowClear className="w-100%" showTime format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        width={1200}
        title="分析结果"
        visible={analysisDialog}
        onOk={() => {
          setanalysisDialog(false);
        }}
        onCancel={() => {
          setanalysisDialog(false);
        }}
      >
        <div>
            <Button onClick={toMark}>研判分析</Button>
            <Button type="primary" onClick={downLoadDetail}>结果下载</Button>
        </div>
        <Table rowSelection={rowSelection} columns={detailColumns} dataSource={detailTableData} />
      </Modal>
    </div>
  );
};

export default Task;
