{
    "type": "object", 
    "required": ["fileName", "tag", "dir"
    ], 
    "properties": {
        "fileName": {
            "type": "string", 
        }, 
        "userId": {
            "type": "string", 
            "pattern": r"^[0-9a-fA-F]{32}$", 
            "default": ""
        },
        "dir": {
            "type": "string",
            "default":""
		},
        "tag": {
            "type": "string", 
            "pattern": r"^[A-Za-z0-9_\-\u4e00-\u9fa5]{1,255}$",
            "default": ""
        }
    }
}