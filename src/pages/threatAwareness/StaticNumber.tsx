import React from 'react';
import cs from 'classnames';
import CountUp from 'react-countup';

import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import style from './index.less';
import { Space } from 'antd';

export type StaticNumberProps = {
  total: number;
  diff: number
}


export const StaticNumber = ({ total, diff }: StaticNumberProps) => {
  return (
    <div className={style.static_number}>
      <CountUp
        className={style.digital_number}
        duration={1}
        delay={0}
        preserveValue
        end={total || 0}
        separator=','
      />
      <div className={style.number_compare}>
        <div className={style.increase}>
          <ArrowUpOutlined className={cs('pr-1', style.increase_arrow)} />
          <Space>
            新增
            <CountUp className={style.compare_num} duration={1} delay={0} end={diff >= 0 ? diff : 0} preserveValue />
          </Space>
        </div>
        <div className={style.decrease}>
          <ArrowDownOutlined className={cs('pr-1', style.decrease_arrow)} />
          <Space>
            减少
            <CountUp className={style.compare_num} duration={1} delay={0} end={diff <= 0 ? -diff : 0} preserveValue />
          </Space>
        </div>
      </div>
    </div>
  );
};
