/*
 * @Author: tianh
 * @Date: 2021-11-17 11:28:55
 * @LastEditors: tianh
 * @LastEditTime: 2021-11-24 10:57:03
 * @Descripttion:
 */
import React from 'react';
import { Card, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import ApiManage from '../apiManage';
import style from '../style.less';
const Index = () => {
  return (
    <div className={style.box}>
      <Card
        title={
          <div>
            <span className={style.btn_right}>接口管理</span>
            <Tooltip
              title={
                <div>
                  <p>说明：1G设备，最多同时启用两个接口。</p>
                  <p>
                    10G设备，有1G的口和10G的口，同时接入多个接口的话，10G的口只能最能保证5Gbps的处理能力，最多同时接入两个10G口，或者1个10G口，和最多5个1G的口。
                  </p>
                </div>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        }
        bordered
        style={{ width: '100%' }}
      >
        <ApiManage />
      </Card>
    </div>
  );
};
export default Index;
