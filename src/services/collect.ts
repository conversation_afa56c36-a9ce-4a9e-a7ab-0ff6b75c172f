/*
 * @Author: 田浩
 * @Date: 2021-07-19 15:32:01
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-10 17:39:35
 * @Descripttion:
 */
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export const getChartData = (data: object) => {
  return request(`dpilog/statistics_info${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getList = (data: object) => {
  return request(`dpilog/list_info${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getDetail = (data: object) => {
  return request(`dpilog/detail_info${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getEmailList = (data: any) => {
  return request(`dpilog/mail_info${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const isExist = (data: object) => {
  return request(`dpilog/mail_down${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const meansList = (data: any) => {
  return request(`assets_list${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const deleteMeans = (data: any) => {
  return request(`assets_list`, {
    method: 'delete',
    data,
  });
};
export const addMeans = (data: any) => {
  return request(`assets_list`, {
    method: 'post',
    data,
  });
};
export const editMeans = (data: any) => {
  return request(`assets_list`, {
    method: 'put',
    data,
  });
};
// export const week_traffic = (data: any) => {
//   return request(`assets_week_traffic${parseQueryString(data)}`, {
//     method: 'get',
//   });
// };

export const recentSampling = (data: any) => {
  return request(`assets_traffic_sample${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const threat_event = (data: any) => {
  return request(`assets_threat_event${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const get_path = (data: any) => {
  return request(`ip_access_relation`, {
    method: 'post',
    data,
  });
  // return request(
  //   `ip_access_relation?ip=************&startTime=1653840000923&stopTime=1656647999000`,
  //   { method: 'get' },
  // );
};

export const detail_path = (data: any) => {
  return request(`ip_access_detail${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const getAttacks = (data: any) => {
  return request(`attack_chains_recovery`, {
    method: 'post',
    data,
  });
};
/**
 * 文件分析取证
 */

// 获取文件相关统计信息
export const getFileStatistics = (data: any) => {
  return request(`file/statistics${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 获取文件下载源IP信息
export const getFileSecIP = (data: any) => {
  return request(`file/src_ip${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 下载文件源IP信息
export const getFileExSecIP = (data: any) => {
  return request(`file/export/src_ip${parseQueryString(data)}`, {
    responseType: 'blob',
    method: 'get',
  });
};
export const getFileIP = (data: any) => {
  return request(`file/ip${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 获取文件下载目的IP信息
export const getFileDstIP = (data: any) => {
  return request(`file/dst_ip${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 下载文件目的IP信息
export const getFileExDstIP = (data: any) => {
  return request(`file/export/dst_ip${parseQueryString(data)}`, {
    method: 'get',
    responseType: 'blob',
  });
};
// 获取文件相关URL信息
export const getFileUrl = (data: any) => {
  return request(`file/url${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 下载文件相关URL信息
export const getFileExUrl = (data: any) => {
  return request(`file/export/url${parseQueryString(data)}`, {
    method: 'get',
    responseType: 'blob',
  });
};
// 获取文件相关邮箱信息
export const getFileEmail = (data: any) => {
  return request(`file/email${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 下载文件相关邮箱信息
export const getFileExEmail = (data: any) => {
  return request(`file/export/email${parseQueryString(data)}`, {
    method: 'get',
    responseType: 'blob',
  });
};
// 获取文件报告列表信息
export const getFileReport = (data: any) => {
  return request(`file/report${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 获取文件报告列表导出
export const getFileExReport = (data: any) => {
  return `/mica-api/api/v1/file/report/export${parseQueryString(data)}`;
};
// 获取文件报告详情信息
export const getFileReportDetail = (data: any) => {
  return request(`file/report/detail${parseQueryString(data)}`, {
    method: 'get',
  });
};
// 导出/下载文件（暂时不做流量包下载）
export const getFileReportBin = (data: any) => {
  return `file/report/binexport${parseQueryString(data)}`;
};


// 威胁扩线表格
export const threatList = (data: any) => {
  return request(`threat-expansion/tasks${parseQueryString(data)}`, {
    method: 'get'
  })
}
// 新增威胁扩线任务
export const addthreatListTask = (data: any) => {
  return request(`threat-expansion/tasks`, { method: 'post', data: data })
}
// 删除威胁扩线
export const deleteThreat = (id: any) => {
  return request(`threat-expansion/tasks/${id}`, { method: 'delete' })
}

// 执行威胁扩线任务
export const getAnalysis = (id: any) => {
  return request(`threat-expansion/tasks/${id}/run`, {
    method: 'post',

  })
}
export const getAnalysisTask = (id: any) => {
  return request(`threat-expansion/tasks/${id}`, {
    method: 'get'
  })
}
// 更新威胁扩线引擎
export const updateEngines = (id: any, data: any) => {
  return request(`threat-expansion/engines/${id}`, { method: 'put', data: data })
}
// 获取引擎列表
export const getEngines = () => {
  return request(`threat-expansion/engines`, { method: 'get' })
}

export const updateAnalysis = (id: any, data: any) => {
  return request(`threat-expansion/tasks/${id}`, { method: 'put', data: data })
}

export const dataSourceList = (data:any)=>{
  return request(`ip_analysis/audit${parseQueryString(data)}`)
}

export const maliceDetail = (data:any)=>{
  return request(``,{method:'get',data:data})
}

export const addDatasource = (data:any)=>{
  return request(`ip_analysis/audit`,{method:'post',data:data})
}

export const delDatasource = (data:any)=>{
  return request(`ip_analysis/audit`,{method:'delete',data:data})
}

export const taskList  = (data:any)=>{
  return request(`ip_analysis/retention${parseQueryString(data)}`)
}

export const actionTask = (data:any)=>{
  return request(`ip_analysis/retention`,{method:'put',data:data})
}

export const getTaskReslut = (data:any)=>{
  return request(`ip_analysis/result${parseQueryString(data)}`)
}

export const downloadResult = (data:any)=>{
  return request(`ip_analysis/result`,{method:'post',data:data})
}

export const createTask = (data:any)=>{
  return request(`ip_analysis/task`,{method:'post',data:data})
}
// 指纹库
export const fingerList = (data:any)=>{
 return request(`threat-expansion/aptorgs${parseQueryString(data)}`)
}

export const getHiveListData = (data:any)=>{
  return request(`cert/hives${parseQueryString(data)}`)
}

export const addHiveData = (data:any)=>{
  return request("cert/hives",{
    method:'post',
    data
  })
}

export const editHiveData = (id:string,data:any)=>{
  return request(`cert/hives/${id}`,{
    method:'put',
    data:data
  })
}
export const getHiveTemplate = ()=>{
  return request ('cert/hive_rule_template')
}

export const deleteHiveData = (id:string)=>{
  return request(`cert/hives/${id}`,{
    method:'delete'
  })
}

export const getHiveDetail = (id:string)=>{
  return request(`cert/hives/${id}`)
}

export const getIPtask = (data:any)=>{
  return request (`cert/analyis${parseQueryString(data)}`,)
}

export const deleteIPtask  = (id:string)=>{
  return request(``,{
    method:''
  })
}

export const addIPtask = (data:any)=>{
  return request('cert/analyis',{
    method:'post',
    data:data
  })
}

export const editIptask = (data:any)=>{
  return request('cert/analyis',{
    method:'put',
    data:data
  })
}

export const getResultData = (data:any)=>{
  return request(`cert/analyis/result${parseQueryString(data)}`)
}

export const downloadResultData = (data:any)=>{
  return request(`cert/analyis/result/download`,{
    method:'post',
    responseType: 'blob',
    data:data
  })
}
export const exportReportFile = (data:any)=>{
  return request('cert/analyis/export',{
    method:'post',
    responseType: 'blob',
    data:data
  })
}

export const filingInformation = (data:any)=>{
  return request(`cert/ipc_search`,{
    method:'post',
    data:data
  })
}

export const startDataSource = (id:string)=>{
  return request(`cert/hives/${id}/run`,{
    method:'post',
  })
}

export const stopDataSource = (id:string)=>{
  return request(`cert/hives/${id}/stop`,{
    method:'post'
  })
}

export const setFlag = (data:any)=>{
  return request(`cert/analyis/set_flag`,{
    method:'post',
    data:data
  })
}

export const getLogsData = (data:any)=>{
  return request(`cert/analyis/detail_result${parseQueryString(data)}`,{
    method:'get'
  })
}

export const exportLogsFile = (data:any)=>{
  return request('cert/analyis/result/detail_download',{
    method:'post',
    responseType: 'blob',
    data:data
  })
}


export const getChooseReportData = (data:any)=>{
  return request(`cert/analyis/get_result`,{
    method:'post',
    data:data
  })
}