#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import time

from api_1_0.utils.flask_log import ndr_log_to_box
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator
from config.config import NdrLog
from utils import param_check
from flask import request

from .zeye import zoomeye_singleton

TABLE_NAME = 'threat_expansion_engines'

ENGINE_FIELDS = {
    "name": "",
    "username": "",
    "password": "",
    "api_key": "",
}


def data_from_args(args):
    data = {}
    for k in ENGINE_FIELDS:
        if k in args:
            data[k] = args.get(k)

    return data


def timestamp():
    return int(time.time() * 1000)


LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def engine_default(e):
    return {
        "_id": e,
        "name": e,
        "username": "",
        "password": "",
        "api_key": ""
    }


def run_task(engine, dock):
    mongodb = MongoDB("ndr")
    data = mongodb.find_one(TABLE_NAME, {"name": engine})
    if data is None:
        data = engine_default(engine)

    if engine == 'zoomeye':
        if not data.get('api_key', ''):
            raise ValueError("api_key not set")

        zoomeye_singleton.set_api_key(data.get('api_key', ''))
        result = zoomeye_singleton.search(dock)
    else:
        raise ValueError("engine not found")

    return result


class Engines(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def get(self):
        """
        获取引擎列表
        """
        try:
            result = self.mongodb.find(self.table_name, {})
            data = []
            for r in result:
                data.append(r)

            engines = ['zoomeye', 'shodan', 'fofa', 'censys', 'other']
            for r in data:
                if r['name'] in engines:
                    engines.remove(r['name'])

            for e in engines:
                data.append(engine_default(e))

            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Get threat expansion engines ERROR: %s' % e)
            return flask_response('failed', False, {})


class Engine(Resource):
    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.table_name = TABLE_NAME

    def get(self, **kwargs):
        """
        获取引擎
        """
        try:
            f = {'_id': kwargs['engine_id']}
            data = self.mongodb.find_one(self.table_name, f)
            if data is None:
                data = {}
            else:
                data["_id"] = data["_id"]
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Get threat expansion engine ERROR: %s' % e)
            return flask_response('failed', False, {})

    @param_check.check_flask_args(Validator("threat_expansion_engine_schema"), request)
    def put(self, **kwargs):
        """
        更新引擎
        """
        f = {'_id': kwargs['engine_id']}
        args = kwargs['args']
        data = data_from_args(args)

        try:
            r = self.mongodb.update_one(self.table_name, f, {"$set": data}, insert=True)
            if r == 0:
                raise ValueError("update failed")

            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析引擎更新成功")
            return flask_response('', True, data)
        except Exception as e:
            LOG.error('Update threat expansion engines ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析引擎更新失败")
            return flask_response('update failed', False, {})

    @param_check.check_flask_args(Validator("threat_expansion_engine_schema"), request)
    def patch(self, **kwargs):
        """
        修改引擎
        """
        return self.put(kwargs)

    def delete(self, **kwargs):
        """
        创建引擎
        """
        f = {'_id': kwargs['engine_id']}
        try:
            data = self.mongodb.find_one(self.table_name, f)
            if data:
                r = self.mongodb.delete(self.table_name, f)
                if r == 0:
                    raise ValueError("delete failed")

            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析引擎删除成功")
            return flask_response('', True, {})
        except Exception as e:
            LOG.error('Delete threat expansion engines ERROR: %s' % e)
            ndr_log_to_box(NdrLog.Type.OPERATE, "威胁扩线分析引擎删除失败")
            return flask_response('delete failed', False, {})
