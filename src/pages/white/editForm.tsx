/*
 * @Author: 田浩
 * @Date: 2021-09-30 14:14:55
 * @LastEditors: tianh
 * @LastEditTime: 2022-01-12 17:18:10
 * @Descripttion:
 */
import React, { forwardRef } from 'react';
import { Form, Input, Select, InputNumber, DatePicker } from 'antd';
import { WhiteType } from '@/utils/enumList';
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};
const { Option } = Select;

const EditForm = forwardRef((props: any, ref: any) => {
  const { value, isAdd } = props;
  const [form] = Form.useForm();
  return (
    <div>
      <Form form={form} ref={ref} initialValues={value} {...layout}>
        <Form.Item
          label="新增白名单"
          name="target"
          rules={[{ required: true, message: '请输入目标' }]}
        >
          <Select
            mode="tags"
            showSearch={false}
            open={false}
            tokenSeparators={[' ']}
            disabled={!isAdd}
          />
        </Form.Item>
        <Form.Item
          label="类型"
          name="type"
          rules={[{ required: true, message: '选择类型' }]}
        >
          <Select disabled={!isAdd}>
            {WhiteType.map(item => {
              return (
                <Option value={item.value} key={item.key}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item label="备注" name="tag">
          <Input />
        </Form.Item>
      </Form>
    </div>
  );
});
export default EditForm;
