
syntax = "proto3";

package port;

// The acl service definition.
service PortService {
  rpc GetPorts (PortRequest) returns (PortResponse) {}
  rpc GetPortLinks (PortLinkRequest) returns (PortLinkResponse) {}
  rpc CreateForwardPolicy (CreateForwardPolicyRequest) returns (ReplyHeader) {}
  rpc DeleteForwardPolicy (DeleteForwardPolicyRequest) returns (ReplyHeader) {}
  rpc LinkModeSet (LinkModeSetRequest) returns (ReplyHeader) {}
  rpc IPv4ConfigSet(IPv4ConfigSetRequest) returns (ReplyHeader){}
  rpc MirroringPort(MirroringPortRequest) returns (ReplyHeader){}
  rpc UndoMirroringPort(UndoMirroringPortRequest) returns (ReplyHeader){}
  rpc IPv6ConfigSet(IPv6ConfigSetRequest) returns (ReplyHeader){}
}

message ReplyHeader {
  uint32 code = 1;
  string message = 2;
}

message PortRequest {

}

message PortResponse {
  ReplyHeader msg = 1;
  repeated PortInfo ports = 2;
}

message PortInfo {
  uint32 port_id = 1;
  uint32 port_status = 2;
  uint32 link_mode = 3;
  uint32 address = 4;
  uint32 netmask = 5;
  bytes ether_address = 6;
}

message PortLinkRequest {

}

message PortLinkResponse {
  ReplyHeader msg = 1;
  repeated PortLinkStatus status_list = 2;
}

message PortLinkStatus {
  uint32 port_id = 1;
  uint32 link_status = 2;
}

message CreateForwardPolicyRequest {
  uint32 mode = 1;
  uint32 in_port = 2;
  uint32 out_port = 3;
}

message DeleteForwardPolicyRequest {
  uint32 in_port = 1;
}

message LinkModeSetRequest {
  uint32 port_id = 1;
  uint32 mode = 2;
}

message IPv4ConfigSetRequest {
  uint32 port_id = 1;
  string address = 2;
  string netmask = 3;
}

message MirroringPortRequest {
  uint32 port_id = 1;
  uint32 group_id = 2;
  uint32 direction = 3;
}

message UndoMirroringPortRequest {
  uint32 port_id = 1;
}

message IPv6ConfigSetRequest {
  uint32 port_id = 1;
  string address = 2;
  uint32 prefix_len = 3;
}