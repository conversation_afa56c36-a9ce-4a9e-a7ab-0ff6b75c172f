#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : init_anomaly_detection.py
# @Software: PyCharm

"""异常检测系统初始化脚本"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api_1_0.anomaly_detection.init_database import init_anomaly_detection_database, create_sample_data
from utils.logger import get_ndr_logger
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def main():
    """主函数"""
    print("=" * 60)
    print("NDR异常检测系统初始化")
    print("=" * 60)
    
    try:
        # 1. 初始化数据库
        print("\n1. 初始化数据库...")
        init_anomaly_detection_database()
        print("✓ 数据库初始化完成")
        
        # 2. 创建示例数据
        print("\n2. 创建示例数据...")
        create_sample_data()
        print("✓ 示例数据创建完成")
        
        # 3. 验证初始化结果
        print("\n3. 验证初始化结果...")
        verify_initialization()
        print("✓ 初始化验证通过")
        
        print("\n" + "=" * 60)
        print("🎉 异常检测系统初始化完成！")
        print("=" * 60)
        
        print("\n📋 系统功能说明:")
        print("• 5个核心检测模型：时间序列趋势、基线偏移、重复连接、长连接、地理异常")
        print("• 支持多模型组合检测，默认全选")
        print("• 支持历史回溯和定时检测两种任务类型")
        print("• 支持IP地址和域名检测目标")
        print("• 模型参数统一配置管理")
        print("• 自动化任务调度和告警")
        print("• Web界面管理和分析")

        print("\n🚀 下一步操作:")
        print("1. 启动NDR系统")
        print("2. 访问Web界面：异常流量和行为分析")
        print("3. 在模型配置页面调整检测参数")
        print("4. 在任务管理页面创建检测任务")
        print("5. 查看检测结果和告警")
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {str(e)}")
        LOG.error(f"异常检测系统初始化失败: {str(e)}")
        sys.exit(1)


def verify_initialization():
    """验证初始化结果"""
    from utils.database import MongoDB
    
    mongodb = MongoDB('ndr')
    
    # 检查集合是否存在
    collections = ['anomaly_tasks', 'anomaly_results', 'anomaly_alerts', 'model_configs']
    for collection in collections:
        count = mongodb.count(collection, {})
        print(f"  - {collection}: {count} 条记录")

    # 检查索引
    for collection in collections:
        try:
            indexes = mongodb.list_indexes(collection)
            print(f"  - {collection} 索引数量: {len(indexes)}")
        except Exception as e:
            print(f"  - {collection} 索引检查失败: {str(e)}")


if __name__ == '__main__':
    main()
