import CodeHighlight from '@/components/CodeHighlight';
import KillChainStages from '@/components/KillChainStages/index';
import { getKillchains, threatClassify } from '@/services/alarm';
import {
  EditRule,
  addRule,
  delCustom,
  disableData,
  enableData,
  getFeatureTable,
  getGroupData,
  groupDelData,
} from '@/services/feature';
import {
  controlStatus,
  featureStatus,
  markType,
  threatFlagImg,
  threatFlagNameMap
} from '@/utils/enumList';
import { ellipsis, getQueryVariable } from '@/utils/utils';
import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Form,
  Input,
  Modal,
  Popconfirm,
  Row,
  Select,
  Spin,
  Table,
  Tooltip,
  message
} from 'antd';
import cn from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import AddForm from './addForm';
import { InterFeatureParams } from "./interface";
import store from './store';
import style from './style.less';

const { Option } = Select;

const Single = (props: any) => {
  const [form] = Form.useForm();
  const formRef = useRef<any | null>();
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>(
    'checkbox',
  );
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const [visible, setvisible] = useState(false);
  const [isAdd, setisAdd] = useState(false);
  const [killchainList, setkillchainList] = useState([]);
  const [threatClassifyList, setthreatClassifyList] = useState([]);
  const [params, setparams] = useState<InterFeatureParams>({
    page: 1,
    group_id: '',
    number: '',
    pageSize: 10,
  });
  const [loading, setloading] = useState(false);
  const [tableData, settableData] = useState<any[]>([]);
  const [formValue, setformValue] = useState({ sid: null });
  const [total, settotal] = useState(0);
  const [groupList, setgroupList] = useState([]);
  const [addVisible, setaddVisible] = useState(false);
  const [toGroup, settoGroup] = useState({ title: '', value: '' });

  const groupColumns = [
    {
      title: '状态',
      dataIndex: 'featureStatus',
      render: (t: string) => {
        if (t === 'disable') {
          return `禁用`;
        } else if (t === 'enable' || t === 'active') {
          return `启用`;
        } else {
          return `过时`;
        }
      },
    },
    {
      title: '编号',
      dataIndex: 'sid',
    },
    {
      title: '告警名称',
      dataIndex: 'vulName',
      render: (t: string) => {
        return (
          <Tooltip title={t}>
            <span>{ellipsis(t, 15)}</span>
          </Tooltip>
        );
      },
      // width: '5%',
    },
    {
      title: '特征内容',
      dataIndex: 'featureContent',
      width: '10%',
      render: (t: string) => {
        return (
          <div>
            <Tooltip
              overlayClassName="code-highlight"
              title={<CodeHighlight hasPre={false} language="snort" code={t} />}
              overlayStyle={{ maxWidth: 500 }}
            >
              <div style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}>
                {ellipsis(t, 15)}
              </div>
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: '杀伤链阶段',
      dataIndex: 'lockheedKillchainEN',
      className: '!text-center',
      render: (t: string[]) => {
        return <KillChainStages data={t} />;
      },
    },
    {
      title: '威胁分类',
      dataIndex: 'threatFlag',
      className: '!text-center',
      render: (t: string | number) => (
        <div className={cn('inline-flex items-center', style.textCenter)}>
          <Tooltip title={threatFlagNameMap[t]}>
            <img className="w-4 h-4" src={threatFlagImg[t]} alt="" />
          </Tooltip>
          <div className="ml-1">{threatFlagNameMap[t]}</div>
        </div>
      ),
    },
    {
      title: '威胁得分',
      dataIndex: 'threatScore',
    },
    // {
    //   title: '创建者',
    //   dataIndex: 'author',
    // },
    // {
    //   title: '用户',
    //   dataIndex: 'owner',
    // },
    {
      title: '创建时间',
      dataIndex: 'submitTime',
    },
    {
      title: '状态变更源',
      dataIndex: 'control',
      render: (t: string | number) => (
        <div>{controlStatus.filter(item => item.value === t)[0]?.key}</div>
      ),
    },
    {
      title: '操作',
      render: (t: string, record: { featureStatus: string, isCustomer: boolean }) => {
        const isActive = record?.featureStatus === 'enable' || record?.featureStatus === 'active';
        return (
          <div>
            {record.isCustomer ? (
              <div>
                <Tooltip overlay={isActive ? '已启用的特征不允许操作' : null}>
                  <Button
                    type="link"
                    disabled={isActive}
                    onClick={() => {
                      openModal(record);
                    }}
                  >
                    编辑
                  </Button>
                </Tooltip>
                <Popconfirm
                  title="确定删除吗？"
                  okText="确定"
                  cancelText="取消"
                  onConfirm={() => deleteCurrentList(record)}
                >
                  <Tooltip overlay={isActive ? '已启用的特征不允许操作' : null}>
                    <Button
                      type="link"
                      disabled={isActive}
                    >删除</Button>
                  </Tooltip>
                </Popconfirm>
              </div>
            ) : null}
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    getGruopName();
    getSelkillchain();
    getThreatClassify();
    if (getQueryVariable('number')) {
      params.number = getQueryVariable('number');
      // setparams(params);
    }
    params.group_id = store.getState().group.groupId;
    form.setFieldsValue(params);
    setparams(params);
  }, []);

  useEffect(() => {
    getTableData();
  }, [params]);

  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      delete value.group_id;
      setparams({ ...params, ...value, page: 1, pageSize: 10 });
    });
  };

  // 杀伤链阶段select
  const getSelkillchain = () => {
    getKillchains().then(res => {
      setkillchainList(res.data);
    });
  };

  // 威胁分类
  const getThreatClassify = () => {
    threatClassify().then(res => {
      setthreatClassifyList(res.data);
    });
  };

  const getTableData = () => {
    setloading(true);
    getFeatureTable(params).then(res => {
      setloading(false);
      if (res.flag) {
        settableData(res.data.sidList);
        settotal(res.data.search_count);
        setselectedRowKeys([]);
      } else {
        message.error(res.message);
      }
    });
  };

  const rowSelection = {
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    getCheckboxProps(record: any) {
      return {
        disabled: record.featureStatus === 'deprecated',
      };
    },
    selectedRowKeys,
  };

  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: params.pageSize,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };

  const handleEnble = () => {
    if (!selectedRowKeys.length) {
      message.error('请选择数据');
      return;
    }
    let _params = {
      action: 'enable',
      sidList: [],
    };
    selectedRowKeys.forEach((item: any) => {
      if (tableData[item].featureStatus !== 'active') {
        _params.sidList.push(tableData[item].sid);
      }
    });
    if (_params.sidList && _params.sidList.length) {
      enableData(_params).then(res => {
        if (res.flag) {
          message.success('操作成功');

          getTableData();
        }
      });
    }
  };

  const handleDisable = () => {
    if (!selectedRowKeys.length) {
      message.error('请选择数据');
      return;
    }
    let _params = {
      action: 'disable',
      sidList: [],
    };

    selectedRowKeys.forEach((item: any) => {
      if (tableData[item].featureStatus !== 'active') {
        _params.sidList.push(tableData[item].sid);
      }
    });
    if (_params.sidList && _params.sidList.length) {
      disableData(_params).then(res => {
        if (res.flag) {
          message.success('操作成功');
          getTableData();
        }
      });
    }
  };

  const openModal = (value: any) => {
    setformValue(value);
    setisAdd(false);
    setvisible(true);
  };
  const deleteCurrentList = (value: any) => {
    delCustom({ sid: value.sid }).then(res => {
      if (res.flag) {
        message.success(res.message);
        getTableData();
      } else {
        message.error(res.message);
      }
    });
  };

  const handleOk = () => {
    if (formRef) {
      formRef.current?.validateFields().then((value: any) => {
        if (isAdd) {
          addRule(value).then(res => {
            if (res.flag) {
              message.success('操作成功');
              formRef.current?.resetFields();
              getTableData();
              setvisible(false);
            } else {
              message.error(res.message);
            }
          });
        } else {
          value.sid = formValue.sid;
          EditRule(value).then(res => {
            if (res.flag) {
              message.success('操作成功');
              formRef.current?.resetFields();
              getTableData();
              setvisible(false);
            } else {
              message.error(res.message);
            }
          });
        }
      });
    }
  };
  const handleCancel = () => {
    setvisible(false);
  };
  const handleAdd = () => {
    setformValue({ sid: null });
    setisAdd(true);
    setvisible(true);
  };

  // 获取特征组名
  const getGruopName = () => {
    getGroupData({ enable: true, page: 1 }).then(res => {
      if (res.flag) {
        setgroupList(res.data.detail);
      } else {
        message.error(res.message);
      }
    });
  };

  // 添加到组
  const addToGroup = () => {
    if (!selectedRowKeys.length) {
      message.error('请选择数据');
      return;
    }
    getGruopName();
    setaddVisible(true);
  };
  //  从组内删除
  const delFromGroup = () => {
    if (!selectedRowKeys.length) {
      message.error('请选择数据');
      return;
    }
    let _params = {
      action: 'del',
      group_id: params.group_id,
      sidList: [],
    };
    selectedRowKeys.forEach(item => {
      _params.sidList.push(tableData[item].sid);
    });
    groupDelData(_params).then(res => {
      if (res.flag) {
        message.success('操作成功');
        getTableData();
      } else {
        message.error(res.message);
      }
    });
  };
  const getAddGroup = (value: any, e: any) => {
    settoGroup(e.props);
  };
  const handleAddGroup = () => {
    if (!toGroup.value && toGroup.title) {
      message.error('请选择添加到组');
      return;
    }
    let _params = {
      action: 'add',
      group_id: toGroup.value,
      name: toGroup.title,
      sidList: [],
    };
    selectedRowKeys.forEach(item => {
      _params.sidList.push(tableData[item].sid);
    });
    groupDelData(_params).then(res => {
      if (res.flag) {
        message.success('操作成功');
        cancelGroup();
        getTableData();
      } else {
        message.error(res.message);
      }
    });
  };
  const cancelGroup = () => {
    setaddVisible(false);
  };
  const changeId = (value: string) => {
    if (!value) {
      setparams({ ...params, group_id: '' });
    } else {
      setparams({ ...params, group_id: value });
    }
    const action = {
      type: 'changeGroup',
      value: { groupId: value },
    };
    store.dispatch(action);
  };
  const formItemLayout = {
    labelCol: {
      span: 9, // * ≥576px
    },
    wrapperCol: {
      span: 15,
    },
  };
  return (
    <Spin spinning={loading}>
      <Form
        className="!p-4 bg-d0 b b-solid b-brd6 form-inline"
        form={form}
        onFinish={onSubmit}
        layout="inline"
        initialValues={params}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              label="特征编号"
              name="number"
            >
              <Input placeholder='请输入特征编号' allowClear />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="杀伤链阶段" name="killChains">
              <Select allowClear placeholder='请选择杀伤链阶段'>
                {killchainList.map((item: any) => (
                  <Option
                    key={item.lockheedKillchainEN}
                    value={item.lockheedKillchainEN}
                  >
                    {item.lockheedKillchainCN}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="威胁分类" name="threatFlag">
              <Select allowClear placeholder='请选择威胁分类'>
                {threatClassifyList.map((item: any, index) => (
                  <Option key={item.threatFlag} value={item.threatFlag}>
                    {item.threatFlagCN}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label={<span className="w-5em">告警名称</span>}
              initialValue={params.group_id}
              name="vulInfo"
            >
              <Input allowClear placeholder='请输入告警名称' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item label="特征来源" name="mark">
              <Select allowClear placeholder='请选择特征来源'>
                {markType.map(item => {
                  return (
                    <Option key={item.key} value={item.value}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label={<span className="w-5em">规则状态</span>} name="featureStatus">
              <Select allowClear placeholder="请选择">
                {featureStatus.map(item => {
                  return (
                    <Option key={item.key} value={item.value}>
                      {item.key}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            {groupList ? (
              <Form.Item
                label="特征组名"
                name="group_id"
              // initialValue={params.group_id}
              >
                <Select
                  allowClear
                  placeholder='请选择特征组名'
                  showSearch
                  // labelInValue={true}
                  onChange={changeId}
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.props.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {groupList.map((item: any) => {
                    return (
                      <Option value={item.groupId} key={item.groupId}>
                        {item.groupName}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            ) : null}
          </Col>
          <Col span={6}>
            <Form.Item label="状态变更源" name="control">
              <Select allowClear placeholder="请选择">
                {React.useMemo(() => controlStatus.map(({ key, value }) => (
                  <Option value={value}>
                    {key}
                  </Option>
                )), [controlStatus])}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <div className="mt-2 flex gap-3">
          <Button
            type="primary"
            htmlType="submit"
            icon={<SearchOutlined />}
          >
            搜索
          </Button>
          <Button ghost type="primary" onClick={handleEnble}>
            启用
          </Button>
          <Button danger onClick={handleDisable}>
            禁用
          </Button>
          <Button ghost type="primary" onClick={handleAdd}>
            自定义特征
          </Button>
          {params.group_id ? (
            <Button danger onClick={delFromGroup}>
              从组内删除
            </Button>
          ) : (
            <Button ghost type="primary" onClick={addToGroup}>
              添加到组
            </Button>
          )}
        </div>
      </Form>
      <Table
        className="mt-4"
        rowSelection={{
          type: selectionType,
          ...rowSelection,
        }}
        rowKey={(r, i) => i}
        pagination={pagination}
        dataSource={tableData}
        columns={groupColumns}
      />
      <Modal
        destroyOnClose={true}
        title={isAdd ? '新增特征' : '编辑特征'}
        visible={visible}
        width={1000}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <AddForm
          ref={formRef}
          value={formValue}
          killchainList={killchainList}
        />
      </Modal>
      <Modal
        destroyOnClose={true}
        title="添加到组"
        visible={addVisible}
        onOk={handleAddGroup}
        onCancel={cancelGroup}
      >
        <Form layout="inline">
          <Form.Item label="选择组">
            <Select onChange={getAddGroup}>
              {groupList.map((item: any) => {
                return (
                  <Option
                    value={item.groupId}
                    title={item.groupName}
                    key={item.groupId}
                  >
                    {item.groupName}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Spin>
  );
};
export default Single;
