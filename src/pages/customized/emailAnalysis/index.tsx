import React, { useMemo, useRef, useState } from 'react';
import moment from 'moment';
import { Tabs } from 'antd';
import DateRangePicker from '@/pages/alarm/alert/AlertView/DateRangePicker';
// import DateRangePicker from '@/components/DateRangePicker';
import EmailList from './EmailList';
import EmailAttachmentList from './EmailAttachmentList';
import EmailTargetedAttackList from './EmailTargetedAttackList';

const { TabPane } = Tabs

const range = JSON.parse(sessionStorage.getItem('dateRange') || '[]')

function EmailAnalysis(): JSX.Element {
  const [dateRange, setDateRange] = useState((range.length ? range : [moment().startOf('day'), moment().endOf('day')])?.map((x: number) => moment(x)))
  const [currentType, setCurrentType] = useState('emailList');

  const emailListRef = useRef<{ search: (params: any) => void }>(null);
  const emailAttachmentListRef = useRef<{ search: (params: any) => void }>(null);

  const currentTimeRange = useMemo(() => (
    {
      start_time: dateRange[0].valueOf(),
      end_time: dateRange[1].valueOf()
    }
  ), [dateRange])

  const handleDateRangeChange = (dateRange?: any) => {
    setDateRange(dateRange);
    sessionStorage.setItem('dateRange', JSON.stringify(dateRange?.map((x: moment.Moment) => x.toDate().getTime())))
  }

  const handleJumpSearch = (key: string, params: any) => {
    setCurrentType(key)
    if (key === 'emailList') {
      if (!emailListRef.current) {
        setTimeout(() => {
          emailListRef?.current?.search(params)
        }, 0)
        return
      }
      emailListRef?.current?.search(params)
    }

    if (key === 'emailAttachmentList') {
      if (!emailAttachmentListRef.current) {
        setTimeout(() => {
          emailAttachmentListRef?.current?.search(params)
        }, 0)
        return;
      }
      emailAttachmentListRef?.current?.search(params)
    }
  }

  return (
    <div style={{ minHeight: 'calc(100vh - 75px)' }} >
      <DateRangePicker
        className="fixed top-2 left-65 z-20"
        value={dateRange}
        onChange={handleDateRangeChange}
      />
      <Tabs
        tabBarStyle={{ marginBottom: 0 }}
        defaultActiveKey="emailList"
        activeKey={currentType}
        onChange={setCurrentType}
        destroyInactiveTabPane
        className='bg-white px-4!'
      >
        <TabPane tab="邮件列表" key="emailList" >
          <EmailList ref={emailListRef} timeRange={currentTimeRange} onJumpSearch={handleJumpSearch} />
        </TabPane>
        <TabPane tab="邮件附件列表" key="emailAttachmentList">
          <EmailAttachmentList ref={emailAttachmentListRef} timeRange={currentTimeRange} onJumpSearch={handleJumpSearch} />
        </TabPane>
        <TabPane tab="邮件定向攻击" key="emailAttack">
          <EmailTargetedAttackList timeRange={currentTimeRange} onJumpSearch={handleJumpSearch} />
        </TabPane>
      </Tabs>
    </div>
  );
}

export default EmailAnalysis;