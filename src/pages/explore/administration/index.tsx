import React, { useState } from 'react';
import { Tabs } from 'antd';
import Offline from './offline';
import Online from './online';
const { TabPane } = Tabs;
const Index = (props: any) => {
  const [activeKey, setactiveKey] = useState('1');
  const onChange = (value: any) => {
    setactiveKey(value);
  };
  return (
    <div>
      <Tabs activeKey={activeKey} onChange={onChange}>
        <TabPane tab="离线数据源" key="1">
          {activeKey === '1' ? <Offline /> : null}
        </TabPane>
        <TabPane tab="在线数据源" key="2">
          {activeKey === '2' ? <Online /> : null}
        </TabPane>
      </Tabs>
    </div>
  );
};
export default Index;
