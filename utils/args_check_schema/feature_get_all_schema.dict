{
	"type": "object",
	"required": ["page"],
	"properties": {
		"killChains": {
			"type": "string",
			"default": "",
			"pattern": r'''^(Reconnaissance|Weaponization|Delivery|Exploitation|Installation|Command and Control|Actions on Objective)(-Reconnaissance|-Weaponization|-Delivery|-Exploitation|-Installation|-Command and Control|-Actions on Objective){0,6}$'''
        },
		"threatFlag": {
			"type": "string",
			"default": "",
			"enum": ["Exploits and Attacks", "APT", "Malicious Host", "Suspicious", "Botnet", "Phishing",
				"Scanning", "Malware", "DOS", "Trojan", "Mining", "Ransomware", "Spyware", "Webshell", "URL_malware",
				"Brute force"]
		},
		"vulType": {
			"type": "string",
			"pattern": r'''^([\w-]{0,20})$''',
			"default": ""
		},
		"vulInfo": {
			"type": "string",
			"default": ""
		},
		"lockheedKillchainCN": {
			"type": "string",
			"enum": ["侦查跟踪", "武器构建", "载荷投递", "漏洞利用", "安装植入", "命令控制", "目标达成"],
			"default": ""
		},
		"alterInfo": {
			"type": "string",
			"pattern": r'''^([\w]{0,150})$''',
			"default": ""
		},
	    "threatMinScore": {
            "type": "string",
			"default": "-1",
			"pattern": r"^([0-9]|[1-9][0-9]|100)$"
        },
        "threatMaxScore": {
            "type": "string",
			"default": "101",
			"pattern": r"^([1-9]|[1-9][0-9]|100)$"
        },
		"number": {
			"type": "string",
			"default": "0",
			"pattern": r'''(^[1-9]\d{0,8}$)|(^[1-3]\d{9}$)|(^4[0-1]\d{8}$)|(^42[0-8]\d{7}$)|(^429[0-3]\d{6}$)|(^4294[0-8]\d{5}$)|(^42949[0-5]\d{4}$)|(^429496[0-6]\d{3}$)|(^4294967[0-1]\d{2}$)|(^42949672[0-8]\d$)|(^429496729[0-5]$)'''
		},
		"pageSize": {
			"type": "string",
			"pattern": r'''[1-9]{1}\d{0,3}''',
			"default": "10"
		},
		"page": {
			"type": "string",
			"pattern": r'''[1-9]{1}\d{0,8}''',
			"default": "1"
		},
		"mark": {
			"type": "string",
			"pattern": r'''[1-9]{1}\d{0,8}''',
			"default": "0"
		},
		"featureStatus": {
			"type": "string",
			"default": "",
			"enum": ["disable", "enable", "deprecated"]
		},
		"group_id": {
			"type": "string",
			"default": ""
		},
		"control": {
			"type": "string",
			"default": ""
		}
	}
}