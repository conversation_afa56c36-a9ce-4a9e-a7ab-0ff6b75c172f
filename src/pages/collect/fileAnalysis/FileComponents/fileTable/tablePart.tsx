import { Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { get } from 'lodash';
import ReactJson from 'react-json-view';
import s from './style.less';
interface TablePartProps {
  row: any;
}
const TablePart: React.FC<TablePartProps> = ({ row }) => {
  const [tableObj, setTableObj] = useState({});
  const killchainList = JSON.parse(localStorage.killchainList);
  useEffect(() => {
    const tableObj_ = {
      ID: get(row, '_id', '- -'),
      索引名: get(row, '_index', '- -'),
      相关性评分: get(row, '_score', '- -'),
      索引类型: get(row, '_type', '- -'),
      文件还原时间: get(row, '_source.occurredTime', '- -'),
      目的IP: get(row, '_source.flow.dst_ip', '- -'),
      目的IP所在城市: get(row, '_source.dstIpGeoInfo.city_name', '- -'),
      目的IP所在国家: get(row, '_source.dstIpGeoInfo.country_name', '- -'),
      目的IP国家代码: get(row, '_source.dstIpGeoInfo.areacode', '- -'),
      目的IP经纬度: `${get(row, '_source.dstIpGeoInfo.longitude', '- -')},${get(
        row,
        '_source.dstIpGeoInfo.latitude',
        '- -',
      )}`,
      文件来源: get(row, '_source.file_origin', '- -'),
      文件名: get(row, '_source.filename', '- -'),
      文件大小: get(row, '_source.filesize', '- -'),
      文件ID: get(row, '_source.fuid', '- -'),
      基因: '- -',
      主机: get(row, '_source.hostName', '- -'),
      主机IP: get(row, '_source.hostIp', '- -'),
      知识库: '- -',
      攻击链阶段: killchainList[get(row, '_source.killchain', '')] || '- -',
      病毒家族: get(row, '_source.virus_name.jiangmin', '- -'),
      md5: get(row, '_source.md5', '- -'),
      病毒引擎: get(row, '_source.multiav', '- -'),
      文件类型: get(row, '_source.filetype', '- -'),
      文件报告: '- -',
      威胁等级: get(row, '_source.threatLevel', '- -'),
      sha256: get(row, '_source.sha256', '- -'),
      事件源: '- -',
      源IP: get(row, '_source.flow.src_ip', '- -'),
      源IP所在城市: get(row, '_source.srcIpGeoInfo.city_name', '- -'),
      源IP所在国家: get(row, '_source.srcIpGeoInfo.country_name', '- -'),
      源IP国家代码: get(row, '_source.srcIpGeoInfo.areacode', '- -'),
      源IP经纬度: `${get(row, '_source.srcIpGeoInfo.longitude', '- -')},${get(row, '_source.srcIpGeoInfo.latitude', '- -')}`,
      SSDEEP值: get(row, '_source.ssdeep', '- -'),
      威胁类型: get(row, '_source.threatType', '- -'),
      时间戳: get(row, '_source.ts', '- -'),
      数据包捕获时间: get(row, '_source.observedTime', '- -'),
      用户名: '- -',
    };
    setTableObj(tableObj_);
  }, [row]);
  return (
    <Tabs type="card" className={s.tablePart}>
      <Tabs.TabPane tab="Table" key="table">
        <div>
          {Object.entries(tableObj).map(([k, v]) => {
            return (
              <p className={s.tableP}>
                <span>{k}</span>
                <span>{v}</span>
              </p>
            );
          })}
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane tab="JSON" key="json">
        <ReactJson src={row} displayDataTypes={false} enableClipboard={false}></ReactJson>
      </Tabs.TabPane>
    </Tabs>
  );
};

export default TablePart;
