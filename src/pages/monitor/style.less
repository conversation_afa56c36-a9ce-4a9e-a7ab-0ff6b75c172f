.box {
  // padding: 20px;
}
.top_title {
  // width: 98%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px;
  background: #fff;
  margin-top: 20px;
}
.title {
  color: black;
  font-weight: bold;
  font-size: 16px;
  padding-right: 10px;
}
.item {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}
.cupCharts {
  width: 100%;
  height: 300px;
}
.memory {
  width: 100%;
  height: 300px;
}
.disk_box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 20px;
  background: #fff;
}
.disk_item {
  width: 33%;
  border: 1px solid #f0f0f0;
  padding: 10px;
}
.disk_text {
    padding-right: 8px;
}
.disk_charts {
    display: flex;
    width: 86%;
    margin: 0 auto;
}
.disk_use {
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  background: rgb(255, 128, 0);
  height: 30px;
}
.disk_noUse {
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  background:#59C94F;
  height: 30px;
}
.disk_topText {
  width: 86%;
  margin: 0 auto;
}
.disk_img {
  display: flex;
}
.diskName {
  width: 96px;
  text-align: right;
  line-height: 30px;

}
.disk_title {
  display: flex;
  justify-content: space-between;
}
.disk_bottomText {
  width: 86%;
  margin: 0 auto;
  display: flex;
  justify-content: space-evenly;
}

.flow {
  width:100%;
  height: 400px;
} 
.top {
  display: flex;
  justify-content: space-between;
}
.appFlow {
  width:100%;
  height: 400px;
}

.connect {
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
}
.connect_item {
  width:100%;
  height: 400px;
}

.alone_box {
  margin: 0 auto;
  margin-top: 20px;
}

.switch_box {
  display: inline-block;
  padding-right: 10px;
  transform:  translateY(-4px);
}
.detail_text {
  color: #000;
  padding-right: 10px;
}

.icons {
  cursor: pointer;
  padding-right: 6px;
}