#!/usr/bin/env python3
# -*- coding:utf-8 _*-
# @author: gh0st
# @license: Apache Licence
# @file: get_event_metadata.py
# @time: 2019/07/18
# @software: PyCharm


import datetime
import time

from celery_tasks import app, assets_queue
from celery_tasks.utils import <PERSON><PERSON><PERSON><PERSON>
from config.config import NdrLog
from utils.database import BaseMongoDB
from utils.logger import get_ndr_logger
from collections import Counter

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)
db = BaseMongoDB("ndr")
ch_client = ClickHouse()


# 每五分钟更新
@app.task()
def assets_update_min():
    try:
        present_time = int(time.mktime(datetime.datetime.now().timetuple()))
        history_time = present_time - 300
        sql = "select * from assests where create_time >= %s and create_time <= %s" % (history_time, present_time)
        rst_list = ch_client.get_val_from_database(sql)
        LOG.info("总共update ip数量 %s" % (len(rst_list)))
        for rst in rst_list:
            assest = rst["assest"]
            send_count = rst["send_count"]
            accept_count = rst["accept_count"]
            app_total_traffic = rst["app_total_traffic"]
            enable_port = set()
            app_traffic = {}
            send_traffic = {}
            access_sample = {}
            if rst["enable_port"]:
                enable_port = set(rst["enable_port"].split(","))
            if rst["app_traffic"]:
                data_list = rst["app_traffic"].split(",")
                for i in data_list:
                    app_list = i.split(":")
                    app_traffic[app_list[0]] = int(app_list[1])
            if rst["send_traffic"]:
                data_list = rst["send_traffic"].split(",")
                for i in data_list:
                    send_list = i.split(":")
                    send_traffic[send_list[0]] = int(send_list[1])
            if rst["access_sample"]:
                data_list = rst["access_sample"].split(",")
                for i in data_list:
                    sample_list = i.split(":")
                    access_sample[sample_list[0]] = sample_list[1].split("|")

            db_rst = db.find_one("assets_info", {"ip_addr": assest})
            if db_rst:
                # 周流量   当前时间取向上取整除4小时的整点时间
                week_time = int(((present_time + 14399) // 14400) * 14400)
                week_stream_list = db_rst["latest_week_stream"]
                flag = True
                for week_stream in week_stream_list:
                    if week_time == week_stream["time_stamp"]:
                        week_stream["total_bytes"] = week_stream["total_bytes"] + app_total_traffic
                        flag = False
                if flag:
                    week_stream_list.append({"time_stamp": week_time, "total_bytes": app_total_traffic})
                if len(week_stream_list) > 40:
                    week_stream_list.pop(0)
                popular = db_rst["popular"]
                if send_count or accept_count:
                    popular = popular + 1
                send_count = send_count + db_rst["send_count"]
                accept_count = accept_count + db_rst["accept_count"]
                labels = set(db_rst["labels"])
                if "PC" not in labels:
                    total_count = send_count + accept_count
                    if total_count > 10000 and send_count / total_count > 0.99:
                        labels.add("PC")
                port_set = set()
                db_set = {1433, 3306, 27017, 6379, 9083}
                for i in enable_port:
                    port_set.add(int(i))
                for key in db_rst["enable_port"].keys():
                    port_set.add(int(key))
                # for i in db_rst["enable_port"]:
                #     port_set.add(i)
                if 80 in port_set or 443 in port_set:
                    labels.add("WebServer")
                if 21 in port_set:
                    labels.add("FTP服务器")
                if 25 in port_set:
                    labels.add("邮件服务器")
                if 53 in port_set:
                    labels.add("DNS服务器")
                if db_set & port_set:
                    labels.add("数据库")
                for port in port_set:
                    db_rst["enable_port"][str(port)] = int(time.time())
                app_total_traffic = app_total_traffic + db_rst["app_total_traffic"]
                app_traffic = dict(Counter(db_rst["app_traffic"]) + Counter(app_traffic))
                send_traffic = dict(Counter(db_rst["send_traffic"]) + Counter(send_traffic))
                # 把结果按value值排序，最后只保留TOP-N
                sort_app_traffic = {item[0]: item[1] for item in
                                    sorted(app_traffic.items(), key=lambda item: item[1])[-20:]}
                sort_send_traffic = {item[0]: item[1] for item in
                                     sorted(send_traffic.items(), key=lambda item: item[1])[-100:]}
                body_update = {
                    "send_count": send_count,
                    "accept_count": accept_count,
                    "enable_port": db_rst["enable_port"],
                    "labels": list(labels),
                    "popular": popular,
                    "app_total_traffic": app_total_traffic,
                    "app_traffic": sort_app_traffic,
                    "send_traffic": sort_send_traffic,
                    "latest_week_stream": week_stream_list,
                    "update_time": int(time.time() * 1000)
                }
                if access_sample:
                    body_update["access_sample"] = access_sample
                db.update_many("assets_info", {"ip_addr": assest}, body_update)
    except Exception as e:
        LOG.error('reason[{0}]'.format(str(e)))
        return
