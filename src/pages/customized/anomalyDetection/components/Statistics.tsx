import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Spin,
  Empty,
  Typography,
} from 'antd';
import {
  Line<PERSON>hart,
  Line,
  <PERSON>Chart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { getAnomalyAlertStats } from '@/services/anomaly';
import styles from '../style.less';

const { Option } = Select;
const { Title } = Typography;

const Statistics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<any>({});
  const [timeRange, setTimeRange] = useState<string>('7d');

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await getAnomalyAlertStats();
      if (response.flag) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   fetchStats();
  // }, [timeRange]);

  // 颜色配置
  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
  const SEVERITY_COLORS = {
    critical: '#f5222d',
    high: '#fa8c16',
    medium: '#faad14',
    low: '#52c41a',
  };

  // 格式化数据
  const formatChartData = (data: any[]) => {
    return data?.map((item, index) => ({
      ...item,
      color: COLORS[index % COLORS.length],
    })) || [];
  };

  const formatHourlyData = (data: any[]) => {
    return data?.map(item => ({
      hour: `${item.hour}:00`,
      count: item.count,
    })) || [];
  };

  return (
    <div>
      {/* 时间范围选择 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              异常检测统计分析
            </Title>
          </Col>
          <Col>
            <Select
              value={timeRange}
              onChange={setTimeRange}
              style={{ width: 120 }}
            >
              <Option value="1d">最近1天</Option>
              <Option value="7d">最近7天</Option>
              <Option value="30d">最近30天</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {/* 概览统计 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="最近24小时告警"
                value={stats.alerts_24h || 0}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="最近7天告警"
                value={stats.alerts_7d || 0}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="未处理告警"
                value={stats.unhandled_alerts || 0}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="高危告警"
                value={stats.high_severity_alerts || 0}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={16}>
          {/* 24小时趋势图 */}
          <Col span={12}>
            <Card title="24小时告警趋势" className={styles.chartContainer}>
              {stats.hourly_stats?.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={formatHourlyData(stats.hourly_stats)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="count"
                      stroke="#1890ff"
                      strokeWidth={2}
                      dot={{ fill: '#1890ff' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 严重程度分布 */}
          <Col span={12}>
            <Card title="告警严重程度分布" className={styles.chartContainer}>
              {stats.severity_stats?.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={stats.severity_stats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ _id, count, percent }) => 
                        `${_id}: ${count} (${(percent * 100).toFixed(0)}%)`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {stats.severity_stats.map((entry: any, index: number) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={SEVERITY_COLORS[entry._id] || COLORS[index % COLORS.length]} 
                        />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={16} style={{ marginTop: 16 }}>
          {/* 检测模型分布 */}
          <Col span={12}>
            <Card title="检测模型分布" className={styles.chartContainer}>
              {stats.model_stats?.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={formatChartData(stats.model_stats)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="_id" 
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis />
                    <Tooltip 
                      formatter={(value, name) => [value, '告警数量']}
                      labelFormatter={(label) => {
                        const modelNames = {
                          time_series: '时间序列趋势',
                          baseline: '基线偏移检测',
                          repeat_conn: '重复连接检测',
                          long_conn: '长连接分析',
                          geo_anomaly: '地理/归属异常',
                        };
                        return modelNames[label] || label;
                      }}
                    />
                    <Bar dataKey="count" fill="#1890ff" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 处理状态统计 */}
          <Col span={12}>
            <Card title="告警处理状态" className={styles.chartContainer}>
              {stats.alert_stats?.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={stats.alert_stats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ _id, count, percent }) => {
                        const statusNames = {
                          pending: '待处理',
                          confirmed: '已确认',
                          dismissed: '已忽略',
                        };
                        return `${statusNames[_id] || _id}: ${count} (${(percent * 100).toFixed(0)}%)`;
                      }}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {stats.alert_stats.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
        </Row>

        {/* 详细统计表格 */}
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title="详细统计信息">
              <Row gutter={16}>
                <Col span={8}>
                  <Card size="small" title="检测模型统计">
                    {stats.model_stats?.map((item: any, index: number) => {
                      const modelNames = {
                        time_series: '时间序列趋势',
                        baseline: '基线偏移检测',
                        repeat_conn: '重复连接检测',
                        long_conn: '长连接分析',
                        geo_anomaly: '地理/归属异常',
                      };
                      return (
                        <div key={index} style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                          <span>{modelNames[item._id] || item._id}</span>
                          <span style={{ fontWeight: 'bold' }}>{item.count}</span>
                        </div>
                      );
                    })}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small" title="严重程度统计">
                    {stats.severity_stats?.map((item: any, index: number) => {
                      const severityNames = {
                        critical: '严重',
                        high: '高危',
                        medium: '中危',
                        low: '低危',
                      };
                      return (
                        <div key={index} style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                          <span style={{ color: SEVERITY_COLORS[item._id] }}>
                            {severityNames[item._id] || item._id}
                          </span>
                          <span style={{ fontWeight: 'bold' }}>{item.count}</span>
                        </div>
                      );
                    })}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small" title="处理状态统计">
                    {stats.alert_stats?.map((item: any, index: number) => {
                      const statusNames = {
                        pending: '待处理',
                        confirmed: '已确认',
                        dismissed: '已忽略',
                      };
                      return (
                        <div key={index} style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                          <span>{statusNames[item._id] || item._id}</span>
                          <span style={{ fontWeight: 'bold' }}>{item.count}</span>
                        </div>
                      );
                    })}
                  </Card>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default Statistics;
