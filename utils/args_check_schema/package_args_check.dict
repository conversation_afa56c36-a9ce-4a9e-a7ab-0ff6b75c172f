{
    "type": "object",
    "required": ["src_ip", "replay"],
    "properties": {
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "occurredTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "src_ip": {
		    "type":"string",
		    "format": "ipv4|ipv6",
		    "default": ""
		},
		"dst_ip": {
		    "type":"string",
		    "format": "ipv4|ipv6",
		    "default": ""
		},
		"src_port": {
		    "type":"string",
		    "default": ""
		},
		"dst_port": {
		    "type":"string",
		    "default": ""
		},
        "proto": {
		    "type":"string",
		    "default": ""
		},
		"download": {
            "type": "string",
            "enum": ["0", "1"],
            "default": "1"
        },
        "type": {
            "type": "string",
            "enum": ["killchain", "vul", "ioc", "model", "file"],
            "default": "1"
        },
        "replay": {
            "type": "string",
            "default": "false",
            "enum": ["true", "false"],
        },
        "pcap_filename": {
            "type": "string",
            "default": "",
        },
        "not_fss_pcapname": {
            "type": "string",
            "default": "",
        }
    }
}