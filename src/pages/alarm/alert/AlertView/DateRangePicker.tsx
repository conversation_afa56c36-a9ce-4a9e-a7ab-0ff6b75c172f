import type { TimeRangePickerProps } from 'antd';
import { DatePicker } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import React from 'react';
import ReactDOM from 'react-dom';


const ranges: Record<string, [Moment, Moment]> = {
    '最近一月': [moment().add(-1, 'month'), moment()],
    '最近一周': [moment().add(-1, 'week'), moment()],
    '最近一天': [moment().add(-1, 'day'), moment()],
    '最近一小时': [moment().add(-1, 'hour'), moment()],
    '今天': [moment().startOf('day'), moment().endOf('day')],
    '本周': [moment().startOf('week'), moment().endOf('week')],
    '本月': [moment().startOf('month'), moment().endOf('month')],
    '本年度': [moment().startOf('year'), moment().endOf('year')],
}

export default function DateRangePicker({ className, ...props }: TimeRangePickerProps) {
    const [dom, setDom] = React.useState<Element | null>(null);
    React.useEffect(() => {
        setDom(document.querySelector('#root-master > div > div > section > section'));
    }, []);

    if (!dom) return null;

    return ReactDOM.createPortal(
        <span className={className}>
            <span className="mr-2">选择时间范围：</span>
            <DatePicker.RangePicker
                allowClear={false}
                ranges={ranges}
                format="YYYY-MM-DD HH:mm:ss"
                {...props}
            />
        </span>,
        dom
    )
}