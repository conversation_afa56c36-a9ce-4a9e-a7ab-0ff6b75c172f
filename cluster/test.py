#!/usr/bin/env python3
# coding:utf-8

import sys
sys.path.append('/opt/ndr')

from cluster.api.manager import MyClusterManager
from cluster.api.consistency import Consistency

master = "192.168.122.1"
worker = "192.168.122.28"
my_cluster_mgr = MyClusterManager()
nodes = my_cluster_mgr.get_node_list_from_file()
if not nodes:
    my_cluster_mgr.create_cluster(master, [worker])
else:
    print(nodes)
my_cluster_mgr.echo(worker, "haha")
print(my_cluster_mgr.hdp_status_by_cluster())
print(my_cluster_mgr.file_hash_by_node(worker, '/opt/feature/db/KNDR_IOC.db'))
print(my_cluster_mgr.file_hash_by_cluster('/opt/feature/db/KNDR_IOC.db'))

consistency = Consistency()
print(consistency.check_files())

my_cluster_mgr.sync_file_safe_by_cluster('/opt/tmp/white_list.db', '/opt/tmp/white_list.db')

print(my_cluster_mgr.get_node_info(worker))