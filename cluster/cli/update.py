#!/usr/bin/env python3
# coding:utf-8

import sys
sys.path.append('/opt/ndr')

from os import environ

from ksp import config
from cluster.utils.logger import get_logger
from cluster.utils.upgrade import Upgrade
from cluster.api.manager import MyClusterManager

logger = get_logger(__name__)


class UpdateService:

    def __init__(self):
        self.mgmt_node_ip = config.get("cluster.mgmt_node_ip")
        self.cluster_mgr = MyClusterManager()

    def get_connect(self):
        conn = None
        try:
            conn, _ = self.cluster_mgr.connect_node_by_ssl(self.mgmt_node_ip)
            conn.ping()
        except Exception as err:
            logger.exception(err)
        return conn

    def do_update(self, update_type, update_mode):
        update = Upgrade()
        pkg_info_list = update.get_pkg_info_list_from_file(update_type)
        if not pkg_info_list:
            return
        for pkg_info in pkg_info_list:
            conn = self.get_connect()
            upgrade_log_id = update.record_upgrade_log_start(conn, update_mode, update_type)
            try:
                download_success = update.download_upgrade_file_from_master(conn, pkg_info, update_type)
            except Exception as e:
                logger.error(repr(e))
                update.record_upgrade_log_end(conn, {
                    'id': upgrade_log_id,
                    'status': 'failure',
                    'process_log': 'Download package from master failed'
                })
                conn.close()
                return
            if not download_success:
                update.record_upgrade_log_end(conn, {
                    'id': upgrade_log_id,
                    'status': 'failure',
                    'process_log': 'Download package from master failed'
                })
                conn.close()
                return
            conn.close()

            errorcode, stdout, stderr = update.do_upgrade(pkg_info['update_file_path'], update_type)

            conn = self.get_connect()
            if errorcode != 0:
                update.record_upgrade_log_end(conn, {
                    'id': upgrade_log_id,
                    'status': 'failure',
                    'process_log': stderr
                })
                conn.close()
                return
            update.record_upgrade_log_end(conn, {
                'id': upgrade_log_id,
                'status': 'success',
                'process_log': ''
            })
            conn.close()


def main():
    update_type = environ.get('UPGRADE_TYPE')
    update_mode = environ.get('UPGRADE_MODE')
    assert update_type and update_mode

    update = UpdateService()
    update.do_update(update_type=update_type, update_mode=update_mode)


if __name__ == "__main__":
    main()
