{
	"type": "object",
	"required": ["sid", "vulType", "lockheedKillchainEN", "threatFlag", "threatScore", "featureContent", "attackIp", "victimIp"],
	"properties": {
		"sid": {
			"type": "string",
			"default": ""
		},
		"author": {
			"type": "string",
			"default": "",
			"pattern": r"^.{0,64}$"
		},
		"appProto": {
			"type": "string",
			"default": "",
			"pattern": r"^.{0,64}$"
		},
		"vulName": {
			"type": "string",
			"default": "",
			"pattern": r"^.{0,64}$"
		},
		"vulType": {
			"type": "string",
			"default": "",
			"enum": [
			    "跨站脚本",
                "跨站请求伪造",
                "Sql注入",
                "ldap注入",
                "邮件命令注入",
                "空字节注入",
                "CRLF注入",
                "Ssi注入",
                "Xpath注入",
                "Xml注入",
                "Xquery 注入",
                "命令执行",
                "代码执行",
                "远程文件包含",
                "本地文件包含",
                "功能函数滥用",
                "暴力破解",
                "缓冲区溢出",
                "内容欺骗",
                "证书预测",
                "会话预测",
                "拒绝服务",
                "指纹识别",
                "格式化字符串",
                "http响应伪造",
                "http响应拆分",
                "http请求拆分",
                "http请求伪造",
                "http参数污染",
                "整数溢出",
                "可预测资源定位",
                "会话固定",
                "url重定向",
                "权限提升",
                "解析错误",
                "任意文件创建",
                "任意文件下载",
                "任意文件删除",
                "备份文件发现",
                "数据库发现",
                "目录遍历",
                "目录穿越",
                "文件上传",
                "登录绕过",
                "弱密码",
                "远程密码修改",
                "代码泄漏",
                "路径泄漏",
                "信息泄漏",
                "安全模式绕过",
                "挂马",
                "暗链",
                "后门"
			]
		},
		"cve": {
			"type": "string",
			"default": "",
			"pattern": r"^(|[cveCVE]{3}-\d{4}-\d+)$"
		},
		"lockheedKillchainEN": {
            "type": "string",
            "default": "",
            "pattern": r'''^(Reconnaissance|Weaponization|Delivery|Exploitation|Installation|Command and Control|Actions on Objective)(-Reconnaissance|-Weaponization|-Delivery|-Exploitation|-Installation|-Command and Control|-Actions on Objective){0,6}$'''
        },
		"threatFlag": {
			"type": "string",
			"default": "",
            "enum": ["Exploits and Attacks", "APT", "Malicious Host", "Suspicious", "Botnet", "Phishing",
				"Scanning", "Malware", "DOS", "Trojan", "Mining", "Ransomware", "Spyware", "Webshell", "URL_malware",
				"Brute force"]
		},
		"threatScore": {
			"type" : "number",
			"default": 0,
            "minimum": 0,
            "exclusiveMaximum": 101
		},
		"alterInfo": {
			"type": "string",
			"default": "",
			"pattern": r"^.{0,64}$"
		},
		"is0day": {
			"type": "boolean",
			"default": False,
			"enum": [True,False]
		},
		"featureContent": {
			"type": "string",
			"default": "",
			"pattern": r"^.{1,1024}$"
		},
		"attackIp": {
			"type": "string",
			"default": "",
			"enum": ["src_ip", "dst_ip"]
		},
		"victimIp": {
			"type": "string",
			"default": "",
			"enum": ["src_ip", "dst_ip"]
		},
		"featureStatus": {
			"type": "string",
			"default": "disable",
			"enum": ["enable","disable"]
		},
		"isCustomer": {
		    "type": "boolean",
			"default": True,
			"enum": [True,False]
		}
	}
}