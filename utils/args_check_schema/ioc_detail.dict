{
    "type": "object",
    "required": [],
    "properties": {
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "sort": {
            "type": "string",
            "default": "observedTime",
            "enum": ["observedTime", "threatScore"]
        },
        "celeryId": {
            "type": "string",
            "default": ""
        },
        "killchain": {
            "type": "string",
            "default": "",
            "enum": ["Reconnaissance", "Weaponization", "Delivery", "Exploitation", "Installation", "Command and Control", "Actions on Objective"]
        },
        "groupKey": {
            "type": "string",
            "default": "",
            "enum": ["uniqueId", "src_ip", "dst_ip"]
        },
        "threatLevel": {
            "type": "string",
            "default": "",
            "enum": ["Low", "Medium", "High"],
        },
         "threatScore": {
            "type": "string",
            "default": "",
            "pattern": r'''^([0-9]|[0-9][0-9]|100|\*)-([0-9]|[0-9][0-9]|100|\*)$'''
        },
        "celeryId": {
            "type": "string",
            "default": ""
        },
        "mbinfo": {
            "type": "string",
            "default": ""
        },
        "threatType": {
            "type": "string",
            "default": ""
        },
        "value": {
            "type": "string",
            "default": ""
        },
        "confirm" : {
            "type": "number",
            "enum": [0, 1, 2, 3, 4]
        },
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}