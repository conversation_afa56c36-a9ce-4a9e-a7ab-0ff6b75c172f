# -*- coding: utf-8 -*-
# @Time    : 2019-08-28 10:57
# <AUTHOR> Shark
# @File    : explore_task_stats.py
# @Software: PyCharm
""" 统计探索任务结果数据 """
from flask_restful import Resource

from api_1_0.knowledge.get_killchains import GetKillchains
from utils import database
from utils.es_template.get_es_template import ES_Template
from utils.es_function import get_threat_level, get_term_match
from utils.logger import get_ndr_logger


ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class ExploreTaskStatus(Resource):
    """根据task_id统计该任务的结果数据"""

    def __init__(self):
        """初始化参数"""
        # 初始化 ES 查询模板
        self.es_vul_template = ES_Template().read_template('explore_task_vul_agg')
        self.es_ioc_template = ES_Template().read_template('explore_task_ioc_agg')
        self.es_model_template = ES_Template().read_template('explore_task_model_agg')
        self.es_file_log_template = ES_Template().read_template('explore_task_file_log_agg')
        # 初始化 ES client
        self.es_client = database.get_es_client()

    def get(self, explore_id, id_type):
        """
        :param explore_id:
        :param id_type:
        :return:
        """
        self.format_es_template(explore_id, id_type)
        data = dict()
        try:
            res_vul = self.es_client.search(index="rule-eve", body=self.es_vul_template)
            data["vul"] = self.data_format(res_vul)
        except Exception as error:
            API_LOG.error('Params error.Reason: %s' % str(error))
            data["vul"] = self.get_res_template()
        try:
            res_ioc = self.es_client.search(index="ioc-eve", body=self.es_ioc_template)
            data["ioc"] = self.data_format(res_ioc)
        except Exception as error:
            API_LOG.error('Params error.Reason: %s' % str(error))
            data["ioc"] = self.get_res_template()
        try:
            res_model = self.es_client.search(index="model-eve", body=self.es_model_template)
            data["model"] = self.data_format(res_model)
        except Exception as error:
            API_LOG.error('Params error.Reason: %s' % str(error))
            data["model"] = self.get_res_template()
        try:
            res_file_log = self.es_client.search(index="file-eve", body=self.es_file_log_template)
            data["file_log"] = self.data_format_file_log(res_file_log)
        except Exception as error:
            API_LOG.error('Params error.Reason: %s' % str(error))
            data["file_log"] = self.get_res_template_file_log()

        return data

    def format_es_template(self, explore_id, id_type):
        """ 通过传入参数填充ES查询聚合语句 """
        if id_type == "celeryId":
            self.es_vul_template["query"]["bool"]["must"].append(get_term_match(explore_id, "celeryId.keyword"))
            self.es_ioc_template["query"]["bool"]["must"].append(get_term_match(explore_id, "celeryId.keyword"))
            self.es_model_template["query"]["bool"]["must"].append(get_term_match(explore_id, "celeryId.keyword"))
            self.es_file_log_template["query"]["bool"]["must"].append(get_term_match(explore_id, "celeryId.keyword"))
        else:
            self.es_vul_template["query"]["bool"]["must"].append(get_term_match(explore_id, "taskId.keyword"))
            self.es_ioc_template["query"]["bool"]["must"].append(get_term_match(explore_id, "taskId.keyword"))
            self.es_model_template["query"]["bool"]["must"].append(get_term_match(explore_id, "taskId.keyword"))
            self.es_file_log_template["query"]["bool"]["must"].append(get_term_match(explore_id, "taskId.keyword"))

    def data_format(self, data):
        """ 将es输出结果格式化成前端结构 """

        res = self.get_res_template()
        # alertCount
        res["alertCount"] = data["hits"]["total"]["value"]
        # hitCount
        res["hitCount"] = data["aggregations"]["hitCount"]["value"]
        # threatScore
        if not data["aggregations"]["score"]["value"]:
            res["score"] = 0
        else:
            res["score"] = int(data["aggregations"]["score"]["value"])
        # killChains
        for kill_chain in data["aggregations"]["killChains"]["buckets"]:
            res["killChains"][kill_chain["key"]] = kill_chain["doc_count"]
        # threatLevel
        for threat_level in data["aggregations"]["level"]["buckets"]:
            # level = get_threat_level(level=threat_level["key"])
            res["level"][threat_level["key"]] = threat_level["doc_count"]
        return res
    
    def data_format_file_log(self, data):
        """ 将es输出结果格式化成前端结构 """

        res = self.get_res_template_file_log()
        # alertCount
        res["logCount"] = data["hits"]["total"]["value"]
        # hitCount
        res["hitCount"] = data["aggregations"]["hitCount"]["value"]
        # killChains
        kill_chains = []
        for kill_chain in data["aggregations"]["killchains"]["buckets"]:
            # res["killChains"][kill_chain["key"]] = kill_chain["doc_count"]
            kill_chains.append(kill_chain["key"])
        res["killChains"] = list(set(kill_chains))
        # threatLevel
        for threat_level in data["aggregations"]["threatLevel"]["buckets"]:
            res["threatLevel"][threat_level["key"].lower()] = threat_level["doc_count"]

        # severity

        return res

    @staticmethod
    def get_res_template():
        """
        :return: 返回res模板
        """
        res_template = {
            "alertCount": 0,  # 告警总量
            "hitCount": 0,  # 命中规则数量
            "score": 0,  # 最高得分
            "killChains": {i: 0 for i in list(GetKillchains.killchain_map.keys())},
            "level": {
                "High": 0,
                "Medium": 0,
                "Low": 0
            }
        }

        return res_template

    @staticmethod
    def get_res_template_file_log():
        """
        :return: 返回res模板
        """
        res_template = {
            "logCount": 0,  # 告警总量
            "hitCount": 0,  # 命中规则数量
            "killChains": [],
            "threatLevel": {
                "high": 0,
                "medium": 0,
                "low": 0,
                "safe": 0
            }
        }

        return res_template


class FileTaskResultUpdate(Resource):
    """
    文件检测查询
    """

    def __init__(self):
        self.es_client = database.get_es_client()
        super(FileTaskResultUpdate, self).__init__()

    def get(self, celery_id, size):
        """
        查询数据
        :return:
        """

        try:
            es_template_type = ES_Template().read_template('file_task_result_update')
            es_template_type = self.format_es_temp(es_template_type, celery_id, size)
            alter_data = self.es_client.search(index="file-eve", body=es_template_type)
        
        except Exception as e:
            API_LOG.error('Params error.Reason: %s' % str(e))

        return alter_data
    
    def format_es_temp(self, es_template, celery_id, size):
        es_template["query"]["bool"]["should"][0]["term"]["celeryId.keyword"] = celery_id
        es_template["size"] = size
        return es_template