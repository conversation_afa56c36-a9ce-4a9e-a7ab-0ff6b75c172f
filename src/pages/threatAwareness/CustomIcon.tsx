import React from 'react'
import Icon from '@ant-design/icons';

import type { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';

export type IconName = keyof typeof CUSTOM_ICON_MAP

const bug = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M512.07 73.143c99.035 0 182.857 62.83 182.857 146.212h62.39a36.571 36.571 0 0 1 29.477 14.995c6.144 8.411 12.069 17.042 17.627 25.892l115.347-72.411a36.571 36.571 0 1 1 38.912 62.025l-120.393 75.557c19.748 46.518 32.402 97.28 37.23 150.016h111.908a36.571 36.571 0 0 1 6.583 72.557l-6.583.585h-110.3a503.735 503.735 0 0 1-45.056 187.173L957.29 852.407a36.571 36.571 0 0 1-44.617 57.563l-5.266-4.096L795.059 801.28c-47.908 70.07-113.956 122.514-191.122 146.359a36.571 36.571 0 0 1-44.617-20.846l-44.398-106.276-44.91 107.885a36.571 36.571 0 0 1-43.885 21.065c-74.899-21.504-139.703-69.851-187.978-135.314l-99.108 92.233a36.571 36.571 0 0 1-49.884-53.467l109.934-102.473a501.029 501.029 0 0 1-52.15-201.875H36.64a36.571 36.571 0 0 1-6.583-72.557l6.583-.585h111.908c5.12-55.662 19.018-109.13 40.741-157.989L81.697 249.856a36.571 36.571 0 0 1 32.987-64.95l5.852 2.925 103.863 65.097 12.434-17.847a36.571 36.571 0 0 1 29.988-15.652l62.391-.074.44-10.24c6.582-78.262 87.478-135.972 182.417-135.972zM738.226 292.57H285.912c-42.643 64.659-66.414 145.555-66.414 231.132 0 157.988 81.481 294.473 196.389 344.503l65.39-156.819a36.571 36.571 0 0 1 67.51 0L613.3 865.938c112.348-51.858 191.342-186.66 191.342-342.308 0-78.556-20.041-153.015-56.174-214.601l-10.24-16.458zM512.07 146.286c-62.464 0-109.714 35.4-109.714 73.143h219.429l-.585-7.095c-5.413-34.963-50.542-66.048-109.13-66.048z"></path>
  </svg>
)

const scanning = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M670.9 403.2c21.9 31.7 33.7 69.3 33.7 108.7 0 51.3-20 99.5-56.2 135.7s-84.4 56.2-135.7 56.2-99.5-20-135.7-56.2c-36.2-36.2-56.2-84.4-56.2-135.7s20-99.5 56.2-135.7c32.5-32.5 74.6-51.9 119.9-55.6l-5.1-63.8c-131.6 10.6-235 120.8-235 255.1 0 141.3 114.6 255.9 255.9 255.9s255.9-114.6 255.9-255.9c0-53.8-16.6-103.6-44.9-144.8l-52.8 36.1z"></path><path d="M512.1 65.3v414.9c-17.7 0-32 14.3-32 32s14.3 32 32 32 32-14.3 32-32c0-7.7-2.7-14.7-7.2-20.2l322.4-261.2.4-.4c.1 0 .1-.1.2-.1C778 129 652.8 64.2 512.4 64.2h-.3v1.1z"></path><path d="m881.4 259-.4.4c-.1 0-.1.1-.2.1l-52.1 35.8c14.3 21 26.6 43.3 36.5 66.9 20 47.2 30.1 97.3 30.1 149s-10.1 101.9-30.1 149c-19.3 45.6-46.9 86.6-82.1 121.7-35.2 35.2-76.2 62.8-121.7 82.1-47.2 20-97.3 30.1-149 30.1S410.5 884 363.4 864c-45.6-19.3-86.6-46.9-121.7-82.1-35.2-35.2-62.8-76.2-82.1-121.7-20-47.2-30.1-97.3-30.1-149s10.1-101.9 30.1-149c19.3-45.6 46.9-86.6 82.1-121.7 35.2-35.2 76.2-62.8 121.7-82.1 37.8-16 77.5-25.7 118.4-28.9l-5-62.7-.1-1.1C246.5 83.8 65.4 276.3 65.4 511.2c0 246.8 200.1 447 447 447s447-200.1 447-447c-.1-93.6-28.8-180.4-78-252.2z"></path>
  </svg>
)

const attack = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M269.536 442.752A268.16 268.16 0 0 0 336 713.12c104.704 104.704 273.984 105.056 378.08.96s103.744-273.376-.96-378.08c-69.632-69.632-172.736-93.952-270.336-66.464l-15.68-50.688a321.056 321.056 0 0 1 323.744 79.392c125.312 125.312 125.728 328.256.704 453.248-124.992 125.024-327.936 124.608-453.248-.704C214.72 667.2 184.768 546.208 218.88 427.04l50.688 15.68zm128.768 56.96a130.976 130.976 0 0 0 34.848 122.016c51.36 51.328 135.2 50.816 187.296-1.28 52.096-52.096 52.608-135.936 1.28-187.296-30.592-30.592-75.296-43.104-122.016-34.88l-11.392-51.808a183.84 183.84 0 0 1 171.104 48.96c72.096 72.096 71.36 189.6-1.536 262.464-72.864 72.896-190.4 73.6-262.432 1.536C352 616 333.824 555.552 346.496 488.288l51.808 11.392zm-142.72-338.4 112.384 112.32-5.824 50.784 151.104 151.04c16.544-4.448 35.072-3.52 52.896 15.68 17.856 19.2 18.944 52.256-1.888 73.088a53.12 53.12 0 0 1-75.168.256 52.896 52.896 0 0 1-13.568-51.296l-151.072-151.04-50.752 5.792-112.352-112.352 84.576-9.696 9.696-84.576z"></path>
  </svg>
)

const loophole = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0zm0 960C268.8 960 64 761.6 64 512 64 268.8 262.4 64 512 64c243.2 0 448 198.4 448 448 0 243.2-204.8 448-448 448z" fill="#fff"></path><path d="M780.8 569.6c6.4-19.2 6.4-38.4 6.4-57.6 0-153.6-121.6-275.2-275.2-275.2S236.8 358.4 236.8 512v6.4c-32 25.6-51.2 57.6-51.2 102.4 0 70.4 57.6 128 128 128 12.8 0 25.6 0 44.8-6.4 44.8 25.6 96 44.8 153.6 44.8 76.8 0 147.2-32 192-83.2 19.2 19.2 38.4 38.4 70.4 38.4 51.2 0 89.6-38.4 89.6-89.6 0-44.8-38.4-83.2-83.2-83.2zM512 716.8c-38.4 0-70.4-12.8-96-25.6 12.8-19.2 25.6-44.8 25.6-76.8 0-70.4-57.6-128-128-128h-6.4c6.4-96 96-179.2 204.8-179.2 115.2 0 204.8 89.6 204.8 204.8S627.2 716.8 512 716.8z"></path>
  </svg>
)

const trojan = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M42.667 217.6c-12.8 0-21.334-8.533-21.334-21.333v-108.8c0-36.267 29.867-66.134 66.134-66.134h61.866c12.8 0 21.334 8.534 21.334 21.334S160 64 149.333 64H87.467C74.667 64 64 74.667 64 87.467v108.8c0 12.8-8.533 21.333-21.333 21.333zm106.666 785.067H87.467c-36.267 0-66.134-29.867-66.134-66.134V838.4c0-12.8 8.534-21.333 21.334-21.333S64 825.6 64 838.4v98.133C64 949.333 74.667 960 87.467 960h61.866c12.8 0 21.334 8.533 21.334 21.333s-10.667 21.334-21.334 21.334zm787.2 0h-61.866c-12.8 0-21.334-8.534-21.334-21.334S861.867 960 874.667 960h61.866c12.8 0 23.467-10.667 23.467-23.467V838.4c0-12.8 8.533-21.333 21.333-21.333s21.334 8.533 21.334 21.333v98.133c0 36.267-29.867 66.134-66.134 66.134zm44.8-785.067c-12.8 0-21.333-8.533-21.333-21.333v-108.8C960 74.667 949.333 64 936.533 64h-61.866c-12.8 0-21.334-8.533-21.334-21.333s8.534-21.334 21.334-21.334h61.866c36.267 0 66.134 29.867 66.134 66.134v108.8c0 12.8-8.534 21.333-21.334 21.333zM652.8 893.867h-288c-29.867 0-59.733-14.934-76.8-40.534S266.667 797.867 277.333 768c14.934-40.533 42.667-81.067 78.934-119.467 36.266-51.2 42.666-81.066 42.666-96-12.8 4.267-23.466 8.534-34.133 14.934l-78.933 51.2c-25.6 17.066-61.867 12.8-83.2-10.667l-40.534-42.667c-17.066-19.2-21.333-46.933-6.4-68.266l179.2-279.467c23.467-38.4 66.134-59.733 110.934-55.467 53.333 4.267 128 17.067 185.6 57.6C729.6 288 761.6 330.667 733.867 678.4c-2.134 38.4 0 72.533 8.533 104.533 6.4 27.734 0 55.467-17.067 76.8-19.2 21.334-44.8 34.134-72.533 34.134zM441.6 544c2.133 23.467-4.267 66.133-51.2 132.267l-2.133 2.133c-34.134 34.133-57.6 68.267-70.4 104.533-6.4 14.934-4.267 32 6.4 44.8 10.666 14.934 25.6 21.334 42.666 21.334h288c14.934 0 29.867-6.4 38.4-19.2 8.534-10.667 12.8-25.6 8.534-40.534-8.534-36.266-12.8-74.666-8.534-117.333 27.734-341.333-2.133-362.667-83.2-420.267-49.066-34.133-115.2-44.8-164.266-49.066-29.867-2.134-57.6 12.8-72.534 36.266L192 522.667c-4.267 4.266-4.267 10.666 0 14.933l40.533 42.667c8.534 8.533 19.2 8.533 29.867 4.266l78.933-51.2c19.2-12.8 42.667-21.333 68.267-25.6 42.667-8.533 70.4-23.466 85.333-46.933 17.067-27.733 8.534-55.467 8.534-55.467-4.267-10.666 2.133-23.466 14.933-25.6 10.667-4.266 23.467 2.134 25.6 14.934 0 2.133 12.8 46.933-12.8 91.733-17.067 25.6-46.933 46.933-89.6 57.6z" fill="#fff"></path><path d="M753.067 684.8h-40.534v-42.667h40.534c19.2 0 34.133-14.933 34.133-34.133v-93.867c0-36.266 2.133-66.133 4.267-93.866 6.4-59.734 0-202.667-189.867-258.134-38.4-10.666-83.2-8.533-121.6 6.4-38.4 14.934-68.267 40.534-70.4 40.534l-27.733-32c2.133-2.134 38.4-29.867 81.066-49.067 49.067-19.2 102.4-21.333 149.334-8.533 57.6 17.066 243.2 89.6 219.733 302.933-2.133 25.6-4.267 55.467-4.267 89.6v93.867c2.134 44.8-32 78.933-74.666 78.933zM366.933 384c-4.266 0-8.533-2.133-12.8-4.267-8.533-6.4-10.666-21.333-4.266-29.866l14.933-19.2c6.4-8.534 21.333-10.667 29.867-4.267 8.533 6.4 10.666 21.333 4.266 29.867L384 375.467c-4.267 6.4-10.667 8.533-17.067 8.533zM215.467 509.867h-6.4l-23.467-6.4c-10.667-2.134-19.2-14.934-14.933-25.6 2.133-10.667 14.933-19.2 25.6-14.934l23.466 6.4c10.667 2.134 19.2 14.934 14.934 25.6-2.134 8.534-10.667 14.934-19.2 14.934z" fill="#fff"></path>
  </svg>
)

const remoteControl = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M109.424 348.976H237.12v-61.072H48.368v199.008h61.056zm187.568-61.072h127.36v61.056h-127.36zM48.368 540.912h61.056v248.752H48.368zM48.256 842.4h300.688v61.024H48.256zm919.04-802.384H481.728c-14.4 0-26.032 10.336-26.032 23.104v308.144c0 12.752 11.632 23.104 26.032 23.104h164.704v38.528l-34.656 30.816v7.696h225.44v-7.696l-34.672-30.816v-38.528h164.752c14.352 0 26-10.352 26-23.104V63.12c0-12.768-11.632-23.104-26-23.104zm-24.672 292.72H502.672V86.64h439.952v246.096zm.4 470.448h-.88l.88 3.536v-3.536zm25.088-17.728c-1.728-1.088-2.496-1.664-3.312-2.112-50.368-26.8-100.752-53.6-151.184-80.288a18.272 18.272 0 0 0-8.32-1.712c-77.504-.064-155.008-.08-232.496.048a21.44 21.44 0 0 0-9.36 2.4c-19.152 9.92-38.192 20.064-57.232 30.192l-94.96 50.48.128.992h556.736zM706.08 745.184h88.24v-18.608l44.56 25.92-44.24 25.6v-18.912h-88.56v-14zm-111.056-37.472.832 17.952h9.936l68.88-.032c9.888 0 9.904 0 9.664 9.936-.032 1.264-.16 2.528-.32 4.512H595.84l-.944 18.816-43.536-25.776 43.664-25.408zM961.744 810h-548.72c-18.88 0-36.672 13.056-36.672 31.936v71.472c0 18.88 17.808 33.84 36.672 33.84h31.952v11.44h45.744v-11.44h388.896v11.44h57.168v-11.44h24.96c18.88 0 32.256-14.976 32.256-33.84v-71.472c0-18.88-13.376-31.936-32.256-31.936zm-492.08 110.672H414.88v-31.2h54.784v31.2zm0-51.36H414.88v-31.2h54.784v31.2zm69.904 51.36h-32.4v-32.4h32.4v32.4zm0-50.144h-32.4v-32.416h32.4v32.416zm60.048 50.144h-32.384v-32.4h32.384v32.4zm0-50.144h-32.384v-32.416h32.384v32.416zm60.032 50.144h-32.384v-32.4h32.384v32.4zm0-50.144h-32.384v-32.416h32.384v32.416zm60.064 50.144h-32.4v-32.4h32.4v32.4zm0-50.144h-32.4v-32.416h32.4v32.416zm60.048 50.144h-32.416v-32.4h32.416v32.4zm0-50.144h-32.416v-32.416h32.416v32.416zm60.048 50.144h-32.4v-32.4h32.4v32.4zm0-50.144h-32.4v-32.416h32.4v32.416zm60.032 50.144h-32.4v-32.4h32.4v32.4zm0-50.144h-32.4v-32.416h32.4v32.416zm60.064 50.144h-32.4v-32.4h32.384l.016 32.4zm0-50.144h-32.4v-32.416h32.384l.016 32.416z"></path>
  </svg>
)

const infiltration = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M165.977 475.362h189.402l-92.844-90.701c-7.142-7.142-7.142-18.998 0-26.282l25.996-25.997c7.142-7.142 18.855-7.142 25.997 0l160.977 158.835c6 6 7 15.284 2.857 22.283a18.194 18.194 0 0 1-4 19.854L314.67 691.904c-7.141 7.141-18.711 7.141-25.853 0l-25.854-25.854c-7.142-7.142-7.142-18.712 0-25.854l92.845-91.701H165.977a18.227 18.227 0 0 1-18.283-18.283v-36.567a18.227 18.227 0 0 1 18.283-18.283zM554.351 491.217l160.835-158.835c7.142-7.142 18.854-7.142 25.996 0l25.997 25.997c7.142 7.141 7.142 18.997 0 26.282l-92.987 90.701H859.88a18.227 18.227 0 0 1 18.283 18.283v36.567a18.227 18.227 0 0 1-18.283 18.283H674.049l92.844 91.701c7.142 7.142 7.142 18.712 0 25.854l-25.854 25.853c-7.141 7.142-18.711 7.142-25.853 0L555.494 533.354a18.194 18.194 0 0 1-4-19.854c-4.142-7.142-3.285-16.284 2.857-22.283z" fill="#fff"></path><path d="M1024.143 18.426A18.227 18.227 0 0 0 1005.86.143L18.283 0A18.227 18.227 0 0 0 0 18.283l.429 987.291a18.227 18.227 0 0 0 18.283 18.283l987.576.143a18.227 18.227 0 0 0 18.283-18.283l-.428-987.291zm-90.987 932.441-841.312-.142a18.227 18.227 0 0 1-18.283-18.284l-.428-841.025a18.227 18.227 0 0 1 18.283-18.283l841.311.142A18.227 18.227 0 0 1 951.01 91.56l.429 841.025c0 9.999-8.142 18.283-18.283 18.283z"></path>
  </svg>
)

const harvest = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M707.114 402.879H187.206v67.212h519.908zM187.206 621.12h486.697v-67.607H187.206zm0 151.03h334.876v-67.21H187.206zm0-520.302v66.817h285.06v-66.817z" fill="#fff"></path><path d="M85.992 66.817H556.48v251.849h251.848v324.991h66.817V273.594L619.342 0H78.085A58.514 58.514 0 0 0 19.57 58.514v906.181a58.514 58.514 0 0 0 58.514 58.514h444.392v-66.421H87.574zm537.304 185.032V115.842L750.208 251.85H623.296z" fill="#fff"></path><path d="M989.8 657.495a37.164 37.164 0 0 0-51.792 6.721L731.23 933.066 601.55 819.2a36.77 36.77 0 0 0-48.235 55.747l158.147 139.564a37.164 37.164 0 0 0 53.77-5.14l229.312-300.083a37.164 37.164 0 0 0-6.721-51.793z"></path>
  </svg>
)

const rightArrow = () => (
  <svg width="1em" height="1em" fill="currentColor" viewBox="0 0 1024 1024" aria-hidden="true" >
    <path d="M1024.205 512c0 6.656-2.458 13.26-7.527 18.278L690.842 856.115a25.6 25.6 0 1 1-36.199-36.198l282.522-282.522H25.805a25.6 25.6 0 1 1 0-51.2h910.95L654.643 204.083a25.6 25.6 0 1 1 36.199-36.198l325.836 325.837c4.608 4.608 7.527 11.008 7.527 18.073V512z" fill="#e6e6e6"></path>
  </svg>
)


const CUSTOM_ICON_MAP = {
  bug,
  scanning,
  attack,
  loophole,
  trojan,
  remoteControl,
  infiltration,
  harvest,
  rightArrow
}

const CustomIcon = (props: Partial<CustomIconComponentProps> & { name: IconName }) => (
  <Icon component={CUSTOM_ICON_MAP[props.name]} {...props} />
);

export default CustomIcon
