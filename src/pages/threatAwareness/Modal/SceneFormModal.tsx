import React, { useEffect, useMemo, } from 'react';
import { Button, Cascader, Checkbox, Form, Input, Modal, Radio, Select } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import { useLocalStorage, useThreatNameOptions } from '../hooks';
import ThreatNamesSelector from '../ThreatNameSelector';

import styleModule from '../index.less'

export type Props = {
  show: boolean;
  onCreate: (values: any) => void;
  onEdit: (values: any) => void;
  onClose: () => void;
  value?: any;
  confirmLoading?: boolean;
}

const { Item } = Form;

function SceneFormModal({ show, value, confirmLoading, onClose, onCreate, onEdit }: Props): JSX.Element {
  const [form] = Form.useForm();

  const { get } = useLocalStorage()
  const serverirty = get('threatAwareness.serverirty') || [];
  const attackStatus = get('threatAwareness.attack_status') || [];

  const attackStatusOptions = useMemo(() => attackStatus.map(({ nameCn, value }: any) => ({
    label: nameCn,
    value,
  })), [])

  const severityOptions = useMemo(() => serverirty.map(({ nameCn, value }: any) => ({
    label: nameCn,
    value,
  })), [])

  const handleSubmit = (v: any) => {
    form.validateFields().then((values) => {
      const _values = {
        ...values,
        order: 0,
        ip: values?.ip || '',
        ...(value && { id: value._id })
      }
      if (value) {
        onEdit(_values)
        return;
      }
      onCreate(_values)
    })
  }

  const threatNameOptions = useMemo(() => useThreatNameOptions(), [])

  useEffect(() => {
    form.setFieldsValue(value)
    if (!show) {
      form.resetFields()
    }
  }, [value, show]);

  return (
    <Modal
      visible={show}
      width={600}
      title="新增场景"
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button loading={confirmLoading} type="primary" key="submit" onClick={handleSubmit}>
          提交
        </Button>,
      ]}
      onCancel={onClose}
      destroyOnClose
    >
      <Form form={form} className={styleModule.scene_form} >
        <div>
          <Item
            label="场景名称"
            name="name"
            rules={[{ required: true }]}
          >
            <Input disabled={value} placeholder="请输入名称" />
          </Item>
          <Item
            label="备注"
            name="remark"
            initialValue={''}
          >
            <TextArea placeholder="输入备注" allowClear />
          </Item>
          <Item
            label="场景类型"
            name="type"
            initialValue={1}
            rules={[
              ({ getFieldsValue }) => ({
                validator() {
                  const { ip, attack_status, threat_name, severity } = getFieldsValue()
                  if (ip || attack_status?.length || severity?.length || threat_name?.length) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('自定义项不能同时为空'));
                },
              }),
            ]}
          >
            <Radio checked value={1}>自定义</Radio>
          </Item>
          <Item
            label="IP地址范围"
            name="ip"
          >
            <TextArea placeholder="输入IP地址范围" allowClear />
          </Item>
          <Item
            label="攻击结果"
            name="attack_status"
          >
            <Checkbox.Group options={attackStatusOptions} />
          </Item>
          <Item
            label="威胁名称"
            name="threat_name"
          >
            <ThreatNamesSelector
              style={{ width: '400px' }}
              options={threatNameOptions}
            />
          </Item>
          <Item
            label="威胁等级"
            name="severity"
          >
            <Select
              className='self-center'
              mode="multiple"
              allowClear
              style={{ width: '400px' }}
              maxTagCount="responsive"
              options={severityOptions}
            />
          </Item>
        </div>
      </Form>
    </Modal >
  );
}

export default SceneFormModal;
