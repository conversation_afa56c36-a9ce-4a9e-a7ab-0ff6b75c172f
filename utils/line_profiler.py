# -*- coding: utf-8 -*-
# @Time    : 2019-06-13 14:47
# <AUTHOR> <PERSON>
# @File    : LineProfiler.py
# @Software: PyCharm

from functools import wraps
from line_profiler import LineProfiler


# 查询接口中每行代码执行的时间
def func_line_time(f):
    @wraps(f)
    def decorator(*args, **kwargs):
        func_return = f(*args, **kwargs)
        lp = LineProfiler()
        lp_wrap = lp(f)
        lp_wrap(*args, **kwargs)
        lp.print_stats()
        return func_return
    return decorator
