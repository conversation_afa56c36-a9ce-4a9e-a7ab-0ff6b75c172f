# -*- coding: utf-8 -*-
# @Time    : 2019-05-15 11:11
# <AUTHOR> Shark
# @File    : pcap_download.py
# @Software: PyCharm
import json
import re
import requests
from requests.auth import HTTPDigestAuth
from flask import request
from flask_restful import Resource
from config.config import BackStreamConfig
from api_1_0.back_stream.utils import check_args


class PcapDownload(Resource):
    """api接口/api/v1/pcap/download处理"""
    def __init__(self):
        self.frag_size = 2000
        self.url = ""
        self.args = request.values.to_dict()
        self.response = {
            "message": "",
            "data": {}
        }

    def args_valid_check(self):
        """ 根据正则匹配的方式检测每个参数是否有效，
        无效则返回 False 和错误参数
        有效则返回 True"""

        return check_args(self.args)

    def comb_url(self):
        """ 判断传入的参数是否为空，来确认是否给默认值，并组装最终的 URL 请求"""

        req_url = 'http://{0}:{1}/sessions.pcap?'.format(
            BackStreamConfig.BackStream_HOST,
            BackStreamConfig.BackStream_PORT) + 'expression='

        # 追加在 expression 后面的参数模块
        args_expr = {
            'ip': 'ip',
            'srcIp': 'ip.src',
            'dstIp': 'ip.dst',
            'srcPort': 'port.src',
            'dstPort': 'port.dst',
            'appProto': 'protocol',
            'domain': 'host.dns.all',
            'flowId': 'suricata.flowId',
            'sessionId': 'id'
        }

        for arg in self.args.keys():
            if self.args[arg] and arg in args_expr:
                req_url += args_expr[arg] + '==' + self.args[arg] + '&&'

        req_url = req_url.rstrip('&&')

        # 直接以参数形式添加的模块
        if self.args['startTime']:
            req_url += "&startTime=" + self.args['startTime']
        if self.args['stopTime']:
            req_url += "&stopTime=" + self.args['stopTime']
        # 如果没有传入起止时间，则默认在 12 小时以内搜索结果
        if not self.args['startTime'] and not self.args['stopTime']:
            req_url += "&date=12"
        # 单个 pcap 的 session 数量，默认为 frag_size 个，moloch 支持的单次请求的最大session数量
        if self.args['num']:
            req_url += "&length=" + self.args['num']
        else:
            req_url += "&length=" + str(self.frag_size)
        # 单个 pcap 中记录的 session 开始数目，默认从第 0 个 session 开始
        if self.args['start']:
            req_url += "&start=" + self.args['start']
        else:
            req_url += "&start=0"

        return req_url

    def get_record_num(self):
        """获取此次查询的"""
        url = self.url.replace("pcap", "json")
        resp = requests.get(url, auth=HTTPDigestAuth(
            BackStreamConfig.BackUsername,
            BackStreamConfig.BackPassword
        ))
        record_num = json.loads(resp.content)["recordsFiltered"]

        return record_num

    def get(self):
        """api接口/api/v1/pcap/download的get请求处理"""
        is_valid, msg = self.args_valid_check()

        if not is_valid:
            self.response['message'] = msg
            return self.response

        self.url = self.comb_url()

        # 根据返回的结果数和设置的分包大小判断要分几个包进行下载
        record_num = self.get_record_num()
        record_count = int((record_num - 1) / self.frag_size)
        url_list = []
        pattern = re.compile(r"start=\d*")
        for count in range(record_count + 1):
            url = re.sub(pattern, "start=" + str(count * self.frag_size), self.url)
            url_list.append(url)

        self.response['message'] = ""
        self.response['data'] = {
            "downUrl": url_list
        }
        return self.response
