# -*- coding: utf-8 -*-
# @Time    : 2019-04-29 15:54
# <AUTHOR> hachi
# @File    : authentication.py
# @Software: PyCharm
import os
from api_1_0.router import app
from config.config import FlaskConfig

if __name__ == '__main__':
    app.debug = FlaskConfig.DEBUG
    host = os.environ.get('IP', FlaskConfig.IP)
    port = int(os.environ.get('PORT', FlaskConfig.PORT))
    app.run(host=host, port=port, threaded=True)
