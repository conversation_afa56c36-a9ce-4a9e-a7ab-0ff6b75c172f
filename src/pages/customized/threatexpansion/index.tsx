import React, { useEffect, useState } from 'react';
import {
  Button,
  Collapse,
  DatePicker,
  Form,
  Input,
  Spin,
  Table,
  Modal,
  Row,
  Col,
  message,
  Popconfirm,
  Tooltip,
  Select,
  Radio,
} from 'antd';
const { Panel } = Collapse;
const { TextArea } = Input;
const { Option } = Select;
import moment from 'moment';
import style from './style.less';
import { SearchOutlined } from '@ant-design/icons';
import {
  threatList,
  addthreatListTask,
  deleteThreat,
  getAnalysis,
  getAnalysisTask,
  updateEngines,
  getEngines,
  updateAnalysis,
  fingerList,
} from '@/services/collect';
import { ellipsis } from '@/utils/utils';
import { sig_type, sig_status, expansionModel } from '@/utils/enumList';
const Index = () => {
  const [form] = Form.useForm();
  const [enginesForm] = Form.useForm();
  const [taskForm] = Form.useForm();
  const [total, settotal] = useState(0);
  const [loading, setloading] = useState(false);
  const [taskDetail, settaskDetail] = useState<any>({});
  const [params, setparams] = useState<any>({});
  const [tableData, setTableData] = useState<any>([]);
  const [enginesList, setenginesList] = useState({});
  const [analysisTable, setanalysisTable] = useState([]);
  const [isAdd, setisAdd] = useState(false);
  const [engineModal, setEngineModal] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showTaskResult, setshowTaskResult] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<any>([]);
  const [fingerVisible, setfingerVisible] = useState(false);
  const [fingerTotal, setfingerTotal] = useState(0);
  const [fingerParams, setfingerParams] = useState({
    page: 1,
    pageSize: 10,
  });
  const [fingerListData, setfingerListData] = useState([]);
  useEffect(() => {
    searchList();
  }, [params]);
  // useEffect(() => {
  //   getFingerList();
  // }, [params]);

  const getFingerList = () => {
    let value = { ...fingerParams, org: params.org, start_time: params.start_time, stop_time: params.stop_time };
    fingerList(value).then((res) => {
      if (res.flag) {
        setloading(false);
        setfingerTotal(res.data.total);
        setfingerListData(res.data.cols);
        setfingerVisible(true);
      } else {
        message.error(res.message);
      }
    });
  };
  const changeTime = (value: any) => {
    setparams({
      ...params,
      start_time: moment(value[0]).startOf('seconds').unix() * 1000,
      stop_time: moment(value[1]).startOf('seconds').unix() * 1000,
    });
  };
  const expandedRowRender = (record: any) => {
    let data = { ...record };
    delete data.index;
    const jsonString = JSON.stringify(data, null, 2);
    return (
      <div style={{ width: 1130 }}>
        <pre>
          <code>{jsonString}</code>
        </pre>
      </div>
    );
  };

  const onExpand = (expanded: any, record: any) => {
    console.log(record.key);
    if (expanded) {
      setExpandedRowKeys([record.key]);
    } else {
      setExpandedRowKeys([]);
    }
  };

  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const fingerPagination = {
    total: fingerTotal,
    showSizeChanger: true,
    current: fingerParams.page || 1,
    pageSize: fingerParams.pageSize,
    showTotal: (total: number) => `共${fingerTotal}条`,
    onChange(page: number, pageSize: any) {
     
      setfingerParams({...fingerParams,page,pageSize})
      setfingerParams((data:any)=>{
        let value = { ...data, org: params.org, start_time: params.start_time, stop_time: params.stop_time };
        fingerList(value).then((res) => {
          if (res.flag) {
            setloading(false);
            setfingerTotal(res.data.total);
            setfingerListData(res.data.cols);
            setfingerVisible(true);
          } else {
            message.error(res.message);
          }
        });
        return {...fingerParams,page,pageSize}
   
      })
 
    },
  };
  const editTask = (id: any) => {
    getAnalysisTask(id).then((res) => {
      settaskDetail(res.data);
      setisAdd(false);
      setIsModalOpen(true);
    });
  };
  const handleOk = async () => {
    taskForm.validateFields().then((value: any) => {
      if (isAdd) {
        addthreatListTask(value).then((res) => {
          if (res.flag) {
            setIsModalOpen(false);
            message.success('操作成功');
            searchList();
          } else {
            message.error(res.message);
          }
        });
      } else {
        updateAnalysis(taskDetail._id, value).then((res) => {
          if (res.flag) {
            setIsModalOpen(false);
            message.success('操作成功');
            searchList();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };

  const handleCancel = () => {
    taskForm.resetFields();
    setIsModalOpen(false);
  };
  const changeModelInput = (value: any) => {
    let data = '';
    switch (value.target.value) {
      case '自定义':
        break;
      case 'JARM模型':
        data = 'JAMA:""';
        break;
      case '证书模型':
        data = 'issuer:"",cipher:"",subject:""';
        break;
      case '网站特征模型':
        data = 'title:"",http.request.ua:"",http_header:"",http_body:""';
        break;
      case 'IP模型':
        data = 'IP:""';
        break;
      case '域名模型':
        data = 'Domain:""';
        break;
    }

    taskForm.setFieldValue('dock', data);
  };
  const engineOK = () => {
    // console.log(333, enginesForm);
    enginesForm.validateFields().then((value) => {
      // console.log(value.);
      value.data.forEach((ele: any, index: any) => {
        enginesList.data.forEach((item: any, sub: any) => {
          if (index === sub) {
            ele._id = item._id;
          }
        });
      });
      for (const item of value.data) {
        updateEngines(item._id, item);
      }
    });
    setEngineModal(false);
  };

  const engineCancel = () => {
    setEngineModal(false);
  };
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      setparams(value);
    });
  };
  const deleteTask = (id: any) => {
    deleteThreat(id).then((res) => {
      if (res.flag) {
        onSubmit();
        message.success('操作成功');
      } else {
        message.success(res.message);
      }
    });
  };
  const showResult = (id: any) => {
    setloading(true);
    // getAnalysis(id).then((res) => {
    //   setloading(false);
    //   if (res.flag) {
    //     setshowTaskResult(true);
    //     res.data.forEach((item: any, index: any) => {
    //       item.key = index;
    //     });
    //     setanalysisTable(res.data);
    //   } else {
    //     message.error(res.message);
    //   }
    // });
    getAnalysisTask(id).then((res) => {
      if (res.flag) {
        let data = JSON.parse(res.data.result);
        data.forEach((item: any, index: number) => {
          item.key = index;
        });
        setanalysisTable(data);
        setshowTaskResult(true);
        setloading(false);
      } else {
        message.error(res.message);
      }
    });
  };
  const executeTask = (id: any) => {
    setloading(true);
    getAnalysis(id).then((res) => {
      if (res.flag) {
        message.success('操作成功');
      } else {
        message.error(res.message);
      }
    });
    setloading(false);
  };
  const handleAdd = () => {
    setisAdd(true);
    setIsModalOpen(true);
  };
  const searchList = () => {
    let value = { ...params };

    // if (value.created_at && value.created_at.length) {
    //   value.start_time = moment(value.created_at[0]).startOf('seconds').unix() * 1000;
    //   value.stop_time = moment(value.created_at[1]).startOf('seconds').unix() * 1000;
    // }

    setloading(true);
    delete value.created_at;

    threatList(value).then((res) => {
      if (res.flag) {
        setloading(false);
        settotal(res.data.total);
        setTableData(res.data.cols);
      } else {
        message.error(res.message);
      }
    });
  };

  const getEnginesList = () => {
    getEngines().then((res) => {
      if (res.flag) {
        console.log(res.data)
        setenginesList({ data: res.data });
        setEngineModal(true);
      }
    });
  };
  const fingerColumns = [
    {
      title: '组织名称',
      dataIndex: 'name',
    },
    {
      title: '指纹信息',
      dataIndex: 'fingerprint',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      render: (t: any) => {
        return <span>{moment(t).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
  ];
  const columns = [
    {
      title: '测绘类别',
      dataIndex: 'type',
      align: 'center',
    },
    {
      title: '测绘别名',
      dataIndex: 'alias',
      align: 'center',
    },
    {
      title: '特征类别',
      dataIndex: 'sig_type',
      align: 'center',
    },

    {
      title: '组织/工具',
      dataIndex: 'org',
      align: 'center',
      render: (t: string, recoed: any) => {
        return `${t}/${recoed.tool}`;
      },
    },

    {
      title: '标签',
      dataIndex: 'tag',
      align: 'center',
    },
    {
      title: '测绘引擎',
      dataIndex: 'engine',
      align: 'center',
    },

    {
      title: '测绘语句',
      dataIndex: 'dock',
      align: 'center',
      render: (t: any) => {
        return (
          <Tooltip placement="topLeft" arrowPointAtCenter title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: '特征状态',
      dataIndex: 'sig_status',
    },
    {
      title: '扩线模型',
      dataIndex: 'model',
    },
    {
      title: '说明',
      dataIndex: 'application',
    },
    {
      title: '作者',
      dataIndex: 'author',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (t: any) => {
        return <span>{moment(t).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 200,
      render: (t: any, record: any) => {
        return (
          <>
            <Button
              onClick={() => {
                editTask(record._id);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              编辑
            </Button>
            <Popconfirm title="确定删除吗？" okText="确定" cancelText="取消" onConfirm={() => deleteTask(record._id)}>
              <Tooltip>
                <Button type="link" danger>
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>

            <Button
              onClick={() => {
                executeTask(record._id);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              执行
            </Button>
            <Button
              style={{ color: '#39e600' }}
              onClick={() => {
                showResult(record._id);
              }}
              type="link"
            >
              结果
            </Button>
          </>
        );
      },
    },
  ];
  const analysisColumns = [
    {
      title: '测绘时间',
      dataIndex: 'timestamp',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
    },
    {
      title: '端口',
      dataIndex: 'port',
    },
    {
      title: '国家',
      dataIndex: 'country',
    },
    {
      title: '省份',
      dataIndex: 'province',
    },
    {
      title: '城市',
      dataIndex: 'city',
    },
  ];
  const enginesColumns = [
    {
      title: '测绘引擎',
      dataIndex: 'name',
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'name']}>
            <Select allowClear>
              {engineList.map((item: any) => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        );
      },
    },
    {
      title: 'API token',
      dataIndex: 'api_key',
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'api_key']}>
            <Input />
          </Form.Item>
        );
      },
    },
    {
      title: 'username',
      dataIndex: 'username',
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'username']}>
            <Input />
          </Form.Item>
        );
      },
    },
    {
      title: 'password',
      dataIndex: 'password',
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'password']}>
            <Input />
          </Form.Item>
        );
      },
    },
  ];

  const formItemLayout = {
    labelCol: {
      span: 4, // * ≥576px
    },
    wrapperCol: {},
  };
  const engineList = [
    {
      label: 'zoomEye',
      value: 'zoomEye',
    },
    {
      label: 'fofa',
      value: 'fofa',
    },
    {
      label: 'shodan',
      value: 'shodan',
    },
    {
      label: 'censys',
      value: 'censys',
    },
    {
      label: 'other',
      value: 'other',
    },
  ];
  return (
    <div className={style.threatExpansion}>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form {...formItemLayout} layout="inline" initialValues={params} form={form} className="!flex gap-3 !mt-5">
            <Row>
              <Col span={6}>
                <Form.Item
                  labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                  label="创建时间"
                  name="created_at"
                >
                  <DatePicker.RangePicker
                    onChange={changeTime}
                    allowClear
                    style={{ width: 310 }}
                    className="w-100%"
                    ranges={{
                      近一年: [moment().add(-1, 'year'), moment()],
                      近半年: [moment().add(-6, 'month'), moment()],
                      近一月: [moment().add(-1, 'month'), moment()],
                      近一周: [moment().add(-7, 'd'), moment()],
                      近一天: [moment().add(-1, 'd'), moment()],
                      今天: [moment().startOf('day'), moment().endOf('day')],
                      本周: [moment().startOf('week'), moment().endOf('week')],
                      本月: [moment().startOf('month'), moment().endOf('month')],
                      本年度: [moment().startOf('year'), moment().endOf('year')],
                    }}
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item
                  label="测绘类别"
                  labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                  name="type"
                >
                  <Input allowClear />
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item
                  labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                  label="特征类别"
                  name="sig_type"
                >
                  <Select allowClear>
                    {sig_type.map((item) => {
                      return (
                        <Option key={item.value} value={item.value}>
                          {item.label}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item
                  labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                  label="测绘别名"
                  name="alias"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={3}>
                <Form.Item>
                  <Button
                    style={{ margin: '0 10px' }}
                    className="searchBtn"
                    type="primary"
                    onClick={onSubmit}
                    icon={<SearchOutlined />}
                  >
                    搜索
                  </Button>
                  <Button style={{ color: '#1890ff' }} type="dashed" onClick={handleAdd}>
                    新建
                  </Button>
                </Form.Item>
              </Col>
            </Row>
            <Row className="!2xl:flex">
              <Col span={6}>
                <div style={{ display: 'flex' }}>
                  <Form.Item
                    labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                    style={{ width: '50%' }}
                    label="组织"
                    name="org"
                  >
                    <Input style={{ width: 120 }} />
                  </Form.Item>
                  <Form.Item
                    labelCol={{ style: { textAlign: 'left', minWidth: '40px', maxWidth: '40px' } }}
                    style={{ width: '50%' }}
                    label="工具"
                    name="tool"
                  >
                    <Input style={{ width: 120 }} />
                  </Form.Item>
                </div>
              </Col>
              <Col span={5}>
                <Form.Item
                  labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                  label="标签"
                  name="tag"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item
                  labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                  label="测绘引擎"
                  name="engine"
                >
                  <Select allowClear>
                    {engineList.map((item: any) => {
                      return (
                        <Option key={item.value} value={item.value}>
                          {item.label}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={5}>
                <Form.Item
                  labelCol={{ style: { textAlign: 'left', minWidth: '70px', maxWidth: '70px' } }}
                  label="作者"
                  name="author"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={3}>
                <Form.Item>
                  <Button
                    className="searchBtn"
                    style={{ color: '#1890ff', margin: '0 10px' }}
                    type="dashed"
                    onClick={getEnginesList}
                  >
                    引擎设置
                  </Button>
                  <Button onClick={getFingerList} style={{ color: '#1890ff' }} type="dashed">
                    指纹库
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <Spin spinning={loading}>
        <Table dataSource={tableData} columns={columns} pagination={pagination} />
      </Spin>
      <Modal
        width={1200}
        title={isAdd ? '新增扩线分析任务' : '编辑扩线分析任务'}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        className={style.changeForm}
      >
        <Form initialValues={taskDetail} form={taskForm}>
          <Row>
            <Col span={8}>
              <Form.Item name="alias" label="测绘别名" rules={[{ required: true, message: '请填写!' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="engine" label="测绘引擎" rules={[{ required: true, message: '请填写!' }]}>
                <Select allowClear>
                  {engineList.map((item: any) => {
                    return (
                      <Option key={item.value} value={item.value}>
                        {item.label}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="type" label="测绘类别">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <Form.Item name="org" label="组织">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="tool" label="工具">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="tag" label="标签">
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <Form.Item name="author" label="作者">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="sig_type" label="特征类别">
                <Select allowClear>
                  {sig_type.map((item) => {
                    return (
                      <Option key={item.value} value={item.value}>
                        {item.label}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="sig_status" label="特征状态">
                <Select>
                  {sig_status.map((item) => {
                    return (
                      <Option key={item.value} value={item.value}>
                        {item.label}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="model" label="扩线模型" rules={[{ required: true, message: '请填写!' }]}>
            <Radio.Group onChange={changeModelInput}>
              {expansionModel.map((item) => {
                return <Radio value={item.value}>{item.label}</Radio>;
              })}
            </Radio.Group>
          </Form.Item>

          <Form.Item name="dock" label="测绘语句" rules={[{ required: true, message: '请填写!' }]}>
            <TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
          </Form.Item>
          <Form.Item name="description" label="说明">
            <TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        className={style.hadnleForm}
        okText="保存"
        title="引擎参数设置"
        visible={engineModal}
        onOk={engineOK}
        width={800}
        onCancel={engineCancel}
      >
        <Form initialValues={enginesList} form={enginesForm} component={false}>
          <Form.Item>
            <Table pagination={false} dataSource={enginesList.data} columns={enginesColumns} />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="分析结果"
        width={1200}
        visible={showTaskResult}
        onCancel={() => {
          setshowTaskResult(false);
        }}
      >
        <Table
          expandable={{
            expandedRowRender,
            onExpand,
            expandedRowKeys,
            rowExpandable: (record) => true,
          }}
          columns={analysisColumns}
          dataSource={analysisTable}
        />
      </Modal>
      <Modal
        title="APT组织指纹库"
        width={1200}
        visible={fingerVisible}
        onCancel={() => {
          setfingerVisible(false);
        }}
      >
        <Table columns={fingerColumns} pagination={fingerPagination} dataSource={fingerListData} />
      </Modal>
    </div>
  );
};
export default Index;
