import { isFilter } from '@/utils/enumList';
import { Button, Form, Select, Tag } from 'antd';
import cn from 'classnames';
import React from 'react';
import { isEmpty as _isEmpty } from 'lodash';

type State = {
  item: string;
  logic: string;
  value: string[];
};
type Field = {
  key: string;
  value: string;
  valueFormat?: (item: string) => any;
  valueValidateRules?: {}[];
};

const Option = Select.Option;
const LOGIC_SYMBOL_MAP = new Map([
  ['equal', '=='],
  ['notEqual', '!='],
  ['contain', '包含'],
  ['notContain', '不包含'],
]);

export default function AdvanceFilter({
  className,
  fields,
  value,
  onChange,
}: {
  className?: string;
  fields: Field[];
  value: State[];
  onChange: (value: State[]) => any;
}): React.ReactElement {
  const [form] = Form.useForm<State>();

  const onFinish = (newVal: any) => {
    const fieldConfig = fields.find((x) => x.value === newVal.item);
    if (fieldConfig && fieldConfig.valueFormat) {
      newVal.value = newVal.value.reduce((arr: any[], x: string) => {
        if (x.match(/^\d+$/) && fieldConfig.valueFormat) {
          arr.push(fieldConfig.valueFormat(x));
        }
        return arr;
      }, []);
      if (newVal.value?.length === 0) return;
    }
    onChange([].concat(value || []).concat(newVal));
    form.resetFields();
  };

  return (
    <Form
      className={cn('!flex-nowrap form-inline flex', className)}
      form={form}
      layout="inline"
      requiredMark={false}
      style={{ paddingTop: 16 }}
      onFinish={onFinish}
      onValuesChange={(fields) => {
        if (!fields?.item) return;
        form.setFieldsValue({ logic: undefined, value: [] });
      }}
    >
      <div className="flex" style={{ width: 'calc(66% - 96px)', alignItems: 'baseline' }}>
        <Form.Item className="" label="高级筛选" name="item" rules={[{ required: true, message: '请选择过滤项' }]}>
          <Select placeholder="请选择过滤项">
            {fields.map((item: any) => {
              return (
                <Option key={item.key} value={item.value}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item className="!flex-[2] !mr-2" shouldUpdate={(prev: State, cur: State) => prev.item !== cur.item}>
          {({ getFieldValue }) => {
            const item = getFieldValue('item');
            return (
              <Form.Item name="logic" rules={[{ required: true, message: '请选择筛选条件' }]}>
                {item === 'sid' ? (
                  <Select placeholder="请选择筛选条件">
                    <Option value={isFilter[0].value}>{isFilter[0].key}</Option>
                    <Option value={isFilter[1].value}>{isFilter[1].key}</Option>
                  </Select>
                ) : (
                  <Select placeholder="请选择筛选条件">
                    {isFilter.map((item) => {
                      return (
                        <Option key={item.key} value={item.value}>
                          {item.key}
                        </Option>
                      );
                    })}
                  </Select>
                )}
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item
          style={{ width: 'calc(50% - 5em - 0.5rem' }}
          className="!mr-0"
          shouldUpdate={(prev: State, cur: State) => prev.item !== cur.item}
        >
          {({ getFieldValue }) => {
            const item = getFieldValue('item');
            const fieldConfig = fields.find((x) => x.value === item);
            return (
              <Form.Item
                name="value"
                rules={
                  fieldConfig?.valueValidateRules
                    ? fieldConfig?.valueValidateRules
                    : [{ required: true, message: '请输入筛选值' }]
                }
              >
                <Select placeholder="请输入筛选值" mode="tags" open={false} tokenSeparators={[',']} notFoundContent={null} />
              </Form.Item>
            );
          }}
        </Form.Item>
      </div>
      <div className="flex-[1] flex ml-4" style={{ alignItems: 'center' }}>
        <Button ghost className="!mr-4 w-17" type="primary" htmlType="submit">
          添加
        </Button>

        <Form.Item
          style={{ width: 'calc(92%)' }}
          className="!mr-0"
          shouldUpdate={(prev: State, cur: State) => prev.item !== cur.item}
        >
          {({ getFieldValue }) => {
            const item = getFieldValue('item');
            const logic = getFieldValue('logic');
            return (
              <Form.Item>
                <Select
                  className="!flex-[1]"
                  showSearch={false}
                  mode="multiple"
                  notFoundContent={null}
                  value={value}
                  tagRender={(e: any) => {
                    if (_isEmpty(e?.value)) return <></>;
                    console.log(item);
                    const { value } = e;
                    const symbol = LOGIC_SYMBOL_MAP.get(logic);
                    console.log(symbol);
                    return (
                      <Tag
                        closable
                        key={`${[item]}${symbol}${value.toString()}`}
                        onClose={(data) => {
                          onChange([]);
                        }}
                      >
                        {JSON.stringify(value)}
                      </Tag>
                    );
                  }}
                  onChange={(values) => onChange(values)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
      </div>
    </Form>
  );
}
