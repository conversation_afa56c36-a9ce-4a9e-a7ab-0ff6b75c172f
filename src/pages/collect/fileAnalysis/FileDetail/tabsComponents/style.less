.virusBox{
  .divBox{
    width: 300px;
    height: 160px;
    border: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title{
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 1rem;
    }
  }
  .divBox:not( .divBox:nth-child(1) ){
    margin-left: 1rem;
  }
  .baccc{
background: #ccc;
  }
}
.staticBox{
  :global{
    .ant-collapse{
      background-color: #fff !important;
    }
    .ant-collapse-content{
      border-top:none;
    }
  }
}
.signature{
  .table{
    border: 1px solid #f0f0f0;
  }
  :global{
    .ant-table-thead > tr > th, .ant-table-tbody > tr > td, .ant-table tfoot > tr > th, .ant-table tfoot > tr > td{
      padding: 8px 16px;
    }
    .ant-table-tbody > tr > td > .ant-table-wrapper:only-child .ant-table, .ant-table-tbody > tr > td > .ant-table-expanded-row-fixed > .ant-table-wrapper:only-child .ant-table{
      margin: 0;
    }
  }
}