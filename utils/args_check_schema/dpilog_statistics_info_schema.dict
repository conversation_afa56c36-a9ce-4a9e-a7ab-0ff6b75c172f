{
    "type": "object",
    "required": ["dpilogType"],
    "properties": {
        "startTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
        },
        "dpilogType": {
            "type": "string",
            "default": "",
            "pattern": r"^dpilog_(http|conn|dns|ftp|mail|ssl|files|icmp|mysql|login|dhcp|telnet|nfs|modbus|snmp|tftp|netbios|rip|igmp|smb|mssql)"
        },
        "ip1": {
            "type":"string",
            "default": ""
        },
        "ip2": {
            "type":"string",
            "default": ""
        },
        "isAccurate": {
			"type": "string",
			"default": "1"
		},
        "srcPort": {
            "type":"string",
            "default": "",
            "pattern": r"^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$"
        },
        "dstPort": {
            "type":"string",
            "default": "",
            "pattern": r"^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$"
        },
        "celeryId": {
            "type":"string",
            "default": ""
        },
        "pcap_filename": {
            "type":"string",
            "default": ""
        }
    }
}