{
    "type": "object",
    "required": [],
    "properties": {
        "start_time": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stop_time": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "sig_type": {
            "type": "string",
            "default": ""
        },
        "alias": {
            "type": "string",
            "default": ""
        },
        "org": {
            "type": "string",
            "default": ""
        },
        "tool": {
            "type": "string",
            "default": ""
        },
        "tag": {
            "type": "string",
            "default": ""
        },
        "engine": {
			"type": "string",
			"default": "",
            "enum": ["zoomeye", "fofa", "shodan", "censys", "other"]
		},
        "author": {
            "type": "string",
            "default": ""
        },
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}