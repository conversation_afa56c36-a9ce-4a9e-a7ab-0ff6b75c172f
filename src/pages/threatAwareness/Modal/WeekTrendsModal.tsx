import React from 'react';
import ReactECharts from 'echarts-for-react';
import moment from 'moment';
import { Modal } from 'antd';
import { useWeekTrendsData } from '../hooks';

export type Props = {
  show: boolean;
  trendType: string;
  onClose: () => void
}

function WeekTrendsModal({ show, trendType, onClose }: Props): JSX.Element {

  const { loading, data } = useWeekTrendsData(trendType)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: { show: false },
    grid: {
      // left: '3%',
      // right: '4%',
      // bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data?.timestamps.map((t) => moment(t).format('YYYY-MM-DD')) || []
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '总数',
        type: 'line',
        showSymbol: false,
        data: data?.values || [],
      },
    ]
  };

  return (
    <Modal
      visible={show}
      width={800}
      centered
      title="本周趋势"
      footer={null}
      onCancel={onClose}
    >
      <ReactECharts
        showLoading={loading}
        option={option}
        notMerge={true}
        style={{ width: 752, height: 400 }}
        opts={{ renderer: 'svg' }}
      />
    </Modal>
  );
}

export default WeekTrendsModal;
