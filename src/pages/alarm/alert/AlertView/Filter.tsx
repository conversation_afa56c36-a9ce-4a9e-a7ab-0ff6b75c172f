import { getKillchains, threatClassify, threatFile } from '@/services/alarm';
import { getModalName, getTaskName, getThreatLevel } from '@/services/common';
import { getModelThreatTypes, getThreatenTypes } from '@/services/infoManagement';
import { alarmHandling, intelligenceFilter, keyAssets, loopHoleFilter, modalFilter } from '@/utils/enumList';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Form, Select } from 'antd';
import cn from 'classnames';
import { pick } from 'lodash';
import React from 'react';
import AdvanceFilter from './AdvanceFilter';
import { DEFAULT_FILTER_STATE } from './shared';

interface FilterProps {
  className?: string;
  type: string;
  value: any;
  loading: boolean;
  onChange: (values: any) => void;
}

const ADV_FILTER_OPTIONS = {
  vul: loopHoleFilter,
  ioc: intelligenceFilter,
  model: modalFilter,
  file: modalFilter,
};

export default function Filter({ className, loading, type, value, onChange }: FilterProps) {
  const [form] = Form.useForm();
  React.useEffect(() => {
    form.setFieldsValue(value);
  }, [value]);

  const [taskList, settaskList] = React.useState([]);
  const [killchainList, setkillchainList] = React.useState([]);
  const [threatLevelList, setthreatLevelList] = React.useState([]);
  const [advanceFilterVisible, setAdvanceFilterVisible] = React.useState(false);
  const [threatClassifyList, setthreatClassifyList] = React.useState([]);
  const [threatTypes, setThreatTypes] = React.useState([]);
  const [modalNameList, setModalNameList] = React.useState([]);

  React.useEffect(() => {
    getTaskName({}).then((res) => {
      if (!res.flag) return;
      settaskList(res.data.concat({ taskName: '实时任务', celeryId: '00' }));
    });
    getKillchains().then((res) => {
      setkillchainList(res.data);
      const kill = {};
      res.data.map((item: any) => {
        kill[item.lockheedKillchainEN] = item.lockheedKillchainCN;
      });
      localStorage.killchainList = JSON.stringify(kill);
    });
    getThreatLevel({}).then((res) => {
      setthreatLevelList(res.data);
    });
    if (type === 'vul') {
      threatClassify().then((res) => {
        setthreatClassifyList(res.data);
      });
    } else if (type === 'ioc') {
      getThreatenTypes().then((res) => {
        if (res.flag) {
          setThreatTypes(res.data.threat_type);
        }
      });
    } else if (type === 'model') {
      getModelThreatTypes().then((res) => {
        if (res.flag) {
          setThreatTypes(res.data.threat_type);
        }
      });
      getModalName({}).then((res) => {
        if (res.flag) setModalNameList(res?.data || []);
      });
    } else if (type === 'file') {
      threatFile().then((res) => {
        if (res.flag) {
          setThreatTypes(res.data.threat_type);
        }
      });
    }
  }, []);

  const handleSubmit = React.useCallback(
    (nextValue) => {
      onChange({ ...value, ...nextValue });
    },
    [value],
  );

  return (
    <div className="flex bg-d0 p-4 b b-solid b-brd6 search_box">
      <Form
        className={cn('flex-[1] flex-col gap-4 form-inline', className)}
        form={form}
        initialValues={value}
        onFinish={handleSubmit}
        layout="inline"
      >
        <div className="flex">
          <div className="flex-[1] grid grid-cols-3 gap-4">
            {type === 'vul' ? (
              <Form.Item label="威胁分类" name="threatFlag">
                <Select allowClear placeholder="请选择威胁分类">
                  {threatClassifyList.map((item: any) => (
                    <Select.Option key={item.threatFlag} value={item.threatFlag}>
                      {item.threatFlagCN}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            ) : (
              <Form.Item label="威胁分类" name="threatType">
                <Select allowClear placeholder="请选择威胁分类">
                  {threatTypes.map((item: any) => (
                    <Select.Option key={item} value={item}>
                      {item}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}
            <Form.Item label="威胁等级" name="threatLevel">
              <Select allowClear placeholder="请选择威胁等级">
                {threatLevelList.map((item: { threatLevel: any; threatLevelCN: any }, index: any) => (
                  <Select.Option value={item.threatLevel} key={index}>
                    {item.threatLevelCN}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="杀伤链阶段" name="killchain">
              <Select allowClear placeholder="请选择杀伤链阶段">
                {killchainList.map((item: any) => (
                  <Select.Option key={item.lockheedKillchainEN} value={item.lockheedKillchainEN}>
                    {item.lockheedKillchainCN}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="任务名称" name="celeryId">
              <Select allowClear placeholder="请选择任务名称">
                {taskList.map((item: { celeryId: string; taskName: string }) => {
                  return (
                    <Select.Option key={item.celeryId} value={item.celeryId}>
                      {item.taskName}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
            {type === 'model' ? (
              <Form.Item label="模型名称" name="modelName">
                <Select allowClear placeholder="请选择模型名称">
                  {modalNameList.map(({ modelName }) => (
                    <Select.Option value={modelName} key={modelName}>
                      {modelName}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            ) : (
              <Form.Item label="重点资产" name="mbinfo">
                <Select allowClear placeholder="请选择重点资产">
                  {Object.entries(keyAssets).map(([name, value]) => (
                    <Select.Option value={value} key={value}>
                      {name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}
            <Form.Item label={<span className="pl-1em">告警处理</span>} name="confirm">
              <Select allowClear placeholder="请选择告警处理状态">
                {Object.entries(alarmHandling).map(([name, value]) => (
                  <Select.Option value={value} key={value}>
                    {name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            {type === 'model' && advanceFilterVisible && (
              <Form.Item label="重点资产" name="mbinfo">
                <Select allowClear placeholder="请选择重点资产">
                  {Object.entries(keyAssets).map(([name, value]) => (
                    <Select.Option value={value} key={value}>
                      {name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </div>
          <div className="flex flex-col justify-between items-end ml-4">
            <div>
              <Button
                ghost
                type="primary"
                disabled={loading}
                onClick={React.useCallback(() => {
                  form.setFieldsValue(
                    pick(DEFAULT_FILTER_STATE, [
                      'threatFlag',
                      'threatType',
                      'threatLevel',
                      'killchain',
                      'celeryId',
                      'mbinfo',
                      'confirm',
                      'advanceFilters',
                    ]),
                  );
                  form.submit();
                }, [form])}
              >
                重置
              </Button>
              <Button className="ml-2" type="primary" disabled={loading} htmlType="submit">
                查询
              </Button>
            </div>
            <Button
              className="!px-0"
              type="link"
              icon={advanceFilterVisible ? <UpOutlined /> : <DownOutlined />}
              onClick={React.useCallback(() => {
                setAdvanceFilterVisible(!advanceFilterVisible);
              }, [advanceFilterVisible])}
            >
              {advanceFilterVisible ? '收起全部' : '展开全部'}
            </Button>
          </div>
        </div>
        <div className={cn(' b-t b-t-solid b-t-brd6', { hidden: !advanceFilterVisible })}>
          <Form.Item name="advanceFilters">
            <AdvanceFilter className={cn('w-100%')} fields={ADV_FILTER_OPTIONS[type]} />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
}
