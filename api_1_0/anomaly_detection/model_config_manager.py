# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : model_config_manager.py
# @Software: PyCharm

"""模型参数配置管理API"""

import uuid
from datetime import datetime
from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils.logger import get_ndr_logger
from utils.database import MongoDB
from utils.utils import flask_response
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class ModelConfigList(Resource):
    """模型配置列表管理"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'model_configs'
    
    
    def get(self):
        """获取模型配置列表"""
        try:
            # 获取所有模型配置
            configs = list(self.mongodb.find(self.collection, {}, {'_id': 0}))
            
            # 如果没有配置，创建默认配置
            if not configs:
                self._create_default_configs()
                configs = list(self.mongodb.find(self.collection, {}, {'_id': 0}))
            
            # 转换时间格式
            for config in configs:
                if 'create_time' in config:
                    config['create_time'] = int(config['create_time'].timestamp() * 1000)
                if 'update_time' in config:
                    config['update_time'] = int(config['update_time'].timestamp() * 1000)
            
            return flask_response('获取模型配置成功', True, {'configs': configs})
            
        except Exception as e:
            LOG.error(f"获取模型配置失败: {str(e)}")
            return flask_response(f'获取模型配置失败: {str(e)}', False, {})
    
    
    def post(self):
        """创建新的模型配置"""
        try:
            data = request.get_json()
            
            # 验证必需字段
            required_fields = ['name', 'model_type', 'config']
            for field in required_fields:
                if field not in data:
                    return flask_response(f'缺少必需字段: {field}', False, {})
            
            # 验证模型类型
            valid_models = ['time_series', 'baseline', 'repeat_conn', 'long_conn', 'geo_anomaly']
            if data['model_type'] not in valid_models:
                return flask_response(f'不支持的模型类型: {data["model_type"]}', False, {})
            
            # 生成配置ID
            config_id = str(uuid.uuid4())
            
            # 构建配置数据
            config_data = {
                'config_id': config_id,
                'name': data['name'],
                'description': data.get('description', ''),
                'model_type': data['model_type'],
                'config': data['config'],
                'is_default': data.get('is_default', False),
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'creator': data.get('creator', 'system')
            }
            
            # 如果设置为默认配置，先取消其他默认配置
            if config_data['is_default']:
                self.mongodb.update_many(
                    self.collection,
                    {'model_type': data['model_type'], 'is_default': True},
                    {'$set': {'is_default': False}}
                )
            
            # 保存到数据库
            self.mongodb.insert_one(self.collection, config_data)
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"创建模型配置: {data['name']}")
            
            return flask_response('创建模型配置成功', True, {'config_id': config_id})
            
        except Exception as e:
            LOG.error(f"创建模型配置失败: {str(e)}")
            return flask_response(f'创建模型配置失败: {str(e)}', False, {})
    
    def _create_default_configs(self):
        """创建默认模型配置"""
        default_configs = [
            {
                'config_id': 'default_time_series',
                'name': '默认时间序列配置',
                'description': '时间序列趋势检测的默认参数配置',
                'model_type': 'time_series',
                'config': {
                    'z_score_threshold': 3.0,
                    'time_window_hours': 1,
                    'baseline_days': 7,
                    'metric_type': 'bytes'
                },
                'is_default': True,
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'creator': 'system'
            },
            {
                'config_id': 'default_baseline',
                'name': '默认基线偏移配置',
                'description': '基线偏移检测的默认参数配置',
                'model_type': 'baseline',
                'config': {
                    'baseline_periods': [7, 30, 90],
                    'deviation_threshold': 3.0,
                    'min_baseline_samples': 5,
                    'metric_types': ['connections', 'bytes', 'unique_ips']
                },
                'is_default': True,
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'creator': 'system'
            },
            {
                'config_id': 'default_repeat_conn',
                'name': '默认重复连接配置',
                'description': '重复连接检测的默认参数配置',
                'model_type': 'repeat_conn',
                'config': {
                    'frequency_threshold': 100,
                    'time_window_minutes': 60,
                    'unique_threshold': 10,
                    'pattern_types': ['ip_to_domain', 'ip_to_ip', 'port_scan']
                },
                'is_default': True,
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'creator': 'system'
            },
            {
                'config_id': 'default_long_conn',
                'name': '默认长连接配置',
                'description': '长连接分析的默认参数配置',
                'model_type': 'long_conn',
                'config': {
                    'duration_thresholds': {
                        'http': 600,
                        'https': 1800,
                        'tcp': 3600,
                        'dns': 30,
                        'default': 1800
                    },
                    'min_bytes_threshold': 1024,
                    'percentile_threshold': 95
                },
                'is_default': True,
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'creator': 'system'
            },
            {
                'config_id': 'default_geo_anomaly',
                'name': '默认地理异常配置',
                'description': '地理/归属异常检测的默认参数配置',
                'model_type': 'geo_anomaly',
                'config': {
                    'baseline_days': 30,
                    'min_baseline_records': 10,
                    'geo_change_threshold': 0.8,
                    'asn_change_threshold': 0.7,
                    'suspicious_countries': ['RU', 'CN', 'KP', 'IR', 'SY']
                },
                'is_default': True,
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'creator': 'system'
            }
        ]
        
        for config in default_configs:
            self.mongodb.insert_one(self.collection, config)


class ModelConfigDetail(Resource):
    """模型配置详情管理"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
        self.collection = 'model_configs'
    
    
    def get(self, config_id):
        """获取模型配置详情"""
        try:
            config = self.mongodb.find_one(self.collection, {'config_id': config_id}, {'_id': 0})
            if not config:
                return flask_response('配置不存在', False, {})
            
            # 转换时间格式
            if 'create_time' in config:
                config['create_time'] = int(config['create_time'].timestamp() * 1000)
            if 'update_time' in config:
                config['update_time'] = int(config['update_time'].timestamp() * 1000)
            
            return flask_response('获取配置详情成功', True, config)
            
        except Exception as e:
            LOG.error(f"获取配置详情失败: {str(e)}")
            return flask_response(f'获取配置详情失败: {str(e)}', False, {})
    
    
    def put(self, config_id):
        """更新模型配置"""
        try:
            data = request.get_json()
            
            # 检查配置是否存在
            existing_config = self.mongodb.find_one(self.collection, {'config_id': config_id})
            if not existing_config:
                return flask_response('配置不存在', False, {})
            
            # 构建更新数据
            update_data = {
                'update_time': datetime.now()
            }
            
            # 更新允许的字段
            allowed_fields = ['name', 'description', 'config', 'is_default']
            for field in allowed_fields:
                if field in data:
                    update_data[field] = data[field]
            
            # 如果设置为默认配置，先取消其他默认配置
            if data.get('is_default'):
                self.mongodb.update_many(
                    self.collection,
                    {'model_type': existing_config['model_type'], 'is_default': True},
                    {'$set': {'is_default': False}}
                )
            
            # 更新配置
            self.mongodb.update_one(
                self.collection,
                {'config_id': config_id},
                {'$set': update_data}
            )
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"更新模型配置: {config_id}")
            
            return flask_response('更新配置成功', True, {})
            
        except Exception as e:
            LOG.error(f"更新配置失败: {str(e)}")
            return flask_response(f'更新配置失败: {str(e)}', False, {})
    
    
    def delete(self, config_id):
        """删除模型配置"""
        try:
            # 检查配置是否存在
            config = self.mongodb.find_one(self.collection, {'config_id': config_id})
            if not config:
                return flask_response('配置不存在', False, {})
            
            # 不允许删除默认配置
            if config.get('is_default'):
                return flask_response('不能删除默认配置', False, {})
            
            # 删除配置
            self.mongodb.delete_one(self.collection, {'config_id': config_id})
            
            # 记录日志
            ndr_log_to_box(NdrLog.Type.OPERATE, f"删除模型配置: {config_id}")
            
            return flask_response('删除配置成功', True, {})
            
        except Exception as e:
            LOG.error(f"删除配置失败: {str(e)}")
            return flask_response(f'删除配置失败: {str(e)}', False, {})
