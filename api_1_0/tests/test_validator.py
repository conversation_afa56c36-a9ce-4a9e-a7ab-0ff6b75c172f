from unittest import TestCase
from utils.param_check import Validator


class TestValidator(TestCase):
    rules = {
        "threatFlag": "dos",
        "threatScore": 80,
        "author": "<PERSON>fu",
        "vulName": "Drupalgeddon2 <8.3.9 <8.4.6 <8.5.1 代码执行漏洞",
        "vulType": "代码执行",
        "cve": "CVE-2018-7600",
        "lockheedKillchainStage": 3,
        "lockheedKillchainCN": "载荷投递",
        "lockheedKillchainEN": "Delivery",
        "threatFlag": "Exploits and Attacks",
        "alterInfo": "",
        "submitTime": "2019-06-18",
        "is0day": 1,
        "ruleContent": "alert http any any -> any any (msg: \"ATTACK [PTsecurity] Drupalgeddon2 <8.3.9 <8.4.6 <8.5.1 RCE through registration form (CVE-2018-7600)\"; flow: established, to_client; flowbits:isset,30000014; content: \"200\";http_stat_code; content:\"|22|command|22 3A 22|insert|22|\"; http_server_body; content:\"|22|data|22|\"; reference: cve, 2018-7600; reference: url, research.checkpoint.com/uncovering-drupalgeddon-2; classtype: attempted-admin; reference: url, github.com/ptresearch/AttackDetection; metadata: Open Ptsecurity.com ruleset; sid: 30000019; rev: 2; )",
        "attackIp": "src_ip",
        "victimIp": "dst_ip",
        "appProto": "http"

    }
    validator = Validator('rule_add_schema')
    msg, err_arg, err_value = validator.validate(rules)
    print(msg)
