{
	"type": "object",
	"required": [],
	"properties": {
		"modelNum": {
			"type": "string",
			"pattern": r"^[0-9]\d{0,1}$",
			"default": ""
		},
		"modelName": {
			"type": "string",
			"default": ""
		},
		"modelStatus": {
			"type": "string",
			"enum": ["disable", "enable"],
			"default": ""
		},
		"page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
	}
}