# coding:utf-8
import subprocess
import hashlib
from datetime import datetime
from pathlib import Path

import psutil


def run_command(command, ignore_error=False, timeout=None):
    """
    Run bash command, return stdout or stderr, all data is saved in RAM,
    So do not use this function when data is huge!
    """
    proc = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    try:
        output, error = proc.communicate(timeout=timeout)

        if proc.returncode != 0 and not ignore_error:
            return False, str(error, 'utf-8')
        else:
            return True, output.decode("utf-8")
    except subprocess.TimeoutExpired:
        proc.kill()
        return False, 'Timeout'

def file_hash(filename, hash_algorithm='md5'):
    if not Path(filename).exists():
        return ""

    hash_obj = hashlib.new(hash_algorithm)
    with open(filename, 'rb') as f:
        # 分块读取文件
        chunk_size = 4096
        chunk = f.read(chunk_size)
        while chunk:
            hash_obj.update(chunk)
            chunk = f.read(chunk_size)

    hash_digest = hash_obj.digest()
    hash_hex = hash_digest.hex()
    return hash_hex

def get_boot_time():
    boot_time = psutil.boot_time()
    boot_time_obj = datetime.fromtimestamp(boot_time)
    now_time = datetime.now()
    up_time = (now_time - boot_time_obj).total_seconds()
    return up_time
