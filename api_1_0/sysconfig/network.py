# -*- coding: utf-8 -*-
# @Time    : 2019-06-19 17:10
# <AUTHOR> wu
# @File    : system.py
# @Software: PyCharm

"""module system"""
import json
import os
import time
from threading import Thread
import ipaddress
import psutil
import pexpect
import requests
import yaml
import sh
from flask import request
from flask_restful import Resource

from api_1_0.hdp_grpc.port import grpc_get_port_links
from config.config import NdrLog
from utils.utils import flask_response, supervisorctl_host_service, run_command
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from utils.param_check import Validator, check_flask_args
from utils.cluster import *
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def get_if_link_status():
    link_status = {}
    try:
        response = grpc_get_port_links()
        for status in response.status_list:
            link_status[status.port_id] = status.link_status
    except Exception as e:
        ndr_log_to_box(NdrLog.Type.OPERATE, "get_if_link_status failed, " + str(e))

    return link_status


class Interface(Resource):
    """
    work interface config
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    def get(self):
        raw_data = self.mongodb.find('interface_of_work', {})
        if not raw_data:
            return flask_response("Get interface info config failed.", False, {})

        intf_list = []
        link_status = get_if_link_status()
        for intf in raw_data:
            intf_info = {
                "node_name": intf.get("node_name", ""),
                "if_index": int(intf["if_index"]),
                "if_name": intf["if_name"],
                "pci_addr": intf["pci_addr"],
                "speed": "%dGbps" % intf["speed"],
                "work_mode": intf["work_mode"],
                "status": intf["status"],
                "link": link_status[intf["if_index"]] if intf["if_index"] in link_status.keys() else 0
            }
            intf_list.append(intf_info)
        return flask_response("Get interface info config success.", True, intf_list)

    @check_flask_args(Validator('system_interface_schema'), request)
    def put(self, **kwargs):
        if_name = kwargs['args']['if_name']
        action = kwargs['args']['action']
        work_mode = kwargs['work_mode']
        node_name = kwargs['args'].get('node_name', '')
        condition = {"if_name": if_name}
        if node_name:
            condition["node_name"] = node_name

        # FIXME
        LOG.info("condition: %s", json.dumps(condition))
        if_info = self.mongodb.find_one('interface_of_work', condition)
        if not if_info:
            return flask_response("The interface: %s not exist." % if_name, False, {})

        ret = 'success'
        if "start" == action:
            if "idle" == if_info['status']:
                ret = self.interface_work_status_update(condition, if_info, "working")
        else:
            if "working" == if_info['status']:
                ret = self.interface_work_status_update(condition, if_info, "idle")

        # 检查端口是否被转发组占用，被占用时不能更改模式，除非先删除组
        if work_mode != if_info['work_mode']:
            if work_mode == 'black_hole':
                raw_data = self.mongodb.find_one('interface_forword_cfg',
                                                 {'interface_list': {'$all': [if_info['if_name']]}})
                if not raw_data:
                    self.mongodb.update_one('interface_of_work', condition,
                                            {'$set': {'work_mode': work_mode}})
                else:
                    return flask_response("Can't set black hole mode when the interface used by forword group", False, {})
            else:
                self.mongodb.update_one('interface_of_work', condition,
                                        {'$set': {'work_mode': work_mode}})

        if ret == 'success':
            return flask_response("The interface action set successed!", True, {})
        else:
            return flask_response("The interface action set failed! " + ret, False, {})

    def interface_work_status_update(self, condition, if_info, status):
        # 1, 更新MongoDB中接口状态
        self.mongodb.update('interface_of_work', condition, {'status': status})

        # 2, 组装hdp配置参数port_mask
        node_name = condition.get('node_name', '')
        if node_name:
            condition = {'node_name': node_name, 'status': 'working'}
        else:
            condition = {'status': 'working'}

        work_ifs = self.mongodb.find('interface_of_work', condition)
        mask_number = 0
        for interface in work_ifs:
            mask_number = mask_number | (1 << int(interface['if_index']))

        port_mask = str(hex(mask_number))
        ndr_log_to_box(NdrLog.Type.OPERATE, "Now the hdp-fwd-confg changed, port_mask: %s" % port_mask)

        # 3, 更新HDP配置文件&restart hdp
        LOG.info("node_name: %s", node_name)
        if node_name:
            r = cluster_hdp_update_portmask(node_name, port_mask)
            if r:
                LOG.error("failed: %s", r)
                return "failed"
        else:
            action = "restart"
            self.hdp_cfg_update_and_restart(port_mask, action)
        return 'success'

    @staticmethod
    def hdp_cfg_update_and_restart(port_mask, action):
        cmd = 'sed -i "s/^port_mask.*/port_mask = %s/g" /opt/hdp/config/conf.ini' % port_mask
        ret, msg = run_command(cmd)
        if not ret:
            return "Modify hdp port-mask parameter failed(%s)." % msg

        supervisorctl_host_service(action, 'hdp')
        return 'success'


class InterfaceForword(Resource):
    def __init__(self):
        self.collection = 'interface_forword_cfg'
        self.mongo = MongoDB("ndr")

    def hdp_cfg_update_and_restart(self):
        # 获取interface对应的index
        cfg_idx_grp_list = []
        raw_data = self.mongo.find(self.collection, {"status": "enable"})
        if raw_data:
            for data in raw_data:
                intf_grp_list = []
                for intf in data['interface_list']:
                    raw_index = self.mongo.find_one('interface_of_work', {'if_name': intf})
                    if not raw_index:
                        return False, "Can't find interface %s" % intf
                    intf_grp_list.append(raw_index['if_index'])
                cfg_idx_grp_list.append(tuple(intf_grp_list))

        # 更新配置到conf.ini
        if cfg_idx_grp_list:
            cmd = 'sed -i "s/.*port-fwd.*/port-fwd = %s/g" /opt/hdp/config/conf.ini' % str(cfg_idx_grp_list).replace(' ', '')
        else:
            cmd = 'sed -i "s/.*port-fwd.*/#port-fwd = ()/g" /opt/hdp/config/conf.ini'
        ret, msg = run_command(cmd)

        supervisorctl_host_service("restart", 'hdp')

        return ret, msg

    def get(self, group_name=""):
        data = []
        cond = {} if not group_name else {"group_name": group_name}
        raw_data = self.mongo.find(self.collection, cond, {"_id": 0})
        for detail in raw_data:
            data.append(detail)

        return flask_response("", True, {"data": data})

    @check_flask_args(Validator('system_interface_forword_schema'), request)
    def post(self, **kwargs):
        ret = True
        msg = "success"
        if 2 != len(kwargs['interface_list']) or kwargs['interface_list'][0] == kwargs['interface_list'][1]:
            return flask_response("Arg interface group must have two different members!", False, {})

        raw_data = self.mongo.find_one(self.collection, {'group_name': kwargs['group_name']})
        if raw_data:
            return flask_response("Group name %s has exist!" % kwargs['group_name'], False, {})

        raw_data = self.mongo.find_one(self.collection, {"interface_list": {"$all": kwargs['interface_list']}})
        if raw_data:
            return flask_response("Interface is used by group name %s" % raw_data['group_name'], False, {})

        if 'enable' == kwargs['status']:
            raw_data = self.mongo.find_one(self.collection, {"status": "enable"})
            if raw_data:
                return flask_response("Interface forword only support enable one group!", False, {})

        data = {
            'group_name': kwargs['group_name'],
            'forword_mode': kwargs['forword_mode'],
            'interface_list': kwargs['interface_list'],
            'status': kwargs['status']
        }

        port_speed_li = []
        for interface in kwargs['interface_list']:
            raw_data = self.mongo.find_one('interface_of_work', {'if_name': interface})
            if not raw_data:
                return flask_response("Interface %s is invalid!" % interface, False, {})
            port_speed_li.append(raw_data['speed'])

        if port_speed_li[0] != port_speed_li[1]:
            return flask_response("Interfaces must have the same speed!", False, {})

        self.mongo.insert_one(self.collection, data)

        if "enable" == data['status']:
            ret, msg = self.hdp_cfg_update_and_restart()

        return flask_response("success" if ret else "failed", ret, {"msg": msg})

    @check_flask_args(Validator('system_interface_forword_modify_schema'), request)
    def put(self, **kwargs):
        raw_data = self.mongo.find_one(self.collection, {'group_name': kwargs['group_name']})
        if not raw_data:
            return flask_response("group_name %s not found!" % kwargs['group_name'], False, {})

        if raw_data['status'] != kwargs['status']:
            if 'enable' == kwargs['status']:
                raw_data = self.mongo.find_one(self.collection, {"status": "enable"})
                if raw_data:
                    return flask_response("Interface forword only support enable one group!", False, {})
            self.mongo.update_one(self.collection, {'group_name': kwargs['group_name']},
                                  {"$set": {"status": kwargs['status']}})
        else:
            return flask_response("Status already %s" % raw_data['status'], False, {})

        ret, msg = self.hdp_cfg_update_and_restart()

        return flask_response("success" if ret else "failed", ret, {"msg": msg})

    def delete(self, group_name):
        raw_data = self.mongo.find_one(self.collection, {'group_name': group_name})
        if not raw_data:
            return flask_response("Can't found group name %s" % group_name, False, {})

        if "enable" == raw_data['status']:
            return flask_response("Must disable group name %s before delete!" % group_name, False, {})

        self.mongo.delete(self.collection, {'group_name': group_name})

        return flask_response("", True, {})


def set_address_by_netplan(dev, ipv4_addr, mask, gateway=None, dns=None, apply_config=True):
    """
    :param dev:
    :param ipv4_addr:
    :param mask:
    :param gateway:
    :param dns:
    :param apply_config:
    :return:
    """
    # 获取配置
    netplan_cfg = open('/etc/netplan/50-cloud-init.yaml', 'r')
    cfgs = yaml.load(netplan_cfg, Loader=yaml.SafeLoader)
    # 修改配置
    netplan_cfg.close()
    ip4 = ipaddress.IPv4Network((0, mask))
    cfgs['network']['ethernets'][dev]['addresses'] = [ipv4_addr + '/' + str(ip4.prefixlen)]
    if gateway is not None:
        cfgs['network']['ethernets'][dev]['gateway4'] = gateway
    if dns is not None:
        cfgs['network']['ethernets'][dev]['nameservers'] = {'addresses': dns}

    # 保存配置
    netplan_cfg = open('/etc/netplan/50-cloud-init.yaml', 'w')
    yaml.dump(cfgs, netplan_cfg)
    netplan_cfg.close()
    # 配置生效
    if apply_config:
        sh.sudo('netplan', 'apply')
    return True


class NetCardInfo(Resource):
    """获取网卡信息"""
    @staticmethod
    def get():
        """
        :return:
        """
        net_card_info = []
        loop = 0
        info = psutil.net_if_addrs()
        mongodb = MongoDB("ndr")
        for key, value in info.items():
            if key == "lo":
                continue
            net_card_info.append({"name": key, "type": ""})
            conf_net = mongodb.find_one('network', {'name': key}, {"_id": 0, "type": 1})
            if conf_net:
                net_card_info[loop]["type"] = conf_net["type"]
            else:
                conf_net = mongodb.find_one('network', {'type': "worker"}, {"_id": 0, "type": 1, "workInterfaceList": 1})
                if conf_net and key in conf_net["workInterfaceList"]:
                    net_card_info[loop]["type"] = conf_net["type"]
            for item in value:
                if item[0] == 2:
                    net_card_info[loop].update({"ipAdress": item[1]})
                if item[0] == 17:
                    net_card_info[loop].update({"macAdress": item[1]})
            loop += 1
        return flask_response("", True, {"netcardInfo": net_card_info})


class Network(Resource):
    """网络配置"""
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    # 设置工作口
    def set_net_worker(self, interface_list):
        """
        :param interface_list:
        :return:
        """
        try:
            # 重启hdp进程
            supervisorctl_host_service('restart', 'hdp')
            # 写入 MongoDB
            if self.mongodb.find_one('network', {'type': "worker"}):
                self.mongodb.update('network', {'type': "worker"},
                                    {"workInterfaceList": interface_list,
                                     "updateTime": int(time.time() * 1000)})
            else:
                self.mongodb.insert_one("network",
                                        {'type': "worker",
                                         "workInterfaceList": interface_list})
        except IOError as err_info:
            LOG.error('Set worker network ERROR: %s' % err_info)
            ndr_log_to_box(NdrLog.Type.OPERATE, "工作口配置失败：%s" % err_info)
            return err_info
        LOG.info("Set worker network success.")
        ndr_log_to_box(NdrLog.Type.OPERATE, "工作口配置成功.")
        return ""

    @check_flask_args(Validator('system_network_schema'), request)
    def post(self, **kwargs):
        """config manager network"""
        name = kwargs['args']['name']
        ip_address = kwargs['args']['ip']
        mask = kwargs['args']['mask']
        gateway = kwargs['args']['gateway']
        p_dns = kwargs['args']['primaryDNS']
        s_dns = kwargs['args']['secondDNS']

        dns = [p_dns, s_dns]
        try:
            set_address_by_netplan(name, ip_address, mask, gateway, dns, apply_config=True)
            if self.mongodb.find_one('network', {'type': "manager"}):
                self.mongodb.update('network', {'type': "manager"},
                                    {"name": name,
                                     "ip": ip_address,
                                     "mask": mask,
                                     "gateway": gateway,
                                     "primaryDNS": p_dns,
                                     "secondDNS": s_dns,
                                     "updateTime": int(time.time() * 1000)})
            else:
                self.mongodb.insert_one("network",
                                        {'type': "manager",
                                         "name": name,
                                         "ip": ip_address,
                                         "mask": mask,
                                         "gateway": gateway,
                                         "primaryDNS": p_dns,
                                         "secondDNS": s_dns,
                                         "updateTime": int(time.time() * 1000)})
        except IOError as err_info:
            LOG.error('Set manager network ERROR: %s' % err_info)
            ndr_log_to_box(NdrLog.Type.OPERATE, "管理口配置失败：%s" % err_info)
            return flask_response("%s" % err_info, False, {})
        LOG.info("Set manager network success.")
        ndr_log_to_box(NdrLog.Type.OPERATE, "管理口配置成功.")

        # 设置工作口配置
        interface_list = kwargs['args']['workInterfaceList']
        rst = self.set_net_worker(interface_list)
        if rst != "":
            return flask_response("", False, rst)
        return flask_response("", True, {})

    def get(self):
        """get network config"""
        config = self.mongodb.find_one('network', {'type': "manager"}, {"_id": 0, "type": 0})
        if config:
            worker_config = self.mongodb.find_one('network', {'type': "worker"}, {"_id": 0, "workInterfaceList": 1})
            if worker_config:
                config.update(worker_config)
                return flask_response("", True, config)
        return flask_response("Get network config failed.", False, {})


class NetCheck(Resource):
    """ping"""
    def __init__(self, address_list, cmd):
        self.address_list = address_list
        self.command = cmd
        self.result = []
        self.threads = []
        self.ret_flag = 0

    def trace_func(self, func, *args, **kwargs):
        """
        :param func:
        :param args:
        :param kwargs:
        :return:
        """
        ret = func(*args, **kwargs)
        self.result.append(ret)

    @staticmethod
    def run(address, cmd):
        """
        :param address:
        :param cmd:
        :return:
        """
        status = False
        if cmd == "ping":
            stdout = pexpect.run("ping -c1 %s" % address, timeout=5)
            if stdout.find(b'received') != -1:
                status = True
            return {'ping': address, 'status': status}
        url = address
        if "http://" not in address:
            url = "http://" + address
        try:
            rst = requests.get(url, timeout=5)
            if rst.status_code == 200:
                status = True
            return {'curl': address, 'status': status}
        except Exception as err:
            LOG.error(err)
            return {'curl': address, 'status': False}

    def start(self):
        """
        :return:
        """
        for address in self.address_list:
            thread = Thread(target=self.trace_func, args=(self.run, address, self.command))
            self.threads.append(thread)
        for thread_obj in self.threads:
            thread_obj.start()
        for thread_obj in self.threads:
            thread_obj.join()

        return self.result


class NetWorkTest(Resource):
    """网络检测"""
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @check_flask_args(Validator('system_network_test_schema'), request)
    def post(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        address_list = []
        command = "ping"
        if kwargs['args']:
            command = kwargs['args']['command']
            address_list = kwargs['args']['addressList']
        if not address_list:
            mng = self.mongodb.find_one('network', {'type': "manager"})
            if not mng:
                ndr_log_to_box(NdrLog.Type.OPERATE, "网络检测失败：未配置。")
                return flask_response("Network is not configured.", False, {})
            address_list.append("www.baidu.com")
            address_list.append(mng["gateway"])
            address_list.append(mng["ip"])
            address_list.append(mng["primaryDNS"])
            address_list.append(mng["secondDNS"])
        rst = NetCheck(address_list, command).start()
        access = False
        for term in rst:
            if term["status"]:
                access = True
        return flask_response("", True, {"detail": rst, "reachable": access})
