# -*- coding: utf-8 -*-
# @Time    : 2019-05-15 11:11
# <AUTHOR> Shark
# @File    : spi_view.py
# @Software: PyCharm

import json
import requests
from requests.auth import HTTPDigestAuth
from flask import request
from flask_restful import Resource
from config.config import BackStreamConfig
from api_1_0.back_stream.utils import check_args


class SpiView(Resource):
    """api接口/api/v1/pcap/spiview处理"""

    def __init__(self):
        self.args = request.values.to_dict()
        self.response = {
            "message": "",
            "data": {}
        }

    def args_valid_check(self):
        """ 根据正则匹配的方式检测每个参数是否有效，
        无效则返回 False 和错误参数
        有效则返回 True"""

        return check_args(self.args)

    def comb_url(self):
        """ 判断传入的参数是否为空，来确认是否给默认值，并组装最终的 URL 请求"""

        req_url = 'http://{0}:{1}/spiview.json?'.format(
            BackStreamConfig.BackStream_HOST,
            BackStreamConfig.BackStream_PORT)

        # 直接以参数形式添加的模块
        if self.args['startTime']:
            req_url += "&startTime=" + self.args['startTime']
        if self.args['stopTime']:
            req_url += "&stopTime=" + self.args['stopTime']
        # 如果没有传入起止时间，则默认在 12 小时以内搜索结果
        if not self.args['startTime'] and not self.args['stopTime']:
            req_url += "&date=12"
        # 选择查询的spiview协议内容字段,dstIp,srcIp,protocol字段
        if self.args['spi']:
            req_url += "&spi=" + self.args['spi']
            # 如果给了num参数，则追加到spi，如果没给，则追加默认值10
            if self.args['num']:
                req_url += ":" + self.args['num']
            else:
                req_url += ":10"

        return req_url

    def get(self):
        """api接口/api/v1/pcap/spiview的get请求处理"""
        is_valid, msg = self.args_valid_check()

        if not is_valid:
            self.response['message'] = msg
            return self.response

        resp = requests.get(self.comb_url(), auth=HTTPDigestAuth(
            BackStreamConfig.BackUsername,
            BackStreamConfig.BackPassword
        ))

        self.response['message'] = ""
        self.response['data'] = json.loads(resp.text)['spi']

        return self.response
