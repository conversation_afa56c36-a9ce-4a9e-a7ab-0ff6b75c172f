#!/usr/bin/env python3
# coding:utf-8

import sys
sys.path.append('/opt/ndr')

import json
import argparse

from cluster.api.manager import MyClusterManager
from cluster.api.consistency import Consistency

def check_and_sync_files(check_only:bool, files:list):
    consistency = Consistency()
    if check_only:
        print(json.dumps(consistency.check_files(files)))
    else:
        consistency.check_and_sync_files(files)

def service(op, services:list):
    my_cluster_mgr = MyClusterManager()
    print(my_cluster_mgr.service_by_cluster(op, services))

def main():
    parser = argparse.ArgumentParser(description="Cluster Management Client Programme")

    # 创建子命令解析器
    subparsers = parser.add_subparsers(dest='command', required=True)

    sync_parser = subparsers.add_parser('sync-files', help='Sync files command help')
    sync_parser.add_argument('--check-only', action='store_true', help='Set sync to true')
    sync_parser.add_argument('files', nargs='*', help='List of files to process')

    service_parser = subparsers.add_parser('service', help='Service command help')
    service_parser.add_argument('--op', default='status', help='Set operation, such as status, restart')
    service_parser.add_argument('services', nargs='*', help='List of service to process')

    args = parser.parse_args()
    if args.command == 'sync-files':
        check_and_sync_files(args.check_only, args.files)
    elif args.command == 'service':
        service(args.op, args.services)


if __name__ == "__main__":
    main()