# from api_1_0.tests.back_explore import ExploreTaskWithId
import unittest
from unittest import defaultTestLoader
# from HTMLTestRunner import *
from api_1_0.tests.back_event.test_eventListResource import *
from api_1_0.tests.back_event.test_eventGroupByResource import *


# 测试用例存放路径
case_path_back_alert = './api_1_0/tests/back_event'


# 获取所有测试用例
def get_all_case():
    discover = unittest.defaultTestLoader.discover(case_path_back_alert, pattern="test_get*.py")
    suite = unittest.TestSuite()
    suite.addTest(discover)
    return suite


if __name__ == '__main__':
    # 运行测试用例
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(get_all_case())

    # unittest.main()
    # report = './report.html'
    # with open(report, 'wb') as f:
    #     runner = HTMLTestRunner(f, verbosity=2, title='Test', description='单元测试报告')
    #     # runner.run(TestExploreTaskWithId('test_get_1'))
    #     # runner.run(TestExploreTaskWithId('test_get_2'))
    #     # runner.run(TestExploreTaskWithId('test_get_3'))
    #     # runner.run(TestExploreTaskWithId('test_put_1'))
    #     runner.run(TestEventGroupByResource('test_get_query_random_one_key'))
    #     runner.run(TestEventGroupByResource('test_get_query_random_mul_key'))
    # runner.run(TestEventListResource('test_get_start_time_error'))
    # runner.run(TestEventListResource('test_get_start_time_gt_stop_time'))
    # runner.run(TestEventListResource('test_get_start_time_over_now_time'))
