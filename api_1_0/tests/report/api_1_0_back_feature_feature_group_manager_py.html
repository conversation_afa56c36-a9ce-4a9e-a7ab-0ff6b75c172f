


<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    
    
    <meta http-equiv="X-UA-Compatible" content="IE=emulateIE7" />
    <title>Coverage for api_1_0/back_feature/feature_group_manager.py: 82%</title>
    <link rel="stylesheet" href="style.css" type="text/css">
    
    <script type="text/javascript" src="jquery.min.js"></script>
    <script type="text/javascript" src="jquery.hotkeys.js"></script>
    <script type="text/javascript" src="jquery.isonscreen.js"></script>
    <script type="text/javascript" src="coverage_html.js"></script>
    <script type="text/javascript">
        jQuery(document).ready(coverage.pyfile_ready);
    </script>
</head>
<body class="pyfile">

<div id="header">
    <div class="content">
        <h1>Coverage for <b>api_1_0/back_feature/feature_group_manager.py</b> :
            <span class="pc_cov">82%</span>
        </h1>

        <img id="keyboard_icon" src="keybd_closed.png" alt="Show keyboard shortcuts" />

        <h2 class="stats">
            141 statements &nbsp;
            <span class="run hide_run shortkey_r button_toggle_run">116 run</span>
            <span class="mis shortkey_m button_toggle_mis">25 missing</span>
            <span class="exc shortkey_x button_toggle_exc">0 excluded</span>

            
        </h2>
    </div>
</div>

<div class="help_panel">
    <img id="panel_icon" src="keybd_open.png" alt="Hide keyboard shortcuts" />
    <p class="legend">Hot-keys on this page</p>
    <div>
    <p class="keyhelp">
        <span class="key">r</span>
        <span class="key">m</span>
        <span class="key">x</span>
        <span class="key">p</span> &nbsp; toggle line displays
    </p>
    <p class="keyhelp">
        <span class="key">j</span>
        <span class="key">k</span> &nbsp; next/prev highlighted chunk
    </p>
    <p class="keyhelp">
        <span class="key">0</span> &nbsp; (zero) top of page
    </p>
    <p class="keyhelp">
        <span class="key">1</span> &nbsp; (one) first highlighted chunk
    </p>
    </div>
</div>

<div id="source">
    <table>
        <tr>
            <td class="linenos">
<p id="n1" class="pln"><a href="#n1">1</a></p>
<p id="n2" class="pln"><a href="#n2">2</a></p>
<p id="n3" class="pln"><a href="#n3">3</a></p>
<p id="n4" class="pln"><a href="#n4">4</a></p>
<p id="n5" class="pln"><a href="#n5">5</a></p>
<p id="n6" class="pln"><a href="#n6">6</a></p>
<p id="n7" class="stm run hide_run"><a href="#n7">7</a></p>
<p id="n8" class="pln"><a href="#n8">8</a></p>
<p id="n9" class="stm run hide_run"><a href="#n9">9</a></p>
<p id="n10" class="stm run hide_run"><a href="#n10">10</a></p>
<p id="n11" class="stm run hide_run"><a href="#n11">11</a></p>
<p id="n12" class="stm run hide_run"><a href="#n12">12</a></p>
<p id="n13" class="stm run hide_run"><a href="#n13">13</a></p>
<p id="n14" class="stm run hide_run"><a href="#n14">14</a></p>
<p id="n15" class="stm run hide_run"><a href="#n15">15</a></p>
<p id="n16" class="stm run hide_run"><a href="#n16">16</a></p>
<p id="n17" class="stm run hide_run"><a href="#n17">17</a></p>
<p id="n18" class="stm run hide_run"><a href="#n18">18</a></p>
<p id="n19" class="stm run hide_run"><a href="#n19">19</a></p>
<p id="n20" class="pln"><a href="#n20">20</a></p>
<p id="n21" class="stm run hide_run"><a href="#n21">21</a></p>
<p id="n22" class="pln"><a href="#n22">22</a></p>
<p id="n23" class="pln"><a href="#n23">23</a></p>
<p id="n24" class="stm run hide_run"><a href="#n24">24</a></p>
<p id="n25" class="pln"><a href="#n25">25</a></p>
<p id="n26" class="pln"><a href="#n26">26</a></p>
<p id="n27" class="pln"><a href="#n27">27</a></p>
<p id="n28" class="stm run hide_run"><a href="#n28">28</a></p>
<p id="n29" class="stm run hide_run"><a href="#n29">29</a></p>
<p id="n30" class="pln"><a href="#n30">30</a></p>
<p id="n31" class="pln"><a href="#n31">31</a></p>
<p id="n32" class="pln"><a href="#n32">32</a></p>
<p id="n33" class="stm run hide_run"><a href="#n33">33</a></p>
<p id="n34" class="pln"><a href="#n34">34</a></p>
<p id="n35" class="pln"><a href="#n35">35</a></p>
<p id="n36" class="pln"><a href="#n36">36</a></p>
<p id="n37" class="pln"><a href="#n37">37</a></p>
<p id="n38" class="pln"><a href="#n38">38</a></p>
<p id="n39" class="pln"><a href="#n39">39</a></p>
<p id="n40" class="stm run hide_run"><a href="#n40">40</a></p>
<p id="n41" class="stm run hide_run"><a href="#n41">41</a></p>
<p id="n42" class="stm mis"><a href="#n42">42</a></p>
<p id="n43" class="stm run hide_run"><a href="#n43">43</a></p>
<p id="n44" class="stm mis"><a href="#n44">44</a></p>
<p id="n45" class="stm run hide_run"><a href="#n45">45</a></p>
<p id="n46" class="stm run hide_run"><a href="#n46">46</a></p>
<p id="n47" class="stm run hide_run"><a href="#n47">47</a></p>
<p id="n48" class="stm run hide_run"><a href="#n48">48</a></p>
<p id="n49" class="stm run hide_run"><a href="#n49">49</a></p>
<p id="n50" class="stm run hide_run"><a href="#n50">50</a></p>
<p id="n51" class="stm run hide_run"><a href="#n51">51</a></p>
<p id="n52" class="stm run hide_run"><a href="#n52">52</a></p>
<p id="n53" class="pln"><a href="#n53">53</a></p>
<p id="n54" class="stm mis"><a href="#n54">54</a></p>
<p id="n55" class="pln"><a href="#n55">55</a></p>
<p id="n56" class="stm run hide_run"><a href="#n56">56</a></p>
<p id="n57" class="pln"><a href="#n57">57</a></p>
<p id="n58" class="pln"><a href="#n58">58</a></p>
<p id="n59" class="stm run hide_run"><a href="#n59">59</a></p>
<p id="n60" class="pln"><a href="#n60">60</a></p>
<p id="n61" class="pln"><a href="#n61">61</a></p>
<p id="n62" class="pln"><a href="#n62">62</a></p>
<p id="n63" class="pln"><a href="#n63">63</a></p>
<p id="n64" class="pln"><a href="#n64">64</a></p>
<p id="n65" class="pln"><a href="#n65">65</a></p>
<p id="n66" class="stm run hide_run"><a href="#n66">66</a></p>
<p id="n67" class="stm run hide_run"><a href="#n67">67</a></p>
<p id="n68" class="pln"><a href="#n68">68</a></p>
<p id="n69" class="pln"><a href="#n69">69</a></p>
<p id="n70" class="stm run hide_run"><a href="#n70">70</a></p>
<p id="n71" class="pln"><a href="#n71">71</a></p>
<p id="n72" class="stm run hide_run"><a href="#n72">72</a></p>
<p id="n73" class="pln"><a href="#n73">73</a></p>
<p id="n74" class="pln"><a href="#n74">74</a></p>
<p id="n75" class="pln"><a href="#n75">75</a></p>
<p id="n76" class="pln"><a href="#n76">76</a></p>
<p id="n77" class="pln"><a href="#n77">77</a></p>
<p id="n78" class="pln"><a href="#n78">78</a></p>
<p id="n79" class="stm run hide_run"><a href="#n79">79</a></p>
<p id="n80" class="stm run hide_run"><a href="#n80">80</a></p>
<p id="n81" class="stm run hide_run"><a href="#n81">81</a></p>
<p id="n82" class="stm run hide_run"><a href="#n82">82</a></p>
<p id="n83" class="stm run hide_run"><a href="#n83">83</a></p>
<p id="n84" class="stm run hide_run"><a href="#n84">84</a></p>
<p id="n85" class="pln"><a href="#n85">85</a></p>
<p id="n86" class="stm mis"><a href="#n86">86</a></p>
<p id="n87" class="stm run hide_run"><a href="#n87">87</a></p>
<p id="n88" class="stm run hide_run"><a href="#n88">88</a></p>
<p id="n89" class="stm run hide_run"><a href="#n89">89</a></p>
<p id="n90" class="stm run hide_run"><a href="#n90">90</a></p>
<p id="n91" class="pln"><a href="#n91">91</a></p>
<p id="n92" class="stm run hide_run"><a href="#n92">92</a></p>
<p id="n93" class="pln"><a href="#n93">93</a></p>
<p id="n94" class="pln"><a href="#n94">94</a></p>
<p id="n95" class="stm run hide_run"><a href="#n95">95</a></p>
<p id="n96" class="pln"><a href="#n96">96</a></p>
<p id="n97" class="pln"><a href="#n97">97</a></p>
<p id="n98" class="pln"><a href="#n98">98</a></p>
<p id="n99" class="pln"><a href="#n99">99</a></p>
<p id="n100" class="stm run hide_run"><a href="#n100">100</a></p>
<p id="n101" class="pln"><a href="#n101">101</a></p>
<p id="n102" class="pln"><a href="#n102">102</a></p>
<p id="n103" class="stm run hide_run"><a href="#n103">103</a></p>
<p id="n104" class="stm run hide_run"><a href="#n104">104</a></p>
<p id="n105" class="stm run hide_run"><a href="#n105">105</a></p>
<p id="n106" class="stm run hide_run"><a href="#n106">106</a></p>
<p id="n107" class="stm run hide_run"><a href="#n107">107</a></p>
<p id="n108" class="stm mis"><a href="#n108">108</a></p>
<p id="n109" class="stm mis"><a href="#n109">109</a></p>
<p id="n110" class="pln"><a href="#n110">110</a></p>
<p id="n111" class="stm mis"><a href="#n111">111</a></p>
<p id="n112" class="pln"><a href="#n112">112</a></p>
<p id="n113" class="stm run hide_run"><a href="#n113">113</a></p>
<p id="n114" class="stm run hide_run"><a href="#n114">114</a></p>
<p id="n115" class="stm run hide_run"><a href="#n115">115</a></p>
<p id="n116" class="pln"><a href="#n116">116</a></p>
<p id="n117" class="pln"><a href="#n117">117</a></p>
<p id="n118" class="stm run hide_run"><a href="#n118">118</a></p>
<p id="n119" class="pln"><a href="#n119">119</a></p>
<p id="n120" class="pln"><a href="#n120">120</a></p>
<p id="n121" class="pln"><a href="#n121">121</a></p>
<p id="n122" class="pln"><a href="#n122">122</a></p>
<p id="n123" class="pln"><a href="#n123">123</a></p>
<p id="n124" class="stm run hide_run"><a href="#n124">124</a></p>
<p id="n125" class="stm run hide_run"><a href="#n125">125</a></p>
<p id="n126" class="stm run hide_run"><a href="#n126">126</a></p>
<p id="n127" class="stm run hide_run"><a href="#n127">127</a></p>
<p id="n128" class="stm run hide_run"><a href="#n128">128</a></p>
<p id="n129" class="pln"><a href="#n129">129</a></p>
<p id="n130" class="stm run hide_run"><a href="#n130">130</a></p>
<p id="n131" class="pln"><a href="#n131">131</a></p>
<p id="n132" class="stm run hide_run"><a href="#n132">132</a></p>
<p id="n133" class="pln"><a href="#n133">133</a></p>
<p id="n134" class="stm run hide_run"><a href="#n134">134</a></p>
<p id="n135" class="stm run hide_run"><a href="#n135">135</a></p>
<p id="n136" class="stm mis"><a href="#n136">136</a></p>
<p id="n137" class="stm mis"><a href="#n137">137</a></p>
<p id="n138" class="pln"><a href="#n138">138</a></p>
<p id="n139" class="stm run hide_run"><a href="#n139">139</a></p>
<p id="n140" class="stm run hide_run"><a href="#n140">140</a></p>
<p id="n141" class="stm mis"><a href="#n141">141</a></p>
<p id="n142" class="stm mis"><a href="#n142">142</a></p>
<p id="n143" class="stm mis"><a href="#n143">143</a></p>
<p id="n144" class="stm run hide_run"><a href="#n144">144</a></p>
<p id="n145" class="stm run hide_run"><a href="#n145">145</a></p>
<p id="n146" class="stm mis"><a href="#n146">146</a></p>
<p id="n147" class="stm mis"><a href="#n147">147</a></p>
<p id="n148" class="pln"><a href="#n148">148</a></p>
<p id="n149" class="stm mis"><a href="#n149">149</a></p>
<p id="n150" class="stm run hide_run"><a href="#n150">150</a></p>
<p id="n151" class="stm mis"><a href="#n151">151</a></p>
<p id="n152" class="stm mis"><a href="#n152">152</a></p>
<p id="n153" class="pln"><a href="#n153">153</a></p>
<p id="n154" class="stm mis"><a href="#n154">154</a></p>
<p id="n155" class="pln"><a href="#n155">155</a></p>
<p id="n156" class="stm run hide_run"><a href="#n156">156</a></p>
<p id="n157" class="pln"><a href="#n157">157</a></p>
<p id="n158" class="pln"><a href="#n158">158</a></p>
<p id="n159" class="stm run hide_run"><a href="#n159">159</a></p>
<p id="n160" class="stm mis"><a href="#n160">160</a></p>
<p id="n161" class="stm mis"><a href="#n161">161</a></p>
<p id="n162" class="pln"><a href="#n162">162</a></p>
<p id="n163" class="stm run hide_run"><a href="#n163">163</a></p>
<p id="n164" class="stm run hide_run"><a href="#n164">164</a></p>
<p id="n165" class="pln"><a href="#n165">165</a></p>
<p id="n166" class="pln"><a href="#n166">166</a></p>
<p id="n167" class="stm run hide_run"><a href="#n167">167</a></p>
<p id="n168" class="pln"><a href="#n168">168</a></p>
<p id="n169" class="pln"><a href="#n169">169</a></p>
<p id="n170" class="pln"><a href="#n170">170</a></p>
<p id="n171" class="pln"><a href="#n171">171</a></p>
<p id="n172" class="pln"><a href="#n172">172</a></p>
<p id="n173" class="stm run hide_run"><a href="#n173">173</a></p>
<p id="n174" class="stm run hide_run"><a href="#n174">174</a></p>
<p id="n175" class="pln"><a href="#n175">175</a></p>
<p id="n176" class="pln"><a href="#n176">176</a></p>
<p id="n177" class="stm run hide_run"><a href="#n177">177</a></p>
<p id="n178" class="pln"><a href="#n178">178</a></p>
<p id="n179" class="pln"><a href="#n179">179</a></p>
<p id="n180" class="stm run hide_run"><a href="#n180">180</a></p>
<p id="n181" class="stm run hide_run"><a href="#n181">181</a></p>
<p id="n182" class="stm run hide_run"><a href="#n182">182</a></p>
<p id="n183" class="stm run hide_run"><a href="#n183">183</a></p>
<p id="n184" class="stm run hide_run"><a href="#n184">184</a></p>
<p id="n185" class="stm run hide_run"><a href="#n185">185</a></p>
<p id="n186" class="pln"><a href="#n186">186</a></p>
<p id="n187" class="stm run hide_run"><a href="#n187">187</a></p>
<p id="n188" class="pln"><a href="#n188">188</a></p>
<p id="n189" class="stm run hide_run"><a href="#n189">189</a></p>
<p id="n190" class="stm run hide_run"><a href="#n190">190</a></p>
<p id="n191" class="stm run hide_run"><a href="#n191">191</a></p>
<p id="n192" class="stm run hide_run"><a href="#n192">192</a></p>
<p id="n193" class="pln"><a href="#n193">193</a></p>
<p id="n194" class="stm run hide_run"><a href="#n194">194</a></p>
<p id="n195" class="pln"><a href="#n195">195</a></p>
<p id="n196" class="pln"><a href="#n196">196</a></p>
<p id="n197" class="pln"><a href="#n197">197</a></p>
<p id="n198" class="pln"><a href="#n198">198</a></p>
<p id="n199" class="pln"><a href="#n199">199</a></p>
<p id="n200" class="pln"><a href="#n200">200</a></p>
<p id="n201" class="stm run hide_run"><a href="#n201">201</a></p>
<p id="n202" class="stm run hide_run"><a href="#n202">202</a></p>
<p id="n203" class="stm run hide_run"><a href="#n203">203</a></p>
<p id="n204" class="pln"><a href="#n204">204</a></p>
<p id="n205" class="pln"><a href="#n205">205</a></p>
<p id="n206" class="stm run hide_run"><a href="#n206">206</a></p>
<p id="n207" class="pln"><a href="#n207">207</a></p>
<p id="n208" class="pln"><a href="#n208">208</a></p>
<p id="n209" class="stm run hide_run"><a href="#n209">209</a></p>
<p id="n210" class="stm run hide_run"><a href="#n210">210</a></p>
<p id="n211" class="stm run hide_run"><a href="#n211">211</a></p>
<p id="n212" class="stm mis"><a href="#n212">212</a></p>
<p id="n213" class="stm mis"><a href="#n213">213</a></p>
<p id="n214" class="stm run hide_run"><a href="#n214">214</a></p>
<p id="n215" class="stm run hide_run"><a href="#n215">215</a></p>
<p id="n216" class="pln"><a href="#n216">216</a></p>
<p id="n217" class="pln"><a href="#n217">217</a></p>
<p id="n218" class="pln"><a href="#n218">218</a></p>
<p id="n219" class="pln"><a href="#n219">219</a></p>
<p id="n220" class="pln"><a href="#n220">220</a></p>
<p id="n221" class="pln"><a href="#n221">221</a></p>
<p id="n222" class="pln"><a href="#n222">222</a></p>
<p id="n223" class="stm run hide_run"><a href="#n223">223</a></p>
<p id="n224" class="stm mis"><a href="#n224">224</a></p>
<p id="n225" class="stm mis"><a href="#n225">225</a></p>
<p id="n226" class="pln"><a href="#n226">226</a></p>
<p id="n227" class="stm run hide_run"><a href="#n227">227</a></p>
<p id="n228" class="stm run hide_run"><a href="#n228">228</a></p>
<p id="n229" class="stm run hide_run"><a href="#n229">229</a></p>
<p id="n230" class="stm run hide_run"><a href="#n230">230</a></p>
<p id="n231" class="stm run hide_run"><a href="#n231">231</a></p>
<p id="n232" class="stm run hide_run"><a href="#n232">232</a></p>
<p id="n233" class="stm run hide_run"><a href="#n233">233</a></p>
<p id="n234" class="stm run hide_run"><a href="#n234">234</a></p>
<p id="n235" class="stm run hide_run"><a href="#n235">235</a></p>
<p id="n236" class="pln"><a href="#n236">236</a></p>
<p id="n237" class="stm mis"><a href="#n237">237</a></p>
<p id="n238" class="stm run hide_run"><a href="#n238">238</a></p>
<p id="n239" class="stm run hide_run"><a href="#n239">239</a></p>
<p id="n240" class="stm run hide_run"><a href="#n240">240</a></p>
<p id="n241" class="stm run hide_run"><a href="#n241">241</a></p>
<p id="n242" class="stm run hide_run"><a href="#n242">242</a></p>
<p id="n243" class="stm run hide_run"><a href="#n243">243</a></p>
<p id="n244" class="stm run hide_run"><a href="#n244">244</a></p>

            </td>
            <td class="text">
<p id="t1" class="pln"><span class="com"># -*- coding: utf-8 -*-</span><span class="strut">&nbsp;</span></p>
<p id="t2" class="pln"><span class="com"># @Time    : 2019-08-12 14:10</span><span class="strut">&nbsp;</span></p>
<p id="t3" class="pln"><span class="com"># <AUTHOR> wu</span><span class="strut">&nbsp;</span></p>
<p id="t4" class="pln"><span class="com"># @File    : feature_group_manager.py</span><span class="strut">&nbsp;</span></p>
<p id="t5" class="pln"><span class="com"># @Software: PyCharm</span><span class="strut">&nbsp;</span></p>
<p id="t6" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t7" class="stm run hide_run"><span class="str">"""module explore"""</span><span class="strut">&nbsp;</span></p>
<p id="t8" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t9" class="stm run hide_run"><span class="key">import</span> <span class="nam">datetime</span><span class="strut">&nbsp;</span></p>
<p id="t10" class="stm run hide_run"><span class="key">import</span> <span class="nam">json</span><span class="strut">&nbsp;</span></p>
<p id="t11" class="stm run hide_run"><span class="key">import</span> <span class="nam">time</span><span class="strut">&nbsp;</span></p>
<p id="t12" class="stm run hide_run"><span class="key">import</span> <span class="nam">hashlib</span><span class="strut">&nbsp;</span></p>
<p id="t13" class="stm run hide_run"><span class="key">from</span> <span class="nam">flask</span> <span class="key">import</span> <span class="nam">request</span><span class="strut">&nbsp;</span></p>
<p id="t14" class="stm run hide_run"><span class="key">from</span> <span class="nam">flask_restful</span> <span class="key">import</span> <span class="nam">Resource</span><span class="strut">&nbsp;</span></p>
<p id="t15" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span> <span class="key">import</span> <span class="nam">logger</span><span class="strut">&nbsp;</span></p>
<p id="t16" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">utils</span> <span class="key">import</span> <span class="nam">flask_response</span><span class="strut">&nbsp;</span></p>
<p id="t17" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">database</span> <span class="key">import</span> <span class="nam">MongoDB</span><span class="strut">&nbsp;</span></p>
<p id="t18" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">json_format</span> <span class="key">import</span> <span class="nam">JSONEncoder</span><span class="strut">&nbsp;</span></p>
<p id="t19" class="stm run hide_run"><span class="key">from</span> <span class="nam">utils</span><span class="op">.</span><span class="nam">param_check</span> <span class="key">import</span> <span class="nam">Validator</span><span class="op">,</span> <span class="nam">check_flask_args</span><span class="strut">&nbsp;</span></p>
<p id="t20" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t21" class="stm run hide_run"><span class="nam">LOG</span> <span class="op">=</span> <span class="nam">logger</span><span class="op">.</span><span class="nam">Logger</span><span class="op">(</span><span class="str">"debug"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t22" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t23" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t24" class="stm run hide_run"><span class="key">def</span> <span class="nam">create_group_id</span><span class="op">(</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t25" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t26" class="pln"><span class="str">    :return: group id</span><span class="strut">&nbsp;</span></p>
<p id="t27" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t28" class="stm run hide_run">    <span class="nam">md5_str</span> <span class="op">=</span> <span class="nam">hashlib</span><span class="op">.</span><span class="nam">md5</span><span class="op">(</span><span class="nam">str</span><span class="op">(</span><span class="nam">time</span><span class="op">.</span><span class="nam">clock</span><span class="op">(</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">encode</span><span class="op">(</span><span class="str">'utf-8'</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t29" class="stm run hide_run">    <span class="key">return</span> <span class="nam">md5_str</span><span class="op">.</span><span class="nam">hexdigest</span><span class="op">(</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t30" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t31" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t32" class="pln"><span class="com"># parameter check</span><span class="strut">&nbsp;</span></p>
<p id="t33" class="stm run hide_run"><span class="key">def</span> <span class="nam">param_check</span><span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">sid_list</span><span class="op">,</span> <span class="nam">mongodb</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t34" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t35" class="pln"><span class="str">    :param name:      &#29305;&#24449;&#32452;&#21517;&#31216;</span><span class="strut">&nbsp;</span></p>
<p id="t36" class="pln"><span class="str">    :param sid_list:  &#29305;&#24449;&#21015;&#34920;</span><span class="strut">&nbsp;</span></p>
<p id="t37" class="pln"><span class="str">    :param mongodb:   mongodb</span><span class="strut">&nbsp;</span></p>
<p id="t38" class="pln"><span class="str">    :return:          &#27491;&#30830;&#36820;&#22238;&#31354;&#65292;&#21542;&#21017;&#36820;&#22238;&#38169;&#35823;&#25552;&#31034;&#20449;&#24687;</span><span class="strut">&nbsp;</span></p>
<p id="t39" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t40" class="stm run hide_run">    <span class="nam">rst</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t41" class="stm run hide_run">    <span class="key">if</span> <span class="nam">name</span> <span class="op">==</span> <span class="str">""</span> <span class="key">or</span> <span class="nam">name</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t42" class="stm mis">        <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">'The group name cannot be empty'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t43" class="stm run hide_run">    <span class="key">elif</span> <span class="nam">len</span><span class="op">(</span><span class="nam">name</span><span class="op">)</span> <span class="op">></span> <span class="num">128</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t44" class="stm mis">        <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">'Group name cannot exceed 128 characters in length'</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t45" class="stm run hide_run">    <span class="key">if</span> <span class="nam">sid_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t46" class="stm run hide_run">        <span class="nam">tmp</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t47" class="stm run hide_run">        <span class="key">for</span> <span class="nam">sid</span> <span class="key">in</span> <span class="nam">sid_list</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t48" class="stm run hide_run">            <span class="nam">item</span> <span class="op">=</span> <span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'rules'</span><span class="op">,</span> <span class="op">{</span><span class="str">'sid'</span><span class="op">:</span> <span class="nam">sid</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t49" class="stm run hide_run">            <span class="key">if</span> <span class="nam">item</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t50" class="stm run hide_run">                <span class="nam">tmp</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">sid</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t51" class="stm run hide_run">        <span class="key">if</span> <span class="nam">tmp</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t52" class="stm run hide_run">            <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"Sid "</span> <span class="op">+</span> <span class="nam">str</span><span class="op">(</span><span class="nam">tmp</span><span class="op">)</span> <span class="op">+</span> <span class="str">" is not exist"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t53" class="pln">    <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t54" class="stm mis">        <span class="nam">rst</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"Sid cannot be empty"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t55" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t56" class="stm run hide_run">    <span class="key">return</span> <span class="nam">rst</span><span class="strut">&nbsp;</span></p>
<p id="t57" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t58" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t59" class="stm run hide_run"><span class="key">class</span> <span class="nam">FeatureGroupWithId</span><span class="op">(</span><span class="nam">Resource</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t60" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t61" class="pln"><span class="str">        &#21333;&#20010;&#29305;&#24449;&#32452;&#30340;&#33719;&#21462;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t62" class="pln"><span class="str">        &#21333;&#20010;&#29305;&#24449;&#32452;&#21024;&#38500;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t63" class="pln"><span class="str">        &#21333;&#20010;&#29305;&#24449;&#32452;&#20462;&#25913;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t64" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t65" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t66" class="stm run hide_run">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t67" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span> <span class="op">=</span> <span class="nam">MongoDB</span><span class="op">(</span><span class="str">"ndr"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t68" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t69" class="pln">    <span class="com"># get one feature group</span><span class="strut">&nbsp;</span></p>
<p id="t70" class="stm run hide_run">    <span class="key">def</span> <span class="nam">get</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">group_id</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t71" class="pln">        <span class="str">"""Get one feature group"""</span><span class="strut">&nbsp;</span></p>
<p id="t72" class="stm run hide_run">        <span class="nam">rst</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupId"</span><span class="op">:</span> <span class="nam">group_id</span><span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t73" class="pln">                                    <span class="op">{</span><span class="str">"_id"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t74" class="pln">                                     <span class="str">"groupName"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t75" class="pln">                                     <span class="str">"groupId"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t76" class="pln">                                     <span class="str">"createTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t77" class="pln">                                     <span class="str">"sidList"</span><span class="op">:</span> <span class="num">1</span><span class="strut">&nbsp;</span></p>
<p id="t78" class="pln">                                     <span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t79" class="stm run hide_run">        <span class="key">if</span> <span class="nam">rst</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t80" class="stm run hide_run">            <span class="nam">rst</span><span class="op">[</span><span class="str">'sidStatus'</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t81" class="stm run hide_run">            <span class="key">for</span> <span class="nam">sid</span> <span class="key">in</span> <span class="nam">rst</span><span class="op">[</span><span class="str">'sidList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t82" class="stm run hide_run">                <span class="nam">item</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'rules'</span><span class="op">,</span> <span class="op">{</span><span class="str">'sid'</span><span class="op">:</span> <span class="nam">sid</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t83" class="stm run hide_run">                <span class="key">if</span> <span class="nam">item</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t84" class="stm run hide_run">                    <span class="nam">rst</span><span class="op">[</span><span class="str">'sidStatus'</span><span class="op">]</span><span class="op">[</span><span class="nam">sid</span><span class="op">]</span> <span class="op">=</span> <span class="str">"valid"</span><span class="strut">&nbsp;</span></p>
<p id="t85" class="pln">                <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t86" class="stm mis">                    <span class="nam">rst</span><span class="op">[</span><span class="str">'sidStatus'</span><span class="op">]</span><span class="op">[</span><span class="nam">sid</span><span class="op">]</span> <span class="op">=</span> <span class="str">"valid"</span><span class="strut">&nbsp;</span></p>
<p id="t87" class="stm run hide_run">            <span class="key">del</span> <span class="nam">rst</span><span class="op">[</span><span class="str">"sidList"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t88" class="stm run hide_run">            <span class="nam">col_fmt</span> <span class="op">=</span> <span class="nam">JSONEncoder</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">encode</span><span class="op">(</span><span class="nam">rst</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t89" class="stm run hide_run">            <span class="nam">col_rst</span> <span class="op">=</span> <span class="nam">json</span><span class="op">.</span><span class="nam">loads</span><span class="op">(</span><span class="nam">col_fmt</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t90" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="op">{</span><span class="op">}</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="nam">col_rst</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t91" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t92" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">"Not found"</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t93" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t94" class="pln">    <span class="com"># delete group</span><span class="strut">&nbsp;</span></p>
<p id="t95" class="stm run hide_run">    <span class="key">def</span> <span class="nam">delete</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">group_id</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t96" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t97" class="pln"><span class="str">        :param group_id:</span><span class="strut">&nbsp;</span></p>
<p id="t98" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t99" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t100" class="stm run hide_run">        <span class="nam">group</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">"feature_group"</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupId"</span><span class="op">:</span> <span class="nam">group_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t101" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t102" class="pln">        <span class="com"># delete process</span><span class="strut">&nbsp;</span></p>
<p id="t103" class="stm run hide_run">        <span class="key">if</span> <span class="nam">group</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t104" class="stm run hide_run">            <span class="nam">message</span> <span class="op">=</span> <span class="str">'The feature group does not exists'</span><span class="strut">&nbsp;</span></p>
<p id="t105" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: %s delete failed.%s"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">group_id</span><span class="op">,</span> <span class="nam">message</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t106" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t107" class="stm run hide_run">        <span class="key">if</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t108" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">"The group is used by explore task %s"</span> <span class="op">%</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t109" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: %s delete failed.Reason: used by explore task [ %s ]"</span><span class="strut">&nbsp;</span></p>
<p id="t110" class="pln">                      <span class="op">%</span> <span class="op">(</span><span class="nam">group_id</span><span class="op">,</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t111" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t112" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t113" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">delete</span><span class="op">(</span><span class="str">"feature_group"</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupId"</span><span class="op">:</span> <span class="nam">group_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t114" class="stm run hide_run">        <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"group: %s, %s deleted success."</span> <span class="op">%</span> <span class="op">(</span><span class="nam">group</span><span class="op">[</span><span class="str">"groupName"</span><span class="op">]</span><span class="op">,</span> <span class="nam">group</span><span class="op">[</span><span class="str">"groupId"</span><span class="op">]</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t115" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t116" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t117" class="pln">    <span class="com"># Modify group</span><span class="strut">&nbsp;</span></p>
<p id="t118" class="stm run hide_run">    <span class="op">@</span><span class="nam">check_flask_args</span><span class="op">(</span><span class="nam">Validator</span><span class="op">(</span><span class="str">"feature_group_update_schema"</span><span class="op">)</span><span class="op">,</span> <span class="nam">request</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t119" class="pln">    <span class="key">def</span> <span class="nam">put</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">group_id</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t120" class="pln">        <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t121" class="pln"><span class="str">        :param group_id:</span><span class="strut">&nbsp;</span></p>
<p id="t122" class="pln"><span class="str">        :return:</span><span class="strut">&nbsp;</span></p>
<p id="t123" class="pln"><span class="str">        """</span><span class="strut">&nbsp;</span></p>
<p id="t124" class="stm run hide_run">        <span class="nam">old_group</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupId"</span><span class="op">:</span> <span class="nam">group_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t125" class="stm run hide_run">        <span class="key">if</span> <span class="nam">old_group</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t126" class="stm run hide_run">            <span class="nam">message</span> <span class="op">=</span> <span class="str">"The group does not exist."</span><span class="strut">&nbsp;</span></p>
<p id="t127" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: %s update failed.%s"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">group_id</span><span class="op">,</span> <span class="nam">message</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t128" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t129" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t130" class="stm run hide_run">        <span class="nam">grp_name</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"name"</span><span class="op">]</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t131" class="pln">            <span class="key">if</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"name"</span><span class="op">]</span> <span class="key">else</span> <span class="nam">old_group</span><span class="op">[</span><span class="str">'groupName'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t132" class="stm run hide_run">        <span class="nam">sid_list</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"sidList"</span><span class="op">]</span> <span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t133" class="pln">            <span class="key">if</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"sidList"</span><span class="op">]</span> <span class="key">else</span> <span class="nam">old_group</span><span class="op">[</span><span class="str">'sidList'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t134" class="stm run hide_run">        <span class="nam">rst</span> <span class="op">=</span> <span class="nam">param_check</span><span class="op">(</span><span class="nam">grp_name</span><span class="op">,</span> <span class="nam">sid_list</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t135" class="stm run hide_run">        <span class="key">if</span> <span class="nam">rst</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t136" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: %s update failed.Reason: %s"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">group_id</span><span class="op">,</span> <span class="nam">rst</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t137" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">rst</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t138" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t139" class="stm run hide_run">        <span class="nam">group</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupId"</span><span class="op">:</span> <span class="nam">group_id</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t140" class="stm run hide_run">        <span class="key">if</span> <span class="nam">group</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t141" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">"The group does not exist."</span><span class="strut">&nbsp;</span></p>
<p id="t142" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: %s update failed.Reason: %s does not exist."</span> <span class="op">%</span> <span class="nam">grp_name</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t143" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t144" class="stm run hide_run">        <span class="nam">grp</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupName"</span><span class="op">:</span> <span class="nam">grp_name</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t145" class="stm run hide_run">        <span class="key">if</span> <span class="nam">grp</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span> <span class="key">and</span> <span class="nam">grp</span><span class="op">[</span><span class="str">"groupId"</span><span class="op">]</span> <span class="op">==</span> <span class="nam">group_id</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t146" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">"The same name group [%s] already exists."</span> <span class="op">%</span> <span class="nam">grp_name</span><span class="strut">&nbsp;</span></p>
<p id="t147" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: Update failed.Reason:"</span><span class="strut">&nbsp;</span></p>
<p id="t148" class="pln">                      <span class="str">" The same name group [%s] already exists."</span> <span class="op">%</span> <span class="nam">grp_name</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t149" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t150" class="stm run hide_run">        <span class="key">if</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t151" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">"The group is used by explore task %s"</span> <span class="op">%</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t152" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: %s update failed.Reason: used by explore task [ %s ]"</span><span class="strut">&nbsp;</span></p>
<p id="t153" class="pln">                      <span class="op">%</span> <span class="op">(</span><span class="nam">grp_name</span><span class="op">,</span> <span class="nam">group</span><span class="op">[</span><span class="str">'taskList'</span><span class="op">]</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t154" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t155" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t156" class="stm run hide_run">        <span class="nam">ret</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupId"</span><span class="op">:</span> <span class="nam">group_id</span><span class="op">}</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t157" class="pln">                                  <span class="op">{</span><span class="str">'groupName'</span><span class="op">:</span> <span class="nam">grp_name</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t158" class="pln">                                   <span class="str">'sidList'</span><span class="op">:</span> <span class="nam">sid_list</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t159" class="stm run hide_run">        <span class="key">if</span> <span class="nam">ret</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t160" class="stm mis">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: %s update failed.Reason: %s."</span> <span class="op">%</span> <span class="op">(</span><span class="nam">grp_name</span><span class="op">,</span> <span class="nam">ret</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t161" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">ret</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t162" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t163" class="stm run hide_run">        <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"group: %s update success."</span> <span class="op">%</span> <span class="nam">group_id</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t164" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t165" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t166" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t167" class="stm run hide_run"><span class="key">class</span> <span class="nam">FeatureGroup</span><span class="op">(</span><span class="nam">Resource</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t168" class="pln">    <span class="str">"""</span><span class="strut">&nbsp;</span></p>
<p id="t169" class="pln"><span class="str">        &#21019;&#24314;&#29305;&#24449;&#32452;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t170" class="pln"><span class="str">        &#25353;&#20998;&#39029;&#33719;&#21462;&#29305;&#24449;&#32452;&#65307;</span><span class="strut">&nbsp;</span></p>
<p id="t171" class="pln"><span class="str">    """</span><span class="strut">&nbsp;</span></p>
<p id="t172" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t173" class="stm run hide_run">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t174" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span> <span class="op">=</span> <span class="nam">MongoDB</span><span class="op">(</span><span class="str">"ndr"</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t175" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t176" class="pln">    <span class="com"># create feature group</span><span class="strut">&nbsp;</span></p>
<p id="t177" class="stm run hide_run">    <span class="op">@</span><span class="nam">check_flask_args</span><span class="op">(</span><span class="nam">Validator</span><span class="op">(</span><span class="str">"feature_group_create_schema"</span><span class="op">)</span><span class="op">,</span> <span class="nam">request</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t178" class="pln">    <span class="key">def</span> <span class="nam">post</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t179" class="pln">        <span class="str">"""Post method"""</span><span class="strut">&nbsp;</span></p>
<p id="t180" class="stm run hide_run">        <span class="nam">name</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"name"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t181" class="stm run hide_run">        <span class="nam">group</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="str">"groupName"</span><span class="op">:</span> <span class="nam">name</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t182" class="stm run hide_run">        <span class="key">if</span> <span class="nam">group</span> <span class="key">is</span> <span class="key">not</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t183" class="stm run hide_run">            <span class="nam">message</span> <span class="op">=</span> <span class="str">'The feature group [%s] already exists.'</span> <span class="op">%</span> <span class="nam">name</span><span class="strut">&nbsp;</span></p>
<p id="t184" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: created failed.Reason: %s"</span> <span class="op">%</span> <span class="nam">message</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t185" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t186" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t187" class="stm run hide_run">        <span class="nam">sid_list</span> <span class="op">=</span> <span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"sidList"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t188" class="pln">        <span class="com"># &#21442;&#25968;&#26816;&#26597;</span><span class="strut">&nbsp;</span></p>
<p id="t189" class="stm run hide_run">        <span class="nam">rst</span> <span class="op">=</span> <span class="nam">param_check</span><span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">sid_list</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t190" class="stm run hide_run">        <span class="key">if</span> <span class="nam">rst</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t191" class="stm run hide_run">            <span class="nam">LOG</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"group: [%s] created failed.Reason: %s"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">rst</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t192" class="stm run hide_run">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">rst</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t193" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t194" class="stm run hide_run">        <span class="nam">body</span> <span class="op">=</span> <span class="op">{</span><span class="strut">&nbsp;</span></p>
<p id="t195" class="pln">            <span class="str">"groupName"</span><span class="op">:</span> <span class="nam">name</span><span class="op">,</span>  <span class="com"># &#29305;&#24449;&#32452;&#21517;&#31216;</span><span class="strut">&nbsp;</span></p>
<p id="t196" class="pln">            <span class="str">"groupId"</span><span class="op">:</span> <span class="nam">create_group_id</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>  <span class="com"># &#29305;&#24449;&#32452; ID</span><span class="strut">&nbsp;</span></p>
<p id="t197" class="pln">            <span class="str">"taskList"</span><span class="op">:</span> <span class="op">[</span><span class="op">]</span><span class="op">,</span>  <span class="com"># &#24341;&#29992;&#35813;&#29305;&#24449;&#32452;&#30340;&#25506;&#32034;&#20219;&#21153;ID&#21015;&#34920;</span><span class="strut">&nbsp;</span></p>
<p id="t198" class="pln">            <span class="str">"createTime"</span><span class="op">:</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">strftime</span><span class="op">(</span><span class="str">'%Y-%m-%d %H:%M:%S'</span><span class="op">)</span><span class="op">,</span>  <span class="com"># &#21019;&#24314;&#26102;&#38388;</span><span class="strut">&nbsp;</span></p>
<p id="t199" class="pln">            <span class="str">"sidList"</span><span class="op">:</span> <span class="nam">sid_list</span><span class="op">,</span>  <span class="com"># &#35268;&#21017;&#21015;&#34920;</span><span class="strut">&nbsp;</span></p>
<p id="t200" class="pln">        <span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t201" class="stm run hide_run">        <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">insert_one</span><span class="op">(</span><span class="str">"feature_group"</span><span class="op">,</span> <span class="nam">body</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t202" class="stm run hide_run">        <span class="nam">LOG</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"group: %s, %s created success"</span> <span class="op">%</span> <span class="op">(</span><span class="nam">body</span><span class="op">[</span><span class="str">"groupName"</span><span class="op">]</span><span class="op">,</span> <span class="nam">body</span><span class="op">[</span><span class="str">"groupId"</span><span class="op">]</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t203" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="op">{</span><span class="str">'groupId'</span><span class="op">:</span> <span class="nam">body</span><span class="op">[</span><span class="str">"groupId"</span><span class="op">]</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t204" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t205" class="pln">    <span class="com"># get all groups &#25353;&#20998;&#39029;&#33719;&#21462;&#29305;&#24449;&#32452;</span><span class="strut">&nbsp;</span></p>
<p id="t206" class="stm run hide_run">    <span class="op">@</span><span class="nam">check_flask_args</span><span class="op">(</span><span class="nam">Validator</span><span class="op">(</span><span class="str">"feature_group_get_all_schema"</span><span class="op">)</span><span class="op">,</span> <span class="nam">request</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t207" class="pln">    <span class="key">def</span> <span class="nam">get</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="op">**</span><span class="nam">kwargs</span><span class="op">)</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t208" class="pln">        <span class="str">"""Get method"""</span><span class="strut">&nbsp;</span></p>
<p id="t209" class="stm run hide_run">        <span class="nam">page</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"page"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t210" class="stm run hide_run">        <span class="nam">page_size</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">kwargs</span><span class="op">[</span><span class="str">"args"</span><span class="op">]</span><span class="op">[</span><span class="str">"pageSize"</span><span class="op">]</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t211" class="stm run hide_run">        <span class="key">if</span> <span class="nam">page</span> <span class="op">&lt;=</span> <span class="num">0</span> <span class="key">or</span> <span class="nam">page_size</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t212" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">'Parameter page or pagesize error'</span><span class="strut">&nbsp;</span></p>
<p id="t213" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t214" class="stm run hide_run">        <span class="nam">t_count</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="op">.</span><span class="nam">count</span><span class="op">(</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t215" class="stm run hide_run">        <span class="nam">rst</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find</span><span class="op">(</span><span class="str">'feature_group'</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">,</span> <span class="op">{</span><span class="str">"_id"</span><span class="op">:</span> <span class="num">0</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t216" class="pln">                                                      <span class="str">"groupName"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t217" class="pln">                                                      <span class="str">"groupId"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t218" class="pln">                                                      <span class="str">"createTime"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t219" class="pln">                                                      <span class="str">"sidList"</span><span class="op">:</span> <span class="num">1</span><span class="op">,</span><span class="strut">&nbsp;</span></p>
<p id="t220" class="pln">                                                      <span class="op">}</span><span class="op">)</span><span class="op">.</span><span class="xx">\</span><span class="strut">&nbsp;</span></p>
<p id="t221" class="pln">            <span class="nam">limit</span><span class="op">(</span><span class="nam">page_size</span><span class="op">)</span><span class="op">.</span><span class="nam">skip</span><span class="op">(</span><span class="op">(</span><span class="nam">page</span> <span class="op">-</span> <span class="num">1</span><span class="op">)</span> <span class="op">*</span> <span class="nam">page_size</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t222" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t223" class="stm run hide_run">        <span class="key">if</span> <span class="nam">rst</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t224" class="stm mis">            <span class="nam">message</span> <span class="op">=</span> <span class="str">'Not found'</span><span class="strut">&nbsp;</span></p>
<p id="t225" class="stm mis">            <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="nam">message</span><span class="op">,</span> <span class="key">False</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t226" class="pln"><span class="strut">&nbsp;</span></p>
<p id="t227" class="stm run hide_run">        <span class="nam">cols</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t228" class="stm run hide_run">        <span class="nam">data</span> <span class="op">=</span> <span class="op">{</span><span class="str">"count"</span><span class="op">:</span> <span class="nam">t_count</span><span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t229" class="stm run hide_run">        <span class="nam">print</span><span class="op">(</span><span class="nam">rst</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t230" class="stm run hide_run">        <span class="key">for</span> <span class="nam">col</span> <span class="key">in</span> <span class="nam">rst</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t231" class="stm run hide_run">            <span class="nam">col</span><span class="op">[</span><span class="str">'sidStatus'</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span><span class="strut">&nbsp;</span></p>
<p id="t232" class="stm run hide_run">            <span class="key">for</span> <span class="nam">sid</span> <span class="key">in</span> <span class="nam">col</span><span class="op">[</span><span class="str">'sidList'</span><span class="op">]</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t233" class="stm run hide_run">                <span class="nam">item</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">mongodb</span><span class="op">.</span><span class="nam">find_one</span><span class="op">(</span><span class="str">'rules'</span><span class="op">,</span> <span class="op">{</span><span class="str">'sid'</span><span class="op">:</span> <span class="nam">sid</span><span class="op">}</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t234" class="stm run hide_run">                <span class="key">if</span> <span class="nam">item</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t235" class="stm run hide_run">                    <span class="nam">col</span><span class="op">[</span><span class="str">'sidStatus'</span><span class="op">]</span><span class="op">[</span><span class="nam">sid</span><span class="op">]</span> <span class="op">=</span> <span class="str">"valid"</span><span class="strut">&nbsp;</span></p>
<p id="t236" class="pln">                <span class="key">else</span><span class="op">:</span><span class="strut">&nbsp;</span></p>
<p id="t237" class="stm mis">                    <span class="nam">col</span><span class="op">[</span><span class="str">'sidStatus'</span><span class="op">]</span><span class="op">[</span><span class="nam">sid</span><span class="op">]</span> <span class="op">=</span> <span class="str">"invalid"</span><span class="strut">&nbsp;</span></p>
<p id="t238" class="stm run hide_run">            <span class="key">del</span> <span class="nam">col</span><span class="op">[</span><span class="str">"sidList"</span><span class="op">]</span><span class="strut">&nbsp;</span></p>
<p id="t239" class="stm run hide_run">            <span class="nam">col</span> <span class="op">=</span> <span class="nam">JSONEncoder</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">encode</span><span class="op">(</span><span class="nam">col</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t240" class="stm run hide_run">            <span class="nam">cols</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">json</span><span class="op">.</span><span class="nam">loads</span><span class="op">(</span><span class="nam">col</span><span class="op">)</span><span class="op">)</span><span class="strut">&nbsp;</span></p>
<p id="t241" class="stm run hide_run">        <span class="nam">data</span><span class="op">[</span><span class="str">"detail"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">cols</span><span class="strut">&nbsp;</span></p>
<p id="t242" class="stm run hide_run">        <span class="nam">data</span><span class="op">[</span><span class="str">"page"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">page</span><span class="strut">&nbsp;</span></p>
<p id="t243" class="stm run hide_run">        <span class="nam">data</span><span class="op">[</span><span class="str">"pagesize"</span><span class="op">]</span> <span class="op">=</span> <span class="nam">page_size</span><span class="strut">&nbsp;</span></p>
<p id="t244" class="stm run hide_run">        <span class="key">return</span> <span class="nam">flask_response</span><span class="op">(</span><span class="str">""</span><span class="op">,</span> <span class="key">True</span><span class="op">,</span> <span class="nam">data</span><span class="op">)</span><span class="strut">&nbsp;</span></p>

            </td>
        </tr>
    </table>
</div>

<div id="footer">
    <div class="content">
        <p>
            <a class="nav" href="index.html">&#xab; index</a> &nbsp; &nbsp; <a class="nav" href="https://coverage.readthedocs.io">coverage.py v4.5.4</a>,
            created at 2019-09-10 13:53
        </p>
    </div>
</div>

</body>
</html>
