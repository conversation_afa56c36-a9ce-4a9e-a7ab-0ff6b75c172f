/*
 * @Author: tianh
 * @Date: 2021-11-11 16:33:43
 * @LastEditors: tianh
 * @LastEditTime: 2022-03-24 10:40:46
 * @Descripttion:
 */
import { Card } from 'antd';
import React, { useEffect, useState } from 'react';
import White from '../../white/index';
import Strategy from '../strategy';
import { getTaskData } from '@/services/configs';


const Index = () => {
  const [isFullTrafficVersion, setIsFullTrafficVersion] = useState(false);
  useEffect(() => {
    getTaskData().then((res: any) => {
      if (!res.flag) return;
      const seriesNumber = res?.data?.detail?.divice?.slice(1);
      setIsFullTrafficVersion(seriesNumber != '5000' && seriesNumber != '8000');
    });
  }, [])

  return (
    <div>
      <White />
      {isFullTrafficVersion && <div style={{ marginTop: 40 }}>
        <Card
          title="全流量留存白名单"
          bordered
          style={{ width: '100%' }}
        >
          <Strategy />
        </Card>
      </div>}
    </div>
  );
};

export default Index;
