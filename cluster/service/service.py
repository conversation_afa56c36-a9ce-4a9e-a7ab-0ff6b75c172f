#!/usr/bin/env python3
# coding:utf-8
import rpyc
import subprocess

from ksp import config
from cluster.settings import *
from cluster.utils.logger import get_logger
from cluster.utils.file import *
from cluster.utils.upgrade import Upgrade
from cluster.utils import license, systoolkit
from .utils import get_boot_time


logger = get_logger(__name__)

class KSPService(rpyc.Service):

    allow_set_attrs = ["node_id", "is_passive", "node_status", "mgmt_node_ip", "node_role_exact"]
    system_tool = systoolkit.get_system_tool()

    def __init__(self):
        pass

    ### TODO: 身份验证可以放在 on_connect 中处理
    def on_connect(self, conn):
        self.role = get_json_file(CLUSTER_CFG, 'node_role', 'single')
        self._conn = conn

    def exposed_set_mgmt_node_ip(self, mgmt_node_ip):
        update_json_file(CLUSTER_CFG, 'mgmt_node_ip', mgmt_node_ip)
        node_ip = get_json_file(CLUSTER_CFG, 'node_ip', '')
        if mgmt_node_ip == node_ip:
            update_json_file(CLUSTER_CFG, 'node_role', 'master')
        else:
            update_json_file(CLUSTER_CFG, 'node_role', 'slave')

    def exposed_set_node(self, key, value):
        """
        node_role: single | (master | slave)
        node_role_exact: single | (mgmt | engine | data ...)
        """
        if key not in self.allow_set_attrs:
            return

        update_json_file(CLUSTER_CFG, key, value)
        return

    def exposed_set_node_info(self, node_info):
        content = read_from_json_file(CLUSTER_CFG)
        content['node_name'] = node_info.get('node_name', content.get('node_name', ''))
        content['node_ip'] = node_info.get('node_ip', content.get('node_ip', ''))
        content['node_role'] = node_info.get('node_role', content.get('node_role', ''))
        content['node_status'] = node_info.get('node_status', content.get('node_status', ''))
        content['mgmt_node_ip'] = node_info.get('mgmt_node_ip', content.get('mgmt_node_ip', ''))
        write_to_json_file(CLUSTER_CFG, content)

    def exposed_get_node_info(self):
        content = read_from_json_file(CLUSTER_CFG)
        node_info = {
            'node_ip': content.get('node_ip', ''),
            'node_name': content.get('node_name', ''),
            'node_role': content.get('node_role', ''),
            'mgmt_node_ip': content.get('mgmt_node_ip', ''),
            'device_id': license.get_device_id(),
            'node_status': content.get('node_status', ''),
            'boot_time': get_boot_time(),
            'node_work_ip': self.exposed_get_node_work_ip(),
            'system_version': self.exposed_get_version(),
            'data_version': self.exposed_get_version('data'),
        }
        return json.dumps(node_info)

    def exposed_set_node_role(self, role):
        if role not in ['master', 'slave', 'single']:
            return
        update_json_file(CLUSTER_CFG, 'node_role', role)
        if role == 'single':
            update_json_file(CLUSTER_CFG, 'mgmt_node_ip', '')
        self.role = role

    def exposed_set_cluster_node_list(self, node_ip, node_list, hook_script_path=CLUSTER_HOOK_SCRIPT, **script_args):
        """
        script_args {'params': [...], ...}，params是传递给hook_script的参数
        """
        formated_node_list = []
        for node in node_list:
            formated_node_list.append(node)
        update_json_file(CLUSTER_CFG, 'node_list', formated_node_list)
        update_json_file(CLUSTER_CFG, 'node_ip', node_ip)

        try:
            if hook_script_path:
                logger.info(f"Executing cluster hook script={hook_script_path}, role={self.role}!")
                params = script_args.get('params', [])
                subprocess.Popen("sudo bash +x %s %s %s" % (hook_script_path, self.role, ' '.join(params)), shell=True).communicate()
            else:
                logger.info("Skipping cluster hook script!")
        except Exception as e:
            logger.error("Call cluster hook script error %s" % str(e))
        return True

    def exposed_get_node(self, name):
        if name == "node_role":
            return get_json_file(CLUSTER_CFG, name, 'single')
        return get_json_file(CLUSTER_CFG, name)

    def exposed_get_node_work_ip(self):
        ifmap_config = systoolkit.get_ifmap_config()
        network_tool = systoolkit.get_network_tool()
        try:
            node_work_ip = ''
            node_work_ifaces = ifmap_config.get('work').split(',')
            for iface in node_work_ifaces:
                _, node_work_info, _ = network_tool.get_address(iface)
                if node_work_info:
                    node_work_ip = node_work_info.get(iface).get('addr')
                    break
            return node_work_ip
        except:
            return ''

    def exposed_get_node_stats(self):
        device_info, network_info_list = self.system_tool.get_sysstat_info()
        boot_time = self.system_tool.get_boot_time()
        device_id = license.get_device_id()
        logger.info("exposed_get_node_stats")
        stats = {
            'device_stats': device_info,
            'network_stats_list': network_info_list,
            'boot_time': boot_time,
            'device_id': device_id,
        }
        return json.dumps(stats)

    def exposed_get_version(self, version_type='default'):
        if version_type == 'data':
            return config.get("prod.data_version")
        else:
            return config.get("prod.version")

    def exposed_get_device_id(self):
        return license.get_device_id()

    # FIXME Need to decouple with ksp
    def exposed_restart_sched(self):
        restart_cmd = ['sudo', 'systemctl', 'restart', 'zoomeyebe-*.service', 'systemd-timesyncd.service']
        stop_cmd = ['sudo', 'systemctl', 'stop', 'ksp-update-timer.timer']
        disable_cmd = ['sudo', 'systemctl', 'disable', 'ksp-update-timer.timer']
        try:
            subprocess.Popen(restart_cmd)
        except Exception as e:
            logger.error(e)
        if self.role == 'slave':
            try:
                subprocess.Popen(stop_cmd)
                subprocess.Popen(disable_cmd)
            except Exception as e:
                logger.error(e)

    def exposed_start_mcp_task(self):
        return self.mcp.start_task()

    def exposed_stop_mcp_task(self):
        return self.mcp.stop_task()

    def exposed_get_mcp_task_status(self):
        return self.mcp.task_status()

    def exposed_bind_nic(self, bus_info, **kwargs):
        return self.mcp.bind_nic(bus_info, **kwargs)

    def exposed_unbind_nic(self, bus_info, **kwargs):
        return self.mcp.unbind_nic(bus_info, **kwargs)

    def exposed_nic_status(self):
        return self.mcp.nic_status()

    def exposed_add_instance(self, name, **kwargs):
        return self.mcp.add_instance(name, **kwargs)

    def exposed_del_instance(self, name):
        return self.mcp.del_instance(name)

    def exposed_list_instance(self):
        return self.mcp.list_instance()

    def exposed_ip_stats(self):
        return self.mcp.ip_stats()

    def exposed_list_devices(self):
        return self.console.list_devices()

    def exposed_bind_device(self, dev):
        return self.console.bind_device(dev)

    def exposed_unbind_device(self, dev):
        return self.console.unbind_device(dev)

    def exposed_stop_node(self):
        try:
            self.system_tool.shutdown()
        except Exception as e:
            logger.error(e)

    def exposed_restart_node(self):
        try:
            self.system_tool.reboot()
        except Exception as e:
            logger.error(e)

    # 该API废弃，请不要再使用。
    def exposed_update_sync(self, content):
        """同步更新清单&&启动更新程序"""
        upgrade = Upgrade()
        sync_file_res = upgrade.sync_update_file(content)
        if not sync_file_res:
            logger.info('sync update file(%s) failed', upgrade.sync_file)
            return False
        update_res = upgrade.launch_update()
        if not update_res:
            logger.info('launch update failed')
            return False
        logger.info('sync file(%s) and launch update successfully', upgrade.sync_file)
        return True

    def exposed_get_boot_time(self):
        try:
            return self.system_tool.get_boot_time()
        except Exception as e:
            logger.error(e)

    def exposed_remove_file(self, file_path):
        if self.role != 'slave':
            return
        if not is_safe_path(file_path):
            return False
        try:
            os.remove(file_path)
        except:
            logger.error('Failed to remove file %s' % file_path)
            return False
        logger.info('remove file(%s) successfully' % file_path)
        return True

    def exposed_sync_file(self, content, dst_file_path):
        if self.role != 'slave':
            return
        if not is_safe_path(dst_file_path):
            return False
        try:
            if not os.path.exists(os.path.dirname(dst_file_path)):
                os.makedirs(os.path.dirname(dst_file_path))

            sync_file = open(dst_file_path, 'wb')
            sync_file.write(content)
            sync_file.close()
        except:
            logger.error('Failed to write file %s' % (dst_file_path))
            return False
        logger.info('sync file(%s) successfully' % dst_file_path)
        return True

    def exposed_read_file(self, src_file_path):
        if not is_safe_path(src_file_path):
            return False
        try:
            sync_file = open(src_file_path, 'rb')
            content = sync_file.read()
            sync_file.close()
            return content
        except Exception as ex:
            logger.error('Failed to read file %s %s' % (src_file_path, str(ex)))
            return None

    def exposetd_get_file_md5sum(self, src_file_path):
        if not is_safe_path(src_file_path):
            return False
        try:
            md5sum = checksum(src_file_path)
            return md5sum
        except Exception as ex:
            logger.error('Failed to get md5sum of file %s %s' % (src_file_path, str(ex)))
            return None

    def exposed_read_part_of_file(self, src_file_path, size, offset):
        if not is_safe_path(src_file_path):
            return False
        try:
            src_file = open(src_file_path, 'rb')
            src_file.seek(offset)
            content = src_file.read(size)
            src_file.close()
            return content
        except Exception as ex:
            logger.error('Failed to read file %s %s' % (src_file_path, str(ex)))
            return None

    def exposed_write_part_of_file(self, content, dst_file_path, md5sum=None, is_end=False):
        if not is_safe_path(dst_file_path):
            return False
        try:
            if not os.path.exists(os.path.dirname(dst_file_path)):
                os.makedirs(os.path.dirname(dst_file_path))

            dst_file = open(dst_file_path, 'ab')
            dst_file.write(content)
            dst_file.close()
            if is_end:
                local_md5sum = checksum(dst_file_path)
                if local_md5sum != md5sum:
                    raise Exception("md5 checksum error")
            return True
        except Exception as ex:
            logger.error('Failed to write file %s %s' % (dst_file_path, str(ex)))
            return None

    def exposed_sync_update_pubkey(self, content):
        if self.role != 'slave':
            return
        upgrade = Upgrade()
        sync_file_res = upgrade.save_update_pubkey(content)
        if not sync_file_res:
            logger.info('sync update pubkey(%s) failed', upgrade.update_pubkey_path)
            return False
        return True

    def exposed_sync_update_filelist(self, content):
        if self.role != 'slave':
            return
        upgrade = Upgrade()
        sync_file_res = upgrade.sync_update_file(content)
        if not sync_file_res:
            logger.info('sync update pkglist(%s) failed', upgrade.sync_file)
            return False
        return True

    def exposed_sync_license_file(self, content):
        if self.role != 'slave':
            return
        license_file_path = config.get('license.file')
        try:
            with open(license_file_path, 'wb') as f:
                f.write(content)
            return True
        except:
            logger.info('sync update license(%s) failed', license_file_path)
            return False

    def exposed_do_node_upgrade(self, update_mode, update_type):
        if self.role != 'slave':
            return
        with open(CLUSTER_UPDATE_SYSTEMD_ENV_FILE_PATH, 'w') as fp:
            fp.write('UPGRADE_MODE={}\n'.format(update_mode))
            fp.write('UPGRADE_TYPE={}\n'.format(update_type))
        cmd = ['sudo', 'systemctl', 'start', 'ksp-cluster-update.service', '--no-block']
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))

    def exposed_download_upgrade_file(self, upgrade_pkg_name, update_type):
        home_dir = config.get('update.home')
        if update_type == 'data':
            warehouse_dir = os.path.join(home_dir, 'data_packages')
        else:
            warehouse_dir = os.path.join(home_dir, 'packages')
        path = os.path.join(warehouse_dir, upgrade_pkg_name)

        if not os.path.exists(path):
            return b""
        with open(path, 'rb') as f:
            data = f.read()
        return data

    def exposed_do_node_dataupdate(self, dataupdate_pkginfo_file_path, data_pkg_base_dir):
        if self.role != 'slave':
            return
        with open(CLUSTER_UPDATE_SYSTEMD_ENV_FILE_PATH, 'w') as fp:
            fp.write('DATAUPDATE_PKG_INFO_FILEPATH={}\n'.format(dataupdate_pkginfo_file_path))
            fp.write('DATAUPDATE_PKG_BASEDIR={}\n'.format(data_pkg_base_dir))
        cmd = ['sudo', 'systemctl', 'start', 'ksp-cluster-dataupdate.service', '--no-block']
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))

    def exposed_set_static_ip(self, ksp_ifname, node_ip, new_ip, mask4=None, gw4=None, dns=None,
            mask6=None, gw6=None):
        if self.role == 'master':
            return node_ip
        if not mask4 and not mask6:
            return node_ip
        cmd = [
            'sudo', CLUSTER_KSP_NETWORK_TOOL_PATH, '--dev', ksp_ifname, '--ip', new_ip
        ]
        if mask4:
            cmd.extend(['--mask', mask4])
        if mask6:
            cmd.extend(['--mask6', mask6])
        if gw4:
            cmd.extend(['--gw', gw4])
        if gw6:
            cmd.extend(['--gw6', gw6])
        if dns:
            cmd.extend(['--dns', dns])
        subproc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        stdout, _ = subproc.communicate()
        if subproc.returncode != 0:
            logger.error(stdout.decode('utf-8', errors='replace'))

    def exposed_get_network_info(self):
        network_info = systoolkit.get_network_op().network_get_status()
        return json.dumps(network_info)
