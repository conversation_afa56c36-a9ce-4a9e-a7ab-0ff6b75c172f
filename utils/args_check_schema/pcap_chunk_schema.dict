{
	"type": "object",
	"required": ["fileObject", "fileName", "fileId", "chunkId"
	],
	"properties": {
	    "fileObject": {
			"type": "string",
		},
		"fileName": {
			"type": "string",
		},
		"fileId": {
			 "type": "string",
			 "pattern": r"^[A-Za-z0-9]{1,255}$",
			 "default": ""
		},
		"chunkId": {
			 "type": "string",
			 "pattern": r"^[A-Za-z0-9]{1,255}$"
		},
		"cover": {
		      "type": "string",
                      "enum": ["true", "false"],
                      "default": "false"
	    }
	}
}