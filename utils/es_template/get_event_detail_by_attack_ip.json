{"query": {"bool": {"must": [{"range": {}}, {"term": {"rulesInfo.attackIp.keyword": "***********"}}], "should": []}}, "size": 0, "aggs": {"vulName": {"terms": {"field": "rulesInfo.vulName.keyword", "size": 5}, "aggs": {"alert": {"top_hits": {"_source": {"includes": ["observedTime", "occurredTime", "src_ip", "src_port", "dest_ip", "dest_port", "flow_id", "alert.signature", "alert.category", "alert.signature_id", "rulesInfo", "attackIpGeoip", "victimIpGeoip", "app_proto", "proto", "payload", "score", "level", "mbip.attackIpInfo.name", "mbip.attackIpInfo.type", "mbip.victimIpInfo.name", "mbip.victimIpInfo.type", "replayInterface"]}, "size": 10, "sort": {"occurredTime": "asc"}}}}}, "protolCount": {"terms": {"field": "app_proto.keyword", "size": 2147483647}}, "totalSend": {"sum": {"field": "rulesInfo.totalSend"}}, "firstOccurred": {"min": {"field": "occurredTime"}}, "lastOccurred": {"max": {"field": "occurredTime"}}, "firstObserved": {"min": {"field": "observedTime"}}, "lastObserved": {"max": {"field": "observedTime"}}, "replayInterfaceList": {"terms": {"field": "replayInterface", "size": 2147483647}}}}