import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  StarOutlined,
  StarFilled,
} from '@ant-design/icons';
import {
  getModelConfigs,
  createModelConfig,
  updateModelConfig,
  deleteModelConfig,
  ModelConfig,
} from '@/services/anomaly';
import styles from '../style.less';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

const ModelConfigManagement: React.FC = () => {
  const [configs, setConfigs] = useState<ModelConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<ModelConfig | null>(null);
  const [form] = Form.useForm();

  // 获取配置列表
  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const response = await getModelConfigs();
      if (response.flag) {
        setConfigs(response.data.configs);
      }
    } catch (error) {
      message.error('获取模型配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  // 创建新配置
  const handleCreate = () => {
    setEditingConfig(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑配置
  const handleEdit = (config: ModelConfig) => {
    setEditingConfig(config);
    form.setFieldsValue({
      name: config.name,
      description: config.description,
      model_type: config.model_type,
      is_default: config.is_default,
      ...config.config,
    });
    setModalVisible(true);
  };

  // 删除配置
  const handleDelete = async (configId: string) => {
    try {
      const response = await deleteModelConfig(configId);
      if (response.flag) {
        message.success('删除成功');
        fetchConfigs();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      // 构建配置数据
      const configData = {
        name: values.name,
        description: values.description,
        model_type: values.model_type,
        is_default: values.is_default,
        config: getModelConfigFromForm(values),
      };

      let response;
      if (editingConfig) {
        response = await updateModelConfig(editingConfig.config_id, configData);
      } else {
        response = await createModelConfig(configData);
      }

      if (response.flag) {
        message.success(editingConfig ? '更新成功' : '创建成功');
        setModalVisible(false);
        fetchConfigs();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      message.error('保存失败');
    }
  };

  // 从表单获取模型配置
  const getModelConfigFromForm = (values: any) => {
    const modelType = values.model_type;
    
    switch (modelType) {
      case 'time_series':
        return {
          z_score_threshold: values.z_score_threshold,
          time_window_hours: values.time_window_hours,
          baseline_days: values.baseline_days,
          metric_type: values.metric_type,
        };
      case 'baseline':
        return {
          baseline_periods: values.baseline_periods || [7, 30, 90],
          deviation_threshold: values.deviation_threshold,
          min_baseline_samples: values.min_baseline_samples,
          metric_types: values.metric_types || ['connections', 'bytes', 'unique_ips'],
        };
      case 'repeat_conn':
        return {
          frequency_threshold: values.frequency_threshold,
          time_window_minutes: values.time_window_minutes,
          unique_threshold: values.unique_threshold,
          pattern_types: values.pattern_types || ['ip_to_domain', 'ip_to_ip', 'port_scan'],
        };
      case 'long_conn':
        return {
          duration_thresholds: {
            http: values.http_threshold,
            https: values.https_threshold,
            tcp: values.tcp_threshold,
            dns: values.dns_threshold,
            default: values.default_threshold,
          },
          min_bytes_threshold: values.min_bytes_threshold,
          percentile_threshold: values.percentile_threshold,
        };
      case 'geo_anomaly':
        return {
          baseline_days: values.geo_baseline_days,
          min_baseline_records: values.min_baseline_records,
          geo_change_threshold: values.geo_change_threshold,
          asn_change_threshold: values.asn_change_threshold,
          suspicious_countries: values.suspicious_countries || ['RU', 'CN', 'KP', 'IR', 'SY'],
        };
      default:
        return {};
    }
  };

  // 获取模型类型名称
  const getModelTypeName = (modelType: string) => {
    const names = {
      time_series: '时间序列趋势',
      baseline: '基线偏移检测',
      repeat_conn: '重复连接检测',
      long_conn: '长连接分析',
      geo_anomaly: '地理/归属异常',
    };
    return names[modelType] || modelType;
  };

  // 获取模型类型颜色
  const getModelTypeColor = (modelType: string) => {
    const colors = {
      time_series: 'blue',
      baseline: 'green',
      repeat_conn: 'orange',
      long_conn: 'purple',
      geo_anomaly: 'red',
    };
    return colors[modelType] || 'default';
  };

  const columns = [
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: ModelConfig) => (
        <div>
          <Text strong>{text}</Text>
          {record.is_default && (
            <StarFilled style={{ color: '#faad14', marginLeft: 8 }} />
          )}
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.description}
          </Text>
        </div>
      ),
    },
    {
      title: '模型类型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (modelType: string) => (
        <Tag color={getModelTypeColor(modelType)}>
          {getModelTypeName(modelType)}
        </Tag>
      ),
    },
    {
      title: '是否默认',
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault: boolean) => (
        <Tag color={isDefault ? 'success' : 'default'}>
          {isDefault ? '默认配置' : '自定义配置'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (time: number) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: ModelConfig) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {!record.is_default && (
            <Popconfirm
              title="确定要删除这个配置吗？"
              onConfirm={() => handleDelete(record.config_id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="模型参数配置管理"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新建配置
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="config_id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 配置编辑弹窗 */}
      <Modal
        title={editingConfig ? '编辑模型配置' : '新建模型配置'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={handleSave}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="配置名称"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="请输入配置名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="description" label="配置描述">
                <Input placeholder="请输入配置描述" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="model_type"
                label="模型类型"
                rules={[{ required: true, message: '请选择模型类型' }]}
              >
                <Select placeholder="选择模型类型" disabled={!!editingConfig}>
                  <Option value="time_series">📈 时间序列趋势</Option>
                  <Option value="baseline">📊 基线偏移检测</Option>
                  <Option value="repeat_conn">🔁 重复连接检测</Option>
                  <Option value="long_conn">⛓️ 长连接分析</Option>
                  <Option value="geo_anomaly">📍 地理/归属异常</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="is_default" label="设为默认配置" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          {/* 这里可以根据选择的模型类型动态显示配置参数 */}
          <Card title="模型参数" size="small">
            <Text type="secondary">
              请根据选择的模型类型配置相应的参数。具体参数配置界面将根据模型类型动态显示。
            </Text>
          </Card>
        </Form>
      </Modal>
    </div>
  );
};

export default ModelConfigManagement;
