import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Checkbox,
  Modal,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  message,
  Typography,
} from 'antd';
import {
  ExclamationCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { getAnomalyAlerts, updateAnomalyAlerts, AnomalyAlert } from '@/services/anomaly';
import styles from '../style.less';

const { Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface AlertCenterProps {
  onStatsChange: () => void;
}

const AlertCenter: React.FC<AlertCenterProps> = ({ onStatsChange }) => {
  const [alerts, setAlerts] = useState<AnomalyAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [batchAction, setBatchAction] = useState<string>('');
  const [batchNotes, setBatchNotes] = useState<string>('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    status: '',
    severity: '',
    start_time: '',
    end_time: '',
  });

  // 获取告警列表
  const fetchAlerts = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = {
        page,
        page_size: pageSize,
        ...filters,
      };
      const response = await getAnomalyAlerts(params);
      if (response.flag) {
        setAlerts(response.data.alerts);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      message.error('获取告警列表失败');
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   fetchAlerts();
  // }, [filters]);

  // 批量操作
  const handleBatchAction = (action: string) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的告警');
      return;
    }
    setBatchAction(action);
    setBatchModalVisible(true);
  };

  // 执行批量操作
  const executeBatchAction = async () => {
    try {
      const response = await updateAnomalyAlerts({
        alert_ids: selectedRowKeys,
        status: batchAction,
        notes: batchNotes,
        handled_by: 'current_user', // 应该从用户上下文获取
      });
      if (response.flag) {
        message.success(`成功${batchAction === 'confirmed' ? '确认' : '忽略'}${selectedRowKeys.length}条告警`);
        setSelectedRowKeys([]);
        setBatchModalVisible(false);
        setBatchNotes('');
        fetchAlerts();
        onStatsChange();
      } else {
        message.error(response.message || '批量操作失败');
      }
    } catch (error) {
      message.error('批量操作失败');
    }
  };

  // 获取严重程度颜色和图标
  const getSeverityConfig = (severity: string) => {
    const configs = {
      critical: { color: 'red', icon: '🔴', text: '严重' },
      high: { color: 'orange', icon: '🟠', text: '高危' },
      medium: { color: 'gold', icon: '🟡', text: '中危' },
      low: { color: 'green', icon: '🟢', text: '低危' },
    };
    return configs[severity] || { color: 'default', icon: '⚪', text: severity };
  };

  // 获取状态配置
  const getStatusConfig = (status: string) => {
    const configs = {
      pending: { color: 'processing', text: '待处理' },
      confirmed: { color: 'success', text: '已确认' },
      dismissed: { color: 'default', text: '已忽略' },
    };
    return configs[status] || { color: 'default', text: status };
  };

  const columns = [
    {
      title: '告警时间',
      dataIndex: 'alert_time',
      key: 'alert_time',
      render: (time: number) => moment(time).format('YYYY-MM-DD HH:mm:ss'),
      sorter: true,
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => {
        const config = getSeverityConfig(severity);
        return (
          <Tag color={config.color}>
            {config.icon} {config.text}
          </Tag>
        );
      },
    },
    {
      title: '检测模型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (modelType: string) => {
        const names = {
          time_series: '时间序列趋势',
          baseline: '基线偏移检测',
          repeat_conn: '重复连接检测',
          long_conn: '长连接分析',
          geo_anomaly: '地理/归属异常',
        };
        return <Tag color="blue">{names[modelType] || modelType}</Tag>;
      },
    },
    {
      title: '目标值',
      dataIndex: 'target_value',
      key: 'target_value',
      render: (value: string) => <Text code>{value}</Text>,
    },
    {
      title: '异常分数',
      dataIndex: 'anomaly_score',
      key: 'anomaly_score',
      render: (score: number) => (
        <Text strong style={{ color: score > 0.7 ? '#ff4d4f' : score > 0.5 ? '#faad14' : '#52c41a' }}>
          {(score * 100).toFixed(1)}%
        </Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const config = getStatusConfig(status);
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 300,
    },
    {
      title: '处理人',
      dataIndex: 'handled_by',
      key: 'handled_by',
      render: (handledBy: string) => handledBy || '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: AnomalyAlert) => (
        <Space>
          {record.status === 'pending' && (
            <>
              <Button
                type="text"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleBatchAction('confirmed')}
                style={{ color: '#52c41a' }}
              >
                确认
              </Button>
              <Button
                type="text"
                size="small"
                icon={<CloseOutlined />}
                onClick={() => handleBatchAction('dismissed')}
                style={{ color: '#ff4d4f' }}
              >
                忽略
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => {
      setSelectedRowKeys(keys as string[]);
    },
    getCheckboxProps: (record: AnomalyAlert) => ({
      disabled: record.status !== 'pending',
    }),
  };

  return (
    <div>
      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<CheckOutlined />}
                onClick={() => handleBatchAction('confirmed')}
                disabled={selectedRowKeys.length === 0}
              >
                批量确认
              </Button>
              <Button
                icon={<CloseOutlined />}
                onClick={() => handleBatchAction('dismissed')}
                disabled={selectedRowKeys.length === 0}
              >
                批量忽略
              </Button>
              <Text type="secondary">
                已选择 {selectedRowKeys.length} 条告警
              </Text>
            </Space>
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="告警状态"
                value={filters.status}
                onChange={(value) => setFilters({ ...filters, status: value })}
                style={{ width: 120 }}
                allowClear
              >
                <Option value="pending">待处理</Option>
                <Option value="confirmed">已确认</Option>
                <Option value="dismissed">已忽略</Option>
              </Select>
              <Select
                placeholder="严重程度"
                value={filters.severity}
                onChange={(value) => setFilters({ ...filters, severity: value })}
                style={{ width: 120 }}
                allowClear
              >
                <Option value="critical">严重</Option>
                <Option value="high">高危</Option>
                <Option value="medium">中危</Option>
                <Option value="low">低危</Option>
              </Select>
              <RangePicker
                showTime
                onChange={(dates) => {
                  if (dates) {
                    setFilters({
                      ...filters,
                      start_time: dates[0]?.valueOf().toString() || '',
                      end_time: dates[1]?.valueOf().toString() || '',
                    });
                  } else {
                    setFilters({ ...filters, start_time: '', end_time: '' });
                  }
                }}
              />
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 告警列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={alerts}
          rowKey="alert_id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条告警`,
            onChange: fetchAlerts,
          }}
        />
      </Card>

      {/* 批量操作弹窗 */}
      <Modal
        title={`批量${batchAction === 'confirmed' ? '确认' : '忽略'}告警`}
        visible={batchModalVisible}
        onCancel={() => setBatchModalVisible(false)}
        onOk={executeBatchAction}
        okText="确定"
        cancelText="取消"
      >
        <p>
          确定要{batchAction === 'confirmed' ? '确认' : '忽略'} {selectedRowKeys.length} 条告警吗？
        </p>
        <TextArea
          placeholder="请输入处理备注（可选）"
          value={batchNotes}
          onChange={(e) => setBatchNotes(e.target.value)}
          rows={3}
        />
      </Modal>
    </div>
  );
};

export default AlertCenter;
