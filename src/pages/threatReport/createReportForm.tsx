import React, { forwardRef, useState } from 'react';
import {
  Form,
  Input,
  Checkbox,
  DatePicker,
  Radio,
  RadioChangeEvent,
  Row,
  Col
} from 'antd';

import ReportContentSelect from './reportContentSelector';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';

import { reportAltOptions, reportTypeOptions } from '@/utils/enumList';

const { RangePicker } = DatePicker;

const { Item } = Form;

const CreateReportForm = forwardRef(({ value }: any, ref: any) => {
  const [form] = Form.useForm();
  const [encryptDisabled, setEncryptDisabled] = useState<boolean>(false);
  const [radioValues, setRadioValues] = useState({
    isEncrypted: false,
    hasWaterMark: false,
    hasCustomCompany: false
  });

  const onRadioChange = ({ target: { value } }: RadioChangeEvent, key: string) => {
    setRadioValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleOnTypeChange = (values: CheckboxValueType[]) => {
    if (values.every((type) => type === '1')) {
      setEncryptDisabled(true);
      form.setFieldsValue({
        password: ''
      });

      setRadioValues((prev) => ({
        ...prev,
        isEncrypted: false,
      }));
    } else {
      setEncryptDisabled(false);
    }
  };

  return (
    <Form ref={ref} form={form} initialValues={value}>
      <div>
        <Item
          label="报告名称"
          name="name"
          rules={[{ required: true, message: '报告名称不能为空' }]}
        >
          <Input placeholder="请输入报告名称" />
        </Item>
        <Item
          label="报告格式"
          name="format"
          rules={[{ required: true, message: '请至少选择一个报告格式' }]}
        >
          <Checkbox.Group
            options={reportTypeOptions}
            onChange={handleOnTypeChange}
          />

        </Item>
        <Item
          label="时间范围"
          name="timeRange"
          rules={[{ required: true, message: '请选择时间范围' }]}

        >
          <RangePicker showTime />
        </Item>
        <Item
          label="报告内容"
          name="reportContent" >
          <ReportContentSelect />
        </Item>

        <Row>
          <Col >
            <Item
              label="加密"
              tooltip={{ title: '加密只针对PDF和Word格式的报告有效' }}
            >
              <Radio.Group
                disabled={encryptDisabled}
                options={reportAltOptions}
                value={radioValues.isEncrypted}
                onChange={(e) => onRadioChange(e, 'isEncrypted')}
              />

            </Item>
          </Col>
          <Col span={10}>
            {
              radioValues.isEncrypted &&
              <Item
                label="密码"
                  name="encrypt"
                rules={[{ required: true }]}
              >
                  <Input.Password
                  placeholder="请输入密码"
                />
              </Item>
            }
          </Col>
        </Row>

        <Row>
          <Col >
            <Item
              label="水印"
              tooltip={{ title: '水印功能只针对PDF和Word格式的报告有效' }}
            >
              <Radio.Group
                options={reportAltOptions}
                value={radioValues.hasWaterMark}
                onChange={(e) => onRadioChange(e, 'hasWaterMark')}
              />
            </Item>
          </Col>
          <Col span={10}>
            {
              radioValues.hasWaterMark &&
              <Item
                label="水印"
                  name="watermark"
                rules={[{ required: true }]}
              >
                <Input
                  placeholder="请输入水印"
                />
              </Item>
            }
          </Col>
        </Row>

        <Row>
          <Col >
            <Item
              label="自定义单位"
            >
              <Radio.Group
                options={reportAltOptions}
                value={radioValues.hasCustomCompany}
                onChange={(e) => onRadioChange(e, 'hasCustomCompany')}
              />
            </Item>
          </Col>
          <Col span={10}>
            {
              radioValues.hasCustomCompany &&
              <Item
                label="单位"
                name="custom_name"
                rules={[{ required: true }]}
              >
                <Input
                  placeholder="请输入单位名称"
                />
              </Item>
            }
          </Col>
        </Row>
      </div>
    </Form>
  );
});

export default CreateReportForm;
