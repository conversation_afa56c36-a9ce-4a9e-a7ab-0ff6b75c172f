# -*- coding: utf-8 -*-
# @Time    : 2019-06-24 10:44
# <AUTHOR> <PERSON>
# @File    : get_scheme.py
# @Software: PyCharm
import ast
import os
from utils.logger import get_ndr_logger
from jsonschema import Draft7Validator

LOG = get_ndr_logger("tools_log", __file__)


class Schema:
    """通过加载schema文件返回schema"""

    def __init__(self, schema_name):
        self.name = schema_name
        self.schema = {}
        self.read_schema()

    def get_schema(self):
        """返回schema"""
        Draft7Validator.check_schema(self.schema)
        return self.schema

    def read_schema(self):
        """从文件加载schema并转成dict"""
        try:
            dir_path = os.path.abspath(os.path.split(os.path.abspath(os.path.realpath(__file__)))[0])
            f = open(os.path.join(dir_path, "{0}.dict".format(self.name)), "r")
            schema = f.read()
            self.schema = ast.literal_eval(schema)
        except Exception as e:
            LOG.error("please check {0}.dict:{1}".format(self.name, e))
