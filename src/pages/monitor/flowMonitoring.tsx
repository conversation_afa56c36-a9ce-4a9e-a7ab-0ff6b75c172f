/*
 * @Author: tianh
 * @Date: 2022-02-21 11:42:11
 * @LastEditors: tianh
 * @LastEditTime: 2022-04-26 15:03:34
 * @Descripttion:
 */
import React, { useState } from 'react';

import Detail from './detail';
import Simple from './simple';

const FlowMonitoring = () => {
  const [isSimple, setisSimple] = useState(false);

  const getSwitch = (value: any) => {
    setisSimple(value);
  };
  return (
    <div>
      <Simple isSimple={isSimple} getSwitch={getSwitch} />
    </div>
  );
};

export default FlowMonitoring;
