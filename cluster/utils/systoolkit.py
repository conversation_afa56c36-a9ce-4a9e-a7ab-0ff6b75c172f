#!/usr/bin/env python
# coding=utf-8


def get_ifmap_config():
    from ksp.systoolkit.lib.ifmap import CJsonConf

    ifmap_config = CJsonConf()
    return ifmap_config


def get_network_tool():
    from ksp.systoolkit.lib.net import NetworkTool

    network_tool = NetworkTool()
    return network_tool


def get_system_tool():
    from ksp.systoolkit.lib.system import System

    system_tool = System()
    return system_tool

def get_network_op():
    from ksp.systoolkit.lib.kspnetop import KSPNetworkOperator

    ksp_net_op = KSPNetworkOperator()
    return ksp_net_op
