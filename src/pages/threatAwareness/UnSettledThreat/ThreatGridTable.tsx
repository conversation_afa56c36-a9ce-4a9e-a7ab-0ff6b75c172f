import React from 'react';
import cs from 'classnames';

import { Empty, Pagination, Spin } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

import style from './grid-table.less'

export type Props = {
  page: number;
  itemRender: (record: any) => void;
  data?: any[];
  loading?: boolean;
  className?: string;
  oneColumn?: boolean
  onPageChange?: (page: number) => void;
  total?: number;
  isTotalData?: boolean;
}

function ThreatGridTable({
  className,
  data = [],
  itemRender,
  onPageChange,
  oneColumn,
  loading,
  total,
  isTotalData = true,
  page: current,
}: Props): JSX.Element {

  const pageSize = oneColumn ? 2 : 4


  const handlePageChange = (page: number) => {
    onPageChange?.(page)
  }

  function calculatePageRange(data: any, page: number, pageSize: number) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = page * pageSize;
    const pageData = data.slice(startIndex, endIndex);
    return pageData;
  }

  const currentData = isTotalData ? calculatePageRange(data, current, pageSize) : data 

  return (
    <Spin wrapperClassName='w-full' spinning={loading}>
      <div
        style={style}
        className={cs(style.grid_table, className)}
      >
        {
          !!total ? (

        <div className={cs(style.table_content, {
          [style.table_content_giant_item]: oneColumn
        })}
            >
              {
                currentData.length ?
                  currentData.map((item: any, index: number) => {
                    return <React.Fragment key={item?.id || index}>{itemRender(item)}</React.Fragment>
                  }) :
                  <Empty />
              }
            </div>

          ) : <Empty />

        }

        {
          !!total && (
            <Pagination
              className='flex justify-center'
              total={total}
              size="small"
              current={current}
              pageSize={pageSize}
              showTotal={total => `共 ${total} 条`}
              defaultCurrent={1}
              showSizeChanger={false}
              onChange={handlePageChange}
            />
          )
        }


      </div>
    </Spin>
  );
}

export default ThreatGridTable;
