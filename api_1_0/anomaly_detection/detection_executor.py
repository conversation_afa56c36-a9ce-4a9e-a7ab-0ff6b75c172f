# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : detection_executor.py
# @Software: PyCharm

"""异常检测任务执行器"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Tuple
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from config.config import NdrLog
from api_1_0.utils.flask_log import ndr_log_to_box
from .detection_models.detector_factory import DetectorFactory

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class DetectionExecutor:
    """异常检测任务执行器"""
    
    def __init__(self):
        self.mongodb = MongoDB('ndr')
    
    def execute_task(self, task_id: str) -> bool:
        """
        执行异常检测任务（支持多模型）

        Args:
            task_id: 任务ID

        Returns:
            执行是否成功
        """
        try:
            # 获取任务配置
            task = self.mongodb.find_one('anomaly_tasks', {'task_id': task_id}, {'_id': 0})
            if not task:
                LOG.error(f"任务不存在: {task_id}")
                return False

            # 检查任务状态
            if task.get('status') != 'active':
                LOG.info(f"任务未激活: {task_id}")
                return False

            # 确定检测时间范围
            start_time, end_time = self._get_detection_time_range(task)
            if not start_time or not end_time:
                LOG.error(f"无法确定检测时间范围: {task_id}")
                return False

            # 获取模型类型列表
            model_types = task.get('model_types', [])
            if not model_types:
                LOG.error(f"任务没有配置检测模型: {task_id}")
                return False

            all_results = []
            success_count = 0

            # 为每个模型执行检测
            for model_type in model_types:
                try:
                    # 获取模型配置
                    model_config = self._get_model_config(model_type)

                    # 构建单个模型的任务配置
                    single_task_config = {
                        'task_id': task_id,
                        'model_type': model_type,
                        'target_type': task['target_type'],
                        'target_value': task['target_value'],
                        'detection_config': model_config
                    }

                    # 创建检测器
                    detector = DetectorFactory.create_detector(single_task_config)
                    if not detector:
                        LOG.error(f"创建检测器失败: {task_id} - {model_type}")
                        continue

                    # 执行检测
                    LOG.info(f"执行检测: {task_id} - {model_type}, 时间范围: {start_time} - {end_time}")
                    detection_results = detector.detect(start_time, end_time)

                    if detection_results:
                        # 为结果添加模型标识
                        for result in detection_results:
                            result['model_type'] = model_type
                        all_results.extend(detection_results)

                        # 保存检测结果
                        detector.save_result(detection_results, datetime.now())
                        LOG.info(f"模型{model_type}检测到{len(detection_results)}个异常")

                    success_count += 1

                except Exception as e:
                    LOG.error(f"模型{model_type}检测失败: {str(e)}")
                    continue

            # 记录总体结果
            if all_results:
                LOG.info(f"任务{task_id}总共检测到{len(all_results)}个异常")
                ndr_log_to_box(NdrLog.Type.OPERATE,
                             f"异常检测任务{task['name']}检测到{len(all_results)}个异常")
            else:
                LOG.info(f"任务{task_id}未检测到异常")

            # 更新任务执行信息
            self._update_task_execution_info(task_id, len(all_results))

            return success_count > 0

        except Exception as e:
            LOG.error(f"执行异常检测任务失败: {task_id}, 错误: {str(e)}")
            ndr_log_to_box(NdrLog.Type.OPERATE, f"异常检测任务执行失败: {str(e)}")
            return False
    
    def execute_all_active_tasks(self) -> Dict[str, Any]:
        """
        执行所有活跃的异常检测任务
        
        Returns:
            执行结果统计
        """
        try:
            # 获取所有活跃任务
            active_tasks = list(self.mongodb.find('anomaly_tasks', {'status': 'active'}, {'_id': 0}))
            
            if not active_tasks:
                LOG.info("没有活跃的异常检测任务")
                return {'total': 0, 'success': 0, 'failed': 0, 'results': []}
            
            results = []
            success_count = 0
            failed_count = 0
            
            for task in active_tasks:
                task_id = task['task_id']
                
                # 检查是否需要执行
                if not self._should_execute_task(task):
                    continue
                
                # 执行任务
                success = self.execute_task(task_id)
                
                result = {
                    'task_id': task_id,
                    'task_name': task.get('name', ''),
                    'model_type': task.get('model_type', ''),
                    'success': success,
                    'execution_time': datetime.now().isoformat()
                }
                results.append(result)
                
                if success:
                    success_count += 1
                else:
                    failed_count += 1
            
            summary = {
                'total': len(results),
                'success': success_count,
                'failed': failed_count,
                'results': results
            }
            
            LOG.info(f"批量执行异常检测任务完成: 总数{len(results)}, 成功{success_count}, 失败{failed_count}")
            
            return summary
            
        except Exception as e:
            LOG.error(f"批量执行异常检测任务失败: {str(e)}")
            return {'total': 0, 'success': 0, 'failed': 0, 'results': [], 'error': str(e)}
    
    def _get_detection_time_range(self, task: Dict[str, Any]) -> Tuple[datetime, datetime]:
        """获取检测时间范围"""
        time_config = task.get('time_config', {})
        task_type = time_config.get('type', 'scheduled')

        if task_type == 'historical':
            # 历史回溯任务
            start_timestamp = time_config.get('start_time')
            end_timestamp = time_config.get('end_time')

            if start_timestamp and end_timestamp:
                start_time = datetime.fromtimestamp(int(start_timestamp) / 1000)
                end_time = datetime.fromtimestamp(int(end_timestamp) / 1000)
                return start_time, end_time
            else:
                return None, None

        elif task_type == 'scheduled':
            # 定时任务
            now = datetime.now()
            time_range_hours = time_config.get('time_range_hours', 1)

            end_time = now
            start_time = now - timedelta(hours=time_range_hours)
            return start_time, end_time

        else:
            return None, None

    def _get_model_config(self, model_type: str) -> Dict[str, Any]:
        """获取模型配置"""
        try:
            # 查找该模型类型的默认配置
            config = self.mongodb.find_one(
                'model_configs',
                {'model_type': model_type, 'is_default': True},
                {'_id': 0}
            )

            if config:
                return config.get('config', {})
            else:
                # 如果没有找到配置，返回默认配置
                from .detection_models.detector_factory import DetectorFactory
                return DetectorFactory.get_default_config(model_type)

        except Exception as e:
            LOG.error(f"获取模型配置失败: {model_type}, 错误: {str(e)}")
            # 返回默认配置
            from .detection_models.detector_factory import DetectorFactory
            return DetectorFactory.get_default_config(model_type)
    
    def _should_execute_task(self, task: Dict[str, Any]) -> bool:
        """判断任务是否需要执行"""
        try:
            time_config = task.get('time_config', {})
            task_type = time_config.get('type', 'scheduled')
            last_run_time = task.get('last_run_time')
            now = datetime.now()

            if task_type == 'historical':
                # 历史回溯任务只执行一次
                return last_run_time is None

            elif task_type == 'scheduled':
                # 定时任务

                # 检查任务是否在有效期内
                start_date = time_config.get('start_date')
                end_date = time_config.get('end_date')

                if start_date:
                    start_dt = datetime.fromtimestamp(int(start_date) / 1000)
                    if now < start_dt:
                        return False

                if end_date:
                    end_dt = datetime.fromtimestamp(int(end_date) / 1000)
                    if now > end_dt:
                        return False

                # 检查是否在启用的日期
                enabled_days = time_config.get('enabled_days', [1,2,3,4,5,6,7])
                current_weekday = now.weekday() + 1  # 转换为1-7
                if current_weekday not in enabled_days:
                    return False

                # 如果从未执行过，需要执行
                if not last_run_time:
                    return True

                # 计算下次执行时间
                interval_hours = time_config.get('interval_hours', 24)
                next_run_time = last_run_time + timedelta(hours=interval_hours)

                return now >= next_run_time

            else:
                return False

        except Exception as e:
            LOG.error(f"判断任务执行时间失败: {str(e)}")
            return False
    
    def _update_task_execution_info(self, task_id: str, result_count: int):
        """更新任务执行信息"""
        try:
            update_data = {
                'last_run_time': datetime.now(),
                'update_time': datetime.now(),
                'last_result_count': result_count,
                '$inc': {'run_count': 1}
            }

            self.mongodb.update_one('anomaly_tasks', {'task_id': task_id}, update_data)

        except Exception as e:
            LOG.error(f"更新任务执行信息失败: {task_id}, 错误: {str(e)}")
    
    def execute_manual_detection(self, task_config: Dict[str, Any], 
                                start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """
        执行手动异常检测
        
        Args:
            task_config: 任务配置
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            检测结果
        """
        try:
            # 验证配置
            is_valid, error_msg = DetectorFactory.validate_task_config(task_config)
            if not is_valid:
                return {'success': False, 'error': error_msg, 'results': []}
            
            # 创建检测器
            detector = DetectorFactory.create_detector(task_config)
            if not detector:
                return {'success': False, 'error': '创建检测器失败', 'results': []}
            
            # 执行检测
            LOG.info(f"开始手动异常检测: {task_config.get('model_type')}, 时间范围: {start_time} - {end_time}")
            detection_results = detector.detect(start_time, end_time)
            
            result = {
                'success': True,
                'model_type': task_config.get('model_type'),
                'target_type': task_config.get('target_type'),
                'target_value': task_config.get('target_value'),
                'time_range': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                },
                'detection_count': len(detection_results),
                'results': detection_results,
                'execution_time': datetime.now().isoformat()
            }
            
            LOG.info(f"手动异常检测完成: 检测到{len(detection_results)}个异常")
            
            return result
            
        except Exception as e:
            LOG.error(f"手动异常检测失败: {str(e)}")
            return {'success': False, 'error': str(e), 'results': []}
    
    def get_detection_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取检测统计信息"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # 统计检测结果
            result_stats = self.mongodb.aggs('anomaly_results', [
                {'$match': {'detection_time': {'$gte': start_time, '$lte': end_time}}},
                {'$group': {
                    '_id': '$model_type',
                    'count': {'$sum': 1},
                    'high_severity': {'$sum': {'$cond': [{'$eq': ['$severity', 'high']}, 1, 0]}},
                    'critical_severity': {'$sum': {'$cond': [{'$eq': ['$severity', 'critical']}, 1, 0]}}
                }}
            ])
            
            # 统计告警
            alert_stats = self.mongodb.aggs('anomaly_alerts', [
                {'$match': {'alert_time': {'$gte': start_time, '$lte': end_time}}},
                {'$group': {
                    '_id': '$status',
                    'count': {'$sum': 1}
                }}
            ])
            
            # 统计任务执行
            task_stats = self.mongodb.aggs('anomaly_tasks', [
                {'$match': {'last_run_time': {'$gte': start_time, '$lte': end_time}}},
                {'$group': {
                    '_id': '$status',
                    'count': {'$sum': 1}
                }}
            ])
            
            return {
                'time_range': {
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'days': days
                },
                'result_stats': list(result_stats),
                'alert_stats': list(alert_stats),
                'task_stats': list(task_stats)
            }
            
        except Exception as e:
            LOG.error(f"获取检测统计信息失败: {str(e)}")
            return {'error': str(e)}
