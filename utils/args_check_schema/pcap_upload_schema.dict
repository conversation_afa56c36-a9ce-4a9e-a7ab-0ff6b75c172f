{
	"type": "object",
	"required": ["fileName", "fileId", "chunkList"
	],
	"properties": {
		"fileName": {
			"type": "string",
		},
		"dir": {
			"type": "string",
			"default":""
		},
		"tag": {
			 "type": "string",
             "pattern": r"^|^[A-Za-z0-9_.\-\u4e00-\u9fa5]{1,255}$",
			 "default": ""
		},
		"type": {
		    "type": "number",
		    "enum": [1, 2],
            "default": 1
		},
		"fileId": {
			 "type": "string",
			 "pattern": r"^[A-Za-z0-9]{1,255}$"
		},
		"chunkList": {
			"type": "array",
			"items": {
			    "type": "integer",
			    "pattern": r"^\d{0,99999}$"
			  }
	    }
	}
}