# -*- coding: utf-8 -*-
# @Time    : 2020-05-07 10:54
# <AUTHOR> wu
# @File    : authority.py
# @Software: PyCharm

ADMIN = [
    '/api/v1/knowledge/killchains',
    '/api/v1/knowledge/threat_flag',
    '/api/v1/knowledge/threat_level',
    '/api/v1/knowledge/country',
    '/api/v1/knowledge/event_type',
    '/api/v1/knowledge/task',
    '/api/v1/knowledge/model_name',
    '/api/v1/knowledge/vul_name',
    '/api/v1/knowledge/cve',
    '/api/v1/knowledge/apt_org',
    '/api/v1/alert/top/attackip',
    '/api/v1/alert/top/victimip',
    '/api/v1/alert/top/trigger_count',
    '/api/v1/alert/top/threat_score',
    '/api/v1/alert/list/hud',
    '/api/v1/alert/list',
    '/api/v1/alert/map',
    '/api/v1/alert/detail',
    '/api/v1/alert/group',
    '/api/v1/package',
    '/api/v1/replay',
    '/api/v1/feature',
    '/api/v1/feature/status',
    '/api/v1/feature/update',
    '/api/v1/feature/customer',
    '/api/v1/feature/<string:sid>',
    '/api/v1/feature/active_task/<string:task_id>',
    '/api/v1/feature/active_task',
    '/api/v1/feature/enable_status',
    '/api/v1/feature/group/<string:group_id>',
    '/api/v1/feature/group',
    '/api/v1/event/top/latest',
    '/api/v1/event/top/score',
    '/api/v1/event/list',
    '/api/v1/event/detail',
    '/api/v1/event/stats/killchains',
    '/api/v1/event/group',
    '/api/v1/event/timeline/<string:mode>',
    '/api/v1/ioc_alert/list',
    '/api/v1/ioc_alert/group',
    '/api/v1/event/location',
    '/api/v1/model/list',
    '/api/v1/model/group',
    '/api/v1/pcap/download',
    '/api/v1/pcap/spi',
    '/api/v1/pcap/connect',
    '/api/v1/pcap/session',
    '/api/v1/explore/<string:task_id>',
    '/api/v1/explore',
    '/api/v1/explore/pcap',
    '/api/v1/explore/pcap_singe',
    '/api/v1/explore/pcap_chunk',
    '/api/v1/explore/pcap_upload',
    '/api/v1/explore/pcap_exist',
    '/api/v1/login',
    '/api/v1/logout',
    '/api/v1/user',
    '/api/v1/user/<string:user_id>',
    '/api/v1/system/reboot',
    '/api/v1/system/poweroff',
    '/api/v1/system/status',
    '/api/v1/system/history',
    '/api/v1/system/net/card',
    '/api/v1/system/network',
    '/api/v1/system/net/test',
    '/api/v1/system/about',
    '/api/v1/system/help',
    '/api/v1/report/simple/statistics',
    '/api/v1/report/simple/statistics_level',
    '/api/v1/report/simple/statistics_threat',
    '/api/v1/report/simple/statistics_killchains',
    '/api/v1/report/simple/top_event',
    '/api/v1/report/simple/killchains_flow',
    '/api/v1/report/simple/threat_flow',
    '/api/v1/report/simple/proto_flow',
    '/api/v1/report/simple/top_attackip',
    '/api/v1/report/simple/top_victimip',
    '/api/v1/report/detail/all_flow',
    '/api/v1/report/detail/trigger_count',
    '/api/v1/report/detail/ioc_count',
    '/api/v1/report/detail/killchains_count',
    '/api/v1/report/detail/threat_count',
    '/api/v1/report/detail/ip_flow',
    '/api/v1/report/detail/ip_location',
    '/api/v1/system/log',
    '/api/v1/system/export/log'
]

OPERATOR = [
    '/api/v1/knowledge/killchains',
    '/api/v1/knowledge/threat_flag',
    '/api/v1/knowledge/threat_level',
    '/api/v1/knowledge/country',
    '/api/v1/knowledge/event_type',
    '/api/v1/knowledge/task',
    '/api/v1/knowledge/model_name',
    '/api/v1/knowledge/vul_name',
    '/api/v1/knowledge/cve',
    '/api/v1/knowledge/apt_org',
    '/api/v1/alert/top/attackip',
    '/api/v1/alert/top/victimip',
    '/api/v1/alert/top/trigger_count',
    '/api/v1/alert/top/threat_score',
    '/api/v1/alert/list/hud',
    '/api/v1/alert/list',
    '/api/v1/alert/map',
    '/api/v1/alert/detail',
    '/api/v1/alert/group',
    '/api/v1/package',
    '/api/v1/replay',
    '/api/v1/feature',
    '/api/v1/feature/status',
    '/api/v1/feature/customer',
    '/api/v1/feature/<string:sid>',
    '/api/v1/feature/active_task/<string:task_id>',
    '/api/v1/feature/active_task',
    '/api/v1/feature/enable_status',
    '/api/v1/feature/group/<string:group_id>',
    '/api/v1/feature/group',
    '/api/v1/event/top/latest',
    '/api/v1/event/top/score',
    '/api/v1/event/list',
    '/api/v1/event/detail',
    '/api/v1/event/stats/killchains',
    '/api/v1/event/group',
    '/api/v1/event/timeline/<string:mode>',
    '/api/v1/ioc_alert/list',
    '/api/v1/ioc_alert/group',
    '/api/v1/event/location',
    '/api/v1/model/list',
    '/api/v1/model/group',
    '/api/v1/pcap/download',
    '/api/v1/pcap/spi',
    '/api/v1/pcap/connect',
    '/api/v1/pcap/session',
    '/api/v1/explore/<string:task_id>',
    '/api/v1/explore',
    '/api/v1/explore/pcap',
    '/api/v1/explore/pcap_singe',
    '/api/v1/explore/pcap_chunk',
    '/api/v1/explore/pcap_upload',
    '/api/v1/explore/pcap_exist',
    '/api/v1/login',
    '/api/v1/logout',
    '/api/v1/user',
    '/api/v1/user/<string:user_id>',
    '/api/v1/system/status',
    '/api/v1/system/history',
    '/api/v1/system/about',
    '/api/v1/system/help',
    '/api/v1/report/simple/statistics',
    '/api/v1/report/simple/statistics_level',
    '/api/v1/report/simple/statistics_threat',
    '/api/v1/report/simple/statistics_killchains',
    '/api/v1/report/simple/top_event',
    '/api/v1/report/simple/killchains_flow',
    '/api/v1/report/simple/threat_flow',
    '/api/v1/report/simple/proto_flow',
    '/api/v1/report/simple/top_attackip',
    '/api/v1/report/simple/top_victimip',
    '/api/v1/report/detail/all_flow',
    '/api/v1/report/detail/trigger_count',
    '/api/v1/report/detail/ioc_count',
    '/api/v1/report/detail/killchains_count',
    '/api/v1/report/detail/threat_count',
    '/api/v1/report/detail/ip_flow',
    '/api/v1/report/detail/ip_location'
]

AUDITOR = [
    '/api/v1/login',
    '/api/v1/logout',
    '/api/v1/report/simple/statistics',
    '/api/v1/report/simple/statistics_level',
    '/api/v1/report/simple/statistics_threat',
    '/api/v1/report/simple/statistics_killchains',
    '/api/v1/report/simple/top_event',
    '/api/v1/report/simple/killchains_flow',
    '/api/v1/report/simple/threat_flow',
    '/api/v1/report/simple/proto_flow',
    '/api/v1/report/simple/top_attackip',
    '/api/v1/report/simple/top_victimip',
    '/api/v1/report/detail/all_flow',
    '/api/v1/report/detail/trigger_count',
    '/api/v1/report/detail/ioc_count',
    '/api/v1/report/detail/killchains_count',
    '/api/v1/report/detail/threat_count',
    '/api/v1/report/detail/ip_flow',
    '/api/v1/report/detail/ip_location',
    '/api/v1/system/log',
    '/api/v1/system/export/log'
]
