import React from 'react';

import { useHistory } from 'react-router';
import { searchToObject } from './EmailDetail';
import EmailAttachmentDetailContent from './EmailAttachmentDetailContent';

function EmailAttachmentDetail() {
  const history = useHistory()
  const search = searchToObject(location.search)
  const { end_time, start_time, uuid } = search

  if (!end_time || !start_time || !uuid) {
    history.push('/exception-404')
  }

  return (
    <EmailAttachmentDetailContent data={search as any} />
  );
};

export default EmailAttachmentDetail;

