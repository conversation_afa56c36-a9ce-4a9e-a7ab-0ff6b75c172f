# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : scheduler_manager.py
# @Software: PyCharm

"""调度器管理API"""

from flask import request
from flask_restful import Resource
from config.config import NdrLog
from utils.logger import get_ndr_logger
from utils.utils import flask_response
from api_1_0.utils.flask_log import ndr_log_to_box
from .scheduler import get_scheduler_status, start_scheduler, stop_scheduler, execute_task_manually

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class SchedulerControl(Resource):
    """调度器控制"""
    
    
    def get(self):
        """获取调度器状态"""
        try:
            status = get_scheduler_status()
            return flask_response('获取调度器状态成功', True, status)
            
        except Exception as e:
            LOG.error(f"获取调度器状态失败: {str(e)}")
            return flask_response(f'获取调度器状态失败: {str(e)}', False, {})
    
    
    def post(self):
        """控制调度器启动/停止"""
        try:
            data = request.get_json()
            action = data.get('action')
            
            if action == 'start':
                start_scheduler()
                ndr_log_to_box(NdrLog.Type.OPERATE, "启动异常检测调度器")
                return flask_response('调度器启动成功', True, {})
            elif action == 'stop':
                stop_scheduler()
                ndr_log_to_box(NdrLog.Type.OPERATE, "停止异常检测调度器")
                return flask_response('调度器停止成功', True, {})
            else:
                return flask_response('无效的操作类型', False, {})
            
        except Exception as e:
            LOG.error(f"调度器控制失败: {str(e)}")
            return flask_response(f'调度器控制失败: {str(e)}', False, {})


class TaskExecution(Resource):
    """任务执行控制"""
    
    
    def post(self, task_id):
        """手动执行指定任务"""
        try:
            success = execute_task_manually(task_id)
            
            if success:
                ndr_log_to_box(NdrLog.Type.OPERATE, f"手动执行异常检测任务: {task_id}")
                return flask_response('任务执行成功', True, {})
            else:
                return flask_response('任务执行失败', False, {})
            
        except Exception as e:
            LOG.error(f"手动执行任务失败: {task_id}, 错误: {str(e)}")
            return flask_response(f'任务执行失败: {str(e)}', False, {})
