{
    "type": "object",
    "required": [],
    "properties": {
        "start_time": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stop_time": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "source_name": {
            "type": "string",
            "default": ""
        },
        "data_types": {
            "type": "string",
            "default": ""
        },
        "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}