import request from '@/utils/request';

// 异常检测任务相关接口
export interface AnomalyTask {
  task_id: string;
  name: string;
  description: string;
  model_types: string[];  // 支持多个模型
  target_type: string;
  target_value: string;
  task_type: string;  // historical, scheduled
  time_config: any;   // 时间配置
  alert_config: any;
  status: string;
  create_time: number;
  update_time: number;
  last_run_time?: number;
  run_count: number;
  creator: string;
}

export interface ModelConfig {
  config_id: string;
  name: string;
  description: string;
  model_type: string;
  config: any;
  is_default: boolean;
  create_time: number;
  update_time: number;
  creator: string;
}

export interface AnomalyResult {
  result_id: string;
  task_id: string;
  model_type: string;
  target_type: string;
  target_value: string;
  detection_time: number;
  create_time: number;
  status: string;
  confirmed: boolean;
  anomaly_score: number;
  severity: string;
  description: string;
  details: any;
}

export interface AnomalyAlert {
  alert_id: string;
  result_id: string;
  task_id: string;
  model_type: string;
  target_value: string;
  severity: string;
  anomaly_score: number;
  description: string;
  alert_time: number;
  create_time: number;
  status: string;
  handled_by?: string;
  notes: string;
}

// 获取任务列表
export const getAnomalyTasks = (params: {
  page?: number;
  page_size?: number;
  status?: string;
  model_type?: string;
}) => {
  return request('anomaly/tasks', {
    method: 'GET',
    params,
  });
};

// 创建任务
export const createAnomalyTask = (data: Partial<AnomalyTask>) => {
  return request('anomaly/tasks', {
    method: 'POST',
    data,
  });
};

// 获取任务详情
export const getAnomalyTaskDetail = (taskId: string) => {
  return request(`anomaly/tasks/${taskId}`, {
    method: 'GET',
  });
};

// 更新任务
export const updateAnomalyTask = (taskId: string, data: Partial<AnomalyTask>) => {
  return request(`anomaly/tasks/${taskId}`, {
    method: 'PUT',
    data,
  });
};

// 删除任务
export const deleteAnomalyTask = (taskId: string) => {
  return request(`anomaly/tasks/${taskId}`, {
    method: 'DELETE',
  });
};

// 获取检测结果列表
export const getAnomalyResults = (params: {
  page?: number;
  page_size?: number;
  task_id?: string;
  model_type?: string;
  severity?: string;
  start_time?: number;
  end_time?: number;
  target_value?: string;
}) => {
  return request('anomaly/results', {
    method: 'GET',
    params,
  });
};

// 获取检测结果详情
export const getAnomalyResultDetail = (resultId: string) => {
  return request(`anomaly/results/${resultId}`, {
    method: 'GET',
  });
};

// 更新检测结果状态
export const updateAnomalyResult = (resultId: string, data: {
  status?: string;
  confirmed?: boolean;
  notes?: string;
  handled_by?: string;
}) => {
  return request(`anomaly/results/${resultId}`, {
    method: 'PUT',
    data,
  });
};

// 获取告警列表
export const getAnomalyAlerts = (params: {
  page?: number;
  page_size?: number;
  status?: string;
  severity?: string;
  start_time?: number;
  end_time?: number;
}) => {
  return request('anomaly/alerts', {
    method: 'GET',
    params,
  });
};

// 批量更新告警状态
export const updateAnomalyAlerts = (data: {
  alert_ids: string[];
  status: string;
  handled_by?: string;
  notes?: string;
}) => {
  return request('anomaly/alerts', {
    method: 'PUT',
    data,
  });
};

// 获取告警统计
export const getAnomalyAlertStats = () => {
  return request('anomaly/alerts/stats', {
    method: 'GET',
  });
};

// 获取异常检测统计数据
export const getAnomalyStats = () => {
  return request('anomaly/alerts/stats', {
    method: 'GET',
  });
};

// 手动执行检测
export const executeManualDetection = (data: {
  model_type: string;
  target_type: string;
  target_value: string;
  detection_config: any;
  start_time: number;
  end_time: number;
}) => {
  return request('anomaly/manual_detection', {
    method: 'POST',
    data,
  });
};

// 获取支持的检测模型
export const getSupportedModels = () => {
  return request('anomaly/models', {
    method: 'GET',
  });
};

// 获取模型默认配置
export const getModelDefaultConfig = (modelType: string) => {
  return request(`anomaly/models/${modelType}/config`, {
    method: 'GET',
  });
};

// 模型配置管理API
export const getModelConfigs = () => {
  return request('anomaly/model_configs', {
    method: 'GET',
  });
};

export const createModelConfig = (data: Partial<ModelConfig>) => {
  return request('anomaly/model_configs', {
    method: 'POST',
    data,
  });
};

export const getModelConfigDetail = (configId: string) => {
  return request(`anomaly/model_configs/${configId}`, {
    method: 'GET',
  });
};

export const updateModelConfig = (configId: string, data: Partial<ModelConfig>) => {
  return request(`anomaly/model_configs/${configId}`, {
    method: 'PUT',
    data,
  });
};

export const deleteModelConfig = (configId: string) => {
  return request(`anomaly/model_configs/${configId}`, {
    method: 'DELETE',
  });
};
