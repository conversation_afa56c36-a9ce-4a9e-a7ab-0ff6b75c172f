#!/usr/bin/env python3
# -*- coding:utf-8 -*-
from utils.utils import flask_response
from flask_restful import Resource
from utils.param_check import Validator, check_flask_args
from config.config import UserConfig, NdrLog
from utils import param_check
from flask import request
import datetime
import time
from clickhouse_driver import Client as CH_client
from config.config import ClickHouseConfig
from utils.logger import get_ndr_logger, LogToDb
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class DpilogResource(Resource):
    def __init__(self):
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)

    def __del__(self):
        self.ch_client.disconnect()

    def get_val_from_database(self, sql):
        ret_list = list()

        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except:
            ndr_log_to_box(NdrLog.Type.OPERATE, "ClickHouse数据库连接或执行失败！")
            return 'ClickHouse数据库连接或执行失败！', ret_list

        if raw is None or not raw[0]:
            return 'Not found!', ret_list

        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]

        for i in range(len(raw_val)):
            data = {}

            for idx, item in enumerate(raw_val[i]):
                data[raw_key[idx][0]] = item

            ret_list.append(data)

        return "", ret_list


class DpilogIp(DpilogResource):
    def __init__(self):
        super(DpilogIp, self).__init__()

    def __del__(self):
        super(DpilogIp, self).__del__()

    @param_check.check_flask_args(Validator('dpilog_ip_info_schema'), request)
    def get(self, **kwargs):

        celeryId = kwargs['celeryId']
        sql = "select (t1.ts) from " \
              "(select ts from dpilog_http where celeryId = '%s'  " \
              "union all " \
              "select ts from dpilog_ssl where celeryId = '%s' ) as t1 order by t1.ts desc limit 1" \
              % (celeryId, celeryId)
        msg, ts_list = self.get_val_from_database(sql)
        if len(ts_list) > 0:
            ts_date = ts_list[0]['ts']
        else:
            return flask_response('data is none', True, ts_list)
        date_big = ts_date
        date_one = (date_big + datetime.timedelta(days=-1))
        date_two = (date_big + datetime.timedelta(days=-2))
        date_three = (date_big + datetime.timedelta(days=-3))
        date_four = (date_big + datetime.timedelta(days=-4))

        date_big_1 = str(date_big)[0:11] + "00:00:00"
        date_big_2 = str(date_big)[0:11] + "23:59:59"

        date_one_1 = str(date_one)[0:11] + "00:00:00"
        date_one_2 = str(date_one)[0:11] + "23:59:59"

        date_two_1 = str(date_two)[0:11] + "00:00:00"
        date_two_2 = str(date_two)[0:11] + "23:59:59"

        date_three_1 = str(date_three)[0:11] + "00:00:00"
        date_three_2 = str(date_three)[0:11] + "23:59:59"

        date_four_1 = str(date_four)[0:11] + "00:00:00"
        date_four_2 = str(date_four)[0:11] + "23:59:59"

        white_list = set()
        with open("/opt/ndr/config/white", "r") as f:
            line = f.readlines()
            for i in line:
                white_list.add(i.strip())

        domain_list = []
        domain1 = self.domain_search(white_list, celeryId, date_big_2, date_big_1)
        domain2 = self.domain_search(white_list, celeryId, date_one_2, date_one_1)
        domain3 = self.domain_search(white_list, celeryId, date_two_2, date_two_1)
        domain4 = self.domain_search(white_list, celeryId, date_three_2, date_three_1)
        domain5 = self.domain_search(white_list, celeryId, date_four_2, date_four_1)
        domain_list.append(domain1)
        domain_list.append(domain2)
        domain_list.append(domain3)
        domain_list.append(domain4)
        domain_list.append(domain5)
        return flask_response(msg, True, domain_list)

    def domain_search(self, white_list, celeryId, date1, date2):
        country_list = ['ad', 'ae', 'af', 'ag', 'ai', 'al', 'am', 'an', 'ao', 'aq', 'ar', 'as', 'at', 'au', 'aw', 'az', 'ba', 'bb', 'bd',
                        'be', 'bf', 'bg', 'bh', 'bi', 'bj', 'bm', 'bn', 'bo', 'br', 'bs', 'bt', 'bv', 'bw', 'by', 'bz', 'ca', 'cc', 'cd',
                        'cf', 'cg', 'ch', 'ci', 'ck', 'cl', 'cm', 'cn', 'co', 'cr', 'cu', 'cv', 'cx', 'cy', 'cz', 'de', 'dj', 'dk', 'dm',
                        'do', 'dz', 'ec', 'ee', 'eg', 'eh', 'er', 'es', 'et', 'eu', 'fi', 'fj', 'fk', 'fm', 'fo', 'fr', 'ga', 'gd', 'ge',
                        'gf', 'gg', 'gh', 'gi', 'gl', 'gm', 'gn', 'gp', 'gq', 'gr', 'gs', 'gt', 'gu', 'gw', 'gy', 'hk', 'hm', 'hn', 'hr',
                        'ht', 'hu', 'id', 'ie', 'il', 'im', 'in', 'io', 'iq', 'ir', 'is', 'it', 'je', 'jm', 'jo', 'jp', 'ke', 'kg', 'kh',
                        'ki', 'km', 'kn', 'kp', 'kr', 'kw', 'ky', 'kz', 'la', 'lb', 'lc', 'li', 'lk', 'lr', 'ls', 'lt', 'lu', 'lv', 'ly',
                        'ma', 'mc', 'md', 'mg', 'mh', 'mk', 'ml', 'mm', 'mn', 'mo', 'mp', 'mq', 'mr', 'ms', 'mt', 'mu', 'mv', 'mw', 'mx',
                        'my', 'mz', 'na', 'nc', 'ne', 'nf', 'ng', 'ni', 'nl', 'no', 'np', 'nr', 'nu', 'nz', 'om', 'pa', 'pe', 'pf', 'pg',
                        'ph', 'pk', 'pl', 'pm', 'pn', 'pr', 'ps', 'pt', 'pw', 'py', 'qa', 're', 'ro', 'ru', 'rw', 'sa', 'sb', 'sc', 'sd',
                        'se', 'sg', 'sh', 'si', 'sj', 'sk', 'sl', 'sm', 'sn', 'so', 'sr', 'st', 'sv', 'sy', 'sz', 'tc', 'td', 'tf', 'tg',
                        'th', 'tj', 'tk', 'tl', 'tm', 'tn', 'to', 'tp', 'tr', 'tt', 'tv', 'tw', 'tz', 'ua', 'ug', 'uk', 'um', 'us', 'uy',
                        'uz', 'va', 'vc', 've', 'vg', 'vi', 'vn', 'vu', 'wf', 'ws', 'ye', 'yt', 'yu', 'yr', 'za', 'zm', 'zw']
        sec_domain_list = ['com', 'in', 'gov', 'org', 'web', 'ac', 'co', 'mil', 'net', 'edu', 'int', 'biz']
        domain_list = []
        sql1 = "select distinct(t1.domain) from " \
               "(select distinct(host) as domain from dpilog_http where celeryId = '%s'  and ts >= '%s' and ts <= '%s' " \
               " union all " \
               "select distinct(server_name) as domain from dpilog_ssl where celeryId = '%s' and ts >= '%s' and ts <= '%s') as t1" \
               % (celeryId, date2, date1, celeryId, date2, date1)
        sql2 = "select distinct(t2.domain) from " \
               "(select distinct(host) as domain from dpilog_http where celeryId = '%s' and ts < '%s' " \
               " union all " \
               "select distinct(server_name) as domain from dpilog_ssl where celeryId = '%s' and ts < '%s') as t2" \
               % (celeryId, date2, celeryId, date2)
        # sql = "select * from (" + sql1 + " where t1.domain not in (" + sql2 + ")) as t3"
        LOG.info("--------" + sql1)
        LOG.info("--------" + sql2)
        set2 = set()
        msg, ret_list1 = self.get_val_from_database(sql1)
        msg, ret_list2 = self.get_val_from_database(sql2)
        for domain_info in ret_list2:
            domain = domain_info['domain']
            if domain and domain.__contains__('.'):
                flag = self.judge_legal_ipv4(domain)
                if not flag:
                    doarr = domain.split(".")
                    if len(doarr) < 3:
                        domain_match = doarr[-2] + "." + doarr[-1]
                    elif doarr[-1] in country_list and doarr[-2] in sec_domain_list:
                        domain_match = doarr[-3] + "." + doarr[-2] + "." + doarr[-1]
                    elif doarr[-2] == "asso" and doarr[-1] == "fr":
                        domain_match = doarr[-3] + "." + doarr[-2] + "." + doarr[-1]
                    else:
                        domain_match = doarr[-2] + "." + doarr[-1]
                    set2.add(domain_match)
        for domain_info in ret_list1:
            domain = domain_info['domain']
            if domain and domain.__contains__('.') and not domain.endswith('gov.cn'):
                flag = self.judge_legal_ipv4(domain)
                if not flag:
                    doarr = domain.split(".")
                    if len(doarr) < 3:
                        domain_match = doarr[-2] + "." + doarr[-1]
                    elif doarr[-1] in country_list and doarr[-2] in sec_domain_list:
                        domain_match = doarr[-3] + "." + doarr[-2] + "." + doarr[-1]
                    elif doarr[-2] == "asso" and doarr[-1] == "fr":
                        domain_match = doarr[-3] + "." + doarr[-2] + "." + doarr[-1]
                    else:
                        domain_match = doarr[-2] + "." + doarr[-1]
                    if domain_match not in set2 and domain_match not in white_list:
                        domain_rst = {}
                        domain_rst["domain"] = domain
                        domain_list.append(domain_rst)
        domain_list_info = {}
        domain_list_info['ts'] = date1[0:10]
        domain_list_info['domain_list'] = domain_list
        return domain_list_info

    def judge_legal_ipv4(self, one_str):
        '''
        简单的字符串判断ip
        '''
        if '.' not in one_str:
            return False
        elif one_str.count('.') != 3:
            return False
        else:
            flag = True
            one_list = one_str.split('.')
            for one in one_list:
                try:
                    one_num = int(one)
                    if one_num >= 0 and one_num <= 255:
                        pass
                    else:
                        flag = False
                except:
                    flag = False
            return flag
