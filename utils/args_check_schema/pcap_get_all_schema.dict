{
	"type": "object",
	"properties": {
	    "lastest": {
            "type": "string",
            "default": "0"
        },
		"dir": {
	    "type": "string",
	    "default":""
		},
	    "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10000",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        }
	}
}