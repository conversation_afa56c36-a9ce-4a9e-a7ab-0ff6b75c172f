import React, { useState, useEffect } from 'react';
import style from './style.less';
import { Form, Steps, Button, Input, Switch, Select, message, Checkbox, Spin, Tooltip } from 'antd';
import { securityPolicyInterface, addsecurityPolicy, editsecurityPolicy, testEmail } from '@/services/configs';
import { getpacpList } from '@/services/explore';
import { useHistory } from '@/hooks/global';
import { CheckCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { randomString } from '@/utils/utils';

const { Step } = Steps;
const { Option } = Select;
const { TextArea } = Input;
const EditForm = () => {
  const history = useHistory();
  const [form] = Form.useForm();
  const [isfinish, setisfinish] = useState(false);
  const [loading, setloading] = useState(false);
  const [eamilvisible, seteamilvisible] = useState(false);
  const [syslogvisible, setsyslogvisible] = useState(false);
  const [threatGwVisible, setThreatGwVisible] = useState(false);
  const [current, setcurrent] = useState(0);
  const [pcapFilesList, setpcapFilesList] = useState([]);
  const [interFace, setinterFace] = useState<any>([]);

  // 完成提交
  const onFinish = async () => {
    form.validateFields().then((value: any) => {
      console.log(555, value);
      if (value.policy_name === 'default_policy') {
        value.source_if_list = [];
      }
      if (value.threat_log) {
        value.threat_log = true;
      }

      if (value.email.is_enable) {
        value.email.is_enable = true;
        if (value.email.log_trigger_ip) {
          // 输入框字符串转数组
          value.email.log_trigger_ip = value.email.log_trigger_ip.split(',');
        }
      }
      if (value.syslog.is_enable) {
        value.syslog.is_enable = true;
        if (value.syslog.log_trigger_ip) {
          // 输入框字符串转数组
          value.syslog.log_trigger_ip = value.syslog.log_trigger_ip.split(',');
        }
      }
      if (value.threat_gw?.is_enable) {
        value.threat_gw.is_enable = true;
      }
      // 端口字符串转int类型
      if (value.syslog.server_port) {
        value.syslog.server_port = parseInt(value.syslog.server_port);
      }
      // 端口字符串转int类型
      if (value.email.server_port) {
        value.email.server_port = parseInt(value.email.server_port);
      }
      value.ioc_enable = Boolean(value.ioc_enable);
      value.model_enable = Boolean(value.model_enable);
      value.file_enable = Boolean(value.file_enable);
      // 源主机字符串转数组
      if (value.source_host_ip && value.source_host_ip.length) {
        if (typeof value.source_host_ip === 'string') {
          value.source_host_ip = value.source_host_ip.split(',');
        }
      } else {
        value.source_host_ip = [];
      }

      // 密码转base64
      if (value.email.password) {
        let password = `${value.email.password}${randomString(3)}`;
        value.email.password = window.btoa(unescape(encodeURIComponent(password)));
      }
      if (sessionStorage.getItem('securityPolicyStatus') === 'edit') {
        value.unique_id = JSON.parse(sessionStorage.getItem('securityPolicyForm')).unique_id;
        value.action = 'update';
        value = {
          ...JSON.parse(sessionStorage.getItem('securityPolicyForm')),
          ...value,
        };
        editsecurityPolicy(value).then(res => {
          if (res.flag) {
            setcurrent(current + 1);
            setisfinish(true);
            setTimeout(() => {
              message.success(res.message);
              history.push(`/app/mica/securityPolicy`);
            }, 2000);
            sessionStorage.removeItem('securityPolicyStatus');
            sessionStorage.removeItem('securityPolicyForm');
          } else {
            message.error(res.message);
          }
        });
      } else {
        value.action = 'add';
        addsecurityPolicy(value).then(res => {
          if (res.flag) {
            setcurrent(current + 1);
            setisfinish(true);
            setTimeout(() => {
              message.success(res.message);
              history.push(`/app/mica/securityPolicy`);
            }, 2000);
            sessionStorage.removeItem('securityPolicyStatus');
            sessionStorage.removeItem('securityPolicyForm');
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };

  // 下一步
  const next = () => {
    switch (current) {
      case 0:
        let name = form.getFieldValue('policy_name');
        let source = form.getFieldValue('source_if_list');
        if (!name || !source || !source.length) {
          message.error('请填写完信息后点击下一步');
          return;
        }
        break;
      case 1:
        let rule = form.getFieldValue('rule_groups');
        if (!rule || !rule.length) {
          message.error('请填写完信息后点击下一步');
          return;
        }
        break;
      case 2:
        let mail = form.getFieldValue(['email', 'log_trigger_ip']);
        let syslog = form.getFieldValue(['syslog', 'log_trigger_ip']);
        if (eamilvisible) {
          if (!mail.length) {
            message.error('请填写完信息后点击下一步');
            return;
          }
        }
        if (syslogvisible) {
          if (!syslog.length) {
            message.error('请填写完信息后点击下一步');
            return;
          }
        }
        break;

      default:
        break;
    }

    if (current === 2) {
      if (eamilvisible || syslogvisible || threatGwVisible) {
        setcurrent(current + 1);
      } else {
        onFinish();
      }
    } else {
      setcurrent(current + 1);
    }
  };

  // 上一步
  const back = () => {
    setcurrent(current - 1);
  };

  // 展示邮箱的ip配置
  const showEmailIp = (value: any) => {
    if (value.target.checked) {
      seteamilvisible(true);
    } else {
      seteamilvisible(false);
    }
  };

  // 展示日志外发的ip填写
  const showSyslogIp = (value: any) => {
    if (value.target.checked) {
      setsyslogvisible(true);
    } else {
      setsyslogvisible(false);
    }
  };

  // 改变是否联动阻断 显示地址配置
  const changeThreatGw = (value: any) => {
    if (value.target.checked) {
      setThreatGwVisible(true);
    } else {
      setThreatGwVisible(false);
    }
  };

  // 测试邮箱配置连接
  const handleTest = (submit: boolean) => {
    if (!eamilvisible) {
      onFinish();
    } else {
      form.validateFields().then((value: any) => {
        setloading(true);
        let password = `${value.email.password}${randomString(3)}`;
        let data = {
          mail_server: value.email.mail_server,
          server_port: parseInt(value.email.server_port),
          account: value.email.account,
          password: window.btoa(unescape(encodeURIComponent(password))),
        };
        testEmail(data).then(res => {
          setloading(false);
          if (res.flag) {
            message.success(res.message);
            if (submit) {
              onFinish();
            }
          } else {
            message.error(res.message);
          }
        });
      });
    }
  };

  const getPacpSelect = () => {
    getpacpList({ page: 1 }).then(res => {
      if (res.flag) {
        setpcapFilesList(res.data.detail);
      }
    });
  };

  const getinterFace = () => {
    securityPolicyInterface().then(res => {
      if (res.flag) {
        if (form.getFieldValue('policy_name') === 'default_policy') {
          res.data.push('all');
        }

        setinterFace(res.data);
      } else {
        message.error(res.message);
      }
    });
  };

  useEffect(() => {
    switch (current) {
      case 0:
        getinterFace();
        break;
      case 1:
        getPacpSelect();
        break;

      default:
        break;
    }
  }, [current]);

  useEffect(() => {
    let status = sessionStorage.getItem('securityPolicyStatus');
    if (status === 'edit') {
      let value = JSON.parse(sessionStorage.getItem('securityPolicyForm'));

      if (value.email.is_enable) {
        seteamilvisible(true);
        value.email.log_trigger_ip = value.email.log_trigger_ip.toString();
      }
      if (value.syslog.is_enable) {
        setsyslogvisible(true);
        value.syslog.log_trigger_ip = value.syslog.log_trigger_ip.toString();
      }
      if (value.threat_gw?.is_enable) {
        setThreatGwVisible(true);
      }
      if (value.email.password) {
        // 密码base64 解密
        value.email.password = decodeURIComponent(escape(window.atob(value.email.password)));
        value.email.password = value.email.password.substring(0, value.email.password.length - 3);
      }
      form.setFieldsValue(value);
    }
  }, []);

  const formItemLayout = {
    labelCol: {
      span: 6, // * ≥576px
    },
    wrapperCol: {
      span: 9,
    },
  };
  return (
    <div className={style.page}>
      <Spin spinning={loading}>
        <Steps current={current}>
          <Step title="源接口选择" />
          <Step title="安全能力集选择" />
          <Step title="响应动作配置" />
          {eamilvisible || syslogvisible || threatGwVisible ? <Step title="地址配置" /> : null}
          <Step title="完成" />
        </Steps>
        <Form className={style.form_content} {...formItemLayout} form={form} onFinish={onFinish}>
          <div className={style.form_box} style={{ visibility: current === 0 ? 'visible' : 'hidden' }}>
            <Form.Item
              label="策略名"
              name="policy_name"
              rules={[
                {
                  required: true,
                  message: '请填写',
                },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="源接口"
              name="source_if_list"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Select disabled={form.getFieldValue('policy_name') === 'default_policy'} allowClear mode="multiple">
                {interFace.map((item: any) => {
                  return (
                    <Option key={item} value={item}>
                      {item}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item
              label="源主机"
              name="source_host_ip"
              extra="仅检测指定IP(网段)主机的流量，填写格式如：********/24, *******, *******"
            >
              <TextArea row={4} />
            </Form.Item>
            <div className={style.center_box}>
              <Button
                type="primary"
                className={style.btn}
                onClick={() => {
                  history.go(-1);
                }}
              >
                返回
              </Button>
              <Button type="primary" onClick={next}>
                下一步
              </Button>
            </div>
          </div>
          <div className={style.form_box} style={{ visibility: current === 1 ? 'visible' : 'hidden' }}>
            <Form.Item
              label="规则组"
              name="rule_groups"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Select allowClear mode="multiple">
                {pcapFilesList.map((item: { groupId: string; groupName: string }) => {
                  return (
                    <Option value={item.groupName} key={item.groupId}>
                      {item.groupName}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item label="情报匹配" initialValue={true}  valuePropName="checked" name="ioc_enable">
              <Switch defaultChecked />
            </Form.Item>
            <Form.Item label="模型匹配" initialValue={true} valuePropName="checked" name="model_enable">
              <Switch defaultChecked />
            </Form.Item>
            <Form.Item label="文件匹配" initialValue={true} valuePropName="checked" name="file_enable">
              <Switch defaultChecked />
            </Form.Item>
            <div className={style.center_box}>
              <Button
                type="primary"
                className={style.btn}
                onClick={() => {
                  history.go(-1);
                }}
              >
                返回
              </Button>
              <Button type="primary" className={style.btn} onClick={back}>
                上一步
              </Button>
              <Button type="primary" onClick={next}>
                下一步
              </Button>
            </div>
          </div>
          <div className={style.form_box} style={{ visibility: current === 2 ? 'visible' : 'hidden' }}>
            <Form.Item label="日志告警" initialValue={true} name="threat_log" valuePropName="checked">
              <Checkbox disabled></Checkbox>
            </Form.Item>

            <Form.Item name={['email', 'is_enable']} initialValue={false} label="Email" valuePropName="checked">
              <Checkbox onChange={showEmailIp}></Checkbox>
            </Form.Item>
            {eamilvisible ? (
              <Form.Item
                label="关联IP"
                name={['email', 'log_trigger_ip']}
                extra="0.0.0.0表示所有ip,支持ip段和单个ip，请用小写,分隔 列如：*******,*******/24"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            ) : null}
            <Form.Item label="SysLog" name={['syslog', 'is_enable']} initialValue={false} valuePropName="checked">
              <Checkbox onChange={showSyslogIp}></Checkbox>
            </Form.Item>
            {syslogvisible ? (
              <Form.Item
                label="关联IP"
                name={['syslog', 'log_trigger_ip']}
                extra="0.0.0.0表示所有ip,支持ip段和单个ip，请用小写,分隔 列如：*******,*******/24"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            ) : null}
            <Form.Item
              label={
                <span>
                  联动阻断
                  <Tooltip title="仅支持与威胁情报网关进行联动阻断">
                    <QuestionCircleOutlined style={{ marginLeft: '5px' }} />
                  </Tooltip>
                </span>
              }
              valuePropName="checked"
              initialValue={false}
              name={['threat_gw', 'is_enable']}
            >
              <Checkbox onChange={changeThreatGw}></Checkbox>
            </Form.Item>
            {isfinish ? null : (
              <div className={style.center_box}>
                <Button
                  type="primary"
                  className={style.btn}
                  onClick={() => {
                    history.go(-1);
                  }}
                >
                  返回
                </Button>
                <Button type="primary" className={style.btn} onClick={back}>
                  上一步
                </Button>
                <Button type="primary" onClick={next}>
                  下一步
                </Button>
              </div>
            )}
          </div>
          <div className={style.form_box} style={{ visibility: current === 3 ? 'visible' : 'hidden' }}>
            {eamilvisible ? (
              <div>
                <p>邮箱设置</p>
                <Form.Item
                  name={['email', 'mail_server']}
                  label="SMTP服务器地址"
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="SMTP端口"
                  name={['email', 'server_port']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                    {
                      pattern: /^[1-9]\d*$/,
                      message: '请输入正确端口',
                    },
                    {
                      validator: (_, value) => {
                        value = parseInt(value);
                        if (value < 0 || value > 65535) {
                          console.log('error');

                          return Promise.reject('请输入正确端口');
                        } else {
                          return Promise.resolve();
                        }
                      },
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="邮箱账号"
                  name={['email', 'account']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                    {
                      pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                      message: '邮箱格式不正确',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="邮箱密码"
                  name={['email', 'password']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                  ]}
                >
                  <Input.Password />
                </Form.Item>
                <Form.Item
                  label="收件人"
                  name={['email', 'receiver']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                    {
                      pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
                      message: '收件人格式不正确',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                <div className={style.btn_center}>
                  <Button
                    type="primary"
                    onClick={() => {
                      handleTest(false);
                    }}
                  >
                    测试连接
                  </Button>
                </div>
              </div>
            ) : null}
            {syslogvisible ? (
              <div style={{ marginBottom: '30px' }}>
                <p>Syslog配置</p>
                <Form.Item
                  label="Syslog服务器IP"
                  name={['syslog', 'syslog_server']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="端口"
                  name={['syslog', 'server_port']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                    {
                      pattern: /^[1-9]\d*$/,
                      message: '请输入正确端口',
                    },
                    {
                      validator: (_, value) => {
                        value = parseInt(value);
                        if (value < 0 || value > 65535) {
                          console.log('error');

                          return Promise.reject('请输入正确端口');
                        } else {
                          return Promise.resolve();
                        }
                      },
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </div>
            ) : null}
            {threatGwVisible ? (
              <div>
                <p>联动阻断配置</p>
                <Form.Item
                  label="威胁情报网关ip"
                  name={['threat_gw', 'server']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="token"
                  name={['threat_gw', 'token']}
                  rules={[
                    {
                      required: true,
                      message: '请填写',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </div>
            ) : null}
            {isfinish ? null : (
              <div className={style.center_box}>
                <Button
                  type="primary"
                  className={style.btn}
                  onClick={() => {
                    history.go(-1);
                  }}
                >
                  返回
                </Button>
                <Button type="primary" className={style.btn} onClick={back}>
                  上一步
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    handleTest(true);
                  }}
                >
                  下一步
                </Button>
              </div>
            )}
          </div>
          {isfinish ? (
            <div>
              <div className={style.center_box}>
                <CheckCircleOutlined style={{ fontSize: 100, color: '#33cc33' }} />
              </div>
              <p style={{ textAlign: 'center' }}>完成(即将跳转回安全策略)</p>
            </div>
          ) : null}
        </Form>
      </Spin>
    </div>
  );
};
export default EditForm;
