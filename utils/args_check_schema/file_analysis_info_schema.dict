{
	"type": "object",
	"required": [],
	"properties": {
        "startTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
		},
        "endTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
		},
        "srcIp": {
            "type": "string",
            "default": "",
		},
        "dstIp": {
            "type": "string",
            "default": "",
		},
        "fileName": {
            "type": "string",
            "default": "",
		},
        "fileType": {
            "type": "string",
            "default": "",
		},
        "protocol": {
            "type": "string",
            "default": "",
		},
        "threatLevel": {
            "type": "string",
            "default": "",
		},
        "celeryId": {
            "type": "string",
            "default": "",
		},
        "ip": {
            "type": "string",
            "enum": ["src_ip", "dst_ip"],
            "default": "src_ip",
		}
	}
}