import unittest
from HTMLTestRunner import *
from api_1_0.tests.back_explore.test_opreate import *
from api_1_0.tests.back_feature.test_feature_group_manager import *

if __name__ == '__main__':
    # unittest.main()
    report = './api_1_0/tests/report/report.html'
    with open(report, 'wb') as f:
        runner = HTMLTestRunner(f, verbosity=2, title='UnitTest', description='单元测试报告')
        # 单个探索任务获取
        runner.run(TestExploreTaskWithId('test_get_1'))
        runner.run(TestExploreTaskWithId('test_get_2'))
        # 探索任务修改
        runner.run(TestExploreTaskWithId('test_put_1'))
        runner.run(TestExploreTaskWithId('test_put_2'))
        runner.run(TestExploreTaskWithId('test_put_3'))
        runner.run(TestExploreTaskWithId('test_put_4'))
        runner.run(TestExploreTaskWithId('test_put_5'))
        runner.run(TestExploreTaskWithId('test_put_6'))
        # 探索任务删除
        runner.run(TestExploreTaskWithId('test_delete_1'))
        runner.run(TestExploreTaskWithId('test_delete_2'))
        # 探索任务创建
        runner.run(TestExploreTask('test_post_1'))
        runner.run(TestExploreTask('test_post_2'))
        runner.run(TestExploreTask('test_post_3'))
        runner.run(TestExploreTask('test_post_4'))
        runner.run(TestExploreTask('test_post_5'))
        # 获取所有探索任务
        runner.run(TestExploreTask('test_get_1'))
        runner.run(TestExploreTask('test_get_2'))

        # 单个特征组获取
        runner.run(TestFeatureGroupWithId('test_get_1'))
        runner.run(TestFeatureGroupWithId('test_get_2'))
        # 特征组修改
        runner.run(TestFeatureGroupWithId('test_put_1'))
        runner.run(TestFeatureGroupWithId('test_put_2'))
        # 特征组删除
        runner.run(TestFeatureGroupWithId('test_delete_1'))
        runner.run(TestFeatureGroupWithId('test_delete_2'))
        # 特征组创建
        runner.run(TestFeatureGroup('test_post_1'))
        runner.run(TestFeatureGroup('test_post_2'))
        runner.run(TestFeatureGroup('test_post_3'))
        runner.run(TestFeatureGroup('test_post_4'))
        # 获取所有特征组
        runner.run(TestFeatureGroup('test_get_1'))
