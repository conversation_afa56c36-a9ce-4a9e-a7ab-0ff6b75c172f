
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

import {
  ApiResponse,
  EmailAttachmentListParams,
  EmailDetail,
  EmailDetailParams,
  EmailAttachmentReportDetail,
  EmailFileDetailParams,
  EmailListItem,
  EmailListParams,
  EmailSendRecvInfo,
  EmailSendRecvInfoParams,
  TimeRange,
  EmailAttachmentItem,
  EmailTargetedAttackListItem,
  EmailTargetedAttackParams,
  ReportDetail
} from '@/pages/customized/types';

export type FileAnalysisCommonParams = {
  start_time: string | number;
  end_time: string | number;
  src_ip?: string;
  dst_ip?: string;
  filename?: string;
  tags?: string[];
  threat_name?: string[];
  application?: string[]; // 应用层协议
  md5?: string;
  source?: string[];
  family?: string[]; // 病毒家族
}

export type FileAnalysisListParams = FileAnalysisCommonParams & {
  order?: string;
  page: string | number;
  pageSize: string | number;
}

export type ExportFileReportParams = Omit<FileAnalysisListParams, 'page' | 'pageSize'>

const apiVersion = 'v1';

// 获取文件分析的下拉列表选项
export const getFileAnalysisOptions = () => {
  return request('file_analysis/config/get', {
    apiVersion,
    method: 'get',
  });
};

// 文件分析统计图表数据
export const getFileAnalysisStaticsData = (data: FileAnalysisCommonParams) => {
  return request(`file_analysis/aggregate`, {
    apiVersion,
    method: 'post',
    data,
  });
};

// 文件分析列表
export const getFileList = (data: FileAnalysisListParams) => {
  return request('file_analysis/search', {
    method: 'post',
    data,
    apiVersion,
  });
};

// 文件分析列表下载
export const downloadFileReport = (data: ExportFileReportParams) => {
  return request('file_analysis/report', {
    apiVersion,
    data,
    method: 'post',
    responseType: 'blob',
  });
};


// 邮件分析接口

// 获取邮件列表
export const fetchEmailList = (data: EmailListParams, signal?: AbortSignal) => {
  return request<ApiResponse<{ total: number; detail: EmailListItem[] }>>(
    `mail_analysis/email_list`,
    {
      apiVersion,
      data,
      method: 'post',
      signal,
    });
};

// 获取邮件详情列表
export const fetchEmailDetail = (data: EmailDetailParams) => {
  return request<ApiResponse<{ detail: EmailDetail, total: number }>>(
    `mail_analysis/email_detail${parseQueryString(data)}`,
    {
      apiVersion,
      method: 'get',
    });
};

// 获取邮件威胁文件详情
export const fetchEmailAttachmentDetail = (data: EmailFileDetailParams) => {
  return request<ApiResponse<[EmailAttachmentReportDetail]>>(
    `threat_analyse/file_detail${parseQueryString(data)}`,
    {
      apiVersion,
      method: 'get',
    });
};

// 邮件收发信息获取
export const fetchEmailSendRecvInfo = (data: EmailSendRecvInfoParams) => {
  return request<ApiResponse<{ detail: EmailSendRecvInfo, total: number }>>(
    `mail_analysis/send_rsv_info`,
    {
      apiVersion,
      data,
      method: 'post',
    });
};

// 邮件附件列表获取
export const fetchEmailAttachmentList = (data: EmailAttachmentListParams, signal?: AbortSignal) => {
  return request<ApiResponse<{ detail: EmailAttachmentItem[], total: number }>>(
    `mail_analysis/attachments`,
    {
      apiVersion,
      data,
      method: 'post',
      signal
    });
};

// 邮件定向攻击列表获取
export const fetchEmailTargetedAttackList = (data: EmailTargetedAttackParams, signal?: AbortSignal) => {
  return request<ApiResponse<{ detail: EmailTargetedAttackListItem[], total: number }>>(
    `threat_analyse/attacked${parseQueryString(data)}`,
    {
      apiVersion,
      method: 'get',
      signal
    });
};

// 邮件收件组织列表获取
export const fetchRecvOrgList = (data: TimeRange) => {
  return request<ApiResponse<string[]>>(
    `threat_analyse/rsv_org${parseQueryString(data)}`,
    {
      apiVersion,
      method: 'get',
    });
};


// 邮件附件下载（新）
export const getExportFileUrl = (data: { filePath: string }) => {
  return `log_search/file/report/binexport${parseQueryString(data)}`
};

// 获取文件详情（新）
export const fetchFileDetail = (data: { filePath: string }) => {
  return request<ApiResponse<ReportDetail>>(
    `log_search/file/report/detail${parseQueryString(data)}`,
    {
      apiVersion,
      method: 'get',
    });
};
