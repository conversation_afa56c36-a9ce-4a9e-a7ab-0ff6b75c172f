import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Button, Table, Modal, message, Drawer, Spin, Card, Col, Row } from 'antd';
import moment from 'moment';
import { SearchOutlined } from '@ant-design/icons';
import { hasIn } from 'lodash';
import './style.less';
import { getOnlineTable, delOnlineData, createOnline, getOnlineDetail, tryConnect, editeOnline } from '@/services/explore';
import SourceForm from './sourceForm';
import OnlineForm from './onlineForm';

const { confirm } = Modal;

const Online = (Props: any) => {
  const childForm = useRef<any>();
  const dirForm = useRef<any>();
  const [form] = Form.useForm();
  const [dirs, setdirs] = useState([]);
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox');
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const [total, settotal] = useState(0);
  const [isAdd, setisAdd] = useState(false);
  const [visilble, setvisilble] = useState(false);
  const [connectObj, setConnectObj] = useState({});
  const [chidlValue, setchidlValue] = useState({
    port: 21,
    serverType: 'ftp',
    enable: true,
  });
  const [tableData, settableData] = useState<any>([]);
  const [loading, setloading] = useState(false);
  const [params, setparams] = useState({
    page: 1,
    pageSize: 10,
  });
  const [detailparams, setdetailparams] = useState({
    serverId: '',
    onlyDir: true,
  });
  const [dataList, setdataList] = useState([]);
  useEffect(() => {
    getData(params);
  }, [params]);
  useEffect(() => {
    if (loading) {
      getOnlineDetail(detailparams).then(res => {
        setdataList(res.data.detail);
        // setsourceVisible(true);
        setloading(false);
        if (res.flag) {
        } else {
          message.error(res.message);
        }
      });
    }
  }, [loading]);
  const columns = [
    {
      title: '数据源名称',
      dataIndex: 'aliasName',
    },
    {
      title: '用途',
      dataIndex: 'type',
      render: (e) => {
        const obj = {
          pcap: '流量包存放',
          other: '普通文件存放'
        }
        return <div>{obj[e]}</div>
      }
    },
    {
      title: '数据源类型',
      dataIndex: 'serverType',
      render: (t: any) => {
        return t === 'ftp' ? 'FTP服务器' : '网络文件共享服务器';
      },
    },
    {
      title: '远端服务器IP',
      dataIndex: 'host',
    },
    {
      title: '监控目录',
      dataIndex: 'monitor_dirs',
      render: (t: any) => {
        return (
          <div>
            {t.map((item: any) => {
              return (
                <p style={{ marginRight: 2 }} key={item}>
                  {item}
                </p>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '登录用户',
      dataIndex: 'username',
    },
    // {
    //   title: '类型',
    //   dataIndex: 'serverType',
    //   render: (t: string) => (t === 'ftp' ? 'FTP服务器' : '网络文件共享服务器'),
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      render: (t: number) => {
        return <span>{moment(t).format('YYYY-MM-DD')}</span>;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '200px',
      render: (text: any, record: { serverId: string }) => {
        const { serverId } = record;
        let status = '';
        if (hasIn(connectObj, serverId)) {
          status = connectObj[serverId];
        }
        return (
          <div>
            <Button
              type="link"
              style={{ padding: 0 }}
              onClick={() => {
                handleEdit(record);
              }}
            >
              查看
            </Button>
            <Button
              type="link"
              disabled={status === 'loading'}
              onClick={() => {
                tryConnectFun(serverId);
              }}
            >
              尝试连接
              {status}
            </Button>
          </div>
        );
      },
    },
  ];
  const tryConnectFun = (serverId: string) => {
    setConnectObj({ ...connectObj, [serverId]: '中' });
    tryConnect({ serverId }).then(res => {
      if (res.flag && res.data?.status !== 'disconnected') {
        setConnectObj({ ...connectObj, [serverId]: '(成功)' });
      } else {
        setConnectObj({ ...connectObj, [serverId]: '(失败)' });
      }
    });
  };
  const rowSelection = {
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys,
  };

  // 分页change事件
  const pagination = {
    total: total,
    current: params.page || 1,

    pageSize: params.pageSize,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const getData = (data: object) => {
    let param = { ...data, ...params };
    getOnlineTable(param).then(res => {
      if (res.flag) {
        setselectedRowKeys([]);
        settableData(res.data.detail);
        settotal(res.data.count);
      }
    });
  };

  const showDetail = (value: { serverId: string }) => {
    detailparams.serverId = value.serverId;
    setdetailparams({ ...detailparams });
  };
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      setparams({ ...params, ...value });
    });
  };
  const handeDel = () => {
    if (!selectedRowKeys || !selectedRowKeys.length) {
      message.error('请先选择需要删除的目录');
      return;
    }
    let delArray: {
      serverId: any;
      userId: any;
    }[] = [];
    selectedRowKeys.forEach((item: any) => {
      let value = {
        serverId: tableData.filter(item1 => item1.aliasName === item)[0].serverId,
        userId: tableData.filter(item1 => item1.aliasName === item)[0].serverId,
      };
      delArray.push(value);
    });

    confirm({
      title: '确定删除选中的目录吗?',
      okType: 'danger',
      onOk() {
        delOnlineData({
          fileList: delArray,
        }).then((res: any) => {
          if (res.flag) {
            message.success('操作成功');
            setselectedRowKeys([]);
            getData({});
          } else {
            message.error(res.message);
          }
        });
      },
      onCancel() { },
    });
  };
  const handleEdit = (value: any) => {
    setchidlValue(value);
    setisAdd(false);
    setvisilble(true);
    setdetailparams({ serverId: value.serverId, onlyDir: true });
  };
  const handleAdd = () => {
    setdetailparams({ serverId: '', onlyDir: true });
    setchidlValue({
      port: 21,
      serverType: 'ftp',
      enable: true,
    });
    setisAdd(true);
    setvisilble(true);
  };
  const getDirs = (value: any) => {
    setdirs(value);
  };
  const handleOk = () => {
    if (!dirForm.current) {
      message.error('请连接后选择数据源');
      return;
    }
    childForm.current.validateFields().then((value: any) => {
      dirForm.current.validateFields().then((values: any) => {
        value.serverId = detailparams.serverId;
        value.monitorDirs = values.monitorDirs;
        // 新增
        if (isAdd) {
          delete value.onlyDir;
          createOnline(value).then(res => {
            if (res.flag) {
              setvisilble(false);
              message.success(res.message);
              getData({});
            } else {
              message.error(res.message);
            }
          });
        } else {
          setvisilble(false);
        }
      });
    });
  };

  const handleCancel = () => {
    setvisilble(false);
    getData({});
  };
  return (
    <Spin spinning={loading}>
      <Form form={form} onFinish={onSubmit} layout="inline">
        <Row>
          <Col span={6}>
            <Form.Item label="数据源名称" name="foldName">
              <Input placeholder="请输入数据源名称" />
            </Form.Item>
          </Col>
          <Col offset={12} span={6}>
            <Form.Item>
              <div className="group_btns">
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}
                  className='margin_right_10'>
                  搜索
                </Button>
                <Button
                  onClick={handleAdd}
                  className='margin_right_10'
                  type="primary"
                  ghost
                >
                  新增
                </Button>
                <Button
                  onClick={handeDel}
                  type="primary"
                  ghost
                  danger
                >
                  删除
                </Button>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowSelection={{
          type: selectionType,
          ...rowSelection,
        }}
        pagination={pagination}
        rowKey="aliasName"
        columns={columns}
        dataSource={tableData}
      />
      <Modal
        width={1200}
        destroyOnClose={true}
        title={isAdd ? '新增数据源' : '查看数据源'}
        visible={visilble}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <div style={{ display: 'flex' }}>
          <Card style={{ width: '50%' }}>
            <OnlineForm
              detailparams={detailparams}
              Formvalue={chidlValue}
              showDetail={showDetail}
              isAdd={isAdd}
              ref={childForm}
            />
          </Card>
          <Card style={{ width: '50%' }}>
            {detailparams.serverId ? (
              <SourceForm
                ref={dirForm}
                isAdd={isAdd}
                getDirs={getDirs}
                chidlValue={chidlValue}
                detailparams={detailparams}
                dataList={dataList}
              />
            ) : null}
          </Card>
        </div>
      </Modal>
    </Spin>
  );
};
export default Online;
