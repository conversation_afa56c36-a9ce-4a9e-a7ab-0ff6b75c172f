# -*- coding: utf-8 -*-
# @Time    : 2020-04-14 14:35
# <AUTHOR> wu
# @File    : pcap.py
# @Software: PyCharm

"""module explore"""
import os
import re
import time
import base64
import hashlib
import json
from dateutil import parser
from flask import request
from flask_restful import Resource
from config.config import Ndr<PERSON>og, CustomizedPcapPath
from utils.logger import get_ndr_logger
from utils.utils import flask_response, ftp_server_reconnect, ftp_server_disconnect, pcap_fix
from utils.database import MongoDB
from utils.param_check import Validator, check_flask_args
from utils.best_show_size import size_format
from utils.json_format import JSONEncoder
from api_1_0.auth.auths import Authenticate
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def create_ftp_server_id():
    """create ftp server id"""
    md5_str = hashlib.md5(str(time.clock()).encode('utf-8'))
    return md5_str.hexdigest()


def connect_to_server():
    """
    :return:
    """
    mongodb = MongoDB("ndr")
    ftp_server = mongodb.find_one('ftp_server', {})
    if not ftp_server:
        return "FTP server not configured.", False

    ftp, rst = ftp_server_reconnect(ftp_server['host'], ftp_server['port'],
                                    ftp_server['username'], ftp_server['password'])
    if not rst:
        ftp_server["status"] = False
    else:
        ftp_server["status"] = True

    return ftp, rst


class FTPServer(Resource):
    """
    自定义 pcpap 包处理
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @check_flask_args(Validator("ftp_server_schema"), request)
    def post(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        count = self.mongodb.find('ftp_server', {}).count()
        if count >= 1:
            self.mongodb.drop('ftp_server')

        self.mongodb.insert_one('ftp_server', {
            "host": kwargs["host"],
            "port": kwargs["port"],
            "username": kwargs["username"],
            "password": kwargs["password"],
        })
        ndr_log_to_box(NdrLog.Type.OPERATE, "离线FTP服务器已更新。")

        ftp, rst = ftp_server_reconnect(kwargs["host"], kwargs["port"], kwargs["username"], kwargs["password"])
        print(kwargs["host"], kwargs["port"], kwargs["username"], kwargs["password"], flush=True)
        if not rst:
            LOG.error("FTP server login failed.Reason: %s" % str(ftp))
            ndr_log_to_box(NdrLog.Type.OPERATE, "FTP 服务器[%s]添加失败：%s。" % (kwargs["host"], str(ftp)))

            return flask_response("连接失败！", False, {"msg": str(ftp)})

        ftp_server_disconnect(ftp)

        return flask_response("连接成功。", True, {})

    def get(self):
        """
        从mongodb数据库获取配置
        """
        file_obj = self.mongodb.find_one('ftp_server', {}, {"_id": 0})
        if file_obj is None:
            return flask_response("", True, {})

        return flask_response("", True, {key: str(val) for key, val in file_obj.items()})


class FTPServerDirTree(Resource):
    """
    FTP 服务器文件树查询
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()
        self.dir_rst = []

    def search_file(self, start_info, ftp):
        """
        :param start_info:
        :param ftp:
        :return:
        """
        ftp.cwd(start_info["fileName"])
        dir_res = []
        value = 0
        ftp.dir(start_info["fileName"], dir_res.append)
        for file_dir in dir_res:
            if file_dir.startswith("d"):
                dir_info = {"fileName": ftp.pwd() + "/" + file_dir.split(" ")[-1],
                            "totalSize": 0,
                            "children": []}
                self.search_file(dir_info, ftp)
                start_info["children"].append(dir_info)
                ftp.cwd('..')
            else:
                val = file_dir.split(" ")[-1]
                value += ftp.size(val)
                file_name = (ftp.pwd() + "/" + val).strip()
                # 只展示数据包
                if not file_name.endswith(('.pcap', '.cap', '.pcapng')):
                    continue

                file_time = parser.parse(ftp.voidcmd("MDTM %s" % (ftp.pwd() + "/" + val))[4:].strip())

                size_str = size_format(ftp.size(val) / 1024)
                # 是否允许下载
                downloadable = True
                if size_str.__contains__("G"):
                    if int(size_str[0:1]) >= 2:
                        downloadable = False
                if size_str.__contains__("T"):
                    downloadable = False

                file_info = {
                    "time": int(time.mktime(file_time.timetuple()) * 1000),
                    "size": size_str,
                    "downloadable": downloadable,
                    "fileName": file_name
                }

                start_info["totalSize"] += ftp.size(val)
                start_info["children"].append(file_info)
        start_info["totalSize"] = size_format(start_info["totalSize"] / 1024)
        return start_info

    # get ftp file list
    @check_flask_args(Validator("ftp_get_dir_tree_schema"), request)
    def get(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        ftp, rst = connect_to_server()
        if not rst:
            if ftp in ("FTP server not configured.", ):
                return flask_response("", True, [])
            return flask_response("FTP server is unreachable: %s." % str(ftp), False, {})
        self.dir_rst = {"fileName": kwargs["dir"] if kwargs["dir"] != "./" else ftp.pwd(),
                        "totalSize": 0,
                        "children": []}
        dir_info = self.search_file(self.dir_rst, ftp)
        ftp_server_disconnect(ftp)
        rst = JSONEncoder().encode(dir_info)
        return flask_response("", True, [json.loads(rst)])


class FTPServerFileDownload(Resource):
    """
    FTP 服务器文件下载
    """

    def __init__(self):
        self.mongodb = MongoDB("ndr")
        self.user_name, self.user_id, self.user_ip = Authenticate('').get_current_user()
        self.buffer_size = 1024

    # download ftp file
    @check_flask_args(Validator("ftp_download_schema"), request)
    def post(self, **kwargs):
        """
        :param kwargs:
        :return:
        """
        dir_name = kwargs["dir"]
        ftp, rst = connect_to_server()
        if not rst:
            return flask_response("The FTP server is unreachable: %s." % ftp, False, {})
        try:
            for full_name in kwargs["fileList"]:
                file_name = full_name.split('/')[-1]
                pcap_file_name = CustomizedPcapPath.Path + dir_name + '/' + file_name
                if os.path.exists(pcap_file_name):
                    os.remove(pcap_file_name)
                with open(pcap_file_name, "wb") as write_file:
                    ftp.retrbinary('RETR {0}'.format(full_name), write_file.write, self.buffer_size)

                # fix the pcapfile if needed
                pcap_fix(pcap_file_name)

                body = {"fileName": file_name,
                        "tag": "",
                        "size": os.stat(pcap_file_name).st_size,
                        "createByUserId": self.user_id,
                        "createByUserName": self.user_name,
                        "createTime": int(os.stat(CustomizedPcapPath.Path + dir_name + '/'
                                                  + file_name).st_ctime * 1000),
                        "taskList": [],
                        "dir_name": dir_name,
                        "isLasted": "false",
                        "source": "download"}
                file_obj = self.mongodb.find_one('customized_pcap', {"$and": [{"fileName": file_name},
                                                                              {"createByUserId": self.user_id},
                                                                              {"dir_name": dir_name}]})
                if file_obj is None:
                    self.mongodb.insert_one("customized_pcap", body)
                else:
                    body['taskList'] = file_obj['taskList']
                    self.mongodb.update('customized_pcap', {"_id": file_obj["_id"]}, body)
                LOG.info("File [%s] download successfully." % file_name)
                ndr_log_to_box(NdrLog.Type.OPERATE, "文件[%s]下载成功。" % file_name)
            ftp_server_disconnect(ftp)
        except Exception as error:
            LOG.error(error)
            ndr_log_to_box(NdrLog.Type.OPERATE, "下载失败：%s。" % str(error))
            ftp_server_disconnect(ftp)
            return flask_response(str(error), False, {})
        return flask_response("Download successfully.", True, {})
