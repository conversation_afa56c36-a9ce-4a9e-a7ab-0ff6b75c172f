const MongoClient = require('mongodb').MongoClient;

const mongoUrl = {
	local: 'mongodb://box-mongo:27017/boss?authSource=admin',
	staging: 'mongodb://mongo-v2:27017/boss',
	prod: '********************************************',
};

const dbName = 'boss';

const now = new Date();
const boxModules = [
	// {
	// 	isMenu: true,
	// 	sort: 0,
	// 	name: '首页',
	// 	uri: 'app.home',
	// 	url: '/',
	// 	icon: 'home',
	// 	describe: '',
	// 	parentId: '',
	// 	createdAt: now,
	// 	updatedAt: now,
	// 	position: 'side',
	// },
	{
		isMenu: true,
		sort: 0,
		name: '威胁态势大屏',
		uri: 'screen',
		url: '/app/mica/screen',
		icon: 'home',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '威胁告警',
		uri: 'app.alarm',
		url: '/app/mica/alarm',
		icon: 'exclamation',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '规则告警',
		uri: 'app.alarm.loopHole',
		url: '/app/mica/alarm/loopHole',
		icon: 'bug',
		describe: '',
		parentId: 'app.alarm',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '情报告警',
		uri: 'app.alarm.intelligence',
		url: '/app/mica/alarm/Intelligence',
		icon: 'alert',
		describe: '',
		parentId: 'app.alarm',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '模型告警',
		uri: 'app.alarm.modal',
		url: '/app/mica/alarm/Modal',
		icon: 'gateway',
		describe: '',
		parentId: 'app.alarm',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '文件告警',
		uri: 'app.alarm.file',
		url: '/app/mica/alarm/File',
		icon: 'gateway',
		describe: '',
		parentId: 'app.alarm',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '定制分析',
		uri: 'app.customized',
		url: '/app/mica/customized',
		icon: 'highlight',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '威胁扩线分析',
		uri: 'app.customized.threatexpansion',
		url: '/app/mica/customized/threatexpansion',
		icon: 'highlight',
		describe: '',
		parentId: 'app.customized',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '邮件分析',
		uri: 'app.custom.emailAnalysis',
		url: '/app/mica/customized/emailAnalysis',
		icon: 'mail',
		describe: '',
		parentId: 'app.custom',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '异常行为分析',
		uri: 'app.custom.anomalyDetection',
		url: '/app/mica/customized/anomalyDetection',
		icon: 'exclamation',
		describe: '',
		parentId: 'app.custom',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '分析取证',
		uri: 'app.collect',
		url: '/app/mica/collect',
		icon: 'highlight',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '日志分析',
		uri: 'app.collect.logCollect',
		url: '/app/mica/collect/logCollect',
		icon: 'book',
		describe: '',
		parentId: 'app.collect',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '流量取证',
		uri: 'app.collect.download',
		url: '/app/mica/collect/download',
		icon: 'download',
		describe: '',
		parentId: 'app.collect',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '访问链分析',
		uri: 'app.collect.pathAnalysis',
		url: '/app/mica/collect/pathAnalysis',
		icon: '',
		describe: '',
		parentId: 'app.collect',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '攻击链分析',
		uri: 'app.collect.attacksAnalysis',
		url: '/app/mica/collect/attacksAnalysis',
		icon: '',
		describe: '',
		parentId: 'app.collect',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '文件分析',
		uri: 'app.collect.fileAnalysis',
		url: '/app/mica/collect/fileAnalysis',
		icon: '',
		describe: '',
		parentId: 'app.collect',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: false,
		sort: 2,
		name: '文件分析详情',
		uri: 'app.collect.fileDetail',
		url: '/app/mica/collect/fileDetail',
		icon: '',
		describe: '',
		parentId: 'app.collect',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '威胁报告',
		uri: 'app.threatReport',
		url: '/app/mica/threatReport',
		icon: 'book',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '报告列表',
		uri: 'app.threatReport.reportList',
		url: '/app/mica/threatReportList',
		describe: '',
		parentId: 'app.threatReport',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '周期报告设置',
		uri: 'app.threatReport.reportSetting',
		url: '/app/mica/threatReportSetting',
		describe: '',
		parentId: 'app.threatReport',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '资产感知',
		uri: 'app.means',
		url: '/app/mica/means',
		icon: 'rocket',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '资产画像',
		uri: 'app.means.meansDetail',
		url: '/app/mica/meansDetail',
		icon: 'rocket',
		describe: '',
		parentId: 'app.means',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '资产管理',
		uri: 'app.means.meansManagement',
		url: '/app/mica/meansManagement',
		icon: 'message',
		describe: '',
		parentId: 'app.means',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},

	{
		isMenu: true,
		sort: 1,
		name: '回放探索',
		uri: 'app.explore',
		url: '/app/mica/explore',
		icon: 'zoom-in',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '数据源管理',
		uri: 'app.explore.dataSource',
		url: '/app/mica/dataSource',
		icon: 'cluster',
		describe: '',
		parentId: 'app.explore',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '任务管理',
		uri: 'app.explore.task',
		url: '/app/mica/task',
		icon: 'desktop',
		describe: '',
		parentId: 'app.explore',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '安全知识中心',
		uri: 'app.knowledgeBase',
		url: '/app/mica/knowledgeBase',
		icon: 'key',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '规则管理',
		uri: 'app.knowledgeBase.featureManagement',
		url: '/app/mica/featureManagement',
		icon: 'man',
		describe: '',
		parentId: 'app.knowledgeBase',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '情报管理',
		uri: 'app.knowledgeBase.informationManagement',
		url: '/app/mica/informationManagement',
		icon: 'key',
		describe: '',
		parentId: 'app.knowledgeBase',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '模型管理',
		uri: 'app.knowledgeBase.modelManagement',
		url: '/app/mica/modelManagement',
		icon: 'money-collect',
		describe: '',
		parentId: 'app.knowledgeBase',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '业务配置',
		uri: 'app.config',
		url: '/app/mica/config',
		icon: 'tool',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '基础配置',
		uri: 'app.config.basics',
		url: '/app/mica/basics',
		icon: 'tool',
		describe: '',
		parentId: 'app.config',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '接口管理',
		uri: 'app.config.interfaceManagement',
		url: '/app/mica/interfaceManagement',
		icon: 'tool',
		describe: '',
		parentId: 'app.config',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '安全策略',
		uri: 'app.securityPolicy',
		url: '/app/mica/securityPolicy',
		icon: 'tool',
		describe: '',
		parentId: 'app.config',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: false,
		sort: 2,
		name: '安全策略',
		uri: 'app.securityPolicyForm',
		url: '/app/mica/securityPolicyForm',
		icon: 'tool',
		describe: '',
		parentId: 'app.config',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 3,
		name: '白名单管理',
		uri: 'app.config.whiteList',
		url: '/app/mica/whiteList',
		icon: 'tool',
		describe: '',
		parentId: 'app.config',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '安全报表',
		uri: 'report',
		url: '/app/mica/report',
		icon: 'book',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '系统监控',
		uri: 'app.monitor',
		url: '/app/mica/monitor',
		icon: 'fund',
		describe: '',
		parentId: '',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 0,
		name: '硬件监控',
		uri: 'app.monitor.hardwareMonitoring',
		url: '/app/mica/hardwareMonitoring',
		icon: 'fund',
		describe: '',
		parentId: 'app.monitor',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '流量监控',
		uri: 'app.monitor.flowMonitoring',
		url: '/app/mica/flowMonitoring',
		icon: 'fund',
		describe: '',
		parentId: 'app.monitor',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 1,
		name: '集群管理',
		uri: 'app.clusterManagement',
		url: '/app/mica/clusterManagement',
		icon: 'fund',
		describe: '',
		parentId: 'app.monitor',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: true,
		sort: 2,
		name: '服务监控',
		uri: 'app.monitor.serviceMonitoring',
		url: '/app/mica/serviceMonitoring',
		icon: 'fund',
		describe: '',
		parentId: 'app.monitor',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: false, // 不在菜单中
		sort: 1,
		name: '创建回放任务',
		uri: 'app.explore.task.exploreForm',
		url: '/app/mica/exploreForm',
		icon: '',
		describe: '',
		parentId: 'app.explore.task',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	{
		isMenu: false, // 不在菜单中
		sort: 1,
		name: '离线数据源详情',
		uri: 'app.explore.dataSource.exploreDetail',
		url: '/app/mica/exploreDetail',
		icon: '',
		describe: '',
		parentId: 'app.explore.dataSource',
		createdAt: now,
		updatedAt: now,
		position: 'side',
	},
	// {
	// 	isMenu: false, // 不在菜单中
	// 	sort: 1,
	// 	name: '详情',
	// 	uri: 'app.monitorDetail',
	// 	url: '/app/mica/monitorDetail',
	// 	icon: '',
	// 	describe: '',
	// 	parentId: '',
	// 	createdAt: now,
	// 	updatedAt: now,
	// 	position: 'side',
	// },
];

// 服务监控数据
const boxHealthChecks = [
	// {
	//   "tag": "mica",                  // 用于分组，默认使用 mica 即可
	//   "namespace" : "business",       // 类型，业务应用使用 business
	//   "name" : "业务服务接口",          // 展示名称
	//   "protocol" : "http",            // 协议， http 或 tcp
	//   "host" : "mica-api",            // host，可以填主机IP或容器名
	//   "port" : 7001,                  // 服务监听端口
	//   "url" : "/api/sys/healthcheck", // http接口路由，使用tcp协议时填 ""
	//   "expectStatus" : 200,           // 期望返回http状态，使用tcp协议时填 0
	// },
];

const boxUris = boxModules.map(x => x.uri);

const up = async env => {
	let client;
	try {
		client = await MongoClient.connect(mongoUrl[env]);
	} catch (e) {
		console.error('connect mongo error: ' + e);
		return;
	}
	try {
		const db = client.db(dbName);

		if (boxModules.length) {
			await db.collection('auth_modules').insertMany(
				boxModules.map(module => ({
					...module,
					app: 'mica',
				}))
			);
		}

		await db.collection('auth_groups').updateOne(
			{ name: 'admin' },
			{
				$addToSet: {
					modules: {
						$each: boxUris,
					},
				},
			}
		);
		await db.collection('systems').updateOne(
			{
				key: 'apps',
			},
			{
				$addToSet: {
					value: {
						$each: [
							{
								name: 'mica',
								entry: '/mica/',
							},
						],
					},
				},
			}
		);
		if (boxHealthChecks.length) {
			await db.collection('health_checks').insertMany(boxHealthChecks);
		}
		console.log('导入成功');
	} catch (e) {
		console.error('up error: ' + e);
	} finally {
		client.close();
	}
};

const down = async env => {
	let client;
	try {
		client = await MongoClient.connect(mongoUrl[env]);
	} catch (e) {
		console.error('connect mongo error: ' + e);
		return;
	}
	try {
		const db = client.db(dbName);
		await db.collection('auth_modules').deleteMany({
			$or: [
				{ app: 'mica' },
				{
					uri: {
						$in: boxUris,
					},
				},
			],
		});

		// 给 admin 组重新分配权限
		const sourceModules = await db.collection('auth_modules').find();
		const sourceModulesArray = await sourceModules.toArray();
		const moduleIds = sourceModulesArray.map(x => x.uri);
		await db.collection('auth_groups').updateOne(
			{
				name: 'admin',
			},
			{
				$set: {
					modules: [...moduleIds],
				},
			}
		);
		const sourceApps = await db.collection('systems').findOne({ key: 'apps' });
		const targetAppsArray = sourceApps.value.filter(x => x.name !== 'mica');
		await db.collection('systems').updateOne(
			{
				key: 'apps',
			},
			{
				$set: {
					value: [...targetAppsArray],
				},
			}
		);
		await db.collection('health_checks').deleteMany({
			tag: 'mica',
		});
		console.log('回滚成功');
	} catch (e) {
		console.error('down error: ' + e);
	} finally {
		client.close();
	}
};

const operation = process.argv[2];
if (!operation) {
	console.log('请输入操作: up/down');
	return;
}
if (operation === 'up') {
	up(process.argv[3] || 'local');
} else {
	down(process.argv[3] || 'local');
}
