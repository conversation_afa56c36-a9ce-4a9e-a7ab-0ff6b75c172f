#!/usr/bin/env python3
# coding:utf-8
from cluster.settings import *
from cluster.utils.file import *
from cluster.modules.auth import *

logger = get_logger(__name__)


class ClusterManager:

    def __init__(self,
                 ssl_key=CLUSTER_SSL_CLIENT_KEY,
                 ssl_cert=CLUSTER_SSL_CLIENT_CERT,
                 psk=CLUSTER_PRE_SHARED_KEY,
                 cluster_port=CLUSTER_SERVER_PORT):
        self.cluster_port = cluster_port
        self.ssl_key = ssl_key
        self.ssl_cert = ssl_cert
        self.psk = psk

    @staticmethod
    def get_role():
        return get_json_file(CLUSTER_CFG, 'node_role', 'single')

    @staticmethod
    def get_master_ip():
        return get_json_file(CLUSTER_CFG, 'mgmt_node_ip', '')

    def connect_node_by_ssl(self, node_ip, timeout=None):
        """ 连接节点，使用SSL隧道"""
        try:
            conn = KSSLConnector(self.psk).kssl_connect(node_ip,
                port=self.cluster_port,
                service=KSPService,
                keyfile=self.ssl_key,
                certfile=self.ssl_cert,
                config={'allow_public_attrs': True, "allow_pickle": True, "sync_request_timeout": timeout}
                )
            return conn, None
        except Exception as ex:
            logger.error('Connect failed to node:%s, %s' % (str(node_ip), str(ex)))
            raise ex


    def create_cluster(self, mgmt_node_ip, work_node_ips, hook_script_path=None, **script_args):
        """ 创建集群
            参数: mgmt_node_ip 管理节点IP
                 work_node_ips 工作节点IP列表
                 hook_script_path 回调脚本路径
                 kwargs {'params': [...], ...}，params是传递给hook_script的参数
            返回值: 未能成功加入集群的节点IP
        """
        cluster_members = [mgmt_node_ip] + work_node_ips
        ret1 = self.set_node_list(cluster_members, hook_script_path, **script_args)
        ret2 = self.set_mgmt_node_ip(mgmt_node_ip)
        ret = set(ret1).union(set(ret2))
        return list(ret)

    def delete_cluster(self, hook_script_path=None, **script_args):
        node_list = self.__get_node_list_from_file()
        failed_nodes = []
        for node in node_list:
            self.set_node_role(node, 'single')
            ret = self.__set_node_list_by_node(node, [], hook_script_path, **script_args)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    @staticmethod
    def get_node_list_from_file():
        try:
            cluster_cfg = read_from_json_file(CLUSTER_CFG)
            node_list = cluster_cfg.get('node_list', [])
            return node_list
        except Exception as ex:
            logger.error('Failed to open cluster config file. %s' % str(ex))
            return []

    @staticmethod
    def get_node_ip():
        cluster_cfg = read_from_json_file(CLUSTER_CFG)
        return cluster_cfg.get('node_ip', '')

    def get_node_ip_by_node_name(self, node_name):
        node_list = self.get_node_list()
        for node in node_list:
            if node['node_name'] == node_name:
                return node['node_ip']

        return ''

    @staticmethod
    def get_node_name():
        cluster_cfg = read_from_json_file(CLUSTER_CFG)
        return cluster_cfg.get('node_name', '')

    def __get_node_list_from_file(self):
        try:
            cluster_cfg = read_from_json_file(CLUSTER_CFG)
            node_list = cluster_cfg.get('node_list', [])
            return node_list
        except Exception as ex:
            logger.error('Failed to open cluster config file. %s' % str(ex))
            return []

    def get_node_list(self):
        """ 获取集群中当前的所有节点信息
            返回值：节点信息数组，节点信息结构示例如下：
            {
                "node_ip": "ip4 addr",
                "port": 18913,
                "node_name": "a good mnemonic",
                "node_role": "single/slave/master",
                "node_role_exact": "single | (mgmt | engine | data ...)",
                "mgmt_node_ip": "ip4_addr of mgmt node",
                "device_id": "sn",
                "status": node_status,
                "boot_time": "boot time",
                "node_work_ip": "node_work_ip",
                "node_role_init": "original role of node",
                "system_version": "latest systerm version",
                "data_version": "latest data package version",
            }
        """
        node_list = self.__get_node_list_from_file()
        node_infos = []
        for node in node_list:
            try:
                node_info = self.get_node_info(node)
                #conn, node_info = self.connect_node_by_ssl(node['node_ip'])
                #conn.close()
                node_infos.append(node_info)
            except Exception as ex:
                pass
        return node_infos

    def set_node_list(self, nodes, hook_script_path=None, **script_args):
        """ 设置集群成员信息
            参数：节点信息数组，节点信息结构示例如下：
            {
                "node_ip": "ip4 addr",
                "port": 18913,
                "node_role": "single/slave/master",
                "node_role_exact": "single | (mgmt | engine | data ...)",
                "mgmt_node_ip": "ip4_addr of mgmt node",
                "status": node_status,
                "node_role_init": "original role of node",
            }，
            node_ip/port/node_role/mgmt_node_ip 是必填项
            script_args {'params': [...], ...}，params是传递给hook_script的参数
            返回值：设置失败的节点IP数组
        """
        failed_nodes = []
        for node in nodes:
            ret = self.__set_node_list_by_node(node, nodes, hook_script_path, **script_args)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def add_node(self, node_info, hook_script_path=None, **script_args):
        """
        向集群中新增一个节点，如果节点重复加入集群，直接返回成功
        """
        node_ip = node_info['node_ip']
        node_list = self.__get_node_list_from_file()
        if node_ip in node_list:
            logger.info('Node:%s is already in cluster.' % node_ip)
            return []

        mgmt_node_ip = self.get_master_ip()
        mgmt_node_ip = mgmt_node_ip if mgmt_node_ip else node_info.get("mgmt_node_ip", "")
        node_info['mgmt_node_ip'] = mgmt_node_ip
        node_info['node_role'] = 'slave'
        if self.set_node_info(node_info):
            logger.info('Node:%s set node info failed.' % node_ip)
            return []

        node_list.append(node_ip)
        failed_nodes = []
        for node in node_list:
            ret = self.__set_node_list_by_node(node, node_list, hook_script_path, **script_args)
            if ret:
                failed_nodes.append(ret)

        return failed_nodes

    def delete_node(self, node_ip, hook_script_path=None, **script_args):
        """
        从集群中删除节点
        """
        node_list = self.__get_node_list_from_file()
        origin_node_list = node_list.copy()
        if node_ip not in node_list:
            logger.info('Node:%s is not in cluster.' % node_ip)
            return []

        node_list.remove(node_ip)
        failed_nodes = []
        for node in origin_node_list:
            ret = self.__set_node_list_by_node(node, node_list, hook_script_path, **script_args)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def __set_node_list_by_node(self, node_ip, nodes, hook_script_path=None, **script_args):
        """
        向目标节点设置集群成员信息
        script_args {'params': [...], ...}，params是传递给hook_script的参数
        """
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.set_cluster_node_list(node_ip, nodes, hook_script_path, **script_args)
            conn.close()
        except Exception as ex:
            logger.error('Unicast node list to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def set_mgmt_node_ip(self, mgmt_node_ip):
        """设置集群管理节点IP"""
        failed_nodes = []
        node_list = self.__get_node_list_from_file()
        for node in node_list:
            ret = self.__set_mgmt_node_ip_by_node(node, mgmt_node_ip)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def __set_mgmt_node_ip_by_node(self, node_ip, mgmt_node_ip):
        """向目标节点设置管理节点IP"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.set_mgmt_node_ip(mgmt_node_ip)
            conn.root.set_node('mgmt_node_ip', mgmt_node_ip)
            conn.close()
        except Exception as ex:
            logger.error('Unicast mgmt node ip to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def set_node_info(self, node_info):
        """ 设置目标节点信息
            参数：节点信息结构示例如下：
            {
                "node_ip": "ip4 addr",
                "node_role": "single/slave/master",
                "node_role_exact": "single | (mgmt | engine | data ...)",
                "mgmt_node_ip": "ip4_addr of mgmt node",
                "node_status": node_status,
            }
            node_ip/node_name/status/mgmt_node_ip/node_role_exact/node_role 是必填项
        """
        try:
            conn, _ = self.connect_node_by_ssl(node_info['node_ip'])
            conn.root.set_node_info(node_info)
            conn.close()
        except Exception as ex:
            logger.error('Failed to set node info to dst:%s failed, %s' % (str(node_info['node_ip']), str(ex)))
            return node_info['node_ip']

    def get_node_info(self, node_ip):
        """ 获取指定节点的信息
            返回值：节点信息结构示例如下：
            {
                "node_ip": "ip4 addr",
                "port": 18913,
                "node_name": "a good mnemonic",
                "node_role": "single/slave/master",
                "node_role_exact": "single | (mgmt | engine | data ...)",
                "mgmt_node_ip": "ip4_addr of mgmt node",
                "device_id": "sn",
                "status": node_status,
                "boot_time": "boot time",
                "node_work_ip": "node_work_ip",
                "node_role_init": "original role of node",
                "system_version": "latest systerm version",
                "data_version": "latest data package version",
            }
        """
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            node_info = json.loads(conn.root.get_node_info())
            node_info['port'] = self.cluster_port
            conn.close()
            return node_info
        except Exception as ex:
            logger.error('Failed to get node info to dst:%s failed, %s' % (str(node_ip), str(ex)))

    def set_node_role(self, node_ip, node_role):
        """ 设置指定节点的角色
            参数：node_role = single | (master | slave)
        """
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.set_node_role(node_role)
            conn.close()
        except Exception as ex:
            logger.error('Failed to set node role to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def get_node_role(self, node_ip):
        """获取指定节点角色"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            node_role = conn.root.get_node('node_role')
            conn.close()
            return node_role
        except Exception as ex:
            logger.error('Failed to set node role to dst:%s failed, %s' % (str(node_ip), str(ex)))

    def set_node_status(self, node_ip, node_status):
        """ 设置节点状态信息
            参数：node_status = 应用模块自定义值，对 KSP 透明
        """
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.set_node('node_status', node_status)
            conn.close()
        except Exception as ex:
            logger.error('Failed to set node status to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def get_node_status(self, node_ip):
        """ 获取节点状态信息
        """
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            node_status = conn.root.get_node('node_status')
            conn.close()
            return node_status
        except Exception as ex:
            logger.error('Failed to get node role to dst:%s failed, %s' % (str(node_ip), str(ex)))

    def get_node_sysstats(self, node_ip):
        """ 获取节点系统统计信息
        """
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            node_sysstats = conn.root.get_node_stats()
            node_sysstats = json.loads(node_sysstats)
            conn.close()
            return node_sysstats
        except Exception as ex:
            logger.error('Failed to get node sysstats to dst:%s failed, %s' % (str(node_ip), str(ex)))

    ### TODO: 该API是否必要？前面提供的方法是否能满足需求？尽量不提供，业务模块可以自行维护
    def __set_node_attr(self, node_ip, attr_name, attr_value):
        """设置节点特定属性的值，用于业务应用存储某些信息，对 KSP 透明"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.set_node(attr_name, attr_value)
            conn.close()
        except Exception as ex:
            logger.error('Failed to set %s to dst:%s failed, %s' % (attr_name, str(node_ip), str(ex)))
            return node_ip

    def __get_node_attr(self, node_ip, attr_name):
        """获取指定节点特定属性的值"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            attr_value = conn.root.get_node(attr_name)
            conn.close()
            return attr_value
        except Exception as ex:
            logger.error('Failed to get %s to dst:%s failed, %s' % (attr_name, str(node_ip), str(ex)))

    def poweroff_node(self, node_ip):
        """关闭指定节点"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.stop_node()
            conn.close()
        except Exception as ex:
            # 重启必然会导致异常，不需要再输出错误日志
            pass
            #logger.error('Failed to poweroff dst:%s failed, %s' % (str(node_ip), str(ex)))
            #return node_ip

    def reboot_node(self, node_ip):
        """重启指定节点"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.restart_node()
            conn.close()
        except Exception as ex:
            # 重启必然会导致异常，不需要再输出错误日志
            pass
            #logger.error('Failed to reboot dst:%s failed, %s' % (str(node_ip), str(ex)))
            #return node_ip

    def sync_license_file_by_cluster(self, src_license_path):
        """设置集群证书文件"""
        failed_nodes = []
        node_list = self.__get_node_list_from_file()
        for node in node_list:
            ret = self.sync_license_file_by_node(node, src_license_path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def sync_license_file_by_node(self, node_ip, src_license_path):
        """设置指定节点证书文件"""
        try:
            license_file = open(src_license_path, 'rb')
            license_content = license_file.read()
            license_file.close()
            conn, _ = self.connect_node_by_ssl(node_ip)
            ok = conn.root.sync_license_file(license_content)
            conn.close()
            if not ok:
                raise Exception('sync_license_file error!')
        except Exception as ex:
            logger.error('Failed to sync license to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def sync_update_key_by_cluster(self, src_update_key_path):
        """设置集群升级公钥文件"""
        failed_nodes = []
        node_list = self.__get_node_list_from_file()
        for node in node_list:
            ret = self.sync_update_key_by_node(node, src_update_key_path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def sync_update_key_by_node(self, node_ip, src_update_key_path):
        """设置指定节点升级公钥文件"""
        try:
            update_key_file = open(src_update_key_path, 'rb')
            update_key_content = update_key_file.read()
            update_key_file.close()
            conn, _ = self.connect_node_by_ssl(node_ip)
            ok = conn.root.sync_update_pubkey(update_key_content)
            conn.close()
            if not ok:
                raise Exception('sync_update_pubkey error!')
        except Exception as ex:
            logger.error('Failed to sync update key to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def sync_file_by_cluster(self, src_file_path, dst_file_path):
        """设置集群业务文件，目的路径只能在 /var/ 下，防止破坏系统其他文件，文件大小不能超过2G """
        assert (is_safe_path(dst_file_path))
        failed_nodes = []
        node_list = self.__get_node_list_from_file()
        for node in node_list:
            ret = self.sync_file_by_node(node, src_file_path, dst_file_path)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def sync_file_by_node(self, node_ip, src_file_path, dst_file_path):
        """设置指定节点业务文件，目的路径只能在 /var/ 和 /opt/ 下，防止破坏系统其他文件，文件大小不能超过2G """
        assert (is_safe_path(dst_file_path))
        try:
            file_handle = open(src_file_path, 'rb')
            conn, _ = self.connect_node_by_ssl(node_ip)
            chunksize = 8*1024*1024
            h = hashlib.md5()
            content = file_handle.read(chunksize)
            while content:
                h.update(content)
                ok = conn.root.write_part_of_file(content, dst_file_path, md5sum=None, is_end=False)
                if not ok:
                    raise Exception('sync_file error!')
                content = file_handle.read(chunksize)
            file_handle.close()
            ok = conn.root.write_part_of_file(content, dst_file_path, md5sum=h.digest(), is_end=True)
            if not ok:
                raise Exception('sync_file error!')
            conn.close()
        except Exception as ex:
            logger.error('Failed to sync file to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def write_file_content_by_node(self, node_ip, bytes_content, dst_file_path):
        """将特定内容写入指定节点的目标文件，目的路径只能在 /var/ 和 /opt/ 下，防止破坏系统其他文件，文件大小不能超过2G """
        assert (is_safe_path(dst_file_path))
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            ok = conn.root.sync_file(bytes_content, dst_file_path)
            conn.close()
            if not ok:
                raise Exception('sync_file error!')
        except Exception as ex:
            logger.error('Failed to write file content to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def read_file_content_by_node(self, node_ip, src_file_path):
        """读取指定节点的目标文件，路径只能在 /var/ 和 /opt/ 下，防止随意读取系统文件，文件大小不能超过2G """
        assert (is_safe_path(src_file_path))
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            content = conn.root.read_file(src_file_path)
            conn.close()
            if content is None:
                raise Exception('read_file error!')
            return content
        except Exception as ex:
            logger.error('Failed to read file content from dst:%s, %s' % (str(node_ip), str(ex)))
            return None

    def read_file_by_node(self, node_ip, src_file_path, dst_file_path):
        """读取指定节点的目标文件，路径只能在 /var/ 和 /opt/ 下，防止随意读取系统文件，文件大小不能超过2G
           本函数会对文件进行切割读取，超过100M的文件，建议使用本函数进行操作。
        """
        assert (is_safe_path(src_file_path))
        if os.path.isfile(dst_file_path):
            raise Exception("destination file exists")
        try:
            h = hashlib.md5()
            dst_file = open(dst_file_path, 'wb')
            conn, _ = self.connect_node_by_ssl(node_ip)
            chunksize = 4*1024*1024
            offset = 0
            content = conn.root.read_part_of_file(src_file_path, chunksize, offset)
            while content:
                dst_file.write(content)
                h.update(content)
                offset += chunksize
                content = conn.root.read_part_of_file(src_file_path, chunksize, offset)
            dst_file.close()
            dst_file_md5sum = h.digest()
            src_file_md5sum = conn.root.exposetd_get_file_md5sum(src_file_path)
            conn.close()
            if dst_file_md5sum != src_file_md5sum:
                os.unlink(dst_file_path)
                raise Exception("md5sum check error")
            return True
        except Exception as ex:
            logger.error('Failed to read file content from dst:%s, %s' % (str(node_ip), str(ex)))
            return False

    ### TODO：是否需要暴露 update_sync, download_update_file 等方法？
    def system_update_by_cluster(self, update_mode):
        """整个集群系统升级"""
        node_list = self.__get_node_list_from_file()
        failed_nodes = []
        for node in node_list:
            ret = self.system_update_by_node(node, update_mode)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def system_update_by_node(self, node_ip, update_mode):
        """指定节点系统升级"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.do_node_upgrade(update_mode, 'default')
            conn.close()
        except Exception as ex:
            logger.error('Failed to do upgrade action to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def data_update_by_cluster(self, dataupdate_pkginfo_file_path, data_pkg_base_dir):
        """对整个集群进行特征库升级"""
        node_list = self.__get_node_list_from_file()
        failed_nodes = []
        for node in node_list:
            ret = self.data_update_by_node(node, dataupdate_pkginfo_file_path, data_pkg_base_dir)
            if ret:
                failed_nodes.append(ret)
        return failed_nodes

    def data_update_by_node(self, node_ip, dataupdate_pkginfo_file_path, data_pkg_base_dir):
        """对指定节点进行特征库升级"""
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            conn.root.do_node_dataupdate(dataupdate_pkginfo_file_path, data_pkg_base_dir)
            conn.close()
        except Exception as ex:
            logger.error('Failed to do upgrade action to dst:%s failed, %s' % (str(node_ip), str(ex)))
            return node_ip

    def set_static_ip(self, ksp_ifname, node_ip, new_ip, mask4=None, gw4=None, dns=None, mask6=None, gw6=None):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip, timeout=5)
            conn.root.set_static_ip(ksp_ifname, node_ip, new_ip, mask4, gw4, dns,mask6, gw6)
            conn.close()
            logger.error('Failed to set static ip to dst:%s' % str(node_ip))
            return node_ip
        except TimeoutError:
            logger.info('Set static ip to dst:%s success' % str(new_ip))
        except Exception as ex:
            logger.error('Failed to set static ip to dst:%s, %s' % (str(node_ip), str(ex)))

    def get_network_info(self, node_ip):
        try:
            conn, _ = self.connect_node_by_ssl(node_ip)
            info = conn.root.get_network_info()
            conn.close()
            return info
        except Exception as ex:
            logger.error('Failed to get network info from dst:%s, %s' % (str(node_ip), str(ex)))
            return None
