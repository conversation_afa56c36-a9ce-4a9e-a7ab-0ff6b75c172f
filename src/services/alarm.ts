import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';
// 事件类型
export const EventType = () => request.get('knowledge/event_type');
// 杀伤链阶段
export const getKillchains = () => request.get('knowledge/killchains');
// 威胁分类
export const threatClassify = () => request.get('knowledge/threat_flag');
// 文件威胁分类
export const threatFile = () => request.get('file/threat_type');
// 杀伤链talbe
export const getTable = (data: object) => {
  return request(`event/list${parseQueryString(data)}`, {
    method: 'GET',
    data,
  });
};

// 杀伤链事件详情
export const getKillchainsDetail = (data: object) => {
  return request(`event/detail${parseQueryString(data)}`, {
    method: 'GET',
    data,
  });
};

// 杀伤链下载数据包
export const getDatapackage = (data: object) => {
  return request(`package${parseQueryString(data)}`, {
    method: 'GET',
    responseType: 'blob',
    data,
  });
};

// 漏洞table
export const getLoopHoleList = (data: object) => {
  return request(`alert/list`, {
    method: 'POST',
    data,
  });
};

// 漏洞处理
export const loopHoleConfirm = (data: object) =>
  request('alert/confirm', {
    method: 'POST',
    data,
  });
// 漏洞事件详情
export const loopHoleEventDetail = (data: {
  uniqueId: any;
  confirm: number;
}) => {
  return request("alert/detail", {
    method: 'POST',
    data: {
      ...data,
      detail: true,
      ...(data.page ? { page:`${data.page}` } : {}),
      ...(data.pageSize ? {pageSize:`${data.pageSize}` } : {})
    },
  });
};
// 白名单
export const handleaddWhiteList = (data: any) => {
  return request('whitelist', {
    method: 'post',
    data,
  });
};
// 情报table
export const getIntelligenceList = (data: object) => {
  return request(`ioc_alert/list`, {
    method: 'post',
    data,
  });
};
// 情报事件详情
export const intelligenceDetail = (data: object) => {
  return request(`ioc_alert/detail${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

// 模型table
export const getModalList = (data: object) => {
  return request(`model/list `, {
    method: 'POST',
    data,
  });
};
// 文件table
export const getFileList = (data: object) => {
  return request(`file_alert/list`, {
    method: 'POST',
    data,
  });
};
// 文件详情
export const getFileDetail = (data: object) => {
  return request(`file_alert/detail`, {
    method: 'POST',
    data: {
      ...data,
      detail: true,
      ...(data.page ? { page:`${data.page}` } : {}),
      ...(data.pageSize ? {pageSize:`${data.pageSize}` } : {})
    },
  });
};
// 情报事件详情
export const getIntelligenceDetail = (data: object) => {
  return request(`ioc_alert/detail`, {
    method: 'POST',
    data: {
      ...data,
      detail: true,
      ...(data.page ? { page:`${data.page}` } : {}),
      ...(data.pageSize ? {pageSize:`${data.pageSize}` } : {})
    },
  });
};

// 模型查看更多
export const getModalDetail = (data: object) => {
  return request(`model/detail${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
// 模型事件详情
export const getMdoalEvnet = (data: object) => {
  return request(`model/groupAll`, {
    method: 'POST',
    data: {
      ...data,
      detail: true,
      ...(data.page ? { page:`${data.page}` } : {}),
      ...(data.pageSize ? {pageSize:`${data.pageSize}` } : {})
    },
  });
};

// 情报处理
export const handleConfirm = (data: object) => {
  return request(`alert/confirm`, {
    method: 'post',
    data,
  });
};

// 下载数据包
export const downloadPack = (data: object) => {
  return request(`package${parseQueryString(data)}`, {
    method: 'get',
    responseType: 'blob',
    data,
  });
};

export const getDomainList = (data: any) => {
  return request(`/dpilog/ip_info${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
export const getFlowList = (data: any) => {
  return request(`dpilog/ip_send_count${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
export const getAloneChartData = (data: object) => {
  return request(`dpilog/ip_accept_count${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getPackage = (data: any) => {
  return request(`package${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
