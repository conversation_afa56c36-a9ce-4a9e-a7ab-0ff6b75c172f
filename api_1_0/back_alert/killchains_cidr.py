# -*- coding: utf-8 -*-
# @Time    : 2019-06-20 11:53
# <AUTHOR> Shark
# @File    : killchains_cidr.py
# @Software: PyCharm

import time
from flask import request
from flask_restful import Resource
from utils import param_check, database
from utils.param_check import Validator
from utils.utils import flask_response
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class EventKillchainsIpCidrSearch(Resource):
    """根据一项或多项参数查询IP CIDR Killchains闭环情况"""

    def __init__(self):
        """初始化参数"""

    @param_check.check_flask_args(Validator("killchains_cidr_schema"), request)
    def get(self, **kwargs):
        """根据参数组装ES查询语句，送至ES查询并将结果组装返回"""

        # 参数赋值并做格式转换
        args = {
            "start_time": int(kwargs["args"]["startTime"]),
            "stop_time": int(kwargs["args"]["stopTime"]),
            "attack_ip": kwargs["args"]["attackIp"],
            "victim_ip": kwargs["args"]["victimIp"],
            "count": int(kwargs["args"]["count"])
        }

        if args["start_time"] >= args["stop_time"]:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})

        # query 查询模块
        es_template = get_es_query_code(args)

        # 添加聚合语句
        es_template["aggs"] = get_aggs_killchains_loop(args)

        # 最外层size设置为0，不展示搜索结果，只展示聚合结果
        es_template["size"] = 0
        print(es_template)
        # 连接ES查询
        es_client = database.get_es_client()
        try:
            res = es_client.search(index="rule-eve", body=es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, True, {})
        try:
            data = data_format(res, args)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})


def data_format(data, args):
    """将es输出结果格式化成前端结构"""
    format_data_list = []
    for attack_data in data["aggregations"]["attackIp_range"]["buckets"]:
        format_data = {
            "attackIp": attack_data["key"],
            "triggerCount": attack_data["doc_count"],
            "killchainsList": []
        }
        for victim_data in attack_data["victimIp_range"]["buckets"]:
            format_data["victimIp"] = victim_data["key"]
            for event_data in victim_data["killchains"]["buckets"]:
                if args["count"] > 0:
                    event_list = [event["_source"] for event in event_data["top_event_hits"]["hits"]["hits"]]
                else:
                    event_list = []
                event = {
                    "killchains": event_data["key"],
                    "triggerCount": event_data["doc_count"],
                    "endTime": event_data["max_time"]["value_as_string"],
                    "startTime": event_data["min_time"]["value_as_string"],
                    "eventList": event_list
                }
                format_data["killchainsList"].append(event)

        if "/32" not in args["attack_ip"] and "/32" in format_data["attackIp"]:
            format_data["attackIp"] = format_data["attackIp"].split("/")[0]
        if "/32" not in args["victim_ip"] and "/32" in format_data["victimIp"]:
            format_data["victimIp"] = format_data["victimIp"].split("/")[0]
        format_data_list.append(format_data)
    return format_data_list


def get_es_query_code(args):
    """填充es查询语句的query部分"""
    query_code = {
        "query": {
            "bool": {
                "must": []
            }
        }
    }
    # 添加ES查询时间范围
    start_time = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime(args["start_time"]))
    stop_time = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime(args["stop_time"]))
    query_time_range = {
        "range": {
            "@timestamp": {
                "gt": start_time,
                "lt": stop_time
            }
        }
    }
    query_code["query"]["bool"]["must"].append(query_time_range)

    # 通过term精确匹配
    es_term_list = {
        "attack_ip": "rulesInfo.attackIp",
        "victim_ip": "rulesInfo.victimIp"
    }

    for name, filed in es_term_list.items():
        query = {
            "term": {
                filed: args[name]
            }
        }
        query_code["query"]["bool"]["must"].append(query)
    return query_code


def get_aggs_killchains_loop(args):
    """填充es查询语句的aggs部分"""

    attack_ip = args["attack_ip"]
    victim_ip = args["victim_ip"]
    if "/" not in args["attack_ip"]:
        attack_ip += "/32"
    if "/" not in args["victim_ip"]:
        victim_ip += "/32"

    # 添加ES查询分页参数
    killchains_loop_aggs = {
        "attackIp_range": {
            "ip_range": {
                "field": "rulesInfo.attackIp",
                "ranges": [
                    {"mask": attack_ip}
                ]
            },
            "aggs": {
                "victimIp_range": {
                    "ip_range": {
                        "field": "rulesInfo.victimIp",
                        "ranges": [
                            {"mask": victim_ip}
                        ]
                    },
                    "aggs": {
                        "killchains": {
                            "terms": {
                                "field": "rulesInfo.lockheedKillchainEN.keyword"
                            },
                            "aggs": {
                                "max_time": {
                                    "max": {
                                        "field": "@timestamp"
                                    }
                                },
                                "min_time": {
                                    "min": {
                                        "field": "@timestamp"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    if args["count"] > 0:
        killchains_loop_aggs["attackIp_range"]["aggs"]["victimIp_range"]["aggs"]["killchains"][
            "aggs"]["top_event_hits"] = get_aggs_top_size(args["count"])

    return killchains_loop_aggs


def get_aggs_top_size(count):
    """根据传入的count组合top list查询语句"""
    query = {
        "top_hits": {
            "size": count,
            "_source": {
                "includes": [
                    "src_ip",
                    "src_port",
                    "dest_ip",
                    "dest_port",
                    "flow_id",
                    "payload",
                    "flow",
                    "alert",
                    "rulesInfo"
                ]
            }
        }
    }
    return query
