# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : baseline_detector.py
# @Software: PyCharm

"""基线偏移异常检测模型"""

import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any
from .base_detector import BaseDetector
from utils.logger import get_ndr_logger
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class BaselineDetector(BaseDetector):
    """基线偏移异常检测器
    
    计算长周期内的行为基线，对比当前偏离程度
    比如某域名访问量相比历史基线超3倍
    """
    
    def __init__(self, task_config: Dict[str, Any]):
        super().__init__(task_config)
        self.baseline_periods = self.detection_config.get('baseline_periods', [7, 30, 90])  # 天数
        self.deviation_threshold = self.detection_config.get('deviation_threshold', 3.0)  # 偏离倍数
        self.min_baseline_samples = self.detection_config.get('min_baseline_samples', 5)
        self.metric_types = self.detection_config.get('metric_types', ['connections', 'bytes', 'unique_ips'])
    
    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """执行基线偏移异常检测"""
        try:
            results = []
            
            # 获取当前数据
            current_data = self._get_current_data(start_time, end_time)
            if not current_data:
                LOG.info(f"当前时段无数据: {self.target_value}")
                return results
            
            # 计算当前指标
            current_metrics = self._calculate_metrics(current_data)
            
            # 对每个基线周期进行检测
            for period_days in self.baseline_periods:
                baseline_data = self._get_baseline_data(start_time, period_days)
                if not baseline_data:
                    continue
                
                baseline_metrics = self._calculate_baseline_metrics(baseline_data, period_days)
                
                # 检测每个指标的异常
                for metric_type in self.metric_types:
                    anomaly_result = self._detect_baseline_anomaly(
                        current_metrics, baseline_metrics, metric_type, 
                        period_days, start_time, end_time
                    )
                    if anomaly_result:
                        results.append(anomaly_result)
            
            return results
            
        except Exception as e:
            LOG.error(f"基线偏移异常检测失败: {str(e)}")
            return []
    
    def _get_current_data(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取当前数据"""
        if self.target_type == 'domain':
            # 对于域名，查询DNS和HTTP数据
            dns_data = self.get_historical_data('dpilog_dns', start_time, end_time)
            http_data = self.get_historical_data('dpilog_http', start_time, end_time)
            return dns_data + http_data
        else:
            # 对于IP，查询连接数据
            return self.get_historical_data('dpilog_conn', start_time, end_time)
    
    def _get_baseline_data(self, current_start: datetime, period_days: int) -> List[Dict[str, Any]]:
        """获取基线数据"""
        baseline_data = []
        
        # 获取过去N天的数据，排除当前时段
        for i in range(1, period_days + 1):
            day_start = current_start - timedelta(days=i)
            day_end = day_start + timedelta(hours=24)
            
            if self.target_type == 'domain':
                dns_data = self.get_historical_data('dpilog_dns', day_start, day_end)
                http_data = self.get_historical_data('dpilog_http', day_start, day_end)
                baseline_data.extend(dns_data + http_data)
            else:
                conn_data = self.get_historical_data('dpilog_conn', day_start, day_end)
                baseline_data.extend(conn_data)
        
        return baseline_data
    
    def _calculate_metrics(self, data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算当前数据指标"""
        if not data:
            return {}
        
        # 统计连接数
        connections = len(data)
        
        # 统计字节数
        total_bytes = 0
        unique_ips = set()
        
        for record in data:
            # 字节数统计
            total_bytes += record.get('src_bytes', 0) + record.get('dst_bytes', 0)
            
            # 唯一IP统计
            if 'src_ip' in record:
                unique_ips.add(record['src_ip'])
            if 'dst_ip' in record:
                unique_ips.add(record['dst_ip'])
        
        return {
            'connections': connections,
            'bytes': total_bytes,
            'unique_ips': len(unique_ips)
        }
    
    def _calculate_baseline_metrics(self, baseline_data: List[Dict[str, Any]], period_days: int) -> Dict[str, Dict[str, float]]:
        """计算基线指标"""
        if not baseline_data:
            return {}
        
        # 按天分组计算指标
        daily_metrics = {}
        
        for record in baseline_data:
            day_key = record['ts'].strftime('%Y-%m-%d')
            if day_key not in daily_metrics:
                daily_metrics[day_key] = {'connections': 0, 'bytes': 0, 'unique_ips': set()}
            
            daily_metrics[day_key]['connections'] += 1
            daily_metrics[day_key]['bytes'] += record.get('src_bytes', 0) + record.get('dst_bytes', 0)
            
            if 'src_ip' in record:
                daily_metrics[day_key]['unique_ips'].add(record['src_ip'])
            if 'dst_ip' in record:
                daily_metrics[day_key]['unique_ips'].add(record['dst_ip'])
        
        # 转换为数值并计算统计信息
        metrics_values = {
            'connections': [],
            'bytes': [],
            'unique_ips': []
        }
        
        for day_data in daily_metrics.values():
            metrics_values['connections'].append(day_data['connections'])
            metrics_values['bytes'].append(day_data['bytes'])
            metrics_values['unique_ips'].append(len(day_data['unique_ips']))
        
        # 计算基线统计信息
        baseline_stats = {}
        for metric_type, values in metrics_values.items():
            if values and len(values) >= self.min_baseline_samples:
                baseline_stats[metric_type] = {
                    'mean': statistics.mean(values),
                    'median': statistics.median(values),
                    'std': statistics.stdev(values) if len(values) > 1 else 0,
                    'max': max(values),
                    'min': min(values),
                    'count': len(values),
                    'values': values
                }
        
        return baseline_stats
    
    def _detect_baseline_anomaly(self, current_metrics: Dict[str, float], 
                               baseline_metrics: Dict[str, Dict[str, float]], 
                               metric_type: str, period_days: int,
                               start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """检测基线异常"""
        if metric_type not in current_metrics or metric_type not in baseline_metrics:
            return None
        
        current_value = current_metrics[metric_type]
        baseline_stats = baseline_metrics[metric_type]
        baseline_mean = baseline_stats['mean']
        
        if baseline_mean == 0:
            return None
        
        # 计算偏离倍数
        deviation_ratio = current_value / baseline_mean
        
        # 判断是否异常
        if deviation_ratio >= self.deviation_threshold:
            # 计算异常分数
            anomaly_score = min((deviation_ratio - 1) / 10.0, 1.0)
            
            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)
            
            # 计算变化率
            change_rate = (deviation_ratio - 1) * 100
            
            return {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'deviation_ratio': deviation_ratio,
                'current_value': current_value,
                'baseline_mean': baseline_mean,
                'baseline_std': baseline_stats['std'],
                'change_rate': change_rate,
                'metric_type': metric_type,
                'baseline_period_days': period_days,
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"{self.target_value}在{metric_type}指标上出现基线偏移异常，"
                             f"当前值{current_value}，{period_days}天基线均值{baseline_mean:.2f}，"
                             f"偏离倍数{deviation_ratio:.2f}，变化率{change_rate:.1f}%",
                'details': {
                    'current_metrics': current_metrics,
                    'baseline_stats': baseline_stats,
                    'detection_config': self.detection_config
                }
            }
        
        return None
