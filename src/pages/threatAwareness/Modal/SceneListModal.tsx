import React, { useState } from 'react';
import { Button, Modal, Table } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import SceneFormModal from './SceneFormModal';
import { useSceneList } from './hooks';

export type Props = {
  show: boolean;
  onClose: () => void;
  onCreate: () => void;
  style?: React.CSSProperties;
  className?: string;
}

const { confirm } = Modal;

function SceneListModal({ show, onClose, onCreate }: Props): JSX.Element {

  const {
    loading,
    list,
    total,
    pagination,
    confirmLoading,
    setPagination,
    sceneOperate,
    sceneDelete
  } = useSceneList()

  const [selectedRowKeys, setselectedRowKeys] = useState([]);

  const [currentEditScene, setCurentScene] = useState()

  const [showForm, setShow] = useState(false)

  const showDeleteConfirm = (id?: string) => {
    confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除吗？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => handleDelete(id),
    });
  };

  const handleDelete = (id?: string) => {
    return Promise.resolve().then(() => {
      if (id) {
        return sceneDelete([id]);
      }
      return sceneDelete(selectedRowKeys);
    }).then(() => {
      setselectedRowKeys([])
      onCreate?.()
    })
  }

  const groupColumns = [
    {
      title: '序号',
      width: 50,
      dataIndex: 'order',
    },
    {
      title: '场景名称',
      width: 150,
      dataIndex: 'name',
    },
    {
      title: '备注',
      width: 200,
      dataIndex: 'remark',
    },
    {
      title: '添加日期',
      width: 180,
      dataIndex: 'create',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      render: (_: any, record: any) => {
        return (
          <>
            <Button
              type="link"
              onClick={() => handleEditSceneModal(record)}
            >
              编辑
            </Button>
            <Button
              type="link"
              danger
              onClick={() => showDeleteConfirm(record._id)}
            >
              删除
            </Button>
          </>
        );
      },
    },
  ];

  const handleOnPageChange = (page: number, pageSize: number) => {
    setPagination({
      page,
      pageSize,
    })
  }

  const handleEditSceneModal = (record: any) => {
    setCurentScene(record)
    setShow(true)
  }

  const handleCreateSceneModal = () => {
    setCurentScene(undefined)
    setShow(true)
  }

  const handleCreateAndEdit = (values: object, isEdit: boolean) => {
    sceneOperate(values, isEdit).then(flag => {
      if (flag) setShow(false);
      if (!isEdit) {
        onCreate?.();
      }
    })
  }


  const paginationData = {
    total,
    showSizeChanger: true,
    showQuickJumper: true,
    current: pagination.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: pagination.pageSize,
    onChange: handleOnPageChange,
  };

  const rowSelection = {
    fixed: true,
    columnWidth: 60,
    onChange: (selectedRowKeys: []) => {
      setselectedRowKeys(selectedRowKeys);
    },
    selectedRowKeys,
  };

  return (
    <Modal
      visible={show}
      width={900}
      centered
      title="场景列表"
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
      onCancel={onClose}
      destroyOnClose
    >
      <div className='flex gap-3'>
        <Button
          type="primary"
          onClick={handleCreateSceneModal}
        >
          新增
        </Button>
        <Button
          type="primary"
          disabled={!selectedRowKeys.length}
          danger
          onClick={() => showDeleteConfirm()}
        >
          删除
        </Button>
        {
          !!selectedRowKeys.length && (
            <span className='flex items-center'>
              已经选择 {selectedRowKeys.length} 条数据
            </span>
          )
        }
      </div>
      <Table
        loading={loading}
        className="mt-4"
        size="small"
        rowKey="_id"
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        scroll={{ y: 500 }}
        pagination={paginationData}
        dataSource={list}
        columns={groupColumns}
      />
      <SceneFormModal
        onCreate={(v) => handleCreateAndEdit(v, false)}
        onEdit={(v) => handleCreateAndEdit(v, true)}
        confirmLoading={confirmLoading}
        show={showForm}
        value={currentEditScene}
        onClose={() => setShow(false)}
      />
    </Modal>
  );
}

export default SceneListModal;
