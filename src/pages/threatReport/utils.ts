import CryptoJS from "crypto-js";

// AES加密/解密工具，用于加密威胁报告的密码

export function encryptData(data: string, secret: string): string {
  return CryptoJS.AES.encrypt(
    CryptoJS.enc.Utf8.parse(data),
    CryptoJS.enc.Utf8.parse(secret),
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.ZeroPadding
    }
  ).toString();
}

export function decryptData(encryptData: string, secret: string): string {
  return CryptoJS.enc.Utf8.stringify(CryptoJS.AES.decrypt(
    encryptData,
    CryptoJS.enc.Utf8.parse(secret),
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.ZeroPadding
    }
  )).toString();
}