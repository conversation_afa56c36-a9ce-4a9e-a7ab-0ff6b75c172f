// 错误通知类型
export enum ShowType {
  NONE = 1,
  MESSAGE_SUCCESS = 2,
  MESSAGE_ERROR = 3,
  MESSAGE_WARNING = 4,
  MESSAGE_INFO = 5,
  NOTIFICATION_SUCCESS = 6,
  NOTIFICATION_ERROR = 7,
  NOTIFICATION_WARNING = 8,
  NOTIFICATION_INFO = 9,
  ALERT_SUCCESS = 10,
  ALERT_ERROR = 11,
  ALERT_WARNING = 12,
  ALERT_INFO = 13,
}

export const REPEAT_TYPE = {
  CRON: 'cron',
  SINGLE: 'single',
  EVERY: 'every',
};

export const MAX_SIX_NUMBER_REGEX = /^[0-9]{0,6}$/;
export const EXACT_SIX_NUMBER_REGEX = /^[0-9]{6}$/;
export const EMAIL_REGEX = /^([a-zA-Z0-9_\-.]+)@([a-zA-Z0-9_\-.]+)\.([a-zA-Z]{2,5})$/;
export const LITE_PHONE_REGEX = /^[\d|(|)|+\s-]{0,20}$/;
/* eslint-disable-next-line no-useless-escape */
export const PASSWORD_REGEX = /^(?=.*\d)(?=.*[A-Za-z])(?=.*[~`!@#$%^&*_'";:><,\|\?\.\-\+\=\[\]\(\)\{\}\/])([\w~`!@#$%^&*_'";:><,\|\?\.\-\+\=\[\]\(\)\{\}\/]{8,16})$/;
export const PASSWORD_REGEX_DESCRIPTION =
  '密码需要由 8-16 个字符（必须包含字母、数字、常用特殊字符）组成';
export const IP_ADDRESS_REGEX = /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/;
export const NETMASK_REGEX = /^(254|252|248|240|224|192|128|0)\.0\.0\.0$|^(255\.(254|252|248|240|224|192|128|0)\.0\.0)$|^(255\.255\.(254|252|248|240|224|192|128|0)\.0)$|^(255\.255\.255\.(254|252|248|240|224|192|128|0))$/;
