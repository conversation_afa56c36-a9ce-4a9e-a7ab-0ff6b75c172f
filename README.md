# 概述
流量分析产品

# 项目架构
```bash
.
├── README.md
├── api
│   ├── __init__.py
│   ├── __pycache__
│   ├── back_ids 	  ## BackIDS	模块
│   ├── back_stream   ## BackStream 模块
│   ├── router.py
│   ├── rules
│   ├── tests
│   └── utils
├── app.py
├── builder
│   └── README.md
├── config.py		  ## 配置文件
├── docs
│   ├── NDR系统API说明文档.md 
│   ├── NDR系统整体架构说明文档.md
│   └── install
└── requirements.txt
```

# 配置启动

## 安装 Gunicorn


## 配置 Gunicorn 
```python
# -*- coding: utf-8 -*-
# @Time    : 2019-05-50 15:54

from gevent import monkey
import multiprocessing

monkey.patch_all()
debug = True
loglevel = 'debug'
bind = '127.0.0.1:8080'
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = 'gevent'
```
## 启动
```bash
gunicorn -c gconfig.py  app:app
```

## 配置 Nginx
```bash
location ~ /api/ {
        proxy_set_header x-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_pass http://localhost:8080;
    }
```

## 编译grpc
```bash
# 安装编译proto工具
pip3 install grpc_tools==1.48.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
# 编译proto，注意：如果proto文件没有改变或者新增，不需要重新编译grpc
cd protos
python3 -m grpc_tools.protoc --proto_path=. --python_out=../protos_pb --grpc_python_out=../protos_pb  *.proto

# 生产环境需要安装
pip3 install grpcio==1.48.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip3 install protobuf==3.14.0 -i https://pypi.tuna.tsinghua.edu.cn/simple
```