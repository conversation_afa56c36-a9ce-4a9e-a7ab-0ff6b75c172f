{
    "type": "object",
    "required": ["startTime", "stopTime"],
    "properties": {
        "startTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "pattern": r"^\d{12,13}$"
        },
        "timeMode": {
            "type": "string",
            "default": "occurred",
            "enum": ["occurred", "observed"]
        },
        "sort": {
            "type": "string",
            "default": "count",
            "enum": ["count","score"]
        },
        "country": {
            "type": "string",
            "default": ""
        }
    }
}