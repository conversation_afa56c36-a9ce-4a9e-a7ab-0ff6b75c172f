import React, { useEffect, useState } from "react";
import { Tag, message as Message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import moment from "moment";
import { getInfoLogs, getInfoVersion} from '@/services/knowledgeCenter';
import styles from "./style.less";
import UpgradeItem from "../components/upgrade";

interface DataType {
  user: string;
  updateType: string;
  startTime: number;
  endTime: number;
  beforeVersion: string;
  affterVersion: string;
  status: string
}

const Upgrade = () => {

  const [total, setTotal] = useState(0);
  const [params, setParams] = useState({
    page: 1,
    pageSize: 10
  })
  const [currentMsg, setCurrentMsg] = useState<{ version: string }>({
    version: ''
  })
  const [data, setData] = useState<DataType[]>([]);
  const columns: ColumnsType = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: 'start_time',
      render: (text: number) => moment(text * 1000).format('yyyy-MM-DD HH:mm:ss')
    },
    {
      title: '结束时间',
      dataIndex: 'stop_time',
      key: 'stop_time',
      render: (text: number) => moment(text * 1000).format('yyyy-MM-DD HH:mm:ss')
    },
    {
      title: '原始版本',
      dataIndex: 'first_version',
      key: 'first_version',
    },
    {
      title: '更新后版本',
      dataIndex: 'end_version',
      key: 'end_version',
    },
    {
      title: '执行状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => text === 'success' ? <Tag color="green">success</Tag> : <Tag color="red">error</Tag>
    },
  ]

  // 获取当前版本信息
  const getFeatureMsg = () => {
    getInfoVersion().then((res: any) => {
      if (res.flag) {
        setCurrentMsg({ version: res.data.version })
      } else {
        Message.error(res.message)
      }
    }).catch(error => {
      Message.error(error)
    })
  }

  // 获取升级日志
  const getUpdateLogs = () => {
    getInfoLogs(params).then((res: any) => {
      if (res.flag) {
        setData(res.data.log)
        setTotal(res.data.total)
      } else {
        Message.error(res.message)
      }
    }).catch(error => {
      Message.error(error)
    })
  }

  useEffect(() => {
    getUpdateLogs();
    getFeatureMsg();
  }, [params])

  return <div className={styles.upgrade}>
    <UpgradeItem 
    title='情报库升级' 
    updateUrl='/mica-api/api/v1/customize/ioc/update'
    updateFilename='fileName'
    data={data} 
    total={total} 
    currentMsg={currentMsg} 
    columns={columns} 
    params={params} 
    setParams={setParams} 
    getUpdateLogs={getUpdateLogs} 
    getFeatureMsg={getFeatureMsg} />
  </div>
}

export default Upgrade;