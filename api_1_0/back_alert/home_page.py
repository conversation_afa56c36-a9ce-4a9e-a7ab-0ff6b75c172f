# -*- coding: utf-8 -*-
# @Time    : 2019-07-18 15:30
# <AUTHOR> Shark
# @File    : top_threat_score.py
# @Software: PyCharm
import calendar
import ipaddress
from datetime import datetime

from flask import request
from flask_restful import Resource

from utils.database import MongoDB
from utils.ndr_base import NdrResource
from utils.utils import flask_response
from utils import param_check
from utils.param_check import Validator
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class FallAssets(Resource):
    """失陷资产统计，统计热度top20"""

    def __init__(self):
        self.mongodb = MongoDB("ndr")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):

        start_time = int(kwargs["startTime"])
        end_time = int(kwargs["endTime"])

        if start_time >= end_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, [{}])
        # rst = self.mongodb.find("assets_info", {
        #     "$and": [{"labels": {"$in": ["失陷资产"]}}, {"create_time": {'$gte': start_time, '$lte': end_time}}]}).sort(
        #     [("popular", -1)]).limit(20)
        try:
            rst = self.mongodb.find("assets_info", {"create_time": {'$gte': start_time, '$lte': end_time}}) \
                .sort([("popular", -1)])
            return_data = []
            for asset in rst:
                if ipaddress.ip_address(asset['ip_addr']).is_private:
                    return_data.append({"ip_addr": asset['ip_addr'],
                                        "type": asset['type'],
                                        "level": "",
                                        "enable_port": list(asset['enable_port'].keys()),
                                        "send_count": asset['send_count'],
                                        "accept_count": asset['accept_count'],
                                        "app_total_traffic": asset['app_total_traffic']
                                        })
                if return_data.__len__() > 19:
                    break
            return flask_response('', True, return_data)
        except Exception as e:
            API_LOG.error(e)
            flask_response("时间范围错误，开始时间大于结束时间", False, [{}])


class KillchainStatistics(NdrResource):
    """杀伤链统计"""

    def __init__(self):
        super(KillchainStatistics, self).__init__(es_template="home_page_killchain")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["startTime"])
        end_time = int(kwargs["endTime"])
        if start_time >= end_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, {})
        query = {
            "range": {
                "observedTime": {
                    "gte": start_time,
                    "lte": end_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(query)
        # 连接ES查询
        try:
            res = self.es_client.search(index="rule-eve,ioc-eve,model-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    @staticmethod
    def data_format(res):
        buckets = res["aggregations"]["groups"]["buckets"]
        data_return = {
            "Reconnaissance": 0,
            "Weaponization": 0,
            "Delivery": 0,
            "Exploitation": 0,
            "Installation": 0,
            "Command and Control": 0,
            "Actions on Objective": 0
        }
        for bucket in buckets:
            data_return[bucket["key"]] = bucket["doc_count"]
        return data_return


class ThreatLevel(NdrResource):
    """威胁等级统计"""

    def __init__(self):
        super(ThreatLevel, self).__init__(es_template="home_page_threat_level")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["startTime"])
        end_time = int(kwargs["endTime"])
        if start_time >= end_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, {})
        query = {
            "range": {
                "observedTime": {
                    "gte": start_time,
                    "lte": end_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(query)
        # 连接ES查询
        try:
            res = self.es_client.search(index="rule-eve,ioc-eve,model-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    @staticmethod
    def data_format(res):
        buckets = res["aggregations"]["groups"]["buckets"]
        data_return = {"totalCount": res["hits"]["total"]["value"], "threat": []}
        for bucket in buckets:
            data_return["threat"].append({"threat_level": bucket["key"], "count": bucket["doc_count"]})
        return data_return


class ThreatType(NdrResource):
    """攻击类型统计"""

    def __init__(self):
        super(ThreatType, self).__init__(es_template="home_page_threat_type")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["startTime"])
        end_time = int(kwargs["endTime"])
        if start_time >= end_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, {})
        query = {
            "range": {
                "observedTime": {
                    "gte": start_time,
                    "lte": end_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(query)
        # 连接ES查询
        try:
            res = self.es_client.search(index="rule-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, [{}])
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response("", False, [{}])

    @staticmethod
    def data_format(res):
        buckets = res["aggregations"]["groups"]["buckets"]
        total = res["hits"]["total"]["value"]
        if total == 0:
            return []

        data_return = []
        last_prop = 0
        if len(buckets) > 1:
            for i in range(0, len(buckets) - 1):
                prop = round(buckets[i]["doc_count"] / total, 2)
                threat_type = buckets[i]["key"]
                data_return.append({"threat_type": threat_type, "prop": prop})
                last_prop += prop
        prop = round(1 - last_prop, 2)
        threat_type = buckets[-1]["key"]
        data_return.append({"threat_type": threat_type, "prop": prop})
        return data_return


class LatestAlarm(NdrResource):
    """最新告警统计"""

    def __init__(self):
        super(LatestAlarm, self).__init__(es_template="home_page_latest_alarm")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["startTime"])
        end_time = int(kwargs["endTime"])
        if start_time >= end_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, {})
        query = {
            "range": {
                "observedTime": {
                    "gte": start_time,
                    "lte": end_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(query)
        # 连接ES查询
        try:
            res = self.es_client.search(index="rule-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, [{}])
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [{}])

    @staticmethod
    def data_format(res):
        hits = res["hits"]["hits"]
        return_data = []
        for hit in hits:
            return_data.append({"occurredTime": hit["_source"]["occurredTime"],
                                "eve_type": hit["_source"]["eve_type"],
                                "src_ip": hit["_source"]["flow"]["src_ip"],
                                "dst_ip": hit["_source"]["flow"]["dst_ip"],
                                "threatFlag": hit["_source"]["threatFlag"],
                                "threatScore": hit["_source"]["threatScore"],
                                "killchain": hit["_source"]["killchain"],
                                })
        return return_data


class AttackSource(NdrResource):
    """攻击源top5"""

    def __init__(self):
        super(AttackSource, self).__init__(es_template="home_page_attack_source")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["startTime"])
        end_time = int(kwargs["endTime"])
        if start_time >= end_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, {})
        query = {
            "range": {
                "observedTime": {
                    "gte": start_time,
                    "lte": end_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(query)
        # 连接ES查询
        try:
            res = self.es_client.search(index="rule-eve,ioc-eve,model-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, [{}])
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [{}])

    @staticmethod
    def data_format(res):
        buckets = res["aggregations"]["groups"]["buckets"]
        data_return = []
        for bucket in buckets:
            data_return.append({"ip": bucket["key"], "count": bucket["doc_count"]})
        return data_return


class AttackLocation(NdrResource):
    """攻击地理位置"""

    def __init__(self):
        super(AttackLocation, self).__init__(es_template="home_page_attack_location")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["startTime"])
        end_time = int(kwargs["endTime"])
        if start_time >= end_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, {})
        query = {
            "range": {
                "observedTime": {
                    "gte": start_time,
                    "lte": end_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(query)
        # 连接ES查询
        try:
            res = self.es_client.search(index="rule-eve,ioc-eve,model-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, [{}])
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [{}])

    @staticmethod
    def data_format(res):
        hits = res["hits"]["hits"]
        return_data = []
        for hit in hits:
            return_data.append({"latitude": hit["_source"]["srcIpGeoInfo"]["latitude"],
                                "longitude": hit["_source"]["srcIpGeoInfo"]["longitude"],
                                "occurredTime": hit["_source"]["occurredTime"],
                                "dst_ip": hit["_source"]["flow"]["dst_ip"],
                                "src_ip": hit["_source"]["flow"]["src_ip"]
                                })
        return return_data


class AlarmTrend(NdrResource):
    """告警趋势"""

    def __init__(self):
        super(AlarmTrend, self).__init__(es_template="home_page_alarm_trend")

    @param_check.check_flask_args(Validator('get_home_page_schema'), request)
    def get(self, **kwargs):
        '''
            当间隔等于一天时，按照小时进行展示
            当间隔时间大于一天不满一个月，按照天进行展示
            当间隔时间大于一个月按照月进行展示
        '''
        start_time = int(kwargs["startTime"])
        stop_time = int(kwargs["endTime"])
        if start_time >= stop_time:
            return flask_response("时间范围错误，开始时间大于结束时间", False, [{}])
        query = {
            "range": {
                "observedTime": {
                    "gte": start_time,
                    "lte": stop_time
                }
            }
        }
        self.es_template["query"]["bool"]["must"].append(query)

        # 一天按照小时展示
        if stop_time - start_time == 3600 * 24 * 1000:
            time_period = '1h'
        # 大于1天小于31天按照天进行展示
        elif stop_time - start_time < 3600 * 24 * 31 * 1000:
            time_period = '1d'
        # 超过30天按照月进行展示
        else:
            time_period = '1M'
        self.es_template["aggs"]["group_by"]["date_histogram"]["interval"] = time_period
        # 连接ES查询
        try:
            res = self.es_client.search(index="rule-eve,ioc-eve,model-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, [{}])
        try:
            data = self.data_format(res, time_period, start_time, stop_time)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [{}])

    @staticmethod
    def data_format(res, time_period, start_time, stop_time):
        time_block_list = list()
        if time_period == "1h":
            index = start_time
            while index < stop_time:
                time_block_list.append(index)
                index += 3600 * 1000
        elif time_period == "1d":
            index = start_time
            while index < stop_time:
                time_block_list.append(index)
                index += 3600 * 24 * 1000
        else:
            start_date = datetime.fromtimestamp(int(start_time / 1000))
            index = int(start_date.replace(day=1, hour=0, minute=0, second=0).timestamp() * 1000)
            while index < stop_time:
                time_block_list.append(index)
                start_date = datetime.fromtimestamp(index / 1000)
                monthRange = calendar.monthrange(start_date.year, start_date.month)[1]
                index += 3600 * 24 * monthRange * 1000
        return_data = [
            {
                "time": time_block,
                "count": 0

            } for time_block in time_block_list
        ]
        buckets = res["aggregations"]["group_by"]["buckets"]
        for bucket in buckets:
            index = time_block_list.index(bucket["key"])
            return_data[index]["count"] = bucket["doc_count"]
        return return_data
