
# -*- coding: utf-8 -*-
# @Time    : 2020-03-23 08:10
# <AUTHOR> wu
# @File    : help.py
# @Software: PyCharm

"""about"""
from flask_restful import Resource
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from utils.database import MongoDB

LOG = get_ndr_logger('api_log', __file__)


class Help(Resource):
    """关于"""
    def __init__(self):
        self.mongodb = MongoDB("ndr")

    def get(self):
        """about"""
        ndr_help = self.mongodb.find_one('ndr_help', {}, {"_id": 0})
        if ndr_help:
            return flask_response("", True, ndr_help)
        return flask_response("Get help info failed.", False, {})

    def set(self):
        """
        :return:
        """
        # 系统初始化时，获取相关参数写入 mongo
        h_info = \
            {
                "faq": [
                    {
                        "id": "faq_1",
                        "title": "扫描的目标，可以是IP地址或者域名吗？",
                        "content": "可以。产品所针对的目标，包括IP地址和域名。"
                    },
                    {
                        "id": "faq_2",
                        "title": "扫描可以获取目标哪些信息？",
                        "content": "针对目标进行资源探测，可以获取以下信息：是否存活、设备类型、设备厂商、设备品牌、设备型号、地理位置、操作系统、开放端口、使用软件和版本等。"
                    }
                ],
                "term": [
                    {
                        "id": "term_1",
                        "title": "目标",
                        "content": "单个目标指的是：IP地址或者域名。"
                    },
                    {
                        "id": "term_2",
                        "title": "目标组",
                        "content": "一个目标组，包含一个或多个目标。"
                    },
                    {
                        "id": "term3",
                        "title": "目标组名称",
                        "content": "用来表示目标组。不同的目标组，名称不相同。目标组名称，在系统中具有唯一性。"
                    }
                ]
            }

        self.mongodb.insert_one("help", h_info)
