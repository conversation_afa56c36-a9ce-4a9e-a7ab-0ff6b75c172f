import React, { useState } from 'react';
import { Tabs } from 'antd';
import Single from './single';
import Group from './group';
import Upgrade from "./upgrade";
const { TabPane } = Tabs;
const Index = () => {
  const [activeKey, setactiveKey] = useState('1');
  const [group, setgroup] = useState({});
  const getValue = (value: React.SetStateAction<{}>) => {
    setactiveKey('1');
  };
  const onChange = (value: React.SetStateAction<string>) => {
    setactiveKey(value);
  };
  return (
    <Tabs activeKey={activeKey} onChange={onChange}>
      <TabPane tab="特征管理" key="1">
        {activeKey === '1' ? <Single group={group} /> : ''}
      </TabPane>
      <TabPane tab="特征组管理" key="2">
        {activeKey === '2' ? <Group getValue={getValue} /> : ''}
      </TabPane>
      <TabPane tab="特征库升级" key="3">
        {activeKey === '3' ? <Upgrade /> : ''}
      </TabPane>
    </Tabs>
  );
};
export default Index;
