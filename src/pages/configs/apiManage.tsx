/*
 * @Author: 田浩
 * @Date: 2021-08-18 11:21:23
 * @LastEditors: tianh
 * @LastEditTime: 2022-07-06 10:54:14
 * @Descripttion:
 */
import React, { useState } from 'react';
import { Tabs } from 'antd';
import Interface from './interface';
import Work from './work';
const { TabPane } = Tabs;
const ApiManage = () => {
  const [activeKey, setactiveKey] = useState('work');
  const changeTabs = (value: string) => {
    setactiveKey(value);
  };
  return (
    <div>
      <Tabs onChange={changeTabs} activeKey={activeKey} defaultActiveKey="work" destroyInactiveTabPane>
        <TabPane tab="工作口设置" key="work">
          <Work />
        </TabPane>
        <TabPane tab="接口转发配置" key="interface">
          <Interface />
        </TabPane>
      </Tabs>
    </div>
  );
};
export default ApiManage;
