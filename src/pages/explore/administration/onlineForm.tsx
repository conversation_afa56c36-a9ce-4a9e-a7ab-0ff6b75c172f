import React, { useState, useEffect, forwardRef } from 'react';
import { Form, Input, Select, Button, message } from 'antd';
import { Base64 } from 'js-base64';
import { onlineServerType, isConnect } from '@/utils/enumList';
import { addOnline } from '@/services/explore';

const { Option } = Select;

const onlineForm = forwardRef((props: any, ref: any) => {
  const { isAdd, detailparams } = props;
  const [form] = Form.useForm();
  const [formValue, setformValue] = useState(props.Formvalue);
  const layout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };
  const [collect, setcollect] = useState(false);

  useEffect(() => {
    if (formValue.serverType === 'nas' && formValue.username === '') {
      setformValue({
        ...formValue,
        serverType: 'nfs',
      });
    }
    form.setFieldsValue(formValue);
  }, [formValue]);

  const handleConnect = () => {
    form.validateFields().then((value: any) => {
      if (value.serverType === 'ftp') {
        value.port = parseInt(value.port);
      } else {
        delete value.port;
      }
      if (value.serverType === 'nfs') {
        value.serverType = 'nas';
        value.username = '';
        value.password = '';
      }
      value.password = Base64.encode(value.password);

      if (isAdd) {
        addOnline(value).then(res => {
          if (res.flag) {
            props.showDetail({
              serverId: res.data.serverId,
            });
            setcollect(true);
          } else {
            message.error(res.message);
          }
        });
      } else {
        props.showDetail({
          serverId: detailparams.serverId,
        });
        setcollect(true);
      }
    });
  };
  const changeType = (value: any) => {
    formValue.serverType = value;
    setformValue({ ...formValue });
  };

  return (
    <Form ref={ref} form={form} {...layout}>
      <div>
        <Form.Item
          label="数据源名称"
          name="aliasName"
          rules={[
            {
              required: true,
              message: '请填写!',
            },
          ]}
        >
          <Input disabled={!isAdd} placeholder="请输入数据源名称" />
        </Form.Item>
        <Form.Item
          label="用途"
          name="type"
          rules={[
            {
              required: true,
              message: '请选择用途!',
            },
          ]}
        >
          <Select>
            <Option value="pcap" children="流量包存放" />
            <Option value="other" children="普通文件存放" />
          </Select>
        </Form.Item>
        <Form.Item
          label="数据源类型"
          name="serverType"
          rules={[
            {
              required: true,
              message: '请选择类型!',
            },
          ]}
        >
          <Select disabled={!isAdd} onChange={changeType}>
            {onlineServerType.map((item, index) => {
              return (
                <Option key={index} value={item.value}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Form.Item
            style={{ marginLeft: 14 }}
            wrapperCol={{ span: 16 }}
            labelCol={{ span: 8 }}
            label={<span>远端主机IP</span>}
            name="host"
            rules={[
              {
                required: true,
                message: '请填写!',
              },
            ]}
          >
            <Input disabled={!isAdd} />
          </Form.Item>
          {formValue.serverType === 'ftp' ? (
            <Form.Item
              wrapperCol={{ span: 12 }}
              labelCol={{ span: 8 }}
              label="端口"
              name="port"
              rules={[
                {
                  required: true,
                  message: '请填写!',
                },
              ]}
            >
              <Input disabled={!isAdd} placeholder="请输入端口" />
            </Form.Item>
          ) : (
            <Form.Item
              label="目录名"
              name="filename"
              rules={[
                {
                  required: true,
                  message: '请输入目录名!',
                },
              ]}
            >
              <Input disabled={!isAdd} />
            </Form.Item>
          )}
        </div>
        {formValue.serverType !== 'nfs' ? (
          <div>
            <Form.Item
              label="用户名"
              name="username"
              rules={[
                {
                  required: true,
                  message: '请填写用户名!',
                },
              ]}
            >
              <Input disabled={!isAdd} placeholder="请输入用户名" />
            </Form.Item>
            <Form.Item
              label="密码"
              name="password"
              rules={[
                {
                  required: true,
                  message: '请填写密码!',
                },
              ]}
            >
              <Input type="password" disabled={!isAdd} placeholder="请输入密码" />
            </Form.Item>
          </div>
        ) : null}
        <Form.Item
          label="是否连接"
          name="enable"
          rules={[
            {
              required: true,
              message: '请填写密码!',
            },
          ]}
        >
          <Select disabled>
            {isConnect.map(item => {
              return (
                <Option key={item.key} value={item.value}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        {collect ? null : (
          <Form.Item label="状态">
            <Button type="primary" onClick={handleConnect} disabled={!isAdd}>
              连接
            </Button>
          </Form.Item>
        )}
      </div>
    </Form>
  );
});
export default onlineForm;
