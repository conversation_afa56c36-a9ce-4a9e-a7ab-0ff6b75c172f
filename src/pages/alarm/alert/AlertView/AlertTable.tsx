/* eslint-disable @typescript-eslint/no-unused-vars */
import { handleConfirm } from '@/services/alarm';
import { THREAT_LEVEL } from '@/utils/constants';
import { alarmHandling, loopHoleSort, alarmHandlingName } from '@/utils/enumList';
import { Button, Modal, Select, Table, Tooltip, message, notification, Space, Radio, Form } from 'antd';
import cn from 'classnames';
import { groupBy, throttle } from 'lodash';
import React from 'react';
import EventDetail from './EventDetail';
import AddToWhiteList from './AddToWhiteList';
import { preparePayload, colRenderer, FilterState } from './shared';
import IPGroupDetail from './IPGroupDetail';
import KillChainStages from '@/components/KillChainStages';
import { download } from './download';
import styles from './styles.less';

interface AlertTableProps {
  className?: string;
  filter: FilterState;
  value: any;
  loading: boolean;
  type: string;
  onChange: (values: any) => void;
}

export default function AlertTable({ className, loading, type, filter, value, onChange }: AlertTableProps) {
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = React.useState([]);

  const [scroll, setScroll] = React.useState({ y: 300, x: 1200 });
  const tableWrapperEl = React.useRef();
  React.useEffect(() => {
    setScroll({ x: (tableWrapperEl.current?.clientWidth || 1200) - 32 });

    const handleResize = throttle(() => {
      if (!tableWrapperEl.current) return;
      setScroll({
        x: tableWrapperEl.current?.clientWidth - 32,
      });
    }, 2000);

    let resizeObserver;
    if (window.ResizeObserver !== undefined) {
      resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(tableWrapperEl.current);
    }

    return () => {
      if (resizeObserver) {
        if (tableWrapperEl.current) {
          resizeObserver.unobserve(tableWrapperEl.current);
        } else {
          resizeObserver.disconnect();
        }
      }
    };
  }, []);

  const handleEventConfirm = (state = 1, keys = selectedRowKeys) => {
    return handleConfirm({
      type,
      confirm: state,
      id_list: Array.from(
        new Set(
          keys
            .map((x) => x?.split('\x01')?.[0]?.split(',') || [])
            .reduce((arr, x) => arr.concat(x), [])
            .filter(Boolean),
        ),
      ),
    }).then((res) => {
      if (res.flag) {
        message.success(res.message);
        setSelectedRowKeys([]);
        setTimeout(() => {
          onChange({ ...filter });
        }, 1000);
      } else {
        message.warning(res.message);
      }
    });
  };

  const handleExport = (e: any) => {
    fetch(`/mica-api/api/v1/${type === 'vul' ? 'alert' : type}/export`, {
      method: 'POST',
      body: JSON.stringify(preparePayload(filter)),
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
    })
      .then(async (res) => {
        if (res.headers.get('content-type')?.includes('application/json')) {
          const resp = await res.json();
          throw new Error(resp?.message);
        }
        const contentType = res.headers.get('content-type') || '';
        const contentDisposition = res.headers.get('content-disposition') || '';
        const filename =
          contentDisposition.match(/attachment; filename="?([^"]+)"?/)?.[1] || value.pcap_filename || value.not_fss_pcapname;
        const blob = await res.blob();
        download(blob, filename, contentType);
      })
      .catch((error) => {
        notification.error({
          message: `导出失败`,
          description: error?.message,
        });
      });
  };

  const handleAlertModalConfirmOk = (uniqueId: string) => {
    handleEventConfirm(form.getFieldValue('confirm'), [uniqueId]);
    Modal.destroyAll();
  };

  const uniqueIdCols = [
    {
      title: '发现时间',
      dataIndex: 'observedTime',
      width: 120,
      className: 'ws-nowarp',
      fixed: 'left',
      render: (occurredTime: any) => {
        const time = new Date(occurredTime);
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">{occurredTime ? time.toLocaleDateString('swe') : ''}</div>
            <div className="text-left ws-nowrap">{occurredTime ? time.toLocaleTimeString('swe') : ''}</div>
          </div>
        );
      },
    },
    {
      title: '源IP',
      key: 'src_ip',
      dataIndex: 'flow',
      className: 'ws-nowarp',
      render: colRenderer.srcIp,
    },
    {
      title: '目的IP',
      key: 'dst_ip',
      dataIndex: 'flow',
      className: 'ws-nowarp',
      render: colRenderer.dstIp,
    },
    {
      title: '威胁分类',
      dataIndex: 'threatType',
      className: 'ws-nowrap',
      render(_: any, record: any) {
        switch (type) {
          case 'vul':
            return colRenderer.threatCategory(record?.threatFlag);
          default:
            return record?.threatType;
        }
      },
    },
    {
      title: '威胁等级',
      dataIndex: 'threatLevel',
      className: 'ws-nowrap',
      render: colRenderer.threatLevel,
      filters: Object.entries(THREAT_LEVEL).map(([value, text]) => ({ text, value })),
      filterMultiple: false,
      filteredValue: filter.threatLevel ? [filter.threatLevel] : null,
    },
    type === 'model'
      ? {
          title: '模型名称',
          dataIndex: 'modelName',
        }
      : type === 'ioc'
        ? { title: '告警名称', dataIndex: 'threadName' }
        : {
            title: '告警名称',
            dataIndex: 'vulName',
            className: 'ws-nowrap max-w-40 2xl:max-w-70',
            render: (vulName: string, record: any) => {
              if (type === 'file') {
                return (
                  <div>
                    <Tooltip overlay={record['threatName']}>
                      <div className="truncate">{record['threatName']}</div>
                    </Tooltip>
                  </div>
                );
              }
              return (
                <div>
                  <Tooltip overlay={vulName}>
                    <div className="truncate">{colRenderer.alertName(type, record)}</div>
                  </Tooltip>
                  <div className="text-sm c-t4">{record.cve}</div>
                </div>
              );
            },
          },
    {
      title: '杀伤链阶段',
      dataIndex: 'killchain',
      className: 'ws-nowrap',
      render: (value) => <KillChainStages data={value} />,
    },
    {
      title: '任务名称',
      dataIndex: 'taskType',
      className: 'ws-nowrap',
      render: (t: any, row: { pcap_filename: string; taskName: string }) => {
        if (t === 'replay') {
          const strArr = row.pcap_filename.split('/');
          const pkgName = strArr?.[strArr.length - 1];
          return (
            <>
              <div className="truncate" title={row.taskName}>
                回放任务({row.taskName})
              </div>
              <div className="text-sm c-t4 truncate" title={pkgName}>
                {pkgName}
              </div>
            </>
          );
        }

        return '实时任务';
      },
    },
    {
      title: '告警处理',
      dataIndex: 'confirm',
      className: 'ws-nowrap',
      width: 112,
      fixed: 'right',
      render: (confirm: number, record: any) => (
        <div>
          <Button
            className="!p-0"
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              Modal.confirm({
                title: '告警处理',
                icon: null,
                centered: true,
                width: 600,
                className: styles.alertTableModalConfirmWrap,
                content: (
                  <>
                    <div>{record.describe}</div>
                    <div className="mt-4">
                      <div className={`mb-1 ${styles.bolder}`}>安全建议：</div>
                      <div className="ws-pre-line">{record.advice}</div>
                    </div>
                    <Form form={form}>
                      <Form.Item
                        labelCol={{ style: { minWidth: '0' } }}
                        label="标记为:"
                        style={{ marginTop: '24px', textAlign: 'center' }}
                        name="confirm"
                      >
                        <Radio.Group>
                          {Object.entries(alarmHandling).map(([name, value]) => (
                            <Radio value={value}>{name}</Radio>
                          ))}
                        </Radio.Group>
                      </Form.Item>
                    </Form>
                    <div style={{ width: '100%', marginTop: '24px', textAlign: 'right' }}>
                      <Space>
                        <Button
                          onClick={() => {
                            Modal.destroyAll();
                          }}
                        >
                          取消
                        </Button>
                        {/* <Button
                          type="primary"
                          onClick={() => {
                            handleAlertModalConfirmOk(alarmHandling['已关注'], record.uniqueId);
                          }}
                        >
                          标记为已关注
                        </Button> */}
                        {/* <Button
                          type="primary"
                          onClick={() => {
                            handleAlertModalConfirmOk(alarmHandling['已处理'], record.uniqueId);
                          }}
                        >
                          标记为已处理
                        </Button> */}
                        <Button
                          type="primary"
                          onClick={() => {
                            handleAlertModalConfirmOk(record.uniqueId);
                          }}
                        >
                          确定
                        </Button>
                      </Space>
                    </div>
                  </>
                ),
              });
            }}
          >
            {alarmHandlingName[confirm]}
          </Button>
          {filter.groupKey === 'uniqueId' && <div className="text-sm c-t4 lh-1em"></div>}
        </div>
      ),
    },
    {
      title: '操作',
      dataIndex: 'confirm',
      className: '!text-center ws-nowrap',
      width: 80,
      fixed: 'right',
      render: (t: any, record: any) => (
        <AddToWhiteList filter={filter} value={record} onChange={onChange}>
          加白
        </AddToWhiteList>
      ),
    },
  ];

  let props = { columns: [] };
  switch (filter.groupKey) {
    case 'src_ip':
    case 'dst_ip':
      props = {
        rowKey: filter.groupKey,
        columns: [
          {
            title: filter.groupKey === 'dst_ip' ? '目的IP' : '源IP',
            dataIndex: filter.groupKey,
            className: 'ws-nowarp b-r b-r-solid b-r-brd5',
            render: (ip: string, record: any) => (
              <div>
                <div className="text-bold">
                  {ip}
                  {record?.organisation ? <span className="tag v-middle ml-2">{record.organisation}</span> : null}
                </div>
                <div className="text-sm c-t4">
                  {record.country_name ? (
                    <>
                      <span>{record.country_name}</span>
                      <span className="ml-1">{record.city_name}</span>
                    </>
                  ) : (
                    '--'
                  )}
                </div>
              </div>
            ),
          },
          {
            title: filter.groupKey === 'dst_ip' ? '源IP' : '目的IP',
            dataIndex: 'ip_list',
            className: 'ws-nowarp',
            render: (ip_list: string[]) => (
              <div>
                {ip_list?.slice(0, 10)?.map((value) => {
                  const [ip, tag] = value.split(',');
                  return (
                    <div key={ip}>
                      <span className="tag">{ip}</span>
                      {tag && <span className="tag ml-2 c-t4">{tag}</span>}
                    </div>
                  );
                })}
                {ip_list?.length > 10 && <div className="c-t4 text-sm">...共{ip_list.length}条</div>}
              </div>
            ),
          },
          {
            title: type === 'model' ? '模型名称' : '告警名称',
            dataIndex: 'alarm_name',
            className: 'max-w-40',
            render: (names: { [k: string]: number }, record: any) => {
              const warns = Object.entries(names || {});
              warns.sort((a, b) => b[1] - a[1]);

              return (
                <div className="flex flex-col items-start gap-2">
                  {warns?.slice(0, 10).map(([name, level]) => (
                    <Tooltip key={name} overlay={name}>
                      <div
                        className={cn('truncate max-w-100%', {
                          'tag-notice': level == 0,
                          'tag-warn': level == 1,
                          'tag-error': level == 2,
                        })}
                      >
                        {name}
                      </div>
                    </Tooltip>
                  ))}
                  {warns?.length > 10 && <div className="c-t4 text-sm">...共{warns.length}条</div>}
                </div>
              );
            },
          },
          {
            title: '告警次数',
            dataIndex: 'alarm_count',
            className: 'ws-nowrap',
            render: (count: number) => `${count || 0}次`,
          },
          {
            title: '首次发现时间',
            dataIndex: 'first_time',
            className: 'w-48 ws-nowrap',
            render: (time: number) => (time ? new Date(time).toLocaleString('swe') : ''),
          },
          {
            title: '最后发现时间',
            dataIndex: 'end_time',
            className: 'w-48 ws-nowrap',
            render: (time: number) => (time ? new Date(time).toLocaleString('swe') : ''),
          },
          {
            title: '操作',
            dataIndex: 'confirm',
            className: '!text-center',
            render: (t: any, record: any) => (
              <IPGroupDetail type={type} filter={filter} record={record}>
                详情
              </IPGroupDetail>
            ),
          },
        ],
      };
      break;
    case 'uniqueId':
      props = {
        columns: uniqueIdCols,
        onRow: (record) => ({
          onClick: () => {
            if (expandedRowKeys.includes(record.uniqueId)) {
              setExpandedRowKeys([]);
            } else {
              setExpandedRowKeys([record.uniqueId]);
            }
          },
        }),
        expandable: {
          expandedRowKeys,
          expandIconColumnIndex: -1,
          expandedRowRender(record: any, index): React.ReactElement {
            if (!expandedRowKeys.includes(record.uniqueId)) return null;
            const { uniqueId, confirm, pcap_filename, celeryId, modelName, threatLevel, threatScore, taskName, taskType } =
              record;
            return (
              <EventDetail
                record={record}
                type={type}
                modelName={modelName}
                celeryId={celeryId}
                pcap_filename={pcap_filename}
                id={uniqueId}
                confirm={confirm}
                threatLevel={threatLevel}
                threatScore={threatScore}
                groupKey={filter.groupKey}
                taskName={taskName}
                taskType={taskType}
                value={record?.[filter.groupKey]}
                filter={filter}
              />
            );
          },
        },
      };
      break;
    case 'raw':
      props = { columns: uniqueIdCols };
      break;
  }

  const { tableData, total } = React.useMemo(() => {
    return {
      tableData: value?.alerts || [],
      total: value?.total,
    };
  }, [value]);

  return (
    <div className={cn('mt-4 p-4 bg-d0', className)}>
      <div className="flex items-center">
        <div className="flex-[1]">
          <Select className="w-40" value={filter.groupKey} onChange={(groupKey) => onChange({ ...filter, groupKey })}>
            <Select.Option value="uniqueId">威胁事件模式</Select.Option>
            <Select.Option value="src_ip">源IP聚合模式</Select.Option>
            <Select.Option value="dst_ip">目的IP聚合模式</Select.Option>
            <Select.Option value="raw">原始告警模式</Select.Option>
          </Select>
          <Select
            className="w-40 !ml-2"
            value={`${filter.sort}\x01${filter.order}`}
            onChange={(value: string) => {
              const [sort, order] = value.split('\x01');
              onChange({ ...filter, sort, order });
            }}
          >
            {loopHoleSort.map((item) => (
              <Select.Option key={item.key} value={`${item.payload.sort}\x01${item.payload.order}`}>
                {item.name}
              </Select.Option>
            ))}
          </Select>
        </div>
        <Tooltip overlay={selectedRowKeys.length ? null : '请选择要处理的告警'}>
          <Button
            type="link"
            disabled={!selectedRowKeys.length}
            onClick={() =>
              Modal.confirm({
                title: '确认将选中告警标记为:',
                icon: null,
                centered: true,
                width: 500,
                content: (
                  <Form form={form}>
                    <Form.Item style={{ marginTop: '24px', textAlign: 'center' }} name="confirm">
                      <Radio.Group>
                        {Object.entries(alarmHandling).map(([name, value]) => (
                          <Radio value={value}>{name}</Radio>
                        ))}
                      </Radio.Group>
                    </Form.Item>
                  </Form>
                ),
                onOk: () => {
                  handleEventConfirm(form.getFieldValue('confirm'));
                },
                afterClose: () => {
                  form.resetFields();
                },
              })
            }
          >
            一键标记为
          </Button>
        </Tooltip>
        <Button ghost className="ml-2" type="primary" onClick={(e) => handleExport(e)}>
          导出
        </Button>
      </div>
      <div className="mt-4" ref={tableWrapperEl}>
        <Table
          key={`${type}${filter.groupKey}${filter.sort}${filter.order}${tableData.length}`}
          {...props}
          tableLayout="auto"
          className="b b-solid b-brd6"
          rowKey={filter.groupKey === 'uniqueId' ? 'uniqueId' : (x, i) => `${x.uniqueId}\x01${filter.page}:${i}:${type}`}
          loading={loading}
          scroll={scroll}
          pagination={React.useMemo(
            () => ({
              size: 'small',
              total: total,
              current: filter.page || 1,
              pageSize: filter.pageSize,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total: number) => `共${total}条`,
            }),
            [filter, total],
          )}
          expandIconAsCell={false}
          onChange={({ current, pageSize }, f) => {
            onChange(
              Object.entries(f).reduce(
                (obj, [field, value]) => {
                  obj[field] = value?.[0] || undefined;
                  return obj;
                },
                {
                  ...filter,
                  pageSize,
                  page: pageSize !== filter.pageSize ? 1 : current,
                },
              ),
            );
            setExpandedRowKeys([]);
          }}
          dataSource={tableData}
          rowSelection={React.useMemo(
            () => ({
              selectedRowKeys,
              onChange: (keys: React.Key[]) => {
                setSelectedRowKeys(keys);
              },
            }),
            [selectedRowKeys],
          )}
        />
      </div>
    </div>
  );
}
