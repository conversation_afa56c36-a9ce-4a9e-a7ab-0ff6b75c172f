import React from 'react';
import { Modal } from 'antd';

import ModalChild from '@/pages/workbench/components/eventCom/modalChild';
import { TimeRange } from '../UnSettledThreat/HighRiskHostCard';

export type Props = {
  show: boolean;
  onClose: () => void;
  currentScene?: Record<string, any>
  dateRange: TimeRange;
}

function SceneDetailModal({ show, onClose, currentScene, dateRange }: Props): JSX.Element {
  return (
    <>
      {
        show && currentScene && (
          <Modal
            title="事件详情"
            visible={show}
            onCancel={() => {
              onClose();
            }}
            footer={null}
            width="80vw"
            centered
            destroyOnClose
          >
            <ModalChild
              selRow={currentScene}
              filter={{ page: 1, pageSize: 2, start_time: dateRange.start_time.toString(), end_time: dateRange.end_time.toString() }}
              eventType='event'
              hideMoreOptions
            />
          </Modal>
        )
      }
    </>
  );
}

export default SceneDetailModal;
