#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
""" 告警数据包下载 """
import os
import re
import time
from datetime import datetime
import requests
from flask_restful import Resource
from flask import request, send_file, make_response
from api_1_0.utils.flask_log import ndr_log_to_box
from config.config import PACKAGE_DIR, StenographerConfig, NdrLog, PACKAGE_SIZE_MAX, StenoPcapPath
from utils import param_check
from utils.database import MongoDB
from utils.param_check import Validator
from utils.utils import ip_proto_dict, flask_response, run_command, generate_random_string
from utils.logger import get_ndr_logger
from utils.cluster import *

API_LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)

def get_tmp_pcapfile_path():
    return "/tmp/pcap_" + generate_random_string(10)

def is_ipv4_address(ip):
    pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if re.match(pattern, ip):
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    return False

def get_node_and_pcap_path(filename:str):
    fs = filename.split(":", 2)
    if len(fs) == 2 and is_ipv4_address(fs[0]):
        node, filename = fs[0], fs[1]
        # node不是自己，才需要去远端获取pcap包
        if node != cluster_get_node_ip():
            return node, filename

    return "", filename

def request_pcap(query_str, file_path, url_list):
    file_size = 0
    file_truncated = False
    with open(file_path, 'wb') as pcap_f:
        for url in url_list:
            rst = requests.post(url, data=query_str, timeout=50,
                                cert=(StenographerConfig.instance['cert'],
                                      StenographerConfig.instance['key']),
                                verify=StenographerConfig.instance['ca'],
                                stream=True)
            for chunk in rst.iter_content(chunk_size=1024):
                if chunk:
                    pcap_f.write(chunk)
                    file_size += len(chunk)
                if file_size > PACKAGE_SIZE_MAX:
                    file_truncated = True
                    break
            ndr_log_to_box(NdrLog.Type.OPERATE, "Download pcap: %s + %s; ret_len=%d, bTruncated=%s" %
                               (url, query_str, pcap_f.tell(), file_truncated))
    return

class Package(Resource):
    """ 根据所给条件下载特定数据包 """
    @staticmethod
    def get_format_date_str(timestamp):
        return time.strftime("%Y%m%d", time.localtime(int(int(timestamp) / 1000)))

    def replay_pcap_download(self, kwargs):
        alert_type = kwargs["args"].get("type")
        # 对于重放包，每个节点都有一份，所以无需从远端读取
        _, org_path = get_node_and_pcap_path(kwargs["args"].get("pcap_filename"))
        src_port = kwargs['args'].get('src_port')
        dst_port = kwargs['args'].get('dst_port')
        src_ip = kwargs["args"].get("src_ip")
        dst_ip = kwargs["args"].get("dst_ip")
        src_ip_type = 'ip6' if ':' in src_ip else 'ip'
        dst_ip_type = 'ip6' if ':' in dst_ip else 'ip'
        file_path = os.path.join('/tmp', '_'.join(
            [alert_type, self.get_format_date_str(kwargs["args"].get("occurredTime", 0)), src_ip, dst_ip, src_port, dst_port])) + '.pcap'
        if os.path.exists(file_path):
            os.remove(file_path)
        # 根据条件过滤原始数据包并导出
        command = "tcpdump -r '%s' %s host %s and %s host %s and port %s and port %s -w %s" % (
            org_path, src_ip_type, src_ip, dst_ip_type, dst_ip, src_port, dst_port, file_path
        )
        ret, msg = run_command(command)
        print('download pcap command: [%s]' % command)
        if not ret:
            return None, msg

        return file_path, ''

    def not_fss_download_fn(self, kwargs):
        try:
            node = ""
            replay = kwargs["args"].get("replay")
            if replay == 'true':
                org_path = kwargs["args"].get("pcap_filename")
                if not os.path.exists(org_path):
                    return flask_response("找不到原始文件！", False, {'msg': '找不到原始文件！'})
                file_path, msg = self.replay_pcap_download(kwargs)
                if not file_path:
                    return flask_response("下载数据包失败！", False, {'msg': msg})
            else:
                node, filename = get_node_and_pcap_path(kwargs["args"].get("not_fss_pcapname"))
                file_path = os.path.join(
                    StenoPcapPath.Path, self.get_format_date_str(kwargs["args"].get("occurredTime", 0))) + '/' + \
                            filename
                if node:
                    tmp_file_path = get_tmp_pcapfile_path()
                    cluster_read_file(node, file_path, tmp_file_path)
                    file_path = tmp_file_path

            if not os.path.exists(file_path):
                return flask_response("找不到文件！", False, {'msg': 'filename: %s' % file_path})

            file_obj = send_file(file_path, as_attachment=True)
            if file_path.startswith('/tmp'):
                os.remove(file_path)
        except Exception as e:
            return flask_response("下载数据包失败！", False, {'msg': str(e)})

        return make_response(file_obj)

    def post_not_fss_download_fn(self, kwargs):
        try:
            replay = kwargs["args"].get("replay")
            if replay == 'true':
                org_path = kwargs["args"].get("pcap_filename")
                if not os.path.exists(org_path):
                    return flask_response("找不到原始文件！", False, {'msg': '找不到原始文件！'})
                file_path, msg = self.replay_pcap_download(kwargs)
                if not file_path:
                    return flask_response("下载数据包失败！", False, {'msg': msg})
            else:
                node, filename = get_node_and_pcap_path(kwargs["args"].get("not_fss_pcapname"))
                file_path = os.path.join(
                    StenoPcapPath.Path, self.get_format_date_str(kwargs["args"].get("occurredTime", 0))) + '/' + \
                            filename
                if node:
                    tmp_file_path = get_tmp_pcapfile_path()
                    cluster_read_file(node, file_path, tmp_file_path)
                    file_path = tmp_file_path

            if not os.path.exists(file_path):
                return flask_response("找不到文件！", False, {'msg': 'filename: %s' % file_path})

            return flask_response('获取成功', True, {"file_path": file_path})
        except Exception as e:
            return flask_response("下载数据包失败！", False, {'msg': str(e)})

    @param_check.check_flask_args(Validator('package_args_check'), request)
    def get(self, **kwargs):
        """
        :param kwargs:
        :return:
        """

        mongodb = MongoDB("ndr")
        raw_data = mongodb.find_one('device_info', {})
        if not raw_data:
            return flask_response("Get device info failed.", False, {})

        # 非全流量留存版本
        if raw_data['divice'] in ['D5000', 'D8000']:
            return self.not_fss_download_fn(kwargs)

        # 先尝试直接从回放原始数据包中下载，如果失败，再从全流量留存中下载
        if kwargs["args"].get("replay") == 'true':
            file_path, _ = self.replay_pcap_download(kwargs)
            if file_path and os.path.exists(file_path):
                file_obj = send_file(file_path, as_attachment=True)
                return make_response(file_obj)

        try:
            node = ""
            if kwargs['args'].get('not_fss_pcapname', ''):
                node, filename = get_node_and_pcap_path(kwargs["args"].get("not_fss_pcapname"))
                time_str = filename.split('_')[1]
                file_path = os.path.join(StenoPcapPath.Path, time_str, filename)
                if node:
                    tmp_file_path = get_tmp_pcapfile_path()
                    cluster_read_file(node, file_path, tmp_file_path)
                    file_path = tmp_file_path
            else:
                file_path = ''

            if not os.path.exists(file_path):
                query_str, file_path, url_list = self.format_args(**kwargs)
                if node:
                    cluster_request_pcap(node, query_str, file_path, url_list)
                    tmp_file_path = get_tmp_pcapfile_path()
                    cluster_read_file(node, file_path, tmp_file_path)
                    file_path = tmp_file_path
                else:
                    request_pcap(query_str, file_path, url_list)
        except requests.exceptions.Timeout:
            return flask_response("下载文件超时！", False, {'msg': ''})
        except Exception as e:
            return flask_response("下载文件出错！", False, {'msg': str(e)})
        # 未捕获到数据包
        if not os.path.exists(file_path):
            return flask_response("找不到文件！", False, {'msg': ''})

        ret = send_file(file_path, as_attachment=True)

        if file_path.startswith('/tmp'):
            os.remove(file_path)

        return make_response(ret)

    @param_check.check_flask_args(Validator('package_args_check'), request)
    def post(self, **kwargs):
        """
        :param kwargs:
        :return:
        """

        mongodb = MongoDB("ndr")
        raw_data = mongodb.find_one('device_info', {})
        if not raw_data:
            return flask_response("Get device info failed.", False, {})

        # 非全流量留存版本
        if raw_data['divice'] in ['D5000', 'D8000']:
            return self.post_not_fss_download_fn(kwargs)

        # 先尝试直接从回放原始数据包中获取，如果失败，再从全流量留存中获取
        if kwargs["args"].get("replay") == 'true':
            file_path, _ = self.replay_pcap_download(kwargs)
            if file_path and os.path.exists(file_path):
                return flask_response('获取成功', True, {"file_path": file_path})
        try:
            node, filename = get_node_and_pcap_path(kwargs["args"].get("not_fss_pcapname"))
            time_str = filename.split('_')[1]
            file_path = os.path.join(StenoPcapPath.Path, time_str, filename)
            if node:
                tmp_file_path = get_tmp_pcapfile_path()
                cluster_read_file(node, file_path, tmp_file_path)
                file_path = tmp_file_path

            if os.path.exists(file_path):
                return flask_response('获取成功', True, {"file_path": file_path})

            query_str, file_path, url_list = self.format_args(**kwargs)
            if node:
                cluster_request_pcap(node, query_str, file_path, url_list)
                tmp_file_path = get_tmp_pcapfile_path()
                cluster_read_file(node, file_path, tmp_file_path)
                file_path = tmp_file_path
            else:
                request_pcap(query_str, file_path, url_list)
        except requests.exceptions.Timeout:
            return flask_response("获取文件信息超时！", False, {'msg': ''})
        except Exception as e:
            return flask_response("获取文件信息出错！", False, {'msg': str(e)})
        # 未捕获到数据包
        if not os.path.exists(file_path):
            return flask_response("找不到文件！", False, {'msg': ''})

        return flask_response('获取成功', True, {"file_path": file_path})

    @staticmethod
    def format_args(**kwargs):
        """
        :param kwargs:
        :return:
        """
        # 查询被被分为两个字句，前一个子句不能区分源ip跟目的ip，后一个字句作为前一个字句的补充
        occurred_time = int(int(kwargs["args"].get("occurredTime", 0)) / 1000)
        start_time = int(int(kwargs["args"].get("startTime", 0)) / 1000)
        stop_time = int(int(kwargs["args"].get("stopTime", 0)) / 1000)
        paraTmp = kwargs["args"].get("src_ip")
        src_ip_one = "host " + paraTmp if paraTmp else ""
        paraTmp = kwargs["args"].get("dst_ip")
        dest_ip_one = "host " + paraTmp if paraTmp else ""
        paraTmp = kwargs["args"].get("src_port")
        src_port_one = "port " + paraTmp if paraTmp else ""
        paraTmp = kwargs["args"].get("dst_port")
        dest_port_one = "port " + paraTmp if paraTmp else ""
        proto = kwargs["args"].get("proto")
        proto = ip_proto_dict.get(proto)
        replay = kwargs["args"].get("replay")
        dload_type = kwargs["args"].get("type")

        # 转换为UTC时间
        buffer_time = 60
        if dload_type in ["model", "ioc"]:
            buffer_time = 300

        if occurred_time:
            after_time = "after " + str(datetime.utcfromtimestamp(occurred_time - buffer_time * 2)).replace(" ", "T") + "Z"
            before_time = "before " + str(datetime.utcfromtimestamp(occurred_time + buffer_time)).replace(" ", "T") + "Z"
        else:
            after_time = "after " + str(datetime.utcfromtimestamp(start_time)).replace(" ", "T") + "Z"
            before_time = "before " + str(datetime.utcfromtimestamp(stop_time)).replace(" ", "T") + "Z"

        path_item_list = []
        if not occurred_time:
            # 取证页面数据包下载
            query_list_one = [after_time, before_time, src_ip_one]
            if dest_ip_one:
                query_list_one.append(dest_ip_one)
            if src_port_one:
                query_list_one.append(src_port_one)
            if dest_port_one:
                query_list_one.append(dest_port_one)
            if proto:
                query_list_one.append(proto)
        elif dload_type == "ioc":
            query_list_one = [after_time, before_time, src_ip_one, dest_ip_one]
            path_item_list.append('ioc')
        elif dload_type == "model":
            query_list_one = [after_time, before_time, src_ip_one, dest_ip_one]
            path_item_list.append('model')
        else:
            query_list_one = [after_time, before_time, src_ip_one, dest_ip_one, src_port_one, dest_port_one]
            path_item_list.append('vul')
            if proto:
                query_list_one.append(proto)

        steno_query_one = " && ".join(query_list_one)

        def convert_local_time(ts):
            return time.strftime('%Y_%m_%d_%H_%M_%S', time.localtime(ts))

        if occurred_time:
            path_item_list.append(convert_local_time(occurred_time))
        else:
            path_item_list.append(convert_local_time(start_time))
        if kwargs["args"].get("src_ip"):
            path_item_list.append(kwargs["args"].get("src_ip"))
        if kwargs["args"].get("dst_ip"):
            path_item_list.append(kwargs["args"].get("dst_ip"))
        file_path = PACKAGE_DIR + '_'.join(path_item_list) + '.pcap'

        url_list = []
        url = StenographerConfig.url
        if "true" == replay:
            url = url.replace("1234", "4321")
        url_list.append(url)

        return steno_query_one, file_path, url_list
