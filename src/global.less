// 全局样式

@import '~antd/lib/style/themes/default.less';

html,
body,
#root-slave,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

.ant-menu a {
  color: rgba(0, 0, 0, 0.65);
}

.ant-pro-sider-menu-logo img {
  border-radius: 4px;
}

.ant-layout-sider-dark {
  .ant-menu a {
    color: rgba(255, 255, 255, 0.65);
  }
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

.ant-pro-sider-menu-sider {
  height: 100%;
  overflow: auto;
}

.ant-layout-content {
  background-color: transparent !important;
}

.searchForm .searchBtn {
  margin-left: 30px;
}

.midBtn {
  margin: 0 10px;
}

.btnBox {
  margin-top: 20px;
}

.ant-row {
  width: 100%;
  padding: 10px 0;
}
.seachItem {
  .ant-row {
    padding: 0 !important;
  }
}
.search_box {
  .ant-row {
    padding: 0 !important;
  }
}

.group_btns {
  display: flex;
  justify-content: flex-end;
}

.ant-form .ant-form-item-label {
  min-width: 100px;
}

.margin_right_10 {
  margin-right: 10px;
}

.margin_left_20 {
  margin-left: 20px;
}

.margin_left_30 {
  margin-left: 30px;
}

.ant-table-wrapper {
  ul.ant-pagination {
    margin: 0;
    background: #fff;
    padding: 0.5rem 1rem;

    &:first-child {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

.form-inline {
  :global {
    div.ant-form-item-label {
      min-width: auto;
    }

    .ant-form-item > .ant-form-item-control {
      flex: 1;
      overflow: hidden;
    }
  }
}
// 滚动条样式修改
::-webkit-scrollbar-track {
  background-color: #fbfbfb;
  border: 1px solid #eee;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c1c1c1;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #7d7d7d;
}
