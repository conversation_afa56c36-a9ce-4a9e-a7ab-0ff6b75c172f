.box {
  display: flex;
}
.center {
  width: 800px;
  height: 400px;
  border: 1px solid red;
}
.btn {
  margin-top: 4px;
}
.chartBox {
  padding-top: 20px;
}
.title {
  font-weight: bold;
  font-size: 18px;
}
.event {
  width: 300px;
  height: 100px;
}
.eventBox {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
}
.eventNumber {
  margin-bottom: 10px;
  font-size: 30px;
}
.high {
  color: #fc0100;
}
.mid {
  color: #f76e00;
}
.low {
  color: #ecd500;
}
.threalFlag {
  padding-top: 60px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 300px;
  margin: 0 auto;
}
.threatFlag {
  width: 20px;
  height: 20px;
}
.top {
  display: flex;
  justify-content: space-between;
}
.threatenNumber {
  color: #f76e00;
  font-weight: bold;
  text-align: center;
}
.threatRank {
  width: 300px;
}
.Listcontent {
  display: flex;
  height: 37px;
}
.progressBarWrap {
  flex: 1;
  margin-right: 10px;
}
.progressBar {
  position: relative;
  width: 100%;
  height: 2px;
  margin: 4px 0;
  background: rgba(63, 79, 93, 0.2);
  .source {
    position: absolute;
    width: 0;
    height: 4px;
    background: #003793;
  }
}
.score {
  width: 65px;
  margin-top: -5px;
}
.dangerousFlow {
  width: 300px;
}
.threatenChartBox {
  width: 100%;
  height: 400px;
  padding-top: 20px;
}
.appFlowBox {
  width: 100%;
  height: 400px;
}
.ant-select {
}

.killChains {
  display: flex;
  width: 100%;
  padding-top: 20px;
}
.images {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin: 20px -40px 20px 0;
  transform: translateY(8px);
}
.icon {
  width: 27px;
  height: 27px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  transform: translateY(-10px);
}
.icon1 {
  background-image: url('../../assets/images/killchains1.svg');
}
.icon2 {
  background-image: url('../../assets/images/killchains2.svg');
}
.icon3 {
  background-image: url('../../assets/images/killchains3.svg');
}
.icon4 {
  background-image: url('../../assets/images/killchains4.svg');
}
.icon5 {
  background-image: url('../../assets/images/killchains5.svg');
}
.icon6 {
  background-image: url('../../assets/images/killchains6.svg');
}
.icon7 {
  background-image: url('../../assets/images/killchains7.svg');
}
.HotEvent {
  // height: 469px;
  // overflow: auto;
  .item {
    display: flex;
    align-items: center;
    height: 82px;
    margin-bottom: 10px;
    max-width: 600px;
    padding: 0 12px;
    background-color: #e9edf0;
    border-left: 3px solid #ecd500;
  }
  .hot_center {
    display: table;
    padding: 0 15px 0 0;
    width: 380px;
  }
  .behaviors {
    max-width: 310px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .ip {
    color: #9a9b9d;
  }
  .number {
    width: 60px;
    color: #f76e00;
    font-weight: bold;
  }
  .success {
    width: 40px;
  }
}

.threatenBox {
  padding:40px 24px 24px 24px;
  // padding-top: 40px;
}
.count {
  color:#fc0100;
}
.TotalKillChains {
  padding-top: 100px;
	margin-bottom: 15px;
	height: 292px;
	.killChainStage {
		display: flex;
		height: 27px;
		align-items: center;
		margin-bottom: 10px;
	}
	.icon{
		width: 27px;
		height: 27px;
		margin-right: 5px;
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center;
	}
	// .icon1 {
	// 	background-image: url('Images/killchains1.svg');
	// }
	// .icon2 {
	// 	background-image: url('Images/killchains2.svg');
	// }
	// .icon3 {
	// 	background-image: url('Images/killchains3.svg');
	// }
	// .icon4 {
	// 	background-image: url('Images/killchains4.svg');
	// }
	// .icon5 {
	// 	background-image: url('Images/killchains5.svg');
	// }
	// .icon6 {
	// 	background-image: url('Images/killchains6.svg');
	// }
	// .icon7 {
	// 	background-image: url('Images/killchains7.svg');
	// }
	.progressBarWrap {
		flex: 1;
		margin: 0 15px;
	}
	.progressBar{
		width: 100%;
		height: 2px;
		position: relative;
		margin:4px 0;
		background: rgba(63, 79, 93, 0.2);
		.source {
		  width: 0;
		  height: 4px;
		  position: absolute;
		  background: rgb(243, 75, 39);
		}

	}
	.number {
		width: 50px;
		color: #F76E00;
		font-weight: bold;
		margin-top: -20px;
		text-align: right;
	}
}