/*
 * @Author: tianh
 * @Date: 2021-11-17 11:13:03
 * @LastEditors: tianh
 * @LastEditTime: 2021-12-17 16:55:39
 * @Descripttion:模型管理
 */

import React, { useState, useEffect } from 'react';
import { Form, Collapse, Row, Col, Select, Input, Table, Button, Switch, Modal, message } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { modelStatus } from '@/utils/enumList';
import KillChainStages from '@/components/KillChainStages/index';
import Safe from '@/pages/configs/safe';
import { getModelList, changeModel } from '@/services/knowledgeCenter';

const { Panel } = Collapse;
const { Option } = Select;

const Index = (props: any) => {
  const [form] = Form.useForm();
  const [dataSource, setdataSource] = useState([]);
  const [total, settotal] = useState(0);
  const [formValue, setformValue] = useState({
    page: 1,
    pageSize: 10,
  });
  const [visible, setvisible] = useState(false);
  useEffect(() => {
    getList();
  }, [formValue]);
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      const formvalue = {
        ...formValue,
        ...value,
        page: 1,
        pageSize: 10,
      };
      setformValue(formvalue);
    });
  };
  const getList = () => {
    getModelList(formValue).then((res) => {
      if (res.flag) {
        settotal(res.data.count);
        setdataSource(res.data.detail);
      }
    });
  };
  // 分页change事件
  const pagination = {
    total: total,
    current: formValue.page || 1,
    pageSize: formValue.pageSize,
    showSizeChanger: true,
    onChange(page: number, pageSize: any) {
      setformValue({ ...formValue, page, pageSize });
    },
  };
  const handleChange = (value: any, record: any) => {
    let _params = {
      modelNum: record.model_num,
      modelStatus: '',
    };
    value ? (_params.modelStatus = 'enable') : (_params.modelStatus = 'disable');
    changeModel(_params).then((res) => {
      if (res.flag) {
        getList();
        message.success('操作成功');
      } else {
        message.error(res.message);
      }
    });
  };
  const columns = [
    { title: '编号', dataIndex: 'model_num' },
    {
      title: '模型名称',
      dataIndex: 'model_name',
    },
    { title: '描述', dataIndex: 'desc', width: 400 },
    {
      title: '威胁得分',
      dataIndex: 'threat_score',
    },
    {
      title: '杀伤链阶段',
      dataIndex: 'killchain',
      className: '!text-center',
      render: (t: any) => {
        return <KillChainStages data={t} />;
      },
    },
    {
      title: '状态',
      dataIndex: 'model_status',
      render: (t: any, record: any) => {
        return (
          <Switch
            onClick={(e) => {
              handleChange(e, record);
            }}
            checked={t === 'enable'}
          />
        );
      },
    },
    {
      title: '模型评价',
      dataIndex: 'model_evaluation',
      width: 400,
      render: (t: any, record: any) => {
        t.false_persent = Math.floor(t.false_persent * 100) / 100;
        if (record.model_status === 'enable') {
          if (t.false_persent > 0.5) {
            return (
              <span>
                该模型最近一周命中<span style={{ color: '#00cc00' }}>{t.total_count}</span>次，误报为
                <span style={{ color: '#ff8000' }}>{t.false_count}</span>次;
                {t.total_count === 0 && (
                  <span style={{ color: '#fc0100' }}>持续没有输出，建议优化。误报较高，建议优化。</span>
                )}
              </span>
            );
          } else {
            return (
              <span>
                该模型最近一周命中<span style={{ color: '#00cc00' }}>{t.total_count}</span>次，误报为
                <span style={{ color: '#ff8000' }}>{t.false_count}</span>次。
                {t.total_count === 0 && <span>持续没有输出，建议优化。</span>}
              </span>
            );
          }
        }
        // return (
        //  <span>{t}</span>
        // );
      },
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   render: (t: any, record: any) => {
    //     if (record.model_name === '自签名证书异常') {
    //       return (
    //         <Button
    //           onClick={() => {
    //             setvisible(true);
    //           }}
    //           type="link"
    //         >
    //           模型参数调整
    //         </Button>
    //       );
    //     } else {
    //       return (
    //         <Button disabled type="link">
    //           模型参数调整
    //         </Button>
    //       );
    //     }
    //   },
    // },
  ];
  const formItemLayout = {
    labelCol: {
      span: 9, // * ≥576px
    },
    wrapperCol: {
      span: 15,
    },
  };
  return (
    <div>
      <Form className="bg-d0 !p-4 b b-solid b-brd6 form-inline" form={form} onFinish={onSubmit} layout="inline">
        <Form.Item className="!flex-[1] !mr-8" label="模型编号" name="modelNum">
          <Input allowClear placeholder="请输入模型编号" />
        </Form.Item>
        <Form.Item className="!flex-[1] !mr-8" label="模型名称" name="modelName">
          <Input allowClear placeholder="请输入模型名称" />
        </Form.Item>
        <Form.Item className="!flex-[1] !mr-8" label="模型状态" name="modelStatus">
          <Select allowClear placeholder="请选择模型状态">
            {modelStatus.map((item: any) => {
              return (
                <Option value={item.value} key={item.key}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        <div className="flex-[1] text-right">
          <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
            搜索
          </Button>
        </div>
      </Form>
      <Table className="mt-4" columns={columns} dataSource={dataSource} pagination={pagination} />
      <Modal
        width={1200}
        visible={visible}
        onOk={() => {
          setvisible(false);
        }}
        onCancel={() => {
          setvisible(false);
        }}
      >
        <Safe />
      </Modal>
    </div>
  );
};
export default Index;
