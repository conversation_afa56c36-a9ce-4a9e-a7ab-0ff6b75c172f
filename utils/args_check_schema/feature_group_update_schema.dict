{
	"type": "object",
	"required": ["action", "group_id", "sidList"],
	"properties": {
	    "group_id": {
            "type": "string"
        },
		"action": {
		    "type": "string",
			"enum": ["add", "del"],
			"default": "add"
		},
		"sidList": {
			"type": "array",
			"items": {
			    "type": "string",
			    "pattern": "(^[1-9]\d{0,8}$)|(^[1-3]\d{9}$)|(^4[0-1]\d{8}$)|(^42[0-8]\d{7}$)|(^429[0-3]\d{6}$)|(^4294[0-8]\d{5}$)|(^42949[0-5]\d{4}$)|(^429496[0-6]\d{3}$)|(^4294967[0-1]\d{2}$)|(^42949672[0-8]\d$)|(^429496729[0-5]$)"
			  }
		}
	}
}