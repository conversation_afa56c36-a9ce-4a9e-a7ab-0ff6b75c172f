import { defineConfig } from 'umi';
import unoCSS from '@unocss/webpack';

export default defineConfig({
  devtool: 'source-map',
  chunks: ['vendors', 'umi'],
  chainWebpack: function(config, { webpack }) {
    config.merge({
      optimization: {
        minimize: true,
        splitChunks: {
          chunks: 'all',
          minSize: 30000,
          minChunks: 2,
          automaticNameDelimiter: '.',
          cacheGroups: {
            vendor: {
              name: 'vendors',
              test({ resource }) {
                return /[\\/]node_modules[\\/]/.test(resource);
              },
              priority: 10,
            },
          },
        },
      },
    });
    config.plugin('UnoCSS').use(unoCSS, [{}]);
  },
});
