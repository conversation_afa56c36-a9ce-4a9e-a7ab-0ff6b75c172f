/*
 * @Author: 田浩
 * @Date: 2021-07-19 15:32:01
 * @LastEditors: 田浩
 * @LastEditTime: 2021-08-06 11:37:30
 * @Descripttion:
 */
export default {
  'error.server.10001': '用户名或密码错误(您还有{count}次机会)',
  'error.server.10002': '用户名已存在',
  'error.server.10003': '账号或密码错误',
  'error.server.10004': '未登录',
  'error.server.10005': '用户权限不足',
  'error.server.10006': '组名已存在',
  'error.server.10007': '未找到用户组',
  'error.server.10008': '该模块 uri 已存在',
  'error.server.10009': '该模块不存在',
  'error.server.10018': '该账号已被锁定',
  'error.server.10020': '密码错误次数已达上限，请在{unlockDate}后尝试登录',
  'error.server.10021': '谷歌认证验证码错误',
  'error.server.10023': '不能删除自己所在的用户组',
  'error.server.20001': '请求参数错误',
  'error.server.20002': '服务器错误',
  'error.server.20003': '客户端权限不足',

  'error.server.status.200': '服务器成功返回请求的数据。',
  'error.server.status.201': '新建或修改数据成功。',
  'error.server.status.202': '一个请求已经进入后台排队（异步任务）。',
  'error.server.status.204': '删除数据成功。',
  'error.server.status.400':
    '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  'error.server.status.401': '用户没有权限（令牌、用户名、密码错误）。',
  'error.server.status.403': '用户得到授权，但是访问是被禁止的。',
  'error.server.status.404':
    '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  'error.server.status.406': '请求的格式不可得。',
  'error.server.status.410': '请求的资源被永久删除，且不会再得到的。',
  'error.server.status.422': '当创建一个对象时，发生一个验证错误。',
  'error.server.status.500': '服务器发生错误，请检查服务器。',
  'error.server.status.502': '网关错误。',
  'error.server.status.503': '服务不可用，服务器暂时过载或维护。',
  'error.server.status.504': '网关超时。',
};
