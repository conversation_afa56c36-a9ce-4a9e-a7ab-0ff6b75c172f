/*
 颜色色调简写约定
    lt -> light(浅色)
    md -> medium(中等)
    dk -> dark（深色）
*/

@primarily: #5867dd;
@white: #ffffff;
@black: #000000;
// 背景
@bg-primarily: #333333;
@bg-color-gray: #eeeeee;
// 字体
@text-color-red: #f04134;
@text-color-gray: #888888;
@text-color-black-lt: #a5a5a5;
@text-color-black-md: #666666;
@text-color-blue: #5867dd;
@text-color-gray-lg: #333;
// 线
@border-color-gray: #e8e8e8;
@border-color-black: #8c8c8c;
@border-color-blue: #5867dd;
@border-color-light-gray: #eee;
@border-color-deep-gray: #555;

// 阴影
@primary-box-shadow: 0 0 13px 0 rgba(82, 63, 105, 0.1);


.scrollY {
  overflow-y: scroll;
  overflow-x: hidden;

  /* 设置滚动条的样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /* 滚动槽 */
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
  }

  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.1);
    // -webkit-box-shadow: rgba(0, 0, 0, 0.5);
  }

  &::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(0, 0, 0, 0.1);
  }
}