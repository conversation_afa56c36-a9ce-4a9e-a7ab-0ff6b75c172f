#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import pandas as pd
from flask import request, send_file, make_response
from flask_restful import Resource

from api_1_0.utils.flask_log import ndr_log_to_box
from config.config import NdrLog, FILE_INDEX
from utils import param_check
from utils.database import get_es_client
from utils.es_function import *
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class FileResource(Resource):
    def __init__(self, **kwargs):
        self.es_client = get_es_client()
        self.start_time = 0
        self.end_time = 0
        self.es_index_list = []
        self.src_ip = ''
        self.dst_ip = ''
        self.file_name = ''
        self.file_type = ''
        self.protocol = ''
        self.celeryId = ''
        self.threat_level = ''
        self.report_date = ''
        self.ip = ''
        for element in kwargs:
            method = getattr(self, "set_" + element, "")
            if callable(method):
                method(kwargs.get(element))

    # ES查询模板 & ES客户端
    def set_es_template(self, es_template):
        self.es_template = ES_Template().read_template(es_template)

    def get_range(self):
        query = {
            "range": {
                "observedTime": {
                    "gte": self.start_time,
                    "lte": self.end_time
                }
            }
        }
        return query

    def format_es_time(self):
        es_index = get_es_index(FILE_INDEX, int(
            self.start_time), int(self.end_time))
        for i in es_index:
            # 判断是否存在索引
            if self.es_client.indices.exists(i):
                self.es_index_list.append(i)

    def format_es_query(self, es_template):
        query = []
        if self.start_time and self.end_time:
            query.append(self.get_range())
        if self.src_ip != '':
            query.append(
                {"term": {"flow.src_ip.keyword": self.src_ip}})
        if self.dst_ip != '':
            query.append(
                {"term": {"flow.dst_ip.keyword": self.dst_ip}})
        if self.file_name != '':
            query.append(
                {"wildcard": {"filename.keyword": "*" + self.file_name + "*"}})
        if self.file_type != '':
            query.append(
                {"term": {"filetype.keyword": self.file_type}})
        if self.protocol != '':
            query.append(
                {"term": {"proto.keyword": self.protocol.lower()}})
        if self.threat_level != '':
            query.append(
                {"term": {"threatLevel.keyword": self.threat_level}})
        if self.celeryId != '':
            query.append(
                {"term": {"celeryId.keyword": self.celeryId}})
        info = es_template["query"]["bool"]["must"]
        for val in info:
            query.append(val)
        es_template["query"]["bool"]["must"] = query
        return es_template


class FileAnalysisStatistic(FileResource):
    """
    文件分析
    """

    def __init__(self):
        super(FileAnalysisStatistic, self).__init__()

    @param_check.check_flask_args(Validator("file_analysis_info_schema"), request)
    def get(self, **kwargs):
        """
        获取文件分析统计
        :return:
        """
        self.start_time = kwargs["args"]["startTime"]
        self.end_time = kwargs["args"]["endTime"]
        self.src_ip = kwargs["args"]["srcIp"]
        self.dst_ip = kwargs["args"]["dstIp"]
        self.file_name = kwargs["args"]["fileName"]
        self.file_type = kwargs["args"]["fileType"]
        self.protocol = kwargs["args"]["protocol"]
        self.threat_level = kwargs["args"]["threatLevel"]
        self.celeryId = kwargs["args"]["celeryId"]

        try:
            return_data = {}
            self.format_es_time()
            if not self.es_index_list:
                return flask_response('索引不存在', False, return_data)

            # 安全等级统计
            es_template_statistic = ES_Template().read_template('get_file_analysis_statistic_threatLevel_total')
            es_template_statistic_level = self.format_es_query(es_template_statistic)
            alter_data = self.es_client.search(index=self.es_index_list, body=es_template_statistic_level)
            return_data = self.data_format_level(alter_data, return_data)

            # 文件检测报告趋势
            interval = self.handle_time()
            es_template_statistic = ES_Template().read_template('get_file_analysis_statistic_report_tendency')
            es_template_statistic_basic = self.format_es_query(es_template_statistic)
            # 设置参数变更
            es_template_statistic_basic["aggs"]["observedTime"]["date_histogram"]["interval"] = interval
            es_template_statistic_basic["aggs"]["observedTime"]["date_histogram"]["extended_bounds"]["min"] = self.start_time
            es_template_statistic_basic["aggs"]["observedTime"]["date_histogram"]["extended_bounds"]["max"] = self.end_time
            report_tendency_data = self.es_client.search(index=self.es_index_list, body=es_template_statistic_basic)
            return_data = self.data_format_safe(report_tendency_data, return_data)
            
            es_template_statistic = ES_Template().read_template('get_file_analysis_statistic_report_tendency')
            es_template_statistic_risk = self.format_es_threat_levels(es_template_statistic, ["Low", "Medium", "High"])
            es_template_statistic_risk = self.format_es_query(es_template_statistic_risk)
            es_template_statistic_risk["aggs"]["observedTime"]["date_histogram"]["interval"] = interval
            es_template_statistic_risk["aggs"]["observedTime"]["date_histogram"]["extended_bounds"]["min"] = self.start_time
            es_template_statistic_risk["aggs"]["observedTime"]["date_histogram"]["extended_bounds"]["max"] = self.end_time
            risk_data = self.es_client.search(index=self.es_index_list, body=es_template_statistic_risk)
            return_data = self.data_format_risk(risk_data, return_data)

            # 按照包类型进行统计
            es_template_type = ES_Template().read_template('get_file_analysis_statistic_basic_type')
            es_template_type = self.format_es_query(es_template_type)
            type_data = self.es_client.search(index=self.es_index_list, body=es_template_type)
            return_data = self.data_format_key(type_data, return_data, "packet_type")

            # 按照病毒样本数统计
            es_template_virus = ES_Template().read_template('get_file_analysis_statistic_basic_type')
            es_template_virus = self.format_es_basic_type(es_template_virus, "multiav.keyword")
            es_template_virus = self.format_es_query(es_template_virus)
            virus_data = self.es_client.search(index=self.es_index_list, body=es_template_virus)
            return_data = self.data_format_key(virus_data, return_data, "virus_engine")

            # 按照高危病毒引擎
            es_template_family = ES_Template().read_template('get_file_analysis_statistic_risk_family')
            es_template_family = self.format_es_query(es_template_family)
            family_data = self.es_client.search(index=self.es_index_list, body=es_template_family)
            return_data = self.data_format_key(family_data, return_data, "high_risk_family")

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error("reason: %s" % str(e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "文件分析统计数据获取失败")
            return flask_response(err_info, False, {})
        ndr_log_to_box(NdrLog.Type.OPERATE, "文件分析统计数据获取成功")
        return flask_response('获取成功', True, return_data)

    @staticmethod
    def format_es_basic_type(es_template, file_type):
        es_template["aggs"]["type"]["terms"]["field"] = file_type
        return es_template

    @staticmethod
    def format_es_threat_levels(es_template, threat_levels):
        es_template["query"]["bool"]["must"][0]["terms"]["threatLevel.keyword"] = threat_levels
        return es_template

    @staticmethod
    def data_format_safe(rst_data, return_data):
        rst_list = rst_data["aggregations"]["observedTime"]["buckets"]
        datas = []
        for rst in rst_list:
            data = {"time": rst["key"], "safe": rst["doc_count"]}
            datas.append(data)
        return_data["report_tendency"] = datas
        return return_data

    @staticmethod
    def data_format_risk(rst_data, return_data):
        rst_list = rst_data["aggregations"]["observedTime"]["buckets"]
        res_data = return_data["report_tendency"]
        for rst in rst_list:
            flag = False
            for key, val in enumerate(res_data):
                if rst["key"] == val["time"]:
                    res_data[key]["risk"] = rst["doc_count"]
                    flag = True
                    break
            if not flag:
                data = {"time": rst["key"], "risk": rst["doc_count"]}
                res_data.append(data)

        return_data["report_tendency"] = res_data
        return return_data

    @staticmethod
    def data_format_key(rst_data, return_data, key):
        datas = []
        rst_list = rst_data["aggregations"]["type"]["buckets"]
        for rst in rst_list:
            data = {"type": rst["key"], "total": rst["doc_count"]}
            datas.append(data)
        return_data[key] = datas
        return return_data

    @staticmethod
    def data_format_level(rst_data, return_data):
        agg_data = rst_data["aggregations"]["threatLevel"]["buckets"]
        return_data["report_total"] = rst_data["hits"]["total"]["value"]
        for key_data in agg_data:
            if key_data["key"] == "Safe":
                return_data["safe_total"] = key_data["doc_count"]
            elif key_data["key"] == "Low":
                return_data["low_risk"] = key_data["doc_count"]
            elif key_data["key"] == "Medium":
                return_data["middle_risk"] = key_data["doc_count"]
            elif key_data["key"] == "High":
                return_data["high_risk"] = key_data["doc_count"]
        return return_data

    def handle_time(self):
        interval = ''
        hour = (int(self.end_time) - int(self.start_time)) / 1000 / 60 / 60
        if hour < 1:
            interval = '5s'
            return interval
        if hour < 6:
            interval = '10m'
            return interval
        day = hour / 24
        if day <= 1:  # 一天范围内按照30min拆分
            interval = '30m'
            return interval
        week = day / 7
        if week <= 1: # 一周范围内按照1天拆分
            interval = '1440m'
        elif week <= 2: # 二周范围内按照2天拆分
            interval = '2880m'
        elif week <= 3: # 三周范围内按照3天拆分
            interval = '4320m'
        elif week <= 4: # 四周范围内按照4天拆分
            interval = '5760m'
        elif week <= 5: # 五周范围内按照6天拆分
            interval = '8640m'
        else: # 大于5周按照周拆分
            interval = 'week'
        return interval


class FileAnalysisIp(FileResource):
    """
    文件分析-ip
    """

    def __init__(self):
        super(FileAnalysisIp, self).__init__(es_template="get_file_analysis_statistic_basic_type")

    @param_check.check_flask_args(Validator("file_analysis_ip_info_schema"), request)
    def get(self, **kwargs):
        """
        统计src_ip数据
        :return:
        """
        self.start_time = kwargs["args"]["startTime"]
        self.end_time = kwargs["args"]["endTime"]
        sort = kwargs["args"]["sort"]
        self.ip = kwargs["args"]["ip"]
        self.src_ip = kwargs["args"]["srcIp"]
        self.dst_ip = kwargs["args"]["dstIp"]
        self.file_name = kwargs["args"]["fileName"]
        self.file_type = kwargs["args"]["fileType"]
        self.protocol = kwargs["args"]["protocol"]
        self.threat_level = kwargs["args"]["threatLevel"]
        self.celeryId = kwargs["args"]["celeryId"]

        try:
            return_data = []
            self.format_es_time()
            if not self.es_index_list:
                return return_data
            es_template_type = self.format_es_query(self.es_template)
            es_template_type = self.format_es_temp_sort(es_template_type, sort)
            es_template_type = self.format_es_basic_type(es_template_type, "flow."+ self.ip +".keyword")
            es_template_type = self.format_es_size(es_template_type, 5)
            alter_data = self.es_client.search(index=self.es_index_list, body=es_template_type)
            return_data = self.data_format(alter_data)

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            return flask_response(err_info, False, {})
        return flask_response('获取成功', True, return_data)

    @staticmethod
    def format_es_temp_sort(es_template, order):
        es_template["aggs"]["type"]["terms"]["order"]["_count"] = order
        return es_template

    @staticmethod
    def format_es_basic_type(es_template, file_type):
        es_template["aggs"]["type"]["terms"]["field"] = file_type
        return es_template

    @staticmethod
    def format_es_size(es_template, size):
        es_template["aggs"]["type"]["terms"]["size"] = size
        return es_template

    @staticmethod
    def data_format(rst_data):
        return_datas = []
        rst_list = rst_data["aggregations"]["type"]["buckets"]
        for rst in rst_list:
            return_data = {"ip": rst["key"], "total": rst["doc_count"]}
            return_datas.append(return_data)
        return return_datas


class FileAnalysisIpExport(FileResource):
    """
    文件分析-src_ip导出
    """

    def __init__(self):
        super(FileAnalysisIpExport, self).__init__(es_template="get_file_analysis_statistic_basic_type")
        self.subTitleFormat = None
        self.excel_file_path = ''
        self.type = 1
        self.sort = "desc"
        self.export_data = []

    @param_check.check_flask_args(Validator("file_analysis_ip_export_info_schema"), request)
    def get(self, **kwargs):
        """
        统计ip数据
        :return:
        """
        self.ip = kwargs["args"]["ip"]
        self.start_time = kwargs["args"]["startTime"]
        self.end_time = kwargs["args"]["endTime"]
        self.sort = kwargs["args"]["sort"]
        self.type = kwargs["args"]["type"]
        self.src_ip = kwargs["args"]["srcIp"]
        self.dst_ip = kwargs["args"]["dstIp"]
        self.file_name = kwargs["args"]["fileName"]
        self.file_type = kwargs["args"]["fileType"]
        self.protocol = kwargs["args"]["protocol"]
        self.threat_level = kwargs["args"]["threatLevel"]
        self.celeryId = kwargs["args"]["celeryId"]

        start_time = time.strftime("%Y-%m-%d", time.localtime(int(self.start_time) / 1000))
        stop_time = time.strftime("%Y-%m-%d", time.localtime(int(self.end_time) / 1000))
        # 记录报告日期区间
        self.report_date = '%s -- %s' % (start_time, stop_time)
        try:
            return_data = []
            self.export_file_path_init()
            self.format_es_time()
            if not self.es_index_list:
                self.export_data = return_data
            else:
                es_template_type = self.format_es_query(self.es_template)
                es_template_type = self.format_es_temp_sort(es_template_type, self.sort)
                es_template_type = self.format_es_basic_type(es_template_type, "flow."+ self.ip + ".keyword")
                es_template_type = self.format_es_size(es_template_type, 5)
                alter_data = self.es_client.search(index=self.es_index_list, body=es_template_type)
                return_data = self.data_format(alter_data)
                self.export_data = return_data
            self.export_excel()

        except Exception as e:
            LOG.error('reason: %s' % str(e))
            return flask_response("Export excel file failed!", False, {'error msg': str(e)})
        return make_response(send_file(self.excel_file_path, as_attachment=True))

    def export_file_path_init(self):
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        self.excel_file_path = "/tmp/ip_report_%s.xlsx" % time_str

    def export_excel(self):
        df = pd.DataFrame(self.export_data)
        if int(self.type) == 1:
            if self.ip == 'src_ip':
                df.rename(columns={"ip": "src_ip:" + self.sort}, inplace=True)
            else:
                df.rename(columns={"ip": "dst_ip:" + self.sort}, inplace=True)
        else:
            name = "降序"
            if self.sort == "asc":
                name = "升序"
            if self.ip == 'src_ip':
                df.rename(columns={"ip": "源IP:" + name, "total": "总计"}, inplace=True)
            else:
                df.rename(columns={"ip": "目的IP:" + name, "total": "总计"}, inplace=True)
        df.to_excel(self.excel_file_path, index=False)

    @staticmethod
    def format_es_temp_sort(es_template, order):
        es_template["aggs"]["type"]["terms"]["order"]["_count"] = order
        return es_template

    @staticmethod
    def format_es_basic_type(es_template, file_type):
        es_template["aggs"]["type"]["terms"]["field"] = file_type
        return es_template

    @staticmethod
    def format_es_size(es_template, size):
        es_template["aggs"]["type"]["terms"]["size"] = size
        return es_template

    @staticmethod
    def data_format(rst_data):
        return_datas = []
        rst_list = rst_data["aggregations"]["type"]["buckets"]
        for rst in rst_list:
            return_data = {"ip": rst["key"], "total": rst["doc_count"]}
            return_datas.append(return_data)
        return return_datas
