import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

const apiVersion = 'v3';


// 18.原始pcap包展示
export const pcapList = (data: object) =>
  request(`workbench/detail/pcap_list${parseQueryString(data)}`, {
    method: 'get',
    apiVersion,
  });
  // 原始pcap信息获取-单条数据详情
export const pcapData = (data: object) =>
request(`workbench/detail/pcap_data${parseQueryString(data)}`,{
  method: 'get',
  apiVersion,
});
// 原始pcap信息获取-内容
export const pcapContent = (data: object) =>
  request(`workbench/detail/pcap_content${parseQueryString(data)}`,{
    method: 'get',
    apiVersion,
  });
  // 原始pcap文件包load
export const loadPcap = (data: object) => {
  return request(`workbench/detail/load_pcap${parseQueryString(data)}`, { method: 'get', apiVersion });
};
// 原始pcap信息获取-退出
export const pcapBye = (data: object) =>
  request(`workbench/detail/pcap_bye${parseQueryString(data)}`,{
    method: 'get',
    apiVersion,
  });
// 流量分析取证搜索
export const getPackage = (data: any) => {
  return request(`package`, {
    method: 'post',
    data
  });
};