# -*- coding: utf-8 -*-
# @Time    : 2024-08-26 10:17
# <AUTHOR> luoyf
# @Software: PyCharm
import copy
import shutil
import re
import time
import zipfile

import pandas as pd
from flask import request, make_response, send_file
from flask_restful import Resource
from utils.utils import flask_response
from utils.database import MongoD<PERSON>
from docx import Document
from docx.enum.table import WD_ALIGN_VERTICAL
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from .hive import *
from celery_tasks import cert_analyis_task
from datetime import datetime
from clickhouse_driver import Client as CH_client
from config.config import ClickHouseConfig


TASK_BACKTRACE = 1
TASK_TRACE = 2


class CertResource(Resource):
    def __init__(self):
        self.args = request.json if request.json else request.values.to_dict()
        self.retention_col = "cert_hive_retention"
        self.source_col = TABLE_NAME
        self.mg = MongoDB('ndr')
        self.root_path = '/opt/ndr/api_1_0/thirdparty/cert'
        # self.root_path = '/tmp'
        self.report_template = os.path.join(self.root_path, 'export_template.docx')
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database='hive')

    def get_clickhouse_data(self, sql):
        print(sql, flush=True)
        raw = self.ch_client.execute(sql, with_column_types=True)
        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]
        ck_raw_list = []
        for i in range(len(raw_val)):
            data = {}
            for idx, item in enumerate(raw_val[i]):
                if isinstance(item, bytes):
                    item = str(item)
                data[raw_key[idx][0]] = item
            ck_raw_list.append(data)

        return ck_raw_list

    @staticmethod
    def get_sql_with_update_id(data_type, updateid, src_ip, dst_ip):
        cond_sql = f"from apt_{data_type} where c_src_ipv4='{src_ip}' and c_dest_ipv4='{dst_ip}' " \
                   f"and update_id='{updateid}' order by c_time desc"

        if 'conn' == data_type:
            select_sql = "select toString(c_stream_time) as c_time, " \
                         "IPv4NumToString(c_src_ipv4) as c_src_ipv4, c_src_port, " \
                         "IPv4NumToString(c_dest_ipv4) as c_dest_ipv4, c_dest_port, " \
                         "IFNULL('', 0) as url, IFNULL('', 0) as body, IFNULL('', 0) as domain, " \
                         "IFNULL('', 0) as cert_ver, IFNULL('', 0) as cert_sni, IFNULL('', 0) as cert, " \
                         "c_up_bytes, c_down_bytes "
        elif 'url' == data_type:
            select_sql = "select toString(c_time) as c_time, " \
                         "IPv4NumToString(c_src_ipv4) as c_src_ipv4, c_src_port, " \
                         "IPv4NumToString(c_dest_ipv4) as c_dest_ipv4, c_dest_port, " \
                         "c_uri as url, c_req_body as body, IFNULL('', 0) as domain, " \
                         "IFNULL('', 0) as cert_ver, IFNULL('', 0) as cert_sni, IFNULL('', 0) as cert, " \
                         "IFNULL(0, 0) as c_up_bytes, IFNULL(0, 0) as c_down_bytes "
        elif 'tls' == data_type:
            select_sql = "select toString(c_time) as c_time, " \
                         "IPv4NumToString(c_src_ipv4) as c_src_ipv4, c_src_port, " \
                         "IPv4NumToString(c_dest_ipv4) as c_dest_ipv4, c_dest_port, " \
                         "IFNULL('', 0) as url, IFNULL('', 0) as body, IFNULL('', 0) as domain, " \
                         "c_version as cert_ver, c_sni as cert_sni, c_certificate as cert, " \
                         "IFNULL(0, 0) as c_up_bytes, IFNULL(0, 0) as c_down_bytes "
        elif 'dns' == data_type:
            select_sql = "select toString(c_time) as c_time, " \
                         "IPv4NumToString(c_src_ipv4) as c_src_ipv4, c_src_port, " \
                         "IPv4NumToString(c_dest_ipv4) as c_dest_ipv4, c_dest_port, " \
                         "IFNULL('', 0) as url, IFNULL('', 0) as body, c_domain as domain, " \
                         "IFNULL('', 0) as cert_ver, IFNULL('', 0) as cert_sni, IFNULL('', 0) as cert, " \
                         "IFNULL(0, 0) as c_up_bytes, IFNULL(0, 0) as c_down_bytes "
        elif 'ljc' == data_type:
            select_sql = "select toString(c_start_time) as c_time, " \
                         "IPv4NumToString(c_sip) as c_src_ipv4, c_sport as c_src_port, " \
                         "IPv4NumToString(c_dip) as c_dest_ipv4, c_dport as c_dest_port, " \
                         "IFNULL('', 0) as url, IFNULL('', 0) as body, IFNULL('', 0) as domain, " \
                         "IFNULL('', 0) as cert_ver, IFNULL('', 0) as cert_sni, IFNULL('', 0) as cert, " \
                         "IFNULL(0, 0) as c_up_bytes, IFNULL(0, 0) as c_down_bytes "
        else:
            raise Exception('Don not support data type %s in function get_sql_with_data_types' % data_type)

        return select_sql + cond_sql


class Retention(CertResource):
    def get_source_data(self, source_name):
        source_data = self.mg.find_one(self.source_col, {'source_name': source_name})
        if not source_data:
            return None, flask_response(f"source '{source_name}' not exists!", True, {})

        return source_data, None

    def check_time_param(self):
        try:
            if self.args['start_time'] > self.args['end_time']:
                return 'end time must be large than start time!'

            if TASK_BACKTRACE == self.args['task_type']:
                if not self.args['start_time'] or not self.args['end_time']:
                    return 'must include start time and end time when task type is backtrace!'
                if self.args['end_time'] > datetime.now().strftime('%Y%m%d%H'):
                    return 'end time must be little than now when task type is backtrace!'
            else:
                # start_time = '' => start_time = now time
                # end_time = '' => end_time = never end
                if self.args['start_time'] and self.args['start_time'] < datetime.now().strftime('%Y%m%d%H'):
                    return 'start time must be large than now when task type is trace!'
                if self.args['end_time'] and self.args['end_time'] < datetime.now().strftime('%Y%m%d%H'):
                    return 'end time must be large than now when task type is trace!'

            return ''
        except Exception as e:
            return str(e)

    def post(self):
        try:
            if 'name' not in self.args.keys() or 'source_name' not in self.args.keys():
                return flask_response('parma name or source_name not include.', True, {})

            task_data = self.mg.find_one(self.retention_col, {'name': self.args['name']})
            if task_data:
                return flask_response(f"task {self.args['name']} already exists!", True, {})

            source_data, ret = self.get_source_data(self.args['source_name'])
            if not source_data:
                return ret

            if self.args.get('data_types') not in DATA_TYPES:
                return flask_response('data_types not in %s' % str(DATA_TYPES), False, {})

            target_ioc = [f"{r['ip']}[{r['ports']}]" if r['ports'] else r['ip'] for r in source_data['rules']]

            data = {
                'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'name': self.args['name'],
                'source_name': self.args['source_name'],
                'data_types': self.args.get('data_types'),
                'target_ioc': target_ioc,
                'task_type': self.args.get('task_type', TASK_BACKTRACE),
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': '',
                'status': HIVE_STATUS_RUNNING,
                'celery_id': '',
                'update_id': source_data['update_id'],
                'statistics': source_data['statistics'],
                'result': []
            }

            self.mg.insert_one(self.retention_col, data)

            # start task
            cert_analyis_task.start_analyis_task.delay(
                self.args['name'], source_data['update_id'], self.args.get('data_types'))

            return flask_response('create task success.', True, {})
        except Exception as e:
            return flask_response('create task failed!', True, {'err': str(e)})

    def put(self):
        """
        include action: edit, start, stop, delete
        """
        try:
            if 'name' not in self.args.keys() or 'action' not in self.args.keys():
                return flask_response('parma name or action not include!', True, {})

            task_data = self.mg.find_one(self.retention_col, {'name': self.args['name']})
            if not task_data:
                return flask_response(f"task {self.args['name']} not exists!", True, {})

            if 'edit' == self.args['action']:
                source_data, ret = self.get_source_data(task_data['source_name'])
                if not source_data:
                    return ret

                if task_data['status'] == HIVE_STATUS_RUNNING:
                    return flask_response(f"task {task_data['name']} is running! Please stop it first.", True, {})

                target_ioc = [f"{r['ip']}[{r['ports']}]" if r['ports'] else r['ip'] for r in source_data['rules']]

                if self.args.get('data_types') not in DATA_TYPES:
                    return flask_response('data_types not in %s' % str(DATA_TYPES), False, {})

                # task name and task type is not allowed to be modified.
                self.mg.update(self.retention_col, {'name': self.args['name']}, {
                    'source_name': self.args['source_name'],
                    'data_types': self.args.get('data_types'),
                    'task_type': self.args.get('task_type', TASK_BACKTRACE),
                    'target_ioc': target_ioc,
                    'update_id': source_data['update_id'],
                    'statistics': source_data['statistics'],
                    'celery_id': '',
                    'result': []
                })
            elif 'start' == self.args['action']:
                if task_data['status'] == HIVE_STATUS_RUNNING:
                    return flask_response(f"task {task_data['name']} is already running!", True, {})

                source_data, ret = self.get_source_data(task_data['source_name'])
                if not source_data:
                    return ret

                self.mg.update(self.retention_col, {'name': self.args['name']}, {
                    'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'end_time': '',
                    'status': HIVE_STATUS_RUNNING,
                    'celery_id': '',
                    'update_id': source_data['update_id'],
                    'statistics': source_data['statistics'],
                    'result': []
                })
                # start task
                cert_analyis_task.start_analyis_task.delay(
                    self.args['name'], source_data['update_id'], task_data['data_types'])
            elif 'stop' == self.args['action']:
                if task_data['status'] != HIVE_STATUS_RUNNING:
                    return flask_response(f"task {task_data['name']} is already stop!", True, {})

                source_data, ret = self.get_source_data(task_data['source_name'])
                if not source_data:
                    return ret

                self.mg.update(self.retention_col, {'name': self.args['name']}, {
                    'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'status': HIVE_STATUS_SUCCESS
                })
                # stop task
                cert_analyis_task.stop_analyis_task.delay(task_data['name'])
            elif 'delete' == self.args['action']:
                if task_data['status'] == HIVE_STATUS_RUNNING:
                    return flask_response(f"task {self.args['name']} is running, Please stop it first!", True, {})

                self.mg.delete(self.retention_col, {'name': self.args['name']})
            else:
                return flask_response(f"can't support {self.args['action']} action!", True, {})

            return flask_response(f"{self.args['action']} {self.args['name']} success.", True, {})
        except Exception as e:
            return flask_response(f"{self.args['action']} {self.args['name']} failed!", True, {'err': str(e)})

    def get(self):
        try:
            page = int(self.args.get('page', 1))
            page_size = int(self.args.get('pageSize', 10))
            count = 0
            data = {
                'count': count,
                'page': page,
                'pageSize': page_size,
                'detail': []
            }
            if 'name' in self.args.keys():
                task_data = self.mg.find(self.retention_col, {'name': self.args['name']}).sort([('create_time', -1)]).limit(
                    page_size).skip((page - 1) * page_size)
            else:
                task_data = self.mg.find(self.retention_col, {}, {'result': 0}).sort([('create_time', -1)]).limit(
                    page_size).skip((page - 1) * page_size)

            if not task_data:
                return flask_response(f"get task {self.args['name']} failed!", True, data)

            for task in task_data:
                source_data = self.mg.find_one(self.source_col, {'source_name': task['source_name']})

                data['detail'].append({
                    'name': task['name'],
                    'start_time': task['start_time'],
                    'end_time': task['end_time'],
                    'source_name': task['source_name'],
                    'task_type': task['task_type'],
                    'data_types': task['data_types'],
                    'target_ioc': task['target_ioc'],
                    'status': task['status'],
                    'notice': '数据源有更新' if source_data and source_data['statistics'] != task.get('statistics', {}) else ''
                })
                count += 1

            data['count'] = count

            return flask_response('get task success.', True, data)
        except Exception as e:
            return flask_response('get task list failed!', True, {'err': str(e)})


class RetentionResult(CertResource):
    def get(self):
        try:
            if 'name' not in self.args.keys():
                return flask_response('task name not include!', True, {})

            page = int(self.args.get('page', 1))
            page_size = int(self.args.get('pageSize', 10))
            count = 0
            data = {
                'count': count,
                'page': page,
                'pageSize': page_size,
                'detail': []
            }

            task_data = self.mg.find_one(self.retention_col, {'name': self.args['name']})
            if not task_data:
                return flask_response(f"get task {self.args['name']} failed!", True, data)

            results = sorted(task_data['result'], key=lambda x: x['connections'], reverse=True)
            data['count'] = len(results)
            data['detail'] = results[(page - 1) * page_size:page * page_size]

            return flask_response('get task detail success.', True, data)
        except Exception as e:
            return flask_response('get task detail failed!', True, {'err': str(e)})

    def post(self):
        try:
            if 'name' not in self.args.keys():
                return flask_response('task name not include!', True, {})

            task_data = self.mg.find_one(self.retention_col, {'name': self.args['name']})
            if not task_data:
                return flask_response(f"get task {self.args['name']} failed!", True, [])

            if not task_data['result']:
                return flask_response(f"task {self.args['name']} have no result.", True, [])

            save_data = []
            for result in task_data['result']:
                if result['flag']:
                    save_data.append({
                        'src_ip': result['src_ip'],
                        'src_port': result['src_port'],
                        'dst_ip': result['dst_ip'],
                        'dst_port': result['dst_port'],
                        'start_time': result['start_time'],
                        'end_time': result['end_time'],
                        'url': result['url'],
                        'body': result['body'],
                        'connections': result['connections'],
                        'upstream': result['upstream'],
                        'downstream': result['downstream']
                    })

            result_excel = os.path.join(self.root_path, "task_%s_result_%s.xlsx" % (
                self.args['name'], datetime.now().strftime('%Y-%m-%d_%H:%M:%S')))

            if os.path.exists(result_excel):
                os.remove(result_excel)

            with pd.ExcelWriter(result_excel) as writer:
                df1 = pd.DataFrame(save_data)
                df1.to_excel(writer, index=False, encoding='utf-8', sheet_name='aggs_result')

                # result detail xlsx download. 写到第二个sheet中
                detail_result = []
                for result in save_data:
                    sql = self.get_sql_with_update_id(
                        task_data['data_types'], task_data['update_id'], result['src_ip'], result['dst_ip'])
                    raw_list = self.get_clickhouse_data(sql)
                    for detail in raw_list:
                        detail_result.append(detail)

                df2 = pd.DataFrame(detail_result)
                df2.to_excel(writer, index=False, encoding='utf-8', sheet_name='detail_result')

            if os.path.exists(result_excel):
                return make_response(send_file(result_excel, as_attachment=True))

            return flask_response('download failed!', True, {'err': 'excel file not exists!'})
        except Exception as e:
            return flask_response('download failed!', True, {'err': str(e)})


class RetentionAnalyis(CertResource):
    def post(self):
        try:
            if 'name' not in self.args.keys():
                return flask_response('task name not include!', True, {})

            task_data = self.mg.find_one(self.retention_col, {'name': self.args['name']})
            if not task_data:
                return flask_response(f"get task {self.args['name']} failed!", True, [])

            result_data = [result for result in task_data['result'] if result['flag']]

            return flask_response('analyis success.', True, {"result": result_data})
        except Exception as e:
            return flask_response('analyis failed!', True, {'err': str(e)})


class RetentionOperateFlag(CertResource):
    def post(self):
        try:
            if 'flag' not in self.args.keys() or 'id' not in self.args.keys():
                return flask_response('flag or id not include!', True, {})

            self.mg.update(self.retention_col, {'result.id': self.args['id']}, {"result.$.flag": self.args['flag']})

            return flask_response('set flag success.', True, {})
        except Exception as e:
            return flask_response('set flag failed!', True, {'err': str(e)})


class RetentionResultDetail(CertResource):
    def get(self):
        try:
            if 'id' not in self.args.keys():
                return flask_response('id not include!', True, {})

            page = int(self.args.get('page', 1))
            page_size = int(self.args.get('pageSize', 10))
            count = 0
            data = {
                'count': count,
                'page': page,
                'pageSize': page_size,
                'detail': []
            }

            task_data = self.mg.find_one(self.retention_col, {'result.id': self.args['id']})
            if not task_data:
                return flask_response(f"get task with result id failed!", True, data)

            result = {}
            for i in task_data['result']:
                if i['id'] == self.args['id']:
                    result = i
                    break

            if not result:
                return flask_response(
                    f"not found result with id {self.args['id']} in task {task_data['name']}!", True, data)

            sql = self.get_sql_with_update_id(
                task_data['data_types'], task_data['update_id'], result['src_ip'], result['dst_ip'])
            raw_list = self.get_clickhouse_data(sql)
            count = len(raw_list)
            for i in raw_list[(page - 1) * page_size: (page - 1) * page_size + page_size]:
                data['detail'].append(i)

            data['count'] = count

            return flask_response('get result detail success.', True, data)
        except Exception as e:
            return flask_response('get result detail failed!', True, {'err': str(e)})

    def post(self):
        try:
            if 'id' not in self.args.keys():
                return flask_response('id not include!', True, {})

            task_data = self.mg.find_one(self.retention_col, {'result.id': self.args['id']})
            if not task_data:
                return flask_response(f"get task with result id failed!", True, {})

            result = {}
            for i in task_data['result']:
                if i['id'] == self.args['id']:
                    result = i
                    break

            if not result:
                return flask_response(
                    f"not found result with id {self.args['id']} in task {task_data['name']}!", True, {})

            sql = self.get_sql_with_update_id(
                task_data['data_types'], task_data['update_id'], result['src_ip'], result['dst_ip'])
            raw_list = self.get_clickhouse_data(sql)
            save_data = []
            for i in raw_list:
                save_data.append(i)

            result_excel = os.path.join(self.root_path, "%s_%s_detail_result_%s.xlsx" % (
                result['src_ip'], result['dst_ip'], datetime.now().strftime('%Y-%m-%d_%H:%M:%S')))

            df = pd.DataFrame(save_data)
            df.to_excel(result_excel, index=False, encoding='utf-8')

            if os.path.exists(result_excel):
                return make_response(send_file(result_excel, as_attachment=True))
        except Exception as e:
            return flask_response('download result detail failed!', True, {'err': str(e)})


class DocExport(CertResource):
    @staticmethod
    def count_ip_addresses(text):
        # 使用正则表达式匹配IP地址
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ip_addresses = re.findall(ip_pattern, text)

        # 统计IP地址数量
        count = len(ip_addresses)

        return count

    @staticmethod
    def convert_date(date_str, day=True):
        # 将字符串解析为日期对象
        date = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")

        # 将日期对象格式化为所需的字符串格式
        formatted_date = date.strftime("%Y年%m月%d日") if day else date.strftime("%Y年%m月%d日%H时%M分")

        return formatted_date

    @staticmethod
    def zip_dir(dirpath, zip_name):
        zip_f = zipfile.ZipFile(zip_name, "w", zipfile.ZIP_DEFLATED)
        for path, dirnames, filenames in os.walk(dirpath):
            # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
            fpath = path.replace(dirpath, '')

            for filename in filenames:
                zip_f.write(os.path.join(path, filename), os.path.join(fpath, filename))
        zip_f.close()

    def post(self):
        try:
            if 'data' not in self.args.keys():
                return flask_response('data not include!', True, {})

            flag_1 = 712
            flag_2 = 368
            flag_time = ''

            export_path = os.path.join(self.root_path, 'export_path')
            if os.path.exists(export_path):
                shutil.rmtree(export_path)

            os.mkdir(export_path)

            doc = Document(self.report_template)
            # data list order: the first item is objectId, and list value as below:
            # company_name inv_facilities inv_ip involved_port inv_domain inv_mail_acnt inv_mail_owner
            # datetime group attack_apt apt_number attack_ip attack_port attack_domain attack_proto
            # phishing_email phishing_file attack_url attack_samples attack_ioc event_model_rule
            # event_ident_cause event_alert_score event_alert_id event_model_type org_lead_type inv_pcap_rule
            # attack_country attack_source industry business_type start_time end_time event_confirm event_notif
            # inv_rule_cfg_person inv_ioc_cfg_person event_discover_person conn_cnt upstream downstream total_flow
            # attack_type extention_type event_uniq_id report_person attack_source_dir interval_months
            # attack_importance_coe attack_type_coe external_report_coe leader_instruct_coe monitor_person
            # clue_cfg_coe clue_source_coe clue_cfg_person forensic_analysis_coe forensic_analysis_person
            # system_maint_coe system_maint_person attack_body jarm ja3s analysis_process
            for result in self.args['data']:
                new_doc = copy.deepcopy(doc)
                task_data = self.mg.find_one(self.retention_col, {'result.id': result['id']})
                if not task_data:
                    return flask_response(f"export get task with result id failed!", True, {})
                for paragraph in new_doc.paragraphs:
                    if '{受害单位}' in paragraph.text:
                        paragraph.text = paragraph.text.replace('{受害单位}', str(result['company_name']))
                    if '{APT组织}' in paragraph.text:
                        paragraph.text = paragraph.text.replace('{APT组织}', str(result['attack_apt']))
                    if '{攻击开始时间年月}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{攻击开始时间年月}', self.convert_date(str(result['start_time'])))
                    if '{攻击结束时间年月}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{攻击结束时间年月}', self.convert_date(str(result['end_time'])))
                    if '{攻击开始时间}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{攻击开始时间}', self.convert_date(str(result['start_time'])))
                    if '{攻击结束时间}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{攻击结束时间}', self.convert_date(str(result['end_time'])))
                    if '{攻击开始时间时分}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{攻击开始时间时分}', self.convert_date(str(result['start_time']), day=False))
                    if '{攻击结束时间时分}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{攻击结束时间时分}', self.convert_date(str(result['end_time']), day=False))
                    if '{发现时间}' in paragraph.text:
                        date_str = str(result['datetime'])
                        try:
                            if date_str:
                                year = date_str[:4]
                                month = date_str[4:6]
                                day = date_str[6:]
                                # 拼接为所需的日期格式
                                formatted_date = f"{year}年{month}月{day}日"
                            else:  # 如果没有自定义时间，则取当前时间
                                formatted_date = datetime.now().strftime("%Y年%m月%d日")
                        except Exception as err:
                            formatted_date = datetime.now().strftime("%Y年%m月%d日")
                        paragraph.text = paragraph.text.replace('{发现时间}', formatted_date)
                    if '{受害IP}' in paragraph.text:
                        paragraph.text = paragraph.text.replace('{受害IP}', str(result['inv_ip']))
                    if '{受害者IP数量}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{受害者IP数量}', str(self.count_ip_addresses(str(result['inv_ip']))))
                    if '{攻击者IP}' in paragraph.text:
                        paragraph.text = paragraph.text.replace('{攻击者IP}', str(result['attack_ip']))
                    if '{攻击者IP端口}' in paragraph.text:
                        paragraph.text = paragraph.text.replace(
                            '{攻击者IP端口}', ' '.join([str(i) for i in result['attack_port']]))
                    if '{通联次数}' in paragraph.text:
                        paragraph.text = paragraph.text.replace('{通联次数}', str(result['conn_cnt']))
                    if '{，数据传输不少总流量大小MB，}' in paragraph.text:
                        try:
                            stream = str(round(int(result['total_flow']) / 1024 / 1024, 2))
                        except Exception as err:
                            stream = '0'
                        if stream == '0':
                            paragraph.text = paragraph.text.replace('{，数据传输不少总流量大小MB，}', '。')
                        else:
                            paragraph.text = paragraph.text.replace('{，数据传输不少总流量大小MB，}', f'，数据传输不少总流量大小{stream}MB，')
                    if '{编号1}' in paragraph.text:
                        paragraph.text = paragraph.text.replace('{编号1}', str(flag_1))
                    if '{编号2}' in paragraph.text:
                        paragraph.text = paragraph.text.replace('{编号2}', str(flag_2))
                    if '{编号3}' in paragraph.text:
                        date_str = str(result['datetime'])
                        try:
                            if date_str:
                                year = date_str[:4]
                                month = date_str[4:6]
                                day = date_str[6:]
                                # 拼接为所需的日期格式
                                formatted_date = f"{year}{month}{day}"
                            else:  # 如果没有自定义时间，则取当前时间
                                formatted_date = datetime.now().strftime("%Y%m%d")
                        except Exception as err:
                            formatted_date = datetime.now().strftime("%Y%m%d")
                        paragraph.text = paragraph.text.replace('{编号3}', formatted_date + "-LZH")
                    if '部分通联日志见下表：' in paragraph.text:
                        data = [
                            ['源IP', '源端口', '目的IP', '目的端口', '时间']
                        ]

                        sql = self.get_sql_with_update_id(
                            task_data['data_types'], task_data['update_id'], result['inv_ip'], result['attack_ip'])
                        raw_list = self.get_clickhouse_data(sql)
                        for i in raw_list[:6]:
                            data.append([
                                i['c_src_ipv4'], str(i['c_src_port']), i['c_dest_ipv4'], str(i['c_dest_port']),
                                i['c_time']
                            ])
                        if len(data):
                            table = new_doc.add_table(rows=len(data), cols=5)
                            paragraph._element.addnext(table._tbl)
                            table.style = "Table Grid"
                            table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                            # 填充数据
                            for i in range(0, len(data)):
                                for j in range(5):
                                    # 在指定的单元格位置添加文本
                                    table.cell(i, j).text = data[i][j]
                                    # 设置垂直对齐为顶部
                                    table.cell(i, j).vertical_alignment = WD_ALIGN_VERTICAL.TOP

                # 遍历表格
                for table in new_doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            # 遍历表格段落内容，回到上个步骤，将cell当作paragraph处理
                            for paragraph in cell.paragraphs:
                                paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                                if '{受害单位}' in cell.text:
                                    paragraph.text = paragraph.text.replace('{受害单位}', str(result['company_name']))
                                if '{受害IP}' in cell.text:
                                    paragraph.text = paragraph.text.replace('{受害IP}', str(result['inv_ip']))
                                if '{攻击者IP}' in cell.text:
                                    paragraph.text = paragraph.text.replace('{攻击者IP}', str(result['attack_ip']))
                                if '{协议}' in paragraph.text:
                                    port_str = 'TLS' if task_data['data_types'] == 'tls' else 'TCP'
                                    paragraph.text = paragraph.text.replace('{协议}', port_str)
                                if '{攻击者IP端口}' in cell.text:
                                    paragraph.text = paragraph.text.replace(
                                        '{攻击者IP端口}', ','.join([str(i) for i in result['attack_port']]))
                                if '{攻击开始时间}' in cell.text:
                                    paragraph.text = paragraph.text.replace(
                                        '{攻击开始时间}', self.convert_date(str(result['start_time'])))
                                if '{攻击结束时间}' in cell.text:
                                    paragraph.text = paragraph.text.replace(
                                        '{攻击结束时间}', self.convert_date(str(result['end_time'])))

                # 生成输出文件名，使用前缀和行号进行命名
                # CNCERT-NetMon-APT-00000708-000364-20231122-SGX-关于国家邮政局主机遭境外APT组织攻击窃密的报告.docx
                file_name = "CNCERT-NetMon-APT-00000" + str(flag_1) + "-000" + str(
                    flag_2) + "-" + flag_time + "-LZH-关于" + str(
                    result['company_name']) + "主机遭境外APT组织攻击窃密的报告"
                output_file = os.path.join(export_path, f"{file_name}.docx")
                # flag_1 += 1
                # flag_2 += 1

                new_doc.save(output_file)

            zip_path = os.path.join(self.root_path, 'report.zip')
            self.zip_dir(export_path, zip_path)

            if not os.path.exists(zip_path):
                flask_response('export failed!', True, {'err': 'zip file not exists!'})

            return make_response(send_file(zip_path, as_attachment=True))
        except Exception as e:
            return flask_response('export failed!', False, {'err': str(e)})
