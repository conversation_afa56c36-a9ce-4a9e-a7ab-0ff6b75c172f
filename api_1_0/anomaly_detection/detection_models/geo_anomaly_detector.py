# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : geo_anomaly_detector.py
# @Software: PyCharm

"""地理/归属异常检测模型"""

from collections import defaultdict, Counter
from datetime import datetime, timedelta
from typing import Dict, List, Any, Set
from .base_detector import BaseDetector
from utils.logger import get_ndr_logger
from utils.database import get_es_client
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class GeoAnomalyDetector(BaseDetector):
    """地理/归属异常检测器

    某IP突然切换国家/ASN
    比如用户IP突然从中国切到俄罗斯
    """

    def __init__(self, task_config: Dict[str, Any]):
        super().__init__(task_config)
        self.baseline_days = self.detection_config.get('baseline_days', 30)
        self.min_baseline_records = self.detection_config.get('min_baseline_records', 10)
        self.geo_change_threshold = self.detection_config.get('geo_change_threshold', 0.8)
        self.asn_change_threshold = self.detection_config.get('asn_change_threshold', 0.7)
        self.suspicious_countries = self.detection_config.get('suspicious_countries', [
            'RU', 'CN', 'KP', 'IR', 'SY'
        ])

        # Elasticsearch客户端
        self.es_client = get_es_client()

    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """执行地理/归属异常检测"""
        try:
            results = []

            # 获取当前时段的地理信息
            current_geo_data = self._get_current_geo_data(start_time, end_time)
            if not current_geo_data:
                LOG.info(f"当前时段无地理数据: {self.target_value}")
                return results

            # 获取基线地理信息
            baseline_geo_data = self._get_baseline_geo_data(start_time)
            if not baseline_geo_data:
                LOG.info(f"无基线地理数据: {self.target_value}")
                return results

            # 分析地理位置变化
            geo_results = self._detect_geo_changes(
                baseline_geo_data, current_geo_data, start_time, end_time
            )
            results.extend(geo_results)

            # 分析ASN归属变化
            asn_results = self._detect_asn_changes(
                baseline_geo_data, current_geo_data, start_time, end_time
            )
            results.extend(asn_results)

            # 检测可疑地理位置
            suspicious_results = self._detect_suspicious_locations(
                current_geo_data, start_time, end_time
            )
            results.extend(suspicious_results)

            return results

        except Exception as e:
            LOG.error(f"地理/归属异常检测失败: {str(e)}")
            return []

    def _get_current_geo_data(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取当前时段的地理信息数据"""
        try:
            # 从Elasticsearch获取告警数据中的地理信息
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "timestamp": {
                                        "gte": int(start_time.timestamp() * 1000),
                                        "lte": int(end_time.timestamp() * 1000)
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": 1000,
                "_source": ["src_ip", "dst_ip", "src_ip_location", "dst_ip_location",
                           "attacker_location", "victim_location", "timestamp"]
            }

            # 根据目标类型添加过滤条件
            if self.target_type == 'ip':
                query["query"]["bool"]["should"] = [
                    {"term": {"src_ip": self.target_value}},
                    {"term": {"dst_ip": self.target_value}},
                    {"term": {"attacker": self.target_value}},
                    {"term": {"victim": self.target_value}}
                ]
                query["query"]["bool"]["minimum_should_match"] = 1

            response = self.es_client.search(index="rule-eve-*", body=query)

            geo_data = []
            for hit in response['hits']['hits']:
                source = hit['_source']
                geo_data.append(source)

            return geo_data

        except Exception as e:
            LOG.error(f"获取当前地理数据失败: {str(e)}")
            return []

    def _get_baseline_geo_data(self, current_start: datetime) -> List[Dict[str, Any]]:
        """获取基线地理信息数据"""
        try:
            baseline_start = current_start - timedelta(days=self.baseline_days)
            baseline_end = current_start - timedelta(hours=1)  # 排除当前时段

            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "timestamp": {
                                        "gte": int(baseline_start.timestamp() * 1000),
                                        "lte": int(baseline_end.timestamp() * 1000)
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": 5000,
                "_source": ["src_ip", "dst_ip", "src_ip_location", "dst_ip_location",
                           "attacker_location", "victim_location", "timestamp"]
            }

            if self.target_type == 'ip':
                query["query"]["bool"]["should"] = [
                    {"term": {"src_ip": self.target_value}},
                    {"term": {"dst_ip": self.target_value}},
                    {"term": {"attacker": self.target_value}},
                    {"term": {"victim": self.target_value}}
                ]
                query["query"]["bool"]["minimum_should_match"] = 1

            response = self.es_client.search(index="rule-eve-*", body=query)

            geo_data = []
            for hit in response['hits']['hits']:
                source = hit['_source']
                geo_data.append(source)

            return geo_data

        except Exception as e:
            LOG.error(f"获取基线地理数据失败: {str(e)}")
            return []

    def _detect_geo_changes(self, baseline_data: List[Dict[str, Any]],
                          current_data: List[Dict[str, Any]],
                          start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测地理位置变化"""
        results = []

        # 统计基线地理位置分布
        baseline_countries = Counter()
        baseline_cities = Counter()

        for record in baseline_data:
            locations = self._extract_locations(record)
            for location in locations:
                if location.get('country_name'):
                    baseline_countries[location['country_name']] += 1
                if location.get('city_name'):
                    baseline_cities[location['city_name']] += 1

        if not baseline_countries:
            return results

        # 统计当前地理位置分布
        current_countries = Counter()
        current_cities = Counter()

        for record in current_data:
            locations = self._extract_locations(record)
            for location in locations:
                if location.get('country_name'):
                    current_countries[location['country_name']] += 1
                if location.get('city_name'):
                    current_cities[location['city_name']] += 1

        # 检测国家变化
        country_anomaly = self._analyze_location_change(
            baseline_countries, current_countries, 'country',
            self.geo_change_threshold, start_time, end_time
        )
        if country_anomaly:
            results.append(country_anomaly)

        # 检测城市变化
        city_anomaly = self._analyze_location_change(
            baseline_cities, current_cities, 'city',
            self.geo_change_threshold, start_time, end_time
        )
        if city_anomaly:
            results.append(city_anomaly)

        return results

    def _detect_asn_changes(self, baseline_data: List[Dict[str, Any]],
                          current_data: List[Dict[str, Any]],
                          start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测ASN归属变化"""
        results = []

        # 这里需要从IP地理信息中提取ASN信息
        # 由于示例数据结构中没有ASN字段，这里提供框架

        # 统计基线ASN分布
        baseline_asns = Counter()
        # TODO: 从地理位置数据中提取ASN信息

        # 统计当前ASN分布
        current_asns = Counter()
        # TODO: 从地理位置数据中提取ASN信息

        # 检测ASN变化
        if baseline_asns and current_asns:
            asn_anomaly = self._analyze_location_change(
                baseline_asns, current_asns, 'asn',
                self.asn_change_threshold, start_time, end_time
            )
            if asn_anomaly:
                results.append(asn_anomaly)

        return results

    def _detect_suspicious_locations(self, current_data: List[Dict[str, Any]],
                                   start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测可疑地理位置"""
        results = []

        # 统计当前出现的国家
        current_countries = set()
        suspicious_records = []

        for record in current_data:
            locations = self._extract_locations(record)
            for location in locations:
                country_code = location.get('areacode', '').upper()
                if country_code:
                    current_countries.add(country_code)
                    if country_code in self.suspicious_countries:
                        suspicious_records.append((record, location))

        # 如果发现可疑国家
        if suspicious_records:
            suspicious_countries_found = set()
            for record, location in suspicious_records:
                suspicious_countries_found.add(location.get('areacode', '').upper())

            # 计算异常分数
            anomaly_score = min(len(suspicious_records) / 10.0, 1.0)

            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)

            result = {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'anomaly_type': 'suspicious_location',
                'suspicious_countries': list(suspicious_countries_found),
                'suspicious_count': len(suspicious_records),
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"检测到{self.target_value}出现在可疑地理位置：{', '.join(suspicious_countries_found)}，"
                             f"共{len(suspicious_records)}条记录",
                'details': {
                    'suspicious_records': suspicious_records[:5],  # 保存前5条记录
                    'detection_config': self.detection_config
                }
            }

            results.append(result)

        return results

    def _extract_locations(self, record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从记录中提取地理位置信息"""
        locations = []

        # 提取各种可能的地理位置字段
        location_fields = ['src_ip_location', 'dst_ip_location', 'attacker_location', 'victim_location']

        for field in location_fields:
            if field in record and record[field]:
                locations.append(record[field])

        return locations

    def _analyze_location_change(self, baseline_counter: Counter, current_counter: Counter,
                               location_type: str, threshold: float,
                               start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """分析位置变化"""
        if not baseline_counter or not current_counter:
            return None

        # 计算基线主要位置
        baseline_main = baseline_counter.most_common(3)  # 取前3个主要位置

        # 计算当前主要位置
        current_main = current_counter.most_common(3)

        # 计算位置变化程度
        baseline_locations = set(baseline_counter.keys())
        current_locations = set(current_counter.keys())

        # 新出现的位置
        new_locations = current_locations - baseline_locations
        # 消失的位置
        disappeared_locations = baseline_locations - current_locations

        # 计算变化比例
        if baseline_locations:
            change_ratio = len(new_locations) / len(baseline_locations)
        else:
            change_ratio = 1.0

        # 判断是否异常
        if change_ratio >= threshold or len(new_locations) >= 2:
            # 计算异常分数
            anomaly_score = min(change_ratio, 1.0)

            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)

            return {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'anomaly_type': f'{location_type}_change',
                'change_ratio': change_ratio,
                'baseline_locations': [loc for loc, _ in baseline_main],
                'current_locations': [loc for loc, _ in current_main],
                'new_locations': list(new_locations),
                'disappeared_locations': list(disappeared_locations),
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"检测到{self.target_value}的{location_type}发生异常变化，"
                             f"新出现{len(new_locations)}个位置，变化比例{change_ratio:.2%}",
                'details': {
                    'baseline_distribution': dict(baseline_counter.most_common(10)),
                    'current_distribution': dict(current_counter.most_common(10)),
                    'detection_config': self.detection_config
                }
            }

        return None

    def _detect_suspicious_locations(self, current_data: List[Dict[str, Any]],
                                   start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测可疑地理位置"""
        results = []

        # 统计当前出现的国家
        current_countries = set()
        suspicious_records = []

        for record in current_data:
            locations = self._extract_locations(record)
            for location in locations:
                country_code = location.get('areacode', '').upper()
                if country_code:
                    current_countries.add(country_code)
                    if country_code in self.suspicious_countries:
                        suspicious_records.append((record, location))

        # 如果发现可疑国家
        if suspicious_records:
            suspicious_countries_found = set()
            for record, location in suspicious_records:
                suspicious_countries_found.add(location.get('areacode', '').upper())

            # 计算异常分数
            anomaly_score = min(len(suspicious_records) / 10.0, 1.0)

            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)

            result = {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'anomaly_type': 'suspicious_location',
                'suspicious_countries': list(suspicious_countries_found),
                'suspicious_count': len(suspicious_records),
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"检测到{self.target_value}出现在可疑地理位置：{', '.join(suspicious_countries_found)}，"
                             f"共{len(suspicious_records)}条记录",
                'details': {
                    'suspicious_records': suspicious_records[:5],  # 保存前5条记录
                    'detection_config': self.detection_config
                }
            }

            results.append(result)

        return results

    def _extract_locations(self, record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从记录中提取地理位置信息"""
        locations = []

        # 提取各种可能的地理位置字段
        location_fields = ['src_ip_location', 'dst_ip_location', 'attacker_location', 'victim_location']

        for field in location_fields:
            if field in record and record[field]:
                locations.append(record[field])

        return locations


class GeoAnomalyDetector(BaseDetector):
    """地理/归属异常检测器
    
    某IP突然切换国家/ASN
    比如用户IP突然从中国切到俄罗斯
    """
    
    def __init__(self, task_config: Dict[str, Any]):
        super().__init__(task_config)
        self.baseline_days = self.detection_config.get('baseline_days', 30)
        self.min_baseline_records = self.detection_config.get('min_baseline_records', 10)
        self.geo_change_threshold = self.detection_config.get('geo_change_threshold', 0.8)  # 地理位置变化阈值
        self.asn_change_threshold = self.detection_config.get('asn_change_threshold', 0.7)  # ASN变化阈值
        self.suspicious_countries = self.detection_config.get('suspicious_countries', [
            'RU', 'CN', 'KP', 'IR', 'SY'  # 可疑国家代码
        ])
        
        # Elasticsearch客户端
        self.es_client = get_es_client()
    
    def detect(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """执行地理/归属异常检测"""
        try:
            results = []
            
            # 获取当前时段的地理信息
            current_geo_data = self._get_current_geo_data(start_time, end_time)
            if not current_geo_data:
                LOG.info(f"当前时段无地理数据: {self.target_value}")
                return results
            
            # 获取基线地理信息
            baseline_geo_data = self._get_baseline_geo_data(start_time)
            if not baseline_geo_data:
                LOG.info(f"无基线地理数据: {self.target_value}")
                return results
            
            # 分析地理位置变化
            geo_results = self._detect_geo_changes(
                baseline_geo_data, current_geo_data, start_time, end_time
            )
            results.extend(geo_results)
            
            # 分析ASN归属变化
            asn_results = self._detect_asn_changes(
                baseline_geo_data, current_geo_data, start_time, end_time
            )
            results.extend(asn_results)
            
            # 检测可疑地理位置
            suspicious_results = self._detect_suspicious_locations(
                current_geo_data, start_time, end_time
            )
            results.extend(suspicious_results)
            
            return results
            
        except Exception as e:
            LOG.error(f"地理/归属异常检测失败: {str(e)}")
            return []
    
    def _get_current_geo_data(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取当前时段的地理信息数据"""
        try:
            # 从Elasticsearch获取告警数据中的地理信息
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "timestamp": {
                                        "gte": int(start_time.timestamp() * 1000),
                                        "lte": int(end_time.timestamp() * 1000)
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": 1000,
                "_source": ["src_ip", "dst_ip", "src_ip_location", "dst_ip_location", 
                           "attacker_location", "victim_location", "timestamp"]
            }
            
            # 根据目标类型添加过滤条件
            if self.target_type == 'ip':
                query["query"]["bool"]["should"] = [
                    {"term": {"src_ip": self.target_value}},
                    {"term": {"dst_ip": self.target_value}},
                    {"term": {"attacker": self.target_value}},
                    {"term": {"victim": self.target_value}}
                ]
                query["query"]["bool"]["minimum_should_match"] = 1
            
            response = self.es_client.search(index="rule-eve-*", body=query)
            
            geo_data = []
            for hit in response['hits']['hits']:
                source = hit['_source']
                geo_data.append(source)
            
            return geo_data
            
        except Exception as e:
            LOG.error(f"获取当前地理数据失败: {str(e)}")
            return []
    
    def _get_baseline_geo_data(self, current_start: datetime) -> List[Dict[str, Any]]:
        """获取基线地理信息数据"""
        try:
            baseline_start = current_start - timedelta(days=self.baseline_days)
            baseline_end = current_start - timedelta(hours=1)  # 排除当前时段
            
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "timestamp": {
                                        "gte": int(baseline_start.timestamp() * 1000),
                                        "lte": int(baseline_end.timestamp() * 1000)
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": 5000,
                "_source": ["src_ip", "dst_ip", "src_ip_location", "dst_ip_location", 
                           "attacker_location", "victim_location", "timestamp"]
            }
            
            if self.target_type == 'ip':
                query["query"]["bool"]["should"] = [
                    {"term": {"src_ip": self.target_value}},
                    {"term": {"dst_ip": self.target_value}},
                    {"term": {"attacker": self.target_value}},
                    {"term": {"victim": self.target_value}}
                ]
                query["query"]["bool"]["minimum_should_match"] = 1
            
            response = self.es_client.search(index="rule-eve-*", body=query)
            
            geo_data = []
            for hit in response['hits']['hits']:
                source = hit['_source']
                geo_data.append(source)
            
            return geo_data
            
        except Exception as e:
            LOG.error(f"获取基线地理数据失败: {str(e)}")
            return []
    
    def _detect_geo_changes(self, baseline_data: List[Dict[str, Any]], 
                          current_data: List[Dict[str, Any]],
                          start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测地理位置变化"""
        results = []
        
        # 统计基线地理位置分布
        baseline_countries = Counter()
        baseline_cities = Counter()
        
        for record in baseline_data:
            locations = self._extract_locations(record)
            for location in locations:
                if location.get('country_name'):
                    baseline_countries[location['country_name']] += 1
                if location.get('city_name'):
                    baseline_cities[location['city_name']] += 1
        
        if not baseline_countries:
            return results
        
        # 统计当前地理位置分布
        current_countries = Counter()
        current_cities = Counter()
        
        for record in current_data:
            locations = self._extract_locations(record)
            for location in locations:
                if location.get('country_name'):
                    current_countries[location['country_name']] += 1
                if location.get('city_name'):
                    current_cities[location['city_name']] += 1
        
        # 检测国家变化
        country_anomaly = self._analyze_location_change(
            baseline_countries, current_countries, 'country', 
            self.geo_change_threshold, start_time, end_time
        )
        if country_anomaly:
            results.append(country_anomaly)
        
        # 检测城市变化
        city_anomaly = self._analyze_location_change(
            baseline_cities, current_cities, 'city',
            self.geo_change_threshold, start_time, end_time
        )
        if city_anomaly:
            results.append(city_anomaly)
        
        return results
    
    def _detect_asn_changes(self, baseline_data: List[Dict[str, Any]], 
                          current_data: List[Dict[str, Any]],
                          start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测ASN归属变化"""
        results = []
        
        # 这里需要从IP地理信息中提取ASN信息
        # 由于示例数据结构中没有ASN字段，这里提供框架
        
        # 统计基线ASN分布
        baseline_asns = Counter()
        # TODO: 从地理位置数据中提取ASN信息
        
        # 统计当前ASN分布
        current_asns = Counter()
        # TODO: 从地理位置数据中提取ASN信息
        
        # 检测ASN变化
        if baseline_asns and current_asns:
            asn_anomaly = self._analyze_location_change(
                baseline_asns, current_asns, 'asn',
                self.asn_change_threshold, start_time, end_time
            )
            if asn_anomaly:
                results.append(asn_anomaly)
        
        return results
    
    def _detect_suspicious_locations(self, current_data: List[Dict[str, Any]],
                                   start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """检测可疑地理位置"""
        results = []
        
        # 统计当前出现的国家
        current_countries = set()
        suspicious_records = []
        
        for record in current_data:
            locations = self._extract_locations(record)
            for location in locations:
                country_code = location.get('areacode', '').upper()
                if country_code:
                    current_countries.add(country_code)
                    if country_code in self.suspicious_countries:
                        suspicious_records.append((record, location))
        
        # 如果发现可疑国家
        if suspicious_records:
            suspicious_countries_found = set()
            for record, location in suspicious_records:
                suspicious_countries_found.add(location.get('areacode', '').upper())
            
            # 计算异常分数
            anomaly_score = min(len(suspicious_records) / 10.0, 1.0)
            
            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)
            
            result = {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'anomaly_type': 'suspicious_location',
                'suspicious_countries': list(suspicious_countries_found),
                'suspicious_count': len(suspicious_records),
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"检测到{self.target_value}出现在可疑地理位置：{', '.join(suspicious_countries_found)}，"
                             f"共{len(suspicious_records)}条记录",
                'details': {
                    'suspicious_records': suspicious_records[:5],  # 保存前5条记录
                    'detection_config': self.detection_config
                }
            }
            
            results.append(result)
        
        return results
    
    def _extract_locations(self, record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从记录中提取地理位置信息"""
        locations = []
        
        # 提取各种可能的地理位置字段
        location_fields = ['src_ip_location', 'dst_ip_location', 'attacker_location', 'victim_location']
        
        for field in location_fields:
            if field in record and record[field]:
                locations.append(record[field])
        
        return locations
    
    def _analyze_location_change(self, baseline_counter: Counter, current_counter: Counter,
                               location_type: str, threshold: float,
                               start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """分析位置变化"""
        if not baseline_counter or not current_counter:
            return None
        
        # 计算基线主要位置
        baseline_total = sum(baseline_counter.values())
        baseline_main = baseline_counter.most_common(3)  # 取前3个主要位置
        
        # 计算当前主要位置
        current_total = sum(current_counter.values())
        current_main = current_counter.most_common(3)
        
        # 计算位置变化程度
        baseline_locations = set(baseline_counter.keys())
        current_locations = set(current_counter.keys())
        
        # 新出现的位置
        new_locations = current_locations - baseline_locations
        # 消失的位置
        disappeared_locations = baseline_locations - current_locations
        
        # 计算变化比例
        if baseline_locations:
            change_ratio = len(new_locations) / len(baseline_locations)
        else:
            change_ratio = 1.0
        
        # 判断是否异常
        if change_ratio >= threshold or len(new_locations) >= 2:
            # 计算异常分数
            anomaly_score = min(change_ratio, 1.0)
            
            # 确定严重程度
            severity = self.get_severity_level(anomaly_score)
            
            return {
                'anomaly_score': anomaly_score,
                'severity': severity,
                'anomaly_type': f'{location_type}_change',
                'change_ratio': change_ratio,
                'baseline_locations': [loc for loc, count in baseline_main],
                'current_locations': [loc for loc, count in current_main],
                'new_locations': list(new_locations),
                'disappeared_locations': list(disappeared_locations),
                'time_window': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'description': f"检测到{self.target_value}的{location_type}发生异常变化，"
                             f"新出现{len(new_locations)}个位置，变化比例{change_ratio:.2%}",
                'details': {
                    'baseline_distribution': dict(baseline_counter.most_common(10)),
                    'current_distribution': dict(current_counter.most_common(10)),
                    'detection_config': self.detection_config
                }
            }
        
        return None
