# -*-coding:utf-8-*-
# @File    : system_monitor.data.py
# @Software: PyCharm
import copy

import requests
import time
import json
from clickhouse_driver import Client as CH_client
from flask import request
from flask_restful import Resource

from api_1_0.hdp_grpc.die import grpc_newssnlog_switch
from utils import param_check
from utils.es_function import get_range
from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils.utils import flask_response
from utils.logger import get_ndr_logger
from config.config import NdrLog, ClickHouseConfig
from utils.database import MongoDB

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)
ES_LOG = get_ndr_logger('es_log', __file__)


class TrafficMonitor(Resource):
    """ 流量监控 """

    def __init__(self):
        self.db = MongoDB("ndr")
        self.ch_client = CH_client(
            host=ClickHouseConfig.CH_HOST,
            user=ClickHouseConfig.CH_USER,
            password=ClickHouseConfig.CH_PASSWORD,
            database=ClickHouseConfig.CH_DATABASE)

    def __del__(self):
        self.ch_client.disconnect()

    def get_val_from_database(self, sql):
        ret_list = list()
        try:
            raw = self.ch_client.execute(sql, with_column_types=True)
        except:
            LOG.error("ClickHouse数据库连接或执行失败！")
            return ret_list
        if raw is None or not raw[0]:
            return ret_list
        # raw_val is column value list
        raw_val = raw[0]
        # raw_key is column key and type tuple list, ex: [('ts', 'Datetime')]
        raw_key = raw[1]
        for i in range(len(raw_val)):
            data = {}
            for idx, item in enumerate(raw_val[i]):
                if isinstance(item, bytes):
                    item = str(item)
                data[raw_key[idx][0]] = item
            ret_list.append(data)
        return ret_list


class InterfaceSpeed(TrafficMonitor):
    """
    接口流量速率
    """

    def __init__(self):
        super(InterfaceSpeed, self).__init__()

    def __del__(self):
        super(InterfaceSpeed, self).__del__()

    def get(self):
        try:
            return_data = {
                "total": {},
                "speed": {}
            }
            rst = self.db.find('monitor_interface', {}, {"_id": 0}).sort("createTime", 1)
            count = self.db.count('monitor_interface', {})
            lastest = rst[count - 1]['createTime'] * 1000
            for col in rst:
                create_time = col['createTime'] * 1000
                if 'createTime' not in return_data['speed']:
                    return_data["speed"]['createTime'] = [create_time]
                    prev_time = create_time
                elif prev_time != create_time:
                    return_data["speed"]['createTime'].append(create_time)
                    prev_time = create_time

                node_name = col.get('node_name', '')
                for key, value in col.items():
                    if node_name:
                        key = node_name + "_" + key

                    if lastest == create_time and key.endswith('total'):
                        if key not in return_data['total']:
                            return_data["total"][key] = value
                        else:
                            return_data["total"][key] += value

                    if key.endswith("speed"):
                        if key not in return_data["speed"]:
                            return_data["speed"][key] = [value]
                        else:
                            return_data["speed"][key].append(value)
            return flask_response("successful", True, return_data)
        except Exception as error:
            LOG.error("Interface get failed.Reason：%s" % str(error))
            return flask_response("Interface get failed", False, {})


class TrafficOutPut(TrafficMonitor):
    """
    流量吞吐速率
    """

    def __init__(self):
        super(TrafficOutPut, self).__init__()

    def __del__(self):
        super(TrafficOutPut, self).__del__()

    def get(self):
        try:
            min_data = 0
            obj = self.db.find_one('system_config', {})
            if obj is None:
                return flask_response('Not found', False, {})
            performance = obj["performance"]
            if performance == "1G":
                max_data = 1000
            else:
                max_data = 10000
            return_data = {
                "max": max_data,
                "out_total": 0,
                "min": min_data
            }
            rst = self.db.find('monitor_interface', {}, {"_id": 0}).sort("createTime", -1).limit(1)
            for key, value in rst[0].items():
                if key.endswith("speed"):
                    return_data["out_total"] += int(value)
            if return_data["out_total"] > max_data:
                return_data["out_total"] = max_data
        except Exception as e:
            LOG.error("get traffic output  failed:%s" % e)
            return flask_response("get traffic output failed", False, {})
        return flask_response("successful", True, return_data)


class AppTraffic(TrafficMonitor):
    """
    应用流量统计
    """

    def __init__(self):
        super(AppTraffic, self).__init__()

    def __del__(self):
        super(AppTraffic, self).__del__()

    @param_check.check_flask_args(Validator('app_traffic_schema'), request)
    def post(self, **kwargs):
        app_list = kwargs["app_list"]
        #  当前时间取向下取整小时的整点时间
        hour_time = (int(time.time()) // 3600) * 3600
        #  当前时间取向下取整5秒的整点时间
        present_time = (int(time.time()) // 5) * 5
        # 如果协议列表为空/当前整点时间请求 则计算一次过去一小时 top10 协议
        try:
            if not app_list or present_time - hour_time <= 5:
                time_cond = '(ts >= %s)' % (present_time - 3600)
                app_traffic_sql = "select application,sum(orig_ip_bytes)+sum(resp_ip_bytes) as total_bytes " \
                                  "from dpilog_conn where %s group by application " \
                                  "order by total_bytes desc limit 10 " % time_cond
                rst_list = self.get_val_from_database(app_traffic_sql)
                app_list.clear()
                for rst in rst_list:
                    app_list.append("'%s'" % rst["application"])
            # 取过去10分钟时间
            past_min_time = present_time - 10 * 60
            return_data = {"app_list": app_list, "app_data": []}
            app_dict = {}
            for i in app_list:
                app_dict[i.replace("'", "")] = 0
            for i in range(0, 120):
                data = {
                    "time": (past_min_time + 5 * i) * 1000,
                    "app": copy.deepcopy(app_dict)
                }
                return_data["app_data"].append(data)
            time_cond = '(ts >= %s and ts <= %s)' % (past_min_time, present_time)
            app_traffic_sql = "select toStartOfInterval(ts, INTERVAL 5 second) as time,application," \
                              "sum(orig_ip_bytes)+sum(resp_ip_bytes) as total_bytes from dpilog_conn " \
                              "where application in (%s) and %s group by (time,application)" \
                              " order by time" \
                              % (",".join(app_list), time_cond)
            rst_list = self.get_val_from_database(app_traffic_sql)
            if rst_list:
                for rst in rst_list:
                    rst_time = int(rst["time"].timestamp() * 1000)
                    for i in return_data["app_data"]:
                        if rst_time == i["time"]:
                            i["app"][rst["application"]] = rst["total_bytes"] >> 10
                            break
        except Exception as e:
            LOG.error("get app traffic failed:%s" % e)
            return flask_response("get app traffic failed", False, {})
        return flask_response("successful", True, return_data)


class NewConnSpeed(TrafficMonitor):
    """
    新建链接速率
    """

    def __init__(self):
        super(NewConnSpeed, self).__init__()

    def __del__(self):
        super(NewConnSpeed, self).__del__()

    def get(self):
        try:
            # 创建链接日志
            grpc_newssnlog_switch(True)
        except Exception as e:
            LOG.info("create conn failed:%s" % e)
        try:
            max_data = 1000
            data_return = {"min": 0, "speed": 0, "max": max_data}
            interval = 5
            filter_time = int(time.time()) - interval
            new_conn_sql = "select count(1) as total from dpilog_new_conn where create_time >=%s " % filter_time
            rst_list = self.get_val_from_database(new_conn_sql)
            total = rst_list[0]["total"]
            speed = total // interval
            if 1000 <= speed < 10000:
                max_data = 10000
            elif 10000 <= speed < 100000:
                max_data = 100000
            elif 100000 <= speed < 200000:
                max_data = 200000
            elif 200000 <= speed:
                max_data = 400000
            data_return["speed"] = speed
            data_return["max"] = max_data
        except Exception as e:
            LOG.error("get new_conn speed failed:%s" % e)
            return flask_response("get new_conn speed failed", False, {})
        return flask_response("successful", True, {"speed": data_return})


class NewConnTop(TrafficMonitor):
    """
    新建链接top10
    """

    def __init__(self):
        super(NewConnTop, self).__init__()

    def __del__(self):
        super(NewConnTop, self).__del__()

    def get(self):
        return_data = {
            "send_data": [],
            "receive_data": []
        }
        interval = 600
        filter_time = int(time.time()) - interval
        try:
            send_sql = "select count(1) as total,src_ip from dpilog_new_conn where create_time >=%s " \
                       "group by src_ip order by total desc limit 10" % filter_time
            send_list = self.get_val_from_database(send_sql)
            receive_sql = "select count(1) as total,dst_ip from dpilog_new_conn where create_time >=%s " \
                          "group by dst_ip order by total desc limit 10" % filter_time
            receive_list = self.get_val_from_database(receive_sql)
            for data in send_list:
                total = data["total"]
                src_ip = data["src_ip"]
                return_data["send_data"].append({"total": total, "src_ip": src_ip})
            for data in receive_list:
                total = data["total"]
                dst_ip = data["dst_ip"]
                return_data["receive_data"].append({"total": total, "dst_ip": dst_ip})
        except Exception as e:
            LOG.error("get new_conn speed failed:%s" % e)
            return flask_response("get new_conn speed failed", False, {})
        return flask_response("successful", True, return_data)


class TrafficEvent(NdrResource):
    """
    流量事件
    """

    def __init__(self):
        super(TrafficEvent, self).__init__(es_template="traffic_event")

    def get(self):
        stop_time = int(time.time()) * 1000
        start_time = (int(time.time()) - 10 * 60) * 1000
        time_range = get_range('observedTime', start_time, stop_time)
        self.es_template["query"]["bool"]["must"].append(time_range)
        try:
            res = self.es_client.search(index="model-eve", body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            return flask_response(err_info, False, {})

    def data_format(self, es_data):
        data_return = {
            "alerts": []
        }
        rst_list = es_data["aggregations"]["group_id"]["buckets"]
        num = 1
        model_name_list = ["ndr_model_0004", "ndr_model_0003", "ndr_model_0015", "ndr_model_0016"]
        for rst in rst_list:
            model = {}
            data = rst["doc_ids"]["hits"]["hits"][0]["_source"]
            if data["modelCode"] in model_name_list:
                model_name = data["modelName"]
                model_info = data["info"]
                src_ip = data["flow"]["src_ip"]
                dst_ip = data["flow"]["dst_ip"]
                model["num"] = num
                model["model_name"] = model_name
                model["model_info"] = model_info
                model["src_ip"] = src_ip
                model["dst_ip"] = dst_ip
                data_return["alerts"].append(model)
                num += 1
                if num > 10:
                    break
        return data_return
