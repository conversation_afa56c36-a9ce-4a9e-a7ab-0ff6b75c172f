#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by daMao

import json
from flask import request
from flask_restful import Resource

from api_1_0.knowledge.get_killchains import GetKillchains
from utils.param_check import Validator
from utils.utils import flask_response
from utils.es_template.get_es_template import ES_Template
from utils.es_function import *
from utils import param_check
from utils.database import get_es_client
from config.config import RULE_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class simpleReportResource(Resource):
    # 此接口已拆分为三个子接口，启用
    field_template = {
        "observed": "observedTime",
        "occurred": "occurredTime",
    }
    level_template = {
        0: "Low",
        1: "Medium",
        2: "High"
    }

    def __init__(self):
        self.es_template = ES_Template().read_template("report_event_count")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        start_time = int(kwargs["args"]["startTime"])
        stop_time = int(kwargs["args"]["stopTime"])
        if start_time >= stop_time:
            message = "时间范围错误，开始时间大于结束时间"
            return flask_response(message, False, {})

        timeMode = self.field_template[kwargs["args"]["timeMode"]]
        self.es_template["query"]["bool"]["must"].append(get_range(timeMode, start_time, stop_time))
        country = kwargs["country"]
        if country:
            country_term = get_country_term(country)
            self.es_template["query"]["bool"]["must"].append(country_term)

        # 连接ES查询
        es_client = get_es_client()
        try:
            res = es_client.search(index=RULE_INDEX, body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, res):
        buckets = res["aggregations"]["attackip"]["buckets"]
        data_return = {
            "totalCount": len(buckets),
            "threatLevels": {
                "High": 0,
                "Medium": 0,
                "Low": 0,
            },
            "Killchains": {i: 0 for i in list(GetKillchains.killchain_map.keys())},
            "threatFlags": {
                "Exploits and Attacks": 0,
                "APT": 0,
                "Malicious host": 0,
                "Suspicious": 0,
                "Botnet": 0,
                "Phishing": 0,
                "Scanning": 0,
                "Malware": 0,
                "DoS": 0,
            }
        }

        for bucket in buckets:
            # 获取危险等级
            level = self.level_template.get(bucket["threatLevel"]["value"])
            if level:
                data_return["threatLevels"][level] += 1

            # 统计杀伤链阶段
            for Killchain in bucket["lockheedKillchainENList"]["buckets"]:
                key = Killchain["key"]
                if key:
                    data_return["Killchains"][key] += 1

            # 威胁分类统计
            for threat_flag in bucket["threatFlagList"]["buckets"]:
                key = threat_flag["key"]
                if key:
                    data_return["threatFlags"][key] += 1

        return data_return
