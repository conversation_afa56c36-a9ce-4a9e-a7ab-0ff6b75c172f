# -*- coding: utf-8 -*-
# @Time    : 2019-05-05 14:10
# <AUTHOR> hachi
# @File    : api_feature_manager.py
# @Software: PyCharm

import os, json

from api_1_0.hdp_grpc.die import grpc_feature_upgrade
from celery_tasks import app
from config.config import Ndr<PERSON>og, SystemFeaturePath, DetectProg
from utils.database import BaseMongoDB
from utils.utils import run_command, get_hdp_feature_str_from_query
from utils.cluster import *
from utils.logger import get_ndr_logger

LOG = get_ndr_logger(NdrLog.Type.RUN, __file__)


@app.task()
def periodic_task(upgrade=False):
    """
    保存生效规则
    :return:
    """
    db = BaseMongoDB("ndr")
    features = db.find("feature", {"featureStatus": "enable"}, {
        "vulName": 1, "cve": 1, "threatScore": 1, "threatFlag": 1, "lockheedKillchainCN": 1,
        "lockheedKillchainEN": 1, "featureGroup": 1, "featureContent": 1})
    with open(SystemFeaturePath.HDP_DEFAULT_FEATURE, 'w') as hdp_file:
        for feature in features:
            # 保存所有的特征
            feat_str = get_hdp_feature_str_from_query(feature)
            if not feat_str:
                LOG.error("Update feature failed! Reason: feature data not correct!\n %s" % str(feature))
                return
            hdp_file.write(feat_str)

    # db.update_many("feature", {"featureStatus": "enable"}, {"featureStatus": "active"})
    # 更新特征库
    sipack_path = os.path.join(SystemFeaturePath.RULES_PATH, 'ips-sigpack-en.dat')
    ret, msg = run_command('%s --featurePath %s --sigPackPath %s' % (
        DetectProg.Hdp_SigCompile, SystemFeaturePath.HDP_DEFAULT_FEATURE, sipack_path))

    if not ret:
        LOG.error("Update feature failed! Reason:\n %s" % msg)
        return

    run_command('cp %s %s' % (sipack_path, SystemFeaturePath.HDP_DEFAULT_SIGPACK_PATH))

    if upgrade:
        response = grpc_feature_upgrade(SystemFeaturePath.HDP_DEFAULT_SIGPACK_PATH)
        if not response:
            cluster_sync_file(SystemFeaturePath.HDP_DEFAULT_SIGPACK_PATH)
            LOG.error("Update feature failed! Reason: Grpc hdp upgrade failed!\n")
        else:
            cluster_hdp_feature_upgrade(SystemFeaturePath.HDP_DEFAULT_SIGPACK_PATH)
            LOG.info("Upgrade feature success return: %d\n" % response.code)
    else:
        cluster_sync_file(SystemFeaturePath.HDP_DEFAULT_SIGPACK_PATH)
