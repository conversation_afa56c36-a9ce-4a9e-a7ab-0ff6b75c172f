import React from 'react';
import { useHistory } from '@/hooks/global';
import { Link } from 'umi';

interface IProps {
  to: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export default ({ children, to, ...rest }: IProps) => {
  const history = useHistory();
  if (window.__POWERED_BY_QIANKUN__) {
    return (
      <a
        onClick={() => {
          history.push(to);
        }}
        {...rest}
      >
        {children}
      </a>
    );
  }
  return (
    <Link to={to} {...rest}>
      {children}
    </Link>
  );
};
