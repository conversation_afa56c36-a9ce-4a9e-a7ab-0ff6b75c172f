import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export type ThreatReportListParam = {
  page: number;
  pageSize: number;
  order: string;
};

export type ThreatReportCreateParams = {
  name: string;
  format: string[];
  content: string[];
  high_risk?: number[];
  risk?: number[];
  encrypt?: string;
  watermark?: string;
  custom_name?: string;
};

export type ThreatReportDeleteParam = {
  ids: string;
};

export type DownloadReportParam = {
  name: string;
};

const apiVersion = 'v3';

// 威胁报告列表
export const getThreatReportList = (data: ThreatReportListParam) => {
  return request(`threatreport/list${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// 创建自定义威胁报告提交参数
export const createThreatReport = (data: ThreatReportCreateParams) => {
  return request('threatreport/create', {
    apiVersion,
    method: 'post',
    data,
  });
};

// 删除威胁报告，ids为','拼接的id
export const deleteThreatReport = (data: ThreatReportDeleteParam) => {
  return request(`threatreport/del${parseQueryString(data)}`, {
    method: 'delete',
    apiVersion,
  });
};

// 威胁报告下载
export const downloadReport = (data: DownloadReportParam) => {
  return request(`threatreport/download${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
    responseType: 'blob',
  });
};

// 获取周期报告配置
export const getThreatReportConfig = () => {
  return request('threatreport/config', {
    apiVersion,
    method: 'get',
  });
};

// 修改周期报告设置
export const setThreatReportConfig = (data: object) => {
  return request('threatreport/config', {
    apiVersion,
    method: 'post',
    data,
  });
};
