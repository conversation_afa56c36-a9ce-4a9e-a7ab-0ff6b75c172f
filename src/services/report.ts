/*
 * @Author: 田浩
 * @Date: 2021-07-19 15:32:01
 * @LastEditors: 田浩
 * @LastEditTime: 2021-10-25 11:06:03
 * @Descripttion:
 */
import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export const eventDetail = (data: object) => {
  return request(`report/simple/statistics_level${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getThreatenType = (data: object) => {
  return request(`report/simple/statistics_threat${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getFlow = (data: object) => {
  return request(`report/simple/threat_flow${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getDanger = (data: object) => {
  return request(`report/simple/proto_flow${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const allFlow = (data: object) => {
  return request(`report/detail/all_flow${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const gethandleExportData = (data: object) => {
  return request(`event/list${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

export const getExportFlie = (data: object) => {
  return request(`report/export${parseQueryString(data)}`, {
    method: 'post',
    data,
  });
};

export const killchainsFlow = (data: any) => {
  return request(`/report/simple/killchains_flow${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const hotEventList = (data: any) => {
  return request(`report/simple/top_event${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const getKillChainsList = (data: any) => {
  return request(
    `report/simple/statistics_killchains${parseQueryString(data)}`,
    {
      method: 'get',
    },
  );
};
