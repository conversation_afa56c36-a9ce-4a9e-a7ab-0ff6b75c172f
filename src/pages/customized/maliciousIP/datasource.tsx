import React, { useEffect, useState } from 'react';
import {
  Button,
  Upload,
  Collapse,
  Select,
  Form,
  Input,
  Spin,
  Table,
  Modal,
  Row,
  Col,
  message,
  Popconfirm,
  Tooltip,
  DatePicker,
} from 'antd';
const { Panel } = Collapse;
const { Option } = Select;
import { data_types } from '@/utils/enumList';
import { SearchOutlined, UploadOutlined } from '@ant-design/icons';
import {
  getHiveListData,
  addHiveData,
  editHiveData,
  getHiveTemplate,
  deleteHiveData,
  delDatasource,
  getHiveDetail,
  startDataSource,
  stopDataSource,
} from '@/services/collect';
import moment from 'moment';
import { ellipsis } from '@/utils/utils';
const DataSource = () => {
  const [form] = Form.useForm();
  const [taskForm] = Form.useForm();
  const [total, settotal] = useState(0);
  const [loading, setloading] = useState(false);
  const [detailData, setdetailData] = useState<any>({});
  const [params, setparams] = useState<any>({});
  const [tableData, setTableData] = useState<any>([]);
  const [id, setId] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [title, settitle] = useState('');

  const columns = [
    {
      title: '数据源名称',
      dataIndex: 'source_name',
    },

    {
      title: '数据类型',
      dataIndex: 'data_types',
    },
    {
      title: '规则概要信息',
      dataIndex: 'rules',
      render: (t: any[]) => {
        let arr: string[] = [];
        t.forEach(item => {
          arr.push(`${item.ip} ${item.ports}`);
        });
        return (
          <div>
            <Tooltip placement="bottom" title={arr.toString()}>
              {ellipsis(arr.toString())}
            </Tooltip>
          </div>
        );
      },
    },

    {
      title: '起止时间',
      dataIndex: 'start_time',
      render: (t: string, record: any) => {
        return (
          <div>
            {moment(parseInt(record.start_time) * 1000).format('YYYY-MM-DD HH:mm:ss')}-
            {moment(parseInt(record.end_time) * 1000).format('YYYY-MM-DD HH:mm:ss')}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (t: any) => {
        return (
          <div className="w-22">
            <div className="text-left ws-nowrap">{t === 'idle' ? '就绪' : ''}</div>
            <div className="text-left ws-nowrap">{t === 'failed' ? '获取失败' : ''}</div>
            <div className="text-left ws-nowrap">{t === 'running' ? '获取中' : ''}</div>
            <div className="text-left ws-nowrap">{t === 'success' ? '获取成功' : ''}</div>
          </div>
        );
      },
    },
    {
      title: '条数',
      dataIndex: 'statistics',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 160,
      align: 'center',
      render: (t: any, record: any) => {
        return (
          <>
            {record.status !== 'running' && (
              <Button
                onClick={() => {
                  handleStart(record._id);
                }}
                style={{ color: '#1890ff' }}
                type="link"
              >
                开始
              </Button>
            )}
            {record.status === 'running' && (
              <Button
                onClick={() => {
                  handleStop(record._id);
                }}
                style={{ color: '#1890ff' }}
                type="link"
              >
                停止
              </Button>
            )}
            <Button
              onClick={() => {
                handleEdit(record);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              编辑
            </Button>
            <Popconfirm title="确定删除吗？" okText="确定" cancelText="取消" onConfirm={() => deleteTask(record.name)}>
              <Tooltip>
                <Button
                  onClick={() => {
                    handleDel(record._id);
                  }}
                  type="link"
                  danger
                >
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>

            <Button
              onClick={() => {
                executeTask(record._id);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];
  useEffect(() => {
    searchList();
  }, [params]);

  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const handleEdit = (data: any) => {
    getHiveDetail(data._id).then(res => {
      if (res.flag) {
        setId(data._id);
        res.data.time = [moment(res.data.start_time * 1000), moment(res.data.end_time * 1000)];
        setdetailData(res.data);
        settitle('编辑数据源');

        setIsModalOpen(true);
      } else {
        message.error(res.message);
      }
    });
  };
  const handleOk = async () => {
    taskForm.validateFields().then((value: any) => {
      value.start_time = value.time[0].valueOf() / 1000;
      value.end_time = value.time[1].valueOf() / 1000;
      let formData = new FormData();
      fileList.forEach((file: any) => {
        console.log(file);
        formData.append('file', file.originFileObj);
      });

      for (const key in value) {
        if (key !== 'file') {
          formData.append(key, value[key]);
        }
      }
      if (title === '新增数据源') {
        addHiveData(formData).then(res => {
          if (res.flag) {
            setIsModalOpen(false);
            message.success('操作成功');
            taskForm.resetFields();
            searchList();
          } else {
            message.error(res.message);
          }
        });
      } else if (title === '编辑数据源') {
        editHiveData(id, formData).then(res => {
          if (res.flag) {
            setIsModalOpen(false);
            message.success('操作成功');
            taskForm.resetFields();
            searchList();
          } else {
            message.error(res.message);
          }
        });
      } else {
        setIsModalOpen(false);
      }
    });
  };
  const handleDel = (id: string) => {
    deleteHiveData(id).then(res => {
      if (res.flag) {
        message.success('操作成功');
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const downloadTemplate = async () => {
    // const res = await getHiveTemplate();
    // console.log(21212,res)
    // const a = document.createElement('a');
    // a.href = URL.createObjectURL(res);
    // a.download = '情报模版.xlsx';
    // a.click();
    // URL.revokeObjectURL(a.href);
    console.log(1);
    const url = `${window.location.origin}/mica-api/api/v1/cert/hive_rule_template`;
    window.open(url);
  };
  const handleStart = (data: any) => {
    startDataSource(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleStop = (data: any) => {
    stopDataSource(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      setparams({ ...params, ...value });
    });
  };
  const deleteTask = (name: any) => {
    delDatasource({ name: name }).then(res => {
      if (res.flag) {
        onSubmit();
        message.success('操作成功');
      } else {
        message.success(res.message);
      }
    });
  };

  const executeTask = (id: any) => {
    setloading(true);
    settitle('数据源详情');
    getHiveDetail(id).then(res => {
      if (res.flag) {
        res.data.time = [moment(res.data.start_time * 1000), moment(res.data.end_time * 1000)];
        taskForm.resetFields();
        taskForm.setFieldsValue(res.data);
        setdetailData(res.data);
        setIsModalOpen(true);
      } else {
        message.error(res.message);
      }
    });
    setloading(false);
  };
  const handleAdd = () => {
    setdetailData({})
    taskForm.resetFields();
    settitle('新增数据源');
    setIsModalOpen(true);
  };
  const searchList = () => {
    let value = { ...params };
    getHiveListData(value).then(res => {
      if (res.flag) {
        setloading(false);
        settotal(res.data.total);
        setTableData(res.data.cols);
      } else {
        message.error(res.message);
      }
    });
  };

  const formItemLayout = {
    labelCol: {
      span: 4, // * ≥576px
    },
    wrapperCol: {},
  };
  const uploadChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };
  const props = {
    onRemove: (file: any) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file: any) => {
      setFileList([...fileList, file]);
      return false;
    },
    fileList,
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form {...formItemLayout} layout="inline" initialValues={params} form={form} className="!flex gap-3 !mt-5">
            <Row>
              <Col span={8}>
                <Form.Item label="数据源名称:" name="source_name">
                  <Input />
                </Form.Item>
              </Col>
              <Col offset={12} span={4}>
                <div style={{ textAlign: 'right', padding: 10 }}>
                  <Button
                    style={{ margin: '0 10px' }}
                    className="searchBtn"
                    type="primary"
                    onClick={onSubmit}
                    icon={<SearchOutlined />}
                  >
                    搜索
                  </Button>
                  <Button style={{ color: '#1890ff' }} type="dashed" onClick={handleAdd}>
                    新建
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <Spin spinning={loading}>
        <Table dataSource={tableData} columns={columns} pagination={pagination} />
      </Spin>
      <Modal destroyOnClose={true} width={800} title={title} visible={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
        <Form preserve={false} disabled={title === '数据源详情'} initialValues={detailData} form={taskForm}>
          <Form.Item name="source_name" label="数据源名称" rules={[{ required: true, message: '请填写!' }]}>
            <Input />
          </Form.Item>

          <Form.Item name="data_types" label="数据类型" rules={[{ required: true, message: '请选择!' }]}>
            <Select mode="multiple" allowClear>
              {data_types.map(item => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="time" label="起止时间" rules={[{ required: true, message: '请填写!' }]}>
            <DatePicker.RangePicker
              allowClear
              className="w-100%"
              ranges={{
                最近1分钟: [moment(new Date()).add(-1, 'minute'), moment()],
                最近5分钟: [moment(new Date()).add(-5, 'minute'), moment()],
                最近30分钟: [moment(new Date()).add(-30, 'minute'), moment()],
                最近1小时: [moment(new Date()).add(-1, 'hour'), moment()],
                最近4小时: [moment(new Date()).add(-4, 'hour'), moment()],
                最近12小时: [moment(new Date()).add(-12, 'hour'), moment()],
                最近1天: [moment(new Date()).add(-1, 'day'), moment()],
              }}
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>

          <Form.Item name="file" label="筛选规则" rules={title === '新增数据源' && [{ required: true, message: '请选择!' }]}>
            <Upload {...props} maxCount={1} onChange={uploadChange} accept=".xls, .xlsx">
              <Button type="primary" ghost icon={<UploadOutlined />}>
                选择导入的规则文件
              </Button>
            </Upload>
          </Form.Item>
          <Form.Item>
            <Button onClick={downloadTemplate} type="link">
              模板下载
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default DataSource;
