import React, { useEffect, useState } from 'react';
import { get } from 'lodash';
import s from './style.less';
import cn from 'classnames';

interface FileListProps {
  data?: any;
}
const FileNum = ({ data }: FileListProps) => {
  console.log('[ data ]-10', data);
  const [item, setItem] = useState([
    { text: '文件检测报告统计', num: 0 },
    { text: '安全文件数', num: 0 },
    { text: '低危文件', num: 0 },
    { text: '中危文件', num: 0 },
    { text: '高危及危急文件数', num: 0 },
  ]);
  useEffect(() => {
    if (!isNaN(get(data, 'report_total'))) {
      setItem([
        { text: '文件检测报告统计', num: data.report_total },
        { text: '安全文件数', num: data.safe_total || 0 },
        { text: '低危文件', num: data.low_risk || 0 },
        { text: '中危文件', num: data.middle_risk || 0 },
        { text: '高危及危急文件数', num: data.high_risk || 0 },
      ]);
    }
  }, [data]);
  return (
    <div className={cn(s.fileNum)}>
      {item.map(e => (
        <div className="bg-d0 p-1">
          <p className="c-t4">{e.text}</p>
          <div className={s.numBox}>
            <p className={s.num}>{e.num}</p>
            <p>数量</p>
          </div>
        </div>
      ))}
    </div>
  );
};
export default FileNum;
