import json
from datetime import timed<PERSON>ta
import hashlib
import requests

from config.config import Ndr<PERSON>og
from utils.database import MongoDB, get_es_client
from utils.es_function import *
from utils.es_template.get_es_template import ES_Template
from utils.logger import get_ndr_logger
from utils.ndr_base import NdrResource

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class Basic(NdrResource):

    def __init__(self):
        self.user_id = ""
        super(Basic, self).__init__()

    def get_current_user_id(self, request):
        """获取用户ID信息"""
        box_headers = dict(request.headers)
        box_headers.pop('From-Web', None)
        box_headers.pop('Content-Type', None)
        rst = requests.get("http://boss-api-v2-prd:7001/api/user/auth", headers=box_headers, timeout=3)
        ret_data = json.loads(rst.text)
        self.user_id = ret_data["data"]["id"]

    @staticmethod
    def get_hash_file_name(pcap_name):
        return hashlib.md5(pcap_name.encode(encoding='utf-8')).hexdigest()
