create table if not exists hive.apt_conn(
    create_time  DateTime,
    update_id  String,
    c_netnum  Int32,
    c_ip  IPv4,
    c_flowid  String,
    c_src_ipv4  IPv4,
    c_src_ipv6  IPv6,
    c_src_port  Int32,
    c_s_tunnel_ip  IPv4,
    c_s_tunnel_port  Int32,
    c_dest_ipv4  IPv4,
    c_dest_ipv6  IPv6,
    c_dest_port  Int32,
    c_d_tunnel_ip  IPv4,
    c_d_tunnel_port  Int32,
    c_packet_group  Int32,
    c_proto_type  Int32,
    c_connect_status  Int32,
    c_direct  Int32,
    c_server_dir  Int32,
    c_up_packets  Int64,
    c_up_bytes  Int64,
    c_down_packets  Int64,
    c_down_bytes  Int64,
    c_c2s_packet_jitter  Int32,
    c_s2c_packet_jitter  Int32,
    c_log_time  DateTime,
    c_app_type  String,
    c_stream_time  DateTime,
    c_hostr  String,
    c_s_boundary  Int64,
    c_s_region  Int64,
    c_s_city  Int64,
    c_s_district  Int64,
    c_s_operators  Int64,
    c_s_owner  String,
    c_d_boundary  Int64,
    c_d_region  Int64,
    c_d_city  Int64,
    c_d_district  Int64,
    c_d_operators  Int64,
    c_d_owner  String
  ) ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(create_time)
    ORDER BY update_id
    SETTINGS index_granularity=8192;

create table if not exists hive.apt_url(
    create_time  DateTime,
    update_id  String,
    c_netnum  Int32,
    c_ip  IPv4,
    c_time  DateTime,
    c_flowid  String,
    c_src_ipv4  IPv4,
    c_src_ipv6  IPv6,
    c_src_port  Int32,
    c_s_tunnel_ip  IPv4,
    c_s_tunnel_port  Int32,
    c_dest_ipv4  IPv4,
    c_dest_ipv6  IPv6,
    c_dest_port  Int32,
    c_d_tunnel_ip  IPv4,
    c_d_tunnel_port  Int32,
    c_proto_type  Int64,
    c_hostr  String,
    c_uri  String,
    c_cookie  String,
    c_ua  String,
    c_method  String,
    c_response_code  Int32,
    c_content_length  Int32,
    c_content_type  String,
    c_accept  String,
    c_accept_encoding  String,
    c_location  String,
    c_referer  String,
    c_filetype  String,
    c_filename  String,
    c_s_boundary  Int64,
    c_s_region  Int64,
    c_s_city  Int64,
    c_s_district  Int64,
    c_s_operators  Int64,
    c_s_owner  String,
    c_d_boundary  Int64,
    c_d_region  Int64,
    c_d_city  Int64,
    c_d_district  Int64,
    c_d_operators  Int64,
    c_d_owner  String,
    c_msisdn  String,
    c_imsi  String,
    c_imei  String,
    c_meid  String,
    c_uli  String,
    c_bsid  String,
    c_apn  String,
    c_req_body  String,
    c_res_bod  String
  ) ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(create_time)
    ORDER BY update_id
    SETTINGS index_granularity=8192;

create table if not exists hive.apt_tls(
    create_time  DateTime,
    update_id  String,
    c_netnum  Int32,
    c_ip  IPv4,
    c_time  DateTime,
    c_flowid  String,
    c_src_ipv4  IPv4,
    c_src_ipv6  IPv6,
    c_src_port  Int32,
    c_s_tunnel_ip  IPv4,
    c_s_tunnel_port  Int32,
    c_dest_ipv4  IPv4,
    c_dest_ipv6  IPv6,
    c_dest_port  Int32,
    c_d_tunnel_ip  IPv4,
    c_d_tunnel_port  Int32,
    c_sni  String,
    c_version  String,
    c_certificate  String,
    c_s_boundary  Int64,
    c_s_region  Int64,
    c_s_city  Int64,
    c_s_district  Int64,
    c_s_operators  Int64,
    c_s_owner  String,
    c_d_boundary  Int64,
    c_d_region  Int64,
    c_d_city  Int64,
    c_d_district  Int64,
    c_d_operators  Int64,
    c_d_owner  String
  ) ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(create_time)
    ORDER BY update_id
    SETTINGS index_granularity=8192;

create table if not exists hive.apt_dns(
    create_time  DateTime,
    update_id  String,
    c_dnsid  Int32,
    c_ipversion  Int32,
    c_sip  IPv4,
    c_dip  IPv4,
    c_sipv6  IPv6,
    c_dipv6  IPv6,
    c_sport  Int32,
    c_domain  String,
    c_type  String,
    c_scount  Int32,
    c_srate  Int32,
    c_dir  Int32,
    c_rcode  String,
    c_aa  Int32,
    c_rd  Int32,
    c_qlen  Int32,
    c_rlen  Int32,
    C_rrname  String,
    c_rrtype  String,
    c_value  String,
    c_ttl  Int64,
    c_avalue  String,
    c_time  Datetime,
    c_province  String,
    c_city  String,
    c_isp  String,
    c_pcode  String,
    c_nodeip  IPv4
  ) ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(create_time)
    ORDER BY update_id
    SETTINGS index_granularity=8192;

create table if not exists hive.apt_ljc(
    create_time  DateTime,
    update_id  String,
    c_rip  IPv4,
    c_sip  IPv4,
    c_dip  IPv4,
    c_nip  IPv4,
    c_input  Int32,
    c_output  Int32,
    c_packets  Int32,
    c_bytes  Int32,
    c_load_time  Datetime,
    c_start_time  Datetime,
    c_end_time  Datetime,
    c_sport  Int32,
    c_dport  Int32,
    c_flags  Int32,
    c_proto  Int32,
    c_tos  Int32,
    c_sas  Int32,
    c_das  Int32,
    c_src  Int32,
    c_drc  Int32,
    c_province  Int32,
    c_operator  Int32,
    c_rid  Int32,
    c_spc  Int32,
    c_dpc  Int32,
    c_scc  Int32,
    c_dcc  Int32,
    c_sop  Int32,
    c_dop  Int32
  ) ENGINE = MergeTree()
    PARTITION BY toYYYYMMDD(create_time)
    ORDER BY update_id
    SETTINGS index_granularity=8192;