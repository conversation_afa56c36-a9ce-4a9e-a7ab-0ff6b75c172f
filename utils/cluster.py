#!/usr/bin/env python3
# coding:utf-8

import os
import json
from celery_tasks import app

cluster = True
CLUSTER_CFG = "/etc/ksp/cluster/cluster.json"

def read_from_json_file(file_path):
    if not os.path.isfile(file_path):
        return {}
    with open(file_path, 'r') as f:
        content = f.read()
    if not content.strip():
        return {}
    content_json = json.loads(content)
    return content_json

def cluster_set_mode(mode:bool):
    global cluster
    cluster = mode

def cluster_get_node_name():
    if not cluster:
        return ""

    cluster_cfg = read_from_json_file(CLUSTER_CFG)
    return cluster_cfg.get('node_name', '')

def cluster_get_node_ip():
    if not cluster:
        return ""

    cluster_cfg = read_from_json_file(CLUSTER_CFG)
    return cluster_cfg.get('node_ip', '')

def cluster_add_node(node_info):
    if not cluster:
        return

    r = app.send_task('host_celery.cluster.add_node', args=[node_info], queue='host_task', routing_key='host_task')
    return r.get(timeout=5)

def cluster_delete_node(node_ip):
    if not cluster:
        return

    r = app.send_task('host_celery.cluster.delete_node', args=[node_ip], queue='host_task', routing_key='host_task')
    return r.get(timeout=5)

def cluster_set_mgmt_node_ip(node_ip):
    if not cluster:
        return

    r = app.send_task('host_celery.cluster.set_mgmt_node_ip', args=[node_ip], queue='host_task', routing_key='host_task')
    return r.get(timeout=5)

def cluster_sync_file(src_file_path, dest_file_path=None):
    if not cluster:
        return

    if not dest_file_path:
        dest_file_path = src_file_path

    app.send_task('host_celery.cluster.sync_file', args=[src_file_path, dest_file_path], queue='host_task', routing_key='host_task')

def cluster_read_file(node_ip, src_file_path, dest_file_path=None):
    if not cluster:
        return

    if not dest_file_path:
        dest_file_path = src_file_path

    r = app.send_task('host_celery.cluster.read_file_by_node', args=[node_ip, src_file_path, dest_file_path], queue='host_task', routing_key='host_task')
    r.get(timeout=60)


def cluster_remove_file(file_path):
    if not cluster:
        return

    app.send_task('host_celery.cluster.remove_file', args=[file_path], queue='host_task', routing_key='host_task')

def cluster_request_pcap(node_ip, query_str, file_path, url_list):
    if not cluster:
        return

    r = app.send_task('host_celery.cluster.request_pcap', args=[node_ip, query_str, file_path, url_list], queue='host_task', routing_key='host_task')
    r.get(timeout=60)


# 先同步文件，然后通知hdp特征升级
def cluster_hdp_feature_upgrade(path):
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_feature_upgrade', args=[path], queue='host_task', routing_key='host_task')


# 先通知hdp删除sid_list的特征，然后同步特征文件，最后通知hdp添加特征
def cluster_hdp_feature_modify(sid_list: list, rule_path, rule_info_path):
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_feature_modify', args=[sid_list, rule_path, rule_info_path], queue='host_task', routing_key='host_task')


def cluster_hdp_feature_operate(sid_list: list):
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_feature_operate', args=[sid_list], queue='host_task', routing_key='host_task')

# 先同步文件，然后通知hdp添加特征
def cluster_hdp_feature_add(rule_path, rule_info_path):
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_feature_add', args=[rule_path, rule_info_path], queue='host_task', routing_key='host_task')


def cluster_hdp_set_file_protocol(file_app_cfg, file_app_list: list):
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_set_file_protocol', args=[file_app_cfg, file_app_list], queue='host_task', routing_key='host_task')

def cluster_hdp_set_file_type(data: int):
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_set_file_type', args=[data], queue='host_task', routing_key='host_task')

def cluster_hdp_set_file_size(min_size: int, max_size: int):
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_set_file_size', args=[min_size, max_size], queue='host_task', routing_key='host_task')

def cluster_sync_file_and_restart_hdp(file_path):
    if not cluster:
        return

    app.send_task('host_celery.cluster.sync_file_and_restart_hdp', args=[file_path], queue='host_task', routing_key='host_task')

def cluster_hdp_update_portmask(node_name, port_mask):
    if not cluster:
        return

    r = app.send_task('host_celery.cluster.hdp_update_portmask', args=[node_name, port_mask], queue='host_task', routing_key='host_task').get(timeout=5)
    return r

def cluster_hdp_restart():
    if not cluster:
        return

    app.send_task('host_celery.cluster.hdp_restart', args=[], queue='host_task', routing_key='host_task')
