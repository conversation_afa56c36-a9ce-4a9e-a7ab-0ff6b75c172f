#!/usr/bin/env python3
# coding:utf-8

import os
import yaml
import json
import hashlib

def get_conf(cfg_file):
    if not os.path.isfile(cfg_file):
        return None
    with open(cfg_file, 'r') as f:
        conf = yaml.load(f, Loader=yaml.SafeLoader)
    return conf

def read_from_json_file(file_path):
    if not os.path.isfile(file_path):
        return {}
    with open(file_path, 'r') as f:
        content = f.read()
    if not content.strip():
        return {}
    content_json = json.loads(content)
    return content_json

def write_to_json_file(file_path, content_json):
    with open(file_path, 'w') as fp:
        fp.write(json.dumps(content_json, ensure_ascii=False, sort_keys=True))
        fp.flush()


def get_json_file(file_path, key, default=None):
    data = read_from_json_file(file_path)
    return data.get(key, default)

def update_json_file(file_path, key, value):
    contents = read_from_json_file(file_path)
    contents[key] = value
    write_to_json_file(file_path, contents)

def is_safe_path(file_path):
    if file_path.startswith('/var'):
        return True
    if file_path.startswith('/opt'):
        return True
    return False

def checksum(filename, hash_factory=hashlib.md5, chunk_num_blocks=128):
    h = hash_factory()
    with open(filename,'rb') as f:
        for chunk in iter(lambda: f.read(chunk_num_blocks*h.block_size), b''):
            h.update(chunk)
    return h.digest()