.fileNum {
  display: grid;
  grid-template-rows: 8rem;
  grid-template-areas:
    'a a'
    'b c';
  grid-gap: 0.5rem;
  height: 22rem;
  p{
    margin: 0;
  }
  &>div:nth-child(1){
    grid-area: a;
  }
  &>div:nth-child(2){
    grid-area: b;
  }
  &>div:nth-child(3){
    grid-area: c;
  }
  .numBox{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    .num{
      font-size: 2rem;
      font-weight: bold;
    }
  }
}
.fileList{
  p{
    margin-bottom: 0;
  }
  :global{
    .ant-table-thead > tr > th,.ant-table-tbody > tr>td  {
      background: #fff;
      color: #686c78;
      padding: 6px 12px;
      border-bottom: 1px solid #f0f0f0;
     }
     .ant-table-tbody>tr.ant-table-row:hover>td, .ant-table-tbody>tr>td.ant-table-cell-row-hover{
      background: none !important;
    }
    .ant-empty{
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
.FileEcharts{
  background: #fff;
  position: relative;
  &>div{
    display: flex;
    align-items: center;
    justify-content: center;
  }

}
.fileWord{
  min-height: 300px;
}
:global{
  .ant-empty{
    color: rgba(0, 0, 0, 0.25);
  }
}