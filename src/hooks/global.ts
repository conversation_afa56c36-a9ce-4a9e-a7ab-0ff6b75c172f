import userStore from '@/store/user';
import globalStore from '@/store/global';
import { useModel, history } from 'umi';
import { isDev } from '@/utils/utils';
export function reloadAuthInfo() {
  userStore.dispatch('userInfo');
  globalStore.dispatch('getUserModule');
}

export function useHistory() {
  let env = isDev();
  if (!env) {
    const { GlobalHistory } = useModel('@@qiankunStateFromMaster');
    if (window.__POWERED_BY_QIANKUN__) {
      return GlobalHistory || history;
    }
  }

  return history;
}
