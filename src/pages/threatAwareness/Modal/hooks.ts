import { createScene, deleteScene, getSceneNameList, updateScene } from "@/services/threatAwareness";
import { message } from "antd";
import { useEffect, useState } from "react";

export function useSceneList() {
  const [loading, setLoading] = useState(false)
  const [list, setList] = useState([])
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
  })
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [total, setTotal] = useState(0)

  const getScene = async () => {
    try {
      setLoading(true);
      const { data: { list, total }, flag } = await getSceneNameList(pagination)
      if (flag) {
        setList(list);
        setTotal(total)
      }
    } catch (error: any) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  }

  const sceneOperate = async (values: object, isEdit = false) => {
    const fetchMethod = isEdit ? updateScene : createScene
    try {
      setConfirmLoading(true)
      const { flag, message: msg } = await fetchMethod(values)
      if (flag) {
        message.success(msg);
        setPagination(prev => ({
          ...prev,
          page: 1
        }))
      } else {
        throw new Error(msg)
      }
      return flag
    } catch (error: any) {
      message.error(error.message);
    } finally {
      setConfirmLoading(false)
    }
  }

  const sceneDelete = async (ids: string[]) => {
    try {
      setConfirmLoading(true)
      const { flag, message: msg } = await deleteScene({ ids })
      if (flag) {
        message.success(msg);
        setPagination(prev => ({
          ...prev,
          page: 1
        }))
      } else {
        throw new Error(msg)
      }
      return flag
    } catch (error: any) {
      message.error(error.message);
    } finally {
      setConfirmLoading(false)
    }
  }

  useEffect(() => {
    getScene();
  }, [pagination])


  return {
    list,
    loading,
    confirmLoading,
    total,
    pagination,
    setPagination,
    sceneOperate,
    sceneDelete,
  }

}