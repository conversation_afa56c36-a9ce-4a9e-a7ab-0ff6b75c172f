# -*- coding: utf-8 -*-
# @Time    : 2019-08-27 13:31
# <AUTHOR> <PERSON>
# @File    : es_function.py
# @Software: PyCharm
import re
import time

from utils.utils import en_to_cn_country
from dateutil import parser


def get_month_list(start_day, end_day):
    """计算开始时间和结束时间之间的月份"""
    months = (end_day.year - start_day.year) * 12 + end_day.month - start_day.month
    month_range = ['%s%s' % (start_day.year + mon // 12, mon % 12 + 1 if mon % 12 + 1 > 9 else "0" + str(mon % 12 + 1))
                   for mon in range(start_day.month - 1, start_day.month + months)]
    return month_range


def get_es_index(index_type, start_time, end_time):
    """获取es索引"""
    start_time = parser.parse(time.ctime(int(start_time) // 1000)).date()
    end_time = parser.parse(time.ctime(int(end_time) // 1000)).date()
    # 求当前时间的es索引 (1-7)=1 (8-14)=2 (15-21)=3 (21<)=4
    start_num = (start_time.day + 6) // 7 if (start_time.day + 6) // 7 < 5 else 4
    end_num = (end_time.day + 6) // 7 if (end_time.day + 6) // 7 < 5 else 4
    start_index = '%s-%s%s%s' % (index_type, start_time.year,
                                 start_time.month if start_time.month > 9 else "0" + str(start_time.month),
                                 "0" + str(start_num))
    end_index = '%s-%s%s%s' % (index_type, end_time.year,
                               end_time.month if end_time.month > 9 else "0" + str(end_time.month),
                               "0" + str(end_num))
    month_list = get_month_list(start_time, end_time)
    index_list = []
    for i in month_list:
        for j in range(1, 5):
            index_list.append(index_type + "-" + i + "0" + str(j))
    # 获取开始-结束之间的所有index
    index_list = index_list[index_list.index(start_index):index_list.index(end_index) + 1]
    return index_list


def get_es_template(es_template, kwargs):
    if 'startTime' in kwargs and 'stopTime' in kwargs:
        start_time = int(kwargs["startTime"])
        stop_time = int(kwargs["stopTime"])
        time_range = get_range('observedTime', start_time, stop_time)
        es_template["query"]["bool"]["must"].append(time_range)
    if kwargs["detail"]:
        return es_template  # 详情查询不在需要后续查询条件

    filter_arg = kwargs["filter_arg"]
    # 单个filter结构为：{'item': 'src_ip', 'logic': 'notEqual', 'value': ['*******']}
    for filter_object in filter_arg:
        item = filter_object["item"]
        logic = filter_object["logic"]
        value_list = filter_object["value"]
        if logic == "equal":
            query = get_terms(item, value_list)
            es_template["query"]["bool"]["must"].append(query)
        elif logic == "notEqual":
            query = get_terms(item, value_list)
            es_template["query"]["bool"]["must_not"].append(query)
        elif logic == "contain":
            for item_value in value_list:
                query = get_contain(item, item_value)
                es_template["query"]["bool"]["must"].append(query)
        else:
            for item_value in value_list:
                query = get_contain(item, item_value)
                es_template["query"]["bool"]["must_not"].append(query)
    order = kwargs["order"]
    page = int(kwargs["page"])
    page_size = int(kwargs["pageSize"])
    sort = kwargs["sort"]
    group = kwargs["groupKey"]
    if not group:
        es_template.pop("aggs")
        es_template['from'] = (page - 1) * page_size
        es_template['size'] = page_size
        es_template['sort'] = []
        es_template['sort'].append({sort: {"order": order}})
    else:
        es_template['aggs']['group_id']['aggs']['doc_ids']['top_hits']['sort'].append({sort: {"order": order}})
        es_template['aggs']['group_id']['aggs']['sort_id']['max']['field'] = sort
        es_template['aggs']['group_id']['aggs']['bucket_truncate']['bucket_sort']['sort'].append(
            {"sort_id": {"order": order}})
        if group != "uniqueId":
            group = "flow." + group
            es_template['aggs']['group_id']['aggs']['doc_ids']['top_hits']['size'] = 1000
        group = group + ".keyword"
        es_template['aggs']['group_id']['terms']['field'] = group
        es_template['aggs']['_count']['cardinality']['field'] = group
        es_template['aggs']['group_id']['aggs']['bucket_truncate']['bucket_sort']['from'] = (page - 1) * page_size
        es_template['aggs']['group_id']['aggs']['bucket_truncate']['bucket_sort']['size'] = page_size
    return es_template


def es_query_data_format(data):
    """格式化vulName查询语句"""
    words = data.strip().lower()
    re_word_en = re.findall(r'[A-Za-z\d]+', words)
    re_word_cn = re.findall(r'[\u4e00-\u9fa5]+', words)
    result_en = ["*{0}*".format(word) for word in re_word_en]
    result = " ".join(result_en + re_word_cn)
    return result


def get_range(field, start, stop):
    query = {
        "range": {
            field: {
                "gte": start,
                "lte": stop
            }
        }
    }
    return query


def get_terms(key, value):
    if key == "proto" or key == "src_ip" or key == "dst_ip":
        key = "flow." + key
    if key == "sid":
        query = {
            "terms": {key: value}
        }
    else:
        query = {
            "terms": {key + ".keyword": value}
        }
    return query


def get_contain(key, value):
    if key == "proto" or key == "src_ip" or key == "dst_ip":
        key = "flow." + key
    query = {
        "query_string": {
            "query": es_query_data_format(value),
            "default_field": key,
            "default_operator": "AND"
        }
    }
    return query


def get_mbinfo(value):
    query = {
        "bool": {
            "should": [{"term": {"srcIpMbInfo.from.keyword": value}},
                       {"term": {"dstIpMbInfo.from.keyword": value}}]
        }
    }
    return query


def get_proto_match():
    """
    考虑到当前前段分页直接利用的ES的(from,size)分页，为了避免前端分页显示问题，
    所以直接采用增加ES查询过滤条件，而不是在原来的查询结果后面通过代码过滤不要的结果
    """
    query = {
        "bool": {
            "must_not": {
                "terms": {
                    "proto.keyword": ["http", "ssl"]
                }
            },
            "must": {
                "term": {
                    "iocInfo.info.keyword": "GAC_IOC"
                }
            }
        }
    }
    return query


def get_country_term(cn_country):
    # 中文转英文
    cn_to_en = dict(zip(en_to_cn_country.values(), en_to_cn_country.keys()))
    en_country = cn_to_en.get(cn_country)
    terms = {
        "term": {
            "attackIpGeoip.country_name.keyword": en_country
        }
    }
    return terms


def get_fuzzy_match(query_data, field):
    query = {
        "query_string": {
            "query": es_query_data_format(query_data),
            "default_field": field,
            # "analyzer": "ik_max_word",
            # "analyze_wildcard": "true",
            "default_operator": "and"
        }
    }
    return query


def get_term_match(query_data, field):
    query = {
        "term": {
            field: query_data
        }
    }
    return query


def get_ip_match(ip):
    query = {
        "multi_match": {
            "query": ip,
            "fields": [
                "attackIp",
                "victimIp"
            ]
        }
    }
    return query


def get_country_match(country):
    query = {
        "multi_match": {
            "query": country,
            "fields": [
                "attackIpGeoip.country_name.keyword",
                "victimIpGeoip.country_name.keyword"
            ]
        }
    }
    return query


def get_sort(field, order):
    query = {
        field: {
            "order": order
        }
    }
    return query


def get_aggs_terms(field, size=0):
    aggs = {
        "terms": {
            "field": field
        }
    }
    if size:
        aggs["terms"]["size"] = size
    return aggs


def get_aggs_cardinality(field):
    aggs = {
        "cardinality": {
            "field": field
        }
    }
    return aggs


def get_threat_level(num=None, level=None):
    level2num = {
        "Low": 0,
        "Medium": 1,
        "High": 2,
    }
    num2level = {
        0: "Low",
        1: "Medium",
        2: "High"
    }
    try:
        if num in num2level.keys():
            return num2level[num]
        elif level in level2num.keys():
            return level2num[level]
    except Exception as e:
        print(e)
        return None


def get_file_type_match(status):
    file_match = {"match": {"filetype": status}}
    return file_match


def get_type_match(status):
    type_match = {"match": {"type": status}}
    return type_match


def get_confirm_match(status):
    type_match = {"match": {"confirm": status}}
    return type_match
