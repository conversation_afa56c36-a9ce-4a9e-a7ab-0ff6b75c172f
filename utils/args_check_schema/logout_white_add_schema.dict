{
    "type": "object",
    "required": ["target", "type"],
    "properties": {
        "target": {
            "type": "array",
            "items": {
                "type": "string",
                "default": ""
              },
              "default": []
        },
        "type": {
            "type": "string",
            "enum": ["ip", "domain"]
        },
        "tag": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,128}$"
        }
    }
}