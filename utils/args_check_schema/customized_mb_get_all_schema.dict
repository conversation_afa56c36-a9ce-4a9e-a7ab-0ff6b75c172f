{
    "type": "object",
    "required": [],
    "properties": {
        "ip": {
            "type": "string",
            "default": "",
            "pattern": r"^.{0,128}$"
        },
        "assetsName": {
            "type": "string",
            "default": ""
        },
        "assetsType": {
            "type": "string",
            "default": ""
        },
        "organisation": {
            "type": "string",
            "default": ""
        },
         "page": {
            "type": "string",
            "default": "1",
            "pattern": r"^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|10000)$"
        },
        "pageSize": {
            "type": "string",
            "default": "10",
            "pattern": r"^([1-9]|[1-9][0-9]|100)$"
        }
    }
}