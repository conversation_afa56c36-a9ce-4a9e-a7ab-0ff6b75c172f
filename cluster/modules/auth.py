#!/usr/bin/env python3
# coding:utf-8

import os
import hashlib
import ssl

from rpyc.core import brine
from rpyc.core.stream import SocketStream
from rpyc.utils.factory import connect_stream
from rpyc.utils.authenticators import AuthenticationError, SSLAuthenticator

from ksp import config
from cluster.utils import license
from cluster.utils.logger import get_logger
from cluster.service.service import KSPService


RANDOM_SALT_LEN = 16
logger = get_logger(__name__)

# 使用 license 的方法来验证 client 的方式废弃，改为 client/server 双向预共享密钥的方式。
class KSPAuthenticator:
    def __init__(self):
        self.device_id = license.get_device_id()

    def __call__(self, sock):
        data = sock.recv(512)
        licenses = brine.load(data)
        if self.device_id in licenses:
            return sock, None
        raise AuthenticationError


class MasterAuthenticator:
    def __init__(self):
        self.in_file_path = config.get('license.file')
        self.license = license.get_license(self.in_file_path)

    def connect(self, host, port):
        s = SocketStream.connect(host, port, timeout=1, attempts=2)
        license = self.license.get('device_ids', objname='license')
        s.write(brine.dump(tuple(license)))
        s.poll(1)
        return connect_stream(s, KSPService, {'allow_public_attrs': True, "allow_pickle": True, "sync_request_timeout": None})

## *************************************************************************** ##
## 新增基于SSL通道进行预共享密钥的双向验证：
## 1. psk 是在 rpyc 客户端和服务端 两侧预先配置的一个公共密钥
## 2. 当 SSL 通道建立起来后，客户端和服务端均向对端发送一个固定长度的字节流
##    --  salt + sha1(psk + salt)。salt 每次随机生成。
## 3. 客户端和服务端读取对方发来的字节流，提取 salt，用自己的 psk 进行相同的 hash 运算，
##    判断本地计算的摘要和对端发送的摘要是否相同，以此确定双方 psk 是否一致。
## 4. 只有 psk 一致后，才允许通信信道畅通，否则关闭该连接。
## 5. KSSLAuthenticator 是带有预共享密钥验证的 SSL 认证子，用于 rpyc 服务端
## 6. KSSLConnector 是带有预共享密钥验证的 SSL 连接子，用于 rpyc 客户端
## *************************************************************************** ##

def two_way_authenticate_with_psk(psk, sock):
    psk = bytes(psk, 'ascii')
    salt = os.urandom(RANDOM_SALT_LEN)
    dig = hashlib.sha1(psk + salt).digest()
    sdata = salt + dig
    datalen = len(sdata)
    try:
        # 发送本端数据给对方
        while sdata:
            count = sock.send(sdata)
            sdata = sdata[count:]
        # 接收对方数据(这里有一个假设，发送数据量和接收数据长度是相同的)
        # 计算摘要，验证与共享密钥是否相同
        rdata = sock.recv(datalen)
        rsalt = rdata[:RANDOM_SALT_LEN]
        rdig = rdata[RANDOM_SALT_LEN:]
        local_dig = hashlib.sha1(psk + rsalt).digest()
        if local_dig == rdig:
            return True
        else:
            return False
    except Exception as ex:
        raise ex
class KSSLAuthenticator(SSLAuthenticator):
    def __init__(self, keyfile, certfile, ca_certs=None, cert_reqs=None,
                 ssl_version=None, ciphers=None, psk="some-preshared-key"):
        super().__init__(keyfile, certfile, ca_certs, cert_reqs, ssl_version, ciphers)
        self.psk = psk

    def __call__(self, sock):
        s, cert = super().__call__(sock)
        try:
            ok = two_way_authenticate_with_psk(self.psk, s)
            if not ok:
                logger.error(f"Failed to auth peer {sock.getpeername()}.")
                raise AuthenticationError
        except Exception as ex:
            raise AuthenticationError
        return s, cert


class KSSLConnector():
    def __init__(self, psk="some-preshared-key"):
        self.psk = psk

    def kssl_connect(self, host, port, service, keyfile=None, certfile=None, ca_certs=None,
                cert_reqs=None, ssl_version=None, ciphers=None,
                config={}, ipv6=False, keepalive=False, verify_mode=None):
        """ 扩展了原来的 ssl_connect，在连接建立开始后，
            先进行一次双向的预共享密钥验证，然后再开始标准SSL流程

        creates an SSL-wrapped connection to the given host (encrypted and
        authenticated).

        :param host: the hostname to connect to
        :param port: the TCP port to use
        :param service: the local service to expose (defaults to Void)
        :param config: configuration dict
        :param ipv6: whether to create an IPv6 socket or an IPv4 one(defaults to ``False``)
        :param keepalive: whether to set TCP keepalive on the socket (defaults to ``False``)

        :param keyfile: see ``ssl.SSLContext.load_cert_chain``. May be ``None``
        :param certfile: see ``ssl.SSLContext.load_cert_chain``. May be ``None``
        :param ca_certs: see ``ssl.SSLContext.load_verify_locations``. May be ``None``
        :param cert_reqs: see ``ssl.SSLContext.verify_mode``. By default, if ``ca_cert`` is
                          specified, the requirement is set to ``CERT_REQUIRED``; otherwise
                          it is set to ``CERT_NONE``
        :param ssl_version: see ``ssl.SSLContext``. The default is defined by
                            ``ssl.create_default_context``
        :param ciphers: see ``ssl.SSLContext.set_ciphers``. May be ``None``. New in
                        Python 2.7/3.2
        :param verify_mode: see ``ssl.SSLContext.verify_mode``

        :returns: an RPyC connection
        """

        ssl_kwargs = {"server_side": False}
        if keyfile is not None:
            ssl_kwargs["keyfile"] = keyfile
        if certfile is not None:
            ssl_kwargs["certfile"] = certfile
        if verify_mode is not None:
            ssl_kwargs["cert_reqs"] = verify_mode
        else:
            ssl_kwargs["cert_reqs"] = ssl.CERT_NONE
        if ca_certs is not None:
            ssl_kwargs["ca_certs"] = ca_certs
            ssl_kwargs["cert_reqs"] = ssl.CERT_REQUIRED
        if cert_reqs is not None:
            ssl_kwargs["cert_reqs"] = cert_reqs
        #rpyc 4.1.5 不需要设置此参数
        # elif cert_reqs != ssl.CERT_NONE:
        #     ssl_kwargs["check_hostname"] = False
        if ssl_version is not None:
            ssl_kwargs["ssl_version"] = ssl_version
        if ciphers is not None:
            ssl_kwargs["ciphers"] = ciphers

        ssl_sockstream = SocketStream.ssl_connect(host, port, ssl_kwargs, ipv6=ipv6, keepalive=keepalive)
        ok = two_way_authenticate_with_psk(self.psk, ssl_sockstream.sock)
        if not ok:
            logger.error(f"Failed to auth peer {ssl_sockstream.sock.getpeername()}.")
            raise AuthenticationError
        return connect_stream(ssl_sockstream, service, config)
