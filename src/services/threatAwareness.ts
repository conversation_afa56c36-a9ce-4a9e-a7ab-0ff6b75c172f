import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

export type OverviewStaticParams = {
  start_time: number;
  end_time: number;
  sort_agg_field: string;
  status?: string;
  visit_direction?: number;
  all_host?: number
};

export type DisposeThreatParams = {
  type: string;
  ip_addr?: string;
  action: 'process' | 'restore';
}

export type SceneHaveReadParams = {
  value: boolean;
  uuid: string;
  start_time: string;
  end_time: string;
  terms: string,
  type?: string;
  ip?: string;
  action?: string;
}

export type SceneListParams = {
  name: string;
  is_read: number;
  page: number;
  pageSize: number;
}

export type SceneQueryParams = Pick<OverviewStaticParams, 'start_time' | 'end_time'> & SceneListParams

const apiVersion = 'v3';

// 风险/攻击者/风险主机统计信息获取
export const getOverviewStatic = (data: OverviewStaticParams) => {
  return request(`overview/model_aggregate${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// 威胁事件概览
export const getThreatEvents = (data: Pick<OverviewStaticParams, 'start_time' | 'end_time'>) => {
  return request(`overview/threat_event/get${parseQueryString(data)}`, {
    method: 'get',
    apiVersion,
  });
};

// 攻击阶段预览
export const getKillChain = (data: Pick<OverviewStaticParams, 'start_time' | 'end_time'>) => {
  return request(`overview/kill_chain/get${parseQueryString(data)}`, {
    method: 'get',
    apiVersion,
  });
};

// 处置威胁
export const disposeThreat = (data: DisposeThreatParams) => {
  return request('overview/read/update', {
    apiVersion,
    method: 'post',
    data,
  });
};

// 获取场景
export const getSceneNameList = (data: object) => {
  return request(`overview/scene_opt${parseQueryString(data)}`, {
    method: 'get',
    apiVersion,
  });
};

// 查询场景监测列表 
export const getSceneList = (data: SceneQueryParams) => {
  return request(`overview/scene_opt/search${parseQueryString(data)}`, {
    method: 'get',
    apiVersion,
  });
};

// 场景监测 已读
export const readSceneItem = (data: SceneHaveReadParams) => {
  return request('workbench/event/update', {
    apiVersion,
    data,
    method: 'post',
  });
};

// 创建场景
export const createScene = (data: object) => {
  return request('overview/scene_opt', {
    apiVersion,
    data,
    method: 'post',
  });
};

// 更新场景
export const updateScene = (data: object) => {
  return request('overview/scene_opt', {
    apiVersion,
    data,
    method: 'put',
  });
};

// 删除场景
export const deleteScene = (data: { ids: string[] }) => {
  return request(`overview/scene_opt${parseQueryString(data)}`, {
    apiVersion,
    method: 'delete',
  });
};

// 攻击国家
export const getAttackCountry = (data: Pick<OverviewStaticParams, 'start_time' | 'end_time'> & { sort: string }) => {
  return request(`overview/country/aggregate${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// 事件趋势
export const getEventTrends = (data: Pick<OverviewStaticParams, 'start_time' | 'end_time'>) => {
  return request(`overview/event/aggregate${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// 威胁名称
export const getThreatNames = (data: Pick<OverviewStaticParams, 'start_time' | 'end_time'> & { sort: string }) => {
  return request(`overview/threat_name/aggregate${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// 威胁等级
export const getThreatLevel = (data: Pick<OverviewStaticParams, 'start_time' | 'end_time'>) => {
  return request(`overview/threat_level/aggregate${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// 攻击方向
export const getAttackDirection = (data: Pick<OverviewStaticParams, 'start_time' | 'end_time'>) => {
  return request(`overview/visit_direction/aggregate${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// 忽略列表
export const getIgnoreList = (data: object) => {
  return request(`overview/ignore_opt${parseQueryString(data)}`, {
    apiVersion,
    method: 'get',
  });
};

// ip添加忽略
export const addIgnoreItem = (data: object) => {
  return request('overview/ignore_opt', {
    apiVersion,
    data,
    method: 'post',
  });
};

// 删除忽略列表项目
export const deleteIgnoreItems = (data: { ids: string[] }) => {
  return request(`overview/ignore_opt${parseQueryString(data)}`, {
    apiVersion,
    method: 'delete',
  });
};

// 常量选项
export const getConstOptions = () => {
  return request('overview/config/get', {
    apiVersion,
    method: 'get',
  });
};




