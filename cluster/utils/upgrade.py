#!/usr/bin/env python3
# coding:utf-8

import subprocess
import tempfile
import os
import stat
import shutil
import datetime

from ksp import config
from cluster.settings import CLUSTER_SYSUPDATE_SYNCFILE, CLUSTER_SCHE_CFG
from cluster.utils import license
from cluster.utils.logger import get_logger


logger = get_logger(__name__)


class Upgrade:

    sync_file = CLUSTER_SYSUPDATE_SYNCFILE
    update_pubkey_path = config.get('update.pubkey')

    def __init__(self):
        pass

    def sync_update_file(self, content):
        """同步更新清单"""
        try:
            with open(self.sync_file, 'wb') as sync_file:
                sync_file.write(content)
        except:
            return False
        return True

    def save_update_pubkey(self, content):
        """同步升级公钥清单"""
        try:
            with open(self.update_pubkey_path, 'wb') as sync_file:
                sync_file.write(content)
        except:
            return False
        return True

    def get_pkg_info_list_from_file(self, upgrade_type='default'):
        from ksp.update.lib import downloader
        from ksp.update.lib.version import CVersion

        pkg_info_list = []
        download = downloader.Downloader(upgrade_type)

        _, update_info_list = download.get_update_info_from_server_or_file(from_file=True)
        latest_version = download.get_latest_version()
        if not latest_version:
            logger.info('the upgrade package is up to date')
            return
        latest_version_obj = CVersion(latest_version)
        for update_info in update_info_list:
            update_file_name = update_info['name']
            remote_md5 = update_info['md5']
            remote_version = update_info['version']
            if download.can_sync(update_file_name, remote_md5, remote_version, latest_version_obj):
                update_file_path = download.get_update_file_path(update_file_name)
                pkg_info_list.append({
                    'update_file_name': update_file_name,
                    'update_file_path': update_file_path,
                    'remote_md5': remote_md5
                })
        return pkg_info_list

    def download_upgrade_file_from_master(self, conn, pkg_info, update_type):
        from ksp.update.lib import downloader

        update_file_name = pkg_info['update_file_name']
        update_file_path = pkg_info['update_file_path']
        remote_md5 = pkg_info['remote_md5']

        content = conn.root.download_upgrade_file(update_file_name, update_type)
        if not content:
            return
        prefix = 'cluster_update_'
        _, tmp_file_path = tempfile.mkstemp(prefix=prefix)
        with open(tmp_file_path, 'wb') as tmp_file:
            tmp_file.write(content)

        logger.info('tmp file path is %s', tmp_file_path)
        logger.info('target file path is %s', update_file_path)
        if os.path.isfile(tmp_file_path):
            local_md5 = downloader.md5sum(tmp_file_path)
            if remote_md5 == local_md5:
                shutil.move(tmp_file_path, update_file_path)
                os.chmod(update_file_path, stat.S_IROTH | stat.S_IRUSR | stat.S_IWUSR | stat.S_IWGRP | stat.S_IRGRP)
                logger.info('move to the target path success')
                return True
            else:
                logger.error(
                    'remote md5 is %s, local md5 is %s',
                    remote_md5, local_md5
                )

    def do_upgrade(self, pkg_path, update_type='default'):
        from ksp.update.lib.install import Installer

        pkg_installer = Installer(pkg_path, '/var/ksp/update/log/', update_type)
        with tempfile.TemporaryDirectory() as tmpdirname:
            try:
                pkg_installer.unpack(tmpdirname)
            except Exception:
                pkg_installer.cleanup()
                return 1, "", ""
            errorcode, stdout, stderr = pkg_installer.install()
            if errorcode != 0:
                pkg_installer.cleanup()
                return errorcode, stdout, stderr

        pkg_installer.cleanup()
        return errorcode, stdout, stderr

    def record_upgrade_log_start(self, conn, upgrade_mode, upgrade_type):
        device_id = license.get_device_id()
        upgrade_log = {
                "device_id": device_id,
                "started_at": self.get_utcnow_str(),

                "version_from": config.get('prod.version'),
                "version_to": "",
                "data_version_from": config.get('prod.data_version'),
                "data_version_to": "",

                "upgrade_mode": upgrade_mode,
                "upgrade_type": upgrade_type,

                "status": 'processing',
            }
        try:
            return conn.root.record_upgrade_log_start(upgrade_log)
        except Exception as ex:
            logger.error("Record upgrade log failed for device %s: %s" % (device_id, str(ex)))

    def record_upgrade_log_end(self, conn, upgrade_log):
        upgrade_log['ended_at'] = self.get_utcnow_str()
        upgrade_log['version_to'] = config.get('prod.version')
        upgrade_log['data_version_to'] = config.get('prod.data_version')
        try:
            return conn.root.record_upgrade_log_end(upgrade_log)
        except Exception as ex:
            logger.error('Record upgrade log failed %s' % str(ex))

    def get_utcnow_str(self):
        return datetime.datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

    def launch_update(self):
        """触发升级"""
        cmd = ['sudo',  'systemctl', 'start', 'ksp-update.service']
        try:
            subprocess.Popen(cmd)
        except:
            return False
        return True
