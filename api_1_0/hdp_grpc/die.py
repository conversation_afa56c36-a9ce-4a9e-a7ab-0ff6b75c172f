#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import grpc
import sys
import os
from utils.logger import get_ndr_logger
from config.config import GrpcCfg
from config.config import NdrLog
import protos_pb.die_pb2 as die_pb2
import protos_pb.die_pb2_grpc as die_pb2_grpc

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


# Grpc API #
def grpc_newssnlog_switch(enable):
    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = die_pb2_grpc.DIEServiceStub(channel)
        response = stub.NewSsnLogSwitch(die_pb2.DIENewSsnLogSwitchRequest(enable=enable))

        return response


def upgrade_iterfile(file_path, action, chunk_size=1024):
    metadata = die_pb2.Metadata(action=action, size=os.path.getsize(file_path))
    yield die_pb2.DIEFeatureRequest(metadata=metadata)
    with open(file_path, mode="rb") as f:
        while True:
            chunk = f.read(chunk_size)
            if chunk:
                entry_request = die_pb2.DIEFeatureRequest(file=chunk)
                yield entry_request
            else:  # The chunk was empty, which means we're at the end of the file
                return


def grpc_feature_upgrade(file_path):
    if not os.path.exists(file_path):
        LOG.error('GRPC ERROR: not exist file: ', file_path)
        return None

    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = die_pb2_grpc.DIEServiceStub(channel)
        response = stub.Feature(upgrade_iterfile(file_path, "upgrade"))

        return response


def grpc_feature_operate(sid_list: list):
    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = die_pb2_grpc.DIEServiceStub(channel)
        response = stub.OperateFeature(die_pb2.DIEOperateFeatureRequest(sid=sid_list, action="delete"))

        return response


def add_iterfile(rule_path, rule_info_path, action, chunk_size=1024):
    all_size = os.path.getsize(rule_path) + os.path.getsize(rule_info_path)
    metadata = die_pb2.Metadata(action=action, size=all_size)
    yield die_pb2.DIEAddFeatureRequest(metadata=metadata)
    with open(rule_path, mode="rb") as fp1, open(rule_info_path, mode="rb") as fp2:
        while True:
            chunk = fp1.read(chunk_size)
            if chunk:
                entry_request = die_pb2.DIEAddFeatureRequest(rule=chunk)
                yield entry_request
            else:  # The chunk was empty, which means we're at the end of the file
                break

        while True:
            chunk = fp2.read(chunk_size)
            if chunk:
                entry_request = die_pb2.DIEAddFeatureRequest(rule_info=chunk)
                yield entry_request
            else:  # The chunk was empty, which means we're at the end of the file
                return


def grpc_feature_add(rule_path, rule_info_path):
    if not os.path.exists(rule_path) or not os.path.exists(rule_info_path):
        LOG.error('GRPC ERROR: not exist file: %s or %s ' % (rule_path, rule_info_path))
        return None

    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = die_pb2_grpc.DIEServiceStub(channel)
        response = stub.AddFeature(add_iterfile(rule_path, rule_info_path, "add"))

        return response


def grpc_set_file_protocol(data: list):
    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = die_pb2_grpc.DIEServiceStub(channel)
        response = stub.FileRestoreProtocol(die_pb2.FileRestoreAppRequest(appid=data))

        return response


def grpc_set_file_type(data: int):
    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = die_pb2_grpc.DIEServiceStub(channel)
        response = stub.FileRestoreType(die_pb2.FileRestoreTypeRequest(type=data))

        return response


def grpc_set_file_size(min_size: int, max_size: int):
    with grpc.insecure_channel(GrpcCfg.RemoteAddr) as channel:
        stub = die_pb2_grpc.DIEServiceStub(channel)
        response = stub.FileRestoreSize(die_pb2.FileRestoreSizeRequest(min=min_size, max=max_size))

        return response
