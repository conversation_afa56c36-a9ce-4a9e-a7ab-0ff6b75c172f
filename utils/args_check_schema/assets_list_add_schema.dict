{
	"type": "object",
	"required": ["ip_addr"],
	"properties": {
		"ip_addr": {
			"type": "string",
			"format": "ipv4|ipv6"
		},
		"name": {
			"type": "string",
			"default": ""
		},
		"mac_addr": {
			"type": "string",
			"default": ""
		},
		"type": {
			"type": "string",
			"default": ""
		},
		"enable_port": {
			"type": "object",
			"default":{}
		},
		"assets_num": {
			"type": "string",
			"default": ""
		},
		"is_sercret": {
			"type": "boolean",
			"default": False,
			"enum": [True, False]
		},
		"is_importent": {
			"type": "boolean",
			"default": False,
			"enum": [True, False]
		},
		"location": {
			"type": "string",
			"default": ""
		},
		"country": {
			"type": "string",
			"default": ""
		},
		"country_code": {
			"type": "string",
			"default": ""
		},
		"department": {
			"type": "string",
			"default": ""
		},
		"industry": {
			"type": "string",
			"default": ""
		},
		"organisation": {
			"type": "string",
			"default": ""
		},
		"components": {
			"type": "string",
			"default": ""
		},
		"domain": {
			"type": "string",
			"default": ""
		},
		"other": {
			"type": "string",
			"default": ""
		},
		"person": {
			"type": "string",
			"default": ""
		},
		"os_info": {
			"type": "string",
			"default": ""
		},
		"labels": {
			"type": "string",
			"default": ""
		}
	}
}