#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by da<PERSON><PERSON>
""" 获取单个 model 告警详情 """
from flask import request

from api_1_0.back_event.get_model_list import format_es_temp
from utils.database import MongoDB
from utils.es_function import get_es_template
from utils.ndr_base import NdrResource
from utils.utils import flask_response
from utils.param_check import Validator, check_flask_args
from config.config import MODEL_INDEX
from utils.logger import get_ndr_logger

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class ModelGroupAll(NdrResource):
    """ 获取 model 数据详情 """

    def __init__(self):
        """初始化参数"""
        self.mongodb = MongoDB("ndr")
        super(ModelGroupAll, self).__init__(es_template="model_group_all")

    @check_flask_args(Validator("model_list"), request)
    def post(self, **kwargs):
        option_args = {
            "celeryId": kwargs["celeryId"],
            "modelName": kwargs["modelName"],
            "threatLevel": kwargs["threatLevel"],
            "killchain": kwargs["killchain"],
            "threatScore": kwargs["threatScore"],
            "mbinfo": kwargs["mbinfo"],
            "threatType": kwargs["threatType"]
        }
        if "confirm" in kwargs:
            option_args["confirm"] = kwargs["confirm"]
        es_template = format_es_temp(option_args, self.es_template)
        es_template = get_es_template(es_template, kwargs)
        group = kwargs["groupKey"]
        if group != "uniqueId":
            group = "flow." + group
        group = group + ".keyword"
        unique_filter = {
            "term": {
                group: kwargs["value"]
            }
        }
        es_template["query"]["bool"]["must"].append(unique_filter)
        page = int(kwargs["page"])
        page_size = int(kwargs["pageSize"])
        es_template['from'] = (page - 1) * page_size
        es_template['size'] = page_size
        try:
            res = self.es_client.search(index=MODEL_INDEX, body=es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params %s, reason: %s' % (es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(kwargs["groupKey"], res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params %s,reason: %s' % (res, str(e)))
            return flask_response(err_info, False, {})

    def data_format(self, group_key, es_data):
        """
        :param es_data:
        :return:
        """
        total = es_data["hits"]["total"]['value']
        data_return = {
            "total": total,
            "alters": []
        }
        model_data_list = es_data["hits"]["hits"]
        if group_key == "uniqueId":
            for model_data in model_data_list:
                model_dict = dict()
                model_dict["_id"] = model_data["_id"]
                model_dict["proto"] = model_data["_source"]["flow"]["proto"]
                model_dict["occurredTime"] = model_data["_source"]["occurredTime"]
                model_dict["observedTime"] = model_data["_source"]["observedTime"]
                model_dict["taskType"] = model_data["_source"]["taskType"]
                model_dict["src_mac"] = model_data["_source"]["flow"]["src_mac"]
                model_dict["dst_mac"] = model_data["_source"]["flow"]["dst_mac"]
                model_dict["responseName"] = model_data["_source"]["responseName"]
                model_dict["src_ip"] = model_data["_source"]["flow"]["src_ip"]
                model_dict["dst_ip"] = model_data["_source"]["flow"]["dst_ip"]
                model_dict["src_port"] = model_data["_source"]["flow"]["src_port"]
                model_dict["dst_port"] = model_data["_source"]["flow"]["dst_port"]
                model_dict["keyword"] = model_data["_source"]["keyword"]
                model_dict["session"] = model_data["_source"]["session"]
                model_dict["pcap_filename"] = model_data["_source"]["pcap_filename"]
                model_dict["not_fss_pcapname"] = model_data["_source"]["not_fss_pcapname"]
                data_return["alters"].append(model_dict)
        else:
            for model_detail in model_data_list:
                data_dict = model_detail["_source"]
                data_dict["taskName"] = ""
                celery_id = data_dict["celeryId"]
                rst = self.mongodb.find_one('back_explore', {"celeryId": celery_id})
                if rst:
                    data_dict["taskName"] = rst['taskName']
                data_return['alters'].append(data_dict)
        return data_return


class ModelDetail(NdrResource):
    """ 获取 model 数据详情 """

    def __init__(self):
        super(ModelDetail, self).__init__(es_template="model_detail")

    @check_flask_args(Validator("model_detail"), request)
    def get(self, **kwargs):
        model_log_id = kwargs["modelLogId"]
        self.es_template["query"]["bool"]["must"][0]["term"]["_id"] = model_log_id
        try:
            res = self.es_client.search(index=MODEL_INDEX, body=self.es_template)
        except Exception as e:
            message = str(e)
            ES_LOG.error('params %s, reason: %s' % (self.es_template, str(e)))
            return flask_response(message, False, {})
        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params %s,reason: %s' % (res, str(e)))
            return flask_response(err_info, False, {})

    @staticmethod
    def data_format(es_data):
        """
        :param es_data:
        :return:
        """
        model_detail = es_data["hits"]["hits"][0]["_source"]
        for detail in model_detail["detail"]:
            detail["ts"] = int(detail["ts"] * 1000)
            if detail["topic"] == "login":
                detail["proto"] = detail["application"]
            elif detail["topic"] != "conn":
                detail["proto"] = detail["topic"]
        return model_detail
