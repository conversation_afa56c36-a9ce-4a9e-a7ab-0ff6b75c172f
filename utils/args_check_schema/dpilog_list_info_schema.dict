{
    "type": "object",
    "required": ["dpilogType"],
    "properties": {
        "startTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
        },
        "stopTime": {
            "type": "string",
            "default": "",
            "pattern": r"^\d{12,13}$"
        },
        "dpilogType": {
            "type": "string",
            "default": "",
            "pattern": r"^dpilog_(http|conn|dns|ftp|mail|ssl|files|icmp|mysql|login|dhcp|telnet|nfs|modbus|snmp|tftp|netbios|rip|igmp|smb|mssql)"
        },
        "ip1": {
            "type":"string",
            "default": ""
        },
        "ip2": {
            "type":"string",
            "default": ""
        },
        "isAccurate": {
            "type": "string",
            "default": "false",
            "enum": ["true", "false"]
        },
        "srcPort": {
            "type":"string",
            "default": "",
            "pattern": r"^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$"
        },
        "dstPort": {
            "type":"string",
            "default": "",
            "pattern": r"^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$"
        },
        "keyword": {
            "type":"string",
            "default": ""
        },
        "pageSize": {
            "type":"string",
            "default":"50"
        },
        "celeryId": {
            "type":"string",
            "default": ""
        },
        "page": {
            "type":"string",
            "default": "1"
        },
        "pcap_filename": {
            "type":"string",
            "default": ""
        },
        "subject": {
            "type":"string",
            "default": ""
        },
        "body": {
            "type":"string",
            "default": ""
        },
        "from": {
            "type":"string",
            "default": ""
        },
        "recipient": {
            "type":"string",
            "default": ""
        },
        "file_names": {
            "type":"string",
            "default": ""
        },
        "service": {
            "type":"string",
            "default": ""
        },
        "application": {
            "type":"string",
            "default": ""
        }
    }
}