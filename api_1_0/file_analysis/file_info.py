#!/usr/bin/env python3
# -*- coding:utf-8 -*-
import datetime
import time

import pandas as pd
from flask import request, send_file, make_response

from api_1_0.utils.flask_log import ndr_log_to_box
from api_1_0.file_analysis.file_statistics import FileResource
from config.config import NdrLog
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class FileAnalysisList(FileResource):
    """
    文件分析
    """

    def __init__(self):
        super(FileAnalysisList, self).__init__(es_template="get_file_analysis_statistic_report_list")

    @param_check.check_flask_args(Validator("file_analysis_list_schema"), request)
    def get(self, **kwargs):
        """
        统计list数据
        :return:
        """
        self.start_time = kwargs["args"]["startTime"]
        self.end_time = kwargs["args"]["endTime"]
        sort = kwargs["args"]["sort"]
        page = kwargs["args"]["page"]
        page_size = kwargs["args"]["pageSize"]
        self.src_ip = kwargs["args"]["srcIp"]
        self.dst_ip = kwargs["args"]["dstIp"]
        self.file_name = kwargs["args"]["fileName"]
        self.file_type = kwargs["args"]["fileType"]
        self.protocol = kwargs["args"]["protocol"]
        self.threat_level = kwargs["args"]["threatLevel"]
        self.celeryId = kwargs["args"]["celeryId"]

        try:
            return_data = {}
            self.format_es_time()
            if not self.es_index_list:
                return return_data
            es_template_statistic = self.format_es_query(self.es_template)

            # 过滤掉非威胁得告警
            # filter_arg = {"term": {"threatScore": {"value": 0}}}
            # es_template_statistic["query"]["bool"]["must_not"].append(filter_arg)
            # total
            total_data = self.es_client.search(index=self.es_index_list, body=es_template_statistic)
            return_data["total"] = total_data["hits"]["total"]["value"]
            es_template_statistic = self.format_es_temp_sort(es_template_statistic, sort)
            es_template_statistic = self.format_es_page(es_template_statistic, int(page), int(page_size))
            # data
            alter_data = self.es_client.search(index=self.es_index_list, body=es_template_statistic)
            return_data["detail"] = alter_data["hits"]["hits"]

        except Exception as e:
            err_info = "error: " + str(e)
            LOG.error('reason: %s' % str(e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "获取文件分析列表失败")
            return flask_response(err_info, False, {})

        ndr_log_to_box(NdrLog.Type.OPERATE, "获取文件分析列表成功")
        return flask_response('获取成功', True, return_data)

    @staticmethod
    def format_es_temp_sort(es_template, order):
        es_template["sort"][0]["observedTime"]["order"] = order
        return es_template

    @staticmethod
    def format_es_page(es_template, page, page_size):
        es_template["from"] = (page - 1) * page_size
        es_template["size"] = page_size
        return es_template


class FileAnalysisListExport(FileResource):
    """
    文件分析list导出
    """

    def __init__(self):
        super(FileAnalysisListExport, self).__init__(es_template="get_file_analysis_statistic_report_list_export")
        self.excel_file_path = ''
        self.export_data = []

    @param_check.check_flask_args(Validator("file_analysis_ip_info_schema"), request)
    def get(self, **kwargs):
        """
        统计list数据
        :return:
        """
        self.start_time = kwargs["args"]["startTime"]
        self.end_time = kwargs["args"]["endTime"]
        sort = kwargs["args"]["sort"]
        self.src_ip = kwargs["args"]["srcIp"]
        self.dst_ip = kwargs["args"]["dstIp"]
        self.file_name = kwargs["args"]["fileName"]
        self.file_type = kwargs["args"]["fileType"]
        self.protocol = kwargs["args"]["protocol"]
        self.threat_level = kwargs["args"]["threatLevel"]
        self.celeryId = kwargs["args"]["celeryId"]

        try:
            return_data = []
            self.export_file_path_init()
            self.format_es_time()
            if not self.es_index_list:
                self.export_data = return_data
            else:
                es_template_statistic = self.format_es_query(self.es_template)
                es_template_statistic = self.format_es_temp_sort(es_template_statistic, sort)
                alter_data = self.es_client.search(index=self.es_index_list, body=es_template_statistic)
                self.export_data = alter_data["hits"]["hits"]
            self.export_excel()

        except Exception as e:
            LOG.error('reason: %s' % str(e))
            ndr_log_to_box(NdrLog.Type.OPERATE, "获取文件分析列表导出失败")
            return flask_response("Export excel file failed!", False, {'error msg': str(e)})
        
        ndr_log_to_box(NdrLog.Type.OPERATE, "获取文件分析列表导出成功")
        return make_response(send_file(self.excel_file_path, as_attachment=True))

    def export_file_path_init(self):
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        self.excel_file_path = '/tmp/list_report_%s.xlsx' % time_str

    def export_excel(self):
        datas = []
        for val in self.export_data:
            source = val["_source"]
            threat_level = ''
            occurred_time = source["observedTime"]
            # 将毫秒级时间戳转换为秒级时间戳
            timestamp_seconds = int(occurred_time) / 1000
            # 创建datetime对象
            dt = datetime.datetime.fromtimestamp(timestamp_seconds)
            # 格式化日期时间字符串
            formatted_date = dt.strftime("%Y-%m-%d %H:%M:%S")

            if source["threatLevel"] == 'Safe':
                threat_level = '安全'
            elif source["threatLevel"] == 'Low':
                threat_level = '低危'
            elif source["threatLevel"] == 'Medium':
                threat_level = '中危'
            elif source["threatLevel"] == 'High':
                threat_level = '高危'
            data = [formatted_date, source["proto"], source["filename"], source["filetype"],
                    source["flow"]["dst_ip"], source["flow"]["src_ip"], threat_level]
            datas.append(data)

        df = pd.DataFrame(datas)
        df.columns = ["时间", "协议", "文件名", "文件类型", "目的IP", "源IP", "威胁等级"]
        df.to_excel(self.excel_file_path, index=False)

    @staticmethod
    def format_es_temp_sort(es_template, order):
        es_template["sort"][0]["observedTime"]["order"] = order
        return es_template
