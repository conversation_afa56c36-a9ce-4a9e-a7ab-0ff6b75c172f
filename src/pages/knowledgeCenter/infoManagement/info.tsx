import KillChainStages from '@/components/KillChainStages/index';
import {
  addInfo,
  delAllData,
  delData,
  editInfo,
  getInfoData,
  getInfoModel
} from '@/services/infoManagement';
import { intelligenceType } from '@/utils/enumList';
import { ellipsis } from '@/utils/utils';
import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Collapse,
  Form,
  Input,
  Modal,
  Popconfirm,
  Progress,
  Select,
  Table,
  Tooltip,
  Upload,
  Menu,
  Dropdown,
  message
} from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from "react";
import EditForm from './editForm';
import style from './style.less';


const { Option } = Select;

const Info = (props: { form: any }) => {
  const [form] = Form.useForm();
  const ioc = new URLSearchParams(location.search).get('ioc');
  React.useEffect(() => {
    form.setFieldsValue({ ioc });
  }, []);
  const childForm = useRef<any>({});
  const [params, setparams] = useState({
    page: 1,
    pageSize: 10,
    ioc: ioc || undefined,
  });
  const [tableData, settableData] = useState([]);
  const [total, settotal] = useState(0);
  const [isAdd, setisAdd] = useState(false);
  const [visible, setvisible] = useState(false);
  const [formValue, setformValue] = useState({});
  const [uploadVisible, setuploadVisible] = useState(false);
  const [defaultPercent, setdefaultPercent] = useState(0);
  const columns = [
    { title: 'ioc', dataIndex: 'ioc' },
    { title: '情报类型', dataIndex: 'ioc_type' },
    {
      title: '发现时间',
      dataIndex: 'discoverytime',
      render: (t: number) => {
        if (t) {
          return moment(t).format('YYYY-MM-DD');
        }
      },
    },
    {
      title: '标签',
      dataIndex: 'labels',
      render: (t: any) => {
        return <Tooltip title={t}>{ellipsis(t, 10)}</Tooltip>;
      },
    },
    {
      title: '利用工具',
      dataIndex: 'tools',
    },
    {
      title: '组织名',
      dataIndex: 'organizations',
    },
    { title: '威胁得分', dataIndex: 'score' },
    { title: '威胁分类', dataIndex: 'threat_type' },
    {
      title: '杀伤链阶段',
      dataIndex: 'killchainstage',
      width: 254,
      render: (t: string[]) => {
        if (t) {
          return (
            <div className={style.killchainBox}>
              <KillChainStages data={t} />
            </div>
          );
        }
      },
    },
    {
      title: '参考',
      dataIndex: 'reference',
      render: (t: any) => {
        return <Tooltip title={t}>{ellipsis(t, 10)}</Tooltip>;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              type="link"
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定删除吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={() => handleDel(record)}
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page,
    showTotal: (total: any) => `共${total}条`,
    pageSize: params.pageSize,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };

  const importProps = {
    name: 'fileName',
    action: '/mica-api/api/v1/customize/ioc/import',
    showUploadList: false,
    onChange: (info: {
      file: {
        status: string;
        percent: number;
        response: any;
      };
    }) => {
      if (info.file.status === 'uploading') {
        toggleVisibleStatusTrue(Math.floor(info.file.percent));
      } else if (!info.file.response.flag) {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
        return;
      } else if (info.file.status === 'done' && info.file.response.flag) {
        setTimeout(() => {
          if (info.file.response.data.failed_total) {
            message.warning(
              `${info.file.response.message},${info.file.response.data.failed_total}`,
            );
          } else {
            message.success(info.file.response.message);
          }
          toggleVisibleStatusFalse();
          getTableData();
        }, 3000);
      } else if (info.file.status === 'error') {
        toggleVisibleStatusFalse();
        message.error(`上传失败,${info.file.response.message}` || '上传失败');
        setuploadVisible(false);
      }
    },
    beforeUpload: (file: any) => {
      let pos = file.name.lastIndexOf('.');
      let result = file.name.substring(pos + 1, file.name.length);
      if (!['xls', 'xlsx'].includes(result)) {
        message.error('仅支持上传xls/xlsx格式的文件');
        return Upload.LIST_IGNORE;
      }
    },
  };
  useEffect(() => {
    getTableData();
  }, [params]);

  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      setparams({ ...params, ...value, page: 1, pageSize: 10 });
    });
  };

  const getTableData = () => {
    getInfoData(params).then(res => {
      if (res.flag) {
        settableData(res.data.detail);
        settotal(res.data.total);
      }
    });
  };
  const handleDel = (value: any) => {
    let data = {
      ioc: value.ioc,
      ioc_type: value.ioc_type,
    };
    delData(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        getTableData();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleAdd = () => {
    setisAdd(true);
    setformValue({});
    setvisible(true);
    // let count = Object.keys(childForm.current).length;
    // if (count) {
    //   childForm.current.resetFields();
    // }
  };
  const handleEdit = (value: any) => {
    if (value.discoverytime) {
      value.discoverytime = moment(value.discoverytime);
    }
    setformValue(value);
    setisAdd(false);
    setvisible(true);
  };
  const handleOk = () => {
    childForm.current.validateFields().then((value: any) => {
      if (value.discoverytime) {
        value.discoverytime = moment(value.discoverytime).valueOf();
        value.discoverytime = `${value.discoverytime}`;
        // value.discoverytime = moment(value.discoverytime).valueOf();
      }
      if (isAdd) {
        addInfo(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            setvisible(false);
            getTableData();
          } else {
            message.error(res.message);
          }
        });
      } else {
        !value.discoverytime ? delete value.discoverytime : null;
        editInfo(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            setvisible(false);
            getTableData();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };
  const handleCancel = () => {
    setvisible(false);
  };
  // 关闭进度条
  const toggleVisibleStatusFalse = () => {
    setuploadVisible(false);
  };
  // 展示进度条
  const toggleVisibleStatusTrue = (percent: any) => {
    setuploadVisible(true);
    setdefaultPercent(percent);
  };
  const deleteAll = () => {
    delAllData().then(res => {
      if (res.flag) {
        message.success(res.message);
        getTableData();
      } else {
        message.error(res.message);
      }
    });
  };
  const menu = (
    <Menu>
      <Menu.Item>
        <Upload {...importProps} accept=".xls, .xlsx">
          <span className="margin_right_10" t>
            情报导入
          </span>
        </Upload>
      </Menu.Item>
      <Menu.Item>
        <span className="margin_right_10" onClick={() => { downloadModel() }}>模版下载</span>
      </Menu.Item>
    </Menu>
  );
  const downloadModel = async () => {
    const res = await getInfoModel();
    const a = document.createElement('a');
    a.href = URL.createObjectURL(res);
    a.download = '情报模版.xlsx';
    a.click();
    URL.revokeObjectURL(a.href);
  }
  return (
    <div>
      <Form
        form={form}
        className="!flex gap-3 bg-d0 b-brd6 b b-solid !px-4 !pt-5"
        onFinish={onSubmit}
      >
        <Form.Item label="IOC查询" name="ioc">
          <Input placeholder="请输入IOC" allowClear />
        </Form.Item>
        <Form.Item
          className='!flex-nowrap'
          label="情报类型" name="ioc_type">
          <Select allowClear placeholder='请选择情报类型'>
            {intelligenceType.map(item => {
              return (
                <Option value={item.value} key={item.key}>
                  {item.key}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item label="标签" name="labels">
          {/* <Select allowClear={true}>
            {threatClassifyList.map((item: any, index) => (
              <Option key={item.threatFlag} value={item.threatFlag}>
                {item.threatFlagCN}
              </Option>
            ))}
          </Select> */}
          <Input placeholder="请输入标签" allowClear />
        </Form.Item>
        <Form.Item>
          <div className='flex gap-3'>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SearchOutlined />}
            >
              搜索
            </Button>
            {/* <Upload {...importProps} accept=".xls, .xlsx">
            <Button className='margin_right_10' type="primary" ghost>
              情报导入
            </Button>
          </Upload> */}
            <Dropdown overlay={menu} >
              <Button type="primary" ghost>
                情报导入
              </Button>
            </Dropdown>
            <Button
              onClick={handleAdd}
              type="primary"
              ghost
            >
              自定义情报
            </Button>
            <Popconfirm
              title="确定删除吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={() => {
                deleteAll();
              }}
            >
              <Button type="primary" ghost danger>
                删除所有情报
              </Button>
            </Popconfirm>
          </div>
        </Form.Item>
      </Form>
      <Table
        className="mt-4"
        rowKey={(r, i) => i}
        pagination={pagination}
        dataSource={tableData}
        columns={columns}
      ></Table>
      <Modal
        destroyOnClose={true}
        title={isAdd ? '新增情报' : '编辑情报'}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <EditForm ref={childForm} isAdd={isAdd} value={formValue} />
      </Modal>
      <Modal
        destroyOnClose={true}
        title="文件正在上传..."
        visible={uploadVisible}
        maskClosable={false}
        keyboard={false}
        footer={null}
        closable={false}
      >
        <Progress percent={defaultPercent} status="active" />
      </Modal>
    </div>
  );
};
export default Info;