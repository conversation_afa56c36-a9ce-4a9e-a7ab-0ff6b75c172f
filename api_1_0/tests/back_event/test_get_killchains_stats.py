# -*- coding: utf-8 -*-
# @Time    : 2019-09-02 18:51
# <AUTHOR> Shark
# @File    : test_get_killchains_stats.py
# @Software: PyCharm
import json
import random
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_event.get_killchains_stats import KillchainsStats

timeSlice = ["Minute", "Hour", "Day", "Week", "Month"]


class TestKillchainsStats(TestCase):

    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_startTime_not_exists(self):
        resp = self.client.get(
            api.url_for(KillchainsStats,
                        stopTime=1567008000000,
                        pageSize=10,
                        timeSlice=random.choice(timeSlice)
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_stopTime_not_exists(self):
        resp = self.client.get(
            api.url_for(KillchainsStats,
                        startTime=1567008000000,
                        pageSize=10,
                        timeSlice=random.choice(timeSlice)
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_pageSize_not_exists(self):
        resp = self.client.get(
            api.url_for(KillchainsStats,
                        startTime=1567008000000,
                        stopTime=1578430872367,
                        timeSlice=random.choice(timeSlice)
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)

    def test_timeSlice_not_exists(self):
        resp = self.client.get(
            api.url_for(KillchainsStats,
                        startTime=1567008000000,
                        stopTime=1578430872367,
                        pageSize=10
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_startTime_lt_stopTime(self):
        resp = self.client.get(
            api.url_for(KillchainsStats,
                        startTime=1578430872367,
                        stopTime=1567008000000,
                        pageSize=10,
                        timeSlice=random.choice(timeSlice)
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_all_true(self):
        resp = self.client.get(
            api.url_for(KillchainsStats,
                        startTime=1567180800000,
                        stopTime=1578430872367,
                        pageSize=10,
                        timeSlice=random.choice(timeSlice)
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)
