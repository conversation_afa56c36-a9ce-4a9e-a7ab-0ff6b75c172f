from __future__ import absolute_import, unicode_literals

import json
import sys
sys.path.append('/opt/ndr')
from cluster.api.manager import MyClusterManager

import cluster
from celery import Celery
from celery.schedules import crontab
from kombu import Exchange, Queue
from multiprocessing import Queue as MutiQueue
from .utils import MongoDB, get_redis_container_ip


class RedisConfig:
    Port = 6379
    Password = 'QaHJ7SR4uY'


class SupervisorConfig:
    User = "ndr"
    Password = "1qaz@WSXndr"


app = Celery(
    'host_celery',
    broker="redis://:" + RedisConfig.Password + '@' + get_redis_container_ip("redis") + ":" + str(
        RedisConfig.Port) + "/3",
    backend="redis://:" + RedisConfig.Password + '@' + get_redis_container_ip("redis") + ":" + str(
        RedisConfig.Port) + "/4"
)

product_type = 'D8600'
# 初始化一些东西
try:
    node_name = MyClusterManager.get_node_name()
    condition = {}
    if node_name:
        condition = {'node_name': node_name}
    mongodb = MongoDB("ndr")
    rst_list = mongodb.find("interface_of_work", condition)

    interface_data = {}
    interface_speed = {}
    for rst in rst_list:
        interface_data[rst["if_name"]] = rst["if_index"]
        interface_speed[rst["if_name"]] = rst["speed"]

    sys_cfg = mongodb.find_one("system_config", {})
    product_type = sys_cfg.get('divice', 'D8600')  # 如果没找到型号，按照规定目录最小阈值的型号设为默认值

    if 'realtime' not in sys_cfg \
            or 'replay_lcores_stream_mod' not in sys_cfg \
            or 'replay_lcores_replay_mod' not in sys_cfg:
        print('\nERROR: mongo collection system_config not complete!!\n', flush=True)
        exit(-1)

    gl_stream_lcores = sys_cfg['replay_lcores_stream_mod']
    gl_replay_lcores = sys_cfg['replay_lcores_replay_mod']
    replay_lcores = sys_cfg['replay_lcores_stream_mod'] if sys_cfg['realtime'] else sys_cfg['replay_lcores_replay_mod']
    # 将队列最大值设置为能够使用的lcore最大队列，虽然在实时模式下不能将队列占满，但是为了方便后面切换回放环境时不必再初始化多进程队列
    gl_hdp_lcore_queue = MutiQueue(maxsize=len(gl_replay_lcores))
    print('\n============= lcore queue init ==============')
    for lcore in replay_lcores:
        print('******    hdp replay lcore: %d  is ready...' % lcore)
        gl_hdp_lcore_queue.put(int(lcore))

    print('===========================================\n', flush=True)

    #  =======================================================
    #  !!! 请确认实时的lcore是回放的lcore的子集, 否则会有问题 !!!
    #  =======================================================
    resource_lcores = list(set(gl_replay_lcores).difference(set(gl_stream_lcores)))

    mongodb.close()
except Exception as e:
    print(e)


app.conf.update(
    timezone='Asia/Shanghai',
    task_serializer='json',
    accept_content=['json'],
    worker_concurrency=12,
    # 创建一个hdp_task队列，宿主机的celery任务只接受hdp_task队列的任务，与默认队列celery区分
    celery_queues={
        Queue("hdp_task", Exchange("hdp_task"), routing_key="hdp_task")
    },
    celery_routes={
        "host_celery_task.hdp_pcap_task": {
            "queue": "host_task",
            "routing_key": "host_task"
        },
        "host_celery_task.run_command_in_host": {
            "queue": "host_task",
            "routing_key": "host_task"
        }
    },
    imports=[
        'host_celery.tasks',
        'host_celery.cluster',
        'host_celery.cert'
    ]
)

app.conf.beat_schedule = {
    'every-5-seconds': {
        'task': 'host_celery.tasks.get_cpu_mem_data',
        # 配置计划任务的执行时间，这里是每60秒执行一次
        'schedule': 5.0,
        'args': (),
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    },
    'every-10-seconds': {
        'task': 'host_celery.tasks.get_interface_data',
        # 配置计划任务的执行时间，这里是每10秒执行一次
        'schedule': 10.0,
        'args': (),
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    },
    'every-5-minutes': {
        'task': 'host_celery.tasks.get_disk_data',
        # 配置计划任务的执行时间，这里是每300秒执行一次
        'schedule': 300.0,
        'args': (),
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    },
    'every-1-minutes': {
        'task': 'host_celery.cluster.cluster_check',
        'schedule': crontab(hour=2, minute=0),
        'args': (),
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    },
    'run-every-day-at-2am': {
        'task': 'host_celery.cluster.consistency_check_and_sync',
        'schedule': crontab(hour=2, minute=0),
        'args': (),
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    },
    'time-5-minutes': {
        'task': 'host_celery.tasks.time_monitor',
        # 后端定时任务监控[alert_output, ]
        'schedule': 300.0,
        'args': (),
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    },
    'hdp_time-1-hours': {
        # hdp解析文件files目录监控定时任务
        'task': 'host_celery.tasks.hdp_files_auto_delete_task',
        'schedule': 3600.0,
        'args': (product_type,),
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    },
    'warning_pcap_delete_per_day': {
        'task': 'host_celery.tasks.warning_pcap_delete_task',
        'schedule': crontab(minute=30, hour=3),  # 每天3点30分 执行一次任务
        'options': {
            'routing_key': "monitor_task",
            'queue': "monitor_task"
        }
    }
}


if __name__ == '__main__':
    app.start()
