import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';

// 探索任务table
export const getExploreTable = (data: object) => {
  return request(`explore${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
// 探索任务开关
export const handleSwitch = (id: any, data: { action: any }) => {
  return request(`explore/${id}`, {
    method: 'put',
    data,
  });
};

// 编辑任务 获取数据包列表
export const getpacpList = (data: object) => {
  return request(`feature/group${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
// 编辑任务获取 选择目录

export const getDir = () => {
  return request(`explore/dir`, {
    method: 'get',
  });
};
export const getFtp = (data: string) => {
  return request(`explore/ftp_tree?dir=${data}`, {
    method: 'get',
    data,
  });
};
// 获取最近一次上传
export const getLastUploadDir = (data: object) => {
  return request(`explore/pcap${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};

// 获取数据源
export const getDataSource = () => {
  return request(`explore/nas/list_info?pageSize=1000`, {
    method: 'get',
  });
};
// 探索任务 离线数据源table
export const getOffLineData = (data: object) => {
  return request(`explore/dir${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
// 获取所有离线数据源目录
export const getAllOffLineMenu = () => {
  return request(`explore/dir`, {
    method: 'get',
  });
};

// 删除离线数据源
export const delOffData = (data: object) => {
  return request(`explore/dir`, {
    method: 'delete',
    data,
  });
};

export const uploadFirtst = (data: object) => {
  return request(`/explore/pcap_exist`, {
    method: 'post',
    data,
  });
};
// 合并上传
export const mergeUpload = (data: object) => {
  return request(`explore/pcap_upload`, {
    method: 'post',
    data,
  });
};

// 在线数据源table
export const getOnlineTable = (data: object) => {
  return request(`explore/nas/list_info${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
// 删除在线数据源
export const delOnlineData = (data: object) => {
  return request(`explore/nas`, {
    method: 'delete',
    data,
  });
};

// 添加在线数据源
export const addOnline = (data: object) => {
  return request(`explore/nas`, {
    method: 'post',
    data,
  });
};
export const editeOnline = (data: any) => {
  return request(`explore/nas`, {
    method: 'put',
    data,
  });
};
// 在线数据源详情
export const getOnlineDetail = async (data: object) => {
  return await request(`explore/nas${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
export const createOnline = (data: any) => {
  return request(`explore/nas/config`, {
    method: 'post',
    data,
  });
};
export const upDir = (data: { dir: string, type:string }) => {
  return request('explore/dir', {
    method: 'POST',
    data,
  });
};

export const addTask = (data: object) => {
  return request(`explore`, {
    method: 'post',
    data,
  });
};

export const editData = (data: { taskId: any }) => {
  return request(`explore/${data.taskId}`, {
    method: 'put',
    data,
  });
};

export const deleteData = (data: string) => {
  return request(`explore/${data}`, {
    method: 'delete',
  });
};

export const getTaskPacp = (data: any) => {
  return request(`explore/task_pcap${parseQueryString(data)}`, {
    method: 'get',
  });
};

export const getOffLineDetail = (data: object) => {
  return request(`explore/pcap${parseQueryString(data)}`, {
    method: 'get',
    data,
  });
};
export const ediltTag = (data: object) => {
  return request('explore/pcap', {
    method: 'put',
    data,
  });
};

export const getFTPDir = () => {
  return request(`explore/dir`, {
    method: 'get',
  });
};

export const getFTPTree = () => {
  return request(`explore/ftp_tree`, {
    method: 'get',
  });
};

export const openConnect = (data: object) => {
  return request(`explore/ftp_server`, {
    method: 'post',
    data,
  });
};

export const FTPDownload = (data: object) => {
  return request(`explore/ftp_download`, {
    method: 'post',
    data,
  });
};

export const getFTPstatus = () => {
  return request('explore/ftp_server', {
    method: 'get',
  });
};

export const delOffLineDetail = (data: any) => {
  return request('explore/pcap', {
    method: 'delete',
    data,
  });
};

// 在线数据源/连接尝试
export const tryConnect = (data: any) => {
  return request(`explore/nas/list_info`, {
    method: 'post',
    data
  });
};
