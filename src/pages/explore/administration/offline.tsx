import React, { useEffect, useState, useRef } from 'react';
import { Form, Input, Button, Table, Modal, message, Progress, Spin, Row, Col, Select } from 'antd';
import { getOffLineData, delOffData, uploadFirtst, mergeUpload, upDir, FTPDownload } from '@/services/explore';

import style from './style.less';
import md5 from 'md5';
import axios from 'axios';
import { createFileChunk } from '@/utils/utils';
import Local from './local';
import FTP from './FTP';
import {
  LoadingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SearchOutlined,
  ToTopOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useHistory } from '@/hooks/global';
const { Option } = Select;

const { confirm } = Modal;
const GROUP_SIZE = 2;
let allFileList: any[] | ConcatArray<never> = []; //因为执行到后面的函数里获取fileList 为空所以再次赋值

const Offline = () => {
  let uploadedCount = 0;
  const [form] = Form.useForm();
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox');
  const [downloadloading, setdownloadloading] = useState(false);
  const LocalForm = useRef<any>(null);
  const [tableData, settableData] = useState([]);
  const [uploading, setuploading] = useState(false);
  const [fileList, setfileList] = useState<any>([]);
  const [category, setcategory] = useState('');
  const [params, setparams] = useState({
    page: 1,
    pageSize: 10,
  });
  const [total, settotal] = useState(0);
  const [localVisible, setlocalVisible] = useState(false);
  const [FTPVisible, setFTPVisible] = useState(false);
  const [readyList, setreadyList] = useState([]);
  const [dirVisible, setdirVisible] = useState(false);
  const [dir, setdir] = useState('');
  const [fileType, setFileType] = useState('');
  const [downDir, setdownDir] = useState('');
  const [downFileList, setdownFileList] = useState([]);
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const history = useHistory();
  let dirName = '';
  let dirType = ''
  const layout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };
  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const getData = () => {
    setselectedRowKeys([]);
    getOffLineData(params).then(res => {
      setTimeout(() => {
        settableData(res.data.detail);
        settotal(res.data.count);
      }, 500);
    });
  };
  useEffect(() => {
    getData();
  }, [params]);
  const columns = [
    {
      title: '目录名称',
      dataIndex: 'dirName',
    },
    {
      title: '用途',
      dataIndex: 'type',
      render: e => {
        const obj = {
          1: '流量包存放',
          2: '普通文件存放',
        };
        return <div>{obj[e]}</div>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
    },
    {
      title: '用户',
      dataIndex: 'createByUserName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (t: string, record: any) => {
        const showDetail = (record: any) => {
          history.push(`exploreDetail?dir=${record.dirName}`);
        };
        return (
          <Button
            type="link"
            onClick={() => {
              showDetail(record);
            }}
          >
            查看
          </Button>
        );
      },
    },
  ];
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      const formvalue = {
        ...params,
        ...value,
        page: 1,
        pageSize: 10,
      };
      setparams(formvalue);
    });
  };
  const handeDel = () => {
    if (!selectedRowKeys || !selectedRowKeys.length) {
      message.error('请先选择需要删除的目录');
      return;
    }
    let delArray: {
      dir: any;
      userId: any;
    }[] = [];
    selectedRowKeys.forEach((item: any) => {
      tableData.forEach((ele: any) => {
        if (item === ele.dirName) {
          let value = {
            dir: ele.dirName,
            userId: ele.createByUserId,
          };
          delArray.push(value);
        }
      });
    });
    confirm({
      title: '确定删除选中的目录吗?',
      okType: 'danger',
      onOk() {
        delOffData({ fileList: delArray }).then(res => {
          if (res.flag) {
            close();
            message.success('操作成功');
            getData();
          } else {
            res.message.forEach((item: any) => {
              message.error(item);
            });
          }
        });
      },
      onCancel() { },
    });
  };

  const rowSelection = {
    onChange: (data: any) => {
      setselectedRowKeys(data);
    },
    selectedRowKeys,
  };
  const localUpload = () => {
    setlocalVisible(true);
  };
  const FTPUpload = () => {
    setFTPVisible(true);
  };
  // 分片分组，限制并发上传数
  const chunkGroup = (list = [], size = GROUP_SIZE) => {
    const len = list.length;
    const count = Math.ceil(len / size);
    const group = [];
    for (let i = 0; i < count; i += 1) {
      group.push(list.slice(i * size, (i + 1) * size));
    }

    return group;
  };
  const handleLocalOk = () => {
    let formValue = LocalForm.current.getFieldsValue();
    if (!formValue || !formValue.dir) {
      message.error('请选择上传目录');
      return;
    }
    if (!readyList) {
      message.error('请选择上传文件');
      return;
    }
    // if (readyList.length > 100) {
    //   message.error('一次性最多上传100个文件');
    //   return;
    // }
    setcategory(formValue.dir);
    dirName = formValue.dir;
    dirType = formValue.type
    const fileNames = readyList.map((item: any) => item.file.name);
    let uploadParams = {
      fileNames,
      dir: formValue.dir,
    };
    uploadFirtst(uploadParams)
      .then(async res => {
        if (res.flag) {
          const data = res.data;
          if (data && data.length) {
            Modal.confirm({
              title: '提示',
              cancelText: '否',
              okText: '是',
              content: (
                <div>
                  <div>以下文件已存在，是否覆盖？</div>
                  {res.data.map((item: string) => (
                    <div key={item}>{item}</div>
                  ))}
                </div>
              ),
              onOk: async () => {
                const fileList = readyList.map((item: any) => {
                  const file = item.file;
                  const fileName = file.name;
                  const fileId = md5(`${dirName}${fileName}${file.size}${file.lastModified}`);
                  const chunkList = createFileChunk(file) || [];
                  return {
                    file,
                    fileId,
                    chunkList,
                    uploadResult: 'pending',
                  };
                });
                setuploading(true);
                uploadedCount = allFileList.length;
                allFileList = fileList;
                // 首次上传确认覆盖
                for (let i = 0; i < fileList.length; i++) {
                  await uploadFile(fileList[i], i, true);
                }
                setfileList(fileList);
              },
              onCancel() {
                const fileList = readyList
                  .filter((v: any) => data.indexOf(v.file.name) < 0)
                  .map((v: any) => {
                    const file = v.file;
                    const fileName = file.name;
                    const fileId = md5(`${dirName}${fileName}${file.size}${file.lastModified}`);
                    const chunkList = createFileChunk(file) || [];
                    return {
                      file,
                      fileId,
                      chunkList,
                      uploadResult: 'pending',
                    };
                  });
                // 首次上传不覆盖 断点续传
                setuploading(true);
                setfileList(fileList);
                uploadedCount = fileList.length;
                Promise.allSettled(fileList.map((f: any, i: number) => uploadFile(f, i)));
              },
            });
          } else {
            const fileList = readyList.map((v: any) => {
              const file = v.file;
              const fileName = file.name;
              const fileId = md5(`${dirName}${fileName}${file.size}${file.lastModified}`);
              const chunkList = createFileChunk(file) || [];
              return {
                file,
                fileId,
                chunkList,
                uploadResult: 'pending',
              };
            });
            setuploading(true);
            uploadedCount = allFileList.length;
            // 首次上传
            allFileList = fileList;
            setfileList(fileList);
            Promise.allSettled(fileList.map((f, i) => uploadFile(f, i)));
          }
        } else {
          message.error(res.data.message);
        }
      })
      .catch(err => {
        message.error(err.message);
      });
    setlocalVisible(false);
  };

  // 上传文件
  const uploadFile = async (
    fileItem: {
      fileId: any;
      file: any;
      chunkList: any;
      uploadResult?: string;
    },
    fileIndex: number,
    cover = false,
  ) => {
    const { fileId, file, chunkList } = fileItem;
    const fileName = file.name;
    const chunkPromise = chunkList.map((v: { file: any }, index: any) => {
      const payload = {
        fileObject: v.file,
        fileName,
        fileId,
        chunkId: index,
        cover,
      };
      return {
        payload,
        chunkIndex: index,
      };
    });
    const chunkPromiseGroup = chunkGroup(chunkPromise);
    await uploadChunkGroup(chunkPromiseGroup, 0, fileIndex);
  };
  const chunkProgress = (percent: number, index: any, fileIndex: number) => {
    const { chunkList = [] } = fileList[fileIndex] || allFileList[fileIndex];
    const newChunkList = [].concat(chunkList);
    if (newChunkList[index]) {
      newChunkList[index].percent = percent;
    }
    if (index > 0) {
      for (let i = 0; i < index; i++) {
        newChunkList[i].percent = 100;
      }
    }
    const newFileList = allFileList;

    newFileList[fileIndex].chunkList = newChunkList;
    setfileList(newFileList);
  };
  // 分片组上传
  const uploadChunkGroup = async (group: any, groupIndex: number, fileIndex: number) => {
    let res = null;

    for (let i = 0; i < group.length; i++) {
      //循环最外面的数组
      for (let j = 0; j < group[i].length; j++) {
        const uploaded = window.localStorage.getItem(group[i][j].payload.fileId) || '';
        let lastNum = uploaded.split(',');
        if (lastNum.length) {
          if (parseInt(lastNum[lastNum.length - 1]) >= group[i][j].chunkIndex) {
            chunkProgress(100, group[i][j], fileIndex);
            continue;
          }
        }
        let formData = new FormData();
        Object.keys(group[i][j].payload).forEach(key => {
          formData.append(key, group[i][j].payload[key]);
        });
        formData.append('dir', dirName);
        res = await axios
          .post(`${window.location.protocol}//${window.location.host}/mica-api/api/v1/explore/pcap_chunk`, formData, {
            headers: {
              'content-type': 'multipart/form-data',
            },
            onUploadProgress: e => {
              const { loaded, total } = e;
              const percent = parseInt(((loaded / total) * 100).toFixed(2), 10);
              chunkProgress(percent, group[i][j].chunkIndex, fileIndex);
            },
          })
          .then(res => {
            if (res.status !== 200) {
              uploadError(new Error(res.data.message), fileIndex);
              return false;
            }
            if (res.data.flag) {
              setfileList(fileList);
              allFileList = allFileList;
              const uploadedChunk = window.localStorage.getItem(group[i][j].payload.fileId) || '';
              window.localStorage.setItem(
                group[i][j].payload.fileId,
                `${uploadedChunk}${uploadedChunk ? ',' : ''}${group[i][j].chunkIndex}`,
              );
              return true;
            } else {
              uploadError(new Error(res.data.message), fileIndex);
              return false;
              // break;
              // break;
            }
          });
        if (!res) {
          break;
        }
      }
    }
    if (res) {
      mergeFile(fileIndex);
    }
  };

  // 整体上传进度
  const caclPercent = (fileIndex: number) => {
    const { file, chunkList } = allFileList[fileIndex];
    if (!file || chunkList.length === 0) {
      return 0;
    }
    const loaded = chunkList
      .map((v: any) => v.size * (v.percent / 100))
      .reduce((acc: any, cur: any) => {
        return acc + cur;
      });

    const total = file.size;
    return parseInt(((loaded / total) * 100).toFixed(2), 10);
  };
  // 合并上传
  const mergeFile = (fileIndex: number) => {
    const { fileId, file, chunkList = [] } = allFileList[fileIndex];
    const payload = {
      fileName: file.name,
      tag: '',
      fileId,
      chunkList: [0, chunkList.length - 1],
      dir: dirName,
      type: dirType
    };
    mergeUpload(payload)
      .then(res => {
        if (res.flag) {
          window.localStorage.removeItem(fileId);
          const newFileList = [].concat(fileList);
          allFileList[fileIndex].uploadResult = 'success';
          setfileList(newFileList);
        } else {
          uploadError(new Error(res.message), fileIndex);
        }
      })
      .catch(err => {
        uploadError(err, fileIndex);
      });
  };
  const updateFailedFile = () => {
    // 失败的数组
    setlocalVisible(true);
    setuploading(false);
    const failedList = fileList.filter((v: any) => v.uploadResult === 'failed');
    // handleLocalOk()
    allFileList = allFileList;
    const fileNames = failedList.map((v: any) => v.file.name);
    axios
      .post(`${window.location.protocol}//${window.location.host}/mica-api/api/v1/explore/pcap_exist`, {
        fileNames,
        dir: category,
      })
      .then(res => {
        if (res.status === 200) {
          if (res.data.flag) {
            const { data } = res.data;
            if (data && data.length > 0) {
              Modal.confirm({
                title: '提示',
                cancelText: '否',
                okText: '是',
                content: (
                  <div>
                    <div>以下文件已存在，是否覆盖？</div>
                    {res.data.data.map((v: any) => (
                      <div key={v}>{v}</div>
                    ))}
                  </div>
                ),
                onOk: () => {
                  let fileList = failedList.map((v: any) => {
                    const file = v.file;
                    const fileName = file.name;
                    const fileId = md5(`${dirName}${fileName}${file.size}${file.lastModified}`);
                    const chunkList = createFileChunk(file) || [];
                    return {
                      file,
                      fileId,
                      chunkList,
                      uploadResult: 'pending',
                    };
                  });
                  setfileList(fileList);
                  setuploading(true);
                  uploadedCount = fileList.length;
                  fileList.forEach((v: any, index: any) => {
                    uploadFile(v, index, true);
                  });
                },
                onCancel: () => {
                  let fileList = failedList
                    .filter((v: any) => data.indexOf(v.file.name) < 0)
                    .map((v: any) => {
                      const file = v.file;
                      const fileName = file.name;
                      const fileId = md5(`${dirName}${fileName}${file.size}${file.lastModified}`);
                      const chunkList = createFileChunk(file) || [];
                      return {
                        file,
                        fileId,
                        chunkList,
                        uploadResult: 'pending',
                      };
                    });
                  setuploading(true);
                  setfileList(fileList);
                  uploadedCount = fileList.length;
                  Promise.allSettled(fileList.map((f: any, i: number) => uploadFile(f, i)));
                },
              });
            }
          } else {
            message.error(res.data.message);
          }
        } else {
          message.error('网络链接失败');
        }
      })
      .catch(err => {
        message.error(err.message);
      });
  };
  // 上传失败
  const uploadError = (err: { message: any }, fileIndex: number) => {
    message.error(err.message);
    const newFileList = [].concat(allFileList);
    newFileList[fileIndex].uploadResult = 'failed';
    setfileList(newFileList);
  };
  const handleLoaclCancel = () => {
    setreadyList([]);
    allFileList = [];
    setuploading(false);
    setlocalVisible(false);
  };
  const FTPhandleOk = async () => {
    if (!downDir) {
      message.error('清选择上传目录');
      return;
    }
    if (!downFileList || !downFileList.length) {
      message.error('请选择上传文件');
      return;
    }
    let arr = [],
      index;
    for (index = 0; index < downFileList.length;) {
      arr.push(downFileList.slice(index, (index += 5)));
    }
    for (let item of arr) {
      try {
        setdownloadloading(true);
        let res = await FTPDownload({
          dir: downDir,
          fileList: item,
        });
        if (!res.flag) {
          break;
        }
      } catch (err) {
        message.error('上传失败');
        return;
      }
    }

    setdownloadloading(false);
    setFTPVisible(false);
    message.success('上传成功');
  };

  const FTPhandleCancel = () => {
    setFTPVisible(false);
  };
  const addDir = () => {
    setdirVisible(true);
  };
  const getDownLoadDir = (value: string) => {
    setdownDir(value);
  };
  const getChooseFile = (value: []) => {
    setdownFileList(value);
  };
  const upNewDir = () => {
    upDir({ dir: dir, type: fileType }).then(res => {
      if (res.flag) {
        close();
        message.success('操作成功');
        getData();
      } else {
        message.error(res.message);
      }
    });
  };
  const close = () => {
    setdirVisible(false);
    setdir('');
  };
  let successList = allFileList.filter((v: { uploadResult: string }) => v.uploadResult === 'success');
  let failedList = allFileList.filter((v: { uploadResult: string }) => v.uploadResult === 'failed');
  let restLength = allFileList.length - successList.length - failedList.length;
  let uploadEnd = restLength === 0;
  return (
    <div>
      <Form form={form} onFinish={onSubmit} layout="inline">
        <Row>
          <Col span={6}>
            <Form.Item label="目录名称" name="foldName">
              <Input placeholder="请输入目录名称" />
            </Form.Item>
          </Col>
          <Col offset={10} span={8}>
            <Form.Item>
              <div className="group_btns">
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} className="margin_right_10">
                  搜索
                </Button>
                <Button className="margin_right_10" type="primary" ghost onClick={localUpload} icon={<UploadOutlined />}>
                  从本地上传
                </Button>
                <Button type="primary" onClick={FTPUpload} ghost icon={<ToTopOutlined />} className="margin_right_10">
                  从FTP服务器上传
                </Button>
                <Button className="margin_right_10" type="primary" onClick={addDir} ghost>
                  新建目录
                </Button>
                <Button type="primary" danger ghost onClick={handeDel}>
                  删除
                </Button>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        rowSelection={{
          type: selectionType,
          ...rowSelection,
        }}
        pagination={pagination}
        rowKey={record => record.dirName}
        columns={columns}
        dataSource={tableData}
      />
      <Modal
        title="从本地上传"
        visible={localVisible}
        onOk={handleLocalOk}
        destroyOnClose={true}
        onCancel={handleLoaclCancel}
      >
        <Local getFileList={setreadyList} ref={LocalForm} />
      </Modal>
      <Modal
        width="840px"
        title="从FTP上传"
        visible={FTPVisible}
        destroyOnClose={true}
        onOk={FTPhandleOk}
        okText="下载"
        onCancel={FTPhandleCancel}
      >
        <Spin spinning={downloadloading}>
          <FTP getDownLoadDir={getDownLoadDir} getChooseFile={getChooseFile} />
        </Spin>
      </Modal>
      <Modal
        title={uploadEnd ? '上传结束' : '文件正在上传...'}
        visible={uploading}
        maskClosable={false}
        keyboard={false}
        footer={
          uploadEnd
            ? [
              <div>
                <div>
                  本次上传结束，共上传
                  {readyList.length}个文件，
                  <span
                    style={{
                      color: '#52c41a',
                    }}
                  >
                    成功{successList.length}个
                  </span>
                  ，
                  <span
                    style={{
                      color: '#f5222d',
                    }}
                  >
                    失败{failedList.length}个
                  </span>
                </div>
                {failedList && failedList.length ? (
                  <div className={style.failStatus}>
                    失败文件:
                    {failedList.length &&
                      failedList.map(
                        (item: {
                          fileId: string | number | null | undefined;
                          file: {
                            name: React.ReactNode;
                          };
                        }) => {
                          return <p key={item.fileId}>{item.file.name}</p>;
                        },
                      )}
                  </div>
                ) : (
                  ''
                )}
                <div className={style.update_box}>
                  {failedList && failedList.length ? (
                    <div
                      style={{
                        marginRight: 5,
                      }}
                    >
                      <Button type="primary" onClick={updateFailedFile}>
                        重新上传失败文件
                      </Button>
                    </div>
                  ) : (
                    ''
                  )}
                  <div>
                    <Button type="primary" onClick={handleLoaclCancel}>
                      确定
                    </Button>
                  </div>
                </div>
              </div>,
            ]
            : null
        }
        closable={false}
        className={style.modal}
      >
        {allFileList.map((v: any, index: number) => {
          const fileName = v.file.name;
          let color = '#1a50a1';
          let type = 'loading';
          if (v.uploadResult === 'success') {
            color = '#52c41a';
            type = 'check-circle';
          } else if (v.uploadResult === 'failed') {
            color = '#f5222d';
            type = 'close-circle';
          }
          return (
            <div style={{ marginBottom: '10px' }} key={v.fileId}>
              <div>
                {fileName}

                {type === 'loading' ? (
                  <LoadingOutlined
                    style={{
                      marginLeft: '10px',
                      color,
                    }}
                  />
                ) : null}
                {type === 'check-circle' ? (
                  <CheckCircleOutlined
                    style={{
                      marginLeft: '10px',
                      color,
                    }}
                  />
                ) : null}
                {type === 'close-circle' ? (
                  <CloseCircleOutlined
                    style={{
                      marginLeft: '10px',
                      color,
                    }}
                  />
                ) : null}
              </div>
              <Progress percent={caclPercent(index)} status="active" />
            </div>
          );
        })}
      </Modal>
      <Modal title="新建目录" visible={dirVisible} onOk={upNewDir} onCancel={close}>
        <Form {...layout}>
          <Form.Item label="目录名称">
            <Input
              value={dir}
              onChange={value => {
                setdir(value.target.value);
              }}
            />
          </Form.Item>
          <Form.Item label="用途">
            <Select
              onChange={e => {
                setFileType(e);
              }}
            >
              <Option value={'1'} key={'1'} children={'流量包存放'} />
              <Option value={'2'} key={'2'} children={'普通文件存放'} />
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Offline;
