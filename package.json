{"name": "mica-admin", "version": "1.0.0", "private": true, "description": "", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "cross-env UMI_ENV=prod umi build", "docker-hub:build": "docker build  -f Dockerfile.hub -t  ant-design-pro ./", "docker-prod:build": "docker-compose -f ./docker/docker-compose.yml build", "docker-prod:dev": "docker-compose -f ./docker/docker-compose.yml up", "docker:build": "docker-compose -f ./docker/docker-compose.dev.yml build", "docker:dev": "docker-compose -f ./docker/docker-compose.dev.yml up", "docker:push": "npm run docker-hub:build && npm run docker:tag && docker push antdesign/ant-design-pro", "docker:tag": "docker tag ant-design-pro antdesign/ant-design-pro", "fetch:blocks": "node ./scripts/fetch-blocks.js", "functions:build": "netlify-lambda build ./lambda", "functions:run": "cross-env NODE_ENV=dev netlify-lambda serve ./lambda", "lint": "npm run lint:js && npm run lint:style", "lint-staged": "lint-staged", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src && npm run lint:style", "lint:js": "eslint --ext .js,.jsx,.ts,.tsx ./src", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write **/*/*.{ts,tsx,js,jsx,less,json}", "site": "npm run fetch:blocks && npm run functions:build && umi build", "dev": "cross-env UMI_UI=none UMI_ENV=local HOST=********** PORT=8002 umi dev", "start:no-mock": "cross-env MOCK=none umi dev", "ci": "npm run lint", "release": "standard-version", "init-flow": "bash ./scripts/gitFlow.sh"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"**/*.less": "npm run lint:style", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write", "git add"], "**/*.{js,jsx,ts,tsx}": "npm run lint:js"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/colors": "^3.1.0", "@ant-design/icons": "^4.0.6", "@ant-design/pro-layout": "^6.32.3", "@antv/data-set": "^0.11.1", "@sentry/browser": "^5.9.1", "@umijs/plugin-esbuild": "^1.0.0-beta.2", "@umijs/plugin-qiankun": "^2.18.2", "@umijs/preset-react": "^1.4.8", "antd": "4.24.12", "axios": "^0.21.1", "bizcharts": "^3.5.8", "body-parser": "^1.19.0", "classnames": "^2.2.6", "crypto-js": "^4.1.1", "echarts": "^5.0.1", "echarts-wordcloud": "^2.1.0", "hash.js": "^1.1.7", "highlight.js": "^9.15.10", "hoist-non-react-statics": "^3.3.0", "immutability-helper": "^3.1.1", "js-base64": "^3.7.2", "lodash": "^4.17.11", "lodash-decorators": "^6.0.1", "md5": "^2.3.0", "memoize-one": "^5.0.4", "moment": "^2.24.0", "numeral": "^2.0.6", "nzh": "^1.0.4", "omit.js": "^1.0.2", "path-to-regexp": "^3.0.0", "prismjs": "^1.23.0", "prop-types": "^15.7.2", "qrcode": "^1.4.4", "qs": "^6.7.0", "rc-animate": "^2.8.3", "rc-util": "^5.37.0", "react": "^16.8.6", "react-container-query": "^0.11.0", "react-copy-to-clipboard": "^5.0.1", "react-dnd": "^15.1.1", "react-dnd-html5-backend": "^15.1.2", "react-document-title": "^2.0.3", "react-dom": "^16.8.6", "react-json-view": "^1.21.3", "react-media": "^1.9.2", "react-media-hook2": "^1.0.5", "react-router": "^5.1.2", "react-use": "^13.24.0", "recharts": "1.8.5", "reconnecting-websocket": "^4.4.0", "redux": "^4.0.1", "swr": "^0.2.0", "umi": "^3.2.2", "umi-request": "^1.0.7", "update": "^0.7.4", "validator": "^12.2.0", "webpack-sentry-plugin": "^2.0.2"}, "devDependencies": {"@ant-design/colors": "^3.1.0", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@types/classnames": "^2.2.7", "@types/crypto-js": "^4.1.0", "@types/history": "^4.7.2", "@types/jest": "^24.0.13", "@types/lodash": "^4.14.133", "@types/numeral": "^0.0.26", "@types/qrcode": "^1.3.4", "@types/qs": "^6.5.3", "@types/react": "^16.8.19", "@types/react-document-title": "^2.0.3", "@types/react-dom": "^16.8.4", "@types/react-helmet": "^5.0.15", "@types/validator": "^12.0.1", "@typescript-eslint/eslint-plugin": "^2.12.0", "@typescript-eslint/eslint-plugin-tslint": "^2.12.0", "@typescript-eslint/parser": "^2.12.0", "@umijs/preset-ui": "^2.0.9", "@unocss/webpack": "^0.50.6", "antd-pro-merge-less": "^1.0.0", "babel-eslint": "^10.0.1", "chalk": "^2.4.2", "commitlint-config-cz": "^0.11.1", "cross-env": "^5.2.0", "cross-port-killer": "^1.1.1", "cz-conventional-changelog": "^3.0.2", "cz-customizable": "^6.1.0", "enzyme": "^3.9.0", "eslint": "^6.7.2", "eslint-config-airbnb": "^17.1.0", "eslint-config-airbnb-typescript": "^6.3.1", "eslint-config-prettier": "^4.3.0", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^3.1.1", "eslint-plugin-import": "^2.19.1", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.13.0", "eslint-plugin-react-hooks": "^3.0.0", "express": "^4.17.1", "gh-pages": "^2.0.1", "husky": "^2.3.0", "jest-puppeteer": "^4.2.0", "jsdom-global": "^3.0.2", "lerna": "^3.19.0", "less": "^3.9.0", "lint-staged": "^8.2.0", "mockjs": "^1.0.1-beta3", "mongodb": "^5.1.0", "netlify-lambda": "^1.4.13", "node-fetch": "^2.6.0", "prettier": "^1.18.2", "serverless-http": "^2.3.2", "slash2": "^2.0.0", "standard-version": "^6.0.1", "stylelint": "^10.0.1", "stylelint-config-css-modules": "^1.4.0", "stylelint-config-prettier": "^5.2.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^18.3.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^3.0.0", "tslint": "^5.17.0", "tslint-config-prettier": "^1.18.0", "tslint-eslint-rules": "^5.4.0", "tslint-react": "^4.0.0", "umi-types": "^0.5.7", "unocss": "^0.50.6", "webpack-theme-color-replacer": "^1.1.5"}, "engines": {"node": ">=10.0.0"}, "create-umi": {"ignoreScript": ["docker*", "functions*", "site", "generateMock"], "ignoreDependencies": ["netlify*", "serverless", "express"], "ignore": [".dockerignore", ".git", ".gitpod.yml", "CODE_OF_CONDUCT.md", "Dockerfile", "Dockerfile.*", "lambda", "LICENSE", "netlify.toml", "README.*.md", "azure-pipelines.yml", "docker", "create-umi"]}, "workspace": ["packages/*"], "templateVersion": "2.0.0"}