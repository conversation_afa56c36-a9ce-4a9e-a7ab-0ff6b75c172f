# -*- coding: utf-8 -*-
# @Time    : 2019-05-05 14:10
# <AUTHOR> hachi
# @File    : feature_content_manager.py
# @Software: PyCharm
"""
特征内容处理模块
"""
import os
import time
import json
import sqlite3
from flask import request
from flask_restful import Resource

from utils import param_check
from utils.json_format import J<PERSON><PERSON>ncoder

from celery_tasks.feature_db_update import db_update_feature
from config.config import NdrLog, DetectProg, KnowledgeNDR
from utils.database import MongoDB
from utils.logger import get_ndr_logger, LogToDb
from utils.param_check import Validator, check_db_file_input, check_license_expired
from utils.utils import flask_response, run_command
from api_1_0.utils.flask_log import ndr_log_to_box
from api_1_0.auth.auths import Authenticate

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class FeatureUpdateResource(Resource):
    """
    以 db 方式更新特征，并同步更新知识库
    """

    @staticmethod
    @check_license_expired(request)
    @check_db_file_input(request)
    def post():
        """
        :return:
        """
        start_time = int(time.time())
        try:
            name, _, user_ip = Authenticate().get_current_user()
            db = MongoDB("ndr")
            rule_version = db.find_one("knowledge_config", {})["rule_version"]
            # 判断升级版本是否正确：
            version_line = ""
            with open(KnowledgeNDR.TempPath + "config.json", 'r') as file:
                lines = file.readlines()
                for line in lines:
                    version_line = version_line + line
            version_dict = json.loads(version_line)
            version = version_dict["version"]
            update_mode = version_dict["update_model"]
            update_all = True if update_mode == "all" else False
            if version.count(".") != 1:
                LogToDb().log_to_db("rule_log", name, update_mode, start_time, int(time.time()), rule_version, version,
                                    "failed")
                return flask_response("版本有误,系统版本：%s,升级版本：%s" % (rule_version, version), False, {})

            if rule_version[0] != version[0]:
                LogToDb().log_to_db("rule_log", name, update_mode, start_time, int(time.time()), rule_version, version,
                                    "failed")
                return flask_response("版本有误,系统版本：%s,升级版本：%s" % (rule_version, version), False, {})

            if update_mode == "add":
                if float(rule_version) > float(version):
                    LogToDb().log_to_db("rule_log", name, update_mode, start_time, int(time.time()), rule_version,
                                        version, "failed")
                    return flask_response("版本有误,系统版本：%s,升级版本：%s" % (rule_version, version), False, {})
                # 校验规则是否有误
            with open(KnowledgeNDR.TempPath + "check.feature", 'w') as check_file:
                conn = sqlite3.connect(KnowledgeNDR.TempPath + "knowledge_feature.db")
                cursor = conn.cursor()
                res = cursor.execute(
                    "SELECT sid, author, vulName, vulType, cve, lockheedKillchainStage, lockheedKillchainCN,"
                    " lockheedKillchainEN, threatFlag, alterInfo, submitTime, is0day, ruleContent, attackIp,"
                    " victimIp, threatScore, appProto, status from vul_rules")
                for row in res:
                    feature = {
                        "sid": row[0],
                        "author": row[1],
                        "vulName": row[2],
                        "vulType": row[3],
                        "cve": row[4],
                        "lockheedKillchainStage": row[5],
                        "lockheedKillchainCN": row[6],
                        "lockheedKillchainEN": row[7],
                        "threatFlag": row[8],
                        "alterInfo": row[9],
                        "submitTime": row[10],
                        "is0day": row[11],
                        "featureContent": row[12],
                        "attackIp": row[13],
                        "victimIp": row[14],
                        "threatScore": row[15],
                        "appProto": row[16],
                        "updateTime": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "featureStatus": row[17],
                        "isCustomer": False,
                        "owner": ""
                    }
                    validator = Validator('feature_add_schema')
                    msg, err_value = validator.validate(feature)
                    if msg == "success":
                        # 生成解析规则临时文件
                        check_file.write(feature['featureContent'].strip() + '\n')
                    else:
                        LOG.error(row[0] + " sid parameter error: " + err_value)
                        LogToDb().log_to_db("rule_log", name, update_mode, start_time, int(time.time()), rule_version,
                                            version, "failed")
                        cursor.close()
                        return flask_response("特征参数校验有误，请重新检查", False, {"error": err_value})
                cursor.close()
            os.system("chown -R kslab:kslab %s" % KnowledgeNDR.TempPath)
            # 解析规则
            ret, msg = run_command(
                '%s --checkPath %s' % (DetectProg.Hdp_SigCompile, KnowledgeNDR.TempPath + "check.feature"))
            if ret:
                db_update_feature.delay(update_all, name, update_mode, rule_version, version, start_time)
                ndr_log_to_box(NdrLog.Type.OPERATE, "特征更新。")
            else:
                LOG.error("特征解析有误")
                LogToDb().log_to_db("rule_log", name, update_mode, start_time, int(time.time()), rule_version, version,
                                    "failed")
                return flask_response("特征解析有误，请重新检查", False, {"error": msg[-500:]})
        except Exception as err_msg:
            LOG.error(err_msg)
            ndr_log_to_box(NdrLog.Type.OPERATE, "特征更新失败：%s。" % str(err_msg))
            return flask_response("特征更新失败", False, {"error": str(err_msg)})
        return flask_response("", True, {})


class FeatureUpdateLog(Resource):
    def __init__(self):
        self.db = MongoDB("ndr")

    @param_check.check_flask_args(Validator("feature_update_log"), request)
    def get(self, **kwargs):
        page = int(kwargs["args"]["page"])
        page_size = int(kwargs["args"]["pageSize"])
        logs = []
        try:
            total = self.db.find("rule_log", {}).count()
            cols = self.db.find("rule_log", {}).sort([("start_time", -1)]).limit(page_size).skip(
                (page - 1) * page_size)
            for col in cols:
                col = JSONEncoder().encode(col)
                logs.append(json.loads(col))
            return_data = {"total": total,
                           "log": logs,
                           "page": page,
                           "pageSize": page_size}
            return flask_response("", True, return_data)
        except Exception as e:
            LOG.error("error: %s" % e)
            return flask_response("", False, {})


class FeatureVersion(Resource):
    def __init__(self):
        self.db = MongoDB("ndr")

    def get(self):
        data = {"version": ""}
        try:
            version = self.db.find_one("knowledge_config", {})["rule_version"]
            data["version"] = version
        except Exception as e:
            LOG.error("error: %s" % e)
            return flask_response("", False, {})
        return flask_response("", True, data)
