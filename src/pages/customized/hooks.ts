import { useState } from "react";
import { TablePagination } from "./types";

export function useLoadingData<T>(initData: T, initLoading = false) {
  const [loading, setLoading] = useState<boolean>(initLoading);
  const [data, setData] = useState<T>(initData)

  return {
    loading,
    data,
    setLoading,
    setData,
  }
}

export function usePaginationData(initPagination?: Partial<TablePagination>) {
  const [pagination, setPagination] = useState<TablePagination>({
    page: 1,
    pageSize: 10,
    total: 0,
    ...(initPagination || {}),
  })

  const updatePagination = (value: Partial<TablePagination>, cb?: () => void) => {
    setPagination((prev) => {
      cb?.()
      return {
        ...prev,
        ...value,
      }
    })
  }

  return { pagination, updatePagination }
}