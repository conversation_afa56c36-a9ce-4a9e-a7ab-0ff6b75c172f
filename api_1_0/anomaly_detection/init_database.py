# -*- coding: utf-8 -*-
# @Time    : 2024-08-05
# <AUTHOR> NDR Team
# @File    : init_database.py
# @Software: PyCharm

"""异常检测数据库初始化"""

from datetime import datetime, timedelta
from utils.database import MongoDB
from utils.logger import get_ndr_logger
from config.config import NdrLog

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


def init_anomaly_detection_database():
    """初始化异常检测数据库"""
    try:
        mongodb = MongoDB('ndr')
        
        # 创建异常检测任务集合索引
        LOG.info("创建异常检测任务集合索引...")
        mongodb.create_index('anomaly_tasks', [
            ('task_id', 1),
            ('status', 1),
            ('model_types', 1),
            ('target_type', 1),
            ('target_value', 1),
            ('task_type', 1),
            ('create_time', -1),
            ('last_run_time', -1)
        ])
        
        # 创建异常检测结果集合索引
        LOG.info("创建异常检测结果集合索引...")
        mongodb.create_index('anomaly_results', [
            ('result_id', 1),
            ('task_id', 1),
            ('model_type', 1),
            ('target_type', 1),
            ('target_value', 1),
            ('severity', 1),
            ('status', 1),
            ('confirmed', 1),
            ('detection_time', -1),
            ('create_time', -1)
        ])
        
        # 创建异常告警集合索引
        LOG.info("创建异常告警集合索引...")
        mongodb.create_index('anomaly_alerts', [
            ('alert_id', 1),
            ('result_id', 1),
            ('task_id', 1),
            ('model_type', 1),
            ('severity', 1),
            ('status', 1),
            ('handled_by', 1),
            ('alert_time', -1),
            ('create_time', -1)
        ])
        
        # 创建复合索引用于查询优化
        LOG.info("创建复合索引...")
        
        # 任务查询优化索引
        mongodb.create_index('anomaly_tasks', [
            ('status', 1),
            ('task_type', 1),
            ('create_time', -1)
        ])
        
        # 结果查询优化索引
        mongodb.create_index('anomaly_results', [
            ('model_type', 1),
            ('severity', 1),
            ('detection_time', -1)
        ])
        
        mongodb.create_index('anomaly_results', [
            ('target_value', 1),
            ('detection_time', -1)
        ])
        
        # 告警查询优化索引
        mongodb.create_index('anomaly_alerts', [
            ('status', 1),
            ('severity', 1),
            ('alert_time', -1)
        ])
        
        mongodb.create_index('anomaly_alerts', [
            ('model_type', 1),
            ('alert_time', -1)
        ])
        
        # 创建TTL索引用于自动清理旧数据（可选）
        LOG.info("创建TTL索引...")
        
        # 检测结果保留90天
        mongodb.create_index('anomaly_results', [
            ('create_time', 1)
        ], expireAfterSeconds=90*24*3600)
        
        # 告警保留180天
        mongodb.create_index('anomaly_alerts', [
            ('create_time', 1)
        ], expireAfterSeconds=180*24*3600)

        # 创建模型配置集合索引
        LOG.info("创建模型配置集合索引...")
        mongodb.create_index('model_configs', [
            ('config_id', 1),
            ('model_type', 1),
            ('is_default', 1),
            ('create_time', -1)
        ])

        LOG.info("异常检测数据库初始化完成")
        
    except Exception as e:
        LOG.error(f"异常检测数据库初始化失败: {str(e)}")
        raise e


def create_sample_data():
    """创建示例数据（用于测试）"""
    try:
        mongodb = MongoDB('ndr')
        
        # 创建示例检测任务
        sample_tasks = [
            {
                'task_id': 'sample_task_001',
                'name': '示例多模型检测任务',
                'description': '检测*************的多种异常行为',
                'model_types': ['time_series', 'baseline', 'repeat_conn'],
                'target_type': 'ip',
                'target_value': '*************',
                'task_type': 'scheduled',
                'time_config': {
                    'type': 'scheduled',
                    'schedule_type': 'daily',
                    'interval_hours': 24,
                    'time_range_hours': 1,
                    'enabled_days': [1,2,3,4,5,6,7]
                },
                'alert_config': {
                    'enabled': True,
                    'severity_threshold': 'medium'
                },
                'status': 'active',
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'run_count': 0,
                'creator': 'admin'
            },
            {
                'task_id': 'sample_task_002',
                'name': '示例历史回溯任务',
                'description': '回溯检测example.com域名的历史异常',
                'model_types': ['geo_anomaly', 'long_conn'],
                'target_type': 'domain',
                'target_value': 'example.com',
                'task_type': 'historical',
                'time_config': {
                    'type': 'historical',
                    'start_time': int((datetime.now() - timedelta(days=7)).timestamp() * 1000),
                    'end_time': int(datetime.now().timestamp() * 1000),
                    'time_window_hours': 2
                },
                'alert_config': {
                    'enabled': True,
                    'severity_threshold': 'high'
                },
                'status': 'active',
                'create_time': datetime.now(),
                'update_time': datetime.now(),
                'run_count': 0,
                'creator': 'admin'
            }
        ]
        
        for task in sample_tasks:
            # 检查是否已存在
            existing = mongodb.find_one('anomaly_tasks', {'task_id': task['task_id']})
            if not existing:
                mongodb.insert_one('anomaly_tasks', task)
                LOG.info(f"创建示例任务: {task['name']}")
        
        LOG.info("示例数据创建完成")
        
    except Exception as e:
        LOG.error(f"创建示例数据失败: {str(e)}")


if __name__ == '__main__':
    # 直接运行此脚本时执行初始化
    from datetime import datetime
    
    print("开始初始化异常检测数据库...")
    init_anomaly_detection_database()
    
    print("创建示例数据...")
    create_sample_data()
    
    print("初始化完成！")
