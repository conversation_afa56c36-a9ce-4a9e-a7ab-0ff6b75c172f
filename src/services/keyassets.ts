import request from '@/utils/request';
import { parseQueryString } from '@/utils/utils';


export const getData = (data: object) => {
    return request(`customize/assets${parseQueryString(data)}`, {
        method: 'get',
        data
    })
}

export const delData = (data: { target: any; }) => {
    return request('customize/assets', {
        method: 'delete',
        data
    })
}

export const addData = (data: object)=>{
    return request ('customize/assets',{
        method:'post',
        data
    })
}

export const editData = (data: object)=>{
     return request('customize/assets',{
         method:'put',
         data
     })
}