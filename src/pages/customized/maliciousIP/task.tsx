import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ltip,
  Popconfirm,
  Spin,
  Select,
  Table,
  Form,
  DatePicker,
  Modal,
  Input,
  message,
  Row,
  Col,
  Collapse,
  Checkbox,
} from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import {
  getIPtask,
  addIPtask,
  editIptask,
  getHiveListData,
  getResultData,
  downloadResultData,
  exportReportFile,
  exportLogsFile,
  setFlag,
  getLogsData,
  getChooseReportData,
} from '@/services/collect';
import moment from 'moment';
import { data_types, ftpHidden } from '@/utils/enumList';

import store from '../store';
import { ellipsis } from '@/utils/utils';
const { Panel } = Collapse;
const Task = () => {
  const [analysisVisible, setanalysisVisible] = useState(false);
  const [selectionType, setSelectionType] = useState<'checkbox' | 'radio'>('checkbox');
  const [selectedRowKeys, setselectedRowKeys] = useState([]);
  const [form] = Form.useForm();
  const [taskForm] = Form.useForm();
  const [tableData, settableData] = useState([]);
  const [loading, setloading] = useState(false);
  const [total, settotal] = useState(0);
  const [params, setparams] = useState({ page: 1, pageSize: 10 });

  const [isAdd, setisAdd] = useState(false);
  const [isModalOpen, setisModalOpen] = useState(false);
  const [taskDetail, settaskDetail] = useState<any>({});
  const [sourceDataList, setsourceDataList] = useState<any>([]);
  const [resultVisible, setresultVisible] = useState(false);
  const [resultParams, setresultParams] = useState({ name: '', page: 1, pageSize: 10 });
  const [resultData, setresultData] = useState<any>([]);
  const [resultTotal, setresultTotal] = useState(0);
  const [showlog, setshowlog] = useState(false);
  const [logData, setlogData] = useState([]);
  const [logTotal, setlogTotal] = useState(0);
  const [logParams, setlogParams] = useState<any>({ page: 1, pageSize: 10, id: '' });
  const [customData, setcustomData] = useState({});
  const [customForm] = Form.useForm();
  const customFormRef = useRef(null);
  const [customColumns, setcustomColumns] = useState([
    {
      title: '序号',
      dataIndex: 'id',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'id']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },

    {
      title: `公司名称`,
      dataIndex: 'src_ip_city_name',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'src_ip_city_name']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '涉事设施',
      dataIndex: 'inv_facilities',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'inv_facilities']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },

    {
      title: '涉事IP',
      dataIndex: 'inv_ip',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'inv_ip']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },

    {
      title: '涉事端口',
      dataIndex: 'src_port',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'src_port']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '攻击者所属APT组织名',
      dataIndex: 'attack_apt',
      width: 180,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'attack_apt']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '攻击者IP',
      dataIndex: 'attack_ip',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'attack_ip']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '攻击者端口',
      dataIndex: 'attack_port',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'attack_port']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '攻击协议',
      dataIndex: 'attack_proto',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'attack_proto']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '联通次数',
      dataIndex: 'conn_cnt',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'conn_cnt']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },

    {
      title: '上行流量(字节)',
      dataIndex: 'upstream',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'upstream']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '下行流量(字节)',
      dataIndex: 'downstream',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'downstream']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '总流量大小',
      dataIndex: 'total_flow',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        let value = recoed.upstream + recoed.downstream;
        return (
          <Form.Item>
            <Input disabled value={value} style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '研判依据(分析过程)',
      dataIndex: 'analysis_process',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'analysis_process']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'start_time']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      width: 160,
      render: (t: any, recoed: any, index: any) => {
        return (
          <Form.Item name={['data', index, 'end_time']}>
            <Input style={{ border: 'none' }} />
          </Form.Item>
        );
      },
    },
    {
      title: ``,
      isNew: false,
      dataIndex: 'filter',
      fixed: 'right',
      width: 50,
      filterDropdown: (props: any) => (
        <div style={{ padding: 8, height: 400, overflow: 'auto' }}>
          <div>
            <Checkbox.Group
              value={props.selectedKeys}
              onChange={e => {
                props.setSelectedKeys(e);
              }}
            >
              {columnsHidden.map((item: any) => {
                return (
                  <div key={item.title}>
                    <Checkbox value={item.title}>{item.title}</Checkbox>
                  </div>
                );
              })}
            </Checkbox.Group>
          </div>
          <Button
            type="primary"
            size="small"
            style={{
              width: 60,
              marginRight: 8,
            }}
            onClick={() => {
              handleFilter(props);
            }}
          >
            确定
          </Button>
          <Button
            size="small"
            onClick={() => {
              handleReset();
              props.setSelectedKeys([]);
              props.clearFilters();
            }}
            style={{ width: 60 }}
          >
            重置
          </Button>
        </div>
      ),
    },
  ]);

  useEffect(() => {
    searchList();
  }, [params]);
  useEffect(() => {
    if (resultParams.name) {
      getResultList();
    }
  }, [resultParams]);
  useEffect(() => {
    if (logParams.id) {
      showMoreLog();
    }
  }, [logParams]);
  useEffect(() => {
    store.dispatch({ type: 'changeColumns', value: customColumns });
  }, []);

  useEffect(() => {
    setTimeout(() => {
      customForm.resetFields();
    });
  }, [customData]);

  // 隐藏字段选择
  const handleFilter = (value: any) => {
    let col: any = store.getState().columns;
    const column = col.filter((item: any) => !item.isNew);
    value.selectedKeys.forEach((item: any, index: any) => {
      column.splice(-1, 0, {
        title: item,
        dataIndex: DateMap.get(item),
        isNew: true,
        width: 160,
        render: (t: any, recoed: any, index: any) => {
          return (
            <Form.Item name={['data', index, DateMap.get(item)]}>
              <Input style={{ border: 'none' }} />
            </Form.Item>
          );
        },
      });
    });
    setcustomColumns(column);
    store.dispatch({
      type: 'changeColumns',
      value: column,
    });

    value.confirm();
  };
  const handleReset = () => {
    let arr = store.getState().columns;
    arr = arr.filter((item: any) => {
      return item.isNew === false;
    });
    setcustomColumns([...arr]);
  };
  const handleOk = () => {
    taskForm.validateFields().then((value: any) => {
      if (isAdd) {
        addIPtask(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            taskForm.resetFields();
            setisModalOpen(false);

            searchList();
          } else {
            message.error(res.message);
          }
        });
      } else {
        value.action = 'edit';
        editIptask(value).then(res => {
          if (res.flag) {
            message.success(res.message);
            taskForm.resetFields();
            setisModalOpen(false);
            searchList();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };
  const handleCancel = () => {
    taskForm.setFieldsValue({});
    settaskDetail({});
    taskForm.resetFields();
    setisModalOpen(false);
  };
  const editTask = (data: any) => {
    settaskDetail(data);

    taskForm.setFieldsValue(data);
    setisAdd(false);
    setisModalOpen(true);
  };
  const task_types = [
    {
      label: '回溯任务',
      value: '1',
    },
    {
      label: '追踪任务',
      value: '2',
    },
  ];

  let columnsHidden = [];
  const DateMap = new Map([
    ['涉事域名', 'inv_domain'],
    ['涉事邮箱及账号', 'inv_mail_acnt'],
    ['涉事邮箱持有人', 'inv_mail_owner'],
    ['日期', 'datetime'],
    ['分组', 'group'],
    ['APT组织内部编号', 'apt_number'],
    ['攻击者域名', 'attack_domain'],
    ['攻击钓鱼邮箱', 'phishing_email'],
    ['攻击者钓鱼文件名', 'phishing_file'],
    ['攻击者URL', 'attack_url'],
    ['攻击者恶意样本', 'attack_samples'],
    ['攻击者IOC', 'attack_ioc'],
    ['事件模型规则', 'event_model_rule'],
    ['事件识别原因', 'event_ident_cause'],
    ['事件告警分值', 'event_alert_score'],
    ['事件告警ID', 'event_alert_id'],
    ['事件模型类型', 'event_model_type'],
    ['原始线索来源类型', 'org_lead_type'],
    ['涉事报文规则名称', 'inv_pcap_rule'],
    ['攻击者国家', 'attack_country'],
    ['攻击来源', 'attack_source'],
    ['行业', 'industry'],
    ['业务类型', 'business_type'],
    ['开始时间', 'start_time'],
    ['结束时间', 'end_time'],
    ['事件确认状态', 'event_confirm'],
    ['事件通报状态', 'event_notif'],
    ['涉事报文规则配置人', 'inv_rule_cfg_person'],
    ['涉事IOC配置人', 'inv_ioc_cfg_person'],
    ['事件发现人员', 'event_discover_person'],
    ['被攻击性质', 'attack_type'],
    ['扩展类型', 'extention_type'],
    ['事件唯一ID', 'event_uniq_id'],
    ['研判通报人员', 'report_person'],
    ['攻击来源方向系数', 'attack_source_dir'],
    ['间隔月数', 'interval_months'],
    ['攻击目标重要系数', 'attack_importance_coe'],
    ['攻击类型系数', 'attack_type_coe'],
    ['对外报送系数', 'external_report_coe'],
    ['领导批示系', 'leader_instruct_coe'],
    ['监测通报人员', 'monitor_person'],
    ['线索(模型)配置系数', 'clue_cfg_coe'],
    ['线索来源系数', 'clue_source_coe'],
    ['线索配置人员', 'clue_cfg_person'],
    ['取证分析系数', 'forensic_analysis_coe'],
    ['取证分析人员', 'forensic_analysis_person'],
    ['系统维护系数', 'system_maint_coe'],
    ['系统维护人员', 'system_maint_person'],
    ['攻击者body', 'attack_body'],
    ['JARM', 'jarm'],
    ['JA3S', 'ja3s'],
  ]);
  DateMap.forEach((value, key) => {
    columnsHidden.push({ title: key, dataIndex: value });
  });
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      render: (t: string) => {
        return '回溯任务';
      },
      align: 'center',
    },

    {
      title: '任务时间',
      dataIndex: 'start_time',
      align: 'center',
      render: (t: string, record: any) => {
        return (
          <div>
            <p>开始时间:{t}</p>
            <p>结束时间:{record.end_time}</p>
          </div>
        );
      },
    },

    {
      title: '数据源',
      dataIndex: 'source_name',
      align: 'center',
    },

    {
      title: '日志类型',
      dataIndex: 'data_types',
      align: 'center',
    },
    {
      title: '目标ioc(规则)',
      dataIndex: 'target_ioc',
      align: 'center',
      render: (t: any[]) => {
        return (
          <Tooltip placement="bottom" title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'notice',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (t: any) => {
        return (
          <div className="w-22">
            <div style={{ color: '#fc0100' }} className="text-left ws-nowrap">
              {t === 'failed' ? '失败' : ''}
            </div>
            <div className="text-left ws-nowrap">{t === 'running' ? '执行中' : ''}</div>
            <div style={{ color: '#00cc00' }} className="text-left ws-nowrap">
              {t === 'success' ? '已完成' : ''}
            </div>
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 214,
      render: (t: any, record: any) => {
        return (
          <div>
            <Button
              onClick={() => {
                editTask(record);
              }}
              style={{ color: '#1890ff' }}
              type="link"
            >
              编辑
            </Button>
            <Popconfirm title="确定删除吗？" okText="确定" cancelText="取消" onConfirm={() => deleteTask(record)}>
              <Tooltip>
                <Button type="link" danger>
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>
            {record.status !== 'running' && (
              <Button
                onClick={() => {
                  handleStart(record);
                }}
                style={{ color: '#1890ff' }}
                type="link"
              >
                开始
              </Button>
            )}
            {record.status === 'running' && (
              <Button
                onClick={() => {
                  handleStop(record);
                }}
                style={{ color: '#1890ff' }}
                type="link"
              >
                结束
              </Button>
            )}
            <Button
              style={{ color: '#39e600' }}
              onClick={() => {
                showResult(record);
              }}
              type="link"
            >
              查看分析结果
            </Button>
          </div>
        );
      },
    },
  ];
  const logColumns = [
    {
      title: '时间',
      dataIndex: 'c_time',
      align: 'center',
    },
    {
      title: '源IP',
      dataIndex: 'c_src_ipv4',
      align: 'center',
    },
    {
      title: '源端口',
      dataIndex: 'c_src_port',
      align: 'center',
    },
    {
      title: '目的IP',
      dataIndex: 'c_dest_ipv4',
      align: 'center',
    },
    {
      title: '目的端口',
      dataIndex: 'c_dest_port',
      align: 'center',
    },
    {
      title: 'URL',
      dataIndex: 'url',
      align: 'center',
      render: (t: any[]) => {
        return (
          <Tooltip placement="bottom" title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: 'Body',
      dataIndex: 'body',
      align: 'center',
      render: (t: any[]) => {
        return (
          <Tooltip placement="bottom" title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: 'Domain',
      dataIndex: 'domain',
      align: 'center',
      render: (t: any[]) => {
        return (
          <Tooltip placement="bottom" title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: 'Certificate_Ver',
      dataIndex: 'cert_ver',
      align: 'center',
      render: (t: any[]) => {
        return (
          <Tooltip placement="bottom" title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: 'Certificate_Sni',
      dataIndex: 'cert_sni',
      align: 'center',
      render: (t: any[]) => {
        return (
          <Tooltip placement="bottom" title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: 'Certificate',
      dataIndex: 'cert',
      align: 'center',
      render: (t: any[]) => {
        return (
          <Tooltip placement="bottom" title={t.toString()}>
            {ellipsis(t.toString())}
          </Tooltip>
        );
      },
    },
    {
      title: '上行流量',
      dataIndex: 'c_up_bytes',
      align: 'center',
    },
    {
      title: '下行流量',
      dataIndex: 'c_down_bytes',
      align: 'center',
    },
  ];
  const resultColumns = [
    {
      title: '源IP',
      dataIndex: 'src_ip',
      align: 'center',
      render: (t: string, record: any) => {
        return (
          <div>
            <span>{t}</span>
            <p>单位:{record.src_ip_city_name}</p>
          </div>
        );
      },
    },
    {
      title: '源端口',
      dataIndex: 'src_port',
      align: 'center',
      render: (t: any) => {
        if (t) {
          return (
            <Tooltip placement="bottom" title={t.toString()}>
              {ellipsis(t.toString())}
            </Tooltip>
          );
        }
      },
    },
    {
      title: '目的IP',
      dataIndex: 'dst_ip',
      align: 'center',
      render: (t: string, record: any) => {
        return (
          <div>
            <span>{t}</span>
            <p>单位:{record.dst_ip_city_name}</p>
          </div>
        );
      },
    },
    {
      title: '目的端口',
      dataIndex: 'dst_port',
      align: 'center',
      render: (t: any) => {
        if (t) {
          return (
            <Tooltip placement="bottom" title={t.toString()}>
              {ellipsis(t.toString())}
            </Tooltip>
          );
        }
      },
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      align: 'center',
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      align: 'center',
    },
    {
      title: 'URL',
      dataIndex: 'url',
      align: 'center',
      render: (t: any) => {
        return (
          <Tooltip placement="bottom" title={t}>
            {ellipsis(t)}
          </Tooltip>
        );
      },
    },
    {
      title: 'Body',
      dataIndex: 'body',
      align: 'center',
      render: (t: any) => {
        return (
          <Tooltip placement="bottom" title={t}>
            {ellipsis(t)}
          </Tooltip>
        );
      },
    },
    {
      title: '联通次数',
      dataIndex: 'connections',
      align: 'center',
      width: 100,
      render: (t: string, record: any) => {
        return (
          <span
            onClick={() => {
              setlogParams({ ...logParams, id: record.id });
            }}
            style={{ cursor: 'pointer', color: '#0099ff', borderBottom: '1px solid #0099ff' }}
          >
            {t}
          </span>
        );
      },
    },
    {
      title: '上行流量',
      dataIndex: 'upstream',
      align: 'center',
    },
    {
      title: '下行流量',
      dataIndex: 'downstream',
      align: 'center',
    },
  ];

  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      setparams({ ...params, ...value });
    });
  };
  const searchList = () => {
    let value = { ...params };
    getIPtask(value).then(res => {
      if (res.flag) {
        setloading(false);
        settotal(res.data.total);
        settableData(res.data.detail);
      } else {
        message.error(res.message);
      }
    });
  };

  const handleAdd = () => {
    getHiveListData({ page: 1, pageSize: 99 }).then(res => {
      if (res.flag) {
        setsourceDataList([...sourceDataList, ...res.data.cols]);
        settaskDetail({});
        taskForm.resetFields();
        setisAdd(true);
        setisModalOpen(true);
      } else {
        message.error(res.message);
      }
    });
  };
  const handleStart = (data: any) => {
    data.action = 'start';
    editIptask(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleStop = (data: any) => {
    data.action = 'stop';
    editIptask(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const handleDownload = async (name: string) => {
    const res = await downloadResultData({ name: name });

    let blob = new Blob([res], {
      type: 'application/pdf;chartset=UTF-8',
    });
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = '回溯任务结果.xlsx';
    a.click();

    URL.revokeObjectURL(a.href);
    document.body.removeChild(a);
  };

  const getResultList = () => {
    getResultData(resultParams).then(res => {
      if (res.flag) {
        let arr: ((prevState: never[]) => never[]) | number[] = [];
        res.data.detail.forEach((item: any, index: number) => {
          if (item.flag) {
            arr.push(index);
          }
        });
        setresultTotal(res.data.count);
        setresultData(res.data.detail);
        setresultVisible(true);
        console.log(arr);
        setselectedRowKeys(arr);
      } else {
        message.error(res.message);
      }
    });
  };
  const showResult = (data: any) => {
    setresultParams({ ...resultParams, name: data.name });
  };
  const showMoreLog = () => {
    getLogsData({ ...logParams }).then(res => {
      if (res.flag) {
        setlogTotal(res.data.count);
        setshowlog(true);
        setlogData(res.data.detail);
      }
    });
  };

  const deleteTask = (data: any) => {
    data.action = 'delete';
    editIptask(data).then(res => {
      if (res.flag) {
        message.success(res.message);
        searchList();
      } else {
        message.error(res.message);
      }
    });
  };
  const afterClose = () => {
    console.log(222);
    console.log(1111, customForm);
  };
  const showInfo = async () => {
    if (!selectedRowKeys.length) {
      message.error('请选择数据');
      return;
    }
    let reqData: never[] = [];

    getChooseReportData({ name: resultParams.name }).then(res => {
      if (res.flag) {
        res.data.result.forEach((item: any) => {
          item.attack_body = item.body;
          item.attack_url = item.url;
          item.conn_cnt = item.connections;
          item.inv_ip = item.src_ip;
          item.attack_ip = item.dst_ip;
          item.attack_port = item.dst_port;
        });
        setcustomData({ data: res.data.result });

        setTimeout(() => {
          setanalysisVisible(true);
        });
      }
    });
  };
  const closeAnalysis = () => {
    customForm.resetFields();
    setcustomData({ data: [] });
    setanalysisVisible(false);
  };
  const exportReport = async () => {
    customForm.validateFields().then(async (value: any) => {
      value.data.forEach((item: any) => {
        item.downstream = item.downstream ? parseInt(item.downstream) : 0;
        item.upstream = item.upstream ? parseInt(item.upstream) : 0;
        item.total_flow = item.downstream + item.upstream;
        item.attack_port = item.attack_port ? item.attack_port : '';
        item.company_name = item.src_ip_city_name ? item.src_ip_city_name : '';
        item.attack_apt = item.attack_apt ? item.attack_apt : '';
        item.total_flow = item.total_flow ? item.total_flow : '';
        item.datetime = item.datetime ? item.datetime : '';
        item.attack_proto = item.attack_proto ? item.attack_proto : '';
      });
      const res = await exportReportFile(value);

      let blob = new Blob([res], {
        type: 'application/pdf;chartset=UTF-8',
      });
      const a = document.createElement('a');
      a.href = URL.createObjectURL(blob);
      a.download = '研判报告.zip';
      a.click();

      URL.revokeObjectURL(a.href);
      document.body.removeChild(a);
    });
  };
  const exportLogsData = async () => {
    const res = await exportLogsFile({ id: logParams.id });

    let blob = new Blob([res], {
      type: 'application/pdf;chartset=UTF-8',
    });
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = 'dada_detail_srcip_dstip.xlsx';
    a.click();

    URL.revokeObjectURL(a.href);
    document.body.removeChild(a);
  };
  // 分页change事件
  const pagination = {
    total: total,
    showSizeChanger: true,
    current: params.page || 1,
    pageSize: params.pageSize,
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setparams({ ...params, page, pageSize });
    },
  };
  const resultPagination = {
    total: resultTotal,
    showSizeChanger: true,
    current: resultParams.page || 1,
    pageSize: resultParams.pageSize,
    showTotal: (total: number) => `共${resultTotal}条`,
    onChange(page: number, pageSize: any) {
      setresultParams({ ...resultParams, page, pageSize });
    },
  };
  const logsPagination = {
    total: logTotal,
    showSizeChanger: true,
    current: logParams.page || 1,
    pageSize: logParams.pageSize,
    showTotal: (total: number) => `共${logTotal}条`,
    onChange(page: number, pageSize: any) {
      setlogParams({ ...logParams, page, pageSize });
    },
  };
  const formItemLayout = {
    labelCol: {
      span: 4, // * ≥576px
    },
    wrapperCol: {},
  };
  const rowSelection = {
    onChange: async (data: []) => {
      if (data.length < selectedRowKeys.length) {
        let uniqueValues = selectedRowKeys.filter(item => !data.includes(item));

        for (let i = 0; i < uniqueValues.length; i++) {
          await setFlag({
            id: resultData[uniqueValues[i]].id,
            flag: false,
          });
        }
      } else if (data.length > selectedRowKeys.length) {
        let uniqueValues = data.filter(item => !selectedRowKeys.includes(item));
        for (let i = 0; i < uniqueValues.length; i++) {
          await setFlag({
            id: resultData[uniqueValues[i]].id,
            flag: true,
          });
        }
      }
      await getResultList();
    },
    selectedRowKeys,
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form {...formItemLayout} layout="inline" initialValues={params} form={form} className="!flex gap-3 !mt-5">
            <Row>
              <Col span={8}>
                <Form.Item label="任务名称:" name="name">
                  <Input />
                </Form.Item>
              </Col>
              <Col offset={12} span={4}>
                <div style={{ textAlign: 'right', padding: 10 }}>
                  <Button
                    style={{ margin: '0 10px' }}
                    className="searchBtn"
                    type="primary"
                    onClick={onSubmit}
                    icon={<SearchOutlined />}
                  >
                    搜索
                  </Button>
                  <Button style={{ color: '#1890ff' }} type="dashed" onClick={handleAdd}>
                    新建
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>

      <Spin spinning={loading}>
        <Table rowKey={(r: any, i: any) => i} dataSource={tableData} columns={columns} pagination={pagination} />
      </Spin>

      <Modal
        forceRender
        destroyOnClose
        width={1200}
        title={isAdd ? '新建任务' : '编辑任务'}
        visible={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form preserve={false} initialValues={taskDetail} form={taskForm}>
          <Form.Item name="name" label="任务名称" rules={[{ required: true, message: '请填写!' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="task_type" label="任务类型" rules={[{ required: true, message: '请填写!' }]}>
            <Select>
              {task_types.map(item => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="source_name" label="数据源" rules={[{ required: true, message: '请填写!' }]}>
            <Select>
              {sourceDataList.map((item: any) => {
                return (
                  <Select.Option key={item.source_name} value={item.source_name}>
                    {item.source_name}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="data_types" label="日志类型" rules={[{ required: true, message: '请填写!' }]}>
            <Select allowClear>
              {data_types.map(item => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          {/* <Form.Item name="time" label="任务时间" rules={[{ required: true, message: '请填写!' }]}>
            <DatePicker.RangePicker
              allowClear
              className="w-100%"
              ranges={{
                最近1分钟: [moment(new Date()).add(-1, 'minute'), moment()],
                最近5分钟: [moment(new Date()).add(-5, 'minute'), moment()],
                最近30分钟: [moment(new Date()).add(-30, 'minute'), moment()],
                最近1小时: [moment(new Date()).add(-1, 'hour'), moment()],
                最近4小时: [moment(new Date()).add(-4, 'hour'), moment()],
                最近12小时: [moment(new Date()).add(-12, 'hour'), moment()],
                最近1天: [moment(new Date()).add(-1, 'day'), moment()],
              }}
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item> */}
        </Form>
      </Modal>
      <Modal
        forceRender
        width="100%"
        height="100%"
        onCancel={() => {
          setresultVisible(false);
        }}
        onOk={() => {
          setresultVisible(false);
        }}
        visible={resultVisible}
        title={[
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>分析结果</div>
            <div>
              <Button
                style={{ color: '#1890ff', marginRight: 10 }}
                type="dashed"
                onClick={() => {
                  handleDownload(resultParams.name);
                }}
              >
                结果下载
              </Button>
              <Button style={{ color: '#fff', marginRight: 30 }} type="primary" onClick={showInfo}>
                研判分析
              </Button>
            </div>
          </div>,
        ]}
      >
        <Table
          rowSelection={{
            type: selectionType,
            ...rowSelection,
          }}
          columns={resultColumns}
          rowKey={(r: any, i: any) => i}
          dataSource={resultData}
          pagination={resultPagination}
        />
      </Modal>
      {analysisVisible ? (
        <Modal
          forceRender
          destroyOnClose
          visible={analysisVisible}
          width="100%"
          height="100%"
          style={{
            maxWidth: 'none',
          }}
          afterClose={afterClose}
          onCancel={closeAnalysis}
          onOk={() => {
            setanalysisVisible(false);
          }}
          title={[
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>研判分析</div>
              <div>
                <Button style={{ color: '#fff', marginRight: 30 }} type="primary" onClick={exportReport}>
                  导出报告
                </Button>
              </div>
            </div>,
          ]}
        >
          <Form ref={customFormRef} preserve={false} initialValues={customData} form={customForm} component={false}>
            <Form.Item>
              <Table
                rowKey={(r: any, i: any) => i}
                scroll={{ x: 'max-content' }}
                style={{ minWidth: 700 }}
                pagination={false}
                dataSource={customData.data}
                columns={customColumns}
              />
            </Form.Item>
          </Form>
        </Modal>
      ) : null}

      <Modal
        forceRender
        width={1400}
        style={{
          maxWidth: 'none',
        }}
        onCancel={() => {
          setshowlog(false);
        }}
        onOk={() => {
          setshowlog(false);
        }}
        title={[
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>原始日志详情</div>
            <div>
              <span
                style={{ borderBottom: '1px solid #0099ff', color: '#1890ff', marginRight: 30, cursor: 'pointer' }}
                onClick={exportLogsData}
              >
                详情数据下载
              </span>
            </div>
          </div>,
        ]}
        visible={showlog}
      >
        <Table rowKey={(r: any, i: any) => i} pagination={logsPagination} columns={logColumns} dataSource={logData} />
      </Modal>
    </div>
  );
};
export default Task;
