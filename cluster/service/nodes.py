#!/usr/bin/env python3
# coding:utf-8

import socket
from cluster.settings import CLUSTER_NAME
from cluster.utils.logger import get_logger
from cluster.utils.client import connect, discover

logger = get_logger(__name__)


class NodeInfo:

    def __init__(self, cluster_name=CLUSTER_NAME, extra=None):
        self.cluster_name = cluster_name
        self.extra = extra

    def discover_nodes(self):
        addrs = discover(self.cluster_name)
        if self.extra:
            addrs = addrs + self.extra
        return addrs

    def show_nodes(self):
        node_list = []
        addrs = self.discover_nodes()
        if not addrs:
            return node_list

        for host, port in addrs:
            node_list.append({
                'host': host,
                'port': port
            })
        return node_list

    def node_infos(self):
        node_infos = []
        addrs = self.discover_nodes()
        if not addrs:
            return node_infos
        for host, port in addrs:
            conn = None
            try:
                conn = connect(host, port)
            except socket.error:
                logger.error("server (%s:%s) is down", host, port)
            if not conn:
                continue

            try:
                prod_version = conn.root.get_version()
                device_id = conn.root.get_device_id()
                node_name = conn.root.get_node("node_name")
                node_id = conn.root.get_node("node_id")
                node_role = conn.root.get_node("node_role")
                master_ip = conn.root.get_node("master_ip")
                is_passive = conn.root.get_node("is_passive")
            except:
                logger.info("%s:%s connect faild", host, port)
                continue
            node_infos.append({
                "host": host,
                "port": port,
                "node_id": node_id,
                "version": prod_version,
                "device_id": device_id,
                "node_name": node_name,
                "node_role": node_role,
                "master_ip": master_ip,
                "is_passive": is_passive
            })
        return node_infos