{
	"type": "object",
	"required": ["action"],
	"properties": {
		"action": {
			"type": "string",
			"enum": ["update", "stop", "start"]
		},
		"taskName": {
			"type": "string",
			"default": "",
			"pattern": r"^[A-Za-z0-9_\-\u4e00-\u9fa5]{1,255}$"
		},
		"dir_name": {
			"type": "string",
			"default": ""
		},
		"curFeatureGrpList": {
			"type": "array",
			"items": {
			    "type": "string"
			  }
		},
		"sourceData": {
            "type": "string",
            "default": ""
        },
		"dealWithAll": {
			"type": "boolean",
			"default": False,
			"enum": [True, False]
        },
		"executeNow": {
            "type": "boolean",
			"default": False,
			"enum": [True, False]
        },
        "schedule": {
            "type": "object",
            "properties": {
                "period": {
                    "type": "string",
                    "default": "",
                    "pattern": r"(hour|day|week|month)"
                },
                "baseTime": {
                    "type": "string",
                    "default": ""
                },
                "baseDate": {
                    "type": "number",
                    "pattern": r"^([12][0-9]|3[0-1]|[1-9])$",
                    "default": 1
                },
            }
        }
	}
}