import { Button, Modal, Tooltip, notification, Space} from 'antd';
import cn from 'classnames';
import React from 'react';
import DetailTable from './DetailTable';
import { TYPE_MAP, colRenderer } from './shared';
import { download, downloadEvent } from './download';
import { get } from 'lodash';
import { threatFlagNameMap } from '@/utils/enumList';
import { THREAT_LEVEL } from '@/utils/constants';


export default function LogDetail({ type, value }) {
    const [modalVisible, setModalVisible] = React.useState(false);

    const columns = [
        [
            { title: '源IP', dataIndex: ['flow', 'src_ip'] },
            { title: '地理位置', alias: "源IP地理位置", dataIndex: 'srcIpGeoInfo', render: colRenderer.area },
            { title: '源端口', dataIndex: ['flow', 'src_port'] },
        ],
        [
            {
                title: '目的IP',
                dataIndex: ['flow', 'dst_ip'],
                render(value: string, record:any) {
                    const [ip, tag] = value.split(',');
                    return (
                        <Space key={ip} align="center" size={8}>
                            <span>{ip}</span>
                            {tag && <span className="tag c-t4" style={{marginTop:'3px'}}>{tag}</span>}
                            {record?.dstIpMbInfo?.assets && <span className="tag c-t4" style={{marginTop:'3px'}}>重点资产</span>}
                        </Space>
                    )
                },
                renderText: (value: string) => value || '–',
            },
            { title: '地理位置', alias: "目的IP地理位置", dataIndex: 'dstIpGeoInfo', render: colRenderer.area },
            { title: '目的端口', dataIndex: ['flow', 'dst_port'] },
        ],
        [
            { title: '告警类型', dataIndex: 'taskType', render: () => TYPE_MAP[type] },
            { title: '规则ID', dataIndex: 'sid', render: colRenderer.ruleId },
            { title: '任务名称', dataIndex: 'taskName', render: colRenderer.taskName, renderText: (value: string) => value || '–' },
            {
                title: '数据源',
                dataIndex: 'pcap_filename',
                render: (value: string) => (
                    <Tooltip overlay={value}>{value || '–'}</Tooltip>
                ),
                renderText: (value: string) => value || '–',
            },
            { title: '发生时间', dataIndex: 'occurredTime', render: colRenderer.dateTime },
            { title: '发现时间', dataIndex: 'observedTime', render: colRenderer.dateTime },
        ],
        [
            {
                title: type === 'model' ? '模型名称' : '告警名称',
                dataIndex: type === 'model' ? 'modelName' : 'vulName',
                render: (_: any, record: any) => colRenderer.alertName(type, record),
            },
            {
                title: '威胁类型',
                dataIndex: type === 'vul' ? 'threatFlag' : 'threatType',
                render(_: any, record: any) {
                    switch (type) {
                        case 'vul':
                            return threatFlagNameMap?.[record?.threatFlag];
                        case 'file' :
                        case 'ioc' :
                            return record?.threatType;
                    }
                },
            },
            {
                title: '威胁等级',
                dataIndex: 'threatLevel',
                render: (threatLevel: string) => THREAT_LEVEL?.[threatLevel] || threatLevel,
            },
            { title: '协议', dataIndex: ['flow', 'proto'] },
            { title: '方向', dataIndex: 'direction' },
            { title: '响应', dataIndex: 'responseName' },
        ],
    ]
    if (type === 'ioc') {
        columns.push([
            {
                title: '情报IOC',
                dataIndex: 'ioc',
                render: colRenderer.ioc,
                renderText: (value: string) => value || '–',
            },
            { title: 'APT组织', dataIndex: 'aptOrganization' },
            { title: '情报来源', dataIndex: 'info' },
            { title: '披露时间', dataIndex: 'disclosuretime' },
            { title: '攻击工具', dataIndex: 'tools' },
            { title: '参考', dataIndex: 'refer' },
        ]);
    }

    return (
        <>
            <Button
                type="link"
                onClick={() => setModalVisible(true)}
            >查看详情</Button>
            <Modal
                centered
                visible={modalVisible}
                title={
                    <div className="flex justify-between">
                        <span>日志详情</span>
                        <span className="mr-6">
                            <Button
                                className="!px-2 lh-1em !h-1em !py-0"
                                type="link"
                                onClick={() => downloadEvent(type, value)}
                            >关联数据包下载</Button>
                            <ExportData
                                className="!px-2 lh-1em !h-1em !py-0"
                                fields={columns}
                                value={value}
                            />
                        </span>
                    </div>
                }
                width="75rem"
                footer={null}
                onCancel={() => setModalVisible(false)}
            >
                <div
                    className="grid grid-cols-2 gap-4"
                >
                    {columns.map((cols, idx) =>
                        <DetailTable
                            className={cn({ 'grid-col-span-2': idx === 4 })}
                            columns={cols}
                            value={value}
                        />
                    )}
                </div>
            </Modal>
        </>
    )
}

function ExportData({ className, fields, value }) {
    const [loading, setLoading] = React.useState(false);

    const handleExport = React.useCallback(async () => {
        setLoading(true);

        const payload = fields.reduce((obj, items) => {
            items.forEach(({ title, alias, dataIndex, render, renderText }) => {
                let val = get(value, dataIndex);
                if (render) {
                    val = (renderText || render)(val, value);
                }
                obj[alias || title] = val;
            });
            return obj;
        }, {});

        try {
            const resp = await fetch(`/mica-api/api/v1/detail/export`, {
                method: 'POST',
                headers: { 'content-type': 'application/json;charset=UTF-8' },
                body: JSON.stringify({
                    data: payload,
                }),
            });

            if (resp.headers.get('content-type')?.includes('application/json')) {
                const result = await resp.json();
                throw new Error(result?.message);
            }

            const contentDisposition = resp.headers.get('content-disposition') || '';
            const filename = contentDisposition.match(/attachment; filename="?([^"]+)"?/)?.[1] || '';
            const mime = resp.headers.get('content-type');

            const blob = await resp.blob();

            download(blob, filename, mime);
        } catch (error) {
            notification.error({
                message: `导出失败`,
                description: error?.message,
            });
        } finally {
            setLoading(false);
        }
    }, [fields, value]);

    return (
        <Button
            className={className}
            type="link"
            loading={loading}
            onClick={handleExport}
        >导出</Button>
    );
}