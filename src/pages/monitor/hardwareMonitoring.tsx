/*
 * @Author: tianh
 * @Date: 2022-02-21 11:42:01
 * @LastEditors: tianh
 * @LastEditTime: 2022-05-10 17:47:18
 * @Descripttion:
 */
import React, { useState, useEffect } from 'react';
import style from './style.less';
import { Card, Tooltip, Form, Input, message, Modal, Select } from 'antd';
import moment from 'moment';
import { hardware, memory, disk, getSettings, save_settings, getNodes } from '@/services/monitor';
import { node } from 'prop-types';
const echarts = require('echarts');
const { Option } = Select;
let timer: any = null;
const HardwareMonitoring = () => {
  const [form] = Form.useForm();
  const [visible, setvisible] = useState(false);
  const [settings, setsettings] = useState({});
  const [cpuData, setcpuData] = useState<any>({
    cols: [],
    peak_cpu_time: '',
    peak_cpu: 0,
  });
  const [cpuTemp, setCpuTemp] = useState<any>(0);
  const [memoryData, setmemoryData] = useState<any>({
    cols: [],
    peak_mem_time: '',
    peak_mem: 0,
  });
  const [chooseNode, setchooseNode] = useState('');
  const [diskList, setdiskList] = useState({});
  const [nodesList, setnodesList] = useState([]);
  const getNodesOption = () => {
    getNodes({ page: 1, pageSize: 99 }).then((res) => {
      if (res.flag) {
        res.data.cols.forEach((item: any) => {
          console.log(item);
          if (item.node_role === 'master') {
            console.log('xx', item);
            setchooseNode(item.node_name);
          }
        });
        setnodesList(res.data.cols);
      }
    });
  };

  /**
   * @name:
   * @description: 获取cpu监控数据
   * @param {*}
   * @return {*}
   */
  const getHardwate = (data:any) => {
    hardware({ node_name: data }).then((res) => {
      if (res.flag) {
        const cpu = res.data.cols;
        setCpuTemp(cpu[cpu.length - 1].cpu_temp);
        setcpuData(res.data);
      }
    });
  };

  /**
   * @name:
   * @description: 获取内存监控数据
   * @param {*}
   * @return {*}
   */
  const getMemory = (data:any) => {
    memory({ node_name: data }).then((res) => {
      if (res.flag) {
        setmemoryData(res.data);
      }
    });
  };

  const initCpuChart = () => {
    let chartDom = document.getElementById('cpu');
    let myChart = echarts.init(chartDom);
    let date = cpuData.cols.map((item: any) => {
      return item.createTime;
    });
    let data = cpuData.cols.map((item: any) => {
      return parseInt(`${item.percent * 100}`);
    });
    let option = {
      tooltip: {
        trigger: 'axis',
        position: function (pt: any[]) {
          return [pt[0], '10%'];
        },
      },
      title: {
        text: `历史峰值:${(cpuData.peak_cpu * 100).toFixed(0)}% (时间:${cpuData.peak_cpu_time})`,
        left: 'left',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: date,
      },
      yAxis: {
        min: 0,
        max: 100,
        data: [0.2, 0.4, 0.6, 0.8, 1],
        type: 'value',
        boundaryGap: [0, '100%'],
        axisLabel: {
          formatter: '{value}%', //字符串模板
        },
      },
      // dataZoom: [
      //   {
      //     type: 'inside',
      //     start: 80,
      //     end: 100,
      //   },
      //   {
      //     start: 80,
      //     end: 100,
      //   },
      // ],
      series: [
        {
          name: '占用率%',
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: 'rgb(255, 70, 131)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(255, 158, 68)',
              },
              {
                offset: 1,
                color: 'rgb(255, 70, 131)',
              },
            ]),
          },
          data: data,
        },
      ],
    };

    option && myChart.setOption(option);
  };
  const initMemChart = () => {
    let chartDom = document.getElementById('memory');
    let myChart = echarts.init(chartDom);
    let date = memoryData.cols.map((item: any) => {
      return item.createTime;
    });
    let data = memoryData.cols.map((item: any) => {
      return parseInt(`${item.percent * 100}`);
    });
    // let num =
    let option = {
      tooltip: {
        trigger: 'axis',
        position: function (pt: any[]) {
          return [pt[0], '10%'];
        },
      },
      title: {
        left: 'left',
        text: `历史峰值:${(memoryData.peak_mem * 100).toFixed(0)}% (时间:${memoryData.peak_mem_time})`,
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: date,
      },
      yAxis: {
        min: 0,
        max: 100,
        data: [0.2, 0.4, 0.6, 0.8, 1],
        type: 'value',
        boundaryGap: [0, '100%'],
        axisLabel: {
          formatter: '{value}%', //字符串模板
        },
      },
      // dataZoom: [
      //   {
      //     type: 'inside',
      //     start: 80,
      //     end: 100,
      //   },
      //   {
      //     start: 80,
      //     end: 100,
      //   },
      // ],
      series: [
        {
          name: '占用率%',
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: 'rgb(141,163,226)',
          },
          //   line: {
          label: {
            formatter: '{value}%',
          },
          //   },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(141,163,226)',
              },
              {
                offset: 1,
                color: 'rgb(141,163,226)',
              },
            ]),
          },
          data: data,
        },
      ],
    };

    option && myChart.setOption(option);
  };
  /**
   * @name:
   * @description: 获取磁盘监控数据
   * @param {*}
   * @return {*}
   */

  const getDisk = (data:any) => {
    disk({ node_name: data }).then((res) => {
      if (res.flag) {
        setdiskList(res.data);
      }
    });
  };
  const handleOk = () => {
    form.validateFields().then((value: any) => {
      save_settings(value).then((res) => {
        if (res.flag) {
          setvisible(false);
          message.success(res.message);
        } else {
          message.error(res.message);
        }
      });
    });
  };
  const onCancel = () => {
    setvisible(false);
  };
  const showDetail = () => {
    getSettings().then((res) => {
      if (res.flag) {
        setsettings(res.data);
        setvisible(true);
      } else {
        message.error(res.message);
      }
    });
  };
  const changeData = (value: any) => {
    console.log(timer)
    setchooseNode(value);
    clearInterval(timer);
 
    setTimeout(() => {
      getHardwate(value);
      getDisk(value);
      getMemory(value);
    },500);
    timer = setInterval(() => {
      getHardwate(value);
      getDisk(value);
      getMemory(value);
    }, 10000);
  };
  useEffect(() => {
    getNodesOption();
    getHardwate(chooseNode);
    getDisk(chooseNode);
    getMemory(chooseNode);
  }, []);

  useEffect(() => {
    initCpuChart();
  }, [cpuData]);

  useEffect(() => {
    initMemChart();
  }, [memoryData]);

  useEffect(() => {
    timer = setInterval(() => {
      getHardwate(chooseNode);
      getDisk(chooseNode);
      getMemory(chooseNode);
    }, 10000);
    return () => {
      clearInterval(timer);
    };
  }, []);
  return (
    <div>
      <div className={style.top_title}>
        <div className={style.title}>
          硬件监控
          <div style={{ display: 'inline-block', paddingLeft: 20 }}>
            {nodesList && nodesList.length ? (
              <Form form={form}>
                <Form.Item initialValue={chooseNode} name="chooseNode" label="节点选择">
                  <Select onChange={changeData} style={{ width: 200 }}>
                    {nodesList.map((item: any) => {
                      return (
                        <Option key={item._id} value={item.node_name}>
                          {item.node_name}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Form>
            ) : null}
          </div>
        </div>

        <div>
          {/* <span style={{ paddingRight: 10 }}>
            <span onClick={showDetail} className={style.icons}>
              <SettingOutlined />
            </span>
            硬件告警邮箱配置
          </span> */}
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <div className={style.item}>
        <Card
          title="CPU占用"
          style={{
            width: '50%',
            position: 'relative',
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: '75px',
              right: '60px',
              textAlign: 'center',
            }}
          >
            <div>CPU温度</div>
            {cpuTemp}℃
          </div>
          <div className={style.cupCharts} id="cpu"></div>
        </Card>
        <Card title="内存占用" style={{ width: '50%' }}>
          <div className={style.cupCharts} id="memory"></div>
        </Card>
      </div>
      <div className={style.top_title}>
        <div className={style.title}>磁盘监控</div>
        <div>
          最近更新:
          {moment().format('YYYY-MM-DD HH:mm:ss')}
        </div>
      </div>
      <div className={style.disk_box}>
        {Object.keys(diskList).map((key) => (
          <div className={style.disk_item} key={key}>
            <div className={style.disk_topText}>
              <div className={style.disk_title}>
                <div className={style.disk_text}>
                  磁盘: <span style={{ fontWeight: 'bold' }}>{key}</span>
                </div>
                <div>
                  历史峰值: {`${(diskList[key].peak * 100).toFixed(0)}%`}
                  &nbsp;(时间:
                  {diskList[key].peakTime})
                </div>
              </div>
            </div>
            <div className={style.disk_img}>
              <div className={style.disk_charts}>
                <Tooltip
                  title={`已用:${
                    parseFloat((diskList[key].use / 1024).toFixed(1)) > 1000
                      ? `${(diskList[key].use / 1024 / 1024).toFixed(1)}T`
                      : `${(diskList[key].use / 1024).toFixed(1)}G`
                  }
                `}
                >
                  <div
                    style={{
                      width: `${diskList[key].percent * 100}%`,
                    }}
                    className={style.disk_use}
                  ></div>
                </Tooltip>
                <Tooltip
                  title={`未用:${
                    parseFloat((diskList[key].free / 1024).toFixed(1)) > 1000
                      ? `${(diskList[key].free / 1024 / 1024).toFixed(1)}T`
                      : `${(diskList[key].free / 1024).toFixed(1)}G`
                  }
                `}
                >
                  <div
                    style={{
                      width: `${(1 - diskList[key].percent) * 100}%`,
                    }}
                    className={style.disk_noUse}
                  ></div>
                </Tooltip>
              </div>
            </div>
            <p className={style.disk_bottomText}>
              <span className={style.disk_text}>
                总共:
                {/* 超过1000g 单位换位T显示 */}
                {parseFloat((diskList[key].total / 1024).toFixed(1)) > 1000
                  ? `${(diskList[key].total / 1024 / 1024).toFixed(1)}T`
                  : `${(diskList[key].total / 1024).toFixed(1)}G`}
              </span>
              <span className={style.disk_text}>
                已用:
                {parseFloat((diskList[key].use / 1024).toFixed(1)) > 1000
                  ? `${(diskList[key].use / 1024 / 1024).toFixed(1)}T`
                  : `${(diskList[key].use / 1024).toFixed(1)}G`}
              </span>
              <span className={style.disk_text}>
                未用:
                {parseFloat((diskList[key].free / 1024).toFixed(1)) > 1000
                  ? `${(diskList[key].free / 1024 / 1024).toFixed(1)}T`
                  : `${(diskList[key].free / 1024).toFixed(1)}G`}
              </span>
              <span className={style.disk_text}>使用率:{(diskList[key].percent * 100).toFixed(0)}%</span>
            </p>
          </div>
        ))}
      </div>
      <Modal title="设置" onOk={handleOk} onCancel={onCancel} visible={visible}>
        <Form initialValues={settings} form={form}>
          <Form.Item
            label="SMTP服务器地址"
            name="mail_server"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="SMTP端口"
            name="server_port"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="邮箱账号"
            name="account"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="邮箱密码"
            name="password"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            label="收件人"
            name="receiver"
            rules={[
              {
                required: true,
                message: '请填写',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default HardwareMonitoring;
