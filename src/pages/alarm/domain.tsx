import React, { useState, useEffect } from 'react';
import {
  Form,
  Select,
  Row,
  Button,
  Col,
  Collapse,
  Table,
  message,
  Spin,
  Tooltip,
  Empty,
} from 'antd';
import { getTaskName } from '@/services/common';
import { getDomainList } from '@/services/alarm';
import style from './index.less';
import './index.less';
const { Panel } = Collapse;
const { Option } = Select;
const Domain = (props: any) => {
  const { form } = props;
  const { getFieldDecorator } = form;
  const [taskList, settaskList] = useState([]);
  //   const [isShow, setisShow] = useState(false);
  const [loading, setloading] = useState(false);
  const [tableData, settableData] = useState([]);
  const columns = [
    {
      title: '域名',
      width:'20%',
      dataIndex: 'domain',
      render: (t: string) => {
        return <Tooltip title={t}>{t}</Tooltip>;
      },
    },
  ];
  // 搜索表单
  const onSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    form.validateFields((err: object, value: { celeryId: '' }) => {
      if (!err) {
        if (value.celeryId !== undefined) {
          setloading(true);
          getDomainList(value).then(res => {
            if (res.flag) {
              setloading(false);
              // setisShow(true);
              settableData(res.data);
            } else {
              message.error(res.message);
            }
          });
        }
      }
    });
  };
  useEffect(() => {
    getTaskList();
  }, []);
  const getTaskList = () => {
    getTaskName({}).then(res => {
      if (res.flag) {
        let list = res.data;
        list.push({ taskName: '实时任务', celeryId: '' });
        settaskList(list);
      } else {
        message.error(res.message);
      }
    });
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form className="searchForm" onSubmit={onSubmit} layout="inline">
            <Row>
              <Col span={6}>
                <Form.Item label="任务名称">
                  {getFieldDecorator(
                    'celeryId',
                    {},
                  )(
                    <Select>
                      {taskList.map((item: any) => {
                        return (
                          <Option key={item.celeryId} value={item.celeryId}>
                            {item.taskName}
                          </Option>
                        );
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Button
                  className={style.searchBtn}
                  type="primary"
                  htmlType="submit"
                >
                  搜索
                </Button>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
      <Spin spinning={loading}>
        <div className={style.content}>
          {tableData && tableData.length ? (
            tableData.map((item: any, index) => {
                return (
                  <div key={index} className={style.tableBox}>
                    <p className={style.table_title}>{item.ts}</p>
                    <Table
                      dataSource={item.domain_list}
                      pagination={false}
                      columns={columns}
                    />
                  </div>
                );
            })
          ) : (
            <Empty />
          )}
        </div>
      </Spin>
    </div>
  );
};
export default Form.create()(Domain);
