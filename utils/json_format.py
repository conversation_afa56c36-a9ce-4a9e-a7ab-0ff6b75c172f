"""module ObjectId to json"""
import datetime
import json
from bson import ObjectId


class JSONEncoder(json.JSONEncoder):
    """处理ObjectId & datetime类型无法转为json"""
    def default(self, o):
        """method default"""
        if isinstance(o, ObjectId):
            return str(o)
        if isinstance(o, datetime.datetime):
            return datetime.datetime.strftime(o, '%Y-%m-%d %H:%M:%S')
        return json.JSONEncoder.default(self, o)
