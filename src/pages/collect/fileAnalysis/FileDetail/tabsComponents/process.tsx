import React, { useEffect, useState } from 'react';
import cn from 'classnames';
import moment from 'moment';
import { Empty } from 'antd';

const echarts = require('echarts');

import s from './style.less';
import { any } from 'prop-types';
interface RrocessInfoProps {
  className?: string;
  domId?: string;
  type?: string;
  info: any;
}
const svg1T=''
const svg2T=''
const RrocessInfo = ({ className, domId, type, info }: RrocessInfoProps) => {
  const optionObj = {
    title: {
      text: '',
    },
    tooltip: {},
    animationDurationUpdate: 1500,
    animationEasingUpdate: 'quinticInOut',
    label: {
      normal: {
        show: true,
        textStyle: {
          fontSize: 12,
        },
      },
    },
    series: [
      {
        smooth: true,
        type: 'graph',
        layout: 'none',
        symbolSize: 45,
        focusNodeAdjacency: true,
        roam: true,
        categories: [
          {
            name: '夫妻',
            itemStyle: {
              normal: {
                color: '#009800',
              },
            },
          },
          {
            name: '战友',
            itemStyle: {
              normal: {
                color: '#4592FF',
              },
            },
          },
        ],
        label: {
          normal: {
            show: true,
            textStyle: {
              fontSize: 12,
            },
          },
        },
        data: [
          {
            id: '0',
            name: '开始分析',
            x: 100,
            y: 100,
            category: 0,
            symbol:
              'path://M921.6 450.133333c-6.4-8.533333-14.933333-12.8-25.6-12.8h-10.666667V341.333333c0-40.533333-34.133333-74.666667-74.666666-74.666666H514.133333c-4.266667 0-6.4-2.133333-8.533333-4.266667l-38.4-66.133333c-12.8-21.333333-38.4-36.266667-64-36.266667H170.666667c-40.533333 0-74.666667 34.133333-74.666667 74.666667v597.333333c0 6.4 2.133333 12.8 6.4 19.2 6.4 8.533333 14.933333 12.8 25.6 12.8h640c12.8 0 25.6-8.533333 29.866667-21.333333l128-362.666667c4.266667-10.666667 2.133333-21.333333-4.266667-29.866667zM170.666667 224h232.533333c4.266667 0 6.4 2.133333 8.533333 4.266667l38.4 66.133333c12.8 21.333333 38.4 36.266667 64 36.266667H810.666667c6.4 0 10.666667 4.266667 10.666666 10.666666v96H256c-12.8 0-25.6 8.533333-29.866667 21.333334l-66.133333 185.6V234.666667c0-6.4 4.266667-10.666667 10.666667-10.666667z m573.866666 576H172.8l104.533333-298.666667h571.733334l-104.533334 298.666667z',
            itemStyle: {
              // 设置symbol的颜色
              normal: {
                color: '#666666',
              },
            },
            label: {
              show: true,
              position: 'bottom',
              distance: 5,
            },
          },
          {
            name: '1',
            draggable: false,
            value: 1,
            x: 150,
            y: 150,
            symbol: "rect",
            itemStyle: {
              color: "#ccc"
            }
          },
          {
            value: 2,
            name: '2',
            draggable: false,
            x: 150,
            y: 70,
              symbol: "rect",
            itemStyle: {
              color: "#ccc"
            }
          },
          {
            value: 3,
            name: '3',
            draggable: false,
            x: 200,
            y: 150,
              symbol: "rect",
            itemStyle: {
              color: "#ccc"
            }
          },
          {
            value: 4,
            name: '4',
            draggable: false,
            x: 200,
            y: 70,
              symbol: "rect",
            itemStyle: {
              color: "#ccc"
            }
          },
        ],
        links: [
          {
            source: 0,
            target: 1,
            value: '',
          },
          {
            source: 0,
            target: 2,
            value: '',
          },
          {
            source: 1,
            target: 3,
            value: '',
          },
          {
            source: 2,
            target: 4,
            value: '',
          },
        ],
        lineStyle: {
          normal: {
            opacity: 0.9,
            width: 1,
            curveness: 0,
          },
        },
      },
    ],
  };
  useEffect(() => {
    const fileEcharts1 = echarts.init(document.getElementById('domId'));
    fileEcharts1.setOption(optionObj, true);
    fileEcharts1.resize();
    window.addEventListener('resize', () => {
      fileEcharts1.resize();
    });
    return () => {
      window.removeEventListener('resize', () => {
        fileEcharts1.resize();
      });
    };
  }, [info]);
  return <div id={'domId'} style={{ width: '100%', height: '200px' }}></div>;
};
export default RrocessInfo;
