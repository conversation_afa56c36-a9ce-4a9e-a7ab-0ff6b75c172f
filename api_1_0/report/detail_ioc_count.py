#!/usr/bin/env python3
# -*- coding:utf-8 -*-
# Power by <PERSON><PERSON><PERSON>

from utils.ndr_base import NdrResource
from utils.param_check import Validator
from utils import param_check
from flask import request
from utils.es_function import get_range
from utils.utils import flask_response
from utils.es_function import get_country_term
from utils.logger import get_ndr_logger
from config.config import IOC_INDEX

ES_LOG = get_ndr_logger('es_log', __file__)
API_LOG = get_ndr_logger('api_log', __file__)


class IocSum(NdrResource):
    '''情报总数统计'''

    field_template = {
        "occurred": "occurredTime",
        "observed": "observedTime"
    }

    def __init__(self):
        super(IocSum, self).__init__(es_template="ioc_sum")

    @param_check.check_flask_args(Validator('report_args_check'), request)
    def get(self, **kwargs):
        # 开始时间&结束时间校验
        response = self.time_verify(**kwargs)
        if response is not True:
            return response

        start_time = kwargs["startTime"]
        stop_time = kwargs["stopTime"]
        time_mode = kwargs["timeMode"]
        time_mode = self.field_template[time_mode]
        country = kwargs["country"]
        ranges = get_range(time_mode, start_time, stop_time)
        self.es_template["query"]["bool"]["must"].append(ranges)
        if country:
            country_term = get_country_term(country)
            self.es_template["query"]["bool"]["must"].append(country_term)

        try:
            res = self.es_client.search(index=IOC_INDEX, body=self.es_template)
        except Exception as e:
            err_info = "error: " + str(e)
            ES_LOG.error('params[{0}],reason[{1}]'.format(self.es_template, str(e)))
            return flask_response(err_info, False, [])

        try:
            data = self.data_format(res)
            return flask_response("", True, data)
        except Exception as e:
            err_info = "error: " + str(e)
            API_LOG.error('params[{0}],reason[{1}]'.format(res, str(e)))
            return flask_response(err_info, False, [])

    @staticmethod
    def data_format(data):

        trigger_count = data["hits"]["total"]["value"]
        result = {
            "totalCount": trigger_count
        }

        return result
