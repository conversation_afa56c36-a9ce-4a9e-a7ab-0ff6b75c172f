/*
 * @Author: tianh
 * @Date: 2021-09-30 14:13:32
 * @LastEditors: tianh
 * @LastEditTime: 2022-06-15 14:31:52
 * @Descripttion:
 */
import React from 'react';
import { Tooltip } from 'antd';
import { killChainMap } from '@/utils/enumList';
import classnames from 'classnames';
import _ from 'lodash';
import styles from './index.less';

const KillChainStages = ({ data, showAll = false }: { data: string; showAll?: boolean }) => {
  const killchainsClass = [
    'Reconnaissance',
    'Weaponization',
    'Delivery',
    'Exploitation',
    'Installation',
    'Command and Control',
    'Actions on Objective',
  ];
  return (
    showAll
      ? (
        <div className={styles.killChains}>
          {killchainsClass.map((v, index) => {
            return (
              <div className={styles.killChain} key={v}>
                <Tooltip title={killChainMap[v]} key={index} className={styles.killChainImg}>
                  <div
                    className={classnames(
                      v === 'Actions on Objective' ? styles.success : styles[v],
                      v === 'Command and Control' ? styles.CnC : styles[v],
                      `min-w-4 min-h-4 ${styles.killChainImg} ${styles[v]} ${data.includes(v) ? `${styles.active}` : ''} `,
                    )}
                  ></div>
                </Tooltip>
              </div>
            );
          })}
        </div>
      )
      : (
        <div className={classnames("inline-flex items-center", styles.killChain)}>
          <div
            className={classnames(
              'w-4 h-4',
              data === 'Actions on Objective' ? styles.success : styles[data],
              data === 'Command and Control' ? styles.CnC : styles[data],
              `min-w-4 min-h-4 ${styles.killChainImg} ${styles[data]} ${data?.includes(data) ? `${styles.active}` : ''} `,
            )}
          />
          <div className={classnames('ml-1', styles.text)}>{killChainMap[data]}</div>
        </div>
      )
  );
};
export default KillChainStages;
