# -*- coding: utf-8 -*-
# @Time    : 2019-09-02 18:58
# <AUTHOR> Shark
# @File    : test_get_timeLine_stats.py
# @Software: PyCharm
import json
import random
from unittest import TestCase
from api_1_0.router import app, api
from api_1_0.back_event.get_timeLine_stats import TimeLineStats


mode = ["threatlevel", "killchains"]
timeSlice = ["Minute", "Hour", "Day", "Week", "Month"]


class TestTimeLineStats(TestCase):

    def setUp(self):
        self.app = app
        self.app.config['SERVER_NAME'] = 'example.com'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()

    def tearDown(self):
        pass

    def test_startTime_not_exists(self):
        resp = self.client.get(
            api.url_for(TimeLineStats,
                        mode=random.choice(mode),
                        pageSize=10,
                        timeSlice=random.choice(timeSlice)
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], False)

    def test_pageSize_not_exists(self):
        resp = self.client.get(
            api.url_for(TimeLineStats,
                        mode=random.choice(mode),
                        startTime=1504527700099,
                        timeSlice=random.choice(timeSlice)
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)

    def test_timeSlice_not_exists(self):
        resp = self.client.get(
            api.url_for(TimeLineStats,
                        mode=random.choice(mode),
                        startTime=1567094400000,
                        pageSize=10
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)

    def test_all_true(self):
        mode = ["threatlevel", "killchains"]
        timeSlice = ["Minute", "Hour", "Day", "Week", "Month"]
        resp = self.client.get(
            api.url_for(TimeLineStats,
                        mode=random.choice(mode),
                        startTime=1567094400000,
                        pageSize=10,
                        timeSlice="Month"
                        )
        )
        resp_dict = json.loads(resp.data)
        print(resp_dict)
        self.assertEqual(resp_dict["flag"], True)
