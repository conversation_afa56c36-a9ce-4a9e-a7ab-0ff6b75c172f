# -*- coding: utf-8 -*-
# @Time    : 2020-08-03 14:10
# <AUTHOR> wu
# @File    : customize_ioc_manager.py
# @Software: PyCharm
"""
自定义 自签名模型白名单 管理
"""

import datetime
import sqlite3
from flask import request
from flask_restful import Resource
from config.config import WhiteListCfg, NdrLog
from utils import param_check
from utils.logger import get_ndr_logger
from utils.param_check import Validator
from utils.utils import flask_response
from utils.cluster import *
from api_1_0.utils.flask_log import ndr_log_to_box

LOG = get_ndr_logger(NdrLog.Type.OPERATE, __file__)


class SelfSignWhiteResource(Resource):
    def __init__(self):
        self.sqlite = sqlite3.connect(WhiteListCfg.DbPath)
        self.cursor = self.sqlite.cursor()

    def __del__(self):
        try:
            self.sqlite.close()
        except:
            pass


class SelfSignWhite(SelfSignWhiteResource):
    """ 自签名白名单 处理（增、删、改、查） """

    def __init__(self):
        super(SelfSignWhite, self).__init__()

    def __del__(self):
        super(SelfSignWhite, self).__del__()

    # 修改自签名白名单
    @param_check.check_flask_args(Validator('self_signature_white_schema'), request)
    def put(self, **kwargs):
        """
        :return:
        """
        target = "'%s'" % kwargs["target"]
        tag = "'%s'" % kwargs["tag"]
        try:
            sql = "UPDATE self_signature_white set tag=%s WHERE target=%s" % (tag, target)
            self.cursor.execute(sql)
            self.sqlite.commit()
        except Exception as error:
            LOG.error("self_signature_white update failed.Reason：%s。" % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "自签名白名单[%s]修改失败：%s。" % (target, str(error)))
            return flask_response("self_signature_white update failed", False, {})
        LOG.info("self_signature_white [%s] update successfully." % target)
        ndr_log_to_box(NdrLog.Type.OPERATE, "自签名白名单[%s]修改成功。" % target)
        cluster_sync_file(WhiteListCfg.DbPath)
        return flask_response("self_signature_white [%s] update successfully." % target, True, {})

    # 删除自签名白名单
    @param_check.check_flask_args(Validator('self_signature_white_schema'), request)
    def delete(self, **kwargs):
        """
        :return:
        """
        LOG.info(kwargs)
        target_list = kwargs["target_list"]
        param = ""
        for target in target_list:
            param = param + "'%s'" % target + ","
        if param != "":
            param = "(" + param[0:-1] + ")"
        else:
            param = "()"
        try:
            self.cursor.execute("DELETE from self_signature_white where target in %s;" % param)
            self.sqlite.commit()
            ndr_log_to_box(NdrLog.Type.OPERATE, "自签名白名单删除成功。[%s]" % target_list)
            cluster_sync_file(WhiteListCfg.DbPath)
            return flask_response("self_signature_white delete successfully.", True, {})
        except Exception as error:
            LOG.error("self_signature_white delete failed.Reason：%s" % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "自签名白名单删除失败：%s。" % str(error))
            return flask_response("self_signature_white delete failed", False, {})

    # 获取自签名白名单
    @param_check.check_flask_args(Validator('self_signature_white_schema'), request)
    def get(self, **kwargs):
        """
        :return:
        """
        target = kwargs["target"]
        parm = ""
        if target:
            parm += "target like '%%%s%%'" % target
        if parm != "":
            parm = "where " + parm
        try:
            self.cursor.execute("SELECT * from self_signature_white %s;" % parm)
            white_res = self.cursor.fetchall()
            data = []
            for row in white_res:
                temp = {
                    "target": row[0],
                    "createTime": row[1],
                    "tag": row[2]
                }
                data.append(temp)
            return flask_response("self_signature_white get successful", True, data)
        except Exception as error:
            LOG.error("self_signature_white get failed.Reason：%s" % str(error))
            return flask_response("self_signature_white get failed", False, {})

    # 添加自签名白名单
    @param_check.check_flask_args(Validator('self_signature_white_schema'), request)
    def post(self, **kwargs):
        """
        :return:
        """
        create_time = datetime.datetime.now().strftime("%Y-%m-%d")
        target = kwargs["target"]
        tag = kwargs["tag"]
        parm_str = "'%s'" % target + ",'%s'" % create_time + ",'%s'" % tag
        try:
            self.cursor.execute("INSERT INTO self_signature_white (target, createTime, tag) VALUES (%s)" % parm_str)
            self.sqlite.commit()
        except Exception as error:
            if "UNIQUE constraint" in str(error):
                error = "[%s] already exist." % target
            LOG.error("self_signature_white add failed.Reason：%s" % str(error))
            ndr_log_to_box(NdrLog.Type.OPERATE, "自签名白名单添加失败：%s。" % str(error))
            return flask_response(str(error), False, {})
        LOG.info("self_signature_white add successfully")
        ndr_log_to_box(NdrLog.Type.OPERATE, "自签名白名单[%s]添加成功。" % target)
        cluster_sync_file(WhiteListCfg.DbPath)
        return flask_response("self_signature_white add successfully.", True, {})
