import React, { useMemo, useState } from 'react';
import { LabeledValue, SelectValue } from 'antd/lib/select';
import { Checkbox, Select, } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';

export type Props = {
  value?: string[];
  options: LabeledValue[];
  loading?: boolean;
  disabled?: boolean;
  onChange?: (value: SelectValue[]) => void;
  style?: React.CSSProperties;
  className?: string;
}


function SelectorWithCheckAll({ style, className, options, value, onChange, loading, disabled }: Props): JSX.Element {

  const allValues = useMemo(() => options.map(({ value }) => value), [options]);

  const [checkAll, setCheckAll] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);

  const handleCheckAll = (e: CheckboxChangeEvent) => {
    const { checked } = e.target
    setIndeterminate(false);
    setCheckAll(checked);
    onChange?.(checked ? allValues : []);
  }

  const handleOnSelectChange = (values: SelectValue[]) => {
    setIndeterminate(!!values.length && values.length < allValues.length);
    setCheckAll(values.length === allValues.length);
    onChange?.(values)
  }

  const dropdownRender = (menu: any) => {
    return (
      <>
        {
          !!options.length && (
            <div style={{ height: 30 }} className='px-2'>
              <Checkbox
                indeterminate={indeterminate}
                onChange={handleCheckAll}
                checked={checkAll}
              >
                全选
              </Checkbox>
            </div>
          )
        }
        {menu}
      </>

    )
  }

  return (
    <Select
      style={style}
      mode="multiple"
      loading={loading}
      disabled={loading || disabled}
      maxTagCount="responsive"
      className={className}
      options={options}
      value={value}
      allowClear
      onChange={handleOnSelectChange}
      dropdownRender={dropdownRender}
    />
  );
}

export default SelectorWithCheckAll;
