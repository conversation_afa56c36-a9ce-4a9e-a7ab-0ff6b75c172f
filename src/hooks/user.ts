import { UserInfo } from '@/store/user';
import { isDev } from '@/utils/utils';
import { useModel } from 'umi';

interface Boss<PERSON>ser extends UserInfo {
  user: object;
  can(rule: string): boolean;
}

const PolyfillProps = {
  user: {
    account: 'Faker',
    modules: [],
  },
};

/**
 * 在 React Hooks 中使用当前登录用户
 */
export function useBossUser(): BossUser {
  const { user } = useModel('@@qiankunStateFromMaster') || PolyfillProps;
  const { modules } = user;
  return {
    ...user,
    /**
     * 判断用户是否有权限
     * @param rule RBAC 规则
     */
    can(rule: string): boolean {
      if (!rule) {
        return false;
      }
      if (isDev()) {
        return true;
      }
      return modules.includes(rule);
    },
  };
}

// 当命名导出函数大于一个时可删除以下代码
export default useBossUser;
