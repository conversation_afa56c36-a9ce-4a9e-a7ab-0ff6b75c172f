import React, { useEffect, useState, useRef } from 'react';
import {
  Collapse,
  Col,
  Row,
  Form,
  Select,
  DatePicker,
  Button,
  Menu,
  Table,
  Dropdown,
  Modal,
  message,
  Popconfirm,
  Progress,
  Input,
  Tooltip,
  Typography,
  Spin,
  Space,
} from 'antd';
import { QuestionCircleOutlined, SearchOutlined, DownOutlined } from '@ant-design/icons';
import moment from 'moment';
import { formatTime, ellipsis } from '@/utils/utils';
import KillChainStages from '@/components/KillChainStages/index';
import { getExploreTable, handleSwitch, deleteData, getTaskPacp } from '@/services/explore';
import { exploreSort, exploreTaskType, hit, hitName } from '@/utils/enumList';
import { useHistory } from '@/hooks/global';

import { cloneDeep, keyBy } from 'lodash';
import Addtask from './addtask';
import style from './index.less';
import store from './store';

const severityNameMap = {
  low: '低危',
  medium: '中危',
  high: '高危',
};

const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;

const { Text } = Typography;

const Index = () => {
  const [form] = Form.useForm();
  const taskName = new URLSearchParams(location.search).get('taskName');

  const history = useHistory();
  const timerRef = useRef<NodeJS.Timeout>();
  // let rowRecord = { celeryId: '' };

  const [rowRecord, setRowRecord] = useState({ celeryId: '' });
  const [visible, setvisible] = useState(false);
  const [flowSize, setFlowSize] = useState(0);
  const [visibleForm, setVisibleForm] = useState(false);
  const [pacpList, setpacpList] = useState([]);
  const [pacpCount, setpacpCount] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [total, settotal] = useState(0);
  const [sortType, setsortType] = useState('按创建时间倒序');
  const [expandedRowKeys, setexpandedRowKeys] = useState([]);
  const [formValue, setformValue] = useState<any>({
    taskName,
    time: [],
    createStartTime: null,
    createEndTime: null,
    page: 1,
    pageSize: 10,
    reverse: true,
    sort: 'createTime',
  });
  useEffect(() => {
    searchList({});
  }, [formValue]);
  const columns = [
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '任务执行时间',
      dataIndex: 'taskTime',
      render: (
        _: any,
        record: {
          taskType: string;
          preTime: any;
          nextTime: any;
          startTime: number;
          endTime: number;
        },
      ) => {
        if (record.taskType === 'timedTask') {
          return (
            <div>
              <div>
                {record.preTime
                  ? `上次执行时间：${formatTime(record.preTime, 'YYYY-MM-DD HH:mm:ss')}`
                  : `开始时间${formatTime(record.startTime, 'YYYY-MM-DD HH:mm:ss')}`}
              </div>
              <div>
                下次执行时间：
                {record.nextTime ? formatTime(record.nextTime, 'YYYY-MM-DD HH:mm:ss') : '--'}
              </div>
            </div>
          );
        }
        return (
          <div>
            <div>
              任务开始时间：
              {record.startTime ? formatTime(record.startTime, 'YYYY-MM-DD HH:mm:ss') : '--'}
            </div>
            <div>
              任务结束时间：
              {record.endTime ? formatTime(record.endTime, 'YYYY-MM-DD HH:mm:ss') : '--'}
            </div>
          </div>
        );
      },
    },
    {
      title: '任务类型',
      dataIndex: 'type',
      render: (t: string) => {
        const obj = {
          pcap: '流量分析',
          other: '文件分析',
        };
        return <div>{obj[t]}</div>;
      },
    },
    {
      title: '调度类型',
      dataIndex: 'taskType',
      render: (t: string) => {
        if (t === 'singleTask') {
          return (
            <div>
              <div style={{ color: '#ff8533' }}>单次任务</div>
            </div>
          );
        }
        return (
          <div>
            <div style={{ color: '#00cc00' }}>周期任务</div>
          </div>
        );
      },
    },
    {
      title: '规则组',
      dataIndex: 'curFeatureGrpList',
      render: (t: any[]) => (
        <Text style={{ width: 120 }} ellipsis={{ tooltip: t.map((v: { groupName: any }) => v.groupName).join('、') }}>
          {t.map((v: { groupName: any }) => v.groupName).join('、') || '-'}
        </Text>
      ),
    },
    {
      title: '数据来源',
      dataIndex: 'type',
      render: (t: string, record: any) => {
        if (record.taskType === 'timedTask') {
          return (
            <div>
              <div>
                在线数据源：
                {record.sourceData.aliasName}
              </div>
            </div>
          );
        }
        return (
          <div>
            <div>本地上传</div>
          </div>
        );
      },
    },
    {
      title: '数据包',
      dataIndex: 'pcapList',
      render: (t: any, record: any) => (
        <Button
          type="link"
          onClick={(e) => {
            e.stopPropagation();
            showPacp(record.taskName);
            setFlowSize(record.statisticsInfo.flowSize);
          }}
        >
          查看数据包
        </Button>
      ),
    },
    {
      title: '杀伤链阶段',
      dataIndex: 'statisticsInfo.vul.killChains',
      render: (t: any, record: any) => {
        let arr: any = [];
        record.statisticsInfo?.ioc?.killChains?.forEach((item: any) => {
          arr.push(item);
        });
        record.statisticsInfo?.model?.killChains?.forEach((item: any) => {
          arr.push(item);
        });
        record.statisticsInfo?.vul?.killChains?.forEach((item: any) => {
          arr.push(item);
        });
        record.statisticsInfo?.file_log?.killChains?.forEach((item: any) => {
          arr.push(item);
        });
        arr = [...new Set(arr)];
        return (
          <div>
            <KillChainStages data={arr} showAll />
          </div>
        );
      },
    },
    {
      title: '执行进度',
      dataIndex: 'process',
      render: (t: number, record: any) => {
        const number = parseFloat((t * 100).toFixed(2));
        return <Progress width={40} type="circle" percent={number} />;
      },
    },
    {
      title: '操作',
      dataIndex: 'del',
      width: '160px',
      render: (_: any, record: any) => (
        <section>
          <Button onClick={(e) => editTask(record, e)} type="link" className="!p-0">
            编辑
          </Button>
          <Button onClick={(e) => handle(record, e)} type="link" className="!p-0 ml-1 mr-1">
            {record.process === 1 ? '开始' : '结束'}
          </Button>
          {record.process !== 1 ? (
            <Button type="link" disabled style={{ padding: 0 }}>
              删除
            </Button>
          ) : (
            <Popconfirm
              title="确定删除吗？"
              okText="确定"
              cancelText="取消"
              placement="topLeft"
              onConfirm={(e) => {
                e?.stopPropagation();
                handleDelete(record);
              }}
              onCancel={(e) => {
                e?.stopPropagation();
              }}
            >
              <Button
                type="link"
                onClick={(e) => {
                  e.stopPropagation();
                }}
                style={{ padding: 0 }}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </section>
      ),
    },
  ];

  const handleAdd = () => {
    const action = {
      type: 'changeForm',
      value: {
        taskType: 'singleTask',
        name: '',
        featureGroupList: '',
        pcapFilesList: [],
        status: '',
        curFeatureGrpList: [],
        taskName: '',
        sourceData: '',
        pcapRelayStatusDir: [],
        dir_name: '',
        schedule: {
          period: 'month',
          baseDate: '',
          baseTime: '',
        },
        type: 'pcap',
        taskId: '',
        dir: '',
      },
    };
    const changeAdd = {
      type: 'actionType',
      value: 'add',
    };
    store.dispatch(changeAdd);
    store.dispatch(action);
    setVisibleForm(true);
    // history.push('/app/mica/exploreForm');
  };
  // 搜索表单
  const onSubmit = () => {
    form.validateFields().then((value: any) => {
      const formvalue = {
        ...formValue,
        ...value,
        page: 1,
        pageSize: 10,
      };
      setformValue(formvalue);
    });
  };
  // 获取表格数据
  const searchList = (value: object | undefined) => {
    const params = {
      ...formValue,
      ...value,
    };
    if (params.time && params.time.length) {
      params.createStartTime = moment(params.time[0]).valueOf();
      params.createEndTime = moment(params.time[1]).valueOf();
    }
    getExploreTable(params).then(({ data }: any) => {
      setTableData(data.detail);
      settotal(data.count);
      const isDone = data?.detail?.every(({ process }: any) => process === 1);
      if (!isDone) {
        timerRef.current = setTimeout(() => {
          searchList({});
        }, 5000);
      } else {
        clearTimeout(timerRef.current);
      }
    });
  };
  const handle = (
    value: {
      status: any;
      taskId?: any;
    },
    e: any,
  ) => {
    e.stopPropagation();
    let status = '';
    if (value.process === 1) {
      status = 'start';
    } else {
      status = 'stop';
    }

    handleSwitch(value.taskId, {
      action: status,
    }).then((res) => {
      if (res.flag) {
        message.success('操作成功');
        searchList({});
      } else {
        message.error(res.message);
      }
    });
  };
  const handleViewMoreLog = (type: any) => {
    const { celeryId, startTime, endTime: _endTime } = rowRecord;
    console.log(333, startTime);
    const endTime = _endTime ? _endTime + 60000 * 15 : Date.now();
    if (!celeryId) {
      message.error('日志暂未生成');
    } else {
      switch (type) {
        case 'vul':
          history.push(`alarm/loopHole?${new URLSearchParams({ celeryId, startTime, endTime })}`);
          break;
        case 'ioc':
          history.push(`alarm/Intelligence?${new URLSearchParams({ celeryId, startTime, endTime })}`);
          break;
        case 'model':
          history.push(`alarm/Modal?${new URLSearchParams({ celeryId, startTime, endTime })}`);
          break;
        case 'file_log':
          history.push(`collect/fileAnalysis?${new URLSearchParams({ celeryId, startTime, endTime })}`);
          break;
        case 'pcap':
          history.push(`collect/logCollect?${new URLSearchParams({ celeryId, startTime, endTime })}`);
          break;
        default:
      }
    }
  };
  const handelDPILog = (value: any) => {
    const { celeryId, startTime, endTime } = rowRecord;
    if (!celeryId) {
      message.error('日志暂未生成');
    } else {
      history.push(
        `/app/mica/collect/logCollect?startTime=${startTime}&stopTime=${endTime}&celeryId=${celeryId}&dpilogType=conn`,
      );
    }
  };
  // const getPacpList = (value: any[]) => {
  //   pcapFilesList = value;
  // };

  const handleDelete = (value: { taskId: string }) => {
    deleteData(value.taskId).then((res) => {
      if (res.flag) {
        setexpandedRowKeys([]);
        searchList({});
        message.success(res.message);
      } else {
        res.message.forEach((item: any) => {
          message.error(item);
        });
      }
    });
  };

  // <KillChainStages data={statisticsInfo[key].killChains} showAll />

  const renderExpandedRow = (record: any, index: number) => {
    const { statisticsInfo = {}, endTime, type, process } = record;
    const { durationTime, flowSize, curFeatureCount, ...rest } = statisticsInfo;
    const warnningKeys = type === 'other' ? ['file_log'] : Object.keys(rest);

    return (
      <div className="flex flex-col justify-center">
        {warnningKeys.map((classKey: string) => {
          const threatLevel = statisticsInfo[classKey]?.threatLevel || { high: 0, medium: 0, low: 0 };
          const { killChains = [] } = statisticsInfo?.[classKey];
          const killArr: any = [];
          if (killChains?.length) {
            killChains.map((item) => {
              killArr[item.key] = item.doc_count;
            });
          }
          return (
            <div className={style.log} key={classKey}>
              <div className="font-700 w-94px">
                {hit[classKey]}
                {classKey === 'pcap' ? '解析' : '告警'}日志
              </div>
              <div className="w-150px ml-10px">总日志数：{statisticsInfo[classKey].logCount}</div>
              <div>|</div>
              {classKey !== 'pcap' ? (
                <>
                  <div className="w-300px ml-40px flex">
                    {Object.entries(severityNameMap).map(([key, value]) => {
                      return (
                        <div className="w-80px" key={`file_log_${value}`}>
                          {value}: {threatLevel[key]}
                        </div>
                      );
                    })}
                  </div>
                  <div>|</div>
                  <div className="w-150px ml-10px">
                    {hitName[classKey]}: {statisticsInfo[classKey].hitCount}
                  </div>
                  <div>|</div>
                  <div className="flex ml-10px items-center w-250px">
                    <div style={{ width: '90px' }}>杀伤链阶段：</div>
                    <KillChainStages data={statisticsInfo[classKey].killChains} showAll />
                  </div>
                </>
              ) : (
                <Space size="middle" className="ml-10px pl-30px w-757px">
                  <Tooltip
                    overlayInnerStyle={{ width: '350px' }}
                    title={
                      <div style={{ display: 'grid', 'grid-template-columns': '7rem 7rem 7rem' }}>
                        {Object.entries(statisticsInfo[classKey]).map(([item, value]) => {
                          if (item !== 'logCount') {
                            return (
                              <span key={item}>
                                {item}:{value} &nbsp;&nbsp;
                              </span>
                            );
                          }
                          return null;
                        })}
                      </div>
                    }
                  >
                    <span
                      style={{
                        display: 'inline-block',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                        width: '700px',
                      }}
                    >
                      {Object.entries(statisticsInfo[classKey]).map(([item, value]) => {
                        if (item !== 'logCount') {
                          return (
                            <span key={item}>
                              {item}:{value} &nbsp;&nbsp;
                            </span>
                          );
                        }
                        return null;
                      })}
                    </span>
                  </Tooltip>
                </Space>
              )}
              <div>|</div>
              <div className="ml-10px">
                <Button type="link" onClick={() => handleViewMoreLog(classKey)}>
                  查看更多日志
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  // 点击行
  const onRowClick = (record: any, index: number) => {
    // rowRecord = record;
    setRowRecord(record);
    if (expandedRowKeys.includes(index)) {
      setexpandedRowKeys([]);
    } else {
      setexpandedRowKeys([index]);
    }
  };
  // 翻页时关闭所有展开的行
  const onTableChange = () => {
    setexpandedRowKeys([]);
  };
  const editTask = (val: any, e: any) => {
    e.stopPropagation();
    const value = cloneDeep(val);
    if (value.curFeatureGrpList && value.curFeatureGrpList.length) {
      value.curFeatureGrpList = value.curFeatureGrpList.map((item: { groupId: any }) => item.groupId);
    }
    if (value.schedule.baseTime) {
      if (value.schedule.period === 'hour') {
        value.schedule.baseTime = moment(value.schedule.baseTime, 'mm:ss');
      } else {
        value.schedule.baseTime = moment(value.schedule.baseTime, 'HH:mm:ss');
      }
    }
    if (value.exploreStartDate && value.exploreEndDate) {
      value.time = [moment(value.exploreStartDate), moment(value.exploreEndDate)];
    }
    if (value.taskType === 'timedTask') {
      value.sourceData = value.sourceData.serverId;
    }
    const action = {
      type: 'changeForm',
      value,
    };
    const changeAdd = {
      type: 'actionType',
      value: 'edit',
    };
    // if (value.process !== 1) {
    //   changeAdd.value = 'edit';
    // }
    store.dispatch(changeAdd);
    store.dispatch(action);
    setVisibleForm(true);
    // history.push({
    //   pathname: '/app/mica/exploreForm',
    // });
  };
  const showPacp = (value: any) => {
    getTaskPacp({ taskName: value }).then((res) => {
      if (res.flag) {
        setpacpList(res.data.detail);
        setpacpCount(res.data.count);
        setvisible(true);
      } else {
        message.error(res.message);
      }
    });
  };
  const renderMenu = () => (
    <Menu onClick={handleTypeChange}>
      {exploreSort.map((item) => (
        <Menu.Item title={item.payload} key={item.key}>
          {item.name}
        </Menu.Item>
      ))}
    </Menu>
  );
  const handleTypeChange = (value: any) => {
    exploreSort.forEach((item) => {
      if (item.key === value.key) {
        const form_value = {
          ...formValue,
          sort: item.payload.sort,
          reverse: item.payload.reverse,
        };
        setsortType(item.name);
        setformValue(form_value);
      }
    });
  };
  // 分页change事件
  const pagination = {
    total,
    current: formValue.page || 1,
    pageSize: formValue.pageSize,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number) => `共${total}条`,
    onChange(page: number, pageSize: any) {
      setformValue({ ...formValue, page, pageSize });
      clearInterval(timer);
    },
  };
  return (
    <div>
      <Collapse defaultActiveKey={['1']}>
        <Panel header="搜索条件" key="1">
          <Form form={form} initialValues={{ taskName }} className="!flex" onFinish={onSubmit}>
            <Form.Item label="创建时间" name="time">
              <RangePicker
                allowClear
                ranges={{
                  近一年: [moment().add(-1, 'year'), moment()],
                  近半年: [moment().add(-6, 'month'), moment()],
                  近一月: [moment().add(-1, 'month'), moment()],
                  近一周: [moment().add(-7, 'd'), moment()],
                  近一天: [moment().add(-1, 'd'), moment()],
                  今天: [moment().startOf('day'), moment().endOf('day')],
                  本周: [moment().startOf('week'), moment().endOf('week')],
                  本月: [moment().startOf('month'), moment().endOf('month')],
                  本年度: [moment().startOf('year'), moment().endOf('year')],
                }}
                style={{ width: 300 }}
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
            <Form.Item label="任务名称" name="taskName">
              <Input placeholder="请输入任务名称" allowClear />
            </Form.Item>
            <Form.Item label="任务类型" name="taskType">
              <Select allowClear placeholder="请选择任务类型">
                {exploreTaskType.map((item) => (
                  <Option key={item.key} value={item.key}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item style={{ paddingLeft: 20 }} className="text-right">
              <Button className="margin_right_10" type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleAdd} type="primary" ghost>
                新建任务
              </Button>
            </Form.Item>
          </Form>
        </Panel>
      </Collapse>
      <div className={style.table_top}>
        <span className={style.sortBox}>
          <Dropdown overlay={renderMenu()} trigger={['click']}>
            <a className="ant-dropdown-link">
              {sortType} <DownOutlined />
            </a>
          </Dropdown>
        </span>
      </div>
      <Table
        pagination={pagination}
        expandedRowRender={(record: {}, index: number) => renderExpandedRow(record, index)}
        onRow={(record: object, index: any) => ({
          onClick: () => onRowClick(record, index),
        })}
        onChange={onTableChange}
        expandIconAsCell={false}
        expandIconColumnIndex={-1}
        expandedRowKeys={expandedRowKeys}
        columns={columns}
        rowKey={(r: any, i: any) => i}
        dataSource={tableData}
      ></Table>
      <Modal
        title={
          <div>
            <span style={{ paddingRight: 6 }}>{`数据包列表(总数：${pacpCount})`}</span>
            <span className="pr-2">文件总大小：{flowSize}</span>
            <Tooltip title="数据包的状态有：回放成功、回放失败、回放撤销、下载完成、下载失败">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        }
        onCancel={() => {
          setvisible(false);
        }}
        // title="数据包列表"
        width={1200}
        visible={visible}
        footer={
          <Button
            onClick={() => {
              setvisible(false);
            }}
            type="primary"
          >
            确定
          </Button>
        }
        cancelText={false}
      >
        <div className={style.pacp_box}>
          {pacpList.map((item: any, index: any) => (
            <div key={item.pcapName} style={{ width: '33%' }}>
              <span>
                {item.status}
                &nbsp;&nbsp;&nbsp;&nbsp;
                <Tooltip title={item.pcapName}>{ellipsis(item.pcapName, 40)}</Tooltip>
              </span>
            </div>
          ))}
        </div>
      </Modal>
      <Modal
        title={store.getState().actionType === 'edit' ? '编辑任务' : '新建任务'}
        onCancel={() => {
          setVisibleForm(false);
        }}
        visible={visibleForm}
        width="50%"
        destroyOnClose
        footer={null}
        maskClosable={false}
      >
        {visibleForm && (
          <Addtask
            onhide={() => {
              setVisibleForm(false);
              searchList({});
            }}
          />
        )}
      </Modal>
    </div>
  );
};
export default Index;
