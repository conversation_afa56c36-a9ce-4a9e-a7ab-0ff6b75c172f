import React from 'react';
import { Spin } from 'antd';
import style from './index.less';

interface Params {
  loading?: boolean;
  className?: string;
  tip?: string;
}

function XSpin(params: Params) {
  const { loading = true, className, tip } = params;

  return (
    <div className={className ? `${className} ${style.XSpin}` : style.XSpin}>
      <div />
      <Spin tip={tip || '载入中...'} spinning={loading} />
      <div />
    </div>
  );
}

export default XSpin;
