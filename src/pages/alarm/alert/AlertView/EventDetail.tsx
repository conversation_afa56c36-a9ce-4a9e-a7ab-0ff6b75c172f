/* eslint-disable default-case */
/* eslint-disable jsx-a11y/interactive-supports-focus */
import CodeHighlight from '@/components/CodeHighlight';
import Empty from '@/components/Empty';
import { useHistory } from '@/hooks/global';
import { getIntelligenceDetail, getMdoalEvnet, getModalDetail, loopHoleEventDetail, getFileDetail } from '@/services/alarm';
import { ellipsis, formatTime } from '@/utils/utils';
import { Button, Modal, Spin, Table, Tooltip } from 'antd';
import cn from 'classnames';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { downloadEvent, getPackageFun } from './download';
import { colRenderer, preparePayload } from './shared';
import OriginaPackage from './originaPackage';
export const DETAIL_QUERY_FUNC_MAP = {
  vul: loopHoleEventDetail,
  ioc: getIntelligenceDetail,
  model: getMdoalEvnet,
  file: getFileDetail,
};

const GAP_TIME = 120000;

/**
 * 告警展开行详情
 * @param Props
 * @returns
 */
export default function EventDetail({
  type,
  celeryId,
  pcap_filename,
  groupKey,
  confirm,
  modelName,
  threatLevel,
  threatScore,
  taskName,
  taskType,
  value,
  filter,
  record,
}: any) {
  const [loading, setloading] = useState(true);
  const [data, setdata] = useState([]);
  const [total, settotal] = useState(null);
  const history = useHistory();
  const [active, setActive] = useState({ index: 0, item: {} });
  useEffect(() => {
    getDetail();
  }, [filter]);

  /**
   * 获取当前告警详情
   */
  const getDetail = () => {
    DETAIL_QUERY_FUNC_MAP[type]({
      groupKey,
      confirm,
      value,
      ...preparePayload(filter),
      page: 1,
      pageSize: 10,
    }).then((res) => {
      if (res.flag) {
        const data = res.data.alters.map((item: any, index: number) => {
          return {
            ...item,
            itemId: JSON.stringify(item) + index,
          };
        });
        setdata(data);
        settotal(res.data.total);
        setloading(false);
        setActive({ index: 0, item: data[0] });
      }
    });
  };

  if (_.isEmpty(data)) {
    return (
      <Spin spinning={loading}>
        <Empty className="min-h-14" />
      </Spin>
    );
  }

  // const EventList = EVENT_LIST_COMPONENT_MAP[type];

  return (
    <Spin spinning={loading}>
      <div className="bg-d0">
        <div className="flex w-100% max-w-320 m-[0_auto] p-6 relative" id="surveyKillChainEventListContent">
          <Source history={history} pcap_filename={pcap_filename} activeItem={active.item} celeryId={celeryId} />
          <EventList
            className="flex-[1] of-hidden"
            type={type}
            record={record}
            data={data}
            total={total}
            active={active.index}
            modelName={modelName}
            threatLevel={threatLevel}
            threatScore={threatScore}
            taskName={taskName}
            taskType={taskType}
            onChange={setActive}
          />
          <Target history={history} activeItem={active.item} pcap_filename={pcap_filename} celeryId={celeryId} />
        </div>
      </div>
    </Spin>
  );
}

function Source({
  activeItem,
  history,
  pcap_filename,
  celeryId,
}: {
  type: string;
  activeItem?: any;
  history?: any;
  pcap_filename?: any;
  celeryId: any;
}) {
  /**
   * 跳转到日志页面  时间取前后5分钟
   * @param ip
   * @param dpilogType 日志类型
   */
  const jump = (ip: any, dpilogType: any) => {
    const strArr = pcap_filename.split('/');

    history.push(
      `/app/mica/collect/logCollect?${new URLSearchParams(
        [
          ['ip', ip],
          ['dpilogType', dpilogType],
          ['celeryId', celeryId],
          ['pcap_filename', strArr[strArr.length - 1]],
          ['startTime', activeItem.observedTime - GAP_TIME],
          ['endTime', activeItem.observedTime + GAP_TIME],
        ].concat(/.*[\u4e00-\u9fa5]+.*$/.test(strArr[strArr.length - 1]) ? [['includeCN', true]] : []),
      )}`,
    );
  };

  // 跳转到画像
  const goMeans = (value: any) => {
    history.push(`/app/mica/meansDetail?ip_addr=${value}`);
  };

  // 访问连
  const showPath = (stopTime: any, ip: any) => {
    history.push(`/app/mica/collect/pathAnalysis?startTime=${stopTime - 86400000}&stopTime=${stopTime}&ip=${ip}`);
  };

  // 跳转到攻击链
  const showAttacks = (time: any, ip: any, taskType: any) => {
    history.push(
      `/app/mica/collect/attacksAnalysis?startTime=${time - 1209600000}&stopTime=${
        time + 604800000
      }&ip=${ip}&replay=${taskType}`,
    );
  };

  return (
    <div className="w-70 z-1">
      <div className="text-md w-70 px-6 py-4 bg-d100 b b-solid b-brd6">
        <div>源IP：{activeItem.src_ip}</div>
        <div className="mt-4 flex gap-4">
          <a
            className="mr-2 underline"
            role="button"
            onClick={() => {
              goMeans(activeItem.src_ip);
            }}
          >
            画像
          </a>
          <a
            className="mr-2 underline"
            role="button"
            onClick={(e: any) => {
              jump(activeItem.src_ip, activeItem.proto);
            }}
          >
            日志
          </a>
          <a
            className="mr-2 underline"
            role="button"
            onClick={(e: any) => {
              showPath(activeItem.observedTime, activeItem.src_ip);
            }}
          >
            访问链
          </a>
          <a
            className="mr-2 underline"
            role="button"
            onClick={() => {
              showAttacks(activeItem.observedTime, activeItem.src_ip, activeItem.taskType);
            }}
          >
            攻击链
          </a>
        </div>
      </div>
    </div>
  );
}

function EventList({
  className,
  type,
  data,
  total,
  modelName,
  active,
  threatLevel,
  threatScore,
  taskName,
  taskType,
  record,
  onChange,
}: any) {
  const [visiblePacp, setVisiblePacp] = React.useState(false);
  const [filePath, setFilePath] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const ref = React.useRef();
  const y = active * 28;
  const handleLook = async (type: string, item: Record<string, any>) => {
    setVisiblePacp(true);
    setLoading(true);
    const res = await getPackageFun(type, item);
    setFilePath(res);
    setLoading(false);
  };
  const renderOriginaPackage = () => {
    if (loading) {
      return (
        <div
          style={{
            margin: '20px 0',
            marginBottom: '20px',
            padding: '30px 50px',
            textAlign: 'center',
            background: 'rgba(0, 0, 0, 0.05)',
            borderRadius: '4px',
          }}
        >
          <Spin spinning={loading} />
        </div>
      );
    }
    return <OriginaPackage filePath={filePath} />;
  };

  return (
    <div ref={ref} className={cn('relative flex justify-center', className)}>
      <svg className="w-10 h-100%" viewBox="0 0 40 100%">
        <path fill="none" stroke="#E6EAEF" d={`M 0,45 l 20,0 l 0,${y + 114} l 20,0`} />
      </svg>
      <div id="event-list" className="flex-[1] of-hidden b-b b-b-solid b-b-brd6">
        <div className="mt-8 h-8 lh-8 text-center">共 {total} 条</div>
        {data.map((item: any, index: number) => {
          const isActive = active === index;

          let activeDetail;
          if (isActive) {
            switch (type) {
              case 'vul':
                activeDetail = (
                  <>
                    <div className="b b-solid b-brd6 b-b-none bg-d100 px-6 py-2">
                      <div className="grid grid-cols-[repeat(3,auto)] justify-between ws-nowrap gap-[0.25rem_1rem]">
                        <span className="inline-block truncate">
                          威胁评分：{colRenderer.threatScore(threatScore, { threatLevel })}
                        </span>
                        <span className="inline-block truncate">规则ID：{colRenderer.ruleId(item.sid)}</span>
                        <span className="inline-block truncate">方向：{item.direction || '–'}</span>
                        <span className="inline-block truncate">源端口：{item.src_port || '–'}</span>
                        <span className="inline-block truncate">目的端口：{item.dst_port || '–'}</span>
                        <span className="inline-block truncate">响应：{item.responseName || '–'}</span>
                      </div>
                      <div className="flex justify-between mt-1">
                        <span className="inline-block truncate" title={item.src_mac}>
                          源MAC：{item.src_mac || '–'}
                        </span>
                        <span className="inline-block truncate" title={item.dst_mac}>
                          目的MAC：{item.dst_mac || '–'}
                        </span>
                      </div>
                      <div className="flex mt-1 items-center">
                        <Tooltip
                          placement="bottom"
                          trigger="click"
                          title={
                            <CodeHighlight
                              code={item.payload}
                              language={undefined}
                              hasPre={undefined}
                              changeRule={undefined}
                            />
                          }
                          overlayStyle={{ maxWidth: 500, maxHeight: 300 }}
                        >
                          <a>攻击载荷</a>
                        </Tooltip>
                        <span className="inline-block truncate ml-134px" title={item.dst_mac}>
                          攻击流量：
                          <Button
                            style={{ padding: 0 }}
                            type="link"
                            onClick={() => {
                              handleLook(type, item);
                            }}
                            disabled={!item.pcap_filename && !item.not_fss_pcapname}
                          >
                            查看
                          </Button>
                          /{' '}
                          <Button
                            type="link"
                            style={{ padding: 0 }}
                            disabled={!item.pcap_filename && !item.not_fss_pcapname}
                            onClick={() => downloadEvent(type, item)}
                          >
                            下载
                          </Button>
                        </span>
                      </div>
                      <Modal
                        onOk={() => setVisiblePacp(false)}
                        onCancel={() => setVisiblePacp(false)}
                        visible={visiblePacp}
                        width={1200}
                        destroyOnClose={true}
                      >
                        {renderOriginaPackage()}
                      </Modal>
                    </div>{' '}
                  </>
                );
                break;
              case 'ioc':
                activeDetail = (
                  <div className="b b-solid b-brd6 b-b-none bg-d100 px-6 py-2">
                    <div>
                      情报IOC：
                      <span>{colRenderer.ioc(item.ioc)}</span>
                    </div>
                    <div className="mt-2 grid grid-cols-[repeat(3,auto)] ws-nowrap gap-[0.25rem_1rem]">
                      <span className="inline-block truncate">
                        威胁评分：{colRenderer.threatScore(threatScore, { threatLevel })}
                      </span>
                      <span className="inline-block truncate">源端口：{item.src_port || '–'}</span>
                      <span className="inline-block truncate">目的端口：{item.dst_port || '–'}</span>
                      <span className="inline-block truncate">方向：{item.direction || '–'}</span>
                      <span className="inline-block truncate">响应：{item.responseName || '–'}</span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="inline-block truncate" title={item.src_mac}>
                        源MAC：{item.src_mac || '–'}
                      </span>
                      <span className="inline-block truncate" title={item.dst_mac}>
                        目的MAC：{item.dst_mac || '–'}
                      </span>
                    </div>
                    <div className="flex gap-6 mt-2">
                      <Tooltip
                        placement="bottom"
                        trigger="click"
                        title={
                          <>
                            <IocTitle item={item} />
                            <IocDetail iocInfo={item} />
                          </>
                        }
                        overlayStyle={{ maxWidth: 500, maxHeight: 300 }}
                      >
                        <a>查看更多</a>
                      </Tooltip>
                      <span className="inline-block truncate ml-134px" title={item.dst_mac}>
                        攻击流量：
                        <Button
                          style={{ padding: 0 }}
                          type="link"
                          onClick={() => {
                            handleLook(type, item);
                          }}
                          disabled={!item.pcap_filename && !item.not_fss_pcapname}
                        >
                          查看
                        </Button>
                        /{' '}
                        <Button
                          type="link"
                          style={{ padding: 0 }}
                          disabled={!item.pcap_filename && !item.not_fss_pcapname}
                          onClick={() => downloadEvent(type, item)}
                        >
                          下载
                        </Button>
                      </span>
                    </div>
                    <Modal
                      onOk={() => setVisiblePacp(false)}
                      onCancel={() => setVisiblePacp(false)}
                      visible={visiblePacp}
                      width={1200}
                      destroyOnClose={true}
                    >
                      {renderOriginaPackage()}
                    </Modal>
                  </div>
                );
                break;
              case 'model':
                activeDetail = (
                  <div className="b b-solid b-brd6 b-b-none bg-d100 px-6 py-2">
                    <div className="mt-2 grid grid-cols-[repeat(3,auto)] justify-between ws-nowrap gap-[0.25rem_1rem]">
                      <span className="inline-block truncate">
                        威胁评分：{colRenderer.threatScore(threatScore, { threatLevel })}
                      </span>
                      <span className="inline-block truncate">源端口：{item.src_port || '–'}</span>
                      <span className="inline-block truncate">目的端口：{item.dst_port || '–'}</span>
                      <span className="inline-block truncate">关键字：{item.keyword || '–'}</span>
                      <span className="inline-block truncate">会话数：{item.session || '–'}</span>
                      <span className="inline-block truncate">响应：{item.responseName || '–'}</span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="inline-block truncate" title={item.src_mac}>
                        源MAC：{item.src_mac || '–'}
                      </span>
                      <span className="inline-block truncate" title={item.dst_mac}>
                        目的MAC：{item.dst_mac || '–'}
                      </span>
                    </div>
                    <div className="flex gap-6 mt-2">
                      <ModelDetail modelName={modelName} value={item._id}>
                        源始日志查看
                      </ModelDetail>
                      <span className="inline-block truncate ml-134px" title={item.dst_mac}>
                        攻击流量：
                        <Button
                          style={{ padding: 0 }}
                          type="link"
                          onClick={() => {
                            handleLook(type, item);
                          }}
                          disabled={!item.pcap_filename && !item.not_fss_pcapname}
                        >
                          查看
                        </Button>
                        /{' '}
                        <Button
                          type="link"
                          style={{ padding: 0 }}
                          disabled={!item.pcap_filename && !item.not_fss_pcapname}
                          onClick={() => downloadEvent(type, item)}
                        >
                          下载
                        </Button>
                      </span>
                    </div>
                    <Modal
                      onOk={() => setVisiblePacp(false)}
                      onCancel={() => setVisiblePacp(false)}
                      visible={visiblePacp}
                      width={1200}
                      destroyOnClose={true}
                    >
                      {renderOriginaPackage()}
                    </Modal>
                  </div>
                );
                break;
              case 'file':
                activeDetail = (
                  <div className="b b-solid b-brd6 b-b-none bg-d100 px-6 py-2">
                    <div className="mt-2 grid grid-cols-[repeat(3,auto)] justify-between ws-nowrap gap-[0.25rem_1rem]">
                      <span className="inline-block truncate">
                        威胁评分：{colRenderer.threatScore(threatScore, { threatLevel })}
                      </span>
                      <span className="inline-block truncate">源端口：{item.src_port || '–'}</span>
                      <span className="inline-block truncate">目的端口：{item.dst_port || '–'}</span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="inline-block truncate" title={item.src_mac}>
                        源MAC：{item.src_mac || '–'}
                      </span>
                      <span className="inline-block truncate" title={item.dst_mac}>
                        目的MAC：{item.dst_mac || '–'}
                      </span>
                    </div>
                    <div className="flex gap-6 mt-2">
                      <Tooltip
                        placement="bottom"
                        trigger="click"
                        title={
                          <>
                            <FileTitle record={record} />
                          </>
                        }
                        overlayStyle={{ maxWidth: 500, maxHeight: 300 }}
                      >
                        <a>查看更多</a>
                      </Tooltip>
                      <span className="inline-block truncate ml-134px" title={item.dst_mac}>
                        攻击流量：
                        <Button
                          style={{ padding: 0 }}
                          type="link"
                          onClick={() => {
                            handleLook(type, item);
                          }}
                          disabled={!item.pcap_filename && !item.not_fss_pcapname}
                        >
                          查看
                        </Button>
                        /{' '}
                        <Button
                          type="link"
                          style={{ padding: 0 }}
                          disabled={!item.pcap_filename && !item.not_fss_pcapname}
                          onClick={() => downloadEvent(type, item)}
                        >
                          下载
                        </Button>
                      </span>
                    </div>
                    <Modal
                      onOk={() => setVisiblePacp(false)}
                      onCancel={() => setVisiblePacp(false)}
                      visible={visiblePacp}
                      width={1200}
                      destroyOnClose={true}
                    >
                      {renderOriginaPackage()}
                    </Modal>
                  </div>
                );
                break;
            }
          }
          return (
            <div>
              <div
                className={cn(
                  'w-100% flex justify-between items-center px-6 b b-solid b-brd6 b-b-none cursor-pointer ws-nowrap',
                  isActive ? 'active h-12 text-md c-t5 b-l-u500 b-l-2' : 'h-7 text-sm c-t4',
                )}
                onClick={() => onChange({ index, item })}
              >
                <span>
                  <div>发现时间：{new Date(item.observedTime).toLocaleString('swe')}</div>
                  {isActive && (
                    <div className="text-sm c-t4">发生时间：{new Date(item.occurredTime).toLocaleString('swe')}</div>
                  )}
                </span>
                <span>协议：{item.proto?.toUpperCase()}</span>
              </div>
              {isActive && activeDetail}
            </div>
          );
        })}
      </div>

      <svg className="w-10 h-100%" viewBox="0 0 40 100%">
        <path
          fill="none"
          stroke="#E6EAEF"
          d={`M 0,${y + 114 + 45} l 20,0 l 0,${(y + 114) * -1} l 20,0 l -8,6 l 8,-6 l -8,-6`}
        />
      </svg>
    </div>
  );
}
function FileTitle({ record }: any) {
  console.log('[ item ]-561', record);
  return (
    <div>
      <div>
        <div>文件名：</div>
        <div className="ml-2">{record.filename}</div>
      </div>
      <div>
        <div>文件大小：</div>
        <div className="ml-2">{record.filesize}</div>
      </div>
      <div>
        <div>md5：</div>
        <div className="ml-2">{record.md5}</div>
      </div>
      <div>
        <div>标签：</div>
        <div className="ml-2">{record.labels}</div>
      </div>
    </div>
  );
}
function IocTitle({ item }: any) {
  let title = null;
  if (item.protocol === 'http') {
    title = (
      <div>
        <div>
          <div>访问URL：</div>
          <div>
            {item.host}
            {item.uri}
          </div>
        </div>
        <div>
          <div>HTTP请求方法：</div>
          <div>{item.method}</div>
        </div>
        <div>
          <div>HTTP返回码：</div>
          <div>{item.status_code}</div>
        </div>
        <div>
          <div>HTTP请求头信息：</div>
          <div style={{ width: '320px' }}>User-Agent：{item.user_agent}</div>
        </div>
        <div>
          <div>HTTP请求Body信息：</div>
          <div> Body长度：{item.request_body_len}</div>
        </div>
        <div>
          <div>HTTP响应头信息：</div>
          <div>
            <div>Body长度：{item.response_body_len}</div>
            <div>
              content-type：
              {item.resp_mime_types ? item.resp_mime_types.join(',') : ''}
            </div>
          </div>
        </div>
      </div>
    );
  } else if (item.protocol === 'dns') {
    title = (
      <div>
        <div>
          <div>DNS查询内容：</div>
          <div>{item.query}</div>
        </div>
        <div>
          <div>DNS查询类型：</div>
          <div>{item.qtype_name}</div>
        </div>
        <div>
          <div>DNS查询结果：</div>
          <div>{item.result}</div>
        </div>
      </div>
    );
  } else if (item.protocol === 'tcp' || item.protocol === 'udp' || item.protocol === 'icmp') {
    title = (
      <div>
        <div>
          <div>发送数据包：</div>
          <div>{item.orig_pkts}</div>
        </div>
        <div>
          <div>接收数据包：</div>
          <div>{item.resp_pkts}</div>
        </div>
        <div>
          <div>发送数据量：</div>
          <div>{item.orig_ip_bytes}</div>
        </div>
        <div>
          <div>接收数据量：</div>
          <div style={{ width: '320px' }}>{item.resp_ip_bytes}</div>
        </div>
      </div>
    );
  } else if (item.protocol === 'files') {
    let filename = '';
    const url = item.extracted ? item.extracted.split('/') : '';
    const len = url.length;
    filename = url ? url[len - 1] : '';
    title = (
      <div>
        <div>
          <div>文件名：</div>
          <div>{filename}</div>
        </div>
        <div>
          <div>来源：</div>
          <div>{item.source}</div>
        </div>
      </div>
    );
  } else if (item.topic === 'ftp') {
    title = (
      <div>
        <div>
          <div>command：</div>
          <div>{item.command}</div>
        </div>
        <div>
          <div>arg：</div>
          <div>{item.arg}</div>
        </div>
        <div>
          <div>reply_code：</div>
          <div>{item.reply_code}</div>
        </div>
        <div>
          <div>seq：</div>
          <div>{item.seq}</div>
        </div>
        <div>
          <div>reply_msg：</div>
          <div>{item.reply_msg}</div>
        </div>
        <div>
          <div>keyword：</div>
          <div>{item.keyword}</div>
        </div>
      </div>
    );
  }

  return title;
}

function IocDetail({ iocInfo }: any) {
  return (
    <div>
      <div>
        <span>APT组织：</span>
        <span>{iocInfo.aptOrganization || '--'}</span>
      </div>
      <div>
        <span>情报来源：</span>
        <span>{iocInfo.info || '--'}</span>
      </div>
      {iocInfo.server_name ? (
        <div>
          <span>服务器名称：</span>
          <span>{iocInfo.server_name || '--'}</span>
        </div>
      ) : null}
      <div>
        <span>披露时间：</span>
        <span>{formatTime(iocInfo.disclosuretime, 'YYYY-MM-DD')}</span>
      </div>
      <div>
        <span>工具：</span>
        <span>{iocInfo.tool ? iocInfo.tool.join(',') : '--'}</span>
      </div>
      <div>
        <span>标签：</span>
        <span>{iocInfo.tag ? iocInfo.tag : '--'}</span>
      </div>
      <div>
        <span>参考：</span>
        <span>{iocInfo.refer || '--'}</span>
      </div>
    </div>
  );
}

function ModelDetail({ modelName, value, children }: any) {
  const [loading, setLoading] = React.useState(false);
  const [visible, setVisible] = React.useState(false);
  const [tableData, setTableData] = React.useState({ columns: [], data: [] });

  const handleGetDetail = () => {
    setLoading(true);
    getModalDetail({ modelLogId: value })
      .then((res) => {
        if (!res.data?.detail?.[0]) return;

        let columns = [
          {
            title: '发生时间',
            dataIndex: 'ts',
            render: (t: any) => {
              return new Date(t).toLocaleString('swe');
            },
          },
          {
            title: '源ip',
            dataIndex: 'orig_ip',
          },
          {
            title: '源端口',
            dataIndex: 'orig_port',
          },
          {
            title: '目的ip',
            dataIndex: 'resp_ip',
          },
          {
            title: '目的端口',
            dataIndex: 'resp_port',
          },
          {
            title: '协议',
            dataIndex: 'proto',
          },
        ];

        if (modelName === 'ICMP隐蔽隧道') {
          let data = ['payload_len', 'itype', 'seq', 'echo_id', 'payload'];
          data.forEach((item) => {
            columns.push({
              title: item,
              dataIndex: item,
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          });
        }
        if (modelName === 'DGA_domain' || modelName === 'DNS隧道异常检测' || modelName === '针对中国诱导网站检测模型') {
          if (res.data.detail[0].proto === 'http') {
            columns.push({
              title: '域名',
              dataIndex: 'host',
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          } else {
            columns.push({
              title: '域名',
              dataIndex: 'query',
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          }
        } else if (modelName === '主机扫描') {
          if (res.data.detail[0].proto === 'icmp') {
            columns.push({
              title: 'itype',
              dataIndex: 'itype',
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          } else {
            columns.push({
              title: 'orig_pkts',
              dataIndex: 'orig_pkts',
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          }
        } else if (modelName === '服务器路径爆破检测模型' || modelName === 'APT32_URL规则') {
          columns.push({
            title: 'uri',
            dataIndex: 'uri',
            render: (t: any) => {
              return (
                <Tooltip placement="bottom" title={t}>
                  {ellipsis(t)}
                </Tooltip>
              );
            },
          });
        } else if (modelName === '端口扫描') {
          let data = ['orig_pkts', 'resp_pkts'];
          data.forEach((item) => {
            columns.push({
              title: item,
              dataIndex: item,
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          });
        } else if (modelName === 'RDP爆破') {
          columns.push({
            title: 'established',
            dataIndex: 'established',
            render: (t: any) => {
              return (
                <Tooltip placement="bottom" title={t}>
                  {ellipsis(t)}
                </Tooltip>
              );
            },
          });
        } else if (modelName === '弱口令登录' || modelName === '密码爆破') {
          let data = ['username', 'password'];
          data.forEach((item) => {
            columns.push({
              title: item,
              dataIndex: item,
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          });
        } else if (modelName === 'Ddos_Flood_攻击') {
          if (res.data.detail[0].proto === 'http') {
            let data = ['uri', 'method'];
            data.forEach((item) => {
              columns.push({
                title: item,
                dataIndex: item,
                render: (t: any) => {
                  return (
                    <Tooltip placement="bottom" title={t}>
                      {ellipsis(t)}
                    </Tooltip>
                  );
                },
              });
            });
          } else if (res.data.detail[0].proto === 'dns') {
            columns.push({
              title: '域名',
              dataIndex: 'query',
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          } else if (res.data.detail[0].proto === 'icmp') {
            columns.push({
              title: 'itype',
              dataIndex: 'itype',
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          }
        } else if (modelName === 'WEB命令执行检测') {
          let data = ['method', 'uri', 'code_state', 'body'];
          data.forEach((item) => {
            columns.push({
              title: item,
              dataIndex: item,
              render: (t: any) => {
                return (
                  <Tooltip placement="bottom" title={t}>
                    {ellipsis(t)}
                  </Tooltip>
                );
              },
            });
          });
        }

        setTableData({ columns, data: res.data.detail });
        setVisible(true);
      })
      .finally(() => setLoading(false));
  };

  return (
    <>
      <Button className="!h-auto !lh-1em !p-0" type="link" loading={loading} onClick={handleGetDetail}>
        {children}
      </Button>
      <Modal onOk={() => setVisible(false)} onCancel={() => setVisible(false)} visible={visible} width={1200}>
        <Table pagination={false} dataSource={tableData.data} columns={tableData.columns} />
      </Modal>
    </>
  );
}

function Target(Props: any) {
  const { activeItem, history, pcap_filename, celeryId } = Props;
  // 跳转日志页面
  const jump = (ip: any, dpilogType: any) => {
    const strArr = pcap_filename.split('/');
    console.log('[ strArr ]-864', strArr);
    if (/.*[\u4e00-\u9fa5]+.*$/.test(strArr[strArr.length - 1])) {
      history.push(
        `/app/mica/collect/logCollect?includeCN=true&pcap_filename=${strArr[strArr.length - 1]}&ip=${ip}&startTime=${
          activeItem.observedTime - GAP_TIME
        }&endTime=${activeItem.observedTime + GAP_TIME}&dpilogType=${dpilogType}&celeryId=${celeryId}`,
      );
    } else {
      history.push(
        `/app/mica/collect/logCollect?pcap_filename=${strArr[strArr.length - 1]}&ip=${ip}&startTime=${
          activeItem.observedTime - GAP_TIME
        }&endTime=${activeItem.observedTime + GAP_TIME}&dpilogType=${dpilogType}&celeryId=${celeryId}`,
      );
    }
  };

  // 跳转画像页面
  const goMeans = (value: any) => {
    history.push(`/app/mica/meansDetail?ip_addr=${value}`);
  };

  // 跳转到访问链
  const showPath = (stopTime: any, ip: any) => {
    history.push(`/app/mica/collect/pathAnalysis?startTime=${stopTime - 86400000}&stopTime=${stopTime}&ip=${ip}`);
  };

  // 跳转到攻击链
  const showAttacks = (time: any, ip: any, taskType: any) => {
    history.push(
      `/app/mica/collect/attacksAnalysis?startTime=${time - 1209600000}&stopTime=${
        time + 604800000
      }&ip=${ip}&replay=${taskType}`,
    );
  };

  return (
    <div className="w-70 z-1">
      <div className="text-md w-70 px-6 py-4 text-left bg-d100 b b-solid b-brd6">
        <div>目的IP：{activeItem.dst_ip}</div>
        <div className="mt-4 flex gap-4">
          <a
            className="mr-2 underline"
            onClick={() => {
              goMeans(activeItem.dst_ip);
            }}
          >
            画像
          </a>
          <a
            className="mr-2 underline"
            onClick={() => {
              jump(activeItem.dst_ip, activeItem.proto);
            }}
          >
            日志
          </a>
          <a
            className="mr-2 underline"
            role="button"
            onClick={() => {
              showPath(activeItem.observedTime, activeItem.dst_ip);
            }}
          >
            访问链
          </a>
          <a
            className="mr-2 underline"
            role="button"
            onClick={() => {
              showAttacks(activeItem.observedTime, activeItem.dst_ip, activeItem.taskType);
            }}
          >
            攻击链
          </a>
        </div>
      </div>
    </div>
  );
}
