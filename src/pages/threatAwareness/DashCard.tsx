import React from 'react';
import cs from 'classnames';
import style from './index.less';


export type DashCard = {
  title: React.ReactNode,
  className?: string,
  contentStyle?: React.CSSProperties;
  children?: React.ReactNode
}

export const DashCard = ({ className, title, children, contentStyle }: DashCard) => {
  return (
    <div
      className={cs(style.customize_overview_card, className)}
    >
      <div className={style.title}>{title}</div>
      <div
        style={contentStyle}
        className={style.content}
      >
        {children}
      </div>
    </div>
  );
};
