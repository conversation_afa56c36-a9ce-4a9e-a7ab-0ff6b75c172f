import Web_attack from '@/assets/images/treat_Web_attack.png';
import Exploits from '@/assets/images/treat_Exploits.png';
import APT from '@/assets/images//treat_APT.png';
import Malicious from '@/assets/images/treat_Malicious.png';
import Suspicious from '@/assets/images/treat_Suspicious.png';
import Botnet from '@/assets/images/treat_Botnet.png';
import Phishing from '@/assets/images/treat_Phishing.png';
import Mining from '@/assets/images/treat_Mining.png';
import Scanning from '@/assets/images/treat_Scanning.png';
import Malware from '@/assets/images/treat_Malware.png';
import DOS from '@/assets/images/treat_DoS.png';
import Webshell from '@/assets/images/treat_Webshell.png';
import C2 from '@/assets/images/treat_C2.png';
import Proxy from '@/assets/images/treat_Proxy.png';
import Brute from '@/assets/images/treat_Brute.png';
import Trojan from '@/assets/images/treat_Trojan.png';
import Ransomware from '@/assets/images/treat_Ransomware.png';
import Spam from '@/assets/images/treat_Spam.png';
import Hfs from '@/assets/images/treat_Hfs.png';
import Tor from '@/assets/images/treat_Tor.png';
import URL_malware from '@/assets/images//treat_URL_malware.png';
import VPN from '@/assets/images/treat_VPN.png';
import Spyware from '@/assets/images/treat_Spyware.png';
// 威胁等级
export enum threatLevel {
  '高' = 'High',
  '中' = 'Medium',
  '低' = 'Low',
}

// 攻击结果
export enum attackResults {
  '成功' = 'true',
  '失败' = 'false',
}
// 任务类型
export enum taskType {
  '实时' = 'realTime',
  '回放' = 'replay',
}
// 告警处理select
export const alarmHandling = {
  未处理: 0,
  已处理: 1,
  已关注: 2,
  攻击成功:3,
  误报:4
};
export const alarmHandlingName = {
  0: '未处理',
  1: '已处理',
  2: '已关注',
  3:'攻击成功',
  4:'误报'
};

// 重点资产
export enum keyAssets {
  '系统定义' = 'system',
  '自定义' = 'customize',
}
// 时间类型

export const timeType = [{ value: 'observed', key: '发现时间' }];

export const timeTypeColect = [{ value: 'observedTime', key: '发现时间' }];

export const attacksReplayType = [
  { value: true, key: '是' },
  { value: false, key: '否' },
];

export const threatLevelColorMap = {
  High: '#ff252a',
  Medium: '#ffad00',
  Low: '#ffd800',
  Safe: '#00af40',
};
export const threatFlagNameMap = {
  // Web_attack: 'Web攻击',
  'Exploits and Attacks': '攻击利用',
  APT: 'APT攻击',
  'Malicious Host': '恶意主机',
  Suspicious: '可疑行为',
  Botnet: '僵尸网络',
  Phishing: '钓鱼邮件',
  Scanning: '恶意扫描',
  Malware: '恶意软件',
  DOS: 'DOS攻击',
  Trojan: '远控木马',
  Mining: '挖矿木马',
  Ransomware: '勒索软件',
  Spyware: '间谍软件',
  Webshell: 'WEBSHELL',
  URL_malware: '恶意网站',
  'Brute force': '爆破',
  // C2: 'C2服务器',
  // Proxy: 'proxy代理',
  // Spam: '恶意邮件',
  // Hfs: 'HFS站点',
  // Tor: 'tor节点',
  // VPN: 'VPN节点',
};

export const threatFlagImg = {
  Web_attack,
  'Exploits and Attacks': Exploits,
  APT,
  'Malicious Host': Malicious,
  Suspicious,
  Botnet,
  Phishing,
  Mining,
  Scanning,
  Malware,
  DOS,
  Webshell,
  C2,
  Proxy,
  'Brute force': Brute,
  Trojan,
  Ransomware,
  Spam,
  Hfs,
  Tor,
  URL_malware,
  VPN,
  Spyware,
};

// 漏洞、情报、模型排序
export const loopHoleSort = [
  {
    key: 'observedDesc',
    name: '按发现时间倒序',
    payload: { sort: 'observedTime', order: 'desc' },
  },
  {
    key: 'observed',
    name: '按发现时间升序',
    payload: { sort: 'observedTime', order: 'asc' },
  },
  {
    key: 'threatScoreDesc',
    name: '按威胁得分倒序',
    payload: { sort: 'threatScore', order: 'desc' },
  },
  {
    key: 'threatScore',
    name: '按威胁得分升序',
    payload: { sort: 'threatScore', order: 'asc' },
  },
];

// 探索>任务排序
export const exploreSort = [
  {
    key: 'createTimeFalse',
    name: '按创建时间倒序',
    payload: { sort: 'createTime', reverse: true },
  },
  {
    key: 'createTimeTrue',
    name: '按创建时间正序',
    payload: { sort: 'createTime', reverse: false },
  },
  {
    key: 'exploreTimeFalse',
    name: '按探索时间倒序',
    payload: { sort: 'startTime', reverse: true },
  },
  {
    key: 'exploreTimeTrue',
    name: '按探索时间正序',
    payload: { sort: 'startTime', reverse: false },
  },
];

// 探索> 任务类型
export const exploreTaskType = [
  {
    key: 'singleTask',
    name: '单次任务',
  },
  {
    key: 'timedTask',
    name: '周期任务',
  },
];

export const killChainMap = {
  Reconnaissance: '侦查跟踪',
  Weaponization: '武器构建',
  Delivery: '载荷投递',
  Exploitation: '漏洞利用',
  Installation: '安装植入',
  'Command and Control': '命令控制',
  'Actions on Objective': '目标达成',
};

export const ExploreProgressColor = {
  running: '#003793',
  finish: '#4fc531',
  stop: '#f71f2b',
};
export const FilterMap = [
  { key: 'threatFlag', value: '威胁分类' },
  { key: 'threatLevel', value: ' 威胁等级' },
  { key: 'killChains', value: '杀伤链阶段' },
  { key: 'country', value: '国家 ' },
  { key: 'ip', value: 'IP地址' },
  { key: 'eventType', value: '事件类型' },
];

// 攻击结果

export const levelColor = {
  Low: '#ECD500',
  Medium: '#F76E00',
  High: '#A42400 ',
};

// 告警> 情报> 协议
export const protoOptions = [
  { key: 'http', value: 'http' },
  { key: 'dns', value: 'dns' },
  { key: 'ftp', value: 'ftp' },
  { key: 'ssl', value: 'ssl' },
  { key: 'icmp', value: 'icmp' },
  { key: 'dpd', value: 'dpd' },
  { key: 'tcp', value: 'tcp' },
  { key: 'udp', value: 'udp' },
];
// 探索任务时间周期
export const exploreCycle = [
  { key: 'hour', name: '每小时' },
  { key: 'day', name: '每天' },
  { key: 'week', name: '每周' },
  { key: 'month', name: '每月' },
];

// 探索任务重新处理数据源所有数据
export const backWork = [
  { key: true, name: '是' },
  { key: false, name: '否' },
];
export const bakckDispatch = [
  { key: true, name: '是' },
  { key: false, name: '否' },
];

export const onlineServerType = [
  { key: 'FTP服务器', value: 'ftp' },
  { key: 'CIFS服务器', value: 'nas' },
  { key: 'NFS服务器', value: 'nfs' },
];
export const isConnect = [
  { key: '是', value: true },
  { key: '否', value: false },
];

export const hit = {
  ioc: '情报',
  model: '模型',
  vul: '规则',
  file_log: '文件',
  pcap: '协议',
};
export const hitName = {
  ioc: '命中情报',
  model: '命中模型',
  vul: '命中特征',
  file_log: '命中病毒',
};

export const httpColumns = [
  { key: 'method', label: 'method' },
  { key: 'uri', label: 'uri' },
  { key: 'version', label: 'version' },
  { key: 'host', label: 'host' },
  { key: 'user_agent', label: 'user_agent' },
  { key: 'status_code', label: 'status_code' },
  { key: 'status_msg', label: 'status_msg' },
  { key: 'server', label: 'server' },
  { key: 'content_type', label: 'content_type' },
  { key: 'body', label: 'body' },
  { key: 'pcap_filename', label: 'pcap_filename' },
  // 'method',
  // 'uri',
  // 'version',
  // 'host',
  // 'user_agent',
  // 'status_code',
  // 'status_msg',
  // 'server',
  // 'content_type',
  // 'body',
  // 'pcap_filename',
];
export const httpHidden = [
  'uuid',
  'conn_id',
  'trans_depth',
  'cookie',
  'origin',
  'referrer',
  'xff',
  'request_body_len',
  'response_body_len',
  'set_cookie',
  'file_names',
  'taskId',
  'celeryId',
  'topic',
  'intf',
];

export const dnsColumns = [
  { key: 'query', label: 'query' },
  { key: 'qtype_name', label: 'qtype_name' },
  { key: 'result', label: 'result' },
  { key: 'cname', label: 'cname' },
  { key: 'pcap_filename', label: 'pcap_filename' },
  // 'query',
  // 'qtype_name',
  // 'result',
  // 'cname',
  // 'pcap_filename',
];
export const dnsHidden = [
  'uuid',
  'conn_id',
  'proto',
  'trans_id',
  'qclass_name',
  'mx',
  'flags',
  'query_num',
  'answer_num',
  'auth_num',
  'ext_num',
  'taskId',
  'celeryId',
  'topic',
  'intf',
];

export const ftpColumns = [
  { key: 'user', label: 'user' },
  { key: 'password', label: 'password' },
  { key: 'command', label: 'command' },
  { key: 'arg', label: 'arg' },
  { key: 'reply_code', label: 'reply_code' },
  { key: 'reply_msg', label: 'reply_msg' },
  { key: 'pcap_filename', label: 'pcap_filename' },
  // 'user', 'password', 'command', 'arg', 'reply_code', 'reply_msg', 'pcap_filename'
];
export const ftpHidden = ['uuid', 'conn_id', 'taskId', 'celeryId', 'topic', 'intf', 'file_size', 'cmd_sequence'];

export const sslColumns = [
  { key: 'server_name', label: 'server_name' },
  { key: 'server_cipher', label: 'server_cipher' },
  // { key: 'server_cert_serial_number', label: 'server_cert_serial_number' },
  { key: 'cert_issuer', label: 'cert_issuer' },
  { key: 'cert_validity', label: 'cert_validity' },
  { key: 'cert_subject', label: 'cert_subject' },
  { key: 'pcap_filename', label: 'pcap_filename' },
  // 'server_name',
  // 'server_cipher',
  // 'server_cert_serial_number',
  // 'cert_issuer',
  // 'cert_validity',
  // 'cert_subject',
  // 'pcap_filename',
];
export const sslHidden = [
  'uuid',
  'conn_id',
  'version',
  'client_cipher_num',
  'curve',
  'resumed',
  'alerts',
  'next_protocol',
  'established',
  'ja3',
  'ja3s',
  'taskId',
  'celeryId',
  'topic',
  'intf',
  'server_cert_serial_numbers',
  'server_extension',
  'client_extension',
  'cert_ext_keyId',
  'client_ext_num',
];

export const icmpColumns = [
  { key: 'itype', label: 'itype' },
  { key: 'icode', label: 'icode' },
  { key: 'payload_len', label: 'payload_len' },
  { key: 'payload', label: 'payload' },
  { key: 'pcap_filename', label: 'pcap_filename' },
  // 'itype', 'icode', 'payload_len', 'payload', 'pcap_filename'
];
export const icmpHidden = ['uuid', 'conn_id', 'echo_id', 'seq', 'taskId', 'celeryId', 'topic', 'intf'];

export const filesColumns = [
  { key: 'proto', label: 'proto' },
  { key: 'application', label: 'application' },
  { key: 'filename', label: 'filename' },
  { key: 'filesize', label: 'filesize' },
  { key: 'md5', label: 'md5' },
  { key: 'sha256', label: 'sha256' },
  // 'proto',
  // 'application',
  // 'filename',
  // 'filesize',
  // 'md5',
  // 'sha256',
];
export const filesHidden = ['uuid', 'conn_id', 'sha1', 'taskId', 'celeryId', 'topic', 'intf', 'pcap_filename'];

export const mailColumns = [
  { key: 'service', label: 'service' },
  { key: 'from', label: 'from' },
  { key: 'to', label: 'to' },
  { key: 'cc', label: 'cc' },
  { key: 'subject', label: 'subject' },
  { key: 'body', label: 'body' },
  { key: 'file_names', label: 'file_names' },
  // 'service',
  // 'from',
  // 'to',
  // 'cc',
  // 'subject',
  // 'body',
  // 'file_names',
];
export const mailHidden = [
  'uuid',
  'conn_id',
  'msg_id',
  'date',
  'user_agent',
  'reply_to',
  'bcc',
  'return_path',
  'received',
  'file_md5s',
  'taskId',
  'celeryId',
  'topic',
  'intf',
  'pcap_filename',
];

export const connColumns = [
  { key: 'proto', label: 'proto' },
  { key: 'application', label: 'application' },
  { key: 'orig_pkts', label: 'orig_pkts' },
  { key: 'orig_ip_bytes', label: 'orig_ip_bytes' },
  { key: 'resp_pkts', label: 'resp_pkts' },
  { key: 'resp_ip_bytes', label: 'resp_ip_bytes' },
  { key: 'pcap_filename', label: 'pcap_filename' },
  // 'proto',
  // 'application',
  // 'orig_pkts',
  // 'orig_ip_bytes',
  // 'resp_pkts',
  // 'resp_ip_bytes',
  // 'pcap_filename',
];
export const connHidden = [
  'uuid',
  'conn_id',
  'duration',
  'orig_bytes',
  'resp_bytes',
  'conn_state',
  'normal_start',
  'missed_bytes',
  'history',
  'taskId',
  'celeryId',
  'topic',
  'intf',
];

export const mysqlColumns = [
  { key: 'version', label: 'version' },
  { key: 'username', label: 'username' },
  { key: 'password', label: 'password' },
  { key: 'response_code', label: 'response_code' },
  { key: 'statement', label: 'statement' },
  // 'version', 'username', 'password', 'response_code', 'statement'
];
export const mysqlHidden = [
  'uuid',
  'conn_id',
  'protocol',
  'server_capabilities',
  'auth_plugin',
  'client_auth_plugin',
  'client_capabilities',
  'taskId',
  'celeryId',
  'topic',
  'intf',
  'pcap_filename',
];

export const loginColumns = [
  { key: 'application', label: 'application' },
  { key: 'username', label: 'username' },
  { key: 'password', label: 'password' },
  { key: 'encrypted_password', label: 'encrypted_password' },
  // 'application', 'username', 'password', 'encrypted_password'
];
export const loginHidden = ['uuid', 'conn_id', 'taskId', 'celeryId', 'topic', 'intf', 'pcap_filename'];

export const telnetColumns = [
  { key: 'username', label: 'username' },
  { key: 'password', label: 'password' },
  { key: 'command', label: 'command' },
  // 'username', 'password', 'command'
];
export const telnetHidden = ['uuid', 'conn_id', 'taskId', 'celeryId', 'topic', 'intf', 'pcap_filename'];

export const nfsColumns = [
  { key: 'rpc_ver', label: 'rpc_ver' },
  { key: 'rpc_prg', label: 'rpc_prg' },
  { key: 'rpc_prg_ver', label: 'rpc_prg_ver' },
  { key: 'rpc_proc', label: 'rpc_proc' },
  { key: 'version', label: 'version' },
  { key: 'reply_state', label: 'reply_state' },
  { key: 'reply_len', label: 'reply_len' },
  { key: 'procedure', label: 'procedure' },
  // 'rpc_ver',
  // 'rpc_prg',
  // 'rpc_prg_ver',
  // 'rpc_proc',
  // 'version',
  // 'reply_state',
  // 'reply_len',
  // 'procedure',
];
export const nfsHidden = ['uuid', 'conn_id', 'taskId', 'celeryId', 'topic', 'intf', 'pcap_filename'];

export const modbusColumns = [
  { key: 'trans_id', label: 'trans_id' },
  { key: 'proto_id', label: 'proto_id' },
  { key: 'unit_id', label: 'unit_id' },
  { key: 'func_code', label: 'func_code' },
  { key: 'req_data', label: 'req_data' },
  { key: 'rsp_data', label: 'rsp_data' },
  { key: 'excep_code', label: 'excep_code' },
  { key: 'diag_code', label: 'diag_code' },
  // 'trans_id',
  // 'proto_id',
  // 'unit_id',
  // 'func_code',
  // 'req_data',
  // 'rsp_data',
  // 'excep_code',
  // 'diag_code',
];
export const modbusHidden = ['uuid', 'conn_id', 'taskId', 'celeryId', 'topic', 'intf', 'pcap_filename'];

export const dhcpColumns = [
  { key: 'msg_type', label: 'msg_type' },
  { key: 'client_mac', label: 'client_mac' },
  { key: 'op_msg_type', label: 'op_msg_type' },
  { key: 'op_host_name', label: 'op_host_name' },
  { key: 'op_domain_name', label: 'op_domain_name' },
  { key: 'op_ip_addr', label: 'op_ip_addr' },
  { key: 'op_server_ident', label: 'op_server_ident' },
  // 'msg_type',
  // 'client_mac',
  // 'op_msg_type',
  // 'op_host_name',
  // 'op_domain_name',
  // 'op_ip_addr',
  // 'op_server_ident',
];
export const dhcpHidden = [
  'uuid',
  'conn_id',
  'op_domain_name_server',
  'taskId',
  'celeryId',
  'topic',
  'intf',
  'pcap_filename',
];

export const snmpColumns = [
  { key: 'version', label: 'version' },
  { key: 'community', label: 'community' },
  { key: 'pdu_type', label: 'pdu_type' },
  { key: 'request_id', label: 'request_id' },
  { key: 'trap_type', label: 'trap_type' },
  { key: 'kv_list', label: 'kv_list' },
  // 'version',
  // 'community',
  // 'pdu_type',
  // 'request_id',
  // 'trap_type',
  // 'kv_list',
];
export const snmpHidden = [
  'uuid',
  'conn_id',
  'encrypted',
  'error_status',
  'error_index',
  'enterprise',
  'agent_addr',
  'specific_trap',
  'time_stamp',
  'taskId',
  'celeryId',
  'intf',
  'pcap_filename',
  'topic',
];

export const ripColumns = [
  { key: 'command', label: 'command' },
  { key: 'version', label: 'version' },
  { key: 'ip_mask_metric', label: 'ip_mask_metric' },
  // 'command', 'version', 'ip_mask_metric'
];
export const ripHidden = [
  'uuid',
  'conn_id',
  'net_ext_flag',
  'proto',
  'application',
  'duration',
  'orig_bytes',
  'resp_bytes',
  'conn_state',
  'normal_start',
  'missed_bytes',
  'history',
  'orig_pkts',
  'orig_ip_bytes',
  'resp_pkts',
  'resp_ip_bytes',
  'taskId',
  'celeryId',
  'topic',
  'intf',
  'pcap_filename',
];

export const tftpColumns = [
  { key: 'opcode', label: 'opcode' },
  { key: 'filename', label: 'filename' },
  { key: 'type', label: 'type' },
  { key: 'blknum', label: 'blknum' },
  { key: 'blksize', label: 'blksize' },
  { key: 'tsize', label: 'tsize' },
  { key: 'error_msg', label: 'error_msg' },
  // 'opcode',
  // 'filename',
  // 'type',
  // 'blknum',
  // 'blksize',
  // 'tsize',
  // 'error_msg',
];
export const tftpHidden = ['uuid', 'conn_id', 'net_ext_flag', 'taskId', 'celeryId', 'topic', 'intf', 'pcap_filename'];

export const netbiosColumns = [
  { key: 'service_type', label: 'service_type' },
  { key: 'nbns_msg_type', label: 'nbns_msg_type' },
  { key: 'nbns_trans_id', label: 'nbns_trans_id' },
  { key: 'nbns_name', label: 'nbns_name' },
  { key: 'nbns_rsp_ip', label: 'nbns_rsp_ip' },
  { key: 'nbss_msg_type', label: 'nbss_msg_type' },
  { key: 'nbds_msg_type', label: 'nbds_msg_type' },
  { key: 'nbds_src_ip', label: 'nbds_src_ip' },
  { key: 'nbds_src_port', label: 'nbds_src_port' },
  { key: 'nbds_src_name', label: 'nbds_src_name' },
  { key: 'nbds_dst_name', label: 'nbds_dst_name' },
  // 'service_type',
  // 'nbns_msg_type',
  // 'nbns_trans_id',
  // 'nbns_name',
  // 'nbns_rsp_ip',
  // 'nbss_msg_type',
  // // 'nbns_called_name',
  // // 'nbns_calling_name',
  // // 'nbns_error_code',
  // 'nbds_msg_type',
  // 'nbds_src_ip',
  // 'nbds_src_port',
  // 'nbds_src_name',
  // 'nbds_dst_name',
];
export const netbiosHidden = [
  'uuid',
  'conn_id',
  'nbss_called_name',
  'nbss_calling_name',
  'nbss_error_code',
  'taskId',
  'celeryId',
  'topic',
  'intf',
  'pcap_filename',
];
export const mssqlColumns = [
  { key: 'pkt_type', label: 'pkt_type' },
  { key: 'req_sqls', label: 'req_sqls' },
  { key: 'rsp_tokens', label: 'rsp_tokens' },
];
export const mssqlHidden = ['uuid', 'celeryId', 'conn_id', 'intf', 'pcap_filename', 'taskId', 'topic'];
export const igmpColumns = [
  { key: 'version', label: 'version' },
  { key: 'type', label: 'type' },
  { key: 'max_resp_time', label: 'max_resp_time' },
  { key: 'multicast_addr', label: 'multicast_addr' },
  // 'version', 'type', 'max_resp_time', 'multicast_addr'
];
export const igmpHidden = ['celeryId', 'conn_id', 'intf', 'pcap_filename', 'taskId', 'topic', 'uuid'];
export const smbColumns = [
  { key: 'version', label: 'version' },
  { key: 'username', label: 'username' },
  { key: 'hostname', label: 'hostname' },
  { key: 'tree_connect', label: 'tree_connect' },
  { key: 'command', label: 'command' },
  { key: 'filename', label: 'filename' },
  { key: 'rsp_status', label: 'rsp_status' },
  // 'version',
  // 'username',
  // 'hostname',
  // 'tree_connect',
  // 'command',
  // 'filename',
  // 'rsp_status',
];
export const smbHidden = ['celeryId', 'conn_id', 'intf', 'pcap_filename', 'taskId', 'topic', 'uuid'];

export const vulTypeOptions = [
  { key: '跨站脚本', value: '跨站脚本' },
  { key: '跨站请求伪造', value: '跨站请求伪造' },
  { key: 'Sql注入', value: 'Sql注入' },
  { key: 'ldap注入', value: 'ldap注入' },
  { key: '邮件命令注入', value: '邮件命令注入' },
  { key: '空字节注入', value: '空字节注入' },
  { key: 'CRLF注入', value: 'CRLF注入' },
  { key: 'Ssi注入', value: 'Ssi注入' },
  { key: 'Xpath注入', value: 'Xpath注入' },
  { key: 'Xml注入', value: 'Xml注入' },
  { key: 'Xquery 注入', value: 'Xquery 注入' },
  { key: '命令执行', value: '命令执行' },
  { key: '代码执行', value: '代码执行' },
  { key: '远程文件包含', value: '远程文件包含' },
  { key: '本地文件包含', value: '本地文件包含' },
  { key: '功能函数滥用', value: '功能函数滥用' },
  { key: '暴力破解', value: '暴力破解' },
  { key: '缓冲区溢出', value: '缓冲区溢出' },
  { key: '内容欺骗', value: '内容欺骗' },
  { key: '证书预测', value: '证书预测' },
  { key: '会话预测', value: '会话预测' },
  { key: '拒绝服务', value: '拒绝服务' },
  { key: '指纹识别', value: '指纹识别' },
  { key: '格式化字符串', value: '格式化字符串' },
  { key: 'http响应伪造', value: 'http响应伪造' },
  { key: 'http响应拆分', value: 'http响应拆分' },
  { key: 'http请求拆分', value: 'http请求拆分' },
  { key: 'http请求伪造', value: 'http请求伪造' },
  { key: 'http参数污染', value: 'http参数污染' },
  { key: '整数溢出', value: '整数溢出' },
  { key: '可预测资源定位', value: '可预测资源定位' },
  { key: '会话固定', value: '会话固定' },
  { key: 'url重定向', value: 'url重定向' },
  { key: '权限提升', value: '权限提升' },
  { key: '解析错误', value: '解析错误' },
  { key: '任意文件创建', value: '任意文件创建' },
  { key: '任意文件下载', value: '任意文件下载' },
  { key: '任意文件删除', value: '任意文件删除' },
  { key: '备份文件发现', value: '备份文件发现' },
  { key: '数据库发现', value: '数据库发现' },
  { key: '目录遍历', value: '目录遍历' },
  { key: '目录穿越', value: '目录穿越' },
  { key: '文件上传', value: '文件上传' },
  { key: '登录绕过', value: '登录绕过' },
  { key: '弱密码', value: '弱密码' },
  { key: '远程密码修改', value: '远程密码修改' },
  { key: '代码泄漏', value: '代码泄漏' },
  { key: '路径泄漏', value: '路径泄漏' },
  { key: '信息泄漏', value: '信息泄漏' },
  { key: '安全模式绕过', value: '安全模式绕过' },
  { key: '挂马', value: '挂马' },
  { key: '暗链', value: '暗链' },
  { key: '后门', value: '后门' },
];
export const killChainsOptions = [
  { key: 'Reconnaissance', value: '侦查跟踪' },
  { key: 'Weaponization', value: '武器构建' },
  { key: 'Delivery', value: '载荷投递' },
  { key: 'Exploitation', value: '漏洞利用' },
  { key: 'Installation', value: '安装植入' },
  { key: 'Command and Control', value: '命令控制' },
  { key: 'Actions on Objective', value: '目标达成' },
];

export const ipOption = [
  { key: '源地址', value: 'src_ip' },
  { key: '目的地址', value: 'dst_ip' },
];

export const intelligenceType = [
  { key: 'ip', value: 'ip' },
  { key: '域名', value: 'domain' },
  // { key: 'url', value: 'url' },
];

export const whiteSeachType = [
  { key: '计算机(PC)', value: '计算机(PC)' },
  { key: '服务器', value: '服务器' },
  { key: '网络设备', value: '网络设备' },
  { key: '安全设备', value: '安全设备' },
  { key: '存储设备', value: '存储设备' },
  { key: '通信设备', value: '通信设备' },
  { key: '终端设备', value: '终端设备' },
  { key: '工控设备', value: '工控设备' },
  { key: '其他硬件', value: '其他硬件' },
];
export const WhiteType = [
  { key: 'ip', value: 'ip' },
  { key: '域名', value: 'domain' },
];
export const markType = [
  {
    key: '自定义特征',
    value: '1',
  },
  {
    key: '预定义特征',
    value: '2',
  },
];

export const featureStatus = [
  {
    key: '启用',
    value: 'enable',
  },
  {
    key: '禁用',
    value: 'disable',
  },
  {
    key: '过时',
    value: 'deprecated',
  },
];

export const controlStatus = [
  {
    key: '未曾变更',
    value: 'init',
  },
  {
    key: '系统自动',
    value: 'auto',
  },
  {
    key: '用户手动',
    value: 'user',
  },
];

export const flowAnalysisList = [
  { value: 'all_flow', key: '流量概览统计' },
  // { value: 'killchains_flow.slice_flow', key: '按时间和杀伤链阶段统计' },
  { value: 'threat_flow.slice_flow', key: '按时间和威胁等级统计' },
  { value: 'proto_flow.slice_flow', key: '按时间和应用协议统计' },
  // { value: 'killchains_flow.total', key: '按杀伤链阶段统计的危险流量' },
  { value: 'threat_flow.total', key: '按时间和威胁等级统计的危险流量' },
  { value: 'proto_flow.total', key: '按应用协议统计的危险流量' },
];

export const alarmAnalysisList = [
  { value: 'statistics_level', key: '威胁等级统计' },
  { value: 'statistics_killchains', key: '杀伤链阶段统计' },
  { value: 'statistics_threat', key: '威胁分类统计' },
];
export const fileType = [
  { value: 'excel', key: 'Excel' },
  { value: 'word', key: 'Word' },
  { value: 'pdf', key: 'PDF' },
  { value: 'html', key: 'HTML' },
];

export const agreementsType = [
  { key: 'TCP', value: 'TCP' },
  { key: 'UDP', value: 'UDP' },
  { key: 'ICMP', value: 'ICMP' },
];
export const downloadtaskType = [
  { key: '实时', value: false },
  { key: '回放', value: true },
];

export const intelligenceFilter = [
  {
    key: 'ioc',
    value: 'ioc',
  },
  {
    key: '攻击者组织',
    value: 'aptOrganization',
  },
  {
    key: '协议',
    value: 'proto',
  },
  {
    key: '源ip',
    value: 'src_ip',
  },
  {
    key: '目的ip',
    value: 'dst_ip',
  },
];
export const loopHoleFilter = [
  {
    key: '源ip',
    value: 'src_ip',
  },
  {
    key: '目的ip',
    value: 'dst_ip',
  },
  {
    key: '告警名称',
    value: 'vulName',
  },
  {
    key: '特征编号',
    value: 'sid',
    valueFormat: (x: string) => parseInt(x, 10),
    valueValidateRules: [
      {
        type: 'array',
        required: true,
        defaultField: {
          type: 'string',
          pattern: /^\d+$/,
          message: '请输入特征数字编号',
        },
        message: '请输入有效的特征编号',
      },
    ],
  },
  {
    key: 'CVE',
    value: 'cve',
  },
];
export const modalFilter = [
  {
    key: '源ip',
    value: 'src_ip',
  },
  {
    key: '目的ip',
    value: 'dst_ip',
  },
];
export const isFilter = [
  {
    key: '相等',
    value: 'equal',
  },
  {
    key: '不相等',
    value: 'notEqual',
  },
  {
    key: '包含',
    value: 'contain',
  },
  {
    key: '不包含',
    value: 'notContain',
  },
];

export const policyList = [
  {
    value: 'auto',
    key: '智能白名单过滤',
  },
  {
    value: 'user_define',
    key: '自定义白名单过滤',
  },
  {
    value: 'full_store',
    key: '全量存储',
  },
];
export const FlowType = [
  {
    value: 'domain',
    key: '域名',
  },
  {
    value: 'ipv4',
    key: 'ipv4',
  },
];

export const modelStatus = [
  {
    value: 'disable',
    key: '禁用',
  },
  {
    value: 'enable',
    key: '启用',
  },
];

export const workSatus = [
  {
    key: '黑洞模式',
    value: 'black_hole',
  },
  {
    key: '二层透传模式',
    value: 'l2_transparent_mode',
  },
];

export const forwardingMode = [
  {
    value: 'l2_transparent_mode',
    key: '二层透传模式',
  },
];

export const regPort = /^((6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])|[0-5]?\d{0,4})$/g;

export const reportTypeOptions = [
  { label: 'HTML', value: 'html' },
  { label: 'PDF', value: 'pdf' },
  { label: 'WORD', value: 'word' },
];

export const reportAltOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const riskDetailOptions = [
  { label: '低危', value: 1 },
  { label: '中危', value: 2 },
  { label: '高危', value: 3 },
  { label: '危急', value: 4 },
];

export const reportContentOptions = [
  { label: '概述', value: 'overview' },
  { label: '统计信息', value: 'stat' },
  { label: '高风险主机详情', value: 'high_risk' },
  { label: '风险主机详情', value: 'risk' },
  { label: '处置建议', value: 'suggest' },
];

export const reportCycleOptions = [
  { label: '日报', value: 'day' },
  { label: '周报', value: 'week' },
  { label: '月报', value: 'month' },
  { label: '季报', value: 'season' },
  { label: '年报', value: 'year' },
];

export const reportStatus = {
  process: '处理中',
  fail: '失败',
  finish: '成功',
};
export const sig_type = [
  { label: 'APT', value: 'APT' },
  { label: 'Common', value: 'Common' },
];
export const sig_status = [
  { label: 'active', value: 'active' },
  { label: 'inactive', value: 'inactive' },
];
export const engineList = [
  { label: 'zoomeye', value: 'zoomeye' },
  { label: 'censys', value: 'censys' },
  { label: 'shodan', value: 'shodan' },
  { label: 'fofa', value: 'fofa' },
  { label: 'other', value: 'other' },
];

export const expansionModel = [
  { label: '自定义', value: '自定义' },
  { label: 'JARM模型', value: 'JARM模型' },
  { label: '证书模型', value: '证书模型' },
  { label: '网站特征模型', value: '网站特征模型' },
  { label: 'IP模型', value: 'IP模型' },
  { label: '域名模型', value: '域名模型' },
];


export const data_types = [
  {
    label: 'conn',
    value: 'conn',
  },
  {
    label: 'url',
    value: 'url',
  },
  {
    label: 'tls',
    value: 'tls',
  },
  {
    label: 'dns',
    value: 'dns',
  },
  {
    label: 'ljc',
    value: 'ljc',
  },
];
