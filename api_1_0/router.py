# -*- coding: utf-8 -*-
# @Time    : 2019-05-05 18:59
# <AUTHOR> hachi
# @File    : router.py
# @Software: PyCharm

"""flask"""

import os

import requests
from flask import Flask, request, json, make_response
from flask_restful import Api

from api_1_0.assets import assets
from api_1_0.back_ip import get_ip_access
from api_1_0.knowledge import get_killchains
from api_1_0.knowledge import get_threatFlag
from api_1_0.knowledge import get_threatLevel
from api_1_0.knowledge import get_eventType
from api_1_0.knowledge import get_task
from api_1_0.knowledge import get_modelName
from api_1_0.knowledge import get_aptOrg

from api_1_0.back_stream import connect
from api_1_0.back_stream import pcap_session
from api_1_0.back_stream import pcap_download
from api_1_0.back_stream import spi_view

from api_1_0.back_feature import feature_content_manager, model_manager, feature_update_manager
from api_1_0.back_white_list import self_signature_white_manager
from api_1_0.back_feature import feature_task_manager
from api_1_0.back_feature import feature_group_manager
from api_1_0.back_feature import customize_ioc_manager

from api_1_0.back_alert import home_page
from api_1_0.back_alert import alert_list
from api_1_0.back_alert import top_threat_score
from api_1_0.back_alert import alert_detail

from api_1_0.back_event import top_latest, get_file_list, get_file_detail
from api_1_0.back_event import top_score
from api_1_0.back_event import get_ioc_list
from api_1_0.back_event import get_ioc_detail
from api_1_0.back_event import get_package
from api_1_0.back_event import get_replay_package

from api_1_0.back_explore import operate
from api_1_0.back_explore import pcap
from api_1_0.back_explore import ftp
from api_1_0.back_event import get_model_list
from api_1_0.back_event import get_model_detail
from api_1_0.monitor import system_monitor_data, service_monitor_data, traffic_monitor_data

from api_1_0.sysconfig import system, security_policy_manager
from api_1_0.sysconfig import network
from api_1_0.sysconfig import about
from api_1_0.sysconfig import help
from api_1_0.sysconfig import log

from api_1_0.report import simple_statistics, alert_export, ioc_export, model_export, export_detail, file_export
from api_1_0.report import simple_statistics_levels
from api_1_0.report import simple_statistics_threat
from api_1_0.report import simple_statistics_killchains
from api_1_0.report import simple_top_event
from api_1_0.report import simple_killchains_map
from api_1_0.report import simple_threat_level_map
from api_1_0.report import simple_app_map
from api_1_0.report import simple_top_attackip
from api_1_0.report import simple_top_victimip
from api_1_0.report import detail_all_flow
from api_1_0.report import detail_trigger_count
from api_1_0.report import detail_ioc_count
from api_1_0.report import detail_killchains_count
from api_1_0.report import detail_threat_count
from api_1_0.report import detail_ip_flow
from api_1_0.report import detail_ip_location
from api_1_0.report import export

from api_1_0.back_white_list import logout_white_manager
from api_1_0.back_white_list import fss_white_manager

from api_1_0.confirm import confirm_result

from api_1_0.dpilog import dpilog, dpilog_ip, dpilog_send
from api_1_0.attack_chains_recv import attack_chains_recv
from api_1_0.back_explore import nas
from api_1_0.utils.flask_log import ndr_log_to_box_thread, log_queue
import threading

from api_1_0 import cluster
from api_1_0.mail_analysis.mail_analysis import MailAnalysisList
from api_1_0.mail_analysis.mail_analysis import MailAnalysisDetail
from api_1_0.mail_analysis.mail_analysis import MailAnalysisAttachList
from api_1_0.mail_analysis.mail_analysis import MailAnalysisRoute

from api_1_0.file_analysis import file_statistics, file_info, file_report
from api_3_0.work_bench import pcap_analyse

import api_1_0.threat_expansion as threat_expansion
import api_1_0.thirdparty as thirdparty


from api_1_0.hdp_grpc import port, forward

# anomaly detection
from api_1_0.anomaly_detection import anomaly_task_manager
from api_1_0.anomaly_detection import anomaly_result_manager
from api_1_0.anomaly_detection import anomaly_alert_manager
from api_1_0.anomaly_detection import manual_detection
from api_1_0.anomaly_detection import scheduler_manager
from api_1_0.anomaly_detection import model_config_manager

# init
from celery_tasks.explore_task import recovery_single_task
from utils.utils import flask_response

app = Flask('ndr', static_folder='/opt/ndr/api_1_0/knowledge/static', static_url_path='/api/v1')
app.secret_key = os.urandom(24)
app.config['UPLOAD_FOLDER'] = "/var/"
api = Api(app)

# 启动ndr日志处理线程，将日志输出到标准盒子
log_thread = threading.Thread(target=ndr_log_to_box_thread, args=(log_queue,), name='PollQueueServer')
log_thread.setDaemon(True)  # 设置为守护线程，主进程不必等待子线程结束，不会阻塞
log_thread.start()

# 初始化用户
# user.init_admin()
# user.init_auditor()
# # 进程启动时强制用户下线
# auths.init_online_user()
# 进程启动时强制mount用户定义想要连接的在线数据源
nas.force_link_nas_server()

# @app.before_request
# def before_request():
#     """授权检查"""
#     rst = auths.Authenticate().identify()
#     # print(rst, request.path)
#     if not rst['flag'] and request.path != '/api/v1/login':
#         return jsonify(rst)

recovery_single_task.delay()

# router

# knowledge
api.add_resource(get_killchains.GetKillchains, '/api/v1/knowledge/killchains')
api.add_resource(get_threatFlag.GetThreatFlag, '/api/v1/knowledge/threat_flag')
api.add_resource(get_threatLevel.GetThreatLevel, '/api/v1/knowledge/threat_level')
api.add_resource(get_eventType.GetEventType, '/api/v1/knowledge/event_type')
api.add_resource(get_task.GetTask, '/api/v1/knowledge/task')
api.add_resource(get_modelName.GetModelName, '/api/v1/knowledge/model_name')
api.add_resource(get_aptOrg.GetAptName, '/api/v1/knowledge/apt_org')

# back_alert
api.add_resource(top_threat_score.TopThreatScore, '/api/v1/alert/top/threat_score')
api.add_resource(alert_list.AlertList, '/api/v1/alert/list')
api.add_resource(alert_detail.AlertDetail, '/api/v1/alert/detail')
api.add_resource(get_package.Package, '/api/v1/package')
api.add_resource(get_replay_package.PackageReplay, '/api/v1/replay')

# back_feature
api.add_resource(feature_content_manager.FeatureListResource, '/api/v1/feature')  # 获取多个特征
api.add_resource(feature_update_manager.FeatureUpdateResource, '/api/v1/feature/update')  # 更新特征，更新mongo并同步更新知识库
api.add_resource(feature_update_manager.FeatureUpdateLog, '/api/v1/feature/update_log')  # 获取更新特征日志
api.add_resource(feature_update_manager.FeatureVersion, '/api/v1/feature/db_version')  # 获取特征版本
api.add_resource(feature_content_manager.FeatureModify, '/api/v1/feature/customer')  # 用户自定义规则
# api.add_resource(feature_task_manager.FeatureActiveTaskOne, '/api/v1/feature/active_task/<string:task_id>')  # 激活任务处理单特征
# api.add_resource(feature_task_manager.FeatureActiveTask, '/api/v1/feature/active_task')  # 定时或者立即激活特征列表
api.add_resource(feature_task_manager.FeatureEnableTask, '/api/v1/feature/enable_status')  # 特征启用或禁用
api.add_resource(feature_group_manager.FeatureGroupWithId, '/api/v1/feature/group/id')  # 单个特征组处理
api.add_resource(feature_group_manager.FeatureGroup, '/api/v1/feature/group')  # 创建特征组或批量特征组管理
api.add_resource(customize_ioc_manager.CustomizeIocImport, '/api/v1/customize/ioc/import')  # IOC 文件上传(Excel 格式)
api.add_resource(customize_ioc_manager.IocThreatType, '/api/v1/customize/ioc/threat_type')  # ioc threat_type
api.add_resource(customize_ioc_manager.CustomizeIocSingle, '/api/v1/customize/ioc/single')  # 单条 IOC 处理
api.add_resource(customize_ioc_manager.CustomizeIoc, '/api/v1/customize/ioc')  # 多条 IOC 处理
api.add_resource(customize_ioc_manager.CustomizeIocUpdate, '/api/v1/customize/ioc/update')  # 预定义 IOC 更新
api.add_resource(customize_ioc_manager.IocUpdateLog, '/api/v1/ioc/update_log')  # 获取更新情报日志
api.add_resource(customize_ioc_manager.IocVersion, '/api/v1/ioc/db_version')  # 获取情报版本
api.add_resource(logout_white_manager.LogOutWhiteImport, '/api/v1/whitelist/import')  # 白名单文件上传(Excel 格式)
api.add_resource(logout_white_manager.LogOutWhite, '/api/v1/whitelist')  # 白名单处理(增删改查)
api.add_resource(model_manager.ModelResource, '/api/v1/model/manage')
api.add_resource(model_manager.ModelThreatType, '/api/v1/model/threat_type')

# back_event
api.add_resource(top_latest.TopLatest, '/api/v1/event/top/latest')
api.add_resource(top_score.TopScore, '/api/v1/event/top/score')
api.add_resource(get_ioc_list.IocListResource, '/api/v1/ioc_alert/list')
api.add_resource(get_ioc_detail.IocDetailResource, '/api/v1/ioc_alert/detail')
api.add_resource(get_ioc_list.IocProto, '/api/v1/ioc/proto')
api.add_resource(get_model_list.ModelListResource, '/api/v1/model/list')
api.add_resource(get_model_detail.ModelGroupAll, '/api/v1/model/groupAll')
api.add_resource(get_model_detail.ModelDetail, '/api/v1/model/detail')
api.add_resource(get_file_list.FileListResource, '/api/v1/file_alert/list')
api.add_resource(get_file_detail.FileDetailResource, '/api/v1/file_alert/detail')
api.add_resource(get_file_list.FileThreatType, '/api/v1/file/threat_type')

# back stream
api.add_resource(pcap_download.PcapDownload, '/api/v1/pcap/download')
api.add_resource(spi_view.SpiView, '/api/v1/pcap/spi')
api.add_resource(connect.Connect, '/api/v1/pcap/connect')
api.add_resource(pcap_session.PcapSession, '/api/v1/pcap/session')

# explore
api.add_resource(operate.ExploreTaskWithId, '/api/v1/explore/<string:task_id>')
api.add_resource(operate.ExploreTask, '/api/v1/explore')
api.add_resource(operate.ExploreTaskPcap, '/api/v1/explore/task_pcap')
api.add_resource(pcap.CustomizedData, '/api/v1/explore/pcap')
api.add_resource(pcap.CustomizedDataSinge, '/api/v1/explore/pcap_singe')
api.add_resource(pcap.PcapChunk, '/api/v1/explore/pcap_chunk')
api.add_resource(pcap.PcapUpload, '/api/v1/explore/pcap_upload')
api.add_resource(pcap.PcapExistCheck, '/api/v1/explore/pcap_exist')
api.add_resource(ftp.FTPServerDirTree, '/api/v1/explore/ftp_tree')
api.add_resource(ftp.FTPServer, '/api/v1/explore/ftp_server')
api.add_resource(ftp.FTPServerFileDownload, '/api/v1/explore/ftp_download')
api.add_resource(pcap.MakePcapDir, '/api/v1/explore/dir')
api.add_resource(nas.NASList, '/api/v1/explore/nas/list_info')
api.add_resource(nas.NASOperator, '/api/v1/explore/nas')
api.add_resource(nas.NASMonitorDirs, '/api/v1/explore/nas/config')

# auth
# api.add_resource(auths.Login, '/api/v1/login')
# api.add_resource(auths.Logout, '/api/v1/logout')
# api.add_resource(user.Users, '/api/v1/user')
# api.add_resource(user.UserGetOne, '/api/v1/user/<string:user_id>')  # 单用户处理

# system config
api.add_resource(system.SetSystemConfig, '/api/v1/system/system_config')
api.add_resource(system.SetSystemAlarm, '/api/v1/system/system_alarm')
api.add_resource(system.Reboot, '/api/v1/system/reboot')
api.add_resource(system.PowerOff, '/api/v1/system/poweroff')
api.add_resource(system.DeleteSystemData, '/api/v1/system/delete_system_data')
api.add_resource(system.DeleteEsData, '/api/v1/system/delete_es_data')
api.add_resource(system.GetSystemStatus, '/api/v1/system/status')
api.add_resource(system.GetSystemHistoryStatus, '/api/v1/system/history')
api.add_resource(system.FileProtocol, '/api/v1/system/file_protocol')
api.add_resource(system.FileType, '/api/v1/system/file_type')
api.add_resource(system.FileSize, '/api/v1/system/file_size')
api.add_resource(network.NetCardInfo, '/api/v1/system/net/card')
api.add_resource(network.Network, '/api/v1/system/network')
api.add_resource(network.NetWorkTest, '/api/v1/system/net/test')
api.add_resource(network.Interface, '/api/v1/system/interface')
api.add_resource(network.InterfaceForword, '/api/v1/system/interface_forword/<string:group_name>',
                 '/api/v1/system/interface_forword')
api.add_resource(about.About, '/api/v1/system/about')
api.add_resource(help.Help, '/api/v1/system/help')
api.add_resource(log.WebLog, '/api/v1/system/log')
api.add_resource(log.LogFileDownLoad, '/api/v1/system/export/log')
api.add_resource(fss_white_manager.FssWhite, '/api/v1/system/fss_policy')
api.add_resource(fss_white_manager.FssWhiteImport, '/api/v1/system/fss_policy/import')
api.add_resource(fss_white_manager.FssWhiteConfig, '/api/v1/system/fss_policy/config')

# report
api.add_resource(simple_statistics.simpleReportResource, '/api/v1/report/simple/statistics')
api.add_resource(simple_statistics_levels.StatisticsLevel, '/api/v1/report/simple/statistics_level')
api.add_resource(simple_statistics_threat.StatisticsThreat, '/api/v1/report/simple/statistics_threat')
api.add_resource(simple_statistics_killchains.StatisticsChains, '/api/v1/report/simple/statistics_killchains')
api.add_resource(simple_top_event.EventTopResource, '/api/v1/report/simple/top_event')
api.add_resource(simple_killchains_map.KillchainsMap, '/api/v1/report/simple/killchains_flow')
api.add_resource(simple_threat_level_map.ThreatLevelMap, '/api/v1/report/simple/threat_flow')
api.add_resource(simple_app_map.AppMap, '/api/v1/report/simple/proto_flow')
api.add_resource(simple_top_attackip.TopAttack, '/api/v1/report/simple/top_attackip')
api.add_resource(simple_top_victimip.TopVictim, '/api/v1/report/simple/top_victimip')
api.add_resource(detail_all_flow.AllFlowSum, '/api/v1/report/detail/all_flow')
api.add_resource(detail_trigger_count.TriggerSum, '/api/v1/report/detail/trigger_count')
api.add_resource(detail_ioc_count.IocSum, '/api/v1/report/detail/ioc_count')
api.add_resource(detail_killchains_count.KillchainsSum, '/api/v1/report/detail/killchains_count')
api.add_resource(detail_threat_count.ThreatSum, '/api/v1/report/detail/threat_count')
api.add_resource(detail_ip_flow.IpSum, '/api/v1/report/detail/ip_flow')
api.add_resource(detail_ip_location.IpLocation, '/api/v1/report/detail/ip_location')

# confirm
api.add_resource(confirm_result.WaringResultConfirmResource, '/api/v1/alert/confirm')

# dpilog
api.add_resource(dpilog.DpilogListInfo, '/api/v1/dpilog/list_info')
api.add_resource(dpilog.DpilogStatisticsInfo, '/api/v1/dpilog/statistics_info')
api.add_resource(dpilog.DpilogMailDown, '/api/v1/dpilog/mail_down')
api.add_resource(dpilog.DpilogFileDown, '/api/v1/dpilog/file_down')
api.add_resource(dpilog_ip.DpilogIp, '/api/v1/dpilog/ip_info')
api.add_resource(dpilog_send.DpilogIpSend, '/api/v1/dpilog/ip_send_count')
api.add_resource(dpilog_send.DpilogAccept, '/api/v1/dpilog/ip_accept_count')

# attack chains recovery
api.add_resource(attack_chains_recv.AtkChRecvResource, '/api/v1/attack_chains_recovery')

# export pdf/word/excel/html
api.add_resource(export.Export, '/api/v1/report/export')

# export excel
api.add_resource(alert_export.AlertExport, '/api/v1/alert/export')
api.add_resource(ioc_export.IocExport, '/api/v1/ioc/export')
api.add_resource(model_export.ModelExport, '/api/v1/model/export')
api.add_resource(file_export.FileExport, '/api/v1/file/export')
api.add_resource(export_detail.DetailExport, '/api/v1/detail/export')

# monitor
api.add_resource(system_monitor_data.GetSystemCpu, '/api/v1/monitor/cpu')
api.add_resource(system_monitor_data.GetSystemMem, '/api/v1/monitor/mem')
api.add_resource(system_monitor_data.GetSystemDisk, '/api/v1/monitor/disk')
api.add_resource(service_monitor_data.GetServiceData, '/api/v1/monitor/service')

api.add_resource(traffic_monitor_data.InterfaceSpeed, '/api/v1/monitor/interface')
api.add_resource(traffic_monitor_data.TrafficOutPut, '/api/v1/monitor/traffic_output')
api.add_resource(traffic_monitor_data.AppTraffic, '/api/v1/monitor/application_traffic')
api.add_resource(traffic_monitor_data.TrafficEvent, '/api/v1/monitor/traffic_event')
api.add_resource(traffic_monitor_data.NewConnSpeed, '/api/v1/monitor/new_conn_speed')
api.add_resource(traffic_monitor_data.NewConnTop, '/api/v1/monitor/new_conn_top')

# self_signature_white
api.add_resource(self_signature_white_manager.SelfSignWhite, '/api/v1/self_signature_white')

# assets info
api.add_resource(assets.AssetsList, '/api/v1/assets_list')
api.add_resource(assets.AssetsThreatEvent, '/api/v1/assets_threat_event')
api.add_resource(assets.AssetsOperSystem, '/api/v1/assets_system')
api.add_resource(assets.CustomizedMbUpdate, '/api/v1/customize/assets/update')  # 预定义重点资产升级
api.add_resource(assets.CustomizedMbImport, '/api/v1/customize/assets/import')  # 自定义重点资产文件上传(Excel 格式)

api.add_resource(security_policy_manager.SecurityPolicySingle, '/api/v1/security_policy/single')  # 安全策略获取
api.add_resource(security_policy_manager.SecurityPolicyImport, '/api/v1/security_policy/import')  # 安全策略导入
api.add_resource(security_policy_manager.SecurityPolicyExport, '/api/v1/security_policy/export')  # 安全策略导出
api.add_resource(security_policy_manager.SecurityPolicyMailConn, '/api/v1/security_policy/mail_conn')  # 邮箱测试连接
api.add_resource(security_policy_manager.SecurityPolicyInterface, '/api/v1/security_policy/interface')  # 安全策略接口获取

api.add_resource(get_ip_access.IpAccessRelation, '/api/v1/ip_access_relation')  # ip访问关系
api.add_resource(get_ip_access.IpAccessDetail, '/api/v1/ip_access_detail')  # ip访问详情

api.add_resource(home_page.FallAssets, '/api/v1/home_page_fall_assets')  # 失陷资产
api.add_resource(home_page.KillchainStatistics, '/api/v1/home_page_killchain_statistics')  # 杀伤链统计
api.add_resource(home_page.ThreatLevel, '/api/v1/home_page_threat_level')  # 威胁等级统计
api.add_resource(home_page.ThreatType, '/api/v1/home_page_threat_type')  # 攻击类型统计
api.add_resource(home_page.LatestAlarm, '/api/v1/home_page_latest_alarm')  # 最新告警统计
api.add_resource(home_page.AttackSource, '/api/v1/home_page_attack_source')  # 攻击源top5
api.add_resource(home_page.AttackLocation, '/api/v1/home_page_attack_location')  # 攻击地理位置
api.add_resource(home_page.AlarmTrend, '/api/v1/home_page_alarm_trend')  # 告警趋势

# 文件分析
api.add_resource(file_statistics.FileAnalysisStatistic, '/api/v1/file/statistics')
api.add_resource(file_statistics.FileAnalysisIp, '/api/v1/file/ip')
api.add_resource(file_statistics.FileAnalysisIpExport, '/api/v1/file/export/ip')
api.add_resource(file_info.FileAnalysisList, '/api/v1/file/report')
api.add_resource(file_info.FileAnalysisListExport, '/api/v1/file/report/export')
api.add_resource(file_report.FileAnalysisReport, '/api/v1/file/report/detail')
api.add_resource(file_report.FileAnalysisReportExport, '/api/v1/file/report/binexport')

# 集群管理
api.add_resource(cluster.Nodes, '/api/v1/cluster/nodes')
api.add_resource(cluster.Node, '/api/v1/cluster/nodes/<string:node_name>')
api.add_resource(cluster.NodeLeave, '/api/v1/cluster/nodes/<string:node_name>/leave')
api.add_resource(cluster.NodeJoin, '/api/v1/cluster/nodes/<string:node_name>/join')
api.add_resource(cluster.NodeBeMaster, '/api/v1/cluster/nodes/<string:node_name>/bemaster')

api.add_resource(pcap_analyse.PcapList, '/api/v3/workbench/detail/pcap_list')
api.add_resource(pcap_analyse.LoadPcapFile, '/api/v3/workbench/detail/load_pcap')
api.add_resource(pcap_analyse.PcapData, '/api/v3/workbench/detail/pcap_data')
api.add_resource(pcap_analyse.PcapContent, '/api/v3/workbench/detail/pcap_content')
api.add_resource(pcap_analyse.PcapBye, '/api/v3/workbench/detail/pcap_bye')

# 威胁扩线分析
api.add_resource(threat_expansion.Tasks, '/api/v1/threat-expansion/tasks')
api.add_resource(threat_expansion.Task, '/api/v1/threat-expansion/tasks/<string:task_id>')
api.add_resource(threat_expansion.TaskRunner, '/api/v1/threat-expansion/tasks/<string:task_id>/run')
api.add_resource(threat_expansion.Engines, '/api/v1/threat-expansion/engines')
api.add_resource(threat_expansion.Engine, '/api/v1/threat-expansion/engines/<string:engine_id>')
api.add_resource(threat_expansion.AptOrgs, '/api/v1/threat-expansion/aptorgs')

# 邮件分析
api.add_resource(MailAnalysisList, '/api/v1/mail_analysis/email_list')
api.add_resource(MailAnalysisDetail, '/api/v1/mail_analysis/email_detail')
api.add_resource(MailAnalysisAttachList, '/api/v1/mail_analysis/attachments')
api.add_resource(MailAnalysisRoute, '/api/v1/mail_analysis/send_rsv_info')

# 第三方服务API
## cert hive数据源
api.add_resource(thirdparty.cert.HiveRuleTemplate, '/api/v1/cert/hive_rule_template')
api.add_resource(thirdparty.cert.Hives, '/api/v1/cert/hives')
api.add_resource(thirdparty.cert.Hive, '/api/v1/cert/hives/<string:_id>')
api.add_resource(thirdparty.cert.HiveRun, '/api/v1/cert/hives/<string:_id>/run')
api.add_resource(thirdparty.cert.HiveStop, '/api/v1/cert/hives/<string:_id>/stop')

api.add_resource(thirdparty.cert.Retention, '/api/v1/cert/analyis')
api.add_resource(thirdparty.cert.RetentionResult, '/api/v1/cert/analyis/result', '/api/v1/cert/analyis/result/download')
api.add_resource(thirdparty.cert.DocExport, '/api/v1/cert/analyis/export')
api.add_resource(thirdparty.cert.RetentionOperateFlag, '/api/v1/cert/analyis/set_flag')
api.add_resource(thirdparty.cert.RetentionAnalyis, '/api/v1/cert/analyis/get_result')
api.add_resource(thirdparty.cert.RetentionResultDetail,
                 '/api/v1/cert/analyis/detail_result', '/api/v1/cert/analyis/result/detail_download')

api.add_resource(assets.SearchIPC, '/api/v1/cert/ipc_search')

# anomaly detection API
api.add_resource(anomaly_task_manager.AnomalyTaskList, '/api/v1/anomaly/tasks')
api.add_resource(anomaly_task_manager.AnomalyTaskDetail, '/api/v1/anomaly/tasks/<string:task_id>')
api.add_resource(anomaly_result_manager.AnomalyResultList, '/api/v1/anomaly/results')
api.add_resource(anomaly_result_manager.AnomalyResultDetail, '/api/v1/anomaly/results/<string:result_id>')
api.add_resource(anomaly_alert_manager.AnomalyAlertList, '/api/v1/anomaly/alerts')
api.add_resource(anomaly_alert_manager.AnomalyAlertStats, '/api/v1/anomaly/alerts/stats')
api.add_resource(manual_detection.ManualDetection, '/api/v1/anomaly/manual_detection')
api.add_resource(manual_detection.SupportedModels, '/api/v1/anomaly/models')
api.add_resource(manual_detection.ModelDefaultConfig, '/api/v1/anomaly/models/<string:model_type>/config')
api.add_resource(manual_detection.DetectionStatistics, '/api/v1/anomaly/statistics')
api.add_resource(scheduler_manager.SchedulerControl, '/api/v1/anomaly/scheduler')
api.add_resource(scheduler_manager.TaskExecution, '/api/v1/anomaly/tasks/<string:task_id>/execute')
api.add_resource(model_config_manager.ModelConfigList, '/api/v1/anomaly/model_configs')
api.add_resource(model_config_manager.ModelConfigDetail, '/api/v1/anomaly/model_configs/<string:config_id>')

# gRPC API
# api.add_resource(port.GrpcPort, '/api/v1/port/linkstatus')
api.add_resource(forward.GrpcForword, '/api/v1/port/stats')


@app.errorhandler(404)
def not_found(e):
    """404 page"""
    return e, 404


@app.errorhandler(500)
def exception_handler(e):
    return e, 500


# @app.before_request
def before_request():
    """
    授权检查
    由于使用微前端架构，用户管理及鉴权都交由boss盒子的后端去做；
    因此，我们业务后端必须请求boss的api对当前用户进行鉴权后决定是否响应。
    """
    try:
        box_headers = dict(request.headers)
        box_headers.pop('From-Web', None)  # boss-api需求
        box_headers.pop('Content-Type', None)  # 有的请求类型带有content-Type，如POST，需要去掉，否则不响应

        if box_headers.get('Upgrade', None):
            # box_headers.pop('Upgrade', None)  # 安装系统时，命令行升级资产，ioc，rules，绕过权限检查标志字段
            pass
        else:
            rst = requests.get("http://boss-api-v2-prd:7001/api/user/auth", headers=box_headers, timeout=3)
            ret_data = json.loads(rst.content)
            # print(box_headers, '\n', rst.status_code, rst.content, rst.elapsed.total_seconds(), flush=True)

            if 200 != rst.status_code or 0 != ret_data.get('code', None):
                return flask_response('请先登录！', False, {})
    except Exception as e:
        return flask_response('网络繁忙，请再试一次。', False, {'error': str(e)})
