import { get } from 'lodash';
import React from 'react';
import cn from 'classnames';


export default function DetailTable({ style, className, columns, value }) {
    return (
        <div style={style} className={cn('grid grid-cols-[120px_1fr]', className)}>
            {columns.map(({ title, dataIndex, render }) => {
                let colVal = get(value, dataIndex);
                if (render) {
                    colVal = render(colVal, value);
                }
                return (
                    <>
                        <div className="bg-d100 b b-solid b-brd6 py-1 px-2 ws-nowrap">{title}</div>
                        <div className="b b-solid b-brd6 py-1 px-3 truncate">{colVal || '–'}</div>
                    </>
                );
            })}
        </div>
    );
}
